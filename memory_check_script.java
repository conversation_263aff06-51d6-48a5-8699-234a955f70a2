/*
 * Memory Check Script for L2J Essence Crusader GVE
 * Thêm vào AdminDebug.java để kiểm tra memory usage
 */

// Thêm vào AdminDebug.java trong phần xử lý commands

else if (s.equals("memory_check"))
{
    // L<PERSON>y thông tin memory hiện tại
    Runtime runtime = Runtime.getRuntime();
    long maxMemory = runtime.maxMemory();
    long totalMemory = runtime.totalMemory();
    long freeMemory = runtime.freeMemory();
    long usedMemory = totalMemory - freeMemory;
    
    // Tính phần trăm sử dụng
    double usedPercentage = (double) usedMemory / maxMemory * 100;
    double totalPercentage = (double) totalMemory / maxMemory * 100;
    
    StringBuilder sb = new StringBuilder();
    sb.append("<html><body>");
    sb.append("<center><font color=\"LEVEL\">Memory Usage Report</font></center><br>");
    sb.append("<table width=\"300\">");
    
    // Memory statistics
    sb.append("<tr><td>Max Memory:</td><td>").append(maxMemory / 1024 / 1024).append(" MB</td></tr>");
    sb.append("<tr><td>Total Memory:</td><td>").append(totalMemory / 1024 / 1024).append(" MB</td></tr>");
    sb.append("<tr><td>Used Memory:</td><td>").append(usedMemory / 1024 / 1024).append(" MB</td></tr>");
    sb.append("<tr><td>Free Memory:</td><td>").append(freeMemory / 1024 / 1024).append(" MB</td></tr>");
    sb.append("<tr><td>Used %:</td><td><font color=\"").append(usedPercentage > 80 ? "FF0000" : usedPercentage > 60 ? "FFFF00" : "00FF00").append("\">").append(String.format("%.2f", usedPercentage)).append("%</font></td></tr>");
    
    // World statistics
    sb.append("<tr><td colspan=\"2\"><br><center><font color=\"LEVEL\">World Statistics</font></center></td></tr>");
    sb.append("<tr><td>Online Players:</td><td>").append(World.getInstance().getPlayers().size()).append("</td></tr>");
    sb.append("<tr><td>All Objects:</td><td>").append(World.getInstance().getAllVisibleObjectsCount()).append("</td></tr>");
    sb.append("<tr><td>Max Connected:</td><td>").append(World.MAX_CONNECTED_COUNT).append("</td></tr>");
    
    // Database connections
    sb.append("<tr><td colspan=\"2\"><br><center><font color=\"LEVEL\">Database Info</font></center></td></tr>");
    sb.append("<tr><td>Max DB Conn:</td><td>").append(Config.DATABASE_MAX_CONNECTIONS).append("</td></tr>");
    
    // Thread pool info
    sb.append("<tr><td colspan=\"2\"><br><center><font color=\"LEVEL\">Thread Pool Info</font></center></td></tr>");
    sb.append("<tr><td>Scheduled Pools:</td><td>").append(Config.SCHEDULED_POOL_COUNT).append("</td></tr>");
    sb.append("<tr><td>Instant Pools:</td><td>").append(Config.INSTANT_POOL_COUNT).append("</td></tr>");
    
    // Cache info
    sb.append("<tr><td colspan=\"2\"><br><center><font color=\"LEVEL\">Cache Info</font></center></td></tr>");
    sb.append("<tr><td>HTML Cache:</td><td>").append(HtmCache.getInstance().getLoadedFiles()).append(" files</td></tr>");
    sb.append("<tr><td>HTML Memory:</td><td>").append(String.format("%.2f", HtmCache.getInstance().getMemoryUsage())).append(" MB</td></tr>");
    sb.append("<tr><td>Lazy Cache:</td><td>").append(Config.LAZY_CACHE ? "Enabled" : "Disabled").append("</td></tr>");
    
    sb.append("</table><br>");
    
    // Warning messages
    if (usedPercentage > 85)
    {
        sb.append("<center><font color=\"FF0000\">WARNING: High memory usage!</font></center><br>");
    }
    else if (usedPercentage > 70)
    {
        sb.append("<center><font color=\"FFFF00\">CAUTION: Memory usage getting high</font></center><br>");
    }
    else
    {
        sb.append("<center><font color=\"00FF00\">Memory usage is normal</font></center><br>");
    }
    
    // Action buttons
    sb.append("<center>");
    sb.append("<button value=\"Force GC\" action=\"bypass -h admin_debug gc_force\" width=80 height=21 back=\"L2UI_ct1.button_df\" fore=\"L2UI_ct1.button_df\">");
    sb.append("<button value=\"Refresh\" action=\"bypass -h admin_debug memory_check\" width=80 height=21 back=\"L2UI_ct1.button_df\" fore=\"L2UI_ct1.button_df\">");
    sb.append("</center>");
    
    sb.append("</body></html>");
    
    NpcHtmlMessage html = new NpcHtmlMessage();
    html.setHtml(sb.toString());
    activeChar.sendPacket(html);
    
    // Log to console
    LOGGER.info("Memory Check by " + activeChar.getName() + ": Used=" + (usedMemory/1024/1024) + "MB (" + String.format("%.2f", usedPercentage) + "%), Players=" + World.getInstance().getPlayers().size());
}
else if (s.equals("gc_force"))
{
    long beforeGC = Runtime.getRuntime().totalMemory() - Runtime.getRuntime().freeMemory();
    long startTime = System.currentTimeMillis();
    
    System.gc();
    
    long afterGC = Runtime.getRuntime().totalMemory() - Runtime.getRuntime().freeMemory();
    long gcTime = System.currentTimeMillis() - startTime;
    long freedMemory = beforeGC - afterGC;
    
    activeChar.sendMessage("GC completed in " + gcTime + "ms");
    activeChar.sendMessage("Memory freed: " + (freedMemory / 1024 / 1024) + " MB");
    
    LOGGER.info("Manual GC by " + activeChar.getName() + ": Freed " + (freedMemory/1024/1024) + "MB in " + gcTime + "ms");
    
    // Redirect back to memory check
    ThreadPool.get().schedule(() -> {
        try {
            adminCommandHandler.useAdminCommand(activeChar, "admin_debug memory_check", true);
        } catch (Exception e) {
            // Ignore
        }
    }, 1000);
}
else if (s.equals("memory_detailed"))
{
    StringBuilder sb = new StringBuilder();
    sb.append("<html><body>");
    sb.append("<center><font color=\"LEVEL\">Detailed Memory Analysis</font></center><br>");
    sb.append("<table width=\"350\">");
    
    // JVM Info
    sb.append("<tr><td colspan=\"2\"><center><font color=\"LEVEL\">JVM Information</font></center></td></tr>");
    sb.append("<tr><td>Java Version:</td><td>").append(System.getProperty("java.version")).append("</td></tr>");
    sb.append("<tr><td>JVM Name:</td><td>").append(System.getProperty("java.vm.name")).append("</td></tr>");
    
    // Memory details
    Runtime runtime = Runtime.getRuntime();
    sb.append("<tr><td colspan=\"2\"><br><center><font color=\"LEVEL\">Memory Details</font></center></td></tr>");
    sb.append("<tr><td>Available Processors:</td><td>").append(runtime.availableProcessors()).append("</td></tr>");
    
    // Thread information
    ThreadGroup rootGroup = Thread.currentThread().getThreadGroup();
    ThreadGroup parentGroup;
    while ((parentGroup = rootGroup.getParent()) != null) {
        rootGroup = parentGroup;
    }
    int threadCount = rootGroup.activeCount();
    sb.append("<tr><td>Active Threads:</td><td>").append(threadCount).append("</td></tr>");
    
    // Server uptime
    long uptime = ManagementFactory.getRuntimeMXBean().getUptime();
    long hours = uptime / (1000 * 60 * 60);
    long minutes = (uptime % (1000 * 60 * 60)) / (1000 * 60);
    sb.append("<tr><td>Server Uptime:</td><td>").append(hours).append("h ").append(minutes).append("m</td></tr>");
    
    // Configuration warnings
    sb.append("<tr><td colspan=\"2\"><br><center><font color=\"LEVEL\">Configuration Check</font></center></td></tr>");
    
    if (Config.DATABASE_MAX_CONNECTIONS < 100) {
        sb.append("<tr><td colspan=\"2\"><font color=\"FFFF00\">Warning: DB connections may be low for high load</font></td></tr>");
    }
    
    if (!Config.LAZY_CACHE) {
        sb.append("<tr><td colspan=\"2\"><font color=\"FFFF00\">Info: HTML cache loads all files at startup</font></td></tr>");
    }
    
    if (Config.CACHE_CHAR_NAMES) {
        sb.append("<tr><td colspan=\"2\"><font color=\"FFFF00\">Info: Character names cached in memory</font></td></tr>");
    }
    
    sb.append("</table><br>");
    sb.append("<center>");
    sb.append("<button value=\"Back\" action=\"bypass -h admin_debug memory_check\" width=80 height=21 back=\"L2UI_ct1.button_df\" fore=\"L2UI_ct1.button_df\">");
    sb.append("</center>");
    sb.append("</body></html>");
    
    NpcHtmlMessage html = new NpcHtmlMessage();
    html.setHtml(sb.toString());
    activeChar.sendPacket(html);
}
