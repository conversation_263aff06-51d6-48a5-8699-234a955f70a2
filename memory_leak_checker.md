# Memory Leak Detection Script

## 🔍 C<PERSON>c điểm cần kiểm tra trong code

### 1. Static Collections Monitoring

Thêm vào AdminDebug.java:

```java
else if (s.equals("leak_check"))
{
    StringBuilder sb = new StringBuilder();
    sb.append("<html><body>");
    sb.append("<center><font color=\"LEVEL\">Memory Leak Detection</font></center><br>");
    sb.append("<table width=\"400\">");
    
    // World objects count
    sb.append("<tr><td colspan=\"2\"><center><font color=\"LEVEL\">World Objects</font></center></td></tr>");
    sb.append("<tr><td>All Players:</td><td>").append(World.getInstance().getPlayers().size()).append("</td></tr>");
    sb.append("<tr><td>All Objects:</td><td>").append(World.getInstance().getAllVisibleObjectsCount()).append("</td></tr>");
    sb.append("<tr><td>Pet Instances:</td><td>").append(World.getInstance().getPetsCount()).append("</td></tr>");
    
    // Thread pool status
    sb.append("<tr><td colspan=\"2\"><br><center><font color=\"LEVEL\">Thread Pool Status</font></center></td></tr>");
    
    // Check for potential leaks
    sb.append("<tr><td colspan=\"2\"><br><center><font color=\"LEVEL\">Potential Issues</font></center></td></tr>");
    
    int playerCount = World.getInstance().getPlayers().size();
    int objectCount = World.getInstance().getAllVisibleObjectsCount();
    
    // Object to player ratio check
    double objectRatio = playerCount > 0 ? (double)objectCount / playerCount : 0;
    if (objectRatio > 1000) {
        sb.append("<tr><td colspan=\"2\"><font color=\"FF0000\">WARNING: High object/player ratio (").append(String.format("%.1f", objectRatio)).append(")</font></td></tr>");
    }
    
    // Memory usage check
    Runtime runtime = Runtime.getRuntime();
    long usedMemory = runtime.totalMemory() - runtime.freeMemory();
    double memoryPerPlayer = playerCount > 0 ? (double)(usedMemory / 1024 / 1024) / playerCount : 0;
    
    sb.append("<tr><td>Memory/Player:</td><td>").append(String.format("%.2f", memoryPerPlayer)).append(" MB</td></tr>");
    
    if (memoryPerPlayer > 4.0) {
        sb.append("<tr><td colspan=\"2\"><font color=\"FF0000\">WARNING: High memory per player</font></td></tr>");
    }
    
    sb.append("</table><br>");
    sb.append("<center>");
    sb.append("<button value=\"Detailed Check\" action=\"bypass -h admin_debug leak_detailed\" width=100 height=21 back=\"L2UI_ct1.button_df\" fore=\"L2UI_ct1.button_df\">");
    sb.append("<button value=\"Force Cleanup\" action=\"bypass -h admin_debug force_cleanup\" width=100 height=21 back=\"L2UI_ct1.button_df\" fore=\"L2UI_ct1.button_df\">");
    sb.append("</center>");
    sb.append("</body></html>");
    
    NpcHtmlMessage html = new NpcHtmlMessage();
    html.setHtml(sb.toString());
    activeChar.sendPacket(html);
}
```

### 2. Database Connection Leak Check

```java
else if (s.equals("db_check"))
{
    StringBuilder sb = new StringBuilder();
    sb.append("<html><body>");
    sb.append("<center><font color=\"LEVEL\">Database Connection Check</font></center><br>");
    sb.append("<table width=\"300\">");
    
    sb.append("<tr><td>Max Connections:</td><td>").append(Config.DATABASE_MAX_CONNECTIONS).append("</td></tr>");
    
    // Simulate connection test
    int successfulConnections = 0;
    long startTime = System.currentTimeMillis();
    
    try {
        for (int i = 0; i < 5; i++) {
            try (Connection con = DatabaseFactory.getConnection()) {
                successfulConnections++;
            }
        }
    } catch (Exception e) {
        // Connection issues
    }
    
    long connectionTime = System.currentTimeMillis() - startTime;
    
    sb.append("<tr><td>Test Connections:</td><td>").append(successfulConnections).append("/5</td></tr>");
    sb.append("<tr><td>Connection Time:</td><td>").append(connectionTime).append("ms</td></tr>");
    
    if (connectionTime > 1000) {
        sb.append("<tr><td colspan=\"2\"><font color=\"FF0000\">WARNING: Slow DB connections</font></td></tr>");
    }
    
    if (successfulConnections < 5) {
        sb.append("<tr><td colspan=\"2\"><font color=\"FF0000\">ERROR: Connection failures detected</font></td></tr>");
    }
    
    sb.append("</table>");
    sb.append("</body></html>");
    
    NpcHtmlMessage html = new NpcHtmlMessage();
    html.setHtml(sb.toString());
    activeChar.sendPacket(html);
}
```

## 🛠️ Automated Cleanup Script

### Thêm vào GameServer.java (trong startup):

```java
// Memory leak prevention - cleanup task
ThreadPool.get().scheduleAtFixedRate(() -> {
    try {
        // Force cleanup of disconnected players
        World.getInstance().getPlayers().removeIf(player -> 
            player == null || player.getClient() == null || !player.getClient().isConnected());
        
        // Cleanup empty regions
        for (WorldRegion region : World.getInstance().getAllWorldRegions()) {
            if (region != null && region.getVisibleObjects().isEmpty()) {
                // Region cleanup if needed
            }
        }
        
        // Log memory status
        long usedMemory = GameServer.getInstance().getUsedMemoryMB();
        int playerCount = World.getInstance().getPlayers().size();
        
        if (usedMemory > 6000) { // 6GB threshold
            LOGGER.warning("High memory usage detected: " + usedMemory + "MB with " + playerCount + " players");
        }
        
    } catch (Exception e) {
        LOGGER.log(Level.WARNING, "Error in cleanup task", e);
    }
}, 300000, 300000); // Every 5 minutes
```

## 📊 Memory Monitoring Dashboard

### Tạo file: MemoryMonitor.java

```java
public class MemoryMonitor {
    private static final Logger LOGGER = Logger.getLogger(MemoryMonitor.class.getName());
    private static long lastMemoryCheck = 0;
    private static long lastPlayerCount = 0;
    
    public static void checkMemoryLeak() {
        Runtime runtime = Runtime.getRuntime();
        long currentMemory = runtime.totalMemory() - runtime.freeMemory();
        long currentPlayers = World.getInstance().getPlayers().size();
        
        if (lastMemoryCheck > 0 && lastPlayerCount > 0) {
            long memoryDiff = currentMemory - lastMemoryCheck;
            long playerDiff = currentPlayers - lastPlayerCount;
            
            // Check for memory leak indicators
            if (playerDiff <= 0 && memoryDiff > 50 * 1024 * 1024) { // 50MB increase with no new players
                LOGGER.warning("Potential memory leak detected: +" + (memoryDiff/1024/1024) + "MB with " + playerDiff + " player change");
            }
            
            // Check memory per player ratio
            if (currentPlayers > 0) {
                double memoryPerPlayer = (double)(currentMemory / 1024 / 1024) / currentPlayers;
                if (memoryPerPlayer > 5.0) { // More than 5MB per player
                    LOGGER.warning("High memory per player: " + String.format("%.2f", memoryPerPlayer) + "MB/player");
                }
            }
        }
        
        lastMemoryCheck = currentMemory;
        lastPlayerCount = currentPlayers;
    }
}
```

## 🚨 Critical Memory Leak Patterns

### 1. Event Listener Leaks
```java
// BAD - Listener không được remove
EventDispatcher.getInstance().addListener(listener);

// GOOD - Remove listener khi không cần
EventDispatcher.getInstance().removeListener(listener);
```

### 2. Static Collection Leaks
```java
// BAD - Static collection không bao giờ clear
private static final Map<Integer, SomeObject> CACHE = new HashMap<>();

// GOOD - Có cleanup mechanism
private static final Map<Integer, SomeObject> CACHE = new ConcurrentHashMap<>();
// + cleanup task để remove old entries
```

### 3. Thread Leak Prevention
```java
// BAD - Thread không được cleanup
new Thread(runnable).start();

// GOOD - Sử dụng ThreadPool
ThreadPool.get().execute(runnable);
```

## 📋 Daily Monitoring Checklist

### Kiểm tra hàng ngày:
1. **Memory usage trend** - Có tăng liên tục không?
2. **Player/Object ratio** - Có bất thường không?
3. **GC frequency** - Có tăng đột ngột không?
4. **Database connections** - Có leak không?
5. **Thread count** - Có tăng bất thường không?

### Commands để chạy:
```
//debug memory_check
//debug leak_check  
//debug db_check
//debug gc_force (nếu cần)
```

## 🔧 Emergency Procedures

### Khi phát hiện memory leak:
1. **Immediate**: Force GC và monitor
2. **Short term**: Restart server nếu critical
3. **Long term**: Analyze heap dump và fix code

### Khi memory usage > 85%:
1. Check player count vs memory ratio
2. Force cleanup tasks
3. Consider server restart
4. Investigate recent changes

## 📈 Performance Baselines

### Normal Memory Usage (per player):
- **Startup**: ~2MB/player
- **Normal gameplay**: ~3-4MB/player  
- **Peak activity**: ~5MB/player
- **Critical**: >6MB/player

### Red Flags:
- Memory usage tăng >100MB/hour với stable player count
- Object count tăng không tương ứng với player count
- GC không giải phóng được memory
- Database connection timeouts
