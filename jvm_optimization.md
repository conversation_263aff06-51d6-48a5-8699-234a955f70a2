# JVM Optimization cho L2J Essence Crusader GVE (2000 Players)

## 🚀 <PERSON><PERSON><PERSON>ến nghị JVM Arguments

### Cho GameServer (2000 players)
```bash
# Memory Settings
-Xms4G
-Xmx8G
-XX:NewRatio=2
-XX:SurvivorRatio=8

# Garbage Collector (G1GC - tốt nhất cho server game)
-XX:+UseG1GC
-XX:MaxGCPauseMillis=200
-XX:G1HeapRegionSize=16m
-XX:G1NewSizePercent=20
-XX:G1MaxNewSizePercent=40
-XX:G1MixedGCCountTarget=8
-XX:G1MixedGCLiveThresholdPercent=85

# GC Optimization
-XX:+DisableExplicitGC
-XX:+UseStringDeduplication
-XX:+OptimizeStringConcat

# Performance Tuning
-XX:+UseFastAccessorMethods
-XX:+UseCompressedOops
-XX:+AggressiveOpts
-server

# Memory Leak Detection
-XX:+HeapDumpOnOutOfMemoryError
-XX:HeapDumpPath=./logs/
-XX:+PrintGCDetails
-XX:+PrintGCTimeStamps
-XX:+PrintGCApplicationStoppedTime
-Xloggc:./logs/gc.log

# Network Optimization
-Djava.net.preferIPv4Stack=true
-Dfile.encoding=UTF-8
```

### Cho LoginServer
```bash
# Memory Settings (nhỏ hơn)
-Xms512M
-Xmx1G

# Garbage Collector
-XX:+UseG1GC
-XX:MaxGCPauseMillis=100

# Basic optimizations
-XX:+DisableExplicitGC
-XX:+UseCompressedOops
-server
-Djava.net.preferIPv4Stack=true
```

## 📊 Monitoring và Debugging

### 1. Memory Monitoring Commands
```bash
# Kiểm tra memory usage
jstat -gc [PID] 5s

# Heap dump khi cần
jmap -dump:format=b,file=heapdump.hprof [PID]

# Thread dump
jstack [PID] > threaddump.txt
```

### 2. GC Log Analysis
```bash
# Xem GC logs
tail -f logs/gc.log

# Analyze GC performance
# Sử dụng GCViewer hoặc GCPlot.com
```

## ⚙️ Cấu hình Server.ini tối ưu

```ini
# Database Connections (tăng cho 2000 players)
MaximumDbConnections = 120

# Thread Pool (tối ưu cho high load)
ScheduledPoolCount = 8
InstantPoolCount = 2
ScheduledThreadPoolCount = 6
InstantThreadPoolCount = 8

# Memory Management
PrecautionaryRestartEnabled = True
PrecautionaryRestartMemory = True
PrecautionaryRestartPercentage = 85
PrecautionaryRestartDelay = 300000
```

## ⚙️ Cấu hình General.ini tối ưu

```ini
# Database Cleanup
DatabaseCleanUp = True
CharacterDataStoreInterval = 5
ClanVariablesStoreInterval = 10

# Memory Optimization
LazyCache = True
CacheCharNames = False
WarehouseCache = False

# Item Management
SaveDroppedItem = False
EmptyDroppedItemTableAfterLoad = True
AutoDestroyDroppedItemAfter = 300

# Performance
ForceInventoryUpdate = False
```

## 🔧 Startup Scripts

### GameServer.bat (Windows)
```batch
@echo off
title L2J GameServer
:start
echo Starting L2J Game Server.
echo.

java -Xms4G -Xmx8G ^
-XX:+UseG1GC ^
-XX:MaxGCPauseMillis=200 ^
-XX:G1HeapRegionSize=16m ^
-XX:+DisableExplicitGC ^
-XX:+UseStringDeduplication ^
-XX:+HeapDumpOnOutOfMemoryError ^
-XX:HeapDumpPath=./logs/ ^
-XX:+PrintGCDetails ^
-XX:+PrintGCTimeStamps ^
-Xloggc:./logs/gc.log ^
-Djava.net.preferIPv4Stack=true ^
-Dfile.encoding=UTF-8 ^
-server ^
-cp ./libs/*;l2jserver.jar ^
club.projectessence.gameserver.GameServer

if ERRORLEVEL 2 goto restart
if ERRORLEVEL 1 goto error
goto end

:restart
echo.
echo Admin Restart ...
echo.
goto start

:error
echo.
echo Server terminated abnormally
echo.

:end
echo.
echo server terminated
echo.
pause
```

### GameServer.sh (Linux)
```bash
#!/bin/bash

while :; do
    echo "Starting L2J Game Server."
    
    java -Xms4G -Xmx8G \
    -XX:+UseG1GC \
    -XX:MaxGCPauseMillis=200 \
    -XX:G1HeapRegionSize=16m \
    -XX:+DisableExplicitGC \
    -XX:+UseStringDeduplication \
    -XX:+HeapDumpOnOutOfMemoryError \
    -XX:HeapDumpPath=./logs/ \
    -XX:+PrintGCDetails \
    -XX:+PrintGCTimeStamps \
    -Xloggc:./logs/gc.log \
    -Djava.net.preferIPv4Stack=true \
    -Dfile.encoding=UTF-8 \
    -server \
    -cp "./libs/*:l2jserver.jar" \
    club.projectessence.gameserver.GameServer
    
    [ $? -ne 2 ] && break
    sleep 10
done
```

## 📈 Performance Benchmarks

### Memory Usage Expectations (2000 players)
- **Startup**: ~1.5GB
- **Normal Load**: ~3-4GB
- **Peak Load**: ~6-7GB
- **Critical**: >7.5GB (restart recommended)

### GC Performance Targets
- **GC Pause Time**: <200ms
- **GC Frequency**: <10 times/minute
- **Throughput**: >95%

## 🚨 Warning Signs

### Memory Leak Indicators
1. **Heap usage tăng liên tục** không giảm sau GC
2. **GC frequency tăng** nhưng memory không giảm
3. **OutOfMemoryError** trong logs
4. **Server lag** tăng dần theo thời gian

### Monitoring Commands trong Game
```
//debug memory_check     - Kiểm tra memory usage
//debug gc_force         - Force garbage collection
//debug memory_detailed  - Chi tiết memory analysis
```

## 🔍 Troubleshooting

### Nếu gặp OutOfMemoryError
1. Tăng heap size: `-Xmx12G`
2. Kiểm tra heap dump
3. Optimize database queries
4. Giảm cache sizes

### Nếu GC pause quá cao
1. Tune G1GC parameters
2. Giảm heap size nếu có thể
3. Optimize object allocation

### Nếu memory leak
1. Analyze heap dump với Eclipse MAT
2. Kiểm tra static collections
3. Review event listeners cleanup
4. Check database connection leaks

## 📋 Checklist tối ưu

- [ ] JVM heap size phù hợp (4-8GB)
- [ ] G1GC được enable
- [ ] GC logging được bật
- [ ] Database connection pool đủ lớn
- [ ] Lazy cache được enable
- [ ] Memory monitoring được bật
- [ ] Cleanup tasks hoạt động
- [ ] Heap dump on OOM được enable
