package gabriel.eventEngine.interf;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import gabriel.eventEngine.l2j.CallBack;
import gabriel.eventEngine.l2j.IPlayerBase;

public class PlayerBase implements IPlayerBase
{
	private final Map<Integer, PlayerEventInfo> players = new ConcurrentHashMap<>();
	
	public void load()
	{
		CallBack.getInstance().setPlayerBase(this);
	}
	
	@Override
	public PlayerEventInfo getPlayer(int id)
	{
		return this.players.get(Integer.valueOf(id));
	}
	
	@Override
	public Map<Integer, PlayerEventInfo> getPs()
	{
		return this.players;
	}
	
	protected PlayerEventInfo getPlayer(PlayerInstance player)
	{
		return player.getEventInfo();
	}
	
	@Override
	public PlayerEventInfo addInfo(PlayerEventInfo player)
	{
		this.players.put(Integer.valueOf(player.getPlayersId()), player);
		return player;
	}
	
	@Override
	public void eventEnd(PlayerEventInfo player)
	{
		deleteInfo(player.getOwner());
	}
	
	@Override
	public void playerDisconnected(PlayerEventInfo player)
	{
		eventEnd(player);
	}
	
	@Override
	public void deleteInfo(int player)
	{
		this.players.remove(Integer.valueOf(player));
	}
	
	protected void deleteInfo(PlayerInstance player)
	{
		this.players.remove(Integer.valueOf(player.getObjectId()));
	}
	
	public static final PlayerBase getInstance()
	{
		return SingletonHolder._instance;
	}
	
	private static class SingletonHolder
	{
		protected static final PlayerBase _instance = new PlayerBase();
	}
}
