package gabriel.eventEngine.interf;

import club.projectessence.gameserver.model.WorldObject;
import club.projectessence.gameserver.model.actor.Creature;
import club.projectessence.gameserver.model.actor.Npc;
import club.projectessence.gameserver.model.actor.Playable;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;

import club.projectessence.gameserver.model.items.instance.ItemInstance;
import club.projectessence.gameserver.model.skills.Skill;

import gabriel.eventEngine.events.EventGame;
import gabriel.eventEngine.events.GabrielEventsLoader;
import gabriel.eventEngine.events.engine.EventBuffer;
import gabriel.eventEngine.events.engine.EventConfig;
import gabriel.eventEngine.events.engine.EventManagement;
import gabriel.eventEngine.events.engine.EventManager;
import gabriel.eventEngine.events.engine.mini.EventMode;
import gabriel.eventEngine.events.engine.mini.MiniEventManager;
import gabriel.eventEngine.events.engine.mini.features.AbstractFeature;
import gabriel.eventEngine.events.engine.mini.features.EnchantFeature;
import gabriel.eventEngine.interf.callback.HtmlManager;
import gabriel.eventEngine.interf.callback.api.DescriptionLoader;
import gabriel.eventEngine.interf.delegate.CharacterData;
import gabriel.eventEngine.interf.delegate.SkillData;
import gabriel.eventEngine.l2j.CallBack;

public class GabrielEvents
{
	public static final String						_desc				= "HighFive";
	public static final GabrielEventsLoader.Branch	_branch				= GabrielEventsLoader.Branch.Hi5;
	public static final double						_interfaceVersion	= 2.1D;
	public static final boolean						_allowInstances		= true;
	public static final String						_libsFolder			= "../libs/";
	public static final boolean						_limitedHtml		= false;
	
	public static void start()
	{
		GabrielOut.getInstance().load();
		PlayerBase.getInstance().load();
		Values.getInstance().load();
		GabrielEventsLoader.init(_branch, 2.1D, "HighFive", true, "../libs/", false, true);


	}
	
	public static void loadHtmlManager()
	{
		HtmlManager.load();
		DescriptionLoader.load();
	}
	
	public static void serverShutDown()
	{}
	
	public static void onLogin(PlayerInstance player)
	{
		EventBuffer.getInstance().loadPlayer(player.getEventInfo());
		EventManager.getInstance().onPlayerLogin(player.getEventInfo());
	}
	
	public static PlayerEventInfo getPlayer(PlayerInstance player)
	{
		return GabrielEventsLoader.loaded() ? PlayerBase.getInstance().getPlayer(player) : null;
	}
	
	public static boolean isRegistered(PlayerInstance player)
	{
		PlayerEventInfo pi = getPlayer(player);
		return (pi != null && pi.isRegistered());
	}
	
	public static boolean isInEvent(PlayerInstance player)
	{
		PlayerEventInfo pi = getPlayer(player);
		return (pi != null && pi.isInEvent());
	}
	
	public static boolean isInEvent(Creature ch)
	{
		if (ch instanceof Playable)
		{
			return isInEvent(ch.getActingPlayer());
		}
		return EventManager.getInstance().isInEvent(new CharacterData(ch));
	}
	
	public static boolean allowDie(Creature ch, Creature attacker)
	{
		if (isInEvent(ch) && isInEvent(attacker))
		{
			return EventManager.getInstance().allowDie(new CharacterData(ch), new CharacterData(attacker));
		}
		return true;
	}
	
	public static boolean isInMiniEvent(PlayerInstance player)
	{
		PlayerEventInfo pi = getPlayer(player);
		return (pi != null && pi.getActiveGame() != null);
	}
	
	public static boolean isInMainEvent(PlayerInstance player)
	{
		PlayerEventInfo pi = getPlayer(player);
		return (pi != null && pi.getActiveEvent() != null);
	}
	
	public static boolean canShowToVillageWindow(PlayerInstance player)
	{
		PlayerEventInfo pi = getPlayer(player);
		if (pi != null)
		{
			return pi.canShowToVillageWindow();
		}
		return true;
	}
	
	public static boolean canAttack(PlayerInstance player, Creature target)
	{
		PlayerEventInfo pi = getPlayer(player);
		if (pi != null)
		{
			return pi.canAttack(target);
		}
		return true;
	}
	
	public static boolean onAttack(Creature cha, Creature target)
	{
		return EventManager.getInstance().onAttack(new CharacterData(cha), new CharacterData(target));
	}
	
	public static boolean canSupport(PlayerInstance player, Creature target)
	{
		PlayerEventInfo pi = getPlayer(player);
		if (pi != null)
		{
			return pi.canSupport(target);
		}
		return true;
	}
	
	public static boolean canTarget(PlayerInstance player, WorldObject target)
	{
		return true;
	}
	
	public static void onHit(PlayerInstance player, Creature target, int damage, boolean isDOT)
	{
		PlayerEventInfo pi = getPlayer(player);
		if (pi != null)
		{
			pi.onDamageGive(target, damage, isDOT);
		}
	}
	
	public static void onDamageGive(Creature cha, Creature target, int damage, boolean isDOT)
	{
		EventManager.getInstance().onDamageGive(new CharacterData(cha), new CharacterData(target), damage, isDOT);
	}
	
	public static void onKill(PlayerInstance player, Creature target)
	{
		PlayerEventInfo pi = getPlayer(player);
		if (pi != null)
		{
			pi.notifyKill(target);
		}
	}
	
	public static void onDie(PlayerInstance player, Creature killer)
	{
		PlayerEventInfo pi = getPlayer(player);
		if (pi != null)
		{
			pi.notifyDie(killer);
		}
	}
	
	public static boolean onNpcAction(PlayerInstance player, Npc target)
	{
		PlayerEventInfo pi = getPlayer(player);
		if (pi != null)
		{
			return pi.notifyNpcAction(target);
		}
		return false;
	}
	
	public static boolean canUseItem(PlayerInstance player, ItemInstance item)
	{
		PlayerEventInfo pi = getPlayer(player);
		if (pi != null)
		{
			return pi.canUseItem(item);
		}
		return true;
	}
	
	public static void onUseItem(PlayerInstance player, ItemInstance item)
	{
		PlayerEventInfo pi = getPlayer(player);
		if (pi != null)
		{
			pi.notifyItemUse(item);
		}
	}

	public static void onItemEquip(PlayerInstance player, ItemInstance item)
	{
		// Equipment handling removed - antifeed protection issue was the root cause
	}

	public static void onItemUnequip(PlayerInstance player, ItemInstance item)
	{
		// Equipment handling removed - antifeed protection issue was the root cause
	}









	public static boolean onSay(PlayerInstance player, String text, int channel)
	{
		try
		{
			if (text.startsWith("."))
			{
				if (EventManager.getInstance().tryVoicedCommand(player.getEventInfo(), text))
				{
					return false;
				}
				return true;
			}
		}
		catch (Exception exception)
		{}
		PlayerEventInfo pi = getPlayer(player);
		if (pi != null)
		{
			return pi.notifySay(text, channel);
		}
		return true;
	}
	
	public static boolean canUseSkill(PlayerInstance player, Skill skill)
	{
		PlayerEventInfo pi = getPlayer(player);
		if (pi != null)
		{
			return pi.canUseSkill(skill);
		}
		return true;
	}
	
	public static void onUseSkill(PlayerInstance player, Skill skill)
	{
		PlayerEventInfo pi = getPlayer(player);
		if (pi != null)
		{
			pi.onUseSkill(skill);
		}
	}
	
	public static boolean canDestroyItem(PlayerInstance player, ItemInstance item)
	{
		PlayerEventInfo pi = getPlayer(player);
		if (pi != null)
		{
			return pi.canDestroyItem(item);
		}
		return true;
	}
	
	public static boolean canInviteToParty(PlayerInstance player, PlayerInstance target)
	{
		PlayerEventInfo pi = getPlayer(player);
		PlayerEventInfo targetPi = getPlayer(target);
		if (pi != null)
		{
			if (targetPi == null)
			{
				return false;
			}
			return pi.canInviteToParty(pi, targetPi);
		}
		return true;
	}
	
	public static boolean canTransform(PlayerInstance player)
	{
		PlayerEventInfo pi = getPlayer(player);
		if (pi != null)
		{
			return pi.canTransform(pi);
		}
		return true;
	}
	
	public static int allowTransformationSkill(PlayerInstance player, Skill s)
	{
		PlayerEventInfo pi = getPlayer(player);
		if (pi != null)
		{
			return pi.allowTransformationSkill(s);
		}
		return 0;
	}
	
	public static boolean canBeDisarmed(PlayerInstance player)
	{
		PlayerEventInfo pi = getPlayer(player);
		if (pi != null)
		{
			return pi.canBeDisarmed(pi);
		}
		return true;
	}
	
	public static boolean onBypass(PlayerInstance player, String command)
	{
		if (command.startsWith("nxs_"))
		{
			return EventManager.getInstance().onBypass(player.getEventInfo(), command.substring(4));
		}
		return false;
	}
	
	public static void onAdminBypass(PlayerEventInfo player, String command)
	{
		EventManagement.getInstance().onBypass(player, command);
	}
	
	public static boolean canLogout(PlayerInstance player)
	{
		PlayerEventInfo pi = getPlayer(player);
		return (pi == null || !pi.isInEvent());
	}
	
	public static void onLogout(PlayerInstance player)
	{
		PlayerEventInfo pi = getPlayer(player);
		if (pi != null)
		{
			pi.notifyDisconnect();
		}
	}
	
	public static boolean isObserving(PlayerInstance player)
	{
		return player.getEventInfo().isSpectator();
	}
	
	public static void endObserving(PlayerInstance player)
	{
		EventManager.getInstance().removePlayerFromObserverMode(player.getEventInfo());
	}
	
	public static boolean canSaveShortcuts(PlayerInstance activeChar)
	{
		PlayerEventInfo pi = getPlayer(activeChar);
		if (pi != null)
		{
			pi.canSaveShortcuts();
		}
		return true;
	}
	
	public static int getItemAutoEnchantValue(PlayerInstance player, ItemInstance item)
	{
		if (isInEvent(player))
		{
			PlayerEventInfo pi = PlayerBase.getInstance().getPlayer(player);
			MiniEventManager event = pi.getRegisteredMiniEvent();
			if (event == null)
			{
				return 0;
			}
			for (AbstractFeature f : event.getMode().getFeatures())
			{
				if (f.getType() == EventMode.FeatureType.Enchant)
				{
					switch (item.getItem().getType2())
					{
						case 0:
							return ((EnchantFeature) f).getAutoEnchantWeapon();
						case 1:
							return ((EnchantFeature) f).getAutoEnchantArmor();
						case 2:
							return ((EnchantFeature) f).getAutoEnchantJewel();
					}
				}
			}
			return 0;
		}
		return 0;
	}
	
	public static boolean removeCubics()
	{
		return EventConfig.getInstance().getGlobalConfigBoolean("removeCubicsOnDie");
	}
	
	public static boolean gainPvpPointsOnEvents()
	{
		return EventConfig.getInstance().getGlobalConfigBoolean("pvpPointsOnKill");
	}
	
	public static boolean cbBypass(PlayerInstance player, String command)
	{
		PlayerEventInfo pi = getPlayer(player);
		if (pi != null && command != null)
		{
			return EventManager.getInstance().getHtmlManager().onCbBypass(pi, command);
		}
		return false;
	}
	
	public static String consoleCommand(String cmd)
	{
		if (cmd.startsWith("reload_globalconfig"))
		{
			EventConfig.getInstance().loadGlobalConfigs();
			return "Global configs reloaded.";
		}
		return "This command doesn't exist.";
	}
	
	public static boolean adminCommandRequiresConfirm(String cmd)
	{
		if ((cmd.split(" ")).length > 1)
		{
			String command = cmd.split(" ")[1];
			return EventManagement.getInstance().commandRequiresConfirm(command);
		}
		return false;
	}
	
	public static boolean isSkillOffensive(PlayerInstance activeChar, Skill skill)
	{
		PlayerEventInfo pi = getPlayer(activeChar);
		if (pi != null)
		{
			if (pi.isInEvent())
			{
				EventGame game = pi.getEvent();
				int val = game.isSkillOffensive(new SkillData(skill));
				if (val == 1)
				{
					return true;
				}
				if (val == 0)
				{
					return false;
				}
			}
		}
		return skill.isBad();
	}
	
	public static boolean isSkillNeutral(PlayerInstance activeChar, Skill skill)
	{
		PlayerEventInfo pi = getPlayer(activeChar);
		if (pi != null)
		{
			if (pi.isInEvent())
			{
				EventGame game = pi.getEvent();
				return game.isSkillNeutral(new SkillData(skill));
			}
		}
		return false;
	}
}
