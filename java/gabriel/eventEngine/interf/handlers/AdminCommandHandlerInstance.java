package gabriel.eventEngine.interf.handlers;

import club.projectessence.gameserver.handler.IAdminCommandHandler;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import gabriel.eventEngine.interf.PlayerEventInfo;
import gabriel.eventEngine.l2j.handler.GabEventAdminCommand;

public abstract class AdminCommandHandlerInstance implements IAdminCommandHandler, GabEventAdminCommand
{
	@Override
	public abstract boolean useAdminCommand(String paramString, PlayerEventInfo paramPlayerEventInfo);
	
	@Override
	public final boolean useAdminCommand(String command, PlayerInstance player)
	{
		return useAdminCommand(command, player.getEventInfo());
	}
}
