package gabriel.eventEngine.interf;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Iterator;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ScheduledFuture;

import club.projectessence.Config;
import club.projectessence.commons.concurrent.ThreadPool;
import club.projectessence.commons.database.DatabaseFactory;
import club.projectessence.gameserver.ai.CtrlIntention;
import club.projectessence.gameserver.data.xml.ClassListData;
import club.projectessence.gameserver.enums.ChatType;
import club.projectessence.gameserver.enums.ClassId;
import club.projectessence.gameserver.model.Duel;
import club.projectessence.gameserver.model.Location;
import club.projectessence.gameserver.model.Shortcut;
import club.projectessence.gameserver.model.WorldObject;
import club.projectessence.gameserver.model.actor.Creature;
import club.projectessence.gameserver.model.actor.Npc;
import club.projectessence.gameserver.model.actor.Summon;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.model.cubic.CubicInstance;
import club.projectessence.gameserver.model.effects.EffectFlag;
import club.projectessence.gameserver.model.items.instance.ItemInstance;
import club.projectessence.gameserver.model.olympiad.OlympiadManager;
import club.projectessence.gameserver.model.skills.AbnormalVisualEffect;
import club.projectessence.gameserver.model.skills.BuffInfo;
import club.projectessence.gameserver.model.skills.Skill;
import club.projectessence.gameserver.model.skills.SkillCastingType;
import club.projectessence.gameserver.network.serverpackets.ActionFailed;
import club.projectessence.gameserver.network.serverpackets.CreatureSay;
import club.projectessence.gameserver.network.serverpackets.ExShowScreenMessage;
import club.projectessence.gameserver.network.serverpackets.InventoryUpdate;
import club.projectessence.gameserver.network.serverpackets.ItemList;
import club.projectessence.gameserver.network.serverpackets.MagicSkillLaunched;
import club.projectessence.gameserver.network.serverpackets.MagicSkillUse;
import club.projectessence.gameserver.network.serverpackets.NpcHtmlMessage;
import club.projectessence.gameserver.network.serverpackets.SetupGauge;
import club.projectessence.gameserver.network.serverpackets.ShortCutInit;
import club.projectessence.gameserver.network.serverpackets.ShortCutRegister;
import club.projectessence.gameserver.network.serverpackets.SkillCoolTime;
import club.projectessence.gameserver.util.Broadcast;
import gabriel.eventEngine.events.EventGame;
import gabriel.eventEngine.events.engine.EventConfig;
import gabriel.eventEngine.events.engine.EventManager;
import gabriel.eventEngine.events.engine.base.EventPlayerData;
import gabriel.eventEngine.events.engine.base.EventType;
import gabriel.eventEngine.events.engine.base.Loc;
import gabriel.eventEngine.events.engine.base.PvPEventPlayerData;
import gabriel.eventEngine.events.engine.main.events.AbstractMainEvent;
import gabriel.eventEngine.events.engine.mini.MiniEventGame;
import gabriel.eventEngine.events.engine.mini.MiniEventManager;
import gabriel.eventEngine.events.engine.stats.EventStatsManager;
import gabriel.eventEngine.events.engine.team.EventTeam;
import gabriel.eventEngine.interf.delegate.CharacterData;
import gabriel.eventEngine.interf.delegate.ItemData;
import gabriel.eventEngine.interf.delegate.NpcData;
import gabriel.eventEngine.interf.delegate.PartyData;
import gabriel.eventEngine.interf.delegate.ShortCutData;
import gabriel.eventEngine.interf.delegate.SkillData;
import gabriel.eventEngine.l2j.CallBack;
import gabriel.eventEngine.l2j.ClassType;
import gabriel.eventEngine.util.ScreenMessageThrottle;
import gabriel.eventEngine.l2j.IPlayerEventInfo;
import gabriel.eventEngine.l2j.IValues;

public class PlayerEventInfo implements IPlayerEventInfo
{
	public static final boolean		AFK_CHECK_ENABLED	= EventConfig.getInstance().getGlobalConfigBoolean("afkChecksEnabled");
	public static final int			AFK_WARNING_DELAY	= EventConfig.getInstance().getGlobalConfigInt("afkWarningDelay");
	public static final int			AFK_KICK_DELAY		= EventConfig.getInstance().getGlobalConfigInt("afkKickDelay");
	protected final PlayerInstance	_owner;
	private final int				_playersId;
	private boolean					_isInEvent;
	private boolean					_isRegistered;
	private boolean					_isInFFAEvent;
	private boolean					_isSpectator;
	private boolean					_canBuff;
	private boolean					_canParty			= true;
	private boolean					_isSitForced;
	private boolean					_antifeedProtection;
	private boolean					_titleUpdate;
	protected boolean				_disableAfkCheck;
	private int						_origNameColor;
	private Location				_origLoc;
	private String					_origTitle;
	private double					_origHp;
	private double					_origMp;
	private double					_origCp;
	private long					_origSp;
	private EventPlayerData			_eventData;
	private int						_status;
	protected EventGame				_activeEvent;
	private EventTeam				_eventTeam;
	private MiniEventManager		_registeredMiniEvent;
	private EventType				_registeredMainEvent;
	private AfkChecker				_afkChecker;
	private Radar					_radar;
	private boolean					_hasMarkers;
	private final Set<ShortCutData>	_customShortcuts	= ConcurrentHashMap.newKeySet();
	
	public PlayerEventInfo(PlayerInstance owner)
	{
		this._owner = owner;
		this._playersId = owner == null ? -1 : owner.getObjectId();
		this._isRegistered = false;
		this._isInEvent = false;
		this._isInFFAEvent = false;
		this._status = 0;
		this._disableAfkCheck = false;
		this._titleUpdate = true;
		this._hasMarkers = false;
	}
	
	// Method to set _origLoc at the time of registration
	public void setRegistrationLocation()
	{
		if (this._owner != null)
		{
			this._origLoc = new Location(this._owner.getX(), this._owner.getY(), this._owner.getZ(), this._owner.getHeading());
		}
		else
		{
			this._origLoc = new Location(82698, 148638, -3473, 0);
		}
	}
	
	@Override
	public void initOrigInfo()
	{
		this._origNameColor = this._owner.getAppearance().getNameColor();
		this._origTitle = this._owner.getTitle();
		this._origLoc = new Location(this._owner.getX(), this._owner.getY(), this._owner.getZ(), this._owner.getHeading());

		// Backup original HP/MP/CP/SP before event starts
		this._origHp = this._owner.getCurrentHp();
		this._origMp = this._owner.getCurrentMp();
		this._origCp = this._owner.getCurrentCp();
		this._origSp = this._owner.getSp();

		if (this._origLoc == null)
		{
			if (this._owner != null)
			{
				this._origLoc = new Location(this._owner.getX(), this._owner.getY(), this._owner.getZ(), this._owner.getHeading());
			}
			else
			{
				this._origLoc = new Location(82698, 148638, -3473, 0);
			}
		}
	}
	
	@Override
	public void restoreData()
	{
		_owner.getAppearance().setNameColor(_origNameColor);
		_owner.setTitle(_origTitle, true, true);
		_owner.getAppearance().setVisibleTitle(_origTitle);

		// Restore original HP/MP/CP/SP when leaving event
		if (_owner.isDead())
		{
			_owner.doRevive();
		}

		// Restore to original values, not max values
		if (_origHp > 0) {
			_owner.setCurrentHp(Math.min(_origHp, _owner.getMaxHp()));
		} else {
			_owner.setCurrentHp(_owner.getMaxHp());
		}

		if (_origMp > 0) {
			_owner.setCurrentMp(Math.min(_origMp, _owner.getMaxMp()));
		} else {
			_owner.setCurrentMp(_owner.getMaxMp());
		}

		if (_origCp > 0) {
			_owner.setCurrentCp(Math.min(_origCp, _owner.getMaxCp()));
		} else {
			_owner.setCurrentCp(_owner.getMaxCp());
		}

		// Restore original SP
		if (_origSp > 0) {
			_owner.setSp(_origSp);
		}

		// Delay broadcast to prevent packet spam and client crash
		gabriel.eventEngine.l2j.CallBack.getInstance().getOut().scheduleGeneral(() -> {
			if (_owner != null && _owner.isOnline())
			{
				_owner.broadcastTitleInfo();
				this.broadcastUserInfo(); // Sử dụng throttled version
			}
		}, 500);

		clean();
	}
	
	@Override
	public void onEventStart(EventGame event)
	{
		this.initOrigInfo();
		this._isInEvent = true;
		this._activeEvent = event;
		this._eventData = event.createPlayerData(this);
		if (AFK_CHECK_ENABLED)
		{
			this._afkChecker = new AfkChecker(this);
		}
	}
	
	@Override
	public void clean()
	{
		if (this._afkChecker != null)
		{
			this._afkChecker.stop();
		}
		if (this._radar != null)
		{
			this._radar.disable();
		}
		this._isRegistered = false;
		this._isInEvent = false;
		this._isInFFAEvent = false;
		this._registeredMiniEvent = null;
		this._registeredMainEvent = null;
		this._hasMarkers = false;
		this._activeEvent = null;
		this._eventTeam = null;
		this._canParty = true;
		this._eventData = null;
		this._status = 0;
	}
	
	@Override
	public void teleport(Loc loc, int delay, boolean randomOffset, int instanceId)
	{
		PlayerInstance player = this._owner;
		if (player != null)
		{
			for (Map.Entry<Integer, Summon> integerSummonEntry : player.getServitors().entrySet())
			{
				Summon summon = integerSummonEntry.getValue();
				if (summon != null)
				{
					summon.unSummon(player);
				}
			}
			player.abortCast();
			if (player.isInDuel())
			{
				player.setDuelState(Duel.DUELSTATE_INTERRUPTED);
			}
			player.doRevive();
			for (BuffInfo debuff : player.getEffectList().getDebuffs())
			{
				debuff.finishEffects();
			}
			if (player.isSitting())
			{
				player.standUp();
			}
			player.setTarget((WorldObject) null);
			player.teleToLocation(new Location(loc.getX(), loc.getY(), loc.getZ()), randomOffset);
			if (instanceId != -1)
			{
				player.setInstanceById(instanceId);
			}
			player.setCurrentCp(player.getMaxCp());
			player.setCurrentHp(player.getMaxHp());
			player.setCurrentMp(player.getMaxMp());

			// Đảm bảo HP/MP/CP được set đúng với delay nhỏ để tránh bị override
			CallBack.getInstance().getOut().scheduleGeneral(() -> {
				if (player.isOnline() && !player.isDead()) {
					player.setCurrentHp(player.getMaxHp());
					player.setCurrentMp(player.getMaxMp());
					player.setCurrentCp(player.getMaxCp());
					// Chỉ gọi 1 lần broadcastStatusUpdate trong scheduled task
					player.broadcastStatusUpdate();
				}
			}, 500);

			// Bỏ duplicate broadcastStatusUpdate() để tránh packet spam
			// player.broadcastStatusUpdate();
			this.broadcastUserInfo(); // Sử dụng throttled version
		}
	}
	
	@Override
	public void teleToLocation(Loc loc, boolean randomOffset)
	{
		this._owner.teleToLocation(new Location(loc.getX(), loc.getY(), loc.getZ()), randomOffset);
	}
	
	@Override
	public void teleToLocation(int x, int y, int z, boolean randomOffset)
	{
		this._owner.teleToLocation(new Location(x, y, z), randomOffset);
	}
	
	@Override
	public void teleToLocation(int x, int y, int z, int heading, boolean randomOffset)
	{
		this._owner.teleToLocation(x, y, z, heading, randomOffset);
	}
	
	@Override
	public void setXYZInvisible(int x, int y, int z)
	{
		this._owner.setXYZInvisible(x, y, z);
	}
	
	@Override
	public void setFame(int count)
	{
		this._owner.setFame(count);
	}
	
	@Override
	public int getFame()
	{
		return this._owner.getFame();
	}
	
	protected void notifyKill(Creature target)
	{
		if ((this._activeEvent != null) && !this._isSpectator)
		{
			this._activeEvent.onKill(this, new CharacterData(target));
		}
	}
	
	protected void notifyDie(Creature killer)
	{
		if ((this._activeEvent != null) && !this._isSpectator)
		{
			this._activeEvent.onDie(this, new CharacterData(killer));
		}
	}
	
	protected void notifyDisconnect()
	{
		if ((this._activeEvent != null) && !this._isSpectator)
		{
			this._activeEvent.onDisconnect(this);
		}
		if (this._registeredMainEvent != null)
		{
			EventManager.getInstance().getMainEventManager().unregisterPlayer(this, true);
		}
		else if (this._registeredMiniEvent != null)
		{}
		EventStatsManager.getInstance().onDisconnect(this);
		PlayerBase.getInstance().eventEnd(this);
	}
	
	protected boolean canAttack(Creature target)
	{
		return (this._activeEvent != null) && !this._isSpectator ? this._activeEvent.canAttack(this, new CharacterData(target)) : true;
	}
	
	protected boolean canSupport(Creature target)
	{
		return (this._activeEvent != null) && !this._isSpectator ? this._activeEvent.canSupport(this, new CharacterData(target)) : true;
	}
	
	public void onAction()
	{
		if (this._afkChecker != null)
		{
			this._afkChecker.onAction();
		}
	}
	
	protected void onDamageGive(Creature target, int ammount, boolean isDOT)
	{
		if ((this._activeEvent != null) && !this._isSpectator)
		{
			this._activeEvent.onDamageGive(this.getCharacterData(), new CharacterData(target), ammount, isDOT);
		}
	}
	
	protected boolean notifySay(String text, int channel)
	{
		return this._activeEvent != null ? this._activeEvent.onSay(this, text, channel) : true;
	}
	
	protected boolean notifyNpcAction(Npc npc)
	{
		if (this._isSpectator)
		{
			return true;
		}
		else if (EventManager.getInstance().showNpcHtml(this, new NpcData(npc)))
		{
			return true;
		}
		else
		{
			return this._activeEvent != null ? this._activeEvent.onNpcAction(this, new NpcData(npc)) : false;
		}
	}
	
	protected boolean canUseItem(ItemInstance item)
	{
		if (this._isSpectator)
		{
			return false;
		}
		else
		{
			return this._activeEvent != null ? this._activeEvent.canUseItem(this, new ItemData(item)) : true;
		}
	}
	
	protected void notifyItemUse(ItemInstance item)
	{
		if (this._activeEvent != null)
		{
			this._activeEvent.onItemUse(this, new ItemData(item));
		}
	}
	
	protected boolean canUseSkill(Skill skill)
	{
		if (this._isSpectator)
		{
			return false;
		}
		else
		{
			return this._activeEvent != null ? this._activeEvent.canUseSkill(this, new SkillData(skill)) : true;
		}
	}
	
	protected void onUseSkill(Skill skill)
	{
		if (this._activeEvent != null)
		{
			this._activeEvent.onSkillUse(this, new SkillData(skill));
		}
	}
	
	protected boolean canShowToVillageWindow()
	{
		return false;
	}
	
	protected boolean canDestroyItem(ItemInstance item)
	{
		return this._activeEvent != null ? this._activeEvent.canDestroyItem(this, new ItemData(item)) : true;
	}
	
	protected boolean canInviteToParty(PlayerEventInfo player, PlayerEventInfo target)
	{
		return this._activeEvent != null ? this._activeEvent.canInviteToParty(player, target) : true;
	}
	
	protected boolean canTransform(PlayerEventInfo player)
	{
		return this._activeEvent != null ? this._activeEvent.canTransform(player) : true;
	}
	
	protected boolean canBeDisarmed(PlayerEventInfo player)
	{
		return this._activeEvent != null ? this._activeEvent.canBeDisarmed(player) : true;
	}
	
	protected int allowTransformationSkill(Skill s)
	{
		return this._activeEvent != null ? this._activeEvent.allowTransformationSkill(this, new SkillData(s)) : 0;
	}
	
	protected boolean canSaveShortcuts()
	{
		return this._activeEvent != null ? this._activeEvent.canSaveShortcuts(this) : true;
	}
	
	@Override
	public void setInstanceId(int id)
	{
		this._owner.setInstanceById(id);
	}
	
	@Override
	public void sendPacket(String html)
	{
		this.sendHtmlText(html);
	}
	
	@Override
	public void screenMessage(String message, String name, boolean special)
	{
		if (this._owner != null && ScreenMessageThrottle.canSendMessage(this._owner))
		{
			// Use throttle to prevent screen message spam that can cause client crashes
			this._owner.sendPacket((special ? new ExShowScreenMessage(message, 5000) : new CreatureSay(this._owner, ChatType.PARTYROOM_COMMANDER, name, message, 0)));
		}
	}
	
	@Override
	public void creatureSay(String paramString1, String paramString2, int paramInt)
	{}
	
	public void creatureSay(String message, String announcer, ChatType channel)
	{
		if (this._owner != null)
		{
			this._owner.sendPacket(new CreatureSay(this._owner, channel, announcer, message, 0));
		}
	}
	
	@Override
	public void sendMessage(String message)
	{
		if (this._owner != null)
		{
			this._owner.sendMessage(message);
		}
	}
	
	@Override
	public void sendEventScoreBar(String text)
	{
		if (this._owner != null)
		{
			// Bỏ throttle cho scorebar để hiển thị liên tục
			this._owner.sendPacket(new ExShowScreenMessage(text, 3, 2000));
		}
	}
	
	private long _lastUserInfoBroadcast = 0;
	private long _lastTitleInfoBroadcast = 0;
	private static final long USER_INFO_THROTTLE_DELAY = 500; // 500ms throttle
	private static final long TITLE_INFO_THROTTLE_DELAY = 1000; // 1000ms throttle cho title để tránh vấn đề stat tăng đột biến

	@Override
	public void broadcastUserInfo()
	{
		if (this._owner != null)
		{
			// Throttle broadcastUserInfo để tránh spam packets gây crash client
			long currentTime = System.currentTimeMillis();
			if (currentTime - _lastUserInfoBroadcast >= USER_INFO_THROTTLE_DELAY)
			{
				this._owner.broadcastUserInfo();
				_lastUserInfoBroadcast = currentTime;
			}
		}
	}

	@Override
	public void broadcastTitleInfo()
	{
		if (this._owner != null)
		{
			// Throttle broadcastTitleInfo để tránh vấn đề stat tăng đột biến cho admin trong Deathmatch/LMS
			long currentTime = System.currentTimeMillis();
			if (currentTime - _lastTitleInfoBroadcast >= TITLE_INFO_THROTTLE_DELAY)
			{
				this._owner.broadcastTitleInfo();
				_lastTitleInfoBroadcast = currentTime;
			}
		}
	}
	
	@Override
	public void sendSkillList()
	{
		this._owner.sendSkillList();
	}
	
	@Override
	public void transform(int transformId)
	{
		if (this._owner != null)
		{
			this._owner.transform(transformId, false);
		}
	}
	
	@Override
	public boolean isTransformed()
	{
		return (this._owner != null) && this._owner.isTransformed();
	}
	
	@Override
	public void untransform(boolean removeEffects)
	{
		if ((this._owner != null) && this._owner.isTransformed())
		{
			this._owner.stopTransformation(removeEffects);
		}
	}
	
	@Override
	public ItemData addItem(int id, int ammount, boolean msg)
	{
		return new ItemData(this._owner.addItem("Event Reward", id, ammount, (WorldObject) null, msg));
	}
	
	@Override
	public void addExpAndSp(long exp, int sp)
	{
		this._owner.addExpAndSp(exp, sp);
	}
	
	@Override
	public void doDie()
	{
		this._owner.doDie(this._owner);
	}
	
	@Override
	public void doDie(CharacterData killer)
	{
		this._owner.doDie(killer != null ? killer.getOwner() : null);
	}
	
	@Override
	public ItemData[] getItems()
	{
		List<ItemData> items = new LinkedList<>();
		List<ItemInstance> var2 = new ArrayList<>(this._owner.getInventory().getItems());
		int var3 = var2.size();
		for (int var4 = 0; var4 < var3; ++var4)
		{
			ItemInstance item = var2.get(var4);
			items.add(new ItemData(item));
		}
		return items.toArray(new ItemData[items.size()]);
	}
	
	@Override
	public void getPetSkillEffects(int skillId, int level)
	{
		if (this._owner.getServitors().size() > 0)
		{
			Skill skill = club.projectessence.gameserver.data.xml.SkillData.getInstance().getSkill(skillId, level);
			if (skill != null)
			{
				for (Map.Entry<Integer, Summon> integerSummonEntry : this._owner.getServitors().entrySet())
				{
					skill.applyEffects(integerSummonEntry.getValue(), integerSummonEntry.getValue());
				}
			}
		}
	}
	
	@Override
	public void getSkillEffects(int skillId, int level)
	{
		Skill skill = club.projectessence.gameserver.data.xml.SkillData.getInstance().getSkill(skillId, level);
		if (skill != null)
		{
			skill.applyEffects(this._owner, this._owner);
		}
	}
	
	@Override
	public void addSkill(SkillData skill, boolean store)
	{
		this.getOwner().addSkill(club.projectessence.gameserver.data.xml.SkillData.getInstance().getSkill(skill.getId(), skill.getLevel()), store);
	}
	
	@Override
	public void removeSkill(int id)
	{
		this.getOwner().removeSkill(id);
	}
	
	@Override
	public void removeCubics()
	{
		if (!this._owner.getCubics().isEmpty())
		{
			for (CubicInstance cubic : this._owner.getCubics().values())
			{
				cubic.deactivate();
			}
			this._owner.getCubics().clear();
		}
	}
	
	@Override
	public void removeSummon()
	{
		for (Map.Entry<Integer, Summon> integerSummonEntry : this._owner.getServitors().entrySet())
		{
			Summon summon = integerSummonEntry.getValue();
			if (summon != null)
			{
				summon.unSummon(this._owner);
			}
		}
	}
	
	@Override
	public boolean hasPet()
	{
		return this._owner.getServitors().size() > 0;
	}
	
	@Override
	public void removeBuffsFromPet()
	{
		for (Map.Entry<Integer, Summon> integerSummonEntry : this._owner.getServitors().entrySet())
		{
			Summon summon = integerSummonEntry.getValue();
			if (summon != null)
			{
				summon.stopAllEffects();
			}
		}
	}
	
	@Override
	public void removeBuffs()
	{
		if (this._owner != null)
		{
			this._owner.stopAllEffects();
		}
	}
	
	@Override
	public int getBuffsCount()
	{
		return this._owner.getBuffCount();
	}
	
	@Override
	public int getDancesCount()
	{
		return this._owner.getDanceCount();
	}
	
	@Override
	public int getPetBuffCount()
	{
		for (Map.Entry<Integer, Summon> integerSummonEntry : this._owner.getServitors().entrySet())
		{
			Summon summon = integerSummonEntry.getValue();
			if (summon != null)
			{
				return summon.getBuffCount();
			}
		}
		return 0;
	}
	
	@Override
	public int getPetDanceCount()
	{
		for (Map.Entry<Integer, Summon> integerSummonEntry : this._owner.getServitors().entrySet())
		{
			Summon summon = integerSummonEntry.getValue();
			if (summon != null)
			{
				return summon.getDanceCount();
			}
		}
		return 0;
	}
	
	@Override
	public int getMaxBuffCount()
	{
		return Config.BUFFS_MAX_AMOUNT;
	}
	
	@Override
	public int getMaxDanceCount()
	{
		return Config.DANCES_MAX_AMOUNT;
	}
	
	@Override
	public void removeBuff(int id)
	{
		if (this._owner != null)
		{
			this._owner.stopSkillEffects(true, id);
		}
	}
	
	@Override
	public void abortCasting()
	{
		if (this._owner.isCastingNow())
		{
			this._owner.abortCast();
		}
		if (this._owner.isAttackingNow())
		{
			this._owner.abortAttack();
		}
	}
	
	@Override
	public void setVisible()
	{
		this._owner.setInvisible(false);
	}
	
	@Override
	public void rebuffPlayer()
	{}
	
	@Override
	public void enableAllSkills()
	{
		Iterator<Skill> var1 = this._owner.getAllSkills().iterator();
		while (var1.hasNext())
		{
			Skill skill = (Skill) var1.next();
			if (skill.getReuseDelay() <= 900000)
			{
				this._owner.enableSkill(skill);
			}
		}
		this._owner.sendPacket(new SkillCoolTime(this._owner));
	}
	
	@Override
	public void sendSetupGauge(int time)
	{
		SetupGauge sg = new SetupGauge(this._owner.getObjectId(), 0, time);
		this._owner.sendPacket(sg);
	}
	
	@Override
	public void root()
	{
		this._owner.setImmobilized(true);
		this._owner.getEffectList().startAbnormalVisualEffect(AbnormalVisualEffect.STEALTH);
	}
	
	@Override
	public void unroot()
	{
		if (this._owner.isImmobilized())
		{
			this._owner.setImmobilized(false);
		}
		this._owner.getEffectList().stopAbnormalVisualEffect(AbnormalVisualEffect.STEALTH);
	}
	
	@Override
	public void paralizeEffect(boolean b)
	{
		if (b)
		{
			this._owner.getEffectList().startAbnormalVisualEffect(AbnormalVisualEffect.HOLD_LIGHTING);
		}
		else
		{
			this._owner.getEffectList().stopAbnormalVisualEffect(AbnormalVisualEffect.HOLD_LIGHTING);
		}
	}
	
	@Override
	public void setIsParalyzed(boolean b)
	{
		this._owner.setImmobilized(b);
	}
	
	@Override
	public void setIsInvul(boolean b)
	{
		this._owner.setInvul(b);
	}
	
	@Override
	public void setCanInviteToParty(boolean b)
	{
		this._canParty = b;
	}
	
	@Override
	public boolean canInviteToParty()
	{
		return this._canParty;
	}
	
	@Override
	public void setIsSitForced(boolean b)
	{
		this._isSitForced = b;
	}
	
	@Override
	public boolean isSitForced()
	{
		return this._isSitForced;
	}
	
	@Override
	public boolean hasSummon()
	{
		return this._owner.hasSummon();
	}
	
	@Override
	public Summon getSummon()
	{
		return this._owner.getAnyServitor();
	}
	
	@Override
	public void showEventEscapeEffect()
	{
		this._owner.getAI().setIntention(CtrlIntention.AI_INTENTION_IDLE);
		this._owner.setTarget(this._owner);
		this._owner.disableAllSkills();
		MagicSkillUse msk = new MagicSkillUse(this._owner, 1050, 1, 10000, 0);
		Broadcast.toSelfAndKnownPlayersInRadius(this._owner, msk, 810000);
		SetupGauge sg = new SetupGauge(this._owner.getObjectId(), 0, 10000);
		this._owner.sendPacket(sg);
		// this._owner.setFor(GameTimeController.getInstance().getGameTicks() + 100);
	}
	
	@Override
	public void startAntifeedProtection(boolean broadcast)
	{
		this._owner.startAntifeedProtection(true, false); // Don't broadcast from PlayerInstance
		this._antifeedProtection = true;
		// Use throttled broadcast to prevent stat issues
		if (broadcast)
		{
			gabriel.eventEngine.l2j.CallBack.getInstance().getOut().scheduleGeneral(() -> {
				if (this._owner != null && this._owner.isOnline()) {
					this.broadcastUserInfo();
				}
			}, 1500); // 1.5 second delay
		}
	}
	
	@Override
	public void stopAntifeedProtection(boolean broadcast)
	{
		this._owner.startAntifeedProtection(false, false); // Don't broadcast from PlayerInstance
		this._antifeedProtection = false;
		// Use throttled broadcast to prevent stat issues
		if (broadcast)
		{
			gabriel.eventEngine.l2j.CallBack.getInstance().getOut().scheduleGeneral(() -> {
				if (this._owner != null && this._owner.isOnline()) {
					this.broadcastUserInfo();
				}
			}, 1500); // 1.5 second delay
		}
	}
	
	@Override
	public boolean hasAntifeedProtection()
	{
		return this._antifeedProtection;
	}
	
	@Override
	public void broadcastSkillUse(CharacterData owner, CharacterData target, int skillId, int level)
	{
		Skill skill = club.projectessence.gameserver.data.xml.SkillData.getInstance().getSkill(skillId, level);
		if (skill != null)
		{
			this.getOwner().broadcastPacket(new MagicSkillUse(owner == null ? this.getOwner() : owner.getOwner(), target == null ? this.getOwner() : target.getOwner(), skill.getId(), skill.getLevel(), skill.getHitTime(), skill.getReuseDelay()));
		}
	}
	
	@Override
	public void broadcastSkillLaunched(CharacterData owner, CharacterData target, int skillId, int level)
	{
		Skill skill = club.projectessence.gameserver.data.xml.SkillData.getInstance().getSkill(skillId, level);
		if (skill != null)
		{
			this.getOwner().broadcastPacket(new MagicSkillLaunched(owner == null ? this.getOwner() : owner.getOwner(), skill.getId(), skill.getLevel(), SkillCastingType.NORMAL, new WorldObject[]
			{
				target.getOwner()
			}));
		}
	}
	
	@Override
	public void enterObserverMode(int x, int y, int z)
	{
		this._owner.enterOlympiadObserverMode(new Location(x, y, z), 0);
	}
	
	@Override
	public void removeObserveMode()
	{
		this.setIsSpectator(false);
		this.setActiveGame((MiniEventGame) null);
		this._owner.leaveOlympiadObserverMode();
		this._owner.setInstanceById(0);
		this._owner.teleToLocation(new Location(this.getOrigLoc().getX(), this.getOrigLoc().getY(), this.getOrigLoc().getZ()), true);
	}
	
	@Override
	public void sendStaticPacket()
	{
		this._owner.sendPacket(ActionFailed.STATIC_PACKET);
	}
	
	@Override
	public void sendHtmlText(String text)
	{
		NpcHtmlMessage msg = new NpcHtmlMessage();
		msg.setHtml(text);
		this._owner.sendPacket(msg);
	}
	
	@Override
	public void sendHtmlPage(String path)
	{
		NpcHtmlMessage html = new NpcHtmlMessage();
		html.setFile(null, path);
		this._owner.sendPacket(html);
		this.sendStaticPacket();
	}
	
	@Override
	public void startAbnormalEffect(int mask)
	{
		IValues val = CallBack.getInstance().getValues();
		Optional<AbnormalVisualEffect> eff = Arrays.stream(AbnormalVisualEffect.values()).filter(e -> e.getClientId() == mask).findFirst();
		if (!eff.isPresent())
		{
			return;
		}
		if (mask == val.ABNORMAL_S_INVINCIBLE())
		{
			this._owner.getEffectList().startAbnormalVisualEffect(eff.get());
		}
		else
		{
			this._owner.getEffectList().startAbnormalVisualEffect(eff.get());
		}
	}
	
	@Override
	public void stopAbnormalEffect(int mask)
	{
		IValues val = CallBack.getInstance().getValues();
		Optional<AbnormalVisualEffect> eff = Arrays.stream(AbnormalVisualEffect.values()).filter(e -> e.getClientId() == mask).findFirst();
		if (!eff.isPresent())
		{
			return;
		}
		if (mask == val.ABNORMAL_S_INVINCIBLE())
		{
			this._owner.getEffectList().stopAbnormalVisualEffect(eff.get());
		}
		else
		{
			this._owner.getEffectList().stopAbnormalVisualEffect(eff.get());
		}
	}
	
	@Override
	public void removeOriginalShortcuts()
	{
		if (this._owner != null)
		{
			for (Shortcut allShortCut : this._owner.getAllShortCuts())
			{
				this._owner.deleteShortCut(allShortCut.getId(), allShortCut.getPage());
			}
			this._owner.sendPacket(new ShortCutInit(this._owner));
		}
	}
	
	@Override
	public void restoreOriginalShortcuts()
	{
		if (this._owner != null)
		{
			this._owner.restoreShortCuts();
			this._owner.sendPacket(new ShortCutInit(this._owner));
		}
	}
	
	@Override
	public void removeCustomShortcuts()
	{
		if (this._owner != null)
		{
			Iterator<ShortCutData> var1 = this._customShortcuts.iterator();
			while (var1.hasNext())
			{
				ShortCutData sh = (ShortCutData) var1.next();
				this._owner.deleteShortCut(sh.getSlot(), sh.getPage());
			}
			this._customShortcuts.clear();
		}
	}
	
	@Override
	public void registerShortcut(ShortCutData shortcut, boolean eventShortcut)
	{
		if (eventShortcut)
		{
			this._customShortcuts.add(shortcut);
		}
		if (this._owner != null)
		{
			Shortcut sh = new Shortcut(shortcut.getSlot(), shortcut.getPage(), shortcut.getType(), shortcut.getId(), shortcut.getLevel(), 0, shortcut.getCharacterType());
			this._owner.sendPacket(new ShortCutRegister(sh));
			this._owner.registerShortCut(sh);
		}
	}
	
	@Override
	public void removeShortCut(ShortCutData shortcut, boolean eventShortcut)
	{
		if (eventShortcut && this._customShortcuts.contains(shortcut))
		{
			this._customShortcuts.remove(shortcut);
		}
		if (this._owner != null)
		{
			this._owner.deleteShortCut(shortcut.getSlot(), shortcut.getPage());
		}
	}
	
	@Override
	public ShortCutData createItemShortcut(int slotId, int pageId, ItemData item)
	{
		ShortCutData shortcut = new ShortCutData(slotId, pageId, Values.getInstance().TYPE_ITEM(), item.getObjectId(), 0, 1);
		return shortcut;
	}
	
	@Override
	public ShortCutData createSkillShortcut(int slotId, int pageId, SkillData skill)
	{
		ShortCutData shortcut = new ShortCutData(slotId, pageId, Values.getInstance().TYPE_SKILL(), skill.getId(), skill.getLevel(), 1);
		return shortcut;
	}
	
	@Override
	public ShortCutData createActionShortcut(int slotId, int pageId, int actionId)
	{
		ShortCutData shortcut = new ShortCutData(slotId, pageId, Values.getInstance().TYPE_ACTION(), actionId, 0, 1);
		return shortcut;
	}
	
	public PlayerInstance getOwner()
	{
		return this._owner;
	}
	
	@Override
	public boolean isOnline()
	{
		return this.isOnline(false);
	}
	
	@Override
	public boolean isOnline(boolean strict)
	{
		if (!strict)
		{
			return this._owner != null;
		}
		else
		{
			return (this._owner != null) && this._owner.isOnline();
		}
	}
	
	@Override
	public boolean isDead()
	{
		return this._owner.isDead();
	}
	
	@Override
	public boolean isVisible()
	{
		return !this._owner.isInvisible();
	}
	
	@Override
	public boolean isHero()
	{
		return this._owner.isHero();
	}
	
	@Override
	public void doRevive()
	{
		this._owner.doRevive();
	}
	
	@Override
	public CharacterData getTarget()
	{
		return (this._owner.getTarget() != null) && (this._owner.getTarget() instanceof Creature) ? new CharacterData((Creature) this._owner.getTarget()) : null;
	}
	
	@Override
	public String getPlayersName()
	{
		return this._owner != null ? this._owner.getName() : "";
	}
	
	@Override
	public int getLevel()
	{
		return this._owner != null ? this._owner.getLevel() : 0;
	}
	
	@Override
	public int getPvpKills()
	{
		return this._owner.getPvpKills();
	}
	
	@Override
	public int getPkKills()
	{
		return this._owner.getPkKills();
	}
	
	@Override
	public int getMaxHp()
	{
		return this._owner.getMaxHp();
	}
	
	@Override
	public int getMaxCp()
	{
		return this._owner.getMaxCp();
	}
	
	@Override
	public int getMaxMp()
	{
		return this._owner.getMaxMp();
	}
	
	@Override
	public void setCurrentHp(int hp)
	{
		this._owner.setCurrentHp(hp);
	}
	
	@Override
	public void setCurrentCp(int cp)
	{
		this._owner.setCurrentCp(cp);
	}
	
	@Override
	public void setCurrentMp(int mp)
	{
		this._owner.setCurrentMp(mp);
	}
	
	@Override
	public double getCurrentHp()
	{
		return this._owner.getCurrentHp();
	}
	
	@Override
	public double getCurrentCp()
	{
		return this._owner.getCurrentCp();
	}
	
	@Override
	public double getCurrentMp()
	{
		return this._owner.getCurrentMp();
	}
	
	@Override
	public void healPet()
	{
		for (Map.Entry<Integer, Summon> integerSummonEntry : this._owner.getServitors().entrySet())
		{
			Summon s = integerSummonEntry.getValue();
			s.setCurrentHp(s.getMaxHp());
			s.setCurrentMp(s.getMaxMp());
		}
	}
	
	@Override
	public void setTitle(String title, boolean updateVisible)
	{
		this._owner.setTitle(title, true, false);
		if (updateVisible)
		{
			this._owner.getAppearance().setVisibleTitle(this._owner.getTitle());
		}
	}
	
	@Override
	public boolean isMageClass()
	{
		return this._owner.isMageClass();
	}
	
	@Override
	public boolean isSylphClass()
	{
		return this._owner.isSyplh();
	}
	
	@Override
	public boolean isDeathKnight()
	{
		return this._owner.isDeathKnight();
	}
	
	@Override
	public boolean isVanguard()
	{
		return this._owner.isVanguard();
	}
	
	@Override
	public int getClassIndex()
	{
		return this._owner != null ? this._owner.getClassIndex() : 0;
	}
	
	@Override
	public int getActiveClass()
	{
		return this._owner != null ? this._owner.getActiveClass() : 0;
	}
	
	@Override
	public String getClassName()
	{
		return ClassListData.getInstance().getClass(this._owner.getClassId()).getClassName();
	}
	
	@Override
	public PartyData getParty()
	{
		return this._owner.getParty() == null ? null : new PartyData(this._owner.getParty());
	}
	
	@Override
	public boolean isFighter()
	{
		return ClassId.getClassId(this._owner.getActiveClass()).isOfType(ClassType.Fighter) && !this.isTank();
	}
	
	@Override
	public boolean isPriest()
	{
		return ClassId.getClassId(this._owner.getActiveClass()).isOfType(ClassType.Priest);
	}
	
	@Override
	public boolean isMystic()
	{
		return ClassId.getClassId(this._owner.getActiveClass()).isOfType(ClassType.Mystic) && !this.isDominator();
	}
	
	@Override
	public boolean isDominator()
	{
		return ClassId.getClassId(this._owner.getActiveClass()).isOfType(ClassType.Mystic) && (this._owner.getActiveClass() == 115);
	}
	
	@Override
	public boolean isTank()
	{
		return ClassId.getClassId(this._owner.getActiveClass()).isOfType(ClassType.Fighter) && ((this._owner.getActiveClass() == 90) || (this._owner.getActiveClass() == 91) || (this._owner.getActiveClass() == 99) || (this._owner.getActiveClass() == 106));
	}
	
	@Override
	public gabriel.eventEngine.l2j.ClassType getClassType()
	{
		if (isFighter())
		{
			return gabriel.eventEngine.l2j.ClassType.Fighter;
		}
		else if (isTank())
		{
			return gabriel.eventEngine.l2j.ClassType.Tank;
		}
		else if (isMystic())
		{
			return gabriel.eventEngine.l2j.ClassType.Mystic;
		}
		else if (isPriest())
		{
			return gabriel.eventEngine.l2j.ClassType.Priest;
		}
		else
		{
			return isDominator() ? gabriel.eventEngine.l2j.ClassType.Dominator : gabriel.eventEngine.l2j.ClassType.Fighter;
		}
	}
	
	@Override
	public int getX()
	{
		return this._owner.getX();
	}
	
	@Override
	public int getY()
	{
		return this._owner.getY();
	}
	
	@Override
	public int getZ()
	{
		return this._owner.getZ();
	}
	
	@Override
	public int getHeading()
	{
		return this._owner.getHeading();
	}
	
	@Override
	public int getInstanceId()
	{
		return this._owner.getInstanceId();
	}
	
	@Override
	public int getClanId()
	{
		return this._owner.getClanId();
	}
	
	@Override
	public boolean isGM()
	{
		return this._owner.isGM();
	}
	
	@Override
	public String getIp()
	{
		return this._owner.getClient().getHostAddress();
	}
	
	@Override
	public boolean isInJail()
	{
		return this._owner.isJailed();
	}
	
	@Override
	public boolean isInSiege()
	{
		return this._owner.isInSiege();
	}
	
	@Override
	public boolean isInDuel()
	{
		return this._owner.isInDuel();
	}
	
	@Override
	public boolean isInOlympiad()
	{
		return this._owner.isInOlympiadMode();
	}
	
	@Override
	public boolean isInOlympiadMode()
	{
		return this._owner.isInOlympiadMode();
	}
	
	@Override
	public int getKarma()
	{
		return this._owner.getReputation();
	}
	
	@Override
	public boolean isCursedWeaponEquipped()
	{
		return this._owner.isCursedWeaponEquipped();
	}
	
	@Override
	public boolean isImmobilized()
	{
		return this._owner.isImmobilized();
	}
	
	@Override
	public boolean isParalyzed()
	{
		return this._owner.hasBlockActions();
	}
	
	@Override
	public boolean isAfraid()
	{
		return this._owner.isAffected(EffectFlag.FEAR);
	}
	
	@Override
	public boolean isOlympiadRegistered()
	{
		return OlympiadManager.getInstance().isRegistered(this._owner);
	}
	
	@Override
	public boolean isInStoreMode()
	{
		return this._owner.isInStoreMode();
	}
	
	@Override
	public void sitDown()
	{
		if (this._owner != null)
		{
			this.setIsSitForced(true);
			this._owner.sitDown();
		}
	}
	
	@Override
	public void standUp()
	{
		if (this._owner != null)
		{
			this.setIsSitForced(false);
			this._owner.standUp();
		}
	}
	
	@Override
	public List<SkillData> getSkills()
	{
		List<SkillData> list = new LinkedList<>();
		Iterator<Skill> var2 = this.getOwner().getAllSkills().iterator();
		while (var2.hasNext())
		{
			Skill skill = (Skill) var2.next();
			list.add(new SkillData(skill));
		}
		return list;
	}
	
	@Override
	public List<Integer> getSkillIds()
	{
		List<Integer> list = new LinkedList<>();
		Iterator<Skill> var2 = this.getOwner().getAllSkills().iterator();
		while (var2.hasNext())
		{
			Skill skill = (Skill) var2.next();
			list.add(skill.getId());
		}
		return list;
	}
	
	@Override
	public double getPlanDistanceSq(int targetX, int targetY)
	{
		return this._owner.getPlanDistanceSq(targetX, targetY);
	}
	
	@Override
	public double getDistanceSq(int targetX, int targetY, int targetZ)
	{
		return this._owner.getDistanceSq(targetX, targetY, targetZ);
	}
	
	@Override
	public boolean isRegistered()
	{
		return this._isRegistered;
	}
	
	@Override
	public boolean isInEvent()
	{
		return this._isInEvent;
	}
	
	@Override
	public EventPlayerData getEventData()
	{
		return this._eventData;
	}
	
	@Override
	public void setNameColor(int color)
	{
		this._owner.getAppearance().setNameColor(color);
		// Sử dụng throttled broadcastUserInfo để tránh crash client
		this.broadcastUserInfo();
	}
	
	@Override
	public void setCanBuff(boolean canBuff)
	{
		this._canBuff = canBuff;
	}
	
	@Override
	public boolean canBuff()
	{
		return this._canBuff;
	}
	
	@Override
	public int getPlayersId()
	{
		return this._playersId;
	}
	
	@Override
	public int getKills()
	{
		return this._eventData instanceof PvPEventPlayerData ? ((PvPEventPlayerData) this._eventData).getKills() : 0;
	}
	
	@Override
	public int getDeaths()
	{
		return this._eventData instanceof PvPEventPlayerData ? ((PvPEventPlayerData) this._eventData).getDeaths() : 0;
	}
	
	@Override
	public int getScore()
	{
		return this._eventData.getScore();
	}
	
	@Override
	public int getStatus()
	{
		return this._status;
	}
	
	@Override
	public void raiseKills(int count)
	{
		if (this._eventData instanceof PvPEventPlayerData)
		{
			((PvPEventPlayerData) this._eventData).raiseKills(count);
		}
	}
	
	@Override
	public void raiseDeaths(int count)
	{
		if (this._eventData instanceof PvPEventPlayerData)
		{
			((PvPEventPlayerData) this._eventData).raiseDeaths(count);
		}
	}
	
	@Override
	public void raiseScore(int count)
	{
		this._eventData.raiseScore(count);
	}
	
	@Override
	public void setScore(int count)
	{
		this._eventData.setScore(count);
	}
	
	@Override
	public void setStatus(int count)
	{
		this._status = count;
	}
	
	@Override
	public void setKills(int count)
	{
		if (this._eventData instanceof PvPEventPlayerData)
		{
			((PvPEventPlayerData) this._eventData).setKills(count);
		}
	}
	
	@Override
	public void setDeaths(int count)
	{
		if (this._eventData instanceof PvPEventPlayerData)
		{
			((PvPEventPlayerData) this._eventData).setDeaths(count);
		}
	}
	
	@Override
	public boolean isInFFAEvent()
	{
		return this._isInFFAEvent;
	}
	
	@Override
	public void setIsRegisteredToMiniEvent(boolean b, MiniEventManager minievent)
	{
		this._isRegistered = b;
		this._registeredMiniEvent = minievent;
	}
	
	@Override
	public MiniEventManager getRegisteredMiniEvent()
	{
		return this._registeredMiniEvent;
	}
	
	@Override
	public void setIsRegisteredToMainEvent(boolean b, EventType event)
	{
		this._isRegistered = b;
		this._registeredMainEvent = event;
		// Set registration location when player registers for a main event
		setRegistrationLocation();
	}
	
	@Override
	public EventType getRegisteredMainEvent()
	{
		return this._registeredMainEvent;
	}
	
	@Override
	public MiniEventGame getActiveGame()
	{
		return this._activeEvent instanceof MiniEventGame ? (MiniEventGame) this._activeEvent : null;
	}
	
	@Override
	public AbstractMainEvent getActiveEvent()
	{
		return this._activeEvent instanceof AbstractMainEvent ? (AbstractMainEvent) this._activeEvent : null;
	}
	
	@Override
	public EventGame getEvent()
	{
		return this._activeEvent;
	}
	
	@Override
	public void setActiveGame(MiniEventGame game)
	{
		this._activeEvent = game;
	}
	
	@Override
	public void setEventTeam(EventTeam team)
	{
		this._eventTeam = team;
	}
	
	@Override
	public EventTeam getEventTeam()
	{
		return this._eventTeam;
	}
	
	@Override
	public int getTeamId()
	{
		return this._eventTeam != null ? this._eventTeam.getTeamId() : -1;
	}
	
	@Override
	public Loc getOrigLoc()
	{
		return new Loc(this._origLoc.getX(), this._origLoc.getY(), this._origLoc.getZ());
	}
	
	@Override
	public void setIsSpectator(boolean isSpectator)
	{
		this._isSpectator = isSpectator;
	}
	
	@Override
	public boolean isSpectator()
	{
		return this._isSpectator;
	}
	
	@Override
	public boolean isEventRooted()
	{
		return this._disableAfkCheck;
	}
	
	@Override
	public boolean isTitleUpdated()
	{
		return this._titleUpdate;
	}
	
	@Override
	public void setTitleUpdated(boolean b)
	{
		this._titleUpdate = b;
	}
	
	@Override
	public ItemData getPaperdollItem(int slot)
	{
		return new ItemData(this.getOwner().getInventory().getPaperdollItem(slot));
	}
	
	@Override
	public void equipItem(ItemData item)
	{
		this.getOwner().getInventory().equipItemAndRecord(item.getOwner());
	}
	
	@Override
	public ItemData[] unEquipItemInBodySlotAndRecord(long slot)
	{
		ItemData[] items;
		ItemInstance[] is = getOwner().getInventory().unEquipItemInBodySlotAndRecord(slot);
		items = new ItemData[is.length];
		for (int i = 0; i < is.length; i++)
		{
			items[i] = new ItemData(is[i]);
		}
		return items;
	}
	
	@Override
	public void destroyItemByItemId(int id, int count)
	{
		this.getOwner().getInventory().destroyItemByItemId("", id, count, (PlayerInstance) null, (Object) null);
	}
	
	@Override
	public void inventoryUpdate(ItemData[] items)
	{
		InventoryUpdate iu = new InventoryUpdate();
		ItemData[] var3 = items;
		int var4 = items.length;
		for (int var5 = 0; var5 < var4; ++var5)
		{
			ItemData element = var3[var5];
			iu.addModifiedItem(element.getOwner());
		}
		this.getOwner().sendPacket(iu);
		this.getOwner().sendPacket(new ItemList(1, this.getOwner()));
		this.getOwner().sendPacket(new ItemList(2, this.getOwner()));
		this.getOwner().broadcastUserInfo();
	}
	
	@Override
	public Radar getRadar()
	{
		return this._radar;
	}
	
	@Override
	public void createRadar()
	{
		this._radar = new Radar(this);
	}
	
	@Override
	public void addRadarMarker(int x, int y, int z)
	{
		if (this._owner != null)
		{
			this._owner.getRadar().addMarker(x, y, z);
			this._hasMarkers = true;
		}
	}
	
	@Override
	public void removeRadarMarker(int x, int y, int z)
	{
		if (this._owner != null)
		{
			this._owner.getRadar().removeMarker(x, y, z);
		}
	}
	
	@Override
	public void removeRadarAllMarkers()
	{
		if ((this._owner != null) && this._hasMarkers)
		{
			this._owner.getRadar().removeAllMarkers();
			this._hasMarkers = false;
		}
	}
	
	@Override
	public void disableAfkCheck(boolean b)
	{
		this._disableAfkCheck = b;
		if (!b && (this._afkChecker != null))
		{
			this._afkChecker.check();
		}
	}
	
	@Override
	public int getTotalTimeAfk()
	{
		return this._afkChecker == null ? 0 : Math.max(0, this._afkChecker.totalTimeAfk);
	}
	
	@Override
	public boolean isAfk()
	{
		return this._afkChecker != null ? this._afkChecker.isAfk : false;
	}
	
	@Override
	public AfkChecker getAfkChecker()
	{
		return this._afkChecker;
	}
	
	@Override
	public CharacterData getCharacterData()
	{
		return new CharacterData(this.getOwner());
	}
	
	public class AfkChecker implements Runnable
	{
		private final PlayerEventInfo	_player;
		private ScheduledFuture<?>		_nextTask;
		protected boolean				isAfk;
		protected int					totalTimeAfk;
		private int						tempTimeAfk;
		private boolean					isWarned;
		
		public AfkChecker(PlayerEventInfo player)
		{
			this._player = player;
			this.isWarned = false;
			this.isAfk = false;
			this.totalTimeAfk = 0;
			this.tempTimeAfk = 0;
			this.check();
		}
		
		public void onAction()
		{
			if (PlayerEventInfo.this.isInEvent())
			{
				if (this._nextTask != null)
				{
					this._nextTask.cancel(false);
				}
				this.tempTimeAfk = 0;
				this.isWarned = false;
				if (this.isAfk)
				{
					PlayerEventInfo.this._owner.sendMessage("Welcome back. Total time spent AFK so far: " + this.totalTimeAfk);
					this.isAfk = false;
					if (PlayerEventInfo.this._activeEvent != null)
					{
						PlayerEventInfo.this._activeEvent.playerReturnedFromAfk(this._player);
					}
				}
				this.check();
			}
		}
		
		@Override
		public synchronized void run()
		{
			if (PlayerEventInfo.this.isInEvent())
			{
				if (this.isWarned)
				{
					if (!PlayerEventInfo.this._disableAfkCheck && !PlayerEventInfo.this._owner.isDead())
					{
						if (this.isAfk)
						{
							this.totalTimeAfk += 10;
							this.tempTimeAfk += 10;
						}
						else
						{
							this.isAfk = true;
						}
						if (PlayerEventInfo.this._activeEvent != null)
						{
							PlayerEventInfo.this._activeEvent.playerWentAfk(this._player, false, this.tempTimeAfk);
						}
					}
					this.check(10000L);
				}
				else
				{
					if (!PlayerEventInfo.this._disableAfkCheck && !PlayerEventInfo.this._owner.isDead())
					{
						this.isWarned = true;
						if (PlayerEventInfo.this.getActiveGame() != null)
						{
							PlayerEventInfo.this.getActiveGame().playerWentAfk(this._player, true, 0);
						}
						if (PlayerEventInfo.this.getActiveEvent() != null)
						{
							PlayerEventInfo.this.getActiveEvent().playerWentAfk(this._player, true, 0);
						}
					}
					this.check();
				}
			}
		}
		
		protected synchronized void check()
		{
			if (!PlayerEventInfo.this._disableAfkCheck)
			{
				if (this._nextTask != null)
				{
					this._nextTask.cancel(false);
				}
				this._nextTask = ThreadPool.schedule(this, this.isWarned ? (long) PlayerEventInfo.AFK_KICK_DELAY : (long) PlayerEventInfo.AFK_WARNING_DELAY);
			}
		}
		
		private synchronized void check(long delay)
		{
			if (!PlayerEventInfo.this._disableAfkCheck)
			{
				if (this._nextTask != null)
				{
					this._nextTask.cancel(false);
				}
				if (this.isAfk)
				{
					this._nextTask = ThreadPool.schedule(this, delay);
				}
			}
		}
		
		public void stop()
		{
			if (this._nextTask != null)
			{
				this._nextTask.cancel(false);
			}
			this._nextTask = null;
			this.isAfk = false;
			this.isWarned = false;
			this.totalTimeAfk = 0;
			this.tempTimeAfk = 0;
		}
	}
	
	public class Radar
	{
		private final PlayerEventInfo	_player;
		private ScheduledFuture<?>		_refresh;
		private boolean					_enabled;
		private boolean					_repeat	= false;
		private int						_newX;
		private int						_newY;
		private int						_newZ;
		private int						_currentX;
		private int						_currentY;
		private int						_currentZ;
		private boolean					hasRadar;
		
		public Radar(PlayerEventInfo player)
		{
			this._player = player;
			this._refresh = null;
			this._enabled = false;
			this.hasRadar = false;
		}
		
		public void setLoc(int x, int y, int z)
		{
			this._newX = x;
			this._newY = y;
			this._newZ = z;
		}
		
		public void enable()
		{
			this._enabled = true;
			this.applyRadar();
		}
		
		public void disable()
		{
			this._enabled = false;
			if (this.hasRadar)
			{
				this._player.removeRadarMarker(this._currentX, this._currentY, this._currentZ);
				this.hasRadar = false;
			}
		}
		
		public void setRepeat(boolean nextRepeatPolicy)
		{
			if (!this._enabled || (this._repeat && !nextRepeatPolicy))
			{
				if (this._refresh != null)
				{
					this._refresh.cancel(false);
					this._refresh = null;
				}
			}
			else if (!this._repeat && nextRepeatPolicy)
			{
				if (this._refresh != null)
				{
					this._refresh.cancel(false);
					this._refresh = null;
				}
				this._refresh = CallBack.getInstance().getOut().scheduleGeneral(() ->
				{
					this.applyRadar();
				}, 10000L);
			}
			this._repeat = nextRepeatPolicy;
		}
		
		protected void applyRadar()
		{
			if (this._enabled)
			{
				if (this.hasRadar)
				{
					this._player.removeRadarMarker(this._currentX, this._currentY, this._currentZ);
					this.hasRadar = false;
				}
				this._player.addRadarMarker(this._newX, this._newY, this._newZ);
				this._currentX = this._newX;
				this._currentY = this._newY;
				this._currentZ = this._newZ;
				this.hasRadar = true;
				if (this._repeat)
				{
					this.schedule();
				}
			}
		}
		
		private void schedule()
		{
			this._refresh = CallBack.getInstance().getOut().scheduleGeneral(() ->
			{
				this.applyRadar();
			}, 10000L);
		}
		
		public boolean isEnabled()
		{
			return this._enabled;
		}
		
		public boolean isRepeating()
		{
			return this._repeat;
		}
	}
	
	@Override
	public String getFaction()
	{
		String faction = null;
		try (Connection con = DatabaseFactory.getConnection(); PreparedStatement ps = con.prepareStatement("SELECT faction FROM characters WHERE charId = ?"))
		{
			ps.setInt(1, _playersId);
			try (ResultSet rs = ps.executeQuery())
			{
				if (rs.next())
				{
					faction = rs.getString("faction");
				}
			}
		}
		catch (SQLException e)
		{
			e.printStackTrace();
		}
		return faction;
	}
}
