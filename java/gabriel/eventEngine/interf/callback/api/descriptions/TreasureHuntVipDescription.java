package gabriel.eventEngine.interf.callback.api.descriptions;

import gabriel.eventEngine.events.engine.base.ConfigModel;
import gabriel.eventEngine.events.engine.base.description.EventDescription;

import java.util.Map;

public class TreasureHuntVipDescription
        extends EventDescription {
    public String getDescription(Map<String, ConfigModel> configs) {
        String text = "No information about this event yet.";
        return text;
    }
}


