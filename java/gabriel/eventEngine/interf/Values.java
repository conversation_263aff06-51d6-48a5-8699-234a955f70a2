package gabriel.eventEngine.interf;

import club.projectessence.gameserver.enums.ShortcutType;
import club.projectessence.gameserver.model.items.type.CrystalType;
import club.projectessence.gameserver.model.skills.AbnormalVisualEffect;
import gabriel.eventEngine.interf.delegate.ItemData;
import gabriel.eventEngine.l2j.CallBack;
import gabriel.eventEngine.l2j.IValues;
import gabriel.eventEngine.l2j.WeaponType;

public class Values implements IValues
{
	public Values()
	{}
	
	public void load()
	{
		CallBack.getInstance().setValues(this);
	}
	
	@Override
	public int PAPERDOLL_UNDER()
	{
		return 0;
	}
	
	@Override
	public int PAPERDOLL_HEAD()
	{
		return 1;
	}
	
	@Override
	public int PAPERDOLL_HAIR()
	{
		return 2;
	}
	
	@Override
	public int PAPERDOLL_HAIR2()
	{
		return 3;
	}
	
	@Override
	public int PAPERDOLL_NECK()
	{
		return 4;
	}
	
	@Override
	public int PAPERDOLL_RHAND()
	{
		return 5;
	}
	
	@Override
	public int PAPERDOLL_CHEST()
	{
		return 6;
	}
	
	@Override
	public int PAPERDOLL_LHAND()
	{
		return 7;
	}
	
	@Override
	public int PAPERDOLL_REAR()
	{
		return 8;
	}
	
	@Override
	public int PAPERDOLL_LEAR()
	{
		return 9;
	}
	
	@Override
	public int PAPERDOLL_GLOVES()
	{
		return 10;
	}
	
	@Override
	public int PAPERDOLL_LEGS()
	{
		return 11;
	}
	
	@Override
	public int PAPERDOLL_FEET()
	{
		return 12;
	}
	
	@Override
	public int PAPERDOLL_RFINGER()
	{
		return 13;
	}
	
	@Override
	public int PAPERDOLL_LFINGER()
	{
		return 14;
	}
	
	@Override
	public int PAPERDOLL_LBRACELET()
	{
		return 15;
	}
	
	@Override
	public int PAPERDOLL_RBRACELET()
	{
		return 16;
	}
	
	@Override
	public int PAPERDOLL_DECO1()
	{
		return 17;
	}
	
	@Override
	public int PAPERDOLL_DECO2()
	{
		return 18;
	}
	
	@Override
	public int PAPERDOLL_DECO3()
	{
		return 19;
	}
	
	@Override
	public int PAPERDOLL_DECO4()
	{
		return 20;
	}
	
	@Override
	public int PAPERDOLL_DECO5()
	{
		return 21;
	}
	
	@Override
	public int PAPERDOLL_DECO6()
	{
		return 22;
	}
	
	@Override
	public int PAPERDOLL_CLOAK()
	{
		return 23;
	}
	
	@Override
	public int PAPERDOLL_BELT()
	{
		return 24;
	}
	
	@Override
	public int PAPERDOLL_TOTALSLOTS()
	{
		return 25;
	}
	
	@Override
	public int SLOT_NONE()
	{
		return 0;
	}
	
	@Override
	public int SLOT_UNDERWEAR()
	{
		return 1;
	}
	
	@Override
	public int SLOT_R_EAR()
	{
		return 2;
	}
	
	@Override
	public int SLOT_L_EAR()
	{
		return 4;
	}
	
	@Override
	public int SLOT_LR_EAR()
	{
		return 6;
	}
	
	@Override
	public int SLOT_NECK()
	{
		return 8;
	}
	
	@Override
	public int SLOT_R_FINGER()
	{
		return 16;
	}
	
	@Override
	public int SLOT_L_FINGER()
	{
		return 32;
	}
	
	@Override
	public int SLOT_LR_FINGER()
	{
		return 48;
	}
	
	@Override
	public int SLOT_HEAD()
	{
		return 64;
	}
	
	@Override
	public int SLOT_R_HAND()
	{
		return 128;
	}
	
	@Override
	public int SLOT_L_HAND()
	{
		return 256;
	}
	
	@Override
	public int SLOT_GLOVES()
	{
		return 512;
	}
	
	@Override
	public int SLOT_CHEST()
	{
		return 1024;
	}
	
	@Override
	public int SLOT_LEGS()
	{
		return 2048;
	}
	
	@Override
	public int SLOT_FEET()
	{
		return 4096;
	}
	
	@Override
	public int SLOT_BACK()
	{
		return 8192;
	}
	
	@Override
	public int SLOT_LR_HAND()
	{
		return 16384;
	}
	
	@Override
	public int SLOT_FULL_ARMOR()
	{
		return 32768;
	}
	
	@Override
	public int SLOT_HAIR()
	{
		return 65536;
	}
	
	@Override
	public int SLOT_ALLDRESS()
	{
		return 131072;
	}
	
	@Override
	public int SLOT_HAIR2()
	{
		return 262144;
	}
	
	@Override
	public int SLOT_HAIRALL()
	{
		return 524288;
	}
	
	@Override
	public int SLOT_R_BRACELET()
	{
		return 1048576;
	}
	
	@Override
	public int SLOT_L_BRACELET()
	{
		return 2097152;
	}
	
	@Override
	public int SLOT_DECO()
	{
		return 4194304;
	}
	
	@Override
	public int SLOT_BELT()
	{
		return 268435456;
	}
	
	@Override
	public int SLOT_WOLF()
	{
		return -100;
	}
	
	@Override
	public int SLOT_HATCHLING()
	{
		return -101;
	}
	
	@Override
	public int SLOT_STRIDER()
	{
		return -102;
	}
	
	@Override
	public int SLOT_BABYPET()
	{
		return -103;
	}
	
	@Override
	public int SLOT_GREATWOLF()
	{
		return -104;
	}
	
	@Override
	public int CRYSTAL_NONE()
	{
		return CrystalType.NONE.getCrystalId();
	}
	
	@Override
	public int CRYSTAL_D()
	{
		return CrystalType.D.getCrystalId();
	}
	
	@Override
	public int CRYSTAL_C()
	{
		return CrystalType.C.getCrystalId();
	}
	
	@Override
	public int CRYSTAL_B()
	{
		return CrystalType.B.getCrystalId();
	}
	
	@Override
	public int CRYSTAL_A()
	{
		return CrystalType.A.getCrystalId();
	}
	
	@Override
	public int CRYSTAL_S()
	{
		return CrystalType.S.getCrystalId();
	}
	
	@Override
	public int CRYSTAL_S80()
	{
		return CrystalType.S80.getCrystalId();
	}
	
	@Override
	public int CRYSTAL_S84()
	{
		return CrystalType.S84.getCrystalId();
	}
	
	@Override
	public ShortcutType TYPE_ITEM()
	{
		return ShortcutType.ITEM;
	}
	
	@Override
	public ShortcutType TYPE_SKILL()
	{
		return ShortcutType.SKILL;
	}
	
	@Override
	public ShortcutType TYPE_ACTION()
	{
		return ShortcutType.ACTION;
	}
	
	@Override
	public ShortcutType TYPE_MACRO()
	{
		return ShortcutType.MACRO;
	}
	
	@Override
	public ShortcutType TYPE_RECIPE()
	{
		return ShortcutType.RECIPE;
	}
	
	@Override
	public ShortcutType TYPE_TPBOOKMARK()
	{
		return ShortcutType.BOOKMARK;
	}
	
	@Override
	public int ABNORMAL_NULL()
	{
		return 0;
	}
	
	public WeaponType getWeaponType(ItemData item)
	{
		club.projectessence.gameserver.model.items.type.WeaponType origType = (club.projectessence.gameserver.model.items.type.WeaponType) item.getTemplate().getItemType();
		switch (origType)
		{
			case SWORD:
				return WeaponType.SWORD;
			case BLUNT:
				return WeaponType.BLUNT;
			case DAGGER:
				return WeaponType.DAGGER;
			case BOW:
				return WeaponType.BOW;
			case POLE:
				return WeaponType.POLE;
			case NONE:
				return WeaponType.NONE;
			case DUAL:
				return WeaponType.DUAL;
			case ETC:
				return WeaponType.ETC;
			case FIST:
				return WeaponType.FIST;
			case DUALFIST:
				return WeaponType.DUALFIST;
			case FISHINGROD:
				return WeaponType.FISHINGROD;
			case RAPIER:
				return WeaponType.RAPIER;
			case ANCIENTSWORD:
				return WeaponType.ANCIENTSWORD;
			case CROSSBOW:
				return WeaponType.CROSSBOW;
			case FLAG:
				return WeaponType.FLAG;
			case OWNTHING:
				return WeaponType.OWNTHING;
			case DUALDAGGER:
				return WeaponType.DUALDAGGER;
			default:
				return null;
		}
	}
	
	@Override
	public int ABNORMAL_BLEEDING()
	{
		return AbnormalVisualEffect.DOT_BLEEDING.getClientId();
	}
	
	@Override
	public int ABNORMAL_POISON()
	{
		return AbnormalVisualEffect.DOT_POISON.getClientId();
	}
	
	@Override
	public int ABNORMAL_REDCIRCLE()
	{
		return AbnormalVisualEffect.WORLDCUP_RED_AVE.getClientId();
	}
	
	@Override
	public int ABNORMAL_ICE()
	{
		return AbnormalVisualEffect.ICE_ELEMENTALDESTROY.getClientId();
	}
	
	@Override
	public int ABNORMAL_WIND()
	{
		return AbnormalVisualEffect.DOT_WIND.getClientId();
	}
	
	@Override
	public int ABNORMAL_FEAR()
	{
		return AbnormalVisualEffect.HEROIC_FEAR_AVE_1.getClientId();
	}
	
	@Override
	public int ABNORMAL_STUN()
	{
		return AbnormalVisualEffect.STUN.getClientId();
	}
	
	@Override
	public int ABNORMAL_SLEEP()
	{
		return AbnormalVisualEffect.SLEEP.getClientId();
	}
	
	@Override
	public int ABNORMAL_MUTED()
	{
		return 0;
	}
	
	@Override
	public int ABNORMAL_ROOT()
	{
		return AbnormalVisualEffect.ROOT.getClientId();
	}
	
	@Override
	public int ABNORMAL_HOLD_1()
	{
		return AbnormalVisualEffect.HOLD_LIGHTING.getClientId();
	}
	
	@Override
	public int ABNORMAL_HOLD_2()
	{
		return 0;
	}
	
	@Override
	public int ABNORMAL_UNKNOWN_13()
	{
		return 0;
	}
	
	@Override
	public int ABNORMAL_BIG_HEAD()
	{
		return AbnormalVisualEffect.BIG_HEAD.getClientId();
	}
	
	@Override
	public int ABNORMAL_FLAME()
	{
		return 0;
	}
	
	@Override
	public int ABNORMAL_UNKNOWN_16()
	{
		return 0;
	}
	
	@Override
	public int ABNORMAL_GROW()
	{
		return 0;
	}
	
	@Override
	public int ABNORMAL_FLOATING_ROOT()
	{
		return AbnormalVisualEffect.FLOATING_ROOT.getClientId();
	}
	
	@Override
	public int ABNORMAL_DANCE_STUNNED()
	{
		return AbnormalVisualEffect.DANCE_ROOT.getClientId();
	}
	
	@Override
	public int ABNORMAL_FIREROOT_STUN()
	{
		return 0;
	}
	
	@Override
	public int ABNORMAL_STEALTH()
	{
		return AbnormalVisualEffect.STEALTH.getClientId();
	}
	
	@Override
	public int ABNORMAL_IMPRISIONING_1()
	{
		return 0;
	}
	
	@Override
	public int ABNORMAL_IMPRISIONING_2()
	{
		return 0;
	}
	
	@Override
	public int ABNORMAL_MAGIC_CIRCLE()
	{
		return 0;
	}
	
	@Override
	public int ABNORMAL_ICE2()
	{
		return 0;
	}
	
	@Override
	public int ABNORMAL_EARTHQUAKE()
	{
		return 0;
	}
	
	@Override
	public int ABNORMAL_UNKNOWN_27()
	{
		return 0;
	}
	
	@Override
	public int ABNORMAL_INVULNERABLE()
	{
		return 0;
	}
	
	@Override
	public int ABNORMAL_VITALITY()
	{
		return AbnormalVisualEffect.NAVIT_ADVENT.getClientId();
	}
	
	@Override
	public int ABNORMAL_REAL_TARGET()
	{
		return AbnormalVisualEffect.REAL_TARGET.getClientId();
	}
	
	@Override
	public int ABNORMAL_DEATH_MARK()
	{
		return AbnormalVisualEffect.DEATH_MARK.getClientId();
	}
	
	@Override
	public int ABNORMAL_SKULL_FEAR()
	{
		return 0;
	}
	
	@Override
	public int ABNORMAL_S_INVINCIBLE()
	{
		return AbnormalVisualEffect.INVINCIBILITY.getClientId();
	}
	
	@Override
	public int ABNORMAL_S_AIR_STUN()
	{
		return AbnormalVisualEffect.AIR_BATTLE_ROOT.getClientId();
	}
	
	@Override
	public int ABNORMAL_S_AIR_ROOT()
	{
		return AbnormalVisualEffect.AIR_BATTLE_ROOT.getClientId();
	}
	
	@Override
	public int ABNORMAL_S_BAGUETTE_SWORD()
	{
		return AbnormalVisualEffect.CHANGE_VES_S.getClientId();
	}
	
	@Override
	public int ABNORMAL_S_YELLOW_AFFRO()
	{
		return AbnormalVisualEffect.CHANGE_VES_S.getClientId();
	}
	
	@Override
	public int ABNORMAL_S_PINK_AFFRO()
	{
		return AbnormalVisualEffect.CHANGE_VES_S.getClientId();
	}
	
	@Override
	public int ABNORMAL_S_BLACK_AFFRO()
	{
		return AbnormalVisualEffect.CHANGE_VES_S.getClientId();
	}
	
	@Override
	public int ABNORMAL_S_UNKNOWN8()
	{
		return 0;
	}
	
	@Override
	public int ABNORMAL_S_STIGMA_SHILIEN()
	{
		return AbnormalVisualEffect.STIGMA_OF_SILEN.getClientId();
	}
	
	@Override
	public int ABNORMAL_S_STAKATOROOT()
	{
		return 0;
	}
	
	@Override
	public int ABNORMAL_S_FREEZING()
	{
		return AbnormalVisualEffect.FREEZING.getClientId();
	}
	
	@Override
	public int ABNORMAL_S_VESPER()
	{
		return AbnormalVisualEffect.CHANGE_VES_S.getClientId();
	}
	
	@Override
	public int ABNORMAL_E_AFRO_1()
	{
		return AbnormalVisualEffect.CHANGE_VES_S.getClientId();
	}
	
	@Override
	public int ABNORMAL_E_AFRO_2()
	{
		return AbnormalVisualEffect.CHANGE_VES_S.getClientId();
	}
	
	@Override
	public int ABNORMAL_E_AFRO_3()
	{
		return AbnormalVisualEffect.CHANGE_VES_S.getClientId();
	}
	
	@Override
	public int ABNORMAL_E_EVASWRATH()
	{
		return AbnormalVisualEffect.CHANGE_VES_S.getClientId();
	}
	
	@Override
	public int ABNORMAL_E_HEADPHONE()
	{
		return AbnormalVisualEffect.CHANGE_VES_S.getClientId();
	}
	
	@Override
	public int ABNORMAL_E_VESPER_1()
	{
		return AbnormalVisualEffect.CHANGE_VES_S.getClientId();
	}
	
	@Override
	public int ABNORMAL_E_VESPER_2()
	{
		return AbnormalVisualEffect.CHANGE_VES_S.getClientId();
	}
	
	@Override
	public int ABNORMAL_E_VESPER_3()
	{
		return AbnormalVisualEffect.CHANGE_VES_S.getClientId();
	}
	
	public static final Values getInstance()
	{
		return SingletonHolder._instance;
	}
	
	private static class SingletonHolder
	{
		protected static final Values _instance = new Values();
		
		private SingletonHolder()
		{}
	}
}
