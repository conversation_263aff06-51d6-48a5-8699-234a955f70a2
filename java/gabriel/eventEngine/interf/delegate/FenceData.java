package gabriel.eventEngine.interf.delegate;

import club.projectessence.gameserver.model.World;
import club.projectessence.gameserver.model.WorldObject;
import club.projectessence.gameserver.model.WorldRegion;
import club.projectessence.gameserver.model.actor.instance.FenceInstance;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import gabriel.eventEngine.l2j.delegate.IFenceData;

public class FenceData extends ObjectData implements IFenceData
{
	private final FenceInstance _owner;
	
	public FenceData(FenceInstance cha)
	{
		super((WorldObject) cha);
		this._owner = cha;
	}
	
	public FenceInstance getOwner()
	{
		return this._owner;
	}
	
	public void deleteMe()
	{
		WorldRegion region = this._owner.getWorldRegion();
		this._owner.decayMe();
		if (region != null)
		{
			region.removeVisibleObject((WorldObject) this._owner);
		}
		// this._owner.getKnownList().removeAllKnownObjects();
		World.getInstance().getVisibleObjects(this._owner, PlayerInstance.class).removeAll(World.getInstance().getVisibleObjects(this._owner, PlayerInstance.class));
		World.getInstance().removeObject((WorldObject) this._owner);
	}
}
