package gabriel.eventEngine.interf.delegate;

import java.util.LinkedList;
import java.util.List;

import club.projectessence.gameserver.enums.PartyDistributionType;
import club.projectessence.gameserver.model.Party;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import gabriel.eventEngine.interf.PlayerEventInfo;
import gabriel.eventEngine.l2j.delegate.IPartyData;

public class PartyData implements IPartyData
{
	private final Party _party;
	
	public PartyData(Party p)
	{
		this._party = p;
	}
	
	public PartyData(PlayerEventInfo leader)
	{
		leader.getOwner().setParty(new Party(leader.getOwner(), PartyDistributionType.RANDOM));
		this._party = leader.getOwner().getParty();
	}
	
	public Party getParty()
	{
		return this._party;
	}
	
	public boolean exists()
	{
		return (this._party != null);
	}
	
	public void addPartyMember(PlayerEventInfo player)
	{
		player.getOwner().joinParty(this._party);
	}
	
	public void removePartyMember(PlayerEventInfo player)
	{
		this._party.removePartyMember(player.getOwner(), Party.MessageType.NONE);
	}
	
	public PlayerEventInfo getLeader()
	{
		return this._party.getLeader().getEventInfo();
	}
	
	public PlayerEventInfo[] getPartyMembers()
	{
		List<PlayerEventInfo> players = new LinkedList<>();
		for (PlayerInstance player : this._party.getMembers())
		{
			players.add(player.getEventInfo());
		}
		return players.<PlayerEventInfo> toArray(new PlayerEventInfo[players.size()]);
	}
	
	public int getMemberCount()
	{
		return this._party.getMemberCount();
	}
	
	public int getLeadersId()
	{
		return this._party.getLeaderObjectId();
	}
}
