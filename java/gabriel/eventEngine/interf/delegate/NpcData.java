package gabriel.eventEngine.interf.delegate;

import java.util.Collection;

import club.projectessence.gameserver.data.SpawnTable;
import club.projectessence.gameserver.instancemanager.DBSpawnManager;
import club.projectessence.gameserver.model.Spawn;
import club.projectessence.gameserver.model.World;
import club.projectessence.gameserver.model.actor.Npc;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.model.skills.Skill;
import club.projectessence.gameserver.network.serverpackets.MagicSkillUse;
import gabriel.eventEngine.l2j.delegate.INpcData;

public class NpcData extends CharacterData implements INpcData
{
	private int		_team;
	private boolean	deleted	= false;
	
	public NpcData(Npc npc)
	{
		super(npc);
	}
	
	@Override
	public void deleteMe()
	{
		if (!this.deleted)
		{
			((Npc) this._owner).deleteMe();
		}
		this._owner.deleteMe();
		if (this._owner.isNpc())
		{
			Npc npc = (Npc) _owner;
			Spawn spawn = npc.getSpawn();
			if (spawn != null)
			{
				spawn.stopRespawn();
				if (DBSpawnManager.getInstance().isDefined(spawn.getId()))
				{
					DBSpawnManager.getInstance().deleteSpawn(spawn, true);
				}
				else
				{
					SpawnTable.getInstance().deleteSpawn(spawn, true);
				}
			}
		}
		this.deleted = true;
	}
	
	@Override
	public ObjectData getObjectData()
	{
		return new ObjectData(this._owner);
	}
	
	@Override
	public void setName(String name)
	{
		this._owner.setName(name);
	}
	
	@Override
	public void setTitle(String t)
	{
		this._owner.setTitle(t, false, false);
	}
	
	@Override
	public int getNpcId()
	{
		return ((Npc) this._owner).getId();
	}
	
	@Override
	public void setEventTeam(int team)
	{
		this._team = team;
	}
	
	@Override
	public int getEventTeam()
	{
		return this._team;
	}
	
	@Override
	public void broadcastNpcInfo()
	{
		Collection<PlayerInstance> plrs = World.getInstance().getVisibleObjects(this._owner, PlayerInstance.class);
		for (PlayerInstance player : plrs)
		{
			((Npc) this._owner).sendInfo(player);
		}
	}
	
	@Override
	public void broadcastSkillUse(CharacterData owner, CharacterData target, int skillId, int level)
	{
		Skill skill = club.projectessence.gameserver.data.xml.SkillData.getInstance().getSkill(skillId, level);
		if (skill != null)
		{
			getOwner().broadcastPacket(new MagicSkillUse(owner.getOwner(), target.getOwner(), skill.getId(), skill.getLevel(), skill.getHitTime(), skill.getReuseDelay()));
		}
	}
}
