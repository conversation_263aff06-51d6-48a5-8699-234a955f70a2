package gabriel.eventEngine.interf.delegate;

import club.projectessence.gameserver.data.ItemTable;
import club.projectessence.gameserver.model.items.Armor;
import club.projectessence.gameserver.model.items.Item;
import club.projectessence.gameserver.model.items.Weapon;
import club.projectessence.gameserver.model.items.instance.ItemInstance;
import club.projectessence.gameserver.model.items.type.EtcItemType;
import gabriel.eventEngine.interf.Values;
import gabriel.eventEngine.l2j.WeaponType;
import gabriel.eventEngine.l2j.delegate.IItemData;

public class ItemData implements IItemData
{
	private final ItemInstance	_item;
	private Item				_itemTemplate;
	
	public ItemData(int id)
	{
		this._item = null;
		this._itemTemplate = ItemTable.getInstance().getTemplate(id);
	}
	
	public ItemData(ItemInstance cha)
	{
		this._item = cha;
		if (this._item != null)
		{
			this._itemTemplate = this._item.getItem();
		}
	}
	
	public ItemData(int itemId, int count)
	{
		this._item = ItemTable.getInstance().createItem("Event Engine ItemData", itemId, count, null);
		if (this._item != null)
		{
			this._itemTemplate = this._item.getItem();
		}
	}
	
	public ItemInstance getOwner()
	{
		return this._item;
	}
	
	@Override
	public int getObjectId()
	{
		if (exists())
		{
			return getOwner().getObjectId();
		}
		return -1;
	}
	
	public Item getTemplate()
	{
		return this._itemTemplate;
	}
	
	@Override
	public boolean exists()
	{
		return (this._item != null);
	}
	
	@Override
	public boolean isEquipped()
	{
		if (exists() && this._item.isEquipped())
		{
			return true;
		}
		return false;
	}
	
	@Override
	public int getItemId()
	{
		return this._itemTemplate.getId();
	}
	
	@Override
	public String getItemName()
	{
		return this._itemTemplate.getName();
	}
	
	@Override
	public int getEnchantLevel()
	{
		return (this._item != null) ? this._item.getEnchantLevel() : 0;
	}
	
	@Override
	public int getCrystalType()
	{
		return this._itemTemplate.getCrystalType().getCrystalId();
	}
	
	@Override
	public long getBodyPart()
	{
		return this._itemTemplate.getBodyPart();
	}
	
	@Override
	public boolean isArmor()
	{
		return this._itemTemplate instanceof Armor;
	}
	
	@Override
	public boolean isWeapon()
	{
		return this._itemTemplate instanceof Weapon;
	}
	
	@Override
	public WeaponType getWeaponType()
	{
		if (isWeapon())
		{
			return Values.getInstance().getWeaponType(this);
		}
		return null;
	}
	
	@Override
	public boolean isType2Armor()
	{
		return (this._itemTemplate.getType2() == 1);
	}
	
	@Override
	public boolean isType2Weapon()
	{
		return (this._itemTemplate.getType2() == 0);
	}
	
	@Override
	public boolean isType2Accessory()
	{
		return (this._itemTemplate.getType2() == 2);
	}
	
	@Override
	public boolean isJewellery()
	{
		return (this._itemTemplate.getType2() == 2);
	}
	
	@Override
	public boolean isPotion()
	{
		return (this._itemTemplate.getItemType() == EtcItemType.POTION);
	}
	
	@Override
	public boolean isScroll()
	{
		return (this._itemTemplate.getItemType() == EtcItemType.SCROLL);
	}
	
	@Override
	public boolean isPetCollar()
	{
		if ((this._item != null) && this._item.isEtcItem() && (this._item.getEtcItem().getItemType() == EtcItemType.PET_COLLAR))
		{
			return true;
		}
		return false;
	}
}
