package gabriel.eventEngine.interf.delegate;

import club.projectessence.gameserver.network.serverpackets.ShowBoard;
import gabriel.eventEngine.interf.PlayerEventInfo;
import gabriel.eventEngine.l2j.delegate.IShowBoardData;

public class ShowBoardData implements IShowBoardData
{
	private final ShowBoard _board;
	
	public ShowBoardData(ShowBoard sb)
	{
		this._board = sb;
	}
	
	public ShowBoardData(String text, String id)
	{
		this._board = new ShowBoard(text, id);
	}
	
	@Override
	public void sendToPlayer(PlayerEventInfo player)
	{
		player.getOwner().sendPacket(this._board);
	}
}
