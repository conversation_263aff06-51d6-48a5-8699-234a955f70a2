package gabriel.eventEngine.interf.delegate;

import club.projectessence.gameserver.model.effects.AbstractEffect;
import club.projectessence.gameserver.model.skills.Skill;
import gabriel.eventEngine.l2j.delegate.ISkillData;

public class SkillData implements ISkillData
{
	private final Skill _skill;
	
	public SkillData(Skill cha)
	{
		this._skill = cha;
	}
	
	public SkillData(int skillId, int level)
	{
		this._skill = club.projectessence.gameserver.data.xml.SkillData.getInstance().getSkill(skillId, level);
	}
	
	public Skill getOwner()
	{
		return this._skill;
	}
	
	@Override
	public String getName()
	{
		return this._skill.getName();
	}
	
	@Override
	public int getLevel()
	{
		return this._skill.getLevel();
	}
	
	@Override
	public boolean exists()
	{
		return (this._skill != null);
	}
	
	public boolean isHealSkill()
	{
		for (AbstractEffect effect : this._skill.getAllEffects())
		{
			switch (effect.getEffectType())
			{
				case HEAL:
				case CPHEAL:
				case REBALANCE_HP:
				case MANAHEAL_PERCENT:
				case MANAHEAL_BY_LEVEL:
					return true;
				default:
					break;
			}
		}
		return false;
	}
	
	public boolean blockUsage()
	{
		for (AbstractEffect effect : this._skill.getAllEffects())
		{
			switch (effect.getEffectType())
			{
				case RESURRECTION:
				case RESURRECTION_SPECIAL:
				case TELEPORT:
				case TELEPORT_TO_TARGET:
					return true;
				default:
					break;
			}
		}
		return false;
	}
	
	public boolean isSummon()
	{
		for (AbstractEffect effect : this._skill.getAllEffects())
		{
			switch (effect.getEffectType())
			{
				case SUMMON:
				case SUMMON_NPC:
				case SUMMON_PET:
					return true;
				default:
					break;
			}
		}
		return false;
	}
	
	@Override
	public boolean isResSkill()
	{
		for (AbstractEffect effect : this._skill.getAllEffects())
		{
			switch (effect.getEffectType())
			{
				case RESURRECTION:
				case RESURRECTION_SPECIAL:
					return true;
				default:
					break;
			}
		}
		return false;
	}
	
	@Override
	public int getHitTime()
	{
		return this._skill.getHitTime();
	}
	
	@Override
	public int getReuseDelay()
	{
		return this._skill.getReuseDelay();
	}
	
	@Override
	public int getId()
	{
		return this._skill.getId();
	}
}
