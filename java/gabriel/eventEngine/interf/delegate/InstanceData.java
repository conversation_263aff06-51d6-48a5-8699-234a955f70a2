package gabriel.eventEngine.interf.delegate;

import club.projectessence.gameserver.model.instancezone.Instance;
import gabriel.eventEngine.l2j.delegate.IInstanceData;

public class InstanceData implements IInstanceData
{
	protected Instance _instance;
	
	public InstanceData(Instance i)
	{
		this._instance = i;
	}
	
	public Instance getOwner()
	{
		return this._instance;
	}
	
	public int getId()
	{
		return this._instance.getId();
	}
	
	public String getName()
	{
		return this._instance.getName();
	}
}
