package gabriel.eventEngine.interf.delegate;

import club.projectessence.gameserver.model.actor.instance.DoorInstance;
import gabriel.eventEngine.l2j.delegate.IDoorData;

public class DoorData extends CharacterData implements IDoorData
{
	protected DoorInstance _owner;
	
	public DoorData(DoorInstance d)
	{
		super(d);
		this._owner = d;
	}
	
	@Override
	public DoorInstance getOwner()
	{
		return this._owner;
	}
	
	@Override
	public int getDoorId()
	{
		return this._owner.getId();
	}
	
	@Override
	public boolean isOpened()
	{
		return this._owner.isOpen();
	}
	
	@Override
	public void openMe()
	{
		this._owner.openMe();
	}
	
	@Override
	public void closeMe()
	{
		this._owner.closeMe();
	}
}
