package gabriel.eventEngine.interf.delegate;

import java.util.Arrays;

import club.projectessence.gameserver.enums.ChatType;
import club.projectessence.gameserver.model.actor.Creature;
import club.projectessence.gameserver.model.actor.Playable;
import club.projectessence.gameserver.model.actor.instance.DoorInstance;
import club.projectessence.gameserver.model.skills.AbnormalVisualEffect;
import club.projectessence.gameserver.network.serverpackets.CreatureSay;
import gabriel.eventEngine.events.engine.base.Loc;
import gabriel.eventEngine.interf.PlayerEventInfo;
import gabriel.eventEngine.l2j.delegate.ICharacterData;

public class CharacterData extends ObjectData implements ICharacterData
{
	protected Creature _owner;
	
	public CharacterData(Creature cha)
	{
		super(cha);
		this._owner = cha;
	}
	
	@Override
	public Creature getOwner()
	{
		return this._owner;
	}
	
	@Override
	public double getPlanDistanceSq(int targetX, int targetY)
	{
		return this._owner.getPlanDistanceSq(targetX, targetY);
	}
	
	@Override
	public Loc getLoc()
	{
		return new Loc(this._owner.getX(), this._owner.getY(), this._owner.getZ(), this._owner.getHeading());
	}
	
	@Override
	public int getObjectId()
	{
		return this._owner.getObjectId();
	}
	
	@Override
	public boolean isDoor()
	{
		return this._owner instanceof DoorInstance;
	}
	
	@Override
	public DoorData getDoorData()
	{
		return isDoor() ? new DoorData((DoorInstance) this._owner) : null;
	}
	
	@Override
	public void startAbnormalEffect(int mask)
	{
		AbnormalVisualEffect eff = Arrays.stream(AbnormalVisualEffect.values()).filter(e -> e.getClientId() == mask).findFirst().get();
		this._owner.getEffectList().startAbnormalVisualEffect(eff);
	}
	
	@Override
	public void stopAbnormalEffect(int mask)
	{
		AbnormalVisualEffect eff = Arrays.stream(AbnormalVisualEffect.values()).filter(e -> e.getClientId() == mask).findFirst().get();
		this._owner.getEffectList().stopAbnormalVisualEffect(eff);
	}
	
	@Override
	public PlayerEventInfo getEventInfo()
	{
		if (this._owner instanceof Playable)
		{
			return ((Playable) this._owner).getActingPlayer().getEventInfo();
		}
		return null;
	}
	
	@Override
	public String getName()
	{
		return this._owner.getName();
	}
	
	@Override
	public void creatureSay(int channel, String charName, String text)
	{
		this._owner.broadcastPacket(new CreatureSay(this._owner, ChatType.values()[channel], charName, text, 0));
	}
	
	@Override
	public void doDie(CharacterData killer)
	{
		this._owner.reduceCurrentHp(this._owner.getCurrentHp() * 2.0D, killer.getOwner(), null);
	}
	
	@Override
	public boolean isDead()
	{
		return this._owner.isDead();
	}
}
