package gabriel.eventEngine.interf.delegate;

import club.projectessence.gameserver.model.WorldObject;
import club.projectessence.gameserver.model.actor.Npc;
import club.projectessence.gameserver.model.actor.Summon;
import club.projectessence.gameserver.model.actor.instance.FenceInstance;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import gabriel.eventEngine.l2j.delegate.IObjectData;

public class ObjectData implements IObjectData
{
	protected WorldObject _owner;
	
	public ObjectData(WorldObject cha)
	{
		this._owner = cha;
	}
	
	public WorldObject getOwner()
	{
		return this._owner;
	}
	
	public int getObjectId()
	{
		return this._owner.getObjectId();
	}
	
	public boolean isPlayer()
	{
		return this._owner instanceof PlayerInstance;
	}
	
	public boolean isSummon()
	{
		return this._owner instanceof Summon;
	}
	
	public boolean isFence()
	{
		return this._owner instanceof FenceInstance;
	}
	
	public FenceData getFence()
	{
		if (!isFence())
		{
			return null;
		}
		return new FenceData((FenceInstance) this._owner);
	}
	
	public NpcData getNpc()
	{
		return new NpcData((Npc) this._owner);
	}
	
	public boolean isNpc()
	{
		return this._owner instanceof Npc;
	}
}
