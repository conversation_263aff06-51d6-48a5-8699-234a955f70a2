package gabriel.eventEngine.interf.delegate;

import club.projectessence.gameserver.model.Location;
import club.projectessence.gameserver.model.Spawn;
import club.projectessence.gameserver.model.actor.Npc;
import club.projectessence.gameserver.model.actor.templates.NpcTemplate;
import gabriel.eventEngine.l2j.delegate.INpcTemplateData;

public class NpcTemplateData implements INpcTemplateData
{
	private final NpcTemplate	_template;
	private String				_spawnName	= null;
	private String				_spawnTitle	= null;
	
	public NpcTemplateData(int id)
	{
		this._template = club.projectessence.gameserver.data.xml.NpcData.getInstance().getTemplate(id);
	}
	
	@Override
	public void setSpawnName(String name)
	{
		this._spawnName = name;
	}
	
	@Override
	public void setSpawnTitle(String title)
	{
		this._spawnTitle = title;
	}
	
	@Override
	public boolean exists()
	{
		return (this._template != null);
	}
	
	@Override
	public NpcData doSpawn(int x, int y, int z, int ammount, int instanceId)
	{
		return doSpawn(x, y, z, ammount, 0, instanceId);
	}
	
	@Override
	public NpcData doSpawn(int x, int y, int z, int ammount, int heading, int instanceId)
	{
		return doSpawn(x, y, z, ammount, heading, 0, instanceId);
	}
	
	@Override
	public NpcData doSpawn(int x, int y, int z, int ammount, int heading, int respawn, int instanceId)
	{
		if (this._template == null)
		{
			return null;
		}
		try
		{
			Spawn spawn = new Spawn(this._template);
			spawn.setLocation(new Location(x, y, z));
			spawn.setAmount(1);
			spawn.setHeading(heading);
			spawn.setRespawnDelay(respawn);
			spawn.setInstanceId(instanceId);
			Npc npc = spawn.doSpawn();
			NpcData npcData = new NpcData(npc);
			boolean update = false;
			if (this._spawnName != null)
			{
				npc.setName(this._spawnName);
				update = true;
			}
			if (this._spawnTitle != null)
			{
				npc.setTitle(this._spawnTitle, false, false);
				update = true;
			}
			if (update)
			{
				npcData.broadcastNpcInfo();
			}
			return npcData;
		}
		catch (Exception e)
		{
			e.printStackTrace();
			return null;
		}
	}
}
