package gabriel.eventEngine.interf;

import java.sql.Connection;
import java.util.Iterator;
import java.util.LinkedList;
import java.util.List;
import java.util.Set;
import java.util.concurrent.ScheduledFuture;

import club.projectessence.Config;
import club.projectessence.commons.concurrent.ThreadPool;
import club.projectessence.commons.database.DatabaseFactory;
import club.projectessence.commons.util.Rnd;
import club.projectessence.gameserver.ItemsAutoDestroy;
import club.projectessence.gameserver.cache.HtmCache;
import club.projectessence.gameserver.data.ItemTable;
import club.projectessence.gameserver.data.sql.ClanTable;
import club.projectessence.gameserver.data.xml.DoorData;
import club.projectessence.gameserver.enums.ChatType;
import club.projectessence.gameserver.enums.ClassId;
import club.projectessence.gameserver.enums.FenceState;
import club.projectessence.gameserver.handler.AdminCommandHandler;
import club.projectessence.gameserver.instancemanager.IdManager;
import club.projectessence.gameserver.instancemanager.InstanceManager;
import club.projectessence.gameserver.model.StatSet;
import club.projectessence.gameserver.model.World;
import club.projectessence.gameserver.model.WorldRegion;
import club.projectessence.gameserver.model.actor.instance.DoorInstance;
import club.projectessence.gameserver.model.actor.instance.FenceInstance;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.model.actor.templates.DoorTemplate;
import club.projectessence.gameserver.model.instancezone.Instance;
import club.projectessence.gameserver.model.instancezone.InstanceTemplate;
import club.projectessence.gameserver.model.items.instance.ItemInstance;
import club.projectessence.gameserver.model.items.type.CrystalType;
import club.projectessence.gameserver.network.serverpackets.CreatureSay;
import club.projectessence.gameserver.network.serverpackets.NpcHtmlMessage;
import club.projectessence.gameserver.util.Broadcast;
import gabriel.eventEngine.events.GabrielEventsLoader;
import gabriel.eventEngine.interf.delegate.FenceData;
import gabriel.eventEngine.interf.delegate.InstanceData;
import gabriel.eventEngine.interf.handlers.AdminCommandHandlerInstance;
import gabriel.eventEngine.l2j.CallBack;
import gabriel.eventEngine.l2j.IEventOut;

public class GabrielOut implements IEventOut
{
	public GabrielOut()
	{}
	
	public void load()
	{
		CallBack.getInstance().setSunriseOut(this);
	}
	
	@Override
	public ScheduledFuture<?> scheduleGeneral(Runnable task, long delay)
	{
		return ThreadPool.schedule(task, delay);
	}
	
	@Override
	public ScheduledFuture<?> scheduleGeneralAtFixedRate(Runnable task, long initial, long delay)
	{
		return ThreadPool.get().scheduleAtFixedRate(task, initial, delay);
	}
	
	@Override
	public void executeTask(Runnable task)
	{
		// ThreadPool.execute(task);
		ThreadPool.get().execute(task);
	}
	
	@Override
	public void purge()
	{
		ThreadPool.get().purge();
		// ThreadPool.purge();
	}
	
	@Override
	public int getNextObjectId()
	{
		return IdManager.getInstance().getNextId();
	}
	
	@Override
	public int random(int min, int max)
	{
		return Rnd.get(min, max);
	}
	
	@Override
	public int random(int max)
	{
		return Rnd.get(max);
	}
	
	@Override
	public Connection getConnection()
	{
		return DatabaseFactory.getConnection();
	}
	
	@Override
	public InstanceData createInstance(String name, int duration, long emptyDestroyTime, boolean isPvp)
	{
		Instance instanceId = InstanceManager.getInstance().createInstance();
		InstanceTemplate template = instanceId.getTemplate();
		template.setName(name);
		template.setDuration(duration);
		if (emptyDestroyTime > 0)
		{
			template.setEmptyDestroyTime(emptyDestroyTime);
		}
		template.allowPlayerSummon(false);
		template.setPvP(isPvp);
		return new InstanceData(instanceId);
	}
	
	@Override
	public void addDoorToInstance(int instanceId, int doorId, boolean opened)
	{
		StatSet temp = DoorData.getInstance().getDoorTemplate(doorId);
		DoorTemplate doorTemplate = new DoorTemplate(temp);
		InstanceManager.getInstance().getInstance(instanceId).getTemplate().addDoor(doorId, doorTemplate);
	}
	
	@Override
	public gabriel.eventEngine.interf.delegate.DoorData[] getInstanceDoors(int instanceId)
	{
		List<gabriel.eventEngine.interf.delegate.DoorData> doors = new LinkedList<>();
		Iterator<DoorInstance> var3 = InstanceManager.getInstance().getInstance(instanceId).getDoors().iterator();
		while (var3.hasNext())
		{
			DoorInstance d = (DoorInstance) var3.next();
			doors.add(new gabriel.eventEngine.interf.delegate.DoorData(d));
		}
		return doors.toArray(new gabriel.eventEngine.interf.delegate.DoorData[doors.size()]);
	}
	
	@Override
	public void registerAdminHandler(AdminCommandHandlerInstance handler)
	{
		AdminCommandHandler.getInstance().registerHandler(handler);
	}
	
	@Override
	public PlayerEventInfo getPlayer(int playerId)
	{
		try
		{
			return World.getInstance().getPlayer(playerId).getEventInfo();
		}
		catch (Exception var3)
		{
			return null;
		}
	}
	
	@Override
	public PlayerEventInfo getPlayer(String name)
	{
		try
		{
			return World.getInstance().getPlayer(name).getEventInfo();
		}
		catch (Exception var3)
		{
			return null;
		}
	}
	
	@Override
	public String getClanName(int clanId)
	{
		try
		{
			return ClanTable.getInstance().getClan(clanId).getName();
		}
		catch (Exception var3)
		{
			return null;
		}
	}
	
	@Override
	public String getAllyName(int clanId)
	{
		try
		{
			return ClanTable.getInstance().getClan(clanId).getAllyName();
		}
		catch (Exception var3)
		{
			return null;
		}
	}
	
	@Override
	public void announceToAllScreenMessage(String message, String announcer)
	{
		Broadcast.toAllOnlinePlayers(new CreatureSay(null, ChatType.CRITICAL_ANNOUNCE, "", announcer + ": " + message, 0));
	}
	
	@Override
	public String getHtml(String path)
	{
		NpcHtmlMessage html = new NpcHtmlMessage();
		return !html.setFile((PlayerInstance) null, path) ? null : html.getHtml();
	}
	
	@Override
	public String getEventHtml(String path)
	{
		return this.getHtml(path);
	}
	
	@Override
	public void reloadHtmls()
	{
		HtmCache.getInstance().reload();
	}
	
	@Override
	public String getItemName(int id)
	{
		try
		{
			return ItemTable.getInstance().getTemplate(id).getName();
		}
		catch (Exception var3)
		{
			return "Unknown item";
		}
	}
	
	@Override
	public boolean doorExists(int id)
	{
		return DoorData.getInstance().getDoor(id) != null;
	}
	
	@Override
	public FenceData createFence(int type, int width, int length, int x, int y, int z, int eventId)
	{
		// Giảm height xuống mức tối thiểu để triệt để loại bỏ lag
		// Height 1 = chỉ 1 packet, không có height fences = 0% lag
		return new FenceData(new FenceInstance(x, y, z, "Event", width, length, 1, FenceState.CLOSED));
	}
	
	@Override
	public void spawnFences(List<FenceData> list, int instance)
	{
		if (list == null || list.isEmpty())
		{
			return;
		}

		// Tối ưu spawn fence để triệt để loại bỏ lag
		// Spawn với delay nhỏ giữa các fence để tránh packet burst
		int delay = 0;
		for (FenceData fence : list)
		{
			if (fence != null && fence.getOwner() != null)
			{
				final FenceInstance fenceInstance = fence.getOwner();
				final int currentDelay = delay;

				// Schedule spawn với delay để tránh packet spam
				CallBack.getInstance().getOut().scheduleGeneral(() -> {
					try
					{
						if (instance > 0)
						{
							fenceInstance.setInstanceById(instance);
						}
						// Spawn fence với tối ưu performance
						fenceInstance.spawnMe(fenceInstance.getX(), fenceInstance.getY(), fenceInstance.getZ());
					}
					catch (Exception e)
					{
						// Ignore errors during fence spawn
					}
				}, currentDelay);

				delay += 50; // 50ms delay giữa mỗi fence
			}
		}
	}
	
	@Override
	public void unspawnFences(List<FenceData> list)
	{
		if (list == null || list.isEmpty())
		{
			return;
		}

		// Tối ưu unspawn fence để triệt để loại bỏ lag
		// Unspawn tất cả fence cùng lúc để tránh delay
		for (FenceData fence : list)
		{
			if (fence != null && fence.getOwner() != null)
			{
				try
				{
					FenceInstance fenceInstance = fence.getOwner();
					// Sử dụng deleteMe() thay vì decayMe() để cleanup đúng cách
					fenceInstance.deleteMe();
				}
				catch (Exception e)
				{
					// Ignore errors during fence cleanup
				}
			}
		}

		// Clear list sau khi unspawn
		list.clear();
	}
	
	@Override
	public int getGradeFromFirstLetter(String s)
	{
		if (!s.equalsIgnoreCase("n") && !s.equalsIgnoreCase("ng") && !s.equalsIgnoreCase("no"))
		{
			if (s.equalsIgnoreCase("d"))
			{
				return CrystalType.D.getCrystalId();
			}
			else if (s.equalsIgnoreCase("c"))
			{
				return CrystalType.C.getCrystalId();
			}
			else if (s.equalsIgnoreCase("b"))
			{
				return CrystalType.B.getCrystalId();
			}
			else if (s.equalsIgnoreCase("a"))
			{
				return CrystalType.A.getCrystalId();
			}
			else if (s.equalsIgnoreCase("s"))
			{
				return CrystalType.S.getCrystalId();
			}
			else if (s.equalsIgnoreCase("s80"))
			{
				return CrystalType.S80.getCrystalId();
			}
			else
			{
				return s.equalsIgnoreCase("s84") ? CrystalType.S84.getCrystalId() : 0;
			}
		}
		else
		{
			return CrystalType.NONE.getCrystalId();
		}
	}
	
	@Override
	public Set<Integer> getAllWeaponsId()
	{
		return ItemTable.getInstance().getAllWeaponsId();
	}
	
	@Override
	public Set<Integer> getAllArmorsId()
	{
		return ItemTable.getInstance().getAllArmorsId();
	}
	
	@Override
	public Integer[] getAllClassIds()
	{
		List<Integer> idsList = new LinkedList<>();
		ClassId[] var2 = ClassId.values();
		int var3 = var2.length;
		for (int var4 = 0; var4 < var3; ++var4)
		{
			ClassId id = var2[var4];
			idsList.add(id.getId());
		}
		return idsList.toArray(new Integer[idsList.size()]);
	}
	
	@Override
	public PlayerEventInfo[] getAllPlayers()
	{
		List<PlayerEventInfo> eventInfos = new LinkedList<>();
		Iterator<PlayerInstance> var2 = World.getInstance().getPlayers().iterator();
		while (var2.hasNext())
		{
			PlayerInstance player = (PlayerInstance) var2.next();
			eventInfos.add(player.getEventInfo());
		}
		return eventInfos.toArray(new PlayerEventInfo[eventInfos.size()]);
	}
	
	@Override
	public ItemInstance dropItem(Object owner, int itemId, int count, int x, int y, int z, int enchant, int instanceId)
	{
		// Check if the item exists
		if (ItemTable.getInstance().getTemplate(itemId) == null)
		{
			if (GabrielEventsLoader.detailedDebug)
			{
				System.out.println("BattleRoyale: Failed to drop item ID " + itemId + " - Item template does not exist.");
			}
			return null;
		}
		// Create a new ItemInstance
		ItemInstance item = ItemTable.getInstance().createItem("EventDrop", itemId, count, null, null);
		if (item == null)
		{
			if (GabrielEventsLoader.detailedDebug)
			{
				System.out.println("BattleRoyale: Failed to create item ID " + itemId + " - Item creation returned null.");
			}
			return null;
		}
		// Apply enchant level to the item
		item.setEnchantLevel(enchant);
		// Mark the item as an "event item"
		item.setCustomType1(9999);

		// CRITICAL: Set instance ID BEFORE dropping to ensure proper world placement
		if (instanceId > 0)
		{
			item.setInstanceById(instanceId);
			if (GabrielEventsLoader.detailedDebug)
			{
				System.out.println("BattleRoyale: Set instance ID " + instanceId + " for item ID " + itemId + " before dropping.");
			}
		}
		else if (GabrielEventsLoader.detailedDebug)
		{
			System.out.println("BattleRoyale: Warning: instanceId is " + instanceId + " for item ID " + itemId + ". Dropping in default instance (0).");
		}

		// Drop the item on the ground (instance ID already set)
		item.dropMe(null, x, y, z);

		// CRITICAL: Force set instance ID AFTER dropping (dropMe may reset it)
		if (instanceId > 0)
		{
			// Always force set instance ID after dropMe to ensure it sticks
			item.setInstanceById(instanceId);

			// Verify final instance ID
			if (item.getInstanceId() == instanceId)
			{
				if (GabrielEventsLoader.detailedDebug)
				{
					System.out.println("BattleRoyale: Successfully dropped item ID " + itemId + " (count: " + item.getCount() + ", enchant: " + item.getEnchantLevel() + ") at (" + x + ", " + y + ", " + z + ") in instance " + instanceId);
				}
			}
			else
			{
				if (GabrielEventsLoader.detailedDebug)
				{
					System.out.println("BattleRoyale: CRITICAL ERROR: Item ID " + itemId + " still has wrong instance ID " + item.getInstanceId() + " after force setting to " + instanceId);
				}
			}
		}
		// Add to auto-destroy mechanism if not picked up
		if (!Config.LIST_PROTECTED_ITEMS.contains(itemId) && (((Config.AUTODESTROY_ITEM_AFTER > 0) && !item.getItem().hasExImmediateEffect()) || ((Config.HERB_AUTO_DESTROY_TIME > 0) && item.getItem().hasExImmediateEffect())))
		{
			ItemsAutoDestroy.getInstance().addItem(item);
		}
		// Do not protect the item (anyone can pick it up)
		item.setProtected(false);
		if (GabrielEventsLoader.detailedDebug)
		{
			System.out.println("BattleRoyale: Successfully dropped item ID " + itemId + " (count: " + count + ", enchant: " + enchant + ") at (" + x + ", " + y + ", " + z + ") in instance " + item.getInstanceId());
		}
		return item;
	}
	
	protected static final GabrielOut getInstance()
	{
		return SingletonHolder._instance;
	}
	
	private static class SingletonHolder
	{
		protected static final GabrielOut _instance = new GabrielOut();
		
		private SingletonHolder()
		{}
	}
}
