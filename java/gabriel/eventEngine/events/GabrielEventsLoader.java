package gabriel.eventEngine.events;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.logging.Level;
import java.util.logging.Logger;

import gabriel.eventEngine.events.engine.EventConfig;
import gabriel.eventEngine.events.engine.EventManager;
import gabriel.eventEngine.events.engine.EventMapSystem;
import gabriel.eventEngine.events.engine.EventRewardSystem;
import gabriel.eventEngine.events.engine.EventWarnings;
import gabriel.eventEngine.events.engine.lang.LanguageEngine;
import gabriel.eventEngine.events.engine.main.OldStats;
import gabriel.eventEngine.events.engine.main.base.MainEventInstanceTypeManager;
import gabriel.eventEngine.events.engine.stats.EventStatsManager;
import gabriel.eventEngine.interf.GabrielEvents;
import gabriel.eventEngine.interf.PlayerEventInfo;
import gabriel.eventEngine.playervalue.PlayerValueEngine;

public class GabrielEventsLoader
{
	private static final Logger _log;
	public static final String version = "2.21";
	private static FileWriter fileWriter;
	private static final SimpleDateFormat _toFileFormat;
	public static boolean detailedDebug;
	public static boolean detailedDebugToConsole;
	public static boolean logToFile;
	private static Branch _branch;
	private static double _interfaceVersion;
	private static String _desc;
	private static boolean _instances;
	private static String _libsFolder;
	private static boolean _limitedHtml;
	private static boolean loaded;
	private static boolean loading;
	private static boolean _gmsDebugging;
	private static Set<PlayerEventInfo> _gmsDebuggingSet;
	public static int DEBUG_CHAT_CHANNEL_CLASSIC;
	public static int DEBUG_CHAT_CHANNEL;
	private static File debugFile;
	private static File detailedDebugFile;
	
	public static final void init(final Branch l2branch, final double interfaceVersion, final String desc, final boolean allowInstances, final String libsFolder, final boolean limitedHtml, final boolean cracked)
	{
		GabrielEventsLoader._branch = l2branch;
		GabrielEventsLoader._interfaceVersion = interfaceVersion;
		GabrielEventsLoader._desc = desc;
		GabrielEventsLoader._libsFolder = libsFolder;
		startLoading(GabrielEventsLoader._branch, GabrielEventsLoader._interfaceVersion, GabrielEventsLoader._desc, allowInstances, GabrielEventsLoader._libsFolder, limitedHtml);
	}
	
	private static void startLoading(final Branch l2branch, final double interfaceVersion, final String desc, final boolean allowInstances, final String libsFolder, final boolean limitedHtml)
	{
		GabrielEventsLoader.loading = true;
		EventConfig.getInstance().loadGlobalConfigs();
		final String fileName = createDebugFile();
		if (fileName != null)
		{
			debug("Debug messages are stored in '" + fileName + "'");
		}
		debug("Thanks for using a legal version.");
		GabrielEventsLoader._desc = desc;
		GabrielEventsLoader._instances = allowInstances;
		GabrielEventsLoader._libsFolder = libsFolder;
		GabrielEventsLoader._limitedHtml = limitedHtml;
		debug("Loading engine version 2.2...");
		debug("Using " + GabrielEventsLoader._desc + " interface (for engine v" + interfaceVersion + ").");
		if (interfaceVersion != l2branch._newestVersion)
		{
			debug("Your interface is outdated for this engine!!! Please update it.", Level.SEVERE);
		}
		OldStats.getInstance();
		GabrielEvents.loadHtmlManager();
		GabrielEventsLoader.logToFile = EventConfig.getInstance().getGlobalConfigBoolean("logToFile");
		GabrielEventsLoader.detailedDebug = EventConfig.getInstance().getGlobalConfigBoolean("detailedDebug");
		GabrielEventsLoader.detailedDebugToConsole = EventConfig.getInstance().getGlobalConfigBoolean("detailedDebugToConsole");
		LanguageEngine.init();
		EventManager.getInstance();
		EventConfig.getInstance().loadEventConfigs();
		EventMapSystem.getInstance().loadMaps();
		EventRewardSystem.getInstance();
		EventManager.getInstance().getMainEventManager().loadScheduleData();
		MainEventInstanceTypeManager.getInstance();
		EventStatsManager.getInstance();
		EventWarnings.getInstance();
		PlayerValueEngine.getInstance();

		// Khởi động automatic scheduler sau khi tất cả đã load xong
		EventManager.getInstance().getMainEventManager().initializeAutoScheduler();

		GabrielEventsLoader.loaded = true;
		debug("Version 2.2 successfully loaded.");
	}
	
	public static final boolean isDebugging(final PlayerEventInfo gm)
	{
		return GabrielEventsLoader._gmsDebugging && GabrielEventsLoader._gmsDebuggingSet.contains(gm);
	}
	
	public static final void addGmDebug(final PlayerEventInfo gm)
	{
		if (!GabrielEventsLoader._gmsDebugging)
		{
			GabrielEventsLoader._gmsDebugging = true;
		}
		GabrielEventsLoader._gmsDebuggingSet.add(gm);
	}
	
	public static final void removeGmDebug(final PlayerEventInfo gm)
	{
		if (!GabrielEventsLoader._gmsDebugging)
		{
			return;
		}
		GabrielEventsLoader._gmsDebuggingSet.remove(gm);
		if (GabrielEventsLoader._gmsDebuggingSet.isEmpty())
		{
			GabrielEventsLoader._gmsDebugging = false;
		}
	}
	
	public static final void debug(String msg, final Level level)
	{
		msg = "Event Engine: " + msg;
		final String value = String.valueOf(level);
		switch (value)
		{
			case "INFO":
			{
				GabrielEventsLoader._log.info(msg);
				break;
			}
			case "WARNING":
			{
				GabrielEventsLoader._log.warning(msg);
				break;
			}
			case "SEVERE":
			{
				GabrielEventsLoader._log.log(Level.SEVERE, msg);
				break;
			}
		}
		if (GabrielEventsLoader._gmsDebugging)
		{
			sendToGms(msg, level, false);
		}
		writeToFile(level, msg, false);
	}
	
	public static final void debug(String msg)
	{
		if (!msg.startsWith("Nexus ") && !msg.startsWith("nexus"))
		{
			msg = "Event Engine: " + msg;
		}
		GabrielEventsLoader._log.info(msg);
		if (GabrielEventsLoader._gmsDebugging)
		{
			sendToGms(msg, Level.INFO, false);
		}
		writeToFile(Level.INFO, msg, false);
	}
	
	public static final void sendToGms(final String msg, final Level level, final boolean detailed)
	{
		try
		{
			for (final PlayerEventInfo gm : GabrielEventsLoader._gmsDebuggingSet)
			{
				gm.creatureSay("*" + (detailed ? msg : msg.substring(14)) + "  (" + level.toString() + ")", detailed ? "DD" : "DEBUG", detailed ? GabrielEventsLoader.DEBUG_CHAT_CHANNEL : GabrielEventsLoader.DEBUG_CHAT_CHANNEL_CLASSIC);
			}
		}
		catch (Exception e)
		{
			e.printStackTrace();
		}
	}
	
	public static final void detailedDebug(String msg)
	{
		if (!msg.startsWith("DD "))
		{
			msg = "DD:  " + msg;
		}
		if (GabrielEventsLoader._gmsDebugging)
		{
			sendToGms(msg, Level.INFO, true);
		}
		writeToFile(Level.INFO, msg, true);
	}
	
	public static final boolean allowInstances()
	{
		return GabrielEventsLoader._instances;
	}
	
	public static final String getLibsFolderName()
	{
		return GabrielEventsLoader._libsFolder;
	}
	
	public static final boolean isLimitedHtml()
	{
		return GabrielEventsLoader._limitedHtml;
	}
	
	private static final String createDebugFile()
	{
		String path = "log/EventEngine";
		final File folder = new File(path);
		if (!folder.exists() && !folder.mkdir())
		{
			path = "log";
		}
		GabrielEventsLoader.debugFile = new File(path + "/EventEngine.log");
		if (!GabrielEventsLoader.debugFile.exists())
		{
			try
			{
				GabrielEventsLoader.debugFile.createNewFile();
			}
			catch (IOException e)
			{
				e.printStackTrace();
			}
		}
		int id = 0;
		for (final File f : folder.listFiles())
		{
			if (f.getName().startsWith("EventEngine_detailed"))
			{
				try
				{
					final String name = f.getName().substring(0, f.getName().length() - 4);
					final int id2 = Integer.getInteger(name.substring(21));
					if (id2 > id)
					{
						id = id2;
					}
				}
				catch (Exception ex)
				{
				}
			}
		}
		++id;
		GabrielEventsLoader.detailedDebugFile = new File(path + "/EventEngine_detailed_" + id + ".log");
		if (GabrielEventsLoader.detailedDebugFile.exists())
		{
			try
			{
				GabrielEventsLoader.detailedDebugFile.delete();
			}
			catch (Exception e2)
			{
				e2.printStackTrace();
			}
		}
		if (!GabrielEventsLoader.detailedDebugFile.exists())
		{
			try
			{
				GabrielEventsLoader.detailedDebugFile.createNewFile();
			}
			catch (Exception e2)
			{
				e2.printStackTrace();
			}
		}
		return GabrielEventsLoader.detailedDebugFile.getPath();
	}
	
	public static void writeToFile(final Level level, final String msg, final boolean detailed)
	{
		if (!detailed && !GabrielEventsLoader.logToFile)
		{
			return;
		}
		try
		{
			if (!detailed)
			{
				GabrielEventsLoader.fileWriter = new FileWriter(GabrielEventsLoader.debugFile, true);
			}
			else
			{
				GabrielEventsLoader.fileWriter = new FileWriter(GabrielEventsLoader.detailedDebugFile, true);
			}
			GabrielEventsLoader.fileWriter.write(GabrielEventsLoader._toFileFormat.format(new Date()) + ":  " + msg + " (" + level.getLocalizedName() + ")\r\n");
		}
		catch (Exception ex)
		{
		}
		finally
		{
			try
			{
				GabrielEventsLoader.fileWriter.close();
			}
			catch (Exception ex2)
			{
			}
		}
	}
	
	public static String getTraceString(final StackTraceElement[] trace)
	{
		final StringBuilder sbString = new StringBuilder();
		for (final StackTraceElement element : trace)
		{
			sbString.append(element.toString()).append("\n");
		}
		final String result = sbString.toString();
		return result;
	}
	
	public static void shutdown()
	{
		EventWarnings.getInstance().saveData();
	}
	
	public static boolean loaded()
	{
		return GabrielEventsLoader.loaded;
	}
	
	public static boolean loadedOrBeingLoaded()
	{
		return GabrielEventsLoader.loading;
	}
	
	static
	{
		_log = Logger.getLogger("gabriel");
		_toFileFormat = new SimpleDateFormat("dd/MM/yyyy H:mm:ss");
		GabrielEventsLoader.detailedDebug = false;
		GabrielEventsLoader.detailedDebugToConsole = false;
		GabrielEventsLoader.logToFile = false;
		GabrielEventsLoader.loaded = false;
		GabrielEventsLoader.loading = false;
		GabrielEventsLoader._gmsDebugging = false;
		GabrielEventsLoader._gmsDebuggingSet = ConcurrentHashMap.newKeySet();
		GabrielEventsLoader.DEBUG_CHAT_CHANNEL_CLASSIC = 7;
		GabrielEventsLoader.DEBUG_CHAT_CHANNEL = 6;
	}
	
	public enum Branch
	{
		Freya(2.1),
		Hi5(2.1),
		Hi5Priv(2.1),
		Final(2.1);
		
		public double _newestVersion;
		
		private Branch(final double interfaceVersion)
		{
			this._newestVersion = interfaceVersion;
		}
	}
}
