package gabriel.eventEngine.events.engine.mini.features;


import gabriel.eventEngine.events.engine.base.EventType;
import gabriel.eventEngine.events.engine.mini.EventMode;
import gabriel.eventEngine.interf.PlayerEventInfo;


public class TeamSizeFeature extends AbstractFeature
{
    private int teamSize;
    
    public TeamSizeFeature(final EventType event, final PlayerEventInfo gm, String parametersString) {
        super(event);
        this.teamSize = 5;
        this.addConfig("TeamSize", "The ammount of players in one team in matches started under this mode (overrides the value from general configs). The value must be > 1 otherwise this config will be ignored.", 1);
        if (parametersString == null) {
            parametersString = "5";
        }
        this._params = parametersString;
        this.initValues();
    }
    
    @Override
    protected void initValues() {
        final String[] params = this.splitParams(this._params);
        try {
            this.teamSize = Integer.parseInt(params[0]);
        }
        catch (NumberFormatException e) {
            e.printStackTrace();
        }
    }
    
    public int getTeamSize() {
        return this.teamSize;
    }
    
    @Override
    public boolean checkPlayer(final PlayerEventInfo player) {
        return true;
    }
    
    @Override
    public EventMode.FeatureType getType() {
        return EventMode.FeatureType.TeamSize;
    }
}
