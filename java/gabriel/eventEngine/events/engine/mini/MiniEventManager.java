package gabriel.eventEngine.events.engine.mini;

import java.util.Comparator;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.logging.Logger;

import gabriel.eventEngine.events.Configurable;
import gabriel.eventEngine.events.engine.EventBuffer;
import gabriel.eventEngine.events.engine.EventConfig;
import gabriel.eventEngine.events.engine.EventManager;
import gabriel.eventEngine.events.engine.EventMapSystem;
import gabriel.eventEngine.events.engine.EventWarnings;
import gabriel.eventEngine.events.engine.base.ConfigModel;
import gabriel.eventEngine.events.engine.base.Event;
import gabriel.eventEngine.events.engine.base.EventMap;
import gabriel.eventEngine.events.engine.base.EventType;
import gabriel.eventEngine.events.engine.base.RewardPosition;
import gabriel.eventEngine.events.engine.base.SpawnType;
import gabriel.eventEngine.events.engine.lang.LanguageEngine;
import gabriel.eventEngine.events.engine.mini.features.AbstractFeature;
import gabriel.eventEngine.events.engine.mini.features.DelaysFeature;
import gabriel.eventEngine.events.engine.mini.features.StrenghtChecksFeature;
import gabriel.eventEngine.interf.PlayerEventInfo;
import gabriel.eventEngine.interf.delegate.PartyData;
import gabriel.eventEngine.l2j.CallBack;
import gabriel.eventEngine.l2j.util.Tools;

public abstract class MiniEventManager extends Event implements Runnable, Configurable {
	protected static Logger _log = Logger.getLogger(MiniEventManager.class.getName());
	protected List<RegistrationData> _parties;
	protected int _lastGameId;
	protected List<MiniEventGame> _games;
	protected Map<Integer, Long> _loggedPlayers;
	protected boolean _locked;
	protected boolean _canRun;
	protected boolean _tournamentActive;
	protected EventMode _mode;
	private final Map<String, ConfigModel> _configs;
	private final Map<String, ConfigModel> _mapConfigs;
	private final List<String> _configCategories;
	protected String _htmlDescription = null;
	@SuppressWarnings("unused")
	private Comparator<RegistrationData> _compareByLevel = new Comparator<RegistrationData>() {
		
		@Override
		public int compare(RegistrationData p1, RegistrationData p2) {
			int level1 = p1.getAverageLevel();
			int level2 = p2.getAverageLevel();
			
			return level1 == level2 ? 0 : level1 < level2 ? -1 : 1;
		}
	};
	
	public MiniEventManager(EventType type) {
		super(type);
		_tournamentActive = false;
		_parties = new CopyOnWriteArrayList<RegistrationData>();
		_games = new LinkedList<MiniEventGame>();
		_loggedPlayers = new ConcurrentHashMap<Integer, Long>();
		_mode = new EventMode(getEventType());
		_configs = new ConcurrentHashMap<String, ConfigModel>();
		_mapConfigs = new ConcurrentHashMap<String, ConfigModel>();
		_configCategories = new LinkedList<String>();
		loadConfigs();
		_lastGameId = 0;
		_canRun = false;
	}
	
	@Override
	public void loadConfigs() {
		addConfig(new ConfigModel("DelayToWaitSinceLastMatchMs", "600000", "The delay the player has to wait to join this event again, after the his last event ended. In miliseconds."));
		addConfig(new ConfigModel("TimeLimitMs", "600000", "The delay after the match will be automatically aborted. In ms (miliseconds)."));
		addConfig(new ConfigModel("MaxLevelDifference", "5", "Maximum level difference between opponents in the event."));
		addConfig(new ConfigModel("MinLevelToJoin", "0", "Minimum level for players participating the event (playerLevel >= value)."));
		addConfig(new ConfigModel("MaxLevelToJoin", "100", "Maximum level for players participating the event (playerLevel <= value)."));
		addConfig(new ConfigModel("notAllowedSkills", "", "Put here skills that won't be available for use in this event <font color=7f7f7f>(write one skill's ID and click Add, to remove the skill, simply click on it's ID in the list)</font>", ConfigModel.InputType.MultiAdd));
		addConfig(new ConfigModel("notAllowedItems", "", "Put here items that won't be available for use in this event <font color=7f7f7f>(write one skill's ID and click Add; to remove the skill, simply click on it's ID in the list)</font>", ConfigModel.InputType.MultiAdd));
		addConfig(new ConfigModel("setOffensiveSkills", "", "Skills written here will be usable only on player's opponents/enemies (not teammates) during events. <font color=7f7f7f>(write one skill's ID and click Add; to remove the skill, simply click on it's ID in the list)</font>", ConfigModel.InputType.MultiAdd));
		addConfig(new ConfigModel("setNotOffensiveSkills", "", "Skills written here will be usable only on player's teammates (not opponents/enemies) during events. <font color=7f7f7f>(write one skill's ID and click Add; to remove the skill, simply click on it's ID in the list).", ConfigModel.InputType.MultiAdd));
		addConfig(new ConfigModel("setNeutralSkills", "994", "Skills written here will be usable on both teammates and enemies Useful for example for skill Rush (ID 994), which is by default not offensive, and thus the engine doesn't allow the player to cast it on his opponent <font color=7f7f7f>(write one skill's ID and click Add; to remove the skill, simply click on it's ID in the list)</font>", ConfigModel.InputType.MultiAdd));
		addConfig(new ConfigModel("allowPotions", "false", "Put false if you want to disable potions on this event.", ConfigModel.InputType.Boolean));
		addConfig(new ConfigModel("allowSummons", "true", "Put false if you want to disable summons on this event.", ConfigModel.InputType.Boolean));
		addConfig(new ConfigModel("allowPets", "true", "Put false if you want to disable pets on this event.", ConfigModel.InputType.Boolean));
		addConfig(new ConfigModel("allowHealers", "true", "Put false if you want to disable healers/buffers on this event.", ConfigModel.InputType.Boolean));
		addConfig(new ConfigModel("removeCubics", "false", "Put true to remove cubics upon teleportation to the event.", ConfigModel.InputType.Boolean));
		addConfig(new ConfigModel("dualboxCheckForEnemies", "true", "If enabled, only players with different IPs can be enemies in this event.", ConfigModel.InputType.Boolean));
		addConfig(new ConfigModel("maxPlayersPerIp", "1", "You can specify here how many players with the same IP are allowed to be in the event. Put -1 to disable this feature."));
		addConfig(new ConfigModel("removeBuffsOnStart", "true", "If 'true', all buffs will be removed from players on first teleport to the event.", ConfigModel.InputType.Boolean));
		addConfig(new ConfigModel("removeBuffsOnRespawn", "false", "If 'true', all buffs will be removed from players when they respawn (or when the next round starts).", ConfigModel.InputType.Boolean));
	}
	
	public void setConfigs(Configurable template) {
		try {
			for (ConfigModel templateModel : template.getConfigs().values()) {
				for (ConfigModel thisModel : getConfigs().values()) {
					if (templateModel.getKey().equals(thisModel.getKey())) {
						thisModel.setValue(templateModel.getValue());
						break;
					}
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	
	public abstract boolean checkCanFight(PlayerEventInfo gm, RegistrationData[] teams);
	
	public void check() {
		if (!checkCanRun()) {
			cleanMe(false);
			CallBack.getInstance().getOut().scheduleGeneral(() -> check(), 30000L);
			return;
		}
		if (getStartGameInterval() > 0) {
			CallBack.getInstance().getOut().scheduleGeneral(() -> createGame(), getStartGameInterval());
		}
	}
	
	public boolean checkCanRun() {
		int workingMapsCount = 0;
		for (EventMap map : EventMapSystem.getInstance().getMaps(getEventType()).values()) {
			if (!_mode.getDisMaps().contains(map.getGlobalId()) && canRun(map)) {
				workingMapsCount++;
			}
		}
		return _canRun = (workingMapsCount > 0);
	}
	
	@Override
	public void run() {
		check();
	}
	
	public void createGame() {
	}
	
	protected RegistrationData findOpponent(RegistrationData team) {
		for (RegistrationData opponent : _parties) {
			if (!opponent.isChosen() && opponent.getKeyPlayer().getPlayersId() != team.getKeyPlayer().getPlayersId() && strenghtChecks(team, opponent) && ipChecks(team, opponent)) {
				return opponent;
			}
		}
		return null;
	}
	
	public boolean launchGame(RegistrationData[] teams, EventMap map) {
		return false;
	}
	
	public void cleanMe(boolean abortMatches) {
		_locked = true;
		if (abortMatches) {
			for (MiniEventGame game : _games) {
				game.abortDueToError(LanguageEngine.getMsg("game_aborted"));
			}
		}
		for (RegistrationData data : _parties) {
			data.message(LanguageEngine.getMsg("game_unregistered", getEventName()), false);
			data.register(false, null);
		}
		_games.clear();
		_parties.clear();
		_loggedPlayers.clear();
		_locked = false;
	}
	
	protected int getStartGameInterval() {
		return 30000;
	}
	
	public int getDefaultPartySizeToJoin() {
		return 5;
	}
	
	protected int getNextGameId() {
		return ++_lastGameId;
	}
	
	public int getJoinTimeRestriction() {
		for (AbstractFeature f : _mode.getFeatures()) {
			if (f.getType() == EventMode.FeatureType.Delays) {
				return ((DelaysFeature) f).getRejoinDealy();
			}
		}
		return getInt("DelayToWaitSinceLastMatchMs");
	}
	
	// ==========================================================================
	// REGISTRATION, ETC
	// ==========================================================================
	
	public boolean checkCanRegister(PlayerEventInfo player) {
		if (player == null)
			return false;
		
		if (!EventManager.getInstance().canRegister(player)) {
			player.sendMessage(LanguageEngine.getMsg("registering_status"));
			return false;
		}
		
		if (player.isRegistered()) // TODO main events check
		{
			player.sendMessage(LanguageEngine.getMsg("registering_alreadyRegistered"));
			return false;
		}
		
		// if (!player.getCharacterData().getOwner().destroyItemByItemId("Mini Event Fee", 57, 100000, null, true))
		// {
		// // player.sendMessage("You do not have enough Adena to register for the mini event");
		// player.getCharacterData().getOwner().sendPacket(SystemMessageId.YOU_DO_NOT_HAVE_ENOUGH_ADENA);
		// return false;
		// }
		
		int i = EventWarnings.getInstance().getPoints(player);
		if (i >= EventWarnings.MAX_WARNINGS) {
			player.sendMessage(LanguageEngine.getMsg("registering_warningPoints", EventWarnings.MAX_WARNINGS, i));
			return false;
		}
		
		if (!_mode.checkPlayer(player)) {
			player.sendMessage(LanguageEngine.getMsg("registering_notAllowed"));
			return false;
		}
		
		int playerLevel = player.getLevel();
		int maxLevel = getInt("MaxLevelToJoin");
		int minLevel = getInt("MinLevelToJoin");
		if (playerLevel < minLevel || playerLevel > maxLevel) {
			if (playerLevel < minLevel)
				player.sendMessage(LanguageEngine.getMsg("registering_lowLevel"));
			else
				player.sendMessage(LanguageEngine.getMsg("registering_highLevel"));
			return false;
		}
		
		if (!allowHealers() && player.isPriest()) {
			player.sendMessage("Healers cannot register to this event.");
			return false;
		}
		
		// should never happen to players, maybe with very high ammount of players registered
		if (isTemporaryLocked()) {
			player.sendMessage("Try it again in few seconds. If this thing keeps showing up, then there's propably something fucked up with this event, contact a GameMaster for fix.");
			return false;
		}
		
		if (!timeChecks(player)) {
			player.sendMessage(LanguageEngine.getMsg("registering_timeCheckFailed"));
			return false;
		}
		
		// Check HWID first if enabled, otherwise fall back to IP check
		if (getBoolean("hwidCheck")) {
			if (!hwidChecks(player)) {
				return false;
			}
		} else {
			if (!ipChecks2(player)) {
				return false;
			}
		}
		
		if (requireParty()) // party checks
		{
			if (player.getParty() == null) {
				player.sendMessage("You must have a party to join the event.");
				return false;
			} else {
				if (player.getParty().getLeadersId() != player.getPlayersId()) {
					player.sendMessage(LanguageEngine.getMsg("registering_partyLeader"));
					return false;
				} else if (player.getParty().getMemberCount() != getDefaultPartySizeToJoin()) {
					player.sendMessage(LanguageEngine.getMsg("registering_partyMembers", getDefaultPartySizeToJoin()));
					return false;
				} else {
					if (!checkPartyStatus(player.getParty())) {
						player.sendMessage(LanguageEngine.getMsg("registering_partyCantRegister"));
						return false;
					}
				}
			}
		}
		
		return true;
	}
	
	public boolean registerTeam(PlayerEventInfo player) {
		if (!checkCanRegister(player)) {
			return false;
		}
		
		if (EventConfig.getInstance().getGlobalConfigBoolean("eventSchemeBuffer")) {
			if (!EventBuffer.getInstance().hasBuffs(player)) {
				player.sendMessage(LanguageEngine.getMsg("registering_buffs"));
			}
			EventManager.getInstance().getHtmlManager().showSelectSchemeForEventWindow(player, "mini", getEventType().getAltTitle());
		}
		
		return true;
	}
	
	protected void addParty(RegistrationData playerData) {
		synchronized (_parties) {
			_parties.add(playerData);
		}
	}
	
	public boolean checkCanUnregisterTeam(PlayerEventInfo player) {
		if (player == null || !player.isOnline()) {
			return false;
		}
		if (player.getRegisteredMiniEvent() == null || player.getRegisteredMiniEvent().getEventType() != getEventType()) {
			player.sendMessage(LanguageEngine.getMsg("unregistering_notRegistered"));
			return false;
		} else {
			if (_locked) {
				player.sendMessage("Try it again in few seconds. If this thing keeps showing up, then there's propably something fucked up with this event, contact GameMaster for fix.");
				return false;
			}
		}
		if (requireParty()) {
			if (player.getParty() == null) {
				player.sendMessage(LanguageEngine.getMsg("registering_noParty"));
				return false;
			}
			if (player.getParty().getLeadersId() != player.getPlayersId()) {
				player.sendMessage(LanguageEngine.getMsg("registering_partyLeader_unregister"));
				return false;
			}
		}
		return true;
	}
	
	public boolean unregisterTeam(PlayerEventInfo player) {
		return checkCanUnregisterTeam(player);
	}
	
	public void deleteTeam(RegistrationData team) {
		team.message(LanguageEngine.getMsg("unregistering_unregistered2", getEventType().getHtmlTitle()), false);
		team.register(false, null);
		
		synchronized (_parties) {
			_parties.remove(team);
		}
	}
	
	private boolean checkPartyStatus(PartyData party) {
		final boolean buffs = EventConfig.getInstance().getGlobalConfigBoolean("eventSchemeBuffer");
		for (PlayerEventInfo member : party.getPartyMembers()) {
			if (member != null) {
				if (member.isRegistered()) {
					party.getLeader().sendMessage(LanguageEngine.getMsg("registering_party_memberAlreadyRegistered", member.getPlayersName()));
					return false;
				}
				if (!timeChecks(member)) {
					party.getLeader().sendMessage(LanguageEngine.getMsg("registering_party_timeCheckFail", member.getPlayersName()));
					return false;
				}
				if (!allowHealers() && member.isPriest()) {
					party.getLeader().sendMessage(LanguageEngine.getMsg("registering_party_noHealer"));
					return false;
				} else if (!checkPlayer(member)) {
					party.getLeader().sendMessage("Player " + member.getPlayersName() + " cannot register.");
					return false;
				}
				
				if (buffs) {
					if (!EventBuffer.getInstance().hasBuffs(member)) {
						member.sendMessage(LanguageEngine.getMsg("registering_buffs"));
					}
					EventManager.getInstance().getHtmlManager().showSelectSchemeForEventWindow(member, "mini", getEventType().getAltTitle());
				}
			}
		}
		return true;
	}
	
	protected boolean timeChecks(PlayerEventInfo player) {
		final int delay = getJoinTimeRestriction();
		int id = player.getPlayersId();
		long time = System.currentTimeMillis();
		for (Entry<Integer, Long> e : _loggedPlayers.entrySet()) {
			if (e.getKey() == id) {
				if (time - delay > e.getValue()) {
					_loggedPlayers.remove(e.getKey());
					return true;
				} else if (!player.isGM()) {
					player.sendMessage(LanguageEngine.getMsg("registering_timeCheckFail", (e.getValue() + delay - time) / 60000L));
					return false;
				}
				return true;
			}
		}
		return true;
	}
	
	public int getDelayHaveToWaitToJoinAgain(PlayerEventInfo player) {
		final int delay = getJoinTimeRestriction();
		int id = player.getPlayersId();
		long time = System.currentTimeMillis();
		for (Entry<Integer, Long> e : _loggedPlayers.entrySet()) {
			if (e.getKey() == id) {
				if (time - delay > e.getValue()) {
					_loggedPlayers.remove(e.getKey());
					return 0;
				}
				return (int) (e.getValue() - (time - delay));
			}
		}
		return 0;
	}
	
	public boolean canFight(RegistrationData data) {
		boolean canFight = true;
		
		int playersAmmount = 0;
		
		// party leader is offline
		if (!data.getKeyPlayer().isOnline(true)) // party leader / key player must be online
		{
			canFight = false;
		}
		
		if (!checkPlayer(data.getKeyPlayer())) {
			canFight = false;
		}
		
		if (requireParty()) {
			// party was deleted
			if (data.getParty() == null) {
				canFight = false;
			}
			
			if (data.getParty().getMemberCount() > getDefaultPartySizeToJoin()) {
				data.message(LanguageEngine.getMsg("unregistering_unregistered_partyBig", getEventType().getHtmlTitle()), false);
				canFight = false;
			}
			
			for (PlayerEventInfo pi : data.getPlayers()) {
				if (!pi.isOnline(true)) {
					canFight = false;
				} else {
					// registered on another event
					if (!pi.isRegistered() || pi.getRegisteredMiniEvent().getEventType() != getEventType()) {
						data.getPlayers().remove(pi);
						data.getKeyPlayer().sendMessage(LanguageEngine.getMsg("unregistering_memberKicked_anotherEvent", pi.getPlayersName()));
						continue;
					}
					
					// not in party
					if (pi.getParty() == null || pi.getParty().getLeadersId() != data.getParty().getLeadersId()) {
						data.getPlayers().remove(pi);
						data.getKeyPlayer().sendMessage(LanguageEngine.getMsg("unregistering_memberKicked_leftParty", pi.getPlayersName()));
						continue;
					}
					
					if (!checkPlayer(pi)) {
						data.getPlayers().remove(pi);
						data.getKeyPlayer().sendMessage(LanguageEngine.getMsg("unregistering_memberKicked", pi.getPlayersName()));
					}
					
					playersAmmount++;
				}
			}
			
			if (playersAmmount < getDefaultPartySizeToJoin() / 2) {
				canFight = false;
			}
		}
		
		return canFight;
	}
	
	protected void removeInactiveTeams() {
		for (RegistrationData data : _parties) {
			if (!canFight(data)) {
				deleteTeam(data);
			}
		}
	}
	
	private boolean checkPlayer(PlayerEventInfo pi) {
		if (!EventManager.getInstance().canRegister(pi)) {
			pi.sendMessage(LanguageEngine.getMsg("unregistering_unregistered"));
			return false;
		}
		if (!_mode.checkPlayer(pi)) {
			pi.sendMessage(LanguageEngine.getMsg("unregistering_unregistered"));
			return false;
		}
		if (!allowHealers() && pi.isPriest()) {
			pi.sendMessage(LanguageEngine.getMsg("unregistering_memberKicked"));
			return false;
		}
		return true;
	}
	
	protected boolean strenghtChecks(RegistrationData t1, RegistrationData t2) {
		for (AbstractFeature feature : getMode().getFeatures()) {
			if (feature.getType() == EventMode.FeatureType.StrenghtChecks) {
				return ((StrenghtChecksFeature) feature).canFight(t1, t2);
			}
		}
		return Math.abs(t1.getAverageLevel() - t2.getAverageLevel()) <= getMaxLevelDifference();
	}
	
	protected boolean ipChecks(RegistrationData p1, RegistrationData p2) {
		if (getBoolean("dualboxCheckForEnemies")) {
			for (final PlayerEventInfo player : p1.getPlayers()) {
				if (player != null && player.isOnline() && !player.isGM()) {
					for (final PlayerEventInfo player2 : p2.getPlayers()) {
						if (player2 != null && player2.isOnline() && !player2.isGM() && Tools.isDualBox(player, player2)) {
							if (p1.getPlayers().size() > 1) {
								p1.message("Player " + player.getPlayersName() + " has the same IP as someone in " + p2.getKeyPlayer().getPlayersName() + "'s team.", false);
								p2.message("Player " + player2.getPlayersName() + " has the same IP as someone in " + p1.getKeyPlayer().getPlayersName() + "'s team.", false);
							} else {
								p1.message("Your IP appears to be same as " + p2.getKeyPlayer().getPlayersName() + "'s IP. You can't go against him.", false);
								p2.message("Your IP appears to be same as " + p1.getKeyPlayer().getPlayersName() + "'s IP. You can't go against him.", false);
							}
							return false;
						}
					}
				}
			}
		}
		return true;
	}
	
	protected boolean ipChecks2(PlayerEventInfo player) {
		final int i = getInt("maxPlayersPerIp");
		if (i == -1 || player.isGM()) {
			return true;
		}
		if (!player.isOnline(true)) {
			return false;
		}
		int occurences = 0;
		if (i <= 1) {
			for (final RegistrationData data : _parties) {
				for (final PlayerEventInfo p : data.getPlayers()) {
					if (Tools.isDualBox(player, p)) {
						return false;
					}
				}
			}
		} else {
			final String ip1 = player.getIp();
			if (ip1 == null) {
				return false;
			}
			for (final RegistrationData data2 : _parties) {
				for (final PlayerEventInfo p2 : data2.getPlayers()) {
					if (p2 != null && p2.isOnline() && ip1.equals(p2.getIp())) {
						++occurences;
					}
				}
			}
		}
		if (occurences >= i) {
			player.sendMessage("There is already " + i + " players using your IP. You may not register. Try it again later.");
			return false;
		}
		return true;
	}

	/**
	 * Check HWID restrictions for mini events
	 * @param player The player to check
	 * @return true if player can register, false if HWID limit exceeded
	 */
	protected boolean hwidChecks(PlayerEventInfo player) {
		final int maxPerHwid = getInt("maxPlayersPerHwid");
		if (maxPerHwid == -1 || player.isGM()) {
			return true;
		}
		if (!player.isOnline(true)) {
			return false;
		}

		// Get player's HWID
		String playerHwid = getPlayerHwid(player);
		if (playerHwid == null) {
			return false; // If no HWID, deny registration for security
		}

		int occurences = 0;
		if (maxPerHwid <= 1) {
			// Strict check - no duplicates allowed
			for (final RegistrationData data : _parties) {
				for (final PlayerEventInfo p : data.getPlayers()) {
					String otherHwid = getPlayerHwid(p);
					if (otherHwid != null && playerHwid.equals(otherHwid)) {
						player.sendMessage("There is already a player using your HWID in this event. You may not register.");
						return false;
					}
				}
			}
		} else {
			// Count occurrences
			for (final RegistrationData data : _parties) {
				for (final PlayerEventInfo p : data.getPlayers()) {
					String otherHwid = getPlayerHwid(p);
					if (otherHwid != null && playerHwid.equals(otherHwid)) {
						++occurences;
					}
				}
			}
		}

		if (occurences >= maxPerHwid) {
			player.sendMessage("There is already " + maxPerHwid + " players using your HWID. You may not register. Try it again later.");
			return false;
		}
		return true;
	}

	/**
	 * Get player's HWID (MAC Address) for mini events
	 * @param player The player
	 * @return HWID string or null if not available
	 */
	private String getPlayerHwid(final PlayerEventInfo player) {
		try {
			if (player.getOwner() != null && player.getOwner().getClient() != null) {
				if (player.getOwner().getClient().getHardwareInfo() != null) {
					return player.getOwner().getClient().getHardwareInfo().getMacAddress();
				}
			}
		} catch (Exception e) {
			// Silent fail for mini events
		}
		return null;
	}
	
	public void logPlayer(PlayerEventInfo pi, int position) {
		long time = System.currentTimeMillis();
		int rejoin = getJoinTimeRestriction() / 60000;
		if (position > 1) {
			time -= getJoinTimeRestriction() / position;
			rejoin /= position;
		}
		_loggedPlayers.put(pi.getPlayersId(), time);
		if (pi.isOnline()) {
			pi.sendMessage(LanguageEngine.getMsg("game_delayMsg", rejoin));
		}
	}
	
	public void notifyDisconnect(PlayerEventInfo player) {
	}
	
	public EventMode getMode() {
		return _mode;
	}
	
	public boolean isTemporaryLocked() {
		return _locked;
	}
	
	public void setIsTemporaryLocked(boolean b) {
		_locked = b;
	}
	
	public final void notifyGameEnd(MiniEventGame game) {
		_games.remove(game);
	}
	
	public String getString(String propName) {
		if (_configs.containsKey(propName)) {
			final String value = _configs.get(propName).getValue();
			return value;
		}
		debug("Wrong String config for event " + getEventType().getAltTitle() + ", name " + propName);
		return "";
	}
	
	public int getInt(String propName) {
		if (_configs.containsKey(propName)) {
			final int value = _configs.get(propName).getValueInt();
			return value;
		}
		debug("Wrong int config for event " + getEventType().getAltTitle() + ", name " + propName);
		return 0;
	}
	
	public boolean getBoolean(String propName) {
		if (_configs.containsKey(propName)) {
			return _configs.get(propName).getValueBoolean();
		}
		debug("Wrong boolean config for event " + getEventType().getAltTitle() + ", name " + propName);
		return false;
	}
	
	protected void addConfig(ConfigModel model) {
		_configs.put(model.getKey(), model);
	}
	
	protected void removeConfig(String key) {
		_configs.remove(key);
	}
	
	protected void addConfig(String category, ConfigModel model) {
		if (!_configCategories.contains(category)) {
			_configCategories.add(category);
		}
		_configs.put(model.getKey(), model.setCategory(category));
	}
	
	protected void addMapConfig(ConfigModel model) {
		_mapConfigs.put(model.getKey(), model);
	}
	
	protected void removeMapConfigs() {
		_mapConfigs.clear();
	}
	
	protected void removeConfigs() {
		_configCategories.clear();
		_configs.clear();
	}
	
	@Override
	public Map<String, ConfigModel> getConfigs() {
		return _configs;
	}
	
	@Override
	public void clearConfigs() {
		removeConfigs();
		removeMapConfigs();
	}
	
	@Override
	public List<String> getCategories() {
		return _configCategories;
	}
	
	@Override
	public void setConfig(final String key, final String value, final boolean addToValue) {
		if (!_configs.containsKey(key)) {
			return;
		}
		if (!addToValue) {
			_configs.get(key).setValue(value);
		} else {
			_configs.get(key).addToValue(value);
		}
	}
	
	@Override
	public Map<String, ConfigModel> getMapConfigs() {
		return _mapConfigs;
	}
	
	public boolean canRun() {
		return _canRun;
	}
	
	@Override
	public boolean canRun(EventMap map) {
		return getMissingSpawns(map).length() == 0;
	}
	
	protected String addMissingSpawn(SpawnType type, int team, int count) {
		return "<font color=bfbfbf>" + getMode().getModeName() + " </font><font color=696969>mode</font> -> <font color=9f9f9f>No</font> <font color=B46F6B>" + type.toString().toUpperCase() + "</font> <font color=9f9f9f>spawn for team " + team + " " + ((team == 0) ? "(team doesn't matter)" : "")
			+ " count " + count + " (or more)</font><br1>";
	}
	
	public String getMapConfig(EventMap map, String name) {
		return EventConfig.getInstance().getMapConfig(map, getEventType(), name);
	}
	
	public int getMapConfigInt(EventMap map, String name) {
		return EventConfig.getInstance().getMapConfigInt(map, getEventType(), name);
	}
	
	public boolean getMapConfigBoolean(EventMap map, String name) {
		return EventConfig.getInstance().getMapConfigBoolean(map, getEventType(), name);
	}
	
	protected int getMaxLevelDifference() {
		return getInt("MaxLevelDifference");
	}
	
	@Override
	public String getDescriptionForReward(RewardPosition reward) {
		return null;
	}
	
	public boolean isTournamentActive() {
		return _tournamentActive;
	}
	
	public void setTournamentActive(boolean b) {
		_tournamentActive = b;
	}
	
	public abstract String getHtmlDescription();
	
	public abstract RegistrationData createRegistrationData(PlayerEventInfo player);
	
	public List<MiniEventGame> getActiveGames() {
		return _games;
	}
	
	public int getRegisteredTeamsCount() {
		if (_parties == null) {
			return 0;
		}
		return _parties.size();
	}
	
	public List<RegistrationData> getRegistered() {
		return _parties;
	}
	
	@Override
	public String getEventName() {
		return getEventType().getAltTitle();
	}
	
	public boolean requireParty() {
		return true;
	}
	
	public boolean allowTournament() {
		return true;
	}
	
	public int getMaxGamesCount() {
		return 99;
	}
	
	protected boolean allowHealers() {
		return getBoolean("allowHealers");
	}
}
