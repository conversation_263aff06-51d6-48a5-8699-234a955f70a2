package gabriel.eventEngine.events.engine.mini.events;

import java.util.Arrays;
import java.util.Collections;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.concurrent.ScheduledFuture;

import gabriel.eventEngine.events.GabrielEventsLoader;
import gabriel.eventEngine.events.engine.EventBuffer;
import gabriel.eventEngine.events.engine.EventManager;
import gabriel.eventEngine.events.engine.EventRewardSystem;
import gabriel.eventEngine.events.engine.EventWarnings;
import gabriel.eventEngine.events.engine.base.EventMap;
import gabriel.eventEngine.events.engine.base.EventPlayerData;
import gabriel.eventEngine.events.engine.base.EventSpawn;
import gabriel.eventEngine.events.engine.base.PvPEventPlayerData;
import gabriel.eventEngine.events.engine.base.RewardPosition;
import gabriel.eventEngine.events.engine.base.SpawnType;
import gabriel.eventEngine.events.engine.lang.LanguageEngine;
import gabriel.eventEngine.events.engine.mini.MiniEventGame;
import gabriel.eventEngine.events.engine.mini.RegistrationData;
import gabriel.eventEngine.events.engine.stats.GlobalStats;
import gabriel.eventEngine.events.engine.stats.GlobalStatsModel;
import gabriel.eventEngine.events.engine.team.EventTeam;
import gabriel.eventEngine.events.engine.team.OnePlayerTeam;
import gabriel.eventEngine.interf.PlayerEventInfo;
import gabriel.eventEngine.interf.callback.CallbackManager;
import gabriel.eventEngine.interf.delegate.CharacterData;
import gabriel.eventEngine.l2j.CallBack;

public class OnevsOneGame extends MiniEventGame {
	private final int _teamsAmmount;
	private final int _roundsAmmount;
	protected OnePlayerTeam[] _players;
	private ScheduledFuture<?> _eventEnd;
	private ScheduledFuture<?> _roundStart;
	private int _round;
	
	public OnevsOneGame(int gameId, EventMap arena, OnevsOneManager event, RegistrationData[] teams) {
		super(gameId, arena, event, teams);
		
		_teamsAmmount = event.getTeamsCount();
		_roundsAmmount = event.getRoundsAmmount();
		_players = new OnePlayerTeam[_teamsAmmount];
		
		for (int i = 0; i < _teamsAmmount; ++i) {
			_players[i] = new OnePlayerTeam(teams[i], i + 1, teams[i].getKeyPlayer().getPlayersName());
			teams[i].getKeyPlayer().onEventStart(this);
			_players[i].addPlayer(teams[i].getKeyPlayer(), true);
		}
		CallbackManager.getInstance().eventStarts(1, getEvent().getEventType(), Arrays.asList(_players));
		_round = 0;
	}
	
	@Override
	protected void initEvent() {
		super.initEvent();
		startEvent();
	}
	
	@Override
	protected void startEvent() {
		try {
			broadcastMessage(LanguageEngine.getMsg("game_teleporting"), false);
			_eventEnd = CallBack.getInstance().getOut().scheduleGeneral(() -> endByTime(), getGameTime());
			scheduleMessage(LanguageEngine.getMsg("game_teleportDone"), 1500, true);
			nextRound(false);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	
	private void nextRound(boolean forceEnd) {
		if (_aborted) {
			return;
		}
		if ((_round == _roundsAmmount) || forceEnd) {
			endByDie();
			return;
		} else {
			_round++;
			
			handleDoors(1);
			loadBuffers();
			
			EventSpawn spawn;
			for (OnePlayerTeam team : _players) {
				if ((team.getPlayer() != null) && team.getPlayer().isOnline()) {
					spawn = _arena.getNextSpawn(team.getTeamId(), SpawnType.Regular);
					if (spawn == null) {
						abortDueToError("No regular spawn found for team " + team.getTeamId() + ". Match aborted.");
						clearEvent();
						return;
					}
					
					team.getPlayer().teleport(spawn.getLoc(), 0, false, _instanceId);
					
					team.getPlayer().disableAfkCheck(true);
					team.getPlayer().root();
					
					if ((_round == 1) && getEvent().getBoolean("removeCubics")) {
						team.getPlayer().removeCubics();
					}
					if (_allowSchemeBuffer) {
						EventBuffer.getInstance().buffPlayer(team.getPlayer(), true);
					}
					if (_round == 1) {
						team.getPlayer().enableAllSkills();
					}
				}
			}
			
			final int startTime;
			if (_round == 1) {
				startTime = getEvent().getMapConfigInt(_arena, "FirstRoundWaitDelay");
			} else {
				startTime = getEvent().getMapConfigInt(_arena, "RoundWaitDelay");
			}
			scheduleMessage(LanguageEngine.getMsg("game_roundStartIn", getRoundName(_round, _roundsAmmount), startTime / 1000), 5000, true);
			_roundStart = CallBack.getInstance().getOut().scheduleGeneral(() -> finishRoundStart(), startTime);
		}
	}
	
	private void finishRoundStart() {
		if (_aborted) {
			return;
		}
		unspawnBuffers();
		handleDoors(2);
		for (OnePlayerTeam team : _players) {
			if ((team.getPlayer() != null) && team.getPlayer().isOnline()) {
				team.getPlayer().disableAfkCheck(false);
				team.getPlayer().unroot();
			}
		}
		broadcastMessage(LanguageEngine.getMsg("game_roundStarted", getRoundName(_round, _roundsAmmount)), true);
		if (_round == 1) {
			startAnnouncing();
		}
	}
	
	@Override
	public void onDie(PlayerEventInfo player, CharacterData killer) {
		if (_aborted) {
			return;
		}
		
		updateScore(player, killer);
		
		final OnePlayerTeam team = checkLastAlivePlayer();
		if (team != null) {
			team.raiseScore(1);
			onScore(team.getPlayers(), 1);
			
			final boolean forceEnd = !checkIfTheMatchCanContinue();
			
			if ((_round == _roundsAmmount) || forceEnd) {
				scheduleMessage(LanguageEngine.getMsg("game_matchEnd"), 3000, true);
			} else {
				scheduleMessage(LanguageEngine.getMsg("game_roundWonBy", getRoundName(_round, _roundsAmmount), team.getTeamName()), 3000, true);
			}
			CallBack.getInstance().getOut().scheduleGeneral(() -> nextRound(forceEnd), 4000L);
		}
	}
	
	private boolean checkIfTheMatchCanContinue() {
		int remainingRounds = _roundsAmmount - _round;
		int bestScore = 0;
		int secondScore = 0;
		for (OnePlayerTeam team : _players) {
			if (team.getScore() > bestScore) {
				secondScore = bestScore;
				bestScore = team.getScore();
			} else if ((team.getScore() > secondScore) && (secondScore != bestScore)) {
				secondScore = team.getScore();
			}
		}
		// second team has no chance to win the match anymore
		if (bestScore - secondScore > remainingRounds)
			return false;
		else // there are still enought rounds so the second team can still win the match
			return true;
	}
	
	private OnePlayerTeam checkLastAlivePlayer() {
		int alivePlayers = 0;
		OnePlayerTeam tempTeam = null;
		
		for (OnePlayerTeam team : _players) {
			if (team.getPlayer() != null && team.getPlayer().isOnline()) {
				// there is at least one alive player
				if (!team.getPlayer().isDead()) {
					alivePlayers++;
					tempTeam = team;
					continue;
				}
			}
		}
		
		if (alivePlayers == 1)
			return tempTeam;
		else
			return null;
	}
	
	private void endByTime() {
		if (_aborted) {
			return;
		}
		cancelSchedulers();
		broadcastMessage(LanguageEngine.getMsg("game_matchEnd_timeLimit", getGameTime() / 60000), false);
		scheduleMessage(LanguageEngine.getMsg("game_matchEnd_tie"), 3000, false);
		
		int topScore = 0;
		OnePlayerTeam top = null;
		
		for (OnePlayerTeam team : _players) {
			if (team.getScore() > topScore) {
				topScore = team.getScore();
				top = team;
			}
			
			if (team.getPlayer() != null) {
				EventRewardSystem.getInstance().rewardPlayer(getEvent().getEventType(), getEvent().getMode().getModeId(), team.getPlayer(), RewardPosition.Tie_TimeLimit, null, team.getPlayer().getTotalTimeAfk(), 0, 0);
				getPlayerData(team.getPlayer()).getGlobalStats().raise(GlobalStats.GlobalStatType.LOSES, 1);
				_event.logPlayer(team.getPlayer(), 2);
			}
		}
		
		if (top != null)
			setWinner(top);
		
		saveGlobalStats();
		scheduleClearEvent(8000);
	}
	
	private void endByDie() {
		cancelSchedulers();
		List<OnePlayerTeam> sortedTeams = new LinkedList<OnePlayerTeam>();
		for (OnePlayerTeam team : _players) {
			sortedTeams.add(team);
		}
		
		Collections.sort(sortedTeams, EventManager.getInstance().compareTeamScore);
		
		Map<Integer, List<OnePlayerTeam>> scores = new LinkedHashMap<Integer, List<OnePlayerTeam>>();
		for (OnePlayerTeam team : sortedTeams) {
			if (!scores.containsKey(team.getScore())) {
				scores.put(team.getScore(), new LinkedList<OnePlayerTeam>());
			}
			scores.get(team.getScore()).add(team);
		}
		
		int place = 1;
		for (OnePlayerTeam team : sortedTeams) {
			broadcastMessage(LanguageEngine.getMsg("event_announceScore_includeKills", place, team.getTeamName(), team.getScore(), team.getKills()), false);
			String clanName = team.getPlayer().getOwner().getClan() != null ? team.getPlayer().getOwner().getClan().getName() : "";
			GabrielEventsLoader.detailedDebug("One Vs One Event " + "Player : " + team.getTeamName() + "  - Clan Name: " + clanName + " - Score : " + team.getScore() + " - Kills : " + team.getKills());
			place++;
		}
		
		place = 1;
		for (Entry<Integer, List<OnePlayerTeam>> i : scores.entrySet()) {
			// winners
			if (place == 1) {
				// at least two teams are winners (have the same score) and the match has more than 2 teams
				if (i.getValue().size() > 1) {
					if (_teamsAmmount > i.getValue().size()) {
						StringBuilder tb = new StringBuilder();
						for (OnePlayerTeam team : i.getValue()) {
							tb.append(LanguageEngine.getMsg("event_ffa_announceWinner2_part1", team.getTeamName()));
						}
						
						String s = tb.toString();
						tb = new StringBuilder(s.substring(0, s.length() - 4));
						tb.append(LanguageEngine.getMsg("event_ffa_announceWinner2_part1"));
						
						broadcastMessage(tb.toString(), false);
						
						for (OnePlayerTeam team : i.getValue()) {
							setWinner(team);
							
							if (team.getPlayer() != null) {
								if (team.getPlayer().isOnline()) {
									EventRewardSystem.getInstance().rewardPlayer(getEvent().getEventType(), getEvent().getMode().getModeId(), team.getPlayer(), RewardPosition.Winner, null, team.getPlayer().getTotalTimeAfk(), 0, 0);
									setEndStatus(team.getPlayer(), 1);
								}
								
								getPlayerData(team.getPlayer()).getGlobalStats().raise(GlobalStats.GlobalStatType.WINS, 1);
								_event.logPlayer(team.getPlayer(), 1);
							}
						}
					} else // all teams are 'winners' - have the same score (but > 0)
					{
						broadcastMessage(LanguageEngine.getMsg("event_ffa_announceWinner3"), false);
						
						for (OnePlayerTeam team : i.getValue()) {
							setWinner(team);
							
							if (team.getPlayer() != null) {
								if (team.getPlayer().isOnline()) {
									EventRewardSystem.getInstance().rewardPlayer(getEvent().getEventType(), getEvent().getMode().getModeId(), team.getPlayer(), RewardPosition.Tie, null, team.getPlayer().getTotalTimeAfk(), 0, 0);
								}
								getPlayerData(team.getPlayer()).getGlobalStats().raise(GlobalStats.GlobalStatType.WINS, 1);
								_event.logPlayer(team.getPlayer(), 2);
							}
						}
					}
				} else // single team is winner
				{
					OnePlayerTeam winnerPlayer = i.getValue().get(0);
					setWinner(winnerPlayer);
					broadcastMessage(LanguageEngine.getMsg("event_ffa_announceWinner1", i.getValue().get(0).getTeamName()), false);
					String clanName = winnerPlayer.getPlayer().getOwner().getClan() != null ? winnerPlayer.getPlayer().getOwner().getClan().getName() : "";
					GabrielEventsLoader.detailedDebug("One Vs One Event " + "Winner: " + winnerPlayer.getTeamName() + "  - Clan Name: " + clanName + " - Score : " + winnerPlayer.getScore() + " - Kills : " + winnerPlayer.getKills());
					if (winnerPlayer.getPlayer() != null) {
						if (winnerPlayer.getPlayer().isOnline()) {
							EventRewardSystem.getInstance().rewardPlayer(getEvent().getEventType(), getEvent().getMode().getModeId(), winnerPlayer.getPlayer(), RewardPosition.Winner, null, winnerPlayer.getPlayer().getTotalTimeAfk(), 0, 0);
							setEndStatus(winnerPlayer.getPlayer(), 1);
						}
						getPlayerData(winnerPlayer.getPlayer()).getGlobalStats().raise(GlobalStats.GlobalStatType.WINS, 1);
						_event.logPlayer(winnerPlayer.getPlayer(), 1);
					}
				}
			} else // loosers
			{
				for (OnePlayerTeam team : i.getValue()) {
					if (team.getPlayer() != null) {
						if (team.getPlayer().isOnline()) {
							EventRewardSystem.getInstance().rewardPlayer(getEvent().getEventType(), getEvent().getMode().getModeId(), team.getPlayer(), RewardPosition.Looser, null, team.getPlayer().getTotalTimeAfk(), 0, 0);
							setEndStatus(team.getPlayer(), 0);
						}
						getPlayerData(team.getPlayer()).getGlobalStats().raise(GlobalStats.GlobalStatType.LOSES, 1);
						_event.logPlayer(team.getPlayer(), 2);
					}
				}
			}
			place++;
		}
		saveGlobalStats();
		scheduleClearEvent(8000);
	}
	
	@Override
	public void clearEvent() {
		cancelSchedulers();
		cleanSpectators();
		applyStatsChanges();
		for (OnePlayerTeam team : _players) {
			if ((team.getPlayer() != null) && team.getPlayer().isOnline()) {
				if (team.getPlayer().isImmobilized()) {
					team.getPlayer().unroot();
				}
				if (team.getPlayer().isDead()) {
					team.getPlayer().doRevive();
					team.getPlayer().setCurrentHp(team.getPlayer().getMaxHp());
					team.getPlayer().setCurrentMp(team.getPlayer().getMaxMp());
					team.getPlayer().setCurrentCp(team.getPlayer().getMaxCp());
				}
				team.getPlayer().restoreData();
				team.getPlayer().teleport(team.getPlayer().getOrigLoc(), 0, true, 0);
				team.getPlayer().sendMessage(LanguageEngine.getMsg("event_teleportBack"));
				CallBack.getInstance().getPlayerBase().eventEnd(team.getPlayer());
			}
		}
		if (_fences != null) {
			CallBack.getInstance().getOut().unspawnFences(_fences);
		}
		unspawnMapGuards();
		unspawnNpcs();
		_event.notifyGameEnd(this);
	}
	
	@Override
	public void onDisconnect(PlayerEventInfo player) {
		if ((player != null) && player.isOnline()) {
			if (player.isSpectator()) {
				removeSpectator(player, true);
				return;
			}
			EventWarnings.getInstance().addPoints(player.getPlayersId(), 1);
			if (_teamsAmmount == 2) {
				broadcastMessage(LanguageEngine.getMsg("game_playerDisconnected2", player.getPlayersName()), true);
			} else {
				broadcastMessage(LanguageEngine.getMsg("game_playerDisconnected", player.getPlayersName()), true);
			}
			EventTeam playerTeam = player.getEventTeam();
			player.restoreData();
			player.setXYZInvisible(player.getOrigLoc().getX(), player.getOrigLoc().getY(), player.getOrigLoc().getZ());
			if (!_aborted) {
				playerTeam.removePlayer(player);
				if (checkIfPlayersDisconnected()) {
					broadcastMessage(LanguageEngine.getMsg("event_disconnect_all"), true);
					clearEvent();
					return;
				}
				final OnePlayerTeam team = checkLastAlivePlayer();
				if (team != null) {
					CallBack.getInstance().getOut().scheduleGeneral(() -> nextRound(false), 3000L);
				}
			}
		}
	}
	
	private boolean checkIfPlayersDisconnected() {
		int teamsOn = 0;
		for (OnePlayerTeam team : _players) {
			if ((team.getPlayer() != null) && team.getPlayer().isOnline()) {
				teamsOn++;
				continue;
			}
		}
		return (teamsOn == 0) || (teamsOn == 1);
	}
	
	@Override
	protected void checkPlayersLoc() {
	}
	
	@Override
	protected void checkIfPlayersTeleported() {
	}
	
	private void cancelSchedulers() {
		if (_aborted) {
			return;
		}
		_aborted = true;
		CallbackManager.getInstance().eventEnded(1, getEvent().getEventType(), Arrays.asList(_players));
		if (_announcer != null) {
			_announcer.cancel();
			_announcer = null;
		}
		if (_locChecker != null) {
			_locChecker.cancel(false);
			_locChecker = null;
		}
		if (_eventEnd != null) {
			_eventEnd.cancel(false);
			_eventEnd = null;
		}
		if (_roundStart != null) {
			_roundStart.cancel(false);
			_roundStart = null;
		}
	}
	
	@Override
	public int getInstanceId() {
		return _instanceId;
	}
	
	@Override
	public EventTeam[] getTeams() {
		return _players;
	}
	
	@Override
	public EventPlayerData createPlayerData(final PlayerEventInfo player) {
		final EventPlayerData d = new PvPEventPlayerData(player, this, new GlobalStatsModel(_event.getEventType()));
		return d;
	}
	
	@Override
	public PvPEventPlayerData getPlayerData(final PlayerEventInfo player) {
		return (PvPEventPlayerData) player.getEventData();
	}
}
