package gabriel.eventEngine.events.engine.mini.events;

import java.util.Arrays;
import java.util.Collections;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.concurrent.ScheduledFuture;

import gabriel.eventEngine.events.GabrielEventsLoader;
import gabriel.eventEngine.events.engine.EventBuffer;
import gabriel.eventEngine.events.engine.EventManager;
import gabriel.eventEngine.events.engine.EventRewardSystem;
import gabriel.eventEngine.events.engine.EventWarnings;
import gabriel.eventEngine.events.engine.base.EventMap;
import gabriel.eventEngine.events.engine.base.EventPlayerData;
import gabriel.eventEngine.events.engine.base.EventSpawn;
import gabriel.eventEngine.events.engine.base.PvPEventPlayerData;
import gabriel.eventEngine.events.engine.base.RewardPosition;
import gabriel.eventEngine.events.engine.base.SpawnType;
import gabriel.eventEngine.events.engine.lang.LanguageEngine;
import gabriel.eventEngine.events.engine.mini.MiniEventGame;
import gabriel.eventEngine.events.engine.mini.RegistrationData;
import gabriel.eventEngine.events.engine.stats.GlobalStats;
import gabriel.eventEngine.events.engine.stats.GlobalStatsModel;
import gabriel.eventEngine.events.engine.team.EventTeam;
import gabriel.eventEngine.events.engine.team.FixedPartyTeam;
import gabriel.eventEngine.interf.PlayerEventInfo;
import gabriel.eventEngine.interf.callback.CallbackManager;
import gabriel.eventEngine.interf.delegate.CharacterData;
import gabriel.eventEngine.l2j.CallBack;

public class PartyvsPartyGame extends MiniEventGame {
	private final int _teamsAmmount;
	private final int _roundsAmmount;
	private final FixedPartyTeam[] _teams;
	private int _round;
	private ScheduledFuture<?> _eventEnd;
	private ScheduledFuture<?> _roundStart;
	
	public PartyvsPartyGame(int gameId, EventMap arena, PartyvsPartyManager event, RegistrationData[] teams) {
		super(gameId, arena, event, teams);
		_teamsAmmount = event.getTeamsCount();
		_roundsAmmount = event.getRoundsAmmount();
		_teams = new FixedPartyTeam[_teamsAmmount];
		for (int i = 0; i < _teamsAmmount; i++) {
			_teams[i] = new FixedPartyTeam(teams[i], i + 1, teams[i].getKeyPlayer().getPlayersName() + "'s party", event.getDefaultPartySizeToJoin());
			for (PlayerEventInfo pi : teams[i].getPlayers()) {
				pi.onEventStart(this);
				_teams[i].addPlayer(pi, true);
			}
		}
		CallbackManager.getInstance().eventStarts(1, getEvent().getEventType(), Arrays.asList(_teams));
		_round = 0;
	}
	
	@Override
	protected void initEvent() {
		super.initEvent();
		loadBuffers();
		startEvent();
	}
	
	@Override
	protected void startEvent() {
		try {
			broadcastMessage(LanguageEngine.getMsg("game_teleporting"), true);
			_eventEnd = CallBack.getInstance().getOut().scheduleGeneral(() -> endByTime(), getGameTime());
			scheduleMessage(LanguageEngine.getMsg("game_teleportDone"), 1500, true);
			nextRound(null, false);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	
	protected void nextRound(FixedPartyTeam lastWinner, boolean forceEnd) {
		if (_aborted) {
			return;
		}
		if ((_round == _roundsAmmount) || forceEnd) {
			endByDie();
			return;
		} else {
			_round++;
			boolean removeBuffs = getEvent().getBoolean("removeBuffsOnRespawn");
			if (_round == 1) {
				removeBuffs = getEvent().getBoolean("removeBuffsOnStart");
			}
			handleDoors(1);
			loadBuffers();
			EventSpawn spawn;
			for (FixedPartyTeam team : _teams) {
				spawn = _arena.getNextSpawn(team.getTeamId(), SpawnType.Regular);
				if (spawn == null) {
					abortDueToError("No regular spawn found for team " + team.getTeamId() + ". Match aborted.");
					clearEvent();
					return;
				}
				for (PlayerEventInfo pi : team.getPlayers()) {
					if (!pi.isOnline()) {
						continue;
					}
					pi.teleport(spawn.getLoc(), 0, true, _instanceId);
					if (removeBuffs) {
						pi.removeBuffs();
					}
					pi.disableAfkCheck(true);
					pi.root();
					if ((_round == 1) && getEvent().getBoolean("removeCubics")) {
						pi.removeCubics();
					}
					if (_allowSchemeBuffer) {
						EventBuffer.getInstance().buffPlayer(pi, true);
					}
					if (_round != 1) {
						continue;
					}
					pi.enableAllSkills();
				}
			}
			final int startTime;
			if (_round == 1) {
				startTime = getEvent().getMapConfigInt(_arena, "FirstRoundWaitDelay");
			} else {
				startTime = getEvent().getMapConfigInt(_arena, "RoundWaitDelay");
			}
			scheduleMessage(LanguageEngine.getMsg("game_roundStartIn", getRoundName(_round, _roundsAmmount), startTime / 1000), 5000, true);
			_roundStart = CallBack.getInstance().getOut().scheduleGeneral(() -> finishRoundStart(), startTime);
		}
	}
	
	private void finishRoundStart() {
		if (_aborted) {
			return;
		}
		unspawnBuffers();
		handleDoors(2);
		for (FixedPartyTeam team : _teams) {
			for (PlayerEventInfo pi : team.getPlayers()) {
				if (pi.isOnline()) {
					pi.disableAfkCheck(false);
					pi.unroot();
				}
			}
		}
		broadcastMessage(LanguageEngine.getMsg("game_roundStarted", getRoundName(_round, _roundsAmmount)), true);
		if (_round == 1) {
			startAnnouncing();
		}
	}
	
	@Override
	public void onDie(PlayerEventInfo player, CharacterData killer) {
		if (_aborted) {
			return;
		}
		updateScore(player, killer);
		// final FixedPartyTeam team = getTeamFromPlayer(player);
		final FixedPartyTeam team = checkLastAliveTeam();
		if (team != null) {
			team.raiseScore(1);
			onScore(team.getPlayers(), 1);
			final boolean forceEnd = !checkIfTheMatchCanContinue();
			if ((_round == _roundsAmmount) || forceEnd) {
				scheduleMessage(LanguageEngine.getMsg("game_matchEnd"), 3000, true);
			} else {
				scheduleMessage(LanguageEngine.getMsg("game_roundWonBy", getRoundName(_round, _roundsAmmount), team.getTeamName()), 3000, true);
			}
			CallBack.getInstance().getOut().scheduleGeneral(() -> nextRound(team, forceEnd), 4000L);
		}
	}
	
	private boolean checkIfTheMatchCanContinue() {
		int remainingRounds = _roundsAmmount - _round;
		int bestScore = 0;
		int secondScore = 0;
		for (FixedPartyTeam team : _teams) {
			if (team.getScore() > bestScore) {
				secondScore = bestScore;
				bestScore = team.getScore();
			} else if ((team.getScore() > secondScore) && (secondScore != bestScore)) {
				secondScore = team.getScore();
			}
		}
		// second team has no chance to win the match anymore
		if (bestScore - secondScore > remainingRounds)
			return false;
		else // there are still enought rounds so the second team can still win the match
			return true;
	}
	
	private FixedPartyTeam checkLastAliveTeam() {
		int aliveTeams = 0;
		FixedPartyTeam tempTeam = null;
		for (FixedPartyTeam team : _teams) {
			for (PlayerEventInfo pi : team.getPlayers()) {
				if (pi.isOnline() && !pi.isDead()) {
					aliveTeams++;
					tempTeam = team;
					break;
				}
			}
		}
		if (aliveTeams == 1) {
			return tempTeam;
		}
		return null;
	}
	
	protected void endByTime() {
		if (_aborted) {
			return;
		}
		cancelSchedulers();
		broadcastMessage(LanguageEngine.getMsg("game_matchEnd_timeLimit", getGameTime() / 60000), false);
		scheduleMessage(LanguageEngine.getMsg("game_matchEnd_tie"), 3000, false);
		
		int topScore = 0;
		FixedPartyTeam top = null;
		
		for (FixedPartyTeam team : _teams) {
			
			if (team.getScore() > topScore) {
				topScore = team.getScore();
				top = team;
			}
			
			for (PlayerEventInfo pi : team.getPlayers()) {
				if (pi.isOnline()) {
					EventRewardSystem.getInstance().rewardPlayer(getEvent().getEventType(), getEvent().getMode().getModeId(), pi, RewardPosition.Tie_TimeLimit, null, pi.getTotalTimeAfk(), 0, 0);
				}
				_event.logPlayer(pi, 2);
			}
		}
		
		if (top != null)
			setWinner(top);
		
		saveGlobalStats();
		scheduleClearEvent(8000);
	}
	
	private void endByDie() {
		cancelSchedulers();
		List<FixedPartyTeam> sortedTeams = new LinkedList<FixedPartyTeam>();
		for (FixedPartyTeam team : _teams) {
			sortedTeams.add(team);
		}
		Collections.sort(sortedTeams, EventManager.getInstance().compareTeamScore);
		Map<Integer, List<FixedPartyTeam>> scores = new LinkedHashMap<Integer, List<FixedPartyTeam>>();
		for (FixedPartyTeam team : sortedTeams) {
			if (!scores.containsKey(team.getScore())) {
				scores.put(team.getScore(), new LinkedList<FixedPartyTeam>());
			}
			scores.get(team.getScore()).add(team);
		}
		
		int place = 1;
		for (FixedPartyTeam team : sortedTeams) {
			broadcastMessage(LanguageEngine.getMsg("event_announceScore_includeKills", place, team.getTeamName(), team.getScore(), team.getKills()), false);
			GabrielEventsLoader.detailedDebug("Party Vs Party " + "Team : " + team.getTeamName() + " Score : " + team.getScore() + " Kills : " + team.getKills());
			place++;
		}
		
		place = 1;
		for (Entry<Integer, List<FixedPartyTeam>> i : scores.entrySet()) {
			if (place == 1) {
				if (i.getValue().size() > 1) {
					if (_teamsAmmount > i.getValue().size()) {
						StringBuilder tb = new StringBuilder();
						for (FixedPartyTeam team : i.getValue()) {
							tb.append(LanguageEngine.getMsg("event_team_announceWinner2_part1", team.getTeamName()));
						}
						String s = tb.toString();
						tb = new StringBuilder(s.substring(0, s.length() - 4));
						tb.append(LanguageEngine.getMsg("event_team_announceWinner2_part2"));
						broadcastMessage(tb.toString(), false);
						for (FixedPartyTeam team : i.getValue()) {
							setWinner(team);
							for (PlayerEventInfo pi : team.getPlayers()) {
								if (pi.isOnline()) {
									EventRewardSystem.getInstance().rewardPlayer(getEvent().getEventType(), getEvent().getMode().getModeId(), pi, RewardPosition.Winner, null, pi.getTotalTimeAfk(), 0, 0);
									setEndStatus(pi, 1);
								}
								getPlayerData(pi).getGlobalStats().raise(GlobalStats.GlobalStatType.WINS, 1);
								_event.logPlayer(pi, 1);
							}
						}
					} else {
						broadcastMessage(LanguageEngine.getMsg("event_team_announceWinner3"), false);
						for (FixedPartyTeam team : i.getValue()) {
							for (PlayerEventInfo pi : team.getPlayers()) {
								if (pi.isOnline()) {
									EventRewardSystem.getInstance().rewardPlayer(getEvent().getEventType(), getEvent().getMode().getModeId(), pi, RewardPosition.Tie, null, pi.getTotalTimeAfk(), 0, 0);
								}
								getPlayerData(pi).getGlobalStats().raise(GlobalStats.GlobalStatType.WINS, 1);
								_event.logPlayer(pi, 2);
							}
						}
					}
				} else {
					broadcastMessage(LanguageEngine.getMsg("event_team_announceWinner1", i.getValue().get(0).getTeamName()), false);
					GabrielEventsLoader.detailedDebug("Party Vs Party Event " + "Winner: " + i.getValue().get(0).getTeamName() + " - Score : " + i.getValue().get(0).getScore() + " - Kills : " + i.getValue().get(0).getKills());
					for (PlayerEventInfo pi : i.getValue().get(0).getPlayers()) {
						if (pi.isOnline()) {
							EventRewardSystem.getInstance().rewardPlayer(getEvent().getEventType(), getEvent().getMode().getModeId(), pi, RewardPosition.Winner, null, pi.getTotalTimeAfk(), 0, 0);
							setEndStatus(pi, 1);
						}
						getPlayerData(pi).getGlobalStats().raise(GlobalStats.GlobalStatType.WINS, 1);
						_event.logPlayer(pi, 1);
					}
				}
			} else {
				for (FixedPartyTeam team : i.getValue()) {
					for (PlayerEventInfo pi : team.getPlayers()) {
						if (pi.isOnline()) {
							EventRewardSystem.getInstance().rewardPlayer(getEvent().getEventType(), getEvent().getMode().getModeId(), pi, RewardPosition.Looser, null, pi.getTotalTimeAfk(), 0, 0);
							setEndStatus(pi, 0);
						}
						getPlayerData(pi).getGlobalStats().raise(GlobalStats.GlobalStatType.LOSES, 1);
						_event.logPlayer(pi, 2);
					}
				}
			}
			place++;
		}
		saveGlobalStats();
		scheduleClearEvent(5000);
	}
	
	@Override
	public void clearEvent() {
		cancelSchedulers();
		cleanSpectators();
		applyStatsChanges();
		for (FixedPartyTeam team : _teams) {
			for (PlayerEventInfo pi : team.getPlayers()) {
				if (pi.isOnline()) {
					if (pi.isImmobilized()) {
						pi.unroot();
					}
					if (pi.isDead()) {
						pi.doRevive();
						pi.setCurrentHp(pi.getMaxHp());
						pi.setCurrentMp(pi.getMaxMp());
						pi.setCurrentCp(pi.getMaxCp());
					}
					pi.restoreData();
					pi.teleport(pi.getOrigLoc(), 0, true, 0);
					pi.sendMessage(LanguageEngine.getMsg("event_teleportBack"));
					CallBack.getInstance().getPlayerBase().eventEnd(pi);
				}
			}
		}
		if (_fences != null) {
			CallBack.getInstance().getOut().unspawnFences(_fences);
		}
		unspawnMapGuards();
		unspawnNpcs();
		_event.notifyGameEnd(this);
	}
	
	@Override
	public void onDisconnect(PlayerEventInfo player) {
		if ((player != null) && player.isOnline()) {
			if (player.isSpectator()) {
				removeSpectator(player, true);
				return;
			}
			EventWarnings.getInstance().addPoints(player.getPlayersId(), 1);
			broadcastMessage(LanguageEngine.getMsg("game_playerDisconnected", player.getPlayersName()), true);
			EventTeam playerTeam = player.getEventTeam();
			playerTeam.removePlayer(player);
			player.restoreData();
			player.setXYZInvisible(player.getOrigLoc().getX(), player.getOrigLoc().getY(), player.getOrigLoc().getZ());
			if (!_aborted) {
				if (playerTeam.getPlayers().isEmpty()) {
					broadcastMessage(LanguageEngine.getMsg("game_teamDisconnected", playerTeam.getTeamName()), true);
				}
				if (checkIfTeamsDisconnected()) {
					broadcastMessage(LanguageEngine.getMsg("event_disconnect_all"), true);
					clearEvent();
					return;
				}
				final FixedPartyTeam team = checkLastAliveTeam();
				if (team != null) {
					CallBack.getInstance().getOut().scheduleGeneral(() -> nextRound(team, false), 3000L);
				}
			}
		}
	}
	
	private boolean checkIfTeamsDisconnected() {
		int teamsOn = 0;
		for (FixedPartyTeam team : _teams) {
			for (PlayerEventInfo pi : team.getPlayers()) {
				if (pi.isOnline()) {
					teamsOn++;
					break;
				}
			}
		}
		return (teamsOn == 0) || (teamsOn == 1);
	}
	
	@Override
	protected void checkPlayersLoc() {
	}
	
	@Override
	protected void checkIfPlayersTeleported() {
	}
	
	private void cancelSchedulers() {
		if (_aborted) {
			return;
		}
		_aborted = true;
		CallbackManager.getInstance().eventEnded(1, getEvent().getEventType(), Arrays.asList(_teams));
		if (_announcer != null) {
			_announcer.cancel();
			_announcer = null;
		}
		if (_locChecker != null) {
			_locChecker.cancel(false);
			_locChecker = null;
		}
		if (_eventEnd != null) {
			_eventEnd.cancel(false);
			_eventEnd = null;
		}
		if (_roundStart != null) {
			_roundStart.cancel(false);
			_roundStart = null;
		}
	}
	
	@Override
	public int getInstanceId() {
		return _instanceId;
	}
	
	@Override
	public EventTeam[] getTeams() {
		return _teams;
	}
	
	@Override
	public EventPlayerData createPlayerData(PlayerEventInfo player) {
		final EventPlayerData d = new PvPEventPlayerData(player, this, new GlobalStatsModel(_event.getEventType()));
		return d;
	}
	
	@Override
	public PvPEventPlayerData getPlayerData(PlayerEventInfo player) {
		return (PvPEventPlayerData) player.getEventData();
	}
}
