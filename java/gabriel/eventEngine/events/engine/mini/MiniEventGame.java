package gabriel.eventEngine.events.engine.mini;

import java.util.Arrays;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ScheduledFuture;
import java.util.logging.Level;
import java.util.logging.Logger;

import gabriel.eventEngine.events.EventGame;
import gabriel.eventEngine.events.GabrielEventsLoader;
import gabriel.eventEngine.events.engine.EventConfig;
import gabriel.eventEngine.events.engine.EventManager;
import gabriel.eventEngine.events.engine.base.EventMap;
import gabriel.eventEngine.events.engine.base.EventSpawn;
import gabriel.eventEngine.events.engine.base.SpawnType;
import gabriel.eventEngine.events.engine.lang.LanguageEngine;
import gabriel.eventEngine.events.engine.mini.features.AbstractFeature;
import gabriel.eventEngine.events.engine.mini.features.BufferFeature;
import gabriel.eventEngine.events.engine.mini.features.EnchantFeature;
import gabriel.eventEngine.events.engine.mini.features.ItemGradesFeature;
import gabriel.eventEngine.events.engine.mini.features.ItemsFeature;
import gabriel.eventEngine.events.engine.mini.features.SkillsFeature;
import gabriel.eventEngine.events.engine.mini.features.TimeLimitFeature;
import gabriel.eventEngine.events.engine.stats.EventStatsManager;
import gabriel.eventEngine.events.engine.stats.GlobalStats;
import gabriel.eventEngine.events.engine.stats.GlobalStatsModel;
import gabriel.eventEngine.events.engine.team.EventTeam;
import gabriel.eventEngine.interf.PlayerEventInfo;
import gabriel.eventEngine.interf.delegate.CharacterData;
import gabriel.eventEngine.interf.delegate.DoorData;
import gabriel.eventEngine.interf.delegate.FenceData;
import gabriel.eventEngine.interf.delegate.ItemData;
import gabriel.eventEngine.interf.delegate.NpcData;
import gabriel.eventEngine.interf.delegate.NpcTemplateData;
import gabriel.eventEngine.interf.delegate.SkillData;
import gabriel.eventEngine.l2j.CallBack;

public abstract class MiniEventGame implements Runnable, EventGame {
	protected static Logger _log = Logger.getLogger(MiniEventGame.class.getName());
	protected RegistrationData[] _registeredTeams;
	public static final int MAP_GUARD_ID = 501;
	protected int _instanceId;
	protected int _gameId;
	protected MiniEventManager _event;
	protected EventMap _arena;
	protected Announcer _announcer;
	private LocChecker _locCheckerInstance;
	protected ScheduledFuture<?> _locChecker = null;
	private static int _locCheckDelay = 10000;
	protected List<PlayerEventInfo> _spectators;
	protected List<PlayerEventInfo> _voted;
	protected List<FenceData> _fences;
	protected List<NpcData> _buffers;
	protected List<NpcData> _mapGuards;
	protected List<NpcData> _npcs;
	protected boolean _aborted = false;
	private int[] notAllovedSkillls;
	private int[] notAllovedItems;
	private int[] setOffensiveSkills;
	private int[] setNotOffensiveSkills;
	private int[] setNeutralSkills;
	protected boolean _allowSchemeBuffer;
	protected boolean _allowSummons;
	protected boolean _allowPets;
	
	public MiniEventGame(int gameId, EventMap arena, MiniEventManager event, RegistrationData[] teams) {
		_gameId = gameId;
		_event = event;
		_arena = arena;
		_instanceId = 0;
		_registeredTeams = teams;
	}
	
	public abstract int getInstanceId();
	
	public abstract EventTeam[] getTeams();
	
	protected void initAnnouncer() {
		(_announcer = new Announcer()).setTime(System.currentTimeMillis() + getGameTime());
	}
	
	@Override
	public void run() {
		initEvent();
	}
	
	public void scheduleLocChecker() {
		if (_locCheckerInstance == null) {
			_locCheckerInstance = new LocChecker();
		}
		_locChecker = CallBack.getInstance().getOut().scheduleGeneral(_locCheckerInstance, MiniEventGame._locCheckDelay);
	}
	
	private class LocChecker implements Runnable {
		protected LocChecker() {
		}
		
		@Override
		public void run() {
			try {
				checkPlayersLoc();
			} catch (Exception e) {
				e.printStackTrace();
			}
			scheduleLocChecker();
		}
	}
	
	protected abstract void checkPlayersLoc();
	
	protected abstract void checkIfPlayersTeleported();
	
	public void addSpectator(PlayerEventInfo player) {
		if (_spectators == null) {
			_spectators = new LinkedList<PlayerEventInfo>();
		}
		EventSpawn spectatorLoc = getMap().getNextSpawn(-1, SpawnType.Spectator);
		if (spectatorLoc == null) {
			spectatorLoc = getMap().getNextSpawn(-1, SpawnType.Regular);
		}
		if (spectatorLoc == null) {
			player.sendMessage(LanguageEngine.getMsg("observing_noSpawn"));
			return;
		}
		player.setIsSpectator(true);
		player.setActiveGame(this);
		player.removeSummon();
		player.removeCubics();
		if (player.getParty() != null) {
			player.getParty().removePartyMember(player);
		}
		player.setInstanceId(_instanceId);
		player.enterObserverMode(spectatorLoc.getLoc().getX(), spectatorLoc.getLoc().getY(), spectatorLoc.getLoc().getZ());
		_spectators.add(player);
	}
	
	public void removeSpectator(PlayerEventInfo pi, boolean disconnect) {
		if (!pi.isOnline()) {
			return;
		}
		if (!disconnect) {
			pi.removeObserveMode();
			CallBack.getInstance().getPlayerBase().eventEnd(pi);
		}
		_spectators.remove(pi);
	}
	
	protected void cleanSpectators() {
		if (_spectators != null) {
			for (PlayerEventInfo pi : _spectators) {
				removeSpectator(pi, false);
			}
		}
	}
	
	protected void initEvent() {
		_instanceId = CallBack.getInstance().getOut().createInstance("Game " + getEvent().getEventName() + " ID" + _gameId, getGameTime() + 59000, 0, true).getId();
		handleDoors(0);
		loadFences();
		CallBack.getInstance().getOut().spawnFences(_fences, _instanceId);
		loadNpcs();
		loadMapGuards();
		initAnnouncer();
		_allowSchemeBuffer = EventConfig.getInstance().getGlobalConfigBoolean("eventSchemeBuffer");
		_allowSummons = getEvent().getBoolean("allowSummons");
		_allowPets = getEvent().getBoolean("allowPets");
		if (!_event.getString("notAllowedSkills").equals("")) {
			final String[] splits = _event.getString("notAllowedSkills").split(",");
			notAllovedSkillls = new int[splits.length];
			try {
				for (int i = 0; i < splits.length; ++i) {
					notAllovedSkillls[i] = Integer.parseInt(splits[i]);
				}
				Arrays.sort(notAllovedSkillls);
			} catch (Exception ex) {
			}
		}
		if (!_event.getString("notAllowedItems").equals("")) {
			final String[] splits = _event.getString("notAllowedItems").split(",");
			notAllovedItems = new int[splits.length];
			try {
				for (int i = 0; i < splits.length; ++i) {
					notAllovedItems[i] = Integer.parseInt(splits[i]);
				}
				Arrays.sort(notAllovedItems);
			} catch (Exception ex2) {
			}
		}
		loadOverridenSkillsParameters();
	}
	
	private void loadOverridenSkillsParameters() {
		String s = EventConfig.getInstance().getGlobalConfigValue("setOffensiveSkills");
		try {
			String[] splits = s.split(";");
			setOffensiveSkills = new int[splits.length];
			try {
				for (int i = 0; i < splits.length; i++) {
					setOffensiveSkills[i] = Integer.parseInt(splits[i]);
				}
				Arrays.sort(setOffensiveSkills);
			} catch (Exception e) {
				GabrielEventsLoader.debug("Error while loading GLOBAL config 'setOffensiveSkills' in event " + _event.getEventName() + " - " + e.toString(), Level.SEVERE);
			}
		} catch (Exception e) {
			GabrielEventsLoader.debug("Error while loading GLOBAL config 'setOffensiveSkills' in event " + _event.getEventName() + " - " + e.toString(), Level.SEVERE);
		}
		s = EventConfig.getInstance().getGlobalConfigValue("setNotOffensiveSkills");
		try {
			String[] splits = s.split(";");
			setNotOffensiveSkills = new int[splits.length];
			try {
				for (int i = 0; i < splits.length; i++) {
					setNotOffensiveSkills[i] = Integer.parseInt(splits[i]);
				}
				Arrays.sort(setNotOffensiveSkills);
			} catch (Exception e) {
				GabrielEventsLoader.debug("Error while loading GLOBAL config 'setNotOffensiveSkills' in event " + _event.getEventName() + " - " + e.toString(), Level.SEVERE);
			}
		} catch (Exception e) {
			GabrielEventsLoader.debug("Error while loading GLOBAL config 'setNotOffensiveSkills' in event " + _event.getEventName() + " - " + e.toString(), Level.SEVERE);
		}
		s = EventConfig.getInstance().getGlobalConfigValue("setNeutralSkills");
		try {
			final String[] splits = s.split(";");
			setNeutralSkills = new int[splits.length];
			try {
				for (int i = 0; i < splits.length; i++) {
					setNeutralSkills[i] = Integer.parseInt(splits[i]);
				}
				Arrays.sort(setNeutralSkills);
			} catch (Exception e) {
				GabrielEventsLoader.debug("Error while loading GLOBAL config 'setNeutralSkills' in event " + _event.getEventName() + " - " + e.toString(), Level.SEVERE);
			}
		} catch (Exception e) {
			GabrielEventsLoader.debug("Error while loading GLOBAL config 'setNeutralSkills' in event " + _event.getEventName() + " - " + e.toString(), Level.SEVERE);
		}
	}
	
	@Override
	public int isSkillOffensive(SkillData skill) {
		if (setOffensiveSkills != null && Arrays.binarySearch(setOffensiveSkills, skill.getId()) >= 0) {
			return 1;
		}
		if (setNotOffensiveSkills != null && Arrays.binarySearch(setNotOffensiveSkills, skill.getId()) >= 0) {
			return 0;
		}
		return -1;
	}
	
	@Override
	public boolean isSkillNeutral(SkillData skill) {
		return setNeutralSkills != null && Arrays.binarySearch(setNeutralSkills, skill.getId()) >= 0;
	}
	
	protected void updateScore(PlayerEventInfo player, CharacterData killer) {
		player.raiseDeaths(1);
		player.getEventTeam().raiseDeaths(1);
		
		if (killer != null) {
			if (killer.getEventInfo() != null) {
				if (killer.getEventInfo().getEventTeam() == null) {
					return;
				}
				
				killer.getEventInfo().raiseKills(1);
				
				killer.getEventInfo().getEventTeam().raiseKills(1);
			}
		}
	}
	
	protected void startEvent() {
	}
	
	protected void setEndStatus(final PlayerEventInfo pi, final int status) {
	}
	
	public void applyStatsChanges() {
	}
	
	protected void onScore(List<PlayerEventInfo> players, int ammount) {
	}
	
	protected void abortDueToError(String message) {
		broadcastMessage(message, false);
		clearEvent();
		EventManager.getInstance().debug(_event.getEventType() + " match automatically aborted: " + message);
	}
	
	public void broadcastMessage(String msg, boolean abortable) {
		if (abortable && _aborted) {
			return;
		}
		for (EventTeam team : getTeams()) {
			for (PlayerEventInfo pi : team.getPlayers()) {
				pi.screenMessage(msg, getEvent().getEventName(), false);
			}
		}
		if (_spectators != null) {
			for (PlayerEventInfo pi2 : _spectators) {
				pi2.screenMessage(msg, getEvent().getEventName(), false);
			}
		}
	}
	
	protected boolean checkTeamStatus(int teamId) {
		for (final PlayerEventInfo pi : getTeams()[teamId - 1].getPlayers()) {
			if (pi.isOnline() && !pi.isDead()) {
				return true;
			}
		}
		return false;
	}
	
	protected void loadFences() {
		try {
			_fences = new LinkedList<FenceData>();
			for (EventSpawn spawn : _arena.getSpawns(0, SpawnType.Fence)) {
				_fences.add(CallBack.getInstance().getOut().createFence(2, spawn.getFenceWidth(), spawn.getFenceLength(), spawn.getLoc().getX(), spawn.getLoc().getY(), spawn.getLoc().getZ(), _arena.getGlobalId()));
			}
		} catch (NullPointerException ex) {
		}
	}
	
	private void loadMapGuards() {
		int id = EventConfig.getInstance().getGlobalConfigInt("mapGuardNpcId");
		if (id == -1) {
			return;
		}
		NpcTemplateData template = new NpcTemplateData(id);
		if (!template.exists()) {
			MiniEventGame._log.warning("Missing template for EventMap Guard.");
			return;
		}
		for (EventSpawn spawn : getMap().getSpawns()) {
			if (spawn.getSpawnType() == SpawnType.MapGuard) {
				try {
					NpcData data = template.doSpawn(spawn.getLoc().getX(), spawn.getLoc().getY(), spawn.getLoc().getZ(), 1, _instanceId);
					if (_mapGuards == null) {
						_mapGuards = new LinkedList<NpcData>();
					}
					_mapGuards.add(data);
				} catch (Exception e) {
					e.printStackTrace();
				}
			}
		}
	}
	
	protected void loadNpcs() {
		for (EventSpawn spawn : getMap().getSpawns(-1, SpawnType.Npc)) {
			try {
				int npcId = spawn.getNpcId();
				if (npcId == -1) {
					continue;
				}
				NpcData data = new NpcTemplateData(npcId).doSpawn(spawn.getLoc().getX(), spawn.getLoc().getY(), spawn.getLoc().getZ(), 1, _instanceId);
				if (_npcs == null) {
					_npcs = new LinkedList<NpcData>();
				}
				_npcs.add(data);
			} catch (Exception e) {
				e.printStackTrace();
			}
		}
	}
	
	protected void unspawnNpcs() {
		if (_npcs != null) {
			for (NpcData npc : _npcs) {
				if (npc != null) {
					npc.deleteMe();
				}
			}
			_npcs.clear();
		}
	}
	
	protected void loadBuffers() {
		try {
			int bufferId = EventConfig.getInstance().getGlobalConfigInt("npcBufferId");
			for (AbstractFeature feature : _event.getMode().getFeatures()) {
				if (feature.getType() == EventMode.FeatureType.Buffer) {
					if (!((BufferFeature) feature).canSpawnBuffer()) {
						return;
					}
					if (((BufferFeature) feature).getCustomNpcBufferId() == 0) {
						continue;
					}
					bufferId = ((BufferFeature) feature).getCustomNpcBufferId();
				}
			}
			if (bufferId == -1) {
				return;
			}
			NpcTemplateData template = new NpcTemplateData(bufferId);
			if (!template.exists()) {
				MiniEventGame._log.warning("Missing NPC Buffer's template (ID " + bufferId + ") for event system.");
				return;
			}
			for (EventSpawn spawn : _arena.getSpawns()) {
				if (spawn.getSpawnType() == SpawnType.Buffer) {
					NpcData data = template.doSpawn(spawn.getLoc().getX(), spawn.getLoc().getY(), spawn.getLoc().getZ(), 1, _instanceId);
					if (_buffers == null) {
						_buffers = new LinkedList<NpcData>();
					}
					_buffers.add(data);
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	
	protected void unspawnBuffers() {
		if (_buffers != null) {
			for (NpcData npc : _buffers) {
				if (npc != null) {
					npc.deleteMe();
				}
			}
			_buffers.clear();
		}
	}
	
	protected void unspawnMapGuards() {
		if (_mapGuards != null) {
			for (NpcData npc : _mapGuards) {
				if (npc != null) {
					npc.deleteMe();
				}
			}
			_mapGuards.clear();
		}
	}
	
	protected void handleDoors(int state) {
		if (!_arena.hasDoor()) {
			return;
		}
		DoorAction action;
		if (state == 0) {
			for (EventSpawn doorSpawn : _arena.getDoors()) {
				action = DoorAction.getAction(doorSpawn.getNote(), 1);
				CallBack.getInstance().getOut().addDoorToInstance(_instanceId, doorSpawn.getDoorId(), action == DoorAction.Open);
			}
		} else {
			for (DoorData door : CallBack.getInstance().getOut().getInstanceDoors(_instanceId)) {
				for (EventSpawn doorSpawn : _arena.getDoors()) {
					action = DoorAction.getAction(doorSpawn.getNote(), state);
					if (doorSpawn.getDoorId() == door.getDoorId()) {
						if (action == DoorAction.Close && door.isOpened()) {
							door.closeMe();
						} else {
							if (action != DoorAction.Open || door.isOpened()) {
								continue;
							}
							door.openMe();
						}
					}
				}
			}
		}
	}
	
	@Override
	public void playerWentAfk(PlayerEventInfo player, boolean warningOnly, int afkTime) {
		if (warningOnly) {
			player.sendMessage(LanguageEngine.getMsg("event_afkWarning", PlayerEventInfo.AFK_WARNING_DELAY / 1000, PlayerEventInfo.AFK_KICK_DELAY / 1000));
		} else {
			if (afkTime == 0) {
				player.sendMessage(LanguageEngine.getMsg("event_afkMarked"));
			} else if (afkTime % 60 == 0) {
				player.sendMessage(LanguageEngine.getMsg("event_afkDurationInfo", afkTime / 60));
			}
		}
	}
	
	@Override
	public void playerReturnedFromAfk(PlayerEventInfo player) {
	}
	
	protected void scheduleClearEvent(int delay) {
		CallBack.getInstance().getOut().scheduleGeneral(() -> clearEvent(), 8000L);
	}
	
	protected void startAnnouncing() {
		if (_announcer != null) {
			_announcer.announce = true;
		}
	}
	
	public EventMap getMap() {
		return _arena;
	}
	
	public int getGameId() {
		return _gameId;
	}
	
	public MiniEventManager getEvent() {
		return _event;
	}
	
	protected void saveGlobalStats() {
		Map<PlayerEventInfo, GlobalStatsModel> stats = new ConcurrentHashMap<PlayerEventInfo, GlobalStatsModel>();
		for (EventTeam team : getTeams()) {
			for (PlayerEventInfo pi : team.getPlayers()) {
				getPlayerData(pi).getGlobalStats().raise(GlobalStats.GlobalStatType.COUNT_PLAYED, 1);
				stats.put(pi, getPlayerData(pi).getGlobalStats());
			}
		}
		EventStatsManager.getInstance().getGlobalStats().updateGlobalStats(stats);
	}
	
	public class Announcer implements Runnable {
		private long _start;
		boolean announce = false;
		private ScheduledFuture<?> _nextAnnounce;
		
		public void setTime(long startTime) {
			_start = startTime;
			run();
		}
		
		@Override
		public void run() {
			int delay = (int) Math.round((_start - System.currentTimeMillis()) / 1000.0);
			if (announce && delay > 0) {
				announce(delay);
			}
			int nextMsg = 0;
			if (delay > 3600) {
				nextMsg = delay - 3600;
			} else if (delay > 1800) {
				nextMsg = delay - 1800;
			} else if (delay > 900) {
				nextMsg = delay - 900;
			} else if (delay > 600) {
				nextMsg = delay - 600;
			} else if (delay > 300) {
				nextMsg = delay - 300;
			} else if (delay > 60) {
				nextMsg = delay - 60;
			} else if (delay > 10) {
				nextMsg = delay - 10;
			} else {
				return;
			}
			if (delay > 0) {
				_nextAnnounce = CallBack.getInstance().getOut().scheduleGeneral(this, nextMsg * 1000);
			}
		}
		
		private void announce(int delay) {
			if (delay >= 3600 && delay % 3600 == 0) {
				int d = delay / 3600;
				broadcastMessage(LanguageEngine.getMsg("game_countdown", d, "hour" + ((d == 1) ? "" : "s")), false);
			} else if (delay >= 60) {
				final int d = delay / 60;
				broadcastMessage(LanguageEngine.getMsg("game_countdown", d, "minute" + ((d == 1) ? "" : "s")), false);
			} else {
				broadcastMessage(LanguageEngine.getMsg("game_countdown", delay, "second" + ((delay == 1) ? "" : "s")), false);
			}
		}
		
		public void cancel() {
			if (_nextAnnounce != null) {
				_nextAnnounce.cancel(false);
			}
		}
	}
	
	protected void scheduleMessage(final String message, int delay, final boolean abortable) {
		CallBack.getInstance().getOut().scheduleGeneral(() -> broadcastMessage(message, abortable), delay);
	}
	
	protected String getRoundName(int round, int maxRounds) {
		if (round == maxRounds) {
			return LanguageEngine.getMsg("round_final");
		}
		switch (round) {
			case 1: {
				return LanguageEngine.getMsg("round_1");
			}
			case 2: {
				return LanguageEngine.getMsg("round_2");
			}
			case 3: {
				return LanguageEngine.getMsg("round_3");
			}
			case 4: {
				return LanguageEngine.getMsg("round_4");
			}
			case 5: {
				return LanguageEngine.getMsg("round_5");
			}
			case 6: {
				return LanguageEngine.getMsg("round_6");
			}
			case 7: {
				return LanguageEngine.getMsg("round_7");
			}
			case 8: {
				return LanguageEngine.getMsg("round_8");
			}
			case 9: {
				return LanguageEngine.getMsg("round_9");
			}
			case 10: {
				return LanguageEngine.getMsg("round_10");
			}
			default: {
				return round + "th";
			}
		}
	}
	
	protected int getGameTime() {
		for (AbstractFeature f : _event.getMode().getFeatures()) {
			if (f.getType() == EventMode.FeatureType.TimeLimit) {
				return ((TimeLimitFeature) f).getTimeLimit();
			}
		}
		return _event.getInt("TimeLimitMs");
	}
	
	@Override
	public void onKill(PlayerEventInfo player, CharacterData target) {
	}
	
	@Override
	public boolean canAttack(PlayerEventInfo player, CharacterData target) {
		return target.getEventInfo() == null || (target.getEventInfo().getEvent() == player.getEvent() && target.getEventInfo().getTeamId() != player.getTeamId());
	}
	
	@Override
	public boolean onAttack(CharacterData cha, CharacterData target) {
		return true;
	}
	
	@Override
	public boolean canSupport(PlayerEventInfo player, CharacterData target) {
		return target.getEventInfo() != null && target.getEventInfo().getEvent() == player.getEvent() && target.getEventInfo().getTeamId() == player.getTeamId();
	}
	
	@Override
	public void onDie(PlayerEventInfo player, CharacterData killer) {
	}
	
	@Override
	public void onDamageGive(CharacterData cha, CharacterData target, int damage, boolean isDOT) {
	}
	
	@Override
	public void onDisconnect(PlayerEventInfo player) {
	}
	
	@Override
	public boolean addDisconnectedPlayer(PlayerEventInfo player, EventManager.DisconnectedPlayerData data) {
		return false;
	}
	
	@Override
	public boolean onSay(PlayerEventInfo player, String text, int channel) {
		if (text.equals(".scheme")) {
			EventManager.getInstance().getHtmlManager().showSelectSchemeForEventWindow(player, "none", getEvent().getEventType().getAltTitle());
			return false;
		}
		if (text.equalsIgnoreCase(".voteabort") || text.equalsIgnoreCase(".voteend")) {
			voteEnd(player);
			return false;
		}
		return true;
	}
	
	private void voteEnd(PlayerEventInfo player) {
		if (_voted == null) {
			_voted = new LinkedList<PlayerEventInfo>();
		}
		if (!_voted.contains(player)) {
			_voted.add(player);
			broadcastMessage("A player voted to end this mini event.", true);
			for (EventTeam t : getTeams()) {
				for (PlayerEventInfo p : t.getPlayers()) {
					if (!_voted.contains(p)) {
						return;
					}
				}
			}
			abortDueToError("Players voted to abort this match.");
		}
	}
	
	@Override
	public boolean onNpcAction(PlayerEventInfo player, NpcData npc) {
		return true;
	}
	
	@Override
	public boolean canUseItem(PlayerEventInfo player, ItemData item) {
		if (notAllovedItems != null && Arrays.binarySearch(notAllovedItems, item.getItemId()) >= 0) {
			player.sendMessage(LanguageEngine.getMsg("event_itemNotAllowed"));
			return false;
		}
		if (item.isScroll()) {
			return false;
		}
		if (item.isPotion() && !_event.getBoolean("allowPotions")) {
			return false;
		}
		for (final AbstractFeature f : getEvent().getMode().getFeatures()) {
			if (f.getType() == EventMode.FeatureType.ItemGrades && !((ItemGradesFeature) f).checkItem(player, item)) {
				return false;
			}
			if (f.getType() == EventMode.FeatureType.Items && !((ItemsFeature) f).checkItem(player, item)) {
				return false;
			}
			if (f.getType() == EventMode.FeatureType.Enchant && !((EnchantFeature) f).checkItem(player, item)) {
				return false;
			}
		}
		if (item.isPetCollar() && !_allowPets) {
			player.sendMessage(LanguageEngine.getMsg("event_petsNotAllowed"));
			return false;
		}
		return true;
	}
	
	@Override
	public void onItemUse(PlayerEventInfo player, ItemData item) {
	}
	
	@Override
	public boolean canUseSkill(PlayerEventInfo player, SkillData skill) {
		if (notAllovedSkillls != null && Arrays.binarySearch(notAllovedSkillls, skill.getId()) >= 0) {
			player.sendMessage(LanguageEngine.getMsg("event_skillNotAllowed"));
			return false;
		}
		if (skill.blockUsage()) {
			return false;
		}
		for (final AbstractFeature f : getEvent().getMode().getFeatures()) {
			if (f.getType() == EventMode.FeatureType.Skills && !((SkillsFeature) f).checkSkill(player, skill)) {
				return false;
			}
		}
		if (!_allowSummons && skill.isSummon()) {
			player.sendMessage(LanguageEngine.getMsg("event_summonsNotAllowed"));
			return false;
		}
		return true;
	}
	
	@Override
	public void onSkillUse(PlayerEventInfo player, SkillData skill) {
	}
	
	@Override
	public boolean canDestroyItem(PlayerEventInfo player, ItemData item) {
		return true;
	}
	
	@Override
	public boolean canInviteToParty(PlayerEventInfo player, PlayerEventInfo target) {
		return target.getEvent() == player.getEvent() && target.getTeamId() == player.getTeamId();
	}
	
	@Override
	public boolean canTransform(PlayerEventInfo player) {
		return true;
	}
	
	@Override
	public boolean canBeDisarmed(PlayerEventInfo player) {
		return true;
	}
	
	@Override
	public int allowTransformationSkill(PlayerEventInfo player, SkillData skill) {
		return 0;
	}
	
	@Override
	public boolean canSaveShortcuts(PlayerEventInfo player) {
		return true;
	}
	
	private RegistrationData _winner = null;
	
	public void setWinner(EventTeam team) {
		if (_winner == null)
			_winner = team.getRegData();
	}
	
	public RegistrationData getWinner() {
		if (_winner == null)
			throw new NullPointerException("Winner cannot be null!");
		
		return _winner;
	}
	
	public RegistrationData[] getRegisteredTeams() {
		return _registeredTeams;
	}
}
