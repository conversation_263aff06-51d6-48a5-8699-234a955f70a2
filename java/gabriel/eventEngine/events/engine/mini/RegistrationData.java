package gabriel.eventEngine.events.engine.mini;

import java.util.List;

import gabriel.eventEngine.interf.PlayerEventInfo;
import gabriel.eventEngine.interf.delegate.PartyData;
import gabriel.eventEngine.l2j.CallBack;

public class RegistrationData {
	private final List<PlayerEventInfo> _players;
	private boolean _choosen = false;
	
	public RegistrationData(List<PlayerEventInfo> players) {
		_players = players;
	}
	
	public PlayerEventInfo getKeyPlayer() {
		return _players.get(0);
	}
	
	public List<PlayerEventInfo> getPlayers() {
		return _players;
	}
	
	public PartyData getParty() {
		if (getKeyPlayer().isOnline()) {
			return getKeyPlayer().getParty();
		}
		return null;
	}
	
	public void register(boolean isRegistered, MiniEventManager registeredEvent) {
		for (PlayerEventInfo pi : _players) {
			pi.setIsRegisteredToMiniEvent(isRegistered, registeredEvent);
			if (!isRegistered) {
				CallBack.getInstance().getPlayerBase().eventEnd(pi);
			}
		}
	}
	
	public void message(String msg, boolean screen) {
		for (PlayerEventInfo pi : _players) {
			if (screen) {
				pi.screenMessage(msg, "", true);
			} else {
				pi.sendMessage(msg);
			}
		}
	}
	
	public int getAverageLevel() {
		int i = 0;
		for (PlayerEventInfo player : _players) {
			i += player.getLevel();
		}
		i = Math.round((float) (i / _players.size()));
		return i;
	}
	
	public boolean isChosen() {
		return _choosen;
	}
	
	public void setIsChosen(boolean b) {
		_choosen = b;
	}
}
