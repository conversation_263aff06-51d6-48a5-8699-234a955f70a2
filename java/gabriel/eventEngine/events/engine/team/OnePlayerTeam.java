package gabriel.eventEngine.events.engine.team;

import gabriel.eventEngine.events.engine.mini.RegistrationData;
import gabriel.eventEngine.interf.PlayerEventInfo;

public class OnePlayerTeam extends EventTeam {
	public OnePlayerTeam(RegistrationData regData, int teamId, String teamName) {
		super(regData, teamId, teamName);
	}
	
	public PlayerEventInfo getPlayer() {
		if (getPlayers().isEmpty()) {
			return null;
		}
		return getPlayers().get(0);
	}
	
	@Override
	protected int getTeamSize() {
		return 1;
	}
}
