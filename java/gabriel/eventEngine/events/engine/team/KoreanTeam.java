package gabriel.eventEngine.events.engine.team;

import java.util.LinkedHashMap;
import java.util.Map;

import gabriel.eventEngine.events.engine.mini.RegistrationData;
import gabriel.eventEngine.interf.PlayerEventInfo;

public class KoreanTeam extends EventTeam {
	private final Map<Integer, Integer> _players;
	private int _order;
	private int _nextPlayer;
	private PlayerEventInfo _fighting;
	
	public KoreanTeam(RegistrationData regData, int teamId, String teamName) {
		super(regData, teamId, teamName);
		_players = new LinkedHashMap<Integer, Integer>(getTeamSize());
		_order = 0;
		_nextPlayer = 0;
		_fighting = null;
	}
	
	public boolean isFighting(PlayerEventInfo player) {
		return _fighting != null && _fighting.getPlayersId() == player.getPlayersId();
	}
	
	@Override
	protected int getTeamSize() {
		return 4;
	}
	
	@Override
	public void addPlayer(PlayerEventInfo pi, boolean init) {
		super.addPlayer(pi, init);
		_order++;
		_players.put(_order, pi.getPlayersId());
	}
	
	public PlayerEventInfo getNextPlayer() {
		if (getPlayers().isEmpty()) {
			return null;
		}
		int next = 0;
		do {
			_nextPlayer++;
			next = _players.get(_nextPlayer);
		}
		while (next == 0);
		for (PlayerEventInfo pi : getPlayers()) {
			if (pi.getPlayersId() == next) {
				return _fighting = pi;
			}
		}
		return null;
	}
}
