package gabriel.eventEngine.events.engine.team;

import gabriel.eventEngine.events.engine.EventManager;
import gabriel.eventEngine.events.engine.mini.RegistrationData;
import gabriel.eventEngine.interf.PlayerEventInfo;

public class FixedPartyTeam extends EventTeam {
	private final int _teamSize;
	
	public FixedPartyTeam(RegistrationData regData, int teamId, String teamName, int size) {
		super(regData, teamId, teamName);
		_teamSize = size;
	}
	
	public FixedPartyTeam(RegistrationData regData, int teamId, int size) {
		super(regData, teamId, EventManager.getInstance().getTeamName(teamId) + " team");
		_teamSize = size;
	}
	
	public PlayerEventInfo getLeader() {
		if (getPlayers().isEmpty()) {
			return null;
		}
		return getPlayers().get(0);
	}
	
	@Override
	protected int getTeamSize() {
		return _teamSize;
	}
}
