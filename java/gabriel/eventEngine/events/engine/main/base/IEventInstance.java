package gabriel.eventEngine.events.engine.main.base;

import gabriel.eventEngine.events.engine.main.events.AbstractMainEvent;
import gabriel.eventEngine.interf.delegate.InstanceData;

import java.util.concurrent.ScheduledFuture;

public interface IEventInstance {
    InstanceData getInstance();

    ScheduledFuture<?> scheduleNextTask(int paramInt);

    AbstractMainEvent.Clock getClock();

    boolean isActive();
}


