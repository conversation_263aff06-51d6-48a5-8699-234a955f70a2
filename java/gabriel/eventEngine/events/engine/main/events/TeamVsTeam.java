package gabriel.eventEngine.events.engine.main.events;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import gabriel.eventEngine.events.GabrielEventsLoader;
import gabriel.eventEngine.events.engine.base.ConfigModel;
import gabriel.eventEngine.l2j.CallBack;
import gabriel.eventEngine.events.engine.base.EventMap;
import gabriel.eventEngine.events.engine.base.EventPlayerData;
import gabriel.eventEngine.events.engine.base.EventSpawn;
import gabriel.eventEngine.events.engine.base.EventType;
import gabriel.eventEngine.events.engine.base.Loc;
import gabriel.eventEngine.events.engine.base.PvPEventPlayerData;
import gabriel.eventEngine.events.engine.base.RewardPosition;
import gabriel.eventEngine.events.engine.base.SpawnType;
import gabriel.eventEngine.events.engine.base.description.EventDescription;
import gabriel.eventEngine.events.engine.base.description.EventDescriptionSystem;
import gabriel.eventEngine.events.engine.lang.LanguageEngine;
import gabriel.eventEngine.events.engine.main.MainEventManager;
import gabriel.eventEngine.events.engine.main.base.MainEventInstanceType;
import gabriel.eventEngine.events.engine.stats.GlobalStatsModel;
import gabriel.eventEngine.events.engine.team.EventTeam;
import gabriel.eventEngine.interf.PlayerEventInfo;
import gabriel.eventEngine.interf.callback.CallbackManager;
import gabriel.eventEngine.interf.delegate.CharacterData;
import gabriel.eventEngine.interf.delegate.InstanceData;
import gabriel.eventEngine.interf.delegate.PartyData;

public class TeamVsTeam extends AbstractMainEvent
{
	protected Map<Integer, TvTEventInstance>	_matches;
	protected boolean							_waweRespawn;
	protected int								_teamsCount;
	
	public TeamVsTeam(EventType type, MainEventManager manager)
	{
		super(type, manager);
		_matches = new ConcurrentHashMap<Integer, TvTEventInstance>();
		setRewardTypes(new RewardPosition[]
		{
			RewardPosition.Winner,
			RewardPosition.Looser,
			RewardPosition.Tie,
			RewardPosition.FirstBlood,
			RewardPosition.FirstRegistered,
			RewardPosition.OnKill,
			RewardPosition.KillingSpree
		});
	}
	
	@Override
	public void loadConfigs()
	{
		super.loadConfigs();
		addConfig(new ConfigModel("killsForReward", "0", "The minimum kills count required to get a reward (includes all possible rewards)."));
		addConfig(new ConfigModel("resDelay", "15", "The delay after which the player is resurrected. In seconds."));
		addConfig(new ConfigModel("waweRespawn", "true", "Enables the wawe-style respawn system.", ConfigModel.InputType.Boolean));
		addConfig(new ConfigModel("createParties", "true", "Put 'True' if you want this event to automatically create parties for players in each team.", ConfigModel.InputType.Boolean));
		addConfig(new ConfigModel("maxPartySize", "9", "The maximum size of party, that can be created. Works only if <font color=LEVEL>createParties</font> is true."));
		addConfig(new ConfigModel("teamsCount", "2", "The count of teams in the event. Max is 5. <font color=FF0000>In order to change the count of teams in the event, you must also edit this config in the Instance's configuration.</font>"));
		addConfig(new ConfigModel("firstBloodMessage", "true", "You can turn off/on the first blood announce in the event (first kill made in the event). This is also rewardable - check out reward type FirstBlood.", ConfigModel.InputType.Boolean));
		addInstanceTypeConfig(new ConfigModel("teamsCount", "2", "You may specify the count of teams only for this instance. This config overrides event default teams count."));
	}
	
	@Override
	public void initEvent()
	{
		super.initEvent();
		_waweRespawn = getBoolean("waweRespawn");
		if (_waweRespawn)
		{
			initWaweRespawns(getInt("resDelay"));
		}
		_runningInstances = 0;
	}
	
	@Override
	protected int initInstanceTeams(MainEventInstanceType type)
	{
		_teamsCount = type.getConfigInt("teamsCount");
		if (_teamsCount < 2 || _teamsCount > 5)
		{
			_teamsCount = getInt("teamsCount");
		}
		if (_teamsCount < 2 || _teamsCount > 5)
		{
			_teamsCount = 2;
		}
		createTeams(_teamsCount, type.getInstance().getId());
		return _teamsCount;
	}
	
	/**
	 * Creates teams for the event instance.
	 */
	@Override
	protected void createTeams(int count, int instanceId)
	{
		if (GabrielEventsLoader.detailedDebug)
		{
			print("UpgradingEvent: creating " + count + " teams for instanceId " + instanceId);
		}
		if (count != 2)
		{
			// System.out.println("UpgradingEvent: Invalid team count " + count + ", forcing 2 teams for Fire and Water");
			count = 2;
		}
		_teams.put(instanceId, new ConcurrentHashMap<>());
		createNewTeam(instanceId, 1, "Fire", "Fire Faction");
		createNewTeam(instanceId, 2, "Water", "Water Faction");
		// System.out.println("UpgradingEvent: Created teams for instance " + instanceId + ": Fire (teamId=1), Water (teamId=2)");
	}
	
	@Override
	public void runEvent()
	{
		if (GabrielEventsLoader.detailedDebug)
		{
			print("Event: started runEvent()");
		}

		// Validate spawns before starting event
		if (!validateSpawns())
		{
			announce("Event cannot start: Missing required spawns for Fire and Water teams.");
			clearEvent();
			return;
		}

		if (!dividePlayers())
		{
			clearEvent();
			return;
		}
		_matches.clear();
		for (InstanceData instance : _instances)
		{
			if (GabrielEventsLoader.detailedDebug)
			{
				print("Event: creating eventinstance for instance " + instance.getId());
			}
			TvTEventInstance match = createEventInstance(instance);
			_matches.put(instance.getId(), match);
			_runningInstances++;
			match.scheduleNextTask(0);
			if (GabrielEventsLoader.detailedDebug)
			{
				print("Event: event instance started");
			}
		}
		if (GabrielEventsLoader.detailedDebug)
		{
			print("Event: finished runEvent()");
		}
	}

	/**
	 * Validates that required spawns exist for both Fire and Water teams
	 */
	private boolean validateSpawns()
	{
		EventMap map = _manager.getMap();
		if (map == null)
		{
			debug("Error: No map assigned to event");
			return false;
		}

		// Check Fire team spawns (team 1)
		if (!map.checkForSpawns(SpawnType.Regular, 1, 1))
		{
			debug("Error: Missing spawn for Fire team (team 1)");
			return false;
		}

		// Check Water team spawns (team 2)
		if (!map.checkForSpawns(SpawnType.Regular, 2, 1))
		{
			debug("Error: Missing spawn for Water team (team 2)");
			return false;
		}

		if (GabrielEventsLoader.detailedDebug)
		{
			print("Event: Spawn validation passed - Fire and Water team spawns available");
		}
		return true;
	}
	
	@Override
	public void onEventEnd()
	{
		if (GabrielEventsLoader.detailedDebug)
		{
			print("Event: onEventEnd()");
		}
		int minKills = getInt("killsForReward");
		rewardAllTeams(-1, minKills, minKills);
	}
	
	@Override
	protected synchronized boolean instanceEnded()
	{
		_runningInstances--;
		if (GabrielEventsLoader.detailedDebug)
		{
			print("Event: notifying instance ended: runningInstances = " + _runningInstances);
		}
		if (_runningInstances == 0)
		{
			_manager.end();
			return true;
		}
		return false;
	}
	
	@Override
	protected synchronized void endInstance(int instance, boolean canBeAborted, boolean canRewardIfAborted, boolean forceNotReward)
	{
		if (GabrielEventsLoader.detailedDebug)
		{
			print("Event: endInstance() " + instance + ", canBeAborted " + canBeAborted + ", canReward.. " + canRewardIfAborted + " forceNotReward " + forceNotReward);
		}
		if (forceNotReward)
		{
			_matches.get(instance).forceNotRewardThisInstance();
		}
		_matches.get(instance).setNextState(EventState.END);
		if (canBeAborted)
		{
			_matches.get(instance).setCanBeAborted();
		}
		if (canRewardIfAborted)
		{
			_matches.get(instance).setCanRewardIfAborted();
		}
		_matches.get(instance).scheduleNextTask(0);
	}
	
	@Override
	protected String getScorebar(int instance)
	{
		final int count = _teams.get(instance).size();
		StringBuilder tb = new StringBuilder();
		for (EventTeam team : _teams.get(instance).values())
		{
			if (count <= 4)
			{
				tb.append(team.getTeamName() + ": " + team.getScore() + "  ");
			}
			else
			{
				tb.append(team.getTeamName().substring(0, 1) + ": " + team.getScore() + "  ");
			}
		}
		if (count <= 3)
		{
			tb.append(LanguageEngine.getMsg("event_scorebar_time", _matches.get(instance).getClock().getTime()));
		}
		return tb.toString();
	}
	
	@Override
	protected String getTitle(PlayerEventInfo pi)
	{
		if (_hideTitles)
		{
			return "";
		}
		if (pi.isAfk())
		{
			return "AFK";
		}
		return "Kills: " + getPlayerData(pi).getScore() + " Deaths: " + getPlayerData(pi).getDeaths();
	}
	
	@Override
	public void onKill(PlayerEventInfo player, CharacterData target)
	{
		if (target.getEventInfo() == null)
		{
			return;
		}
		if (player.getTeamId() != target.getEventInfo().getTeamId())
		{
			tryFirstBlood(player);
			giveOnKillReward(player);
			player.getEventTeam().raiseScore(1);
			player.getEventTeam().raiseKills(1);
			getPlayerData(player).raiseScore(1);
			getPlayerData(player).raiseKills(1);
			getPlayerData(player).raiseSpree(1);
			giveKillingSpreeReward(getPlayerData(player));
			if (player.isTitleUpdated())
			{
				player.setTitle(getTitle(player), true);
				player.broadcastTitleInfo();
			}
			CallbackManager.getInstance().playerKills(getEventType(), player, target.getEventInfo());
			setScoreStats(player, getPlayerData(player).getScore());
			setKillsStats(player, getPlayerData(player).getKills());
		}
	}
	
	@Override
	public void onDie(PlayerEventInfo player, CharacterData killer)
	{
		if (GabrielEventsLoader.detailedDebug)
		{
			print("/// Event: onDie - player " + player.getPlayersName() + " (instance " + player.getInstanceId() + "), killer " + killer.getName());
		}
		getPlayerData(player).raiseDeaths(1);
		getPlayerData(player).setSpree(0);
		setDeathsStats(player, getPlayerData(player).getDeaths());
		if (player.isTitleUpdated())
		{
			player.setTitle(getTitle(player), true);
			player.broadcastTitleInfo();
		}
		if (_waweRespawn)
		{
			_waweScheduler.addPlayer(player);
		}
		else
		{
			scheduleRevive(player, getInt("resDelay") * 1000);
		}
	}
	
	@Override
	public EventPlayerData createPlayerData(PlayerEventInfo player)
	{
		return new PvPEventPlayerData(player, this, new GlobalStatsModel(getEventType()));
	}
	
	@Override
	public PvPEventPlayerData getPlayerData(PlayerEventInfo player)
	{
		return (PvPEventPlayerData) player.getEventData();
	}
	
	@Override
	public synchronized void clearEvent(int instanceId)
	{
		if (GabrielEventsLoader.detailedDebug)
		{
			print("Event: called CLEAREVENT for instance " + instanceId);
		}
		try
		{
			if (_matches != null)
			{
				for (TvTEventInstance match : _matches.values())
				{
					if (instanceId == 0 || instanceId == match.getInstance().getId())
					{
						match.abort();
					}
				}
			}
		}
		catch (Exception e)
		{
			e.printStackTrace();
		}

		// Clean up wave respawn scheduler
		if (_waweRespawn && _waweScheduler != null)
		{
			_waweScheduler.stop();
		}

		for (PlayerEventInfo player : getPlayers(instanceId))
		{
			if (player.isOnline())
			{
				if (player.isParalyzed())
				{
					player.setIsParalyzed(false);
				}
				if (player.isImmobilized())
				{
					player.unroot();
				}
				if (!player.isGM())
				{
					player.setIsInvul(false);
				}
				player.removeRadarAllMarkers();
				player.setInstanceId(0);
				if (_removeBuffsOnEnd)
				{
					player.removeBuffs();
				}
				player.restoreData();
				player.teleport(player.getOrigLoc(), 0, true, 0);
				player.sendMessage(LanguageEngine.getMsg("event_teleportBack"));
				if (player.getParty() != null)
				{
					PartyData party = player.getParty();
					party.removePartyMember(player);
				}
				player.broadcastUserInfo();

				// Auto-arrange inventory after event cleanup
				CallBack.getInstance().getOut().scheduleGeneral(() -> {
					if (player.isOnline() && player.getOwner() != null)
					{
						autoArrangeInventory(player);
						//player.sendMessage("Hành trang của bạn đã được tự động sắp xếp lại sau event!");
					}
				}, 2000); // 2 second delay to ensure all cleanup is complete
			}
		}
		clearPlayers(true, instanceId);
	}
	
	@Override
	public synchronized void clearEvent()
	{
		if (GabrielEventsLoader.detailedDebug)
		{
			print("Event: called global clearEvent()");
		}
		clearEvent(0);
	}
	
	@Override
	protected void respawnPlayer(PlayerEventInfo pi, int instance)
	{
		if (GabrielEventsLoader.detailedDebug)
		{
			print("/// Event: respawning player " + pi.getPlayersName() + ", instance " + instance);
		}
		EventSpawn spawn = getSpawn(SpawnType.Regular, pi.getTeamId());
		if (spawn != null)
		{
			Loc loc = new Loc(spawn.getLoc().getX(), spawn.getLoc().getY(), spawn.getLoc().getZ());
			loc.addRadius(spawn.getRadius());
			pi.teleport(loc, 0, true, instance);
			pi.sendMessage(LanguageEngine.getMsg("event_respawned"));
		}
		else
		{
			// Try fallback spawn if team spawn not found
			EventSpawn fallbackSpawn = getSpawn(SpawnType.Regular, -1);
			if (fallbackSpawn != null)
			{
				Loc loc = new Loc(fallbackSpawn.getLoc().getX(), fallbackSpawn.getLoc().getY(), fallbackSpawn.getLoc().getZ());
				loc.addRadius(fallbackSpawn.getRadius());
				pi.teleport(loc, 0, true, instance);
				pi.sendMessage(LanguageEngine.getMsg("event_respawned"));
				debug("Warning: Used fallback spawn for player " + pi.getPlayersName() + " team " + pi.getTeamId());
			}
			else
			{
				debug("Error on respawnPlayer - no spawn type REGULAR, team " + pi.getTeamId() + " has been found. Event aborted.");
				endInstance(instance, true, false, true);
			}
		}
	}
	
	@Override
	public String getEstimatedTimeLeft()
	{
		if (_matches == null)
		{
			return "Starting";
		}
		for (TvTEventInstance match : _matches.values())
		{
			if (match.isActive())
			{
				return match.getClock().getTime();
			}
		}
		return "N/A";
	}
	
	@Override
	public int getTeamsCount()
	{
		return getInt("teamsCount");
	}
	
	@Override
	public String getMissingSpawns(EventMap map)
	{
		StringBuilder tb = new StringBuilder();
		for (int i = 0; i < getTeamsCount(); i++)
		{
			if (!map.checkForSpawns(SpawnType.Regular, i + 1, 1))
			{
				tb.append(addMissingSpawn(SpawnType.Regular, i + 1, 1));
			}
		}
		return tb.toString();
	}
	
	@Override
	public String getHtmlDescription()
	{
		if (_htmlDescription == null)
		{
			EventDescription desc = EventDescriptionSystem.getInstance().getDescription(getEventType());
			if (desc != null)
			{
				_htmlDescription = desc.getDescription(getConfigs());
			}
			else
			{
				_htmlDescription = getInt("teamsCount") + " teams fighting against each other. ";
				_htmlDescription += "Gain score by killing your opponents";
				if (getInt("killsForReward") > 0)
				{
					_htmlDescription = _htmlDescription + " (at least " + getInt("killsForReward") + " kill(s) is required to receive a reward)";
				}
				if (getBoolean("waweRespawn"))
				{
					_htmlDescription = _htmlDescription + " and dead players are resurrected by an advanced wawe-spawn engine each " + getInt("resDelay") + " seconds";
				}
				else
				{
					_htmlDescription = _htmlDescription + " and if you die, you will be resurrected in " + getInt("resDelay") + " seconds";
				}
				if (getBoolean("createParties"))
				{
					_htmlDescription += ". The event automatically creates parties on start";
				}
				_htmlDescription += ".";
			}
		}
		return _htmlDescription;
	}
	
	@Override
	protected AbstractEventInstance getMatch(int instanceId)
	{
		return _matches.get(instanceId);
	}
	
	@Override
	protected TvTEventData createEventData(int instanceId)
	{
		return new TvTEventData(instanceId);
	}
	
	@Override
	protected TvTEventInstance createEventInstance(InstanceData instance)
	{
		return new TvTEventInstance(instance);
	}
	
	@Override
	protected TvTEventData getEventData(int instance)
	{
		return _matches.get(instance)._data;
	}
	
	protected enum EventState
	{
		START,
		FIGHT,
		END,
		TELEPORT,
		INACTIVE;
	}
	
	protected class TvTEventInstance extends AbstractEventInstance
	{
		protected EventState	_state;
		protected TvTEventData	_data;
		
		protected TvTEventInstance(InstanceData instance)
		{
			super(instance);
			_state = EventState.START;
			_data = createEventData(instance.getId());
		}
		
		protected void setNextState(EventState state)
		{
			_state = state;
		}
		
		@Override
		public boolean isActive()
		{
			return _state != EventState.INACTIVE;
		}
		
		@Override
		public void run()
		{
			try
			{
				if (GabrielEventsLoader.detailedDebug)
				{
					print("Event: running task of state " + _state.toString() + "...");
				}
				switch (_state)
				{
					case START:
					{
						if (checkPlayers(_instance.getId()))
						{
							teleportPlayers(_instance.getId(), SpawnType.Regular, false);
							setupTitles(_instance.getId());
							enableMarkers(_instance.getId(), true);
							// Create parties before sitting players to avoid issues
							if (getBoolean("createParties"))
							{
								createParties(getInt("maxPartySize"));
							}
							forceSitAll(_instance.getId());
							setNextState(EventState.FIGHT);
							scheduleNextTask(10000);
							break;
						}
						break;
					}
					case FIGHT:
					{
						forceStandAll(_instance.getId());
						setNextState(EventState.END);
						_clock.startClock(_manager.getRunTime());
						break;
					}
					case END:
					{
						_clock.setTime(0, true);
						setNextState(EventState.INACTIVE);
						if (!instanceEnded() && _canBeAborted)
						{
							if (_canRewardIfAborted)
							{
								rewardAllTeams(_instance.getId(), getInt("killsForReward"), getInt("killsForReward"));
							}
							clearEvent(_instance.getId());
							break;
						}
						break;
					}
				}
				if (GabrielEventsLoader.detailedDebug)
				{
					print("Event: ... finished running task. next state " + _state.toString());
				}
			}
			catch (Throwable e)
			{
				e.printStackTrace();
				_manager.endDueToError(LanguageEngine.getMsg("event_error"));
			}
		}
	}
	
	protected class TvTEventData extends AbstractEventData
	{
		public TvTEventData(int instance)
		{
			super(instance);
		}
	}
}
