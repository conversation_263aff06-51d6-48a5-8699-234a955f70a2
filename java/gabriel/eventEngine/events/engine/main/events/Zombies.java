package gabriel.eventEngine.events.engine.main.events;

import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ScheduledFuture;
import java.util.logging.Level;

import gabriel.eventEngine.events.EventGame;
import gabriel.eventEngine.events.GabrielEventsLoader;
import gabriel.eventEngine.events.engine.base.ConfigModel;
import gabriel.eventEngine.events.engine.base.EventPlayerData;
import gabriel.eventEngine.events.engine.base.EventSpawn;
import gabriel.eventEngine.events.engine.base.EventType;
import gabriel.eventEngine.events.engine.base.Loc;
import gabriel.eventEngine.events.engine.base.PvPEventPlayerData;
import gabriel.eventEngine.events.engine.base.RewardPosition;
import gabriel.eventEngine.events.engine.base.SpawnType;
import gabriel.eventEngine.events.engine.base.description.EventDescription;
import gabriel.eventEngine.events.engine.base.description.EventDescriptionSystem;
import gabriel.eventEngine.events.engine.lang.LanguageEngine;
import gabriel.eventEngine.events.engine.main.MainEventManager;
import gabriel.eventEngine.events.engine.main.base.MainEventInstanceType;
import gabriel.eventEngine.events.engine.stats.GlobalStatsModel;
import gabriel.eventEngine.events.engine.team.EventTeam;
import gabriel.eventEngine.interf.PlayerEventInfo;
import gabriel.eventEngine.interf.callback.CallbackManager;
import gabriel.eventEngine.interf.delegate.CharacterData;
import gabriel.eventEngine.interf.delegate.InstanceData;
import gabriel.eventEngine.interf.delegate.ItemData;
import gabriel.eventEngine.interf.delegate.PartyData;
import gabriel.eventEngine.interf.delegate.SkillData;
import gabriel.eventEngine.l2j.CallBack;

public class Zombies extends HuntingGrounds {
	private static final int SURVIVOR_TEAM_ID = 1;
	private static final int ZOMBIE_TEAM_ID = 2;
	private Map<Integer, Integer> _skillsForSurvivors;
	private Map<Integer, Integer> _skillsForZombies;
	private String _zombiesCount;
	private int _zombieTransformId;
	protected int _zombieInactivityTime;
	private int _zombieMinLevel;
	private int _zombieMinPvps;
	private int _zombieKillScore;
	private int _survivorKillScore;
	protected int _zombiesInitialScore;
	
	public Zombies(EventType type, MainEventManager manager) {
		super(type, manager);
		_skillsForSurvivors = new ConcurrentHashMap<Integer, Integer>();
		_skillsForZombies = new ConcurrentHashMap<Integer, Integer>();
		_zombieKillScore = 1;
		_survivorKillScore = 1;
		_zombiesInitialScore = 0;
		setRewardTypes(new RewardPosition[] {
			RewardPosition.Looser,
			RewardPosition.Tie,
			RewardPosition.Numbered,
			RewardPosition.Range,
			RewardPosition.FirstBlood,
			RewardPosition.FirstRegistered,
			RewardPosition.OnKill,
			RewardPosition.KillingSpree
		});
	}
	
	@Override
	public void loadConfigs() {
		super.loadConfigs();
		removeConfig("skillsForAllPlayers");
		removeConfig("bowWeaponId");
		removeConfig("arrowItemId");
		removeConfig("pistolItemId");
		removeConfig("orbItemId");
		removeConfig("spearItemId");
		removeConfig("teamsCount");
		removeConfig("createParties");
		removeConfig("maxPartySize");
		removeConfig("teamsCount");
		removeConfig("firstBloodMessage");
		removeConfig("waweRespawn");
		addConfig(new ConfigModel("waweRespawn", "true", "Enables the wawe-style respawn system for ", ConfigModel.InputType.Boolean));
		addConfig(new ConfigModel("skillsForPlayers", "90002-1", "IDs of skills which will be given to every survivor (non zombie player) on the event. The purpose of this is to make all survivors equally strong. Format: <font color=LEVEL>SKILLID-LEVEL</font> (eg. '35001-1').", ConfigModel.InputType.MultiAdd));
		addConfig(new ConfigModel("skillsForZombies", "90003-1", "IDs of skills which will be given to every zombie on the event. The purpose of this is to make all zombies equally strong. Format: <font color=LEVEL>SKILLID-LEVEL</font> (eg. '35002-1').", ConfigModel.InputType.MultiAdd));
		addConfig(new ConfigModel("bowWeaponId", "271", "The ID of the bow item which will be given to the survivors (non zombies) and will be the only weapon most players will use during the event. This weapon kills zombies with just one hit."));
		addConfig(new ConfigModel("arrowItemId", "17", "The ID of the arrows which will be given to the player in the event."));
		addConfig(new ConfigModel("pistolItemId", "95410", "The ID of the pistol item which will be given to the survivors (non zombies) and will be the only weapon most players will use during the event. This weapon kills zombies with just one hit."));
		addConfig(new ConfigModel("orbItemId", "94891", "The ID of the orb which will be given to the player in the event."));
		addConfig(new ConfigModel("spearItemId", "97100", "The ID of the spear item which will be given to the survivors (non zombies) and will be the only weapon most players will use during the event. This weapon kills zombies with just one hit."));
		addConfig(new ConfigModel("enableAmmoSystem", "true", "Enable/disable the ammo system based on player's mana. Player's max MP is defaultly modified by a custom passive skill and everytime a player shots and arrow, his MP decreases by a value which is calculated from the ammount of ammo. There is also a MP regeneration system - see the configs below.", ConfigModel.InputType.Boolean));
		addConfig(new ConfigModel("ammoAmmount", "10", "Works if ammo system is enabled. Specifies the max ammount of ammo every player can have."));
		addConfig(new ConfigModel("ammoRestoredPerTick", "1", "Works if ammo system is enabled. Defines the ammount of ammo given to every player each <font color=LEVEL>'ammoRegTickInterval'</font> (configurable) seconds."));
		addConfig(new ConfigModel("ammoRegTickInterval", "10", "Works if ammo system is enabled. Defines the interval of restoring player's ammo. The value is in seconds (eg. value 10 will give ammo every 10 seconds to every player - the ammount of restored ammo is configurable (config <font color=LEVEL>ammoRestoredPerTick</font>)."));
		addConfig(new ConfigModel("countOfZombies", "1/10", "Defines the count of players transformed to zombies in the start of the event. Format: #ZOMBIES/#PLAYERS - <font color=LEVEL>eg. 1/10</font> means there's <font color=LEVEL>1</font> zombie when there are <font color=LEVEL>10</font> players in the event (20 players - 2 zombies, 100 players - 10 zombies, ...). There's always at least one zombie in the event."));
		addConfig(new ConfigModel("zombieTransformId", "303", "The ID of transformation used to morph players into "));
		addConfig(new ConfigModel("zombieInactivityTime", "300", "In seconds. If no player is killed (by zombie) during this time, one random player will be transformed into a zombie and respawned on Zombie respawn (away from other players). Write 0 to disable this feature."));
		addConfig(new ConfigModel("zombieMinLevel", "0", "The minimum level required to become a zombie IN THE START OF THE EVENT."));
		addConfig(new ConfigModel("zombieMinPvPs", "0", "The minimum count of pvps required to become a zombie IN THE START OF THE EVENT."));
		addConfig(new ConfigModel("zombieKillScore", "1", "The count of score points given to a zombie when he kills a player."));
		addConfig(new ConfigModel("survivorKillScore", "1", "The count of score points given to a survivor when he kills a zombie."));
		addConfig(new ConfigModel("zombiesInitialScore", "1", "The initial score given to every zombie who gets automatically transformed in the beginning of the event."));
	}
	
	@Override
	public void initEvent() {
		super.initEvent();
		_bowItemId = getInt("bowWeaponId");
		_arrowItemId = getInt("arrowItemId");
		_pistolItemId = getInt("pistolItemId");
		_orbItemId = getInt("orbItemId");
		_spearItemId = getInt("spearItemId");
		_ammoSystem = getBoolean("enableAmmoSystem");
		_ammoAmmount = getInt("ammoAmmount");
		_ammoRegPerTick = getInt("ammoRestoredPerTick");
		_tickLength = getInt("ammoRegTickInterval");
		_zombiesCount = getString("countOfZombies");
		_zombieTransformId = getInt("zombieTransformId");
		_zombieInactivityTime = getInt("zombieInactivityTime");
		_zombieMinLevel = getInt("zombieMinLevel");
		_zombieMinPvps = getInt("zombieMinPvPs");
		_zombieKillScore = getInt("zombieKillScore");
		_survivorKillScore = getInt("survivorKillScore");
		_zombiesInitialScore = getInt("zombiesInitialScore");
		if (!getString("skillsForPlayers").equals("")) {
			String[] splits = getString("skillsForPlayers").split(",");
			_skillsForSurvivors.clear();
			try {
				for (String split : splits) {
					String id = split.split("-")[0];
					String level = split.split("-")[1];
					_skillsForSurvivors.put(Integer.parseInt(id), Integer.parseInt(level));
				}
			} catch (Exception e) {
				GabrielEventsLoader.debug("Error while loading config 'skillsForPlayers' for event " + getEventName() + " - " + e.toString(), Level.SEVERE);
			}
		}
		if (!getString("skillsForZombies").equals("")) {
			String[] splits = getString("skillsForZombies").split(",");
			_skillsForZombies.clear();
			try {
				for (String split : splits) {
					String id = split.split("-")[0];
					String level = split.split("-")[1];
					_skillsForZombies.put(Integer.parseInt(id), Integer.parseInt(level));
				}
			} catch (Exception e) {
				GabrielEventsLoader.debug("Error while loading config 'skillsForZombies' for event " + getEventName() + " - " + e.toString(), Level.SEVERE);
			}
		}
		_tick = 0;
	}
	
	@Override
	protected int initInstanceTeams(MainEventInstanceType type) {
		createTeams(_teamsCount = 2, type.getInstance().getId());
		return _teamsCount;
	}
	
	@Override
	protected void createTeams(int count, int instanceId) {
		createNewTeam(instanceId, SURVIVOR_TEAM_ID, "Survivors", "Survivors");
		createNewTeam(instanceId, ZOMBIE_TEAM_ID, "Zombies", "Zombies");
	}
	
	@Override
	protected void dividePlayersToTeams(int instanceId, List<PlayerEventInfo> players, int teamsCount) {
		for (PlayerEventInfo pi : players) {
			pi.onEventStart(this);
			_teams.get(instanceId).get(SURVIVOR_TEAM_ID).addPlayer(pi, true);
		}
	}
	
	@Override
	public void runEvent() {
		if (GabrielEventsLoader.detailedDebug) {
			print("Event: started runEvent()");
		}
		if (!dividePlayers()) {
			clearEvent();
			return;
		}
		_matches.clear();
		for (InstanceData instance : _instances) {
			if (GabrielEventsLoader.detailedDebug) {
				print("Event: creating eventinstance for instance " + instance.getId());
			}
			TvTEventInstance match = createEventInstance(instance);
			_matches.put(instance.getId(), match);
			_runningInstances++;
			match.scheduleNextTask(0);
			if (GabrielEventsLoader.detailedDebug) {
				print("Event: event instance started");
			}
		}
		if (GabrielEventsLoader.detailedDebug) {
			print("Event: finished runEvent()");
		}
	}
	
	protected void scheduleSelectZombies(final int instanceId, long delay, final boolean firstRun, final int forceAddNewZombieCount) {
		if (delay == 0L) {
			CallBack.getInstance().getOut().executeTask(() -> {
				List<PlayerEventInfo> newZombies = calculateZombies(instanceId, (forceAddNewZombieCount > 0) ? forceAddNewZombieCount : -1, firstRun);
				if (newZombies != null) {
					for (PlayerEventInfo zombie : newZombies) {
						transformToZombie(zombie);
						try {
							if (firstRun && (_zombiesInitialScore > 0)) {
								zombie.getEventTeam().raiseScore(_zombiesInitialScore);
								getPlayerData(zombie).raiseScore(_zombiesInitialScore);
							} else {
								continue;
							}
						} catch (Exception e) {
							e.printStackTrace();
						}
					}
				}
			});
		} else {
			CallBack.getInstance().getOut().scheduleGeneral(() -> {
				List<PlayerEventInfo> newZombies = calculateZombies(instanceId, (forceAddNewZombieCount > 0) ? forceAddNewZombieCount : -1, firstRun);
				if (newZombies != null) {
					for (PlayerEventInfo zombie : newZombies) {
						transformToZombie(zombie);
						try {
							if (firstRun && (_zombiesInitialScore > 0)) {
								zombie.getEventTeam().raiseScore(_zombiesInitialScore);
								getPlayerData(zombie).raiseScore(_zombiesInitialScore);
							} else {
								continue;
							}
						} catch (Exception e) {
							e.printStackTrace();
						}
					}
				}
			}, delay);
		}
	}
	
	protected List<PlayerEventInfo> calculateZombies(int instanceId, int countToSpawn, boolean start) {
		int playersCount = getPlayers(instanceId).size();
		int survivorsCount = _teams.get(instanceId).get(SURVIVOR_TEAM_ID).getPlayers().size();
		int zombiesCount = _teams.get(instanceId).get(ZOMBIE_TEAM_ID).getPlayers().size();
		if (countToSpawn <= 0) {
			int zombies = Integer.parseInt(_zombiesCount.split("/")[0]);
			int players = Integer.parseInt(_zombiesCount.split("/")[1]);
			if (start) {
				countToSpawn = (int) Math.floor((playersCount / players) * zombies);
				if (countToSpawn < 1) {
					countToSpawn = 1;
				}
			} else {
				countToSpawn = (countToSpawn = (int) Math.floor((playersCount / players) * zombies)) - zombiesCount;
			}
		}
		int i = 0;
		List<PlayerEventInfo> newZombies = new LinkedList<PlayerEventInfo>();
		if (countToSpawn >= survivorsCount) {
			countToSpawn = survivorsCount - 1;
		}
		if (countToSpawn > 0) {
			for (PlayerEventInfo player : getPlayers(instanceId)) {
				if (!start || ((player.getLevel() >= _zombieMinLevel) && (player.getPvpKills() >= _zombieMinPvps))) {
					newZombies.add(player);
					i++;
					if (i >= countToSpawn) {
						break;
					}
				}
			}
		}
		return newZombies;
	}
	
	@Override
	protected void preparePlayers(int instanceId, boolean start) {
		for (PlayerEventInfo player : getPlayers(instanceId)) {
			preparePlayer(player, start);
		}
	}
	
	protected void preparePlayer(PlayerEventInfo player, boolean start) {
		SkillData skill = null;
		if (player.getEventTeam().getTeamId() == SURVIVOR_TEAM_ID) {
			if (start) {
				if (_skillsForSurvivors != null) {
					for (Entry<Integer, Integer> e : _skillsForSurvivors.entrySet()) {
						skill = new SkillData(e.getKey(), e.getValue());
						if (skill.exists()) {
							player.addSkill(skill, false);
						}
					}
					player.sendSkillList();
				}
				ItemData wpn = player.getPaperdollItem(CallBack.getInstance().getValues().PAPERDOLL_RHAND());
				if (wpn != null) {
					player.unEquipItemInBodySlotAndRecord(CallBack.getInstance().getValues().SLOT_R_HAND());
				}
				wpn = player.getPaperdollItem(CallBack.getInstance().getValues().PAPERDOLL_LHAND());
				if (wpn != null) {
					player.unEquipItemInBodySlotAndRecord(CallBack.getInstance().getValues().SLOT_L_HAND());
				}
				if (player.isSylphClass()) {
					ItemData flagItem = player.addItem(_pistolItemId, 1, false);
					player.equipItem(flagItem);
					player.addItem(_orbItemId, 1, false);
				} else if (player.isVanguard()) {
					ItemData flagItem = player.addItem(_spearItemId, 1, false);
					player.equipItem(flagItem);
				} else {
					ItemData flagItem = player.addItem(_bowItemId, 1, false);
					player.equipItem(flagItem);
					player.addItem(_arrowItemId, 400, false);
				}
			} else {
				// Remove survivor skills
				if (_skillsForSurvivors != null) {
					for (Entry<Integer, Integer> e : _skillsForSurvivors.entrySet()) {
						skill = new SkillData(e.getKey(), e.getValue());
						if (skill.exists()) {
							player.removeSkill(skill.getId());
						}
					}
				}

				// Safely remove weapons
				try {
					ItemData wpn = player.getPaperdollItem(CallBack.getInstance().getValues().PAPERDOLL_RHAND());
					if (wpn != null && wpn.exists()) {
						ItemData[] unequiped = player.unEquipItemInBodySlotAndRecord(wpn.getBodyPart());
						player.inventoryUpdate(unequiped);
					}

					// Remove event items safely
					if (player.isSylphClass()) {
						player.destroyItemByItemId(_pistolItemId, 1);
						player.destroyItemByItemId(_orbItemId, 1);
					} else if (player.isVanguard() || player.isDeathKnight()) {
						player.destroyItemByItemId(_spearItemId, 1);
					} else {
						player.destroyItemByItemId(_bowItemId, 1);
						// Remove all arrows
						while (player.getOwner().getInventory().getItemByItemId(_arrowItemId) != null) {
							player.destroyItemByItemId(_arrowItemId, 1);
						}
					}
				} catch (Exception e) {
					if (GabrielEventsLoader.detailedDebug) {
						print("Zombies: Error removing weapons from player " + player.getPlayersName() + ": " + e.getMessage());
					}
				}
			}
		} else if (player.getEventTeam().getTeamId() == ZOMBIE_TEAM_ID) {
			if (start) {
				if (_skillsForZombies != null) {
					for (Entry<Integer, Integer> e : _skillsForZombies.entrySet()) {
						skill = new SkillData(e.getKey(), e.getValue());
						if (skill.exists()) {
							player.addSkill(skill, false);
						}
					}
					player.sendSkillList();
				}
			} else {
				if (_skillsForZombies != null) {
					for (Entry<Integer, Integer> e : _skillsForZombies.entrySet()) {
						skill = new SkillData(e.getKey(), e.getValue());
						if (skill.exists()) {
							player.removeSkill(skill.getId());
						}
					}
				}
			}
		}
	}
	
	protected void zombiesInactive(int instanceId) {
		scheduleSelectZombies(instanceId, 0L, false, 1);
	}
	
	protected void transformToZombie(final PlayerEventInfo player) {
		try {
			if (player.getTeamId() == ZOMBIE_TEAM_ID) {
				// Already a zombie, don't transform again
				if (GabrielEventsLoader.detailedDebug) {
					print("Zombies: Player " + player.getPlayersName() + " is already a zombie, skipping transform");
				}
				return;
			}

			preparePlayer(player, false);
			player.getEventTeam().removePlayer(player);
			_teams.get(player.getInstanceId()).get(ZOMBIE_TEAM_ID).addPlayer(player, true);
			preparePlayer(player, true);
			player.transform(_zombieTransformId);
			getEventData(player.getInstanceId()).setKillMade();

			if (checkIfAnyPlayersLeft(player.getInstanceId())) {
				CallBack.getInstance().getOut().scheduleGeneral(() -> {
					// Enhanced null checks
					AbstractEventInstance match = getMatch(player.getInstanceId());
					if (player.isOnline() && match != null && match.isActive() && player.getTeamId() == ZOMBIE_TEAM_ID) {
						respawnPlayer(player, player.getInstanceId());
						player.sendMessage("You have been infected! You will respawn as a zombie in 10 seconds.");
					}
				}, 10000L);
			}
			player.setTitle(getTitle(player), true);
			player.broadcastTitleInfo();
		} catch (Exception e) {
			if (GabrielEventsLoader.detailedDebug) {
				print("Zombies: Error transforming player " + player.getPlayersName() + " to zombie: " + e.getMessage());
			}
		}
	}
	
	protected void transformToPlayer(PlayerEventInfo player, boolean endOfEvent) {
		if (endOfEvent) {
			player.untransform(true);
		} else {
			try {
				if (player.getTeamId() == ZOMBIE_TEAM_ID) {
					preparePlayer(player, false);
					player.untransform(true);
					player.getEventTeam().removePlayer(player);
					_teams.get(player.getInstanceId()).get(SURVIVOR_TEAM_ID).addPlayer(player, true);
					preparePlayer(player, true);
					if (player.isDead()) {
						CallBack.getInstance().getOut().scheduleGeneral(() -> {
							if (player.isOnline()) {
								respawnPlayer(player, player.getInstanceId());
							}
						}, 10000L);
					}
				}
			} catch (Exception e) {
				GabrielEventsLoader.debug("error while untransforming zombie:");
				clearEvent();
				e.printStackTrace();
			}
		}
	}
	
	protected void untransformAll(int instanceId) {
		for (PlayerEventInfo player : getPlayers(instanceId)) {
			if (player.getTeamId() == ZOMBIE_TEAM_ID) {
				transformToPlayer(player, true);
			}
		}
	}


	
	protected void setAllZombies(int instanceId) {
		for (PlayerEventInfo player : getPlayers(instanceId)) {
			try {
				if (player.getTeamId() != SURVIVOR_TEAM_ID) {
					continue;
				}
				player.getEventTeam().removePlayer(player);
				_teams.get(player.getInstanceId()).get(ZOMBIE_TEAM_ID).addPlayer(player, true);
			} catch (Exception e) {
				e.printStackTrace();
			}
		}
	}
	
	private boolean checkIfAnyPlayersLeft(int instanceId) {
		synchronized (_teams) {
			if (_teams.get(instanceId).get(SURVIVOR_TEAM_ID).getPlayers().size() <= 0) {
				announce(instanceId, "All survivors have died!");
				endInstance(instanceId, true, true, false);
				return false;
			}
		}
		return true;
	}
	
	@Override
	public void onEventEnd() {
		if (GabrielEventsLoader.detailedDebug) {
			print("Event: onEventEnd()");
		}
		int minScore = getInt("killsForReward");
		rewardAllPlayersFromTeam(-1, minScore, 0, ZOMBIE_TEAM_ID);
	}


	
	@Override
	protected String getTitle(final PlayerEventInfo pi) {
		if (pi.isAfk()) {
			return "AFK";
		}
		if (pi.getTeamId() == ZOMBIE_TEAM_ID) {
			return "~ ZOMBIE ~";
		}
		return "Score: " + getPlayerData(pi).getScore();
	}
	
	@Override
	protected String getScorebar(int instance) {
		final int count = _teams.get(instance).size();
		StringBuilder tb = new StringBuilder();
		for (EventTeam team : _teams.get(instance).values()) {
			tb.append(team.getTeamName() + ": " + team.getPlayers().size() + "  ");
		}
		if (count <= 3) {
			tb.append(LanguageEngine.getMsg("event_scorebar_time", _matches.get(instance).getClock().getTime()));
		}
		return tb.toString();
	}
	
	@Override
	protected void clockTick() {
		_tick++;
		if ((_tick % _tickLength) != 0) {
			return;
		}
		if (_ammoSystem) {
			int oneAmmoMp = 0;
			for (TvTEventInstance match : _matches.values()) {
				for (PlayerEventInfo player : getPlayers(match.getInstance().getId())) {
					if (player.getTeamId() == SURVIVOR_TEAM_ID) {
						try {
							oneAmmoMp = player.getMaxMp() / _ammoAmmount;
							int mpToRegenerate = _ammoRegPerTick * oneAmmoMp;
							int currentMp = (int) player.getCurrentMp();
							if (currentMp >= player.getMaxMp()) {
								continue;
							}
							int toAdd = mpToRegenerate;
							if ((currentMp + mpToRegenerate) > player.getMaxMp()) {
								toAdd = player.getMaxMp() - currentMp;
							}
							player.setCurrentMp(currentMp + toAdd);
						} catch (NullPointerException e) {
						}
					}
				}
			}
		}
	}
	
	@Override
	public void onKill(PlayerEventInfo player, CharacterData target) {
		if (target.getEventInfo() == null) {
			return;
		}
		if (player.getPlayersId() != target.getObjectId()) {
			if (player.getTeamId() == ZOMBIE_TEAM_ID) {
				player.getEventTeam().raiseScore(_zombieKillScore);
				player.getEventTeam().raiseKills(_zombieKillScore);
				getPlayerData(player).raiseScore(_zombieKillScore);
				getPlayerData(player).raiseKills(_zombieKillScore);
				getPlayerData(player).raiseSpree(1);
			} else if (player.getTeamId() == SURVIVOR_TEAM_ID) {
				player.getEventTeam().raiseScore(_survivorKillScore);
				player.getEventTeam().raiseKills(_survivorKillScore);
				getPlayerData(player).raiseScore(_survivorKillScore);
				getPlayerData(player).raiseKills(_survivorKillScore);
				getPlayerData(player).raiseSpree(1);
			}
			giveKillingSpreeReward(getPlayerData(player));
			if (player.isTitleUpdated()) {
				player.setTitle(getTitle(player), true);
				player.broadcastTitleInfo();
			}
			CallbackManager.getInstance().playerKills(getEventType(), player, target.getEventInfo());
			setScoreStats(player, getPlayerData(player).getScore());
			setKillsStats(player, getPlayerData(player).getKills());
		}
	}
	
	@Override
	public void onDie(PlayerEventInfo player, CharacterData killer) {
		if (GabrielEventsLoader.detailedDebug) {
			print("/// Event: onDie - player " + player.getPlayersName() + " (instance " + player.getInstanceId() + "), killer " + killer.getName());
		}
		getPlayerData(player).raiseDeaths(1);
		getPlayerData(player).setSpree(0);
		setDeathsStats(player, getPlayerData(player).getDeaths());
		if (player.isTitleUpdated()) {
			player.setTitle(getTitle(player), true);
			player.broadcastTitleInfo();
		}
		if (player.getTeamId() == ZOMBIE_TEAM_ID) {
			if (_waweRespawn) {
				_waweScheduler.addPlayer(player);
			} else {
				scheduleRevive(player, getInt("resDelay") * 1000);
			}
		} else {
			transformToZombie(player);
		}
	}
	
	@Override
	public boolean onAttack(CharacterData cha, CharacterData target) {
		if (_ammoSystem && cha.isPlayer() && target.isPlayer()) {
			final PlayerEventInfo player = cha.getEventInfo();
			if (player.getTeamId() == SURVIVOR_TEAM_ID) {
				final int oneShotMp = player.getMaxMp() / _ammoAmmount;
				if (player.getCurrentMp() < oneShotMp) {
					player.sendMessage("Not enought MP.");
					return false;
				}
				player.setCurrentMp((int) (player.getCurrentMp() - oneShotMp));
			}
		}
		return true;
	}
	
	@Override
	public boolean canUseItem(PlayerEventInfo player, ItemData item) {
		return (player.getTeamId() != ZOMBIE_TEAM_ID) && ((item.getItemId() != _bowItemId) || (item.getItemId() != _pistolItemId) || !item.isEquipped()) && !item.isWeapon() && super.canUseItem(player, item);
	}
	
	@Override
	public boolean canUseSkill(PlayerEventInfo player, SkillData skill) {
		return (getEventType() != EventType.Zombies) && super.canUseSkill(player, skill);
	}


	
	@Override
	public void onDamageGive(CharacterData cha, CharacterData target, int damage, boolean isDOT) {
		try {
			if (cha.isPlayer() && target.isPlayer()) {
				PlayerEventInfo targetPlayer = target.getEventInfo();
				PlayerEventInfo player = cha.getEventInfo();
				if (player.getTeamId() != targetPlayer.getTeamId()) {
					targetPlayer.abortCasting();
					targetPlayer.doDie(cha);
				}
			}
		} catch (NullPointerException ex) {
		}
	}
	
	@Override
	public boolean canDestroyItem(PlayerEventInfo player, ItemData item) {
		// Fix logic: Use AND instead of OR, prevent destroying event weapons
		return (item.getItemId() != _bowItemId && item.getItemId() != _pistolItemId && item.getItemId() != _spearItemId && item.getItemId() != _orbItemId && item.getItemId() != _arrowItemId) && super.canDestroyItem(player, item);
	}
	
	@Override
	public boolean canSupport(PlayerEventInfo player, CharacterData target) {
		return false;
	}
	
	@Override
	public void onDisconnect(PlayerEventInfo player) {
		if (GabrielEventsLoader.detailedDebug) {
			print("Zombies: Player " + player.getPlayersName() + " disconnected, cleaning up");
		}

		// Clean up transform and weapons when player disconnects
		try {
			// Untransform if zombie
			if (player.getTeamId() == ZOMBIE_TEAM_ID) {
				player.untransform(true);
			}

			// Clean up weapons if survivor
			if (player.getTeamId() == SURVIVOR_TEAM_ID) {
				if (player.isSylphClass()) {
					player.destroyItemByItemId(_pistolItemId, 1);
					player.destroyItemByItemId(_orbItemId, 1);
				} else if (player.isVanguard() || player.isDeathKnight()) {
					player.destroyItemByItemId(_spearItemId, 1);
				} else {
					player.destroyItemByItemId(_bowItemId, 1);
					// Remove all arrows
					while (player.getOwner().getInventory().getItemByItemId(_arrowItemId) != null) {
						player.destroyItemByItemId(_arrowItemId, 1);
					}
				}


			}
		} catch (Exception e) {
			if (GabrielEventsLoader.detailedDebug) {
				print("Zombies: Error cleaning up disconnected player: " + e.getMessage());
			}
		}

		// Call parent method
		super.onDisconnect(player);

		// Schedule new zombie selection if needed
		scheduleSelectZombies(player.getInstanceId(), 0L, false, 0);
	}
	
	@Override
	protected boolean checkIfEventCanContinue(int instanceId, PlayerEventInfo disconnectedPlayer) {
		int survivors = 0;
		int zombies = 0;
		for (EventTeam team : _teams.get(instanceId).values()) {
			if (team.getTeamId() == SURVIVOR_TEAM_ID) {
				for (final PlayerEventInfo pi : team.getPlayers()) {
					survivors++;
					continue;
				}
			}
			if (team.getTeamId() == ZOMBIE_TEAM_ID) {
				for (final PlayerEventInfo pi : team.getPlayers()) {
					zombies++;
					continue;
				}
			}
		}
		if (zombies == 0) {
			if (survivors >= 2) {
				return true;
			}
		} else if ((zombies >= 1) && (survivors >= 1)) {
			return true;
		}
		return false;
	}
	
	@Override
	public void teleportPlayers(int instanceId, SpawnType type, boolean ffa) {
		if (GabrielEventsLoader.detailedDebug) {
			print("AbstractMainEvent: ========================================");
		}
		if (GabrielEventsLoader.detailedDebug) {
			print("AbstractMainEvent: STARTING TO TELEPORT PLAYERS (ffa = " + ffa + ")");
		}
		boolean removeBuffs = getBoolean("removeBuffsOnStart");
		if (GabrielEventsLoader.detailedDebug) {
			print("AbstractMainEvent: removeBuffs = " + removeBuffs);
		}
		int i = 0;
		for (PlayerEventInfo player : getPlayers(instanceId)) {
			EventSpawn spawn = getSpawn(type, -1);
			if (spawn == null) {
				if (GabrielEventsLoader.detailedDebug) {
					print("AbstractMainEvent: ! Missing spawn for team " + ((_teams.get(instanceId).size() == 1) ? -1 : player.getTeamId()) + ", map " + _manager.getMap().getMapName() + ", event " + getEventType().getAltTitle() + " !!");
				}
				GabrielEventsLoader.debug("Missing spawn for team " + ((_teams.get(instanceId).size() == 1) ? -1 : player.getTeamId()) + ", map " + _manager.getMap().getMapName() + ", event " + getEventType().getAltTitle() + " !!", Level.SEVERE);
			}
			if (spawn != null) {
				int radius = spawn.getRadius();
				if (radius == -1) {
					radius = 50;
				}
				Loc loc = new Loc(spawn.getLoc().getX(), spawn.getLoc().getY(), spawn.getLoc().getZ());
				loc.addRadius(radius);
				player.teleport(loc, 0, false, instanceId);
				if (GabrielEventsLoader.detailedDebug) {
					print("AbstractMainEvent: /// player " + player.getPlayersName() + " teleported to " + loc.getX() + ", " + loc.getY() + ", " + loc.getZ() + " (radius = " + radius + "), SPAWN ID " + spawn.getSpawnId() + ", SPAWN TEAM " + spawn.getSpawnTeam());
				}
			}
			if (removeBuffs) {
				player.removeBuffs();
			}
			i++;
		}
		if (GabrielEventsLoader.detailedDebug) {
			print("AbstractMainEvent: " + i + " PLAYERS TELEPORTED");
		}
		clearMapHistory(-1, type);
	}
	
	@Override
	public EventPlayerData createPlayerData(PlayerEventInfo player) {
		return new ZombiesEventPlayerData(player, this);
	}
	
	@Override
	public ZombiesEventPlayerData getPlayerData(PlayerEventInfo player) {
		return (ZombiesEventPlayerData) player.getEventData();
	}
	
	@Override
	public synchronized void clearEvent(int instanceId) {
		if (GabrielEventsLoader.detailedDebug) {
			print("Event: called CLEAREVENT for instance " + instanceId);
		}
		try {
			if (_matches != null) {
				for (TvTEventInstance match : _matches.values()) {
					if ((instanceId == 0) || (instanceId == match.getInstance().getId())) {
						match.abort();
						preparePlayers(match.getInstance().getId(), false);
						untransformAll(match.getInstance().getId());
					}
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		for (PlayerEventInfo player : getPlayers(instanceId)) {
			if (player.isOnline()) {
				if (player.isParalyzed()) {
					player.setIsParalyzed(false);
				}
				if (player.isImmobilized()) {
					player.unroot();
				}
				if (!player.isGM()) {
					player.setIsInvul(false);
				}
				player.removeRadarAllMarkers();
				player.setInstanceId(0);
				if (_removeBuffsOnEnd) {
					player.removeBuffs();
				}
				player.restoreData();
				// player.destroyItemByItemId(_bowItemId, 1);
				player.teleport(player.getOrigLoc(), 0, true, 0);
				player.sendMessage(LanguageEngine.getMsg("event_teleportBack"));
				if (player.getParty() != null) {
					PartyData party = player.getParty();
					party.removePartyMember(player);
				}
				player.broadcastUserInfo();

				// Auto-arrange inventory after event cleanup
				CallBack.getInstance().getOut().scheduleGeneral(() -> {
					if (player.isOnline() && player.getOwner() != null)
					{
						autoArrangeInventory(player);
					//player.sendMessage("Hành trang của bạn đã được tự động sắp xếp lại sau event!");
					}
				}, 2000); // 2 second delay to ensure all cleanup is complete
			}
		}
		clearPlayers(true, instanceId);
	}
	
	@Override
	protected void respawnPlayer(PlayerEventInfo pi, int instance) {
		if (GabrielEventsLoader.detailedDebug) {
			print("/// Event: respawning player " + pi.getPlayersName() + ", instance " + instance);
		}
		EventSpawn spawn = null;
		if (pi.getTeamId() == SURVIVOR_TEAM_ID) {
			spawn = getSpawn(SpawnType.Regular, -1);
		} else if (pi.getTeamId() == ZOMBIE_TEAM_ID) {
			spawn = getSpawn(SpawnType.Zombie, -1);
		}
		if (spawn != null) {
			Loc loc = new Loc(spawn.getLoc().getX(), spawn.getLoc().getY(), spawn.getLoc().getZ());
			loc.addRadius(spawn.getRadius());
			pi.teleport(loc, 0, true, instance);
			pi.sendMessage(LanguageEngine.getMsg("event_respawned"));
		} else {
			debug("Error on respawnPlayer - no spawn type REGULAR/ZOMBIE, team " + pi.getTeamId() + " has been found. Event aborted.");
		}
	}
	
	@Override
	public String getHtmlDescription() {
		if (_htmlDescription == null) {
			EventDescription desc = EventDescriptionSystem.getInstance().getDescription(getEventType());
			if (desc != null) {
				_htmlDescription = desc.getDescription(getConfigs());
			} else {
				_htmlDescription = "No information about this event yet.";
			}
		}
		return _htmlDescription;
	}
	
	@Override
	public boolean allowsRejoinOnDisconnect() {
		return false;
	}
	
	@Override
	protected TvTEventData createEventData(int instanceId) {
		return new ZombiesEventData(instanceId);
	}
	
	@Override
	protected ZombiesEventInstance createEventInstance(InstanceData instance) {
		return new ZombiesEventInstance(instance);
	}
	
	@Override
	protected ZombiesEventData getEventData(int instance) {
		return (ZombiesEventData) _matches.get(instance)._data;
	}
	
	public class ZombiesEventPlayerData extends PvPEventPlayerData {
		public ZombiesEventPlayerData(PlayerEventInfo owner, EventGame event) {
			super(owner, event, new GlobalStatsModel(getEventType()));
		}
	}
	
	protected class ZombiesEventInstance extends HGEventInstance {
		protected ZombiesEventInstance(InstanceData instance) {
			super(instance);
		}
		
		@Override
		public void run() {
			try {
				if (GabrielEventsLoader.detailedDebug) {
					print("Event: running task of state " + _state.toString() + "...");
				}
				switch (_state) {
					case START: {
						// Skip checkPlayers for Zombie event since all players start as survivors
						// and zombies are selected later, which would cause checkIfEventCanContinue to fail
						teleportPlayers(_instance.getId(), SpawnType.Regular, false);
						setupTitles(_instance.getId());
						removeStaticDoors(_instance.getId());
						enableMarkers(_instance.getId(), true);
						preparePlayers(_instance.getId(), true);
						scheduleSelectZombies(_instance.getId(), 10000L, true, 0);
						forceSitAll(_instance.getId());
						setNextState(EventState.FIGHT);
						scheduleNextTask(10000);
						break;
					}
					case FIGHT: {
						forceStandAll(_instance.getId());
						setNextState(EventState.END);
						_clock.startClock(_manager.getRunTime());
						break;
					}
					case END: {
						_clock.setTime(0, true);
						setNextState(EventState.INACTIVE);
						untransformAll(_instance.getId());
						setAllZombies(_instance.getId());
						if (!instanceEnded() && _canBeAborted) {
							if (_canRewardIfAborted) {
								int minScore = getInt("killsForReward");
								rewardAllPlayersFromTeam(_instance.getId(), minScore, 0, 2);
							}
							clearEvent(_instance.getId());
							break;
						}
						break;
					}
				}
				if (GabrielEventsLoader.detailedDebug) {
					print("Event: ... finished running task. next state " + _state.toString());
				}
			} catch (Throwable e) {
				e.printStackTrace();
				_manager.endDueToError(LanguageEngine.getMsg("event_error"));
			}
		}
	}
	
	private class InactivityTimer implements Runnable {
		int _instanceId;
		ScheduledFuture<?> _future;
		
		InactivityTimer(int instanceId) {
			_future = null;
			_instanceId = instanceId;
		}
		
		@Override
		public void run() {
			zombiesInactive(_instanceId);
		}
		
		public void schedule() {
			if (_future != null) {
				abort();
			}
			_future = CallBack.getInstance().getOut().scheduleGeneral(this, _zombieInactivityTime * 1000);
		}
		
		private void abort() {
			if (_future != null) {
				_future.cancel(false);
				_future = null;
			}
		}
	}
	
	protected class ZombiesEventData extends HGEventData {
		protected InactivityTimer _inactivityTimer;
		
		public ZombiesEventData(int instance) {
			super(instance);
			_inactivityTimer = null;
		}
		
		private synchronized void startTimer() {
			if (_inactivityTimer == null) {
				_inactivityTimer = new InactivityTimer(_instanceId);
			}
			_inactivityTimer.schedule();
		}
		
		public void setKillMade() {
			if (_zombieInactivityTime > 0) {
				startTimer();
			}
		}
	}
}
