package gabriel.eventEngine.events.engine.main.events;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import gabriel.eventEngine.events.GabrielEventsLoader;
import gabriel.eventEngine.events.engine.EventConfig;
import gabriel.eventEngine.l2j.CallBack;
import gabriel.eventEngine.events.engine.EventManager;
import gabriel.eventEngine.events.engine.base.ConfigModel;
import gabriel.eventEngine.events.engine.base.EventMap;
import gabriel.eventEngine.events.engine.base.EventPlayerData;
import gabriel.eventEngine.events.engine.base.EventSpawn;
import gabriel.eventEngine.events.engine.base.EventType;
import gabriel.eventEngine.events.engine.base.Loc;
import gabriel.eventEngine.events.engine.base.PvPEventPlayerData;
import gabriel.eventEngine.events.engine.base.RewardPosition;
import gabriel.eventEngine.events.engine.base.SpawnType;
import gabriel.eventEngine.events.engine.base.description.EventDescription;
import gabriel.eventEngine.events.engine.base.description.EventDescriptionSystem;
import gabriel.eventEngine.events.engine.lang.LanguageEngine;
import gabriel.eventEngine.events.engine.main.MainEventManager;
import gabriel.eventEngine.events.engine.main.base.MainEventInstanceType;
import gabriel.eventEngine.events.engine.stats.GlobalStatsModel;
import gabriel.eventEngine.interf.PlayerEventInfo;
import gabriel.eventEngine.interf.callback.CallbackManager;
import gabriel.eventEngine.interf.delegate.CharacterData;
import gabriel.eventEngine.interf.delegate.InstanceData;
import gabriel.eventEngine.interf.delegate.PartyData;
import gabriel.eventEngine.util.ScreenMessageThrottle;

public class Deathmatch extends AbstractMainEvent {
	protected Map<Integer, DMEventInstance> _matches;
	protected boolean _waweRespawn;
	protected boolean _antifeed;
	
	public Deathmatch(EventType type, MainEventManager manager) {
		super(type, manager);
		_matches = new ConcurrentHashMap<Integer, DMEventInstance>();
		setRewardTypes(new RewardPosition[] {
			RewardPosition.Winner,
			RewardPosition.Looser,
			RewardPosition.Tie,
			RewardPosition.Numbered,
			RewardPosition.Range,
			RewardPosition.FirstBlood,
			RewardPosition.FirstRegistered,
			RewardPosition.OnKill,
			RewardPosition.KillingSpree
		});
	}
	
	@Override
	public void loadConfigs() {
		super.loadConfigs();
		addConfig(new ConfigModel("killsForReward", "0", "The minimum kills count required to get a reward (includes all possible rewards)."));
		addConfig(new ConfigModel("resDelay", "15", "The delay after which the player is resurrected. In seconds."));
		addConfig(new ConfigModel("waweRespawn", "true", "Enables the wawe-style respawn system.", ConfigModel.InputType.Boolean));
		addConfig(new ConfigModel("firstBloodMessage", "true", "You can turn off/on the first blood announce in the event (first kill made in the event). This is also rewardable - check out reward type FirstBlood.", ConfigModel.InputType.Boolean));
		addConfig(new ConfigModel("antifeedProtection", "true", "Enables the special anti-feed protection. This protection changes player's name, title, race, clan/ally crest, class and basically all of his apperance, sometimes also gender. Note: GM/Admin players are excluded from antifeed protection to prevent stat calculation issues.", ConfigModel.InputType.Boolean));
	}
	
	@Override
	public void initEvent() {
		super.initEvent();
		_waweRespawn = getBoolean("waweRespawn");
		_antifeed = getBoolean("antifeedProtection");
		if (_waweRespawn) {
			initWaweRespawns(getInt("resDelay"));
		}
		_runningInstances = 0;
	}
	
	@Override
	protected int initInstanceTeams(MainEventInstanceType type) {
		// Death Match is always Free-For-All - everyone fights everyone
		createTeams(1, type.getInstance().getId());
		return 1;
	}
	
	@Override
	public void runEvent() {
		if (GabrielEventsLoader.detailedDebug) {
			print("Event: started runEvent()");
		}
		if (!dividePlayers()) {
			clearEvent();
			return;
		}
		_matches.clear();
		for (InstanceData instance : _instances) {
			if (GabrielEventsLoader.detailedDebug) {
				print("Event: creating eventinstance for instance " + instance.getId());
			}
			DMEventInstance match = createEventInstance(instance);
			_matches.put(instance.getId(), match);
			_runningInstances++;
			match.scheduleNextTask(0);
			if (GabrielEventsLoader.detailedDebug) {
				print("Event: event instance started");
			}
		}
		if (GabrielEventsLoader.detailedDebug) {
			print("Event: finished runEvent()");
		}
	}
	
	@Override
	public void onEventEnd() {
		if (GabrielEventsLoader.detailedDebug) {
			print("Event: onEventEnd()");
		}
		int minKills = getInt("killsForReward");
		rewardAllPlayers(-1, minKills, minKills);
		if (_antifeed) {
			for (PlayerEventInfo player : getPlayers(0)) {
				try {
					// Only stop antifeed protection for players who had it enabled (not GM/Admin)
					if (!player.isGM() && player.hasAntifeedProtection()) {
						player.stopAntifeedProtection(false);
					}
				} catch (Exception e) {
					// Log error but continue
					if (GabrielEventsLoader.detailedDebug) {
						print("Error stopping antifeed protection for player " + player.getPlayersName() + ": " + e.getMessage());
					}
				}
			}
		}
	}
	
	@Override
	protected synchronized boolean instanceEnded() {
		_runningInstances--;
		if (GabrielEventsLoader.detailedDebug) {
			print("Event: notifying instance ended: runningInstances = " + _runningInstances);
		}
		if (_runningInstances == 0) {
			_manager.end();
			return true;
		}
		return false;
	}
	
	@Override
	protected synchronized void endInstance(int instance, boolean canBeAborted, boolean canRewardIfAborted, boolean forceNotReward) {
		if (GabrielEventsLoader.detailedDebug) {
			print("Event: endInstance() " + instance + ", canBeAborted " + canBeAborted + ", canReward.. " + canRewardIfAborted + " forceNotReward " + forceNotReward);
		}
		try {
			if (forceNotReward) {
				_matches.get(instance).forceNotRewardThisInstance();
			}
			_matches.get(instance).setNextState(EventState.END);
			if (canBeAborted) {
				_matches.get(instance).setCanBeAborted();
			}
			if (canRewardIfAborted) {
				_matches.get(instance).setCanRewardIfAborted();
			}
			_matches.get(instance).scheduleNextTask(0);
		} catch (Exception ex) {
		}
	}
	
	@Override
	protected String getScorebar(int instance) {
		StringBuilder tb = new StringBuilder();
		int top = 0;
		for (PlayerEventInfo player : getPlayers(instance)) {
			if (getPlayerData(player).getKills() > top) {
				top = getPlayerData(player).getKills();
			}
		}
		tb.append(LanguageEngine.getMsg("dm_topKills", top) + " ");
		tb.append("   " + LanguageEngine.getMsg("event_scorebar_time", _matches.get(instance).getClock().getTime()));
		return tb.toString();
	}
	
	@Override
	protected String getTitle(PlayerEventInfo pi) {
		if (_hideTitles) {
			return "";
		}
		if (pi.isAfk()) {
			return "AFK";
		}
		return LanguageEngine.getMsg("event_title_pvppk", getPlayerData(pi).getScore(), getPlayerData(pi).getDeaths());
	}
	
	private int _scoreUpdateTick = 0;

	@Override
	protected void clockTick() {
		// Update score bar mỗi giây để hiển thị liên tục
		_scoreUpdateTick++;
		if (_scoreUpdateTick >= 1) { // Update mỗi giây
			_scoreUpdateTick = 0;
			// Force score bar update - bỏ throttling để hiển thị liên tục
			for (DMEventInstance match : _matches.values()) {
				if (match != null) {
					String scoreText = getScorebar(match.getInstance().getId());
					for (PlayerEventInfo player : getPlayers(match.getInstance().getId())) {
						// Bỏ throttling check để hiển thị liên tục
						player.sendEventScoreBar(scoreText);
					}
				}
			}
		}
	}

	@Override
	public void onKill(PlayerEventInfo player, CharacterData target) {
		if (target.getEventInfo() == null) {
			return;
		}
		tryFirstBlood(player);
		giveOnKillReward(player);
		getPlayerData(player).raiseScore(1);
		getPlayerData(player).raiseKills(1);
		getPlayerData(player).raiseSpree(1);
		giveKillingSpreeReward(getPlayerData(player));
		if (player.isTitleUpdated()) {
			player.setTitle(getTitle(player), true);
			player.broadcastTitleInfo();
		}
		CallbackManager.getInstance().playerKills(getEventType(), player, target.getEventInfo());
		setScoreStats(player, getPlayerData(player).getScore());
		setKillsStats(player, getPlayerData(player).getKills());
	}
	
	@Override
	public void onDie(PlayerEventInfo player, CharacterData killer) {
		if (GabrielEventsLoader.detailedDebug) {
			print("/// Event: onDie - player " + player.getPlayersName() + " (instance " + player.getInstanceId() + "), killer " + killer.getName());
		}
		getPlayerData(player).raiseDeaths(1);
		getPlayerData(player).setSpree(0);
		setDeathsStats(player, getPlayerData(player).getDeaths());
		if (player.isTitleUpdated()) {
			player.setTitle(getTitle(player), true);
			player.broadcastTitleInfo();
		}
		if (_waweRespawn) {
			_waweScheduler.addPlayer(player);
		} else {
			scheduleRevive(player, getInt("resDelay") * 1000);
		}
	}
	
	@Override
	public boolean canSupport(PlayerEventInfo player, CharacterData target) {
		return player.getPlayersId() == target.getObjectId() || (player.hasSummon() && target.isSummon() && player.getSummon() == target.getOwner());
	}
	
	@Override
	public boolean canAttack(PlayerEventInfo player, CharacterData target) {
		return target.getEventInfo() == null || target.getEventInfo().getEvent() == player.getEvent();
	}
	
	@Override
	public boolean onSay(PlayerEventInfo player, String text, int channel) {
		if (text.equals(".scheme")) {
			EventManager.getInstance().getHtmlManager().showSelectSchemeForEventWindow(player, "none", getEventType().getAltTitle());
			return false;
		}
		if (_antifeed) {
			player.sendMessage(LanguageEngine.getMsg("dm_cantChat"));
			return false;
		}
		return true;
	}
	
	@Override
	public boolean canInviteToParty(PlayerEventInfo player, PlayerEventInfo target) {
		return false;
	}
	
	@Override
	protected boolean checkIfEventCanContinue(int instanceId, PlayerEventInfo disconnectedPlayer) {
		int alive = 0;
		for (PlayerEventInfo pi : getPlayers(instanceId)) {
			if (pi != null && pi.isOnline()) {
				alive++;
			}
		}
		return alive >= 2;
	}
	
	@Override
	public EventPlayerData createPlayerData(PlayerEventInfo player) {
		return new PvPEventPlayerData(player, this, new GlobalStatsModel(getEventType()));
	}
	
	@Override
	public PvPEventPlayerData getPlayerData(PlayerEventInfo player) {
		return (PvPEventPlayerData) player.getEventData();
	}
	
	@Override
	public synchronized void clearEvent(int instanceId) {
		if (GabrielEventsLoader.detailedDebug) {
			print("Event: called CLEAREVENT for instance " + instanceId);
		}
		try {
			if (_matches != null) {
				for (DMEventInstance match : _matches.values()) {
					if (instanceId == 0 || instanceId == match.getInstance().getId()) {
						match.abort();
					}
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		for (PlayerEventInfo player : getPlayers(instanceId)) {
			if (player.isOnline()) {
				if (player.hasAntifeedProtection() && !player.isGM()) {
					player.stopAntifeedProtection(false);
				}
				if (player.isParalyzed()) {
					player.setIsParalyzed(false);
				}
				if (player.isImmobilized()) {
					player.unroot();
				}
				if (!player.isGM()) {
					player.setIsInvul(false);
				}
				player.removeRadarAllMarkers();
				player.setInstanceId(0);
				if (_removeBuffsOnEnd) {
					player.removeBuffs();
				}
				player.restoreData();
				player.teleport(player.getOrigLoc(), 0, true, 0);
				player.sendMessage(LanguageEngine.getMsg("event_teleportBack"));
				if (player.getParty() != null) {
					PartyData party = player.getParty();
					party.removePartyMember(player);
				}
				player.broadcastUserInfo();

				// Auto-arrange inventory after event cleanup
				CallBack.getInstance().getOut().scheduleGeneral(() -> {
					if (player.isOnline() && player.getOwner() != null)
					{
						autoArrangeInventory(player);
						//player.sendMessage("Hành trang của bạn đã được tự động sắp xếp lại sau event!");
					}
				}, 2000); // 2 second delay to ensure all cleanup is complete
			}
		}
		clearPlayers(true, instanceId);
	}
	
	@Override
	public synchronized void clearEvent() {
		if (GabrielEventsLoader.detailedDebug) {
			print("Event: called global clearEvent()");
		}
		clearEvent(0);
	}
	
	@Override
	protected void respawnPlayer(PlayerEventInfo pi, int instance) {
		if (GabrielEventsLoader.detailedDebug) {
			print("/// Event: respawning player " + pi.getPlayersName() + ", instance " + instance);
		}
		EventSpawn spawn = getSpawn(SpawnType.Regular, -1);
		if (spawn != null) {
			Loc loc = new Loc(spawn.getLoc().getX(), spawn.getLoc().getY(), spawn.getLoc().getZ());
			loc.addRadius(spawn.getRadius());
			pi.teleport(loc, 0, true, instance);
			pi.sendMessage(LanguageEngine.getMsg("event_respawned"));
		} else {
			debug("Error on respawnPlayer - no spawn type REGULAR, team -1 (FFA) has been found. Event aborted.");
		}
	}
	
	@Override
	public String getEstimatedTimeLeft() {
		if (_matches == null) {
			return "Starting";
		}
		for (DMEventInstance match : _matches.values()) {
			if (match.isActive()) {
				return match.getClock().getTime();
			}
		}
		return "N/A";
	}
	
	@Override
	public int getTeamsCount() {
		// Death Match is always Free-For-All
		return 1;
	}
	
	@Override
	public String getMissingSpawns(EventMap map) {
		// Death Match uses random spawns for Free-For-All gameplay
		if (!map.checkForSpawns(SpawnType.Regular, -1, 1)) {
			return addMissingSpawn(SpawnType.Regular, 0, 1);
		}
		return "";
	}
	
	@Override
	protected String addExtraEventInfoCb(int instance) {
		int top = 0;
		for (PlayerEventInfo player : getPlayers(instance)) {
			if (getPlayerData(player).getKills() > top) {
				top = getPlayerData(player).getKills();
			}
		}
		String status = "<font color=ac9887>Top kills count: </font><font color=7f7f7f>" + top + "</font>";
		return "<table width=510 bgcolor=3E3E3E><tr><td width=510 align=center>" + status + "</td></tr></table>";
	}
	
	@Override
	public String getHtmlDescription() {
		if (_htmlDescription == null) {
			EventDescription desc = EventDescriptionSystem.getInstance().getDescription(getEventType());
			if (desc != null) {
				_htmlDescription = desc.getDescription(getConfigs());
			} else {
				String divideMethod = getString("divideToTeamsMethod");
				if ("FactionBased".equals(divideMethod)) {
					_htmlDescription = "This is a faction-based death match event. Fire and Water factions fight against each other. Gain score by killing enemy faction members";
				} else {
					_htmlDescription = "This is a free-for-all event, don't expect any help from teammates. Gain score by killing your opponents";
				}
				_htmlDescription = _htmlDescription + " and if you die, you will be resurrected within " + getInt("resDelay") + " seconds. ";
				if (getBoolean("waweRespawn")) {
					_htmlDescription += "Also, wawe-spawn system ensures that all dead players are spawned in the same moment (but in different spots). ";
				}
				if (getBoolean("antifeedProtection")) {
					_htmlDescription += "This event has a protection, which completely changes the appearance of all players and temporary removes their title and clan/ally crests. ";
				}
				if (getInt("killsForReward") > 0) {
					_htmlDescription = _htmlDescription + "In the end, you need at least " + getInt("killsForReward") + " kills to receive a reward.";
				}
			}
		}
		return _htmlDescription;
	}
	
	@Override
	protected AbstractEventInstance getMatch(int instanceId) {
		return _matches.get(instanceId);
	}
	
	@Override
	protected DMData createEventData(int instance) {
		return new DMData(instance);
	}
	
	@Override
	protected DMEventInstance createEventInstance(InstanceData instance) {
		return new DMEventInstance(instance);
	}
	
	@Override
	protected DMData getEventData(int instance) {
		return _matches.get(instance)._data;
	}
	
	protected enum EventState {
		START,
		FIGHT,
		END,
		TELEPORT,
		INACTIVE;
	}
	
	protected class DMEventInstance extends AbstractEventInstance {
		protected EventState _nextState;
		protected DMData _data;
		
		public DMEventInstance(InstanceData instance) {
			super(instance);
			_nextState = EventState.START;
			_data = createEventData(_instance.getId());
		}
		
		protected void setNextState(EventState state) {
			_nextState = state;
		}
		
		@Override
		public boolean isActive() {
			return _nextState != EventState.INACTIVE;
		}
		
		@Override
		public void run() {
			try {
				if (GabrielEventsLoader.detailedDebug) {
					print("Event: running task of state " + _nextState.toString() + "...");
				}
				switch (_nextState) {
					case START: {
						if (checkPlayers(_instance.getId())) {
							if (_antifeed) {
								for (PlayerEventInfo player : getPlayers(_instance.getId())) {
									try {
										// Skip antifeed protection for GM/Admin to prevent stat issues
										if (!player.isGM()) {
											// Only apply antifeed protection to normal players
											player.startAntifeedProtection(false);
										}
									} catch (Exception e) {
										// Log error but continue with event
										if (GabrielEventsLoader.detailedDebug) {
											print("Error applying antifeed protection to player " + player.getPlayersName() + ": " + e.getMessage());
										}
									}
								}
							}
							teleportPlayers(_instance.getId(), SpawnType.Regular, true);
							setupTitles(_instance.getId());
							enableMarkers(_instance.getId(), true);
							forceSitAll(_instance.getId());
							setNextState(EventState.FIGHT);
							scheduleNextTask(10000);
							break;
						}
						break;
					}
					case FIGHT: {
						forceStandAll(_instance.getId());
						setNextState(EventState.END);
						_clock.startClock(_manager.getRunTime());
						break;
					}
					case END: {
						_clock.setTime(0, true);
						setNextState(EventState.INACTIVE);
						if (!instanceEnded() && _canBeAborted) {
							if (_canRewardIfAborted) {
								rewardAllPlayers(_instance.getId(), 0, getInt("killsForReward"));
							}
							clearEvent(_instance.getId());
							break;
						}
						break;
					}
				}
				if (GabrielEventsLoader.detailedDebug) {
					print("Event: ... finished running task. next state " + _nextState.toString());
				}
			} catch (Throwable e) {
				e.printStackTrace();
				_manager.endDueToError(LanguageEngine.getMsg("event_error"));
			}
		}
	}
	
	protected class DMData extends AbstractEventData {
		protected DMData(int instance) {
			super(instance);
		}
	}
}
