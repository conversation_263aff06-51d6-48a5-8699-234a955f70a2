package gabriel.eventEngine.events.engine.main.events;

import java.util.Map;
import java.util.Map.Entry;
import java.util.concurrent.ConcurrentHashMap;
import java.util.logging.Level;

import gabriel.eventEngine.events.GabrielEventsLoader;
import gabriel.eventEngine.events.engine.base.ConfigModel;
import gabriel.eventEngine.events.engine.base.EventType;
import gabriel.eventEngine.events.engine.base.RewardPosition;
import gabriel.eventEngine.events.engine.base.SpawnType;
import gabriel.eventEngine.events.engine.base.description.EventDescription;
import gabriel.eventEngine.events.engine.base.description.EventDescriptionSystem;
import gabriel.eventEngine.events.engine.lang.LanguageEngine;
import gabriel.eventEngine.events.engine.main.MainEventManager;
import gabriel.eventEngine.interf.PlayerEventInfo;
import gabriel.eventEngine.interf.callback.CallbackManager;
import gabriel.eventEngine.interf.delegate.CharacterData;
import gabriel.eventEngine.interf.delegate.InstanceData;
import gabriel.eventEngine.interf.delegate.ItemData;
import gabriel.eventEngine.interf.delegate.PartyData;
import gabriel.eventEngine.interf.delegate.SkillData;
import gabriel.eventEngine.l2j.CallBack;

public class HuntingGrounds extends TeamVsTeam {
	protected int _tick;
	protected int _bowItemId;
	protected int _arrowItemId;
	protected int _pistolItemId;
	protected int _orbItemId;
	protected int _spearItemId;
	protected boolean _ammoSystem;
	protected int _ammoAmmount;
	protected int _ammoRegPerTick;
	protected int _tickLength;
	private final Map<Integer, Integer> _skillsForAll;
	
	public HuntingGrounds(EventType type, MainEventManager manager) {
		super(type, manager);
		_skillsForAll = new ConcurrentHashMap<Integer, Integer>();
		setRewardTypes(new RewardPosition[] {
			RewardPosition.Winner,
			RewardPosition.Looser,
			RewardPosition.Tie,
			RewardPosition.FirstBlood,
			RewardPosition.FirstRegistered,
			RewardPosition.OnKill,
			RewardPosition.KillingSpree
		});
	}
	
	@Override
	public void loadConfigs() {
		super.loadConfigs();
		addConfig(new ConfigModel("skillsForAllPlayers", "90001-1", "IDs of skills which will be given to players on the event. The purpose of this is to make all players equally strong. Format: <font color=LEVEL>SKILLID-LEVEL</font> (eg. '35000-1').", ConfigModel.InputType.MultiAdd));
		addConfig(new ConfigModel("bowWeaponId", "271", "The ID of the bow item which will be given to all players and will be the only weapon most players will use during the event. This weapon kills players with just one hit."));
		addConfig(new ConfigModel("arrowItemId", "17", "The ID of the arrows which will be given to the player in the event."));
		addConfig(new ConfigModel("pistolItemId", "95410", "The ID of the pistol item which will be given to all players and will be the only weapon most players will use during the event. This weapon kills players with just one hit."));
		addConfig(new ConfigModel("orbItemId", "94891", "The ID of the orb which will be given to the player in the event."));
		addConfig(new ConfigModel("spearItemId", "97100", "The ID of the spear item which will be given to vanguard and will be the only weapon most players will use during the event. This weapon kills players with just one hit."));
		addConfig(new ConfigModel("enableAmmoSystem", "true", "Enable/disable the ammo system based on player's mana. Player's max MP is defaultly modified by a custom passive skill and everytime a player shots and arrow, his MP decreases by a value which is calculated from the ammount of ammo. There is also a MP regeneration system - see the configs below.", ConfigModel.InputType.Boolean));
		addConfig(new ConfigModel("ammoAmmount", "10", "Works if ammo system is enabled. Specifies the max ammount of ammo every player can have."));
		addConfig(new ConfigModel("ammoRestoredPerTick", "1", "Works if ammo system is enabled. Defines the ammount of ammo given to every player each <font color=LEVEL>'ammoRegTickInterval'</font> (configurable) seconds."));
		addConfig(new ConfigModel("ammoRegTickInterval", "10", "Works if ammo system is enabled. Defines the interval of restoring player's ammo. The value is in seconds (eg. value 10 will give ammo every 10 seconds to every player - the ammount of restored ammo is configurable (config <font color=LEVEL>ammoRestoredPerTick</font>)."));
	}
	
	@Override
	public void initEvent() {
		super.initEvent();
		_bowItemId = getInt("bowWeaponId");
		_arrowItemId = getInt("arrowItemId");
		_pistolItemId = getInt("pistolItemId");
		_orbItemId = getInt("orbItemId");
		_spearItemId = getInt("spearItemId");
		_ammoSystem = getBoolean("enableAmmoSystem");
		_ammoAmmount = getInt("ammoAmmount");
		_ammoRegPerTick = getInt("ammoRestoredPerTick");
		_tickLength = getInt("ammoRegTickInterval");
		if (!getString("skillsForAllPlayers").equals("")) {
			String[] splits = getString("skillsForAllPlayers").split(",");
			_skillsForAll.clear();
			try {
				for (String split : splits) {
					String id = split.split("-")[0];
					String level = split.split("-")[1];
					_skillsForAll.put(Integer.parseInt(id), Integer.parseInt(level));
				}
			} catch (Exception e) {
				GabrielEventsLoader.debug("Error while loading config 'skillsForAllPlayers' for event " + getEventName() + " - " + e.toString(), Level.SEVERE);
			}
		}
		_tick = 0;
	}
	
	protected void preparePlayers(int instanceId, boolean start) {
	}
	
	protected void handleSkills(int instanceId, boolean add) {
		if (_skillsForAll != null) {
			SkillData skill = null;
			for (PlayerEventInfo player : getPlayers(instanceId)) {
				if (add) {
					for (Entry<Integer, Integer> e : _skillsForAll.entrySet()) {
						skill = new SkillData(e.getKey(), e.getValue());
						if (skill.exists()) {
							player.addSkill(skill, false);
						}
					}
					player.sendSkillList();
				} else {
					for (Entry<Integer, Integer> e : _skillsForAll.entrySet()) {
						skill = new SkillData(e.getKey(), e.getValue());
						if (skill.exists()) {
							player.removeSkill(skill.getId());
						}
					}
				}
			}
		}
	}
	
	protected void handleWeapons(int instanceId, boolean equip) {
		for (PlayerEventInfo player : getPlayers(instanceId)) {
			if (equip) {
				ItemData wpn = player.getPaperdollItem(CallBack.getInstance().getValues().PAPERDOLL_RHAND());
				if (wpn != null) {
					player.unEquipItemInBodySlotAndRecord(CallBack.getInstance().getValues().SLOT_R_HAND());
				}
				wpn = player.getPaperdollItem(CallBack.getInstance().getValues().PAPERDOLL_LHAND());
				if (wpn != null) {
					player.unEquipItemInBodySlotAndRecord(CallBack.getInstance().getValues().SLOT_L_HAND());
				}

				if (player.isSylphClass()) {
					ItemData flagItem = player.addItem(_pistolItemId, 1, false);
					player.equipItem(flagItem);
					player.addItem(_orbItemId, 1, false);
				} else if (player.isVanguard()) {
					ItemData flagItem = player.addItem(_spearItemId, 1, false);
					player.equipItem(flagItem);
				} else {
					ItemData flagItem = player.addItem(_bowItemId, 1, false);
					player.equipItem(flagItem);
					player.addItem(_arrowItemId, 400, false);
				}

			} else {
				// Safely remove event weapons
				try {
					ItemData wpn = player.getPaperdollItem(CallBack.getInstance().getValues().PAPERDOLL_RHAND());
					if (wpn != null && wpn.exists()) {
						ItemData[] unequiped = player.unEquipItemInBodySlotAndRecord(wpn.getBodyPart());
						player.inventoryUpdate(unequiped);
					}

					// Remove event items safely
					if (player.isSylphClass()) {
						player.destroyItemByItemId(_pistolItemId, 1);
						player.destroyItemByItemId(_orbItemId, 1);
					} else if (player.isVanguard()) {
						player.destroyItemByItemId(_spearItemId, 1);
					} else {
						player.destroyItemByItemId(_bowItemId, 1);
						// Remove all arrows, not just some
						while (player.getOwner().getInventory().getItemByItemId(_arrowItemId) != null) {
							player.destroyItemByItemId(_arrowItemId, 1);
						}
					}
				} catch (Exception e) {
					if (GabrielEventsLoader.detailedDebug) {
						print("HuntingGrounds: Error removing weapons from player " + player.getPlayersName() + ": " + e.getMessage());
					}
				}
			}
		}
	}
	
	@Override
	protected void clockTick() {
		_tick++;
		if (_tick % _tickLength != 0) {
			return;
		}
		if (_ammoSystem) {
			int oneAmmoMp = 0;
			for (TvTEventInstance match : _matches.values()) {
				for (PlayerEventInfo player : getPlayers(match.getInstance().getId())) {
					try {
						oneAmmoMp = player.getMaxMp() / _ammoAmmount;
						int mpToRegenerate = _ammoRegPerTick * oneAmmoMp;
						int currentMp = (int) player.getCurrentMp();
						if (currentMp >= player.getMaxMp()) {
							continue;
						}
						int toAdd = mpToRegenerate;
						if (currentMp + mpToRegenerate > player.getMaxMp()) {
							toAdd = player.getMaxMp() - currentMp;
						}
						player.setCurrentMp(currentMp + toAdd);
					} catch (NullPointerException e) {
					}
				}
			}
		}
	}
	
	@Override
	public void onKill(PlayerEventInfo player, CharacterData target) {
		if (target.getEventInfo() == null) {
			return;
		}
		if (player.getTeamId() != target.getEventInfo().getTeamId()) {
			tryFirstBlood(player);
			giveOnKillReward(player);
			player.getEventTeam().raiseScore(1);
			player.getEventTeam().raiseKills(1);
			getPlayerData(player).raiseScore(1);
			getPlayerData(player).raiseKills(1);
			getPlayerData(player).raiseSpree(1);
			giveKillingSpreeReward(getPlayerData(player));
			if (player.isTitleUpdated()) {
				player.setTitle(getTitle(player), true);
				player.broadcastTitleInfo();
			}
			CallbackManager.getInstance().playerKills(getEventType(), player, target.getEventInfo());
			setScoreStats(player, getPlayerData(player).getScore());
			setKillsStats(player, getPlayerData(player).getKills());
		}
	}
	
	@Override
	public void onDie(PlayerEventInfo player, CharacterData killer) {
		if (GabrielEventsLoader.detailedDebug) {
			print("/// Event: onDie - player " + player.getPlayersName() + " (instance " + player.getInstanceId() + "), killer " + killer.getName());
		}
		getPlayerData(player).raiseDeaths(1);
		getPlayerData(player).setSpree(0);
		setDeathsStats(player, getPlayerData(player).getDeaths());
		if (player.isTitleUpdated()) {
			player.setTitle(getTitle(player), true);
			player.broadcastTitleInfo();
		}
		if (_waweRespawn) {
			_waweScheduler.addPlayer(player);
		} else {
			scheduleRevive(player, getInt("resDelay") * 1000);
		}
	}
	
	@Override
	public boolean onAttack(CharacterData cha, CharacterData target) {
		if (_ammoSystem && cha.isPlayer() && target.isPlayer()) {
			final PlayerEventInfo player = cha.getEventInfo();
			final int oneShotMp = player.getMaxMp() / _ammoAmmount;
			if (player.getCurrentMp() < oneShotMp) {
				player.sendMessage("Not enought MP.");
				return false;
			}
			player.setCurrentMp((int) (player.getCurrentMp() - oneShotMp));
		}
		return true;
	}
	
	@Override
	public boolean canUseItem(PlayerEventInfo player, ItemData item) {
		// Fix logic: Allow using event weapons when equipped, prevent using other weapons
		if (item.getItemId() == _bowItemId || item.getItemId() == _pistolItemId || item.getItemId() == _spearItemId) {
			return item.isEquipped(); // Allow using event weapons only when equipped
		}
		return !item.isWeapon() && super.canUseItem(player, item);
	}
	
	@Override
	public void onDamageGive(CharacterData cha, CharacterData target, int damage, boolean isDOT) {
		try {
			if (cha.isPlayer() && target.isPlayer()) {
				PlayerEventInfo targetPlayer = target.getEventInfo();
				targetPlayer.abortCasting();
				targetPlayer.doDie(cha);
			}
		} catch (NullPointerException ex) {
		}
	}
	
	@Override
	public boolean canDestroyItem(PlayerEventInfo player, ItemData item) {
		// Fix logic: Use AND instead of OR to prevent destroying event weapons
		return (item.getItemId() != _bowItemId && item.getItemId() != _pistolItemId && item.getItemId() != _spearItemId) && super.canDestroyItem(player, item);
	}
	
	@Override
	public boolean canSupport(PlayerEventInfo player, CharacterData target) {
		return false;
	}
	
	@Override
	public boolean canUseSkill(PlayerEventInfo player, SkillData skill) {
		return false;
	}



	@Override
	public void onDisconnect(PlayerEventInfo player) {
		if (GabrielEventsLoader.detailedDebug) {
			print("HuntingGrounds: Player " + player.getPlayersName() + " disconnected, cleaning up weapons");
		}

		// Clean up weapons when player disconnects
		try {
			if (player.isSylphClass()) {
				player.destroyItemByItemId(_pistolItemId, 1);
				player.destroyItemByItemId(_orbItemId, 1);
			} else if (player.isVanguard()) {
				player.destroyItemByItemId(_spearItemId, 1);
			} else {
				player.destroyItemByItemId(_bowItemId, 1);
				// Remove all arrows
				while (player.getOwner().getInventory().getItemByItemId(_arrowItemId) != null) {
					player.destroyItemByItemId(_arrowItemId, 1);
				}
			}



		} catch (Exception e) {
			if (GabrielEventsLoader.detailedDebug) {
				print("HuntingGrounds: Error cleaning up weapons for disconnected player: " + e.getMessage());
			}
		}

		super.onDisconnect(player);
	}
	
	@Override
	public synchronized void clearEvent(int instanceId) {
		if (GabrielEventsLoader.detailedDebug) {
			print("Event: called CLEAREVENT for instance " + instanceId);
		}
		try {
			if (_matches != null) {
				for (TvTEventInstance match : _matches.values()) {
					if (instanceId == 0 || instanceId == match.getInstance().getId()) {
						match.abort();
						handleWeapons(match.getInstance().getId(), false);
						handleSkills(match.getInstance().getId(), false);
						preparePlayers(match.getInstance().getId(), false);
					}
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		for (PlayerEventInfo player : getPlayers(instanceId)) {
			if (player.isOnline()) {
				if (player.isParalyzed()) {
					player.setIsParalyzed(false);
				}
				if (player.isImmobilized()) {
					player.unroot();
				}
				if (!player.isGM()) {
					player.setIsInvul(false);
				}
				player.removeRadarAllMarkers();
				player.setInstanceId(0);
				if (_removeBuffsOnEnd) {
					player.removeBuffs();
				}
				player.restoreData();
				player.teleport(player.getOrigLoc(), 0, true, 0);
				player.sendMessage(LanguageEngine.getMsg("event_teleportBack"));
				if (player.getParty() != null) {
					PartyData party = player.getParty();
					party.removePartyMember(player);
				}
				player.broadcastUserInfo();

				// Auto-arrange inventory after event cleanup
				CallBack.getInstance().getOut().scheduleGeneral(() -> {
					if (player.isOnline() && player.getOwner() != null)
					{
						autoArrangeInventory(player);
						//player.sendMessage("Hành trang của bạn đã được tự động sắp xếp lại sau event!");
					}
				}, 2000); // 2 second delay to ensure all cleanup is complete
			}
		}
		clearPlayers(true, instanceId);
	}
	
	@Override
	public String getHtmlDescription() {
		if (_htmlDescription == null) {
			EventDescription desc = EventDescriptionSystem.getInstance().getDescription(getEventType());
			if (desc != null) {
				_htmlDescription = desc.getDescription(getConfigs());
			} else {
				_htmlDescription = getInt("teamsCount") + " teams fighting against each other. ";
				_htmlDescription += "Gain score by killing your opponents";
				if (getInt("killsForReward") > 0) {
					_htmlDescription = _htmlDescription + " (at least " + getInt("killsForReward") + " kill(s) is required to receive a reward)";
				}
				if (getBoolean("waweRespawn")) {
					_htmlDescription = _htmlDescription + " and dead players are resurrected by an advanced wawe-spawn engine each " + getInt("resDelay") + " seconds";
				} else {
					_htmlDescription = _htmlDescription + " and if you die, you will be resurrected in " + getInt("resDelay") + " seconds";
				}
				if (getBoolean("createParties")) {
					_htmlDescription += ". The event automatically creates parties on start";
				}
				_htmlDescription += ".";
			}
		}
		return _htmlDescription;
	}
	
	@Override
	protected TvTEventData createEventData(int instanceId) {
		return new HGEventData(instanceId);
	}
	
	@Override
	protected HGEventInstance createEventInstance(InstanceData instance) {
		return new HGEventInstance(instance);
	}
	
	@Override
	protected HGEventData getEventData(int instance) {
		return (HGEventData) _matches.get(instance)._data;
	}
	
	protected class HGEventInstance extends TvTEventInstance {
		protected HGEventInstance(InstanceData instance) {
			super(instance);
		}
		
		@Override
		public void run() {
			try {
				if (GabrielEventsLoader.detailedDebug) {
					print("Event: running task of state " + _state.toString() + "...");
				}
				switch (_state) {
					case START: {
						if (checkPlayers(_instance.getId())) {
							teleportPlayers(_instance.getId(), SpawnType.Regular, false);
							setupTitles(_instance.getId());
							enableMarkers(_instance.getId(), true);
							handleWeapons(_instance.getId(), true);
							handleSkills(_instance.getId(), true);
							preparePlayers(_instance.getId(), true);
							forceSitAll(_instance.getId());
							setNextState(EventState.FIGHT);
							scheduleNextTask(10000);
							break;
						}
						break;
					}
					case FIGHT: {
						forceStandAll(_instance.getId());
						if (getBoolean("createParties")) {
							createParties(getInt("maxPartySize"));
						}
						setNextState(EventState.END);
						_clock.startClock(_manager.getRunTime());
						break;
					}
					case END: {
						_clock.setTime(0, true);
						setNextState(EventState.INACTIVE);
						if (!instanceEnded() && _canBeAborted) {
							if (_canRewardIfAborted) {
								rewardAllTeams(_instance.getId(), getInt("killsForReward"), getInt("killsForReward"));
							}
							clearEvent(_instance.getId());
							break;
						}
						break;
					}
				}
				if (GabrielEventsLoader.detailedDebug) {
					print("Event: ... finished running task. next state " + _state.toString());
				}
			} catch (Throwable e) {
				e.printStackTrace();
				_manager.endDueToError(LanguageEngine.getMsg("event_error"));
			}
		}
	}
	
	protected class HGEventData extends TvTEventData {
		public HGEventData(int instance) {
			super(instance);
		}
	}
}
