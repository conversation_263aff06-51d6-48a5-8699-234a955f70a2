package gabriel.eventEngine.events.engine.main.events;

import gabriel.eventEngine.events.engine.base.EventType;
import gabriel.eventEngine.events.engine.base.RewardPosition;
import gabriel.eventEngine.events.engine.main.MainEventManager;

public class TreasureHuntPvp extends TreasureHunt {
	public TreasureHuntPvp(EventType type, MainEventManager manager) {
		super(type, manager);
		setRewardTypes(new RewardPosition[] {
			RewardPosition.Looser,
			RewardPosition.Tie,
			RewardPosition.Numbered,
			RewardPosition.Range,
			RewardPosition.FirstBlood,
			RewardPosition.FirstRegistered,
			RewardPosition.OnKill
		});
		_allowPvp = true;
	}
}
