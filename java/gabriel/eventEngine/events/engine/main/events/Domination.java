package gabriel.eventEngine.events.engine.main.events;

import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.concurrent.ConcurrentHashMap;

import club.projectessence.gameserver.model.skills.AbnormalVisualEffect;
import gabriel.eventEngine.events.GabrielEventsLoader;
import gabriel.eventEngine.events.engine.EventManager;
import gabriel.eventEngine.events.engine.base.ConfigModel;
import gabriel.eventEngine.events.engine.base.EventMap;
import gabriel.eventEngine.events.engine.base.EventPlayerData;
import gabriel.eventEngine.events.engine.base.EventSpawn;
import gabriel.eventEngine.events.engine.base.EventType;
import gabriel.eventEngine.events.engine.base.Loc;
import gabriel.eventEngine.events.engine.base.PvPEventPlayerData;
import gabriel.eventEngine.events.engine.base.RewardPosition;
import gabriel.eventEngine.events.engine.base.SpawnType;
import gabriel.eventEngine.events.engine.base.description.EventDescription;
import gabriel.eventEngine.events.engine.base.description.EventDescriptionSystem;
import gabriel.eventEngine.events.engine.lang.LanguageEngine;
import gabriel.eventEngine.events.engine.main.MainEventManager;
import gabriel.eventEngine.events.engine.main.base.MainEventInstanceType;
import gabriel.eventEngine.events.engine.stats.GlobalStatsModel;
import gabriel.eventEngine.events.engine.team.EventTeam;
import gabriel.eventEngine.interf.PlayerEventInfo;
import gabriel.eventEngine.interf.callback.CallbackManager;
import gabriel.eventEngine.interf.delegate.CharacterData;
import gabriel.eventEngine.interf.delegate.InstanceData;
import gabriel.eventEngine.interf.delegate.NpcData;
import gabriel.eventEngine.interf.delegate.PartyData;
import gabriel.eventEngine.l2j.CallBack;

public class Domination extends AbstractMainEvent
{
	protected Map<Integer, DominationEventInstance>	_matches;
	protected boolean								_waweRespawn;
	protected int									_teamsCount;
	protected int									_zoneNpcId;
	protected int									_zoneRadius;
	protected int									_zoneCheckInterval;
	protected int									_scoreForCapturingZone;
	private int										_holdZoneFor;
	protected int									_percentMajorityToScore;
	protected int									_tick;
	
	public Domination(EventType type, MainEventManager manager)
	{
		super(type, manager);
		_matches = new ConcurrentHashMap<Integer, DominationEventInstance>();
		setRewardTypes(new RewardPosition[]
		{
			RewardPosition.Winner,
			RewardPosition.Looser,
			RewardPosition.Tie,
			RewardPosition.FirstBlood,
			RewardPosition.FirstRegistered,
			RewardPosition.OnKill,
			RewardPosition.KillingSpree
		});
	}
	
	@Override
	public void loadConfigs()
	{
		super.loadConfigs();
		addConfig(new ConfigModel("scoreForReward", "0", "The minimum score required to get a reward (includes all possible rewards). Score in this event is gained by standing near the zone, if the player wasn't afk, he should always have some score."));
		addConfig(new ConfigModel("killsForReward", "0", "The minimum kills count required to get a reward (includes all possible rewards)."));
		addConfig(new ConfigModel("resDelay", "15", "The delay after which the player is resurrected. In seconds."));
		addConfig(new ConfigModel("waweRespawn", "true", "Enables the wawe-style respawn system."));
		addConfig(new ConfigModel("zoneNpcId", "8992", "The ID of NPC that symbolizes the zone."));
		addConfig(new ConfigModel("zoneRadius", "180", "The radius of zone to count players inside."));
		addConfig(new ConfigModel("allowZoneNpcEffects", "true", "Enables Zone NPC's special effects, if blue or red team owns it. Due to client limitations, this will only work if the event has 2 teams.", ConfigModel.InputType.Boolean));
		addConfig(new ConfigModel("allowFireworkOnScore", "true", "Enables Zone NPC's small firework effect, when a team scores. Working only if <font color=LEVEL>holdZoneFor</font> is higher than 5 (to prevent spamming this skill).", ConfigModel.InputType.Boolean));
		addConfig(new ConfigModel("allowPlayerEffects", "true", "Enables special effects for players from the team owning the zone and standing near the Zone NPC (in <font color=LEVEL>zoneRadius</font>). Only works if the event has 2 teams.", ConfigModel.InputType.Boolean));
		addConfig(new ConfigModel("zoneCheckInterval", "1", "In seconds. The time after it checks and counts players near the zone(s) and adds score to the team, that has more players inside the zone. Setting this to 1 is usually good (higher values make this event less expensive for cpu :)"));
		addConfig(new ConfigModel("scoreForCapturingZone", "1", "The ammount of points team gets each <font color=LEVEL>scoreCheckInterval</font> seconds if owns the zone."));
		addConfig(new ConfigModel("holdZoneFor", "0", "In seconds. The team needs to own this zone for this time to get <font color=LEVEL>scoreForCapturingZone</font> points. "));
		addConfig(new ConfigModel("percentMajorityToScore", "50", "In percent. In order to score a point, the team must have more players near the zone NPC in <font color=LEVEL>zoneRadius</font> radius, than the other team(s). The ammount of players from the scoring team must be higher than the ammount of players from the other teams by this percent value. Put 100 to make that all other team(s)' players in <font color=LEVEL>zoneRadius</font> must be dead to score; or put 0 to make that it will give score to the team that has more players and not care about any percent counting (eg. if team A has 15 players and team B has 16, it will simply reward team B)."));
		addConfig(new ConfigModel("createParties", "true", "Put 'True' if you want this event to automatically create parties for players in each team."));
		addConfig(new ConfigModel("maxPartySize", "9", "The maximum size of party, that can be created. Works only if <font color=LEVEL>createParties</font> is true."));
		addConfig(new ConfigModel("teamsCount", "2", "The ammount of teams in the event. Max is 5. <font color=FF0000>In order to change the count of teams in the event, you must also edit this config in the Instance's configuration.</font>"));
		addConfig(new ConfigModel("firstBloodMessage", "true", "You can turn off/on the first blood announce in the event (first kill made in the event). This is also rewardable - check out reward type FirstBlood.", ConfigModel.InputType.Boolean));
		addInstanceTypeConfig(new ConfigModel("teamsCount", "2", "You may specify the count of teams only for this instance. This config overrides events default teams count."));
	}
	
	@Override
	public void initEvent()
	{
		super.initEvent();
		_waweRespawn = getBoolean("waweRespawn");
		if (_waweRespawn)
		{
			initWaweRespawns(getInt("resDelay"));
		}
		_zoneNpcId = getInt("zoneNpcId");
		_zoneRadius = (int) Math.pow(getInt("zoneRadius"), 2.0);
		_zoneCheckInterval = getInt("zoneCheckInterval");
		_holdZoneFor = getInt("holdZoneFor");
		_scoreForCapturingZone = getInt("scoreForCapturingZone");
		_percentMajorityToScore = getInt("percentMajorityToScore");
		_runningInstances = 0;
		_tick = 0;
	}
	
	@Override
	protected int initInstanceTeams(MainEventInstanceType type)
	{
		_teamsCount = type.getConfigInt("teamsCount");
		if ((_teamsCount < 2) || (_teamsCount > 5))
		{
			_teamsCount = getInt("teamsCount");
		}
		if ((_teamsCount < 2) || (_teamsCount > 5))
		{
			_teamsCount = 2;
		}
		createTeams(_teamsCount, type.getInstance().getId());
		return _teamsCount;
	}

	/**
	 * Creates teams for the event instance.
	 */
	@Override
	protected void createTeams(int count, int instanceId)
	{
		if (GabrielEventsLoader.detailedDebug)
		{
			print("Domination: creating " + count + " teams for instanceId " + instanceId);
		}
		if (count != 2)
		{
			// System.out.println("Domination: Invalid team count " + count + ", forcing 2 teams for Fire and Water");
			count = 2;
		}
		_teams.put(instanceId, new ConcurrentHashMap<>());
		createNewTeam(instanceId, 1, "Fire", "Fire Faction");
		createNewTeam(instanceId, 2, "Water", "Water Faction");
		// System.out.println("Domination: Created teams for instance " + instanceId + ": Fire (teamId=1), Water (teamId=2)");
	}
	
	@Override
	public void runEvent()
	{
		if (GabrielEventsLoader.detailedDebug)
		{
			print("Event: started runEvent()");
		}
		if (!dividePlayers())
		{
			clearEvent();
			return;
		}
		_matches.clear();
		for (InstanceData instance : _instances)
		{
			if (GabrielEventsLoader.detailedDebug)
			{
				print("Event: creating eventinstance for instance " + instance.getId());
			}
			DominationEventInstance match = createEventInstance(instance);
			_matches.put(instance.getId(), match);
			_runningInstances++;
			match.scheduleNextTask(0);
			if (GabrielEventsLoader.detailedDebug)
			{
				print("Event: event instance started");
			}
		}
		if (GabrielEventsLoader.detailedDebug)
		{
			print("Event: finished runEvent()");
		}
	}
	
	protected void spawnZone(int instanceId)
	{
		if (GabrielEventsLoader.detailedDebug)
		{
			print("Event: spawning Zone for instance " + instanceId);
		}
		clearMapHistory(-1, SpawnType.Zone);
		EventSpawn sp = getSpawn(SpawnType.Zone, -1);
		NpcData zone = spawnNPC(sp.getLoc().getX(), sp.getLoc().getY(), sp.getLoc().getZ(), _zoneNpcId, instanceId, "Zone", "Domination event");
		int radius = sp.getRadius();
		if (radius > 0)
		{
			_zoneRadius = (int) Math.pow(radius, 2.0);
		}
		getEventData(instanceId).addZone(zone, _zoneRadius);
		getEventData(instanceId)._zone.getNpc().setTitle("No owner");
		getEventData(instanceId)._zone.getNpc().broadcastNpcInfo();
	}
	
	protected void unspawnZone(int instanceId)
	{
		if (GabrielEventsLoader.detailedDebug)
		{
			print("Event: unspawning zone for instance " + instanceId);
		}
		if (getEventData(instanceId)._zone != null)
		{
			getEventData(instanceId)._zone.deleteMe();
			if (GabrielEventsLoader.detailedDebug)
			{
				print("Event: zone is not null and was deleted");
			}
		}
		else if (GabrielEventsLoader.detailedDebug)
		{
			print("Event: ... zone is already null!!!");
		}
	}
	
	protected void setZoneEffects(int teamId, NpcData zoneNpc)
	{
		if (getBoolean("allowZoneNpcEffects") && (_teamsCount == 2))
		{
			if (teamId == 1)
			{
				zoneNpc.stopAbnormalEffect(AbnormalVisualEffect.STORM_SIGN2.getClientId());
				zoneNpc.startAbnormalEffect(AbnormalVisualEffect.STORM_SIGN1.getClientId());
			}
			else if (teamId == 2)
			{
				zoneNpc.stopAbnormalEffect(AbnormalVisualEffect.STORM_SIGN1.getClientId());
				zoneNpc.startAbnormalEffect(AbnormalVisualEffect.STORM_SIGN2.getClientId());
			}
			else
			{
				zoneNpc.stopAbnormalEffect(AbnormalVisualEffect.STORM_SIGN2.getClientId());
				zoneNpc.stopAbnormalEffect(AbnormalVisualEffect.STORM_SIGN1.getClientId());
			}
		}
	}
	
	@Override
	public void onEventEnd()
	{
		if (GabrielEventsLoader.detailedDebug)
		{
			print("Event: onEventEnd()");
		}
		int minKills = getInt("killsForReward");
		int minScore = getInt("scoreForReward");
		rewardAllTeams(-1, minScore, minKills);
	}
	
	@Override
	protected synchronized boolean instanceEnded()
	{
		_runningInstances--;
		if (GabrielEventsLoader.detailedDebug)
		{
			print("Event: notifying instance ended: runningInstances = " + _runningInstances);
		}
		if (_runningInstances == 0)
		{
			_manager.end();
			return true;
		}
		return false;
	}
	
	@Override
	protected synchronized void endInstance(int instance, boolean canBeAborted, boolean canRewardIfAborted, boolean forceNotReward)
	{
		if (GabrielEventsLoader.detailedDebug)
		{
			print("Event: endInstance() " + instance + ", canBeAborted " + canBeAborted + ", canReward.. " + canRewardIfAborted + " forceNotReward " + forceNotReward);
		}
		if (forceNotReward)
		{
			_matches.get(instance).forceNotRewardThisInstance();
		}
		_matches.get(instance).setNextState(EventState.END);
		if (canBeAborted)
		{
			_matches.get(instance).setCanBeAborted();
		}
		if (canRewardIfAborted)
		{
			_matches.get(instance).setCanRewardIfAborted();
		}
		_matches.get(instance).scheduleNextTask(0);
	}
	
	@Override
	protected String getScorebar(int instance)
	{
		final int count = _teams.get(instance).size();
		StringBuilder tb = new StringBuilder();
		for (EventTeam team : _teams.get(instance).values())
		{
			if (count <= 4)
			{
				tb.append(team.getTeamName() + ": " + team.getScore() + "  ");
			}
			else
			{
				tb.append(team.getTeamName().substring(0, 1) + ": " + team.getScore() + "  ");
			}
		}
		if (count <= 3)
		{
			tb.append(LanguageEngine.getMsg("event_scorebar_time", _matches.get(instance).getClock().getTime()));
		}
		return tb.toString();
	}
	
	@Override
	protected String getTitle(PlayerEventInfo pi)
	{
		if (_hideTitles)
		{
			return "";
		}
		if (pi.isAfk())
		{
			return "AFK";
		}
		return "Score: " + getPlayerData(pi).getScore();
	}
	
	@Override
	public void onKill(PlayerEventInfo player, CharacterData target)
	{
		if (target.getEventInfo() == null)
		{
			return;
		}
		if (player.getTeamId() != target.getEventInfo().getTeamId())
		{
			tryFirstBlood(player);
			giveOnKillReward(player);
			player.getEventTeam().raiseKills(1);
			getPlayerData(player).raiseKills(1);
			getPlayerData(player).raiseSpree(1);
			giveKillingSpreeReward(getPlayerData(player));
			if (player.isTitleUpdated())
			{
				player.setTitle(getTitle(player), true);
				player.broadcastTitleInfo();
			}
			CallbackManager.getInstance().playerKills(getEventType(), player, target.getEventInfo());
			setKillsStats(player, getPlayerData(player).getKills());
		}
	}
	
	@Override
	public void onDie(PlayerEventInfo player, CharacterData killer)
	{
		if (GabrielEventsLoader.detailedDebug)
		{
			print("/// Event: onDie - player " + player.getPlayersName() + " (instance " + player.getInstanceId() + "), killer " + killer.getName());
		}
		getPlayerData(player).raiseDeaths(1);
		getPlayerData(player).setSpree(0);
		setDeathsStats(player, getPlayerData(player).getDeaths());
		if (player.isTitleUpdated())
		{
			player.setTitle(getTitle(player), true);
			player.broadcastTitleInfo();
		}
		if (_waweRespawn)
		{
			_waweScheduler.addPlayer(player);
		}
		else
		{
			scheduleRevive(player, getInt("resDelay") * 1000);
		}
	}
	
	@Override
	public EventPlayerData createPlayerData(PlayerEventInfo player)
	{
		return new PvPEventPlayerData(player, this, new GlobalStatsModel(getEventType()));
	}
	
	@Override
	public PvPEventPlayerData getPlayerData(PlayerEventInfo player)
	{
		return (PvPEventPlayerData) player.getEventData();
	}
	
	@Override
	public synchronized void clearEvent(int instanceId)
	{
		if (GabrielEventsLoader.detailedDebug)
		{
			print("Event: called CLEAREVENT for instance " + instanceId);
		}
		try
		{
			if (_matches != null)
			{
				for (DominationEventInstance match : _matches.values())
				{
					if ((instanceId == 0) || (instanceId == match.getInstance().getId()))
					{
						match.abort();
						unspawnZone(match.getInstance().getId());
					}
				}
			}
		}
		catch (Exception e)
		{
			e.printStackTrace();
		}
		for (PlayerEventInfo player : getPlayers(instanceId))
		{
			if (player.isOnline())
			{
				if (player.isParalyzed())
				{
					player.setIsParalyzed(false);
				}
				if (player.isImmobilized())
				{
					player.unroot();
				}
				if (!player.isGM())
				{
					player.setIsInvul(false);
				}
				player.stopAbnormalEffect(CallBack.getInstance().getValues().ABNORMAL_IMPRISIONING_1());
				player.stopAbnormalEffect(CallBack.getInstance().getValues().ABNORMAL_REDCIRCLE());
				player.removeRadarAllMarkers();
				player.setInstanceId(0);
				if (_removeBuffsOnEnd)
				{
					player.removeBuffs();
				}
				player.restoreData();
				player.teleport(player.getOrigLoc(), 0, true, 0);
				player.sendMessage(LanguageEngine.getMsg("event_teleportBack"));
				if (player.getParty() != null)
				{
					final PartyData party = player.getParty();
					party.removePartyMember(player);
				}
				player.broadcastUserInfo();
			}
		}
		clearPlayers(true, instanceId);
	}
	
	@Override
	public synchronized void clearEvent()
	{
		if (GabrielEventsLoader.detailedDebug)
		{
			print("Event: called global clearEvent()");
		}
		clearEvent(0);
	}
	
	@Override
	protected void respawnPlayer(PlayerEventInfo pi, int instance)
	{
		if (GabrielEventsLoader.detailedDebug)
		{
			print("/// Event: respawning player " + pi.getPlayersName() + ", instance " + instance);
		}
		EventSpawn spawn = getSpawn(SpawnType.Regular, pi.getTeamId());
		if (spawn != null)
		{
			Loc loc = new Loc(spawn.getLoc().getX(), spawn.getLoc().getY(), spawn.getLoc().getZ());
			loc.addRadius(spawn.getRadius());
			pi.teleport(loc, 0, true, instance);
			pi.sendMessage(LanguageEngine.getMsg("event_respawned"));
		}
		else
		{
			debug("Error on respawnPlayer - no spawn type REGULAR, team " + pi.getTeamId() + " has been found. Event aborted.");
		}
	}
	
	@Override
	protected void clockTick()
	{
		_tick++;
		if ((_tick % _zoneCheckInterval) != 0)
		{
			return;
		}
		Map<Integer, List<PlayerEventInfo>> players = new LinkedHashMap<Integer, List<PlayerEventInfo>>(_teamsCount);
		for (DominationEventInstance match : _matches.values())
		{
			int instanceId = match.getInstance().getId();
			int zoneX = getEventData(instanceId)._zone.getLoc().getX();
			int zoneY = getEventData(instanceId)._zone.getLoc().getY();
			int zoneZ = getEventData(instanceId)._zone.getLoc().getZ();
			for (PlayerEventInfo player : getPlayers(instanceId))
			{
				if ((player.getDistanceSq(zoneX, zoneY, zoneZ) <= _zoneRadius) && player.isVisible() && !player.isDead())
				{
					if (!players.containsKey(player.getTeamId()))
					{
						players.put(player.getTeamId(), new LinkedList<PlayerEventInfo>());
					}
					players.get(player.getTeamId()).add(player);
				}
			}
			int highestCount = 0;
			int team = 0;
			boolean isThereMajorityTeam = true;
			for (Entry<Integer, List<PlayerEventInfo>> teamData : players.entrySet())
			{
				if (teamData.getValue().size() > highestCount)
				{
					highestCount = teamData.getValue().size();
					team = teamData.getKey();
				}
				else
				{
					if ((highestCount != 0) && (teamData.getValue().size() == highestCount))
					{
						isThereMajorityTeam = false;
						break;
					}
					continue;
				}
			}
			if (isThereMajorityTeam && (team != 0))
			{
				boolean ownsZone = false;
				if (_percentMajorityToScore == 0)
				{
					ownsZone = true;
				}
				else if (_percentMajorityToScore == 100)
				{
					boolean teamWithMorePlayers = false;
					for (Entry<Integer, List<PlayerEventInfo>> teamData : players.entrySet())
					{
						if (teamData.getKey() == team)
						{
							continue;
						}
						if (teamData.getValue().size() > 0)
						{
							teamWithMorePlayers = true;
							break;
						}
					}
					if (!teamWithMorePlayers)
					{
						ownsZone = true;
					}
				}
				else
				{
					int majorityTeamPlayers = players.get(team).size();
					boolean teamWithMorePlayers2 = false;
					for (Entry<Integer, List<PlayerEventInfo>> teamData : players.entrySet())
					{
						if (teamData.getKey() == team)
						{
							continue;
						}
						final double d = teamData.getValue().size() / (double) majorityTeamPlayers;
						final int percent = 100 - (int) (d * 100.0);
						if (percent < _percentMajorityToScore)
						{
							teamWithMorePlayers2 = true;
							break;
						}
					}
					if (!teamWithMorePlayers2)
					{
						ownsZone = true;
					}
				}
				if (ownsZone)
				{
					if (getEventData(instanceId)._holdingTeam != team)
					{
						if ((getEventData(instanceId)._holdingTeam != 0) && getBoolean("allowPlayerEffects") && (_teamsCount == 2))
						{
							for (PlayerEventInfo player : _teams.get(instanceId).get(getEventData(instanceId)._holdingTeam).getPlayers())
							{
								player.stopAbnormalEffect((player.getTeamId() == 1) ? AbnormalVisualEffect.STORM_SIGN1.getClientId() : AbnormalVisualEffect.STORM_SIGN2.getClientId());
							}
						}
						announce(instanceId, LanguageEngine.getMsg("dom_gainedZone", _teams.get(instanceId).get(team).getFullName()));
						getEventData(instanceId)._zone.getNpc().setTitle(LanguageEngine.getMsg("dom_npcTitle_owner", _teams.get(instanceId).get(team).getTeamName()));
						getEventData(instanceId)._zone.getNpc().broadcastNpcInfo();
						getEventData(instanceId)._holdingTeam = team;
						getEventData(instanceId)._holdingTime = 0;
						setZoneEffects(team, getEventData(instanceId)._zone);
					}
					else
					{
						ZoneData eventData = getEventData(instanceId);
						eventData._holdingTime += _zoneCheckInterval;
					}
					if (getBoolean("allowPlayerEffects") && (_teamsCount == 2))
					{
						for (PlayerEventInfo player : _teams.get(instanceId).get(team).getPlayers())
						{
							if ((player.getDistanceSq(zoneX, zoneY, zoneZ) <= _zoneRadius) && player.isVisible() && !player.isDead())
							{
								player.startAbnormalEffect((player.getTeamId() == 1) ? AbnormalVisualEffect.STORM_SIGN1.getClientId() : AbnormalVisualEffect.STORM_SIGN2.getClientId());
							}
							else
							{
								player.stopAbnormalEffect((player.getTeamId() == 1) ? AbnormalVisualEffect.STORM_SIGN1.getClientId() : AbnormalVisualEffect.STORM_SIGN2.getClientId());
							}
						}
					}
					if (getEventData(instanceId)._holdingTime >= _holdZoneFor)
					{
						_teams.get(instanceId).get(team).raiseScore(_scoreForCapturingZone);
						for (PlayerEventInfo player : players.get(team))
						{
							getPlayerData(player).raiseScore(_scoreForCapturingZone);
							setScoreStats(player, getPlayerData(player).getScore());
							if (player.isTitleUpdated())
							{
								player.setTitle(getTitle(player), true);
								player.broadcastTitleInfo();
							}
							CallbackManager.getInstance().playerScores(getEventType(), player, _scoreForCapturingZone);
						}
						getEventData(instanceId)._holdingTime = 0;
						if (_holdZoneFor <= 5)
						{
							continue;
						}
						announce(instanceId, "*** " + LanguageEngine.getMsg("dom_score", _teams.get(instanceId).get(team).getFullName()));
						if (!getBoolean("allowFireworkOnScore"))
						{
							continue;
						}
						getEventData(instanceId)._zone.broadcastSkillUse(getEventData(instanceId)._zone, getEventData(instanceId)._zone, 2024, 1);
					}
					else
					{
						int toHold = _holdZoneFor - getEventData(instanceId)._holdingTime;
						boolean announce = false;
						if (getEventData(instanceId)._holdingTime == 0)
						{
							announce = true;
						}
						else if ((toHold >= 60) && ((toHold % 60) == 0))
						{
							announce = true;
						}
						else
						{
							switch (toHold)
							{
								case 5:
								case 10:
								case 20:
								case 30:
								case 45:
								{
									announce = true;
									break;
								}
							}
						}
						if (!announce)
						{
							continue;
						}
						announce(instanceId, "* " + LanguageEngine.getMsg("dom_leftToScore", toHold, false ? "minute" : "second", _teams.get(instanceId).get(team).getFullName()));
					}
				}
				else
				{
					if (getEventData(instanceId)._holdingTeam != 0)
					{
						announce(instanceId, LanguageEngine.getMsg("dom_lostZone", _teams.get(instanceId).get(getEventData(instanceId)._holdingTeam).getFullName()));
						getEventData(instanceId)._zone.getNpc().setTitle(LanguageEngine.getMsg("dom_npcTitle_noOwner"));
						getEventData(instanceId)._zone.getNpc().broadcastNpcInfo();
						setZoneEffects(0, getEventData(instanceId)._zone);
						if (getBoolean("allowPlayerEffects") && (_teamsCount == 2))
						{
							for (PlayerEventInfo player : _teams.get(instanceId).get(getEventData(instanceId)._holdingTeam).getPlayers())
							{
								player.stopAbnormalEffect((player.getTeamId() == 1) ? AbnormalVisualEffect.STORM_SIGN1.getClientId() : AbnormalVisualEffect.STORM_SIGN2.getClientId());
							}
						}
					}
					getEventData(instanceId)._holdingTime = 0;
					getEventData(instanceId)._holdingTeam = 0;
				}
			}
			else
			{
				if (getEventData(instanceId)._holdingTeam != 0)
				{
					announce(instanceId, LanguageEngine.getMsg("dom_lostZone", _teams.get(instanceId).get(getEventData(instanceId)._holdingTeam).getFullName()));
					getEventData(instanceId)._zone.getNpc().setTitle(LanguageEngine.getMsg("dom_npcTitle_noOwner"));
					getEventData(instanceId)._zone.getNpc().broadcastNpcInfo();
					setZoneEffects(0, getEventData(instanceId)._zone);
					if (getBoolean("allowPlayerEffects") && (_teamsCount == 2))
					{
						for (PlayerEventInfo player : _teams.get(instanceId).get(getEventData(instanceId)._holdingTeam).getPlayers())
						{
							player.stopAbnormalEffect((player.getTeamId() == 1) ? AbnormalVisualEffect.STORM_SIGN1.getClientId() : AbnormalVisualEffect.STORM_SIGN2.getClientId());
						}
					}
				}
				getEventData(instanceId)._holdingTime = 0;
				getEventData(instanceId)._holdingTeam = 0;
			}
		}
	}
	
	@Override
	public String getEstimatedTimeLeft()
	{
		if (_matches == null)
		{
			return "Starting";
		}
		for (DominationEventInstance match : _matches.values())
		{
			if (match.isActive())
			{
				return match.getClock().getTime();
			}
		}
		return "N/A";
	}
	
	@Override
	public int getTeamsCount()
	{
		return getInt("teamsCount");
	}
	
	@Override
	public String getMissingSpawns(EventMap map)
	{
		StringBuilder tb = new StringBuilder();
		for (int i = 0; i < getTeamsCount(); i++)
		{
			if (!map.checkForSpawns(SpawnType.Regular, i + 1, 1))
			{
				tb.append(addMissingSpawn(SpawnType.Regular, i + 1, 1));
			}
		}
		if (!map.checkForSpawns(SpawnType.Zone, -1, 1))
		{
			tb.append(addMissingSpawn(SpawnType.Zone, 0, 1));
		}
		return tb.toString();
	}
	
	@Override
	protected String addExtraEventInfoCb(int instance)
	{
		int owningTeam = _matches.get(instance)._zoneData._holdingTeam;
		String status = "<font color=ac9887>Zone owned by:</font> <font color=" + EventManager.getInstance().getDarkColorForHtml(owningTeam) + ">" + EventManager.getInstance().getTeamName(owningTeam) + " team</font>";
		return "<table width=510 bgcolor=3E3E3E><tr><td width=510 align=center>" + status + "</td></tr></table>";
	}
	
	@Override
	public String getHtmlDescription()
	{
		if (_htmlDescription == null)
		{
			EventDescription desc = EventDescriptionSystem.getInstance().getDescription(getEventType());
			if (desc != null)
			{
				_htmlDescription = desc.getDescription(getConfigs());
			}
			else
			{
				_htmlDescription = getInt("teamsCount") + " teams fighting against each other. ";
				_htmlDescription += "The goal of this event is to capture and hold ";
				_htmlDescription += "a zone. The zone is represented by an NPC and to capture it, you need to stand near the NPC and ensure that no other enemies are standing near the zone too. ";
				if (getInt("killsForReward") > 0)
				{
					_htmlDescription = _htmlDescription + "At least " + getInt("killsForReward") + " kill(s) is required to receive a reward. ";
				}
				if (getInt("scoreForReward") > 0)
				{
					_htmlDescription = _htmlDescription + "At least " + getInt("scoreForReward") + " score (obtained when your team owns the zone and you stand near it) is required to receive a reward. ";
				}
				if (getBoolean("waweRespawn"))
				{
					_htmlDescription = _htmlDescription + "Dead players are resurrected by an advanced wawe-spawn engine each " + getInt("resDelay") + " seconds. ";
				}
				else
				{
					_htmlDescription = _htmlDescription + "If you die, you will get resurrected in " + getInt("resDelay") + " seconds. ";
				}
				if (getBoolean("createParties"))
				{
					_htmlDescription += "The event automatically creates parties on start.";
				}
			}
		}
		return _htmlDescription;
	}
	
	@Override
	protected AbstractEventInstance getMatch(int instanceId)
	{
		return _matches.get(instanceId);
	}
	
	@Override
	protected ZoneData createEventData(int instance)
	{
		return new ZoneData(instance);
	}
	
	@Override
	protected DominationEventInstance createEventInstance(InstanceData instance)
	{
		return new DominationEventInstance(instance);
	}
	
	@Override
	protected ZoneData getEventData(int instance)
	{
		return _matches.get(instance)._zoneData;
	}
	
	protected enum EventState
	{
		START,
		FIGHT,
		END,
		TELEPORT,
		INACTIVE;
	}
	
	protected class DominationEventInstance extends AbstractEventInstance
	{
		protected EventState	_state;
		protected ZoneData		_zoneData;
		
		protected DominationEventInstance(InstanceData instance)
		{
			super(instance);
			_state = EventState.START;
			_zoneData = createEventData(instance.getId());
		}
		
		protected void setNextState(EventState state)
		{
			_state = state;
		}
		
		@Override
		public boolean isActive()
		{
			return _state != EventState.INACTIVE;
		}
		
		@Override
		public void run()
		{
			try
			{
				if (GabrielEventsLoader.detailedDebug)
				{
					print("Event: running task of state " + _state.toString() + "...");
				}
				switch (_state)
				{
					case START:
					{
						if (checkPlayers(_instance.getId()))
						{
							teleportPlayers(_instance.getId(), SpawnType.Regular, false);
							setupTitles(_instance.getId());
							enableMarkers(_instance.getId(), true);
							spawnZone(_instance.getId());
							forceSitAll(_instance.getId());
							setNextState(EventState.FIGHT);
							scheduleNextTask(10000);
							break;
						}
						break;
					}
					case FIGHT:
					{
						forceStandAll(_instance.getId());
						if (getBoolean("createParties"))
						{
							createParties(getInt("maxPartySize"));
						}
						setNextState(EventState.END);
						_clock.startClock(_manager.getRunTime());
						break;
					}
					case END:
					{
						_clock.setTime(0, true);
						unspawnZone(_instance.getId());
						setNextState(EventState.INACTIVE);
						if (!instanceEnded() && _canBeAborted)
						{
							if (_canRewardIfAborted)
							{
								rewardAllTeams(_instance.getId(), getInt("scoreForReward"), getInt("killsForReward"));
							}
							clearEvent(_instance.getId());
							break;
						}
						break;
					}
				}
				if (GabrielEventsLoader.detailedDebug)
				{
					print("Event: ... finished running task. next state " + _state.toString());
				}
			}
			catch (Throwable e)
			{
				e.printStackTrace();
				_manager.endDueToError(LanguageEngine.getMsg("event_error"));
			}
		}
	}
	
	protected class ZoneData extends AbstractEventData
	{
		protected NpcData	_zone;
		protected int		_holdingTeam;
		protected int		_holdingTime;
		
		protected ZoneData(int instance)
		{
			super(instance);
		}
		
		protected void addZone(NpcData zone, int radius)
		{
			_zone = zone;
		}
	}
}
