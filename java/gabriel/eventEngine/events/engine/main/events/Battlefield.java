package gabriel.eventEngine.events.engine.main.events;

import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;

import gabriel.eventEngine.events.GabrielEventsLoader;
import gabriel.eventEngine.events.engine.EventManager;
import gabriel.eventEngine.events.engine.base.ConfigModel;
import gabriel.eventEngine.events.engine.base.EventMap;
import gabriel.eventEngine.events.engine.base.EventPlayerData;
import gabriel.eventEngine.events.engine.base.EventSpawn;
import gabriel.eventEngine.events.engine.base.EventType;
import gabriel.eventEngine.events.engine.base.Loc;
import gabriel.eventEngine.events.engine.base.PvPEventPlayerData;
import gabriel.eventEngine.events.engine.base.RewardPosition;
import gabriel.eventEngine.events.engine.base.SpawnType;
import gabriel.eventEngine.events.engine.base.description.EventDescription;
import gabriel.eventEngine.events.engine.base.description.EventDescriptionSystem;
import gabriel.eventEngine.events.engine.lang.LanguageEngine;
import gabriel.eventEngine.events.engine.main.MainEventManager;
import gabriel.eventEngine.events.engine.main.base.MainEventInstanceType;
import gabriel.eventEngine.events.engine.stats.GlobalStatsModel;
import gabriel.eventEngine.events.engine.team.EventTeam;
import gabriel.eventEngine.interf.PlayerEventInfo;
import gabriel.eventEngine.interf.callback.CallbackManager;
import gabriel.eventEngine.interf.delegate.CharacterData;
import gabriel.eventEngine.interf.delegate.InstanceData;
import gabriel.eventEngine.interf.delegate.NpcData;
import gabriel.eventEngine.interf.delegate.PartyData;
import gabriel.eventEngine.l2j.CallBack;

public class Battlefield extends AbstractMainEvent {
	protected Map<Integer, BattlefieldEventInstance> _matches;
	protected boolean _waweRespawn;
	protected int _teamsCount;
	protected int _towerNpcId;
	protected int _towerRadius;
	protected int _towerCheckInterval;
	protected int _scoreForCapturingTower;
	protected int _timeToHoldTowerToCapture;
	private int _holdAllTowersFor;
	protected int _percentMajorityToCapture;
	protected String _scoreType;
	protected int _minPlayersToCaptureTheBase;
	protected boolean isMinPlayersToCaptureTheBaseInPercent;
	protected int _minTowersToOwnToScore;
	protected int tick;
	protected int countOfTowers;
	
	public Battlefield(EventType type, MainEventManager manager) {
		super(type, manager);
		_matches = new ConcurrentHashMap<Integer, BattlefieldEventInstance>();
		setRewardTypes(new RewardPosition[] {
			RewardPosition.Winner,
			RewardPosition.Looser,
			RewardPosition.Tie,
			RewardPosition.FirstBlood,
			RewardPosition.FirstRegistered,
			RewardPosition.OnKill,
			RewardPosition.KillingSpree
		});
	}
	
	@Override
	public void loadConfigs() {
		super.loadConfigs();
		addConfig(new ConfigModel("scoreForReward", "0", "The minimum score required to get a reward (includes all possible rewards). Score in this event is gained by capturing bases."));
		addConfig(new ConfigModel("killsForReward", "0", "The minimum kills count required to get a reward (includes all possible rewards)."));
		addConfig(new ConfigModel("resDelay", "15", "The delay after which the player is resurrected. In seconds."));
		addConfig(new ConfigModel("waweRespawn", "true", "Enables the wawe-style respawn system."));
		addConfig(new ConfigModel("countOfBases", "2", "Specifies how many bases will be in the event. In order to score, one team must capture more bases than the other team(s). If you have 2 or 4 teams set in this event, you should only use odd numbers for the count of towers, such as 3, 5, 7 or 9. Don't forget to create a same count of Base spawns in the map you are running this event in. ", ConfigModel.InputType.Enum).addEnumOptions(new String[] {
			"2",
			"3",
			"4",
			"5",
			"6",
			"7",
			"8",
			"9",
			"10"
		}));
		addConfig(new ConfigModel("baseNpcId", "8998", "The ID of NPC that symbolizes the base."));
		addConfig(new ConfigModel("baseRadius", "180", "The radius of base to count players inside."));
		addConfig(new ConfigModel("allowBaseNpcEffects", "true", "Enables Base NPC's special effects, if blue or red team owns it. Due to client limitations, this will only work if the event has 2 teams.", ConfigModel.InputType.Boolean));
		addConfig(new ConfigModel("allowFireworkOnScore", "true", "Enables Base NPC's small firework effect, when a team scores. Working only if <font color=LEVEL>holdBaseFor</font> is higher than 5 (to prevent spamming this skill).", ConfigModel.InputType.Boolean));
		addConfig(new ConfigModel("allowPlayerEffects", "true", "Enables special effects for players from the team owning the base and standing near the Base NPC (in <font color=LEVEL>baseRadius</font>). Only works if the event has 2 teams.", ConfigModel.InputType.Boolean));
		addConfig(new ConfigModel("baseCheckInterval", "1", "In seconds. The time after it checks and counts players near the base(s) and adds score to the team, that has more players inside the base. Setting this to 1 is usually good (higher values make this event less expensive for cpu)"));
		addConfig(new ConfigModel("minPlayersToCaptureBase", "25%", "The min count of players the team must have near the base in order to capture it. You can set this value in percent by adding % (eg. 5%) - this will calculate the min count of players from the size of the team (eg. 20% and 50 players in the team = at least 10 players are needed to capture a base)."));
		addConfig(new ConfigModel("typeOfScoring", "AllTeams", "Define the way the event will give score to teams for capturing bases. If you select 'AllTeams', the event will score to all teams based on the count of bases they own (eg. team A has 2 bases - will receive 2 score, team B has 1 base - will receive 1 score). Setting 'DominatingTeam' will make it so that only the team which has MORE bases than the other teams will be receiving score points.", ConfigModel.InputType.Enum).addEnumOptions(new String[] {
			"AllTeams",
			"DominatingTeam"
		}));
		addConfig(new ConfigModel("scoreForCapturingBase", "1", "The ammount of points team gets each <font color=LEVEL>scoreCheckInterval</font> seconds if owns the base."));
		addConfig(new ConfigModel("holdBaseToCapture", "0", "In seconds. In order to capture a single base, the team needs to stay for this time near it."));
		addConfig(new ConfigModel("holdAllBasesToScore", "0", "In seconds. If the team captures enought bases to score, they will still need to hold them for this time in order to get <font color=LEVEL>scoreForCapturingBase</font> score."));
		addConfig(new ConfigModel("minTowersToOwnToScore", "1", "The min count of towers one team must own in order to get any score."));
		addConfig(new ConfigModel("percentMajorityToScore", "50", "In percent. In order to score a point, the team must have more players near the base NPC in <font color=LEVEL>baseRadius</font> radius, than the other team(s). The ammount of players from the scoring team must be higher than the ammount of players from the other teams by this percent value. Put 100 to make that all other team(s)' players in <font color=LEVEL>baseRadius</font> must be dead to score; or put 0 to make that it will give score to the team that has more players and not care about any percent counting (eg. if team A has 15 players and team B has 16, it will simply reward team B)."));
		addConfig(new ConfigModel("createParties", "true", "Put 'True' if you want this event to automatically create parties for players in each team."));
		addConfig(new ConfigModel("maxPartySize", "9", "The maximum size of party, that can be created. Works only if <font color=LEVEL>createParties</font> is true."));
		addConfig(new ConfigModel("teamsCount", "2", "The ammount of teams in the event. Max is 5. <font color=FF0000>In order to change the count of teams in the event, you must also edit this config in the Instance's configuration.</font>"));
		addConfig(new ConfigModel("firstBloodMessage", "true", "You can turn off/on the first blood announce in the event (first kill made in the event). This is also rewardable - check out reward type FirstBlood.", ConfigModel.InputType.Boolean));
		addInstanceTypeConfig(new ConfigModel("teamsCount", "2", "You may specify the count of teams only for this instance. This config overrides events default teams count."));
	}
	
	@Override
	public void initEvent() {
		super.initEvent();
		_waweRespawn = getBoolean("waweRespawn");
		if (_waweRespawn) {
			initWaweRespawns(getInt("resDelay"));
		}
		_towerNpcId = getInt("baseNpcId");
		_towerRadius = (int) Math.pow(getInt("baseRadius"), 2.0);
		_towerCheckInterval = getInt("baseCheckInterval");
		String s = getString("minPlayersToCaptureBase");
		if (s.endsWith("%")) {
			_minPlayersToCaptureTheBase = Integer.parseInt(s.substring(0, s.length() - 1));
			isMinPlayersToCaptureTheBaseInPercent = true;
		} else {
			_minPlayersToCaptureTheBase = Integer.parseInt(s);
			isMinPlayersToCaptureTheBaseInPercent = false;
		}
		_scoreType = getString("typeOfScoring");
		_minTowersToOwnToScore = getInt("minTowersToOwnToScore");
		_timeToHoldTowerToCapture = getInt("holdBaseToCapture");
		_holdAllTowersFor = getInt("holdBaseFor");
		_scoreForCapturingTower = getInt("scoreForCapturingBase");
		_percentMajorityToCapture = getInt("percentMajorityToScore");
		countOfTowers = _manager.getMap().getSpawns(-1, SpawnType.Base).size();
		_runningInstances = 0;
		tick = 0;
	}
	
	@Override
	protected int initInstanceTeams(MainEventInstanceType type) {
		_teamsCount = type.getConfigInt("teamsCount");
		if ((_teamsCount < 2) || (_teamsCount > 5)) {
			_teamsCount = getInt("teamsCount");
		}
		if ((_teamsCount < 2) || (_teamsCount > 5)) {
			_teamsCount = 2;
		}
		createTeams(_teamsCount, type.getInstance().getId());
		return _teamsCount;
	}
	
	@Override
	public void runEvent() {
		if (GabrielEventsLoader.detailedDebug) {
			print("Event: started runEvent()");
		}
		if (!dividePlayers()) {
			clearEvent();
			return;
		}
		_matches.clear();
		for (InstanceData instance : _instances) {
			if (GabrielEventsLoader.detailedDebug) {
				print("Event: creating eventinstance for instance " + instance.getId());
			}
			BattlefieldEventInstance match = createEventInstance(instance);
			_matches.put(instance.getId(), match);
			_runningInstances++;
			match.scheduleNextTask(0);
			if (GabrielEventsLoader.detailedDebug) {
				print("Event: event instance started");
			}
		}
		if (GabrielEventsLoader.detailedDebug) {
			print("Event: finished runEvent()");
		}
	}
	
	@Override
	protected void enableMarkers(int instanceId, boolean createEventSpawnMarkers) {
		if (!_enableRadar) {
			return;
		}
		for (EventTeam team : _teams.get(instanceId).values()) {
			for (PlayerEventInfo pi : team.getPlayers()) {
				pi.createRadar();
			}
			startRadar(instanceId, team);
		}
	}
	
	private void startRadar(int instanceId, EventTeam team) {
		try {
			if (!_enableRadar) {
				return;
			}
			EventSpawn zone = selectZoneForRadar(instanceId, team);
			if (zone != null) {
				for (PlayerEventInfo pi : team.getPlayers()) {
					pi.getRadar().setLoc(zone.getLoc().getX(), zone.getLoc().getY(), zone.getLoc().getZ());
					pi.getRadar().setRepeat(true);
					pi.getRadar().enable();
				}
			} else {
				for (PlayerEventInfo pi : team.getPlayers()) {
					pi.getRadar().setRepeat(false);
					pi.getRadar().disable();
				}
			}
		} catch (Exception ex) {
		}
	}
	
	private EventSpawn selectZoneForRadar(int instanceId, EventTeam team) {
		EventSpawn zone = null;
		int teamId = team.getTeamId();
		int topImportance = Integer.MAX_VALUE;
		Tower tempTopImportance = null;
		for (Tower tower : getEventData(instanceId)._towers) {
			if ((tower != null) && (tower.getOwningTeam() == 0) && (tower.getSpawn().getSpawnTeam() == teamId) && (tower.getSpawn().getImportance() < topImportance)) {
				topImportance = tower.getSpawn().getImportance();
				tempTopImportance = tower;
			}
		}
		if (tempTopImportance == null) {
			topImportance = Integer.MAX_VALUE;
			for (Tower tower : getEventData(instanceId)._towers) {
				if ((tower != null) && (tower.getOwningTeam() == 0) && (tower.getSpawn().getImportance() < topImportance)) {
					topImportance = tower.getSpawn().getImportance();
					tempTopImportance = tower;
				}
			}
		}
		if (tempTopImportance == null) {
			topImportance = Integer.MAX_VALUE;
			for (Tower tower : getEventData(instanceId)._towers) {
				if ((tower != null) && (tower.getSpawn().getSpawnTeam() == teamId) && (tower.getOwningTeam() != teamId) && (tower.getSpawn().getImportance() < topImportance)) {
					topImportance = tower.getSpawn().getImportance();
					tempTopImportance = tower;
				}
			}
		}
		if (tempTopImportance == null) {
			topImportance = 0;
			for (Tower tower : getEventData(instanceId)._towers) {
				if ((tower != null) && (tower.getOwningTeam() != teamId) && (tower.getSpawn().getImportance() > topImportance)) {
					topImportance = tower.getSpawn().getImportance();
					tempTopImportance = tower;
				}
			}
		}
		if (tempTopImportance != null) {
			zone = tempTopImportance.getSpawn();
		}
		return zone;
	}
	
	protected void spawnTowers(int instanceId) {
		if (GabrielEventsLoader.detailedDebug) {
			print("Event: spawning bases for instance " + instanceId);
		}
		clearMapHistory(-1, SpawnType.Base);
		int i = 0;
		for (EventSpawn sp : _manager.getMap().getSpawns(-1, SpawnType.Base)) {
			i++;
			NpcData base = spawnNPC(sp.getLoc().getX(), sp.getLoc().getY(), sp.getLoc().getZ(), _towerNpcId, instanceId, "Base " + i, "Domination event");
			getEventData(instanceId).addTower(base, sp.getRadius(), sp);
		}
	}
	
	protected void unspawnTowers(int instanceId) {
		if (GabrielEventsLoader.detailedDebug) {
			print("Event: unspawning base for instance " + instanceId);
		}
		for (Tower tower : getEventData(instanceId)._towers) {
			if (tower.getNpc() != null) {
				tower.setOwningTeam(0, false);
				tower.getNpc().deleteMe();
			}
		}
	}
	
	protected void setBaseEffects(int teamId, NpcData baseNpc) {
		if (getBoolean("allowBaseNpcEffects") && (_teamsCount == 2)) {
			if (teamId == 1) {
				baseNpc.stopAbnormalEffect(499);
				baseNpc.startAbnormalEffect(499);
			} else if (teamId == 2) {
				baseNpc.stopAbnormalEffect(500);
				baseNpc.startAbnormalEffect(500);
			} else {
				baseNpc.stopAbnormalEffect(499);
				baseNpc.stopAbnormalEffect(499);
			}
		}
	}
	
	@Override
	public void onEventEnd() {
		if (GabrielEventsLoader.detailedDebug) {
			print("Event: onEventEnd()");
		}
		int minKills = getInt("killsForReward");
		int minScore = getInt("scoreForReward");
		rewardAllTeams(-1, minScore, minKills);
	}
	
	@Override
	protected synchronized boolean instanceEnded() {
		_runningInstances--;
		if (GabrielEventsLoader.detailedDebug) {
			print("Event: notifying instance ended: runningInstances = " + _runningInstances);
		}
		if (_runningInstances == 0) {
			_manager.end();
			return true;
		}
		return false;
	}
	
	@Override
	protected synchronized void endInstance(int instance, boolean canBeAborted, boolean canRewardIfAborted, boolean forceNotReward) {
		if (GabrielEventsLoader.detailedDebug) {
			print("Event: endInstance() " + instance + ", canBeAborted " + canBeAborted + ", canReward.. " + canRewardIfAborted + " forceNotReward " + forceNotReward);
		}
		if (forceNotReward) {
			_matches.get(instance).forceNotRewardThisInstance();
		}
		_matches.get(instance).setNextState(EventState.END);
		if (canBeAborted) {
			_matches.get(instance).setCanBeAborted();
		}
		if (canRewardIfAborted) {
			_matches.get(instance).setCanRewardIfAborted();
		}
		_matches.get(instance).scheduleNextTask(0);
	}
	
	@Override
	protected String getScorebar(int instance) {
		final int count = _teams.get(instance).size();
		StringBuilder tb = new StringBuilder();
		for (EventTeam team : _teams.get(instance).values()) {
			if (count <= 4) {
				tb.append(team.getTeamName() + ": " + team.getScore() + "  ");
			} else {
				tb.append(team.getTeamName().substring(0, 1) + ": " + team.getScore() + "  ");
			}
		}
		if (count <= 3) {
			tb.append(LanguageEngine.getMsg("event_scorebar_time", _matches.get(instance).getClock().getTime()));
		}
		return tb.toString();
	}
	
	@Override
	protected String getTitle(PlayerEventInfo pi) {
		if (pi.isAfk()) {
			return "AFK";
		}
		return "Score: " + getPlayerData(pi).getScore();
	}
	
	@Override
	public void onKill(PlayerEventInfo player, CharacterData target) {
		if (target.getEventInfo() == null) {
			return;
		}
		if (player.getTeamId() != target.getEventInfo().getTeamId()) {
			tryFirstBlood(player);
			giveOnKillReward(player);
			player.getEventTeam().raiseKills(1);
			getPlayerData(player).raiseKills(1);
			getPlayerData(player).raiseSpree(1);
			giveKillingSpreeReward(getPlayerData(player));
			if (player.isTitleUpdated()) {
				player.setTitle(getTitle(player), true);
				player.broadcastTitleInfo();
			}
			CallbackManager.getInstance().playerKills(getEventType(), player, target.getEventInfo());
			setKillsStats(player, getPlayerData(player).getKills());
		}
	}
	
	@Override
	public void onDie(PlayerEventInfo player, CharacterData killer) {
		if (GabrielEventsLoader.detailedDebug) {
			print("/// Event: onDie - player " + player.getPlayersName() + " (instance " + player.getInstanceId() + "), killer " + killer.getName());
		}
		getPlayerData(player).raiseDeaths(1);
		getPlayerData(player).setSpree(0);
		setDeathsStats(player, getPlayerData(player).getDeaths());
		if (player.isTitleUpdated()) {
			player.setTitle(getTitle(player), true);
			player.broadcastTitleInfo();
		}
		if (_waweRespawn) {
			_waweScheduler.addPlayer(player);
		} else {
			scheduleRevive(player, getInt("resDelay") * 1000);
		}
	}
	
	@Override
	public EventPlayerData createPlayerData(PlayerEventInfo player) {
		return new PvPEventPlayerData(player, this, new GlobalStatsModel(getEventType()));
	}
	
	@Override
	public PvPEventPlayerData getPlayerData(PlayerEventInfo player) {
		return (PvPEventPlayerData) player.getEventData();
	}
	
	@Override
	public synchronized void clearEvent(int instanceId) {
		if (GabrielEventsLoader.detailedDebug) {
			print("Event: called CLEAREVENT for instance " + instanceId);
		}
		try {
			if (_matches != null) {
				for (BattlefieldEventInstance match : _matches.values()) {
					if ((instanceId == 0) || (instanceId == match.getInstance().getId())) {
						match.abort();
						unspawnTowers(match.getInstance().getId());
					}
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		for (PlayerEventInfo player : getPlayers(instanceId)) {
			if (player.isOnline()) {
				if (player.isParalyzed()) {
					player.setIsParalyzed(false);
				}
				if (player.isImmobilized()) {
					player.unroot();
				}
				if (!player.isGM()) {
					player.setIsInvul(false);
				}
				player.removeRadarAllMarkers();
				player.stopAbnormalEffect(CallBack.getInstance().getValues().ABNORMAL_IMPRISIONING_1());
				player.stopAbnormalEffect(CallBack.getInstance().getValues().ABNORMAL_REDCIRCLE());
				player.setInstanceId(0);
				if (_removeBuffsOnEnd) {
					player.removeBuffs();
				}
				player.restoreData();
				player.teleport(player.getOrigLoc(), 0, true, 0);
				player.sendMessage(LanguageEngine.getMsg("event_teleportBack"));
				if (player.getParty() != null) {
					PartyData party = player.getParty();
					party.removePartyMember(player);
				}
				player.broadcastUserInfo();
			}
		}
		clearPlayers(true, instanceId);
	}
	
	@Override
	public synchronized void clearEvent() {
		if (GabrielEventsLoader.detailedDebug) {
			print("Event: called global clearEvent()");
		}
		clearEvent(0);
	}
	
	@Override
	protected void respawnPlayer(PlayerEventInfo pi, int instance) {
		if (GabrielEventsLoader.detailedDebug) {
			print("/// Event: respawning player " + pi.getPlayersName() + ", instance " + instance);
		}
		EventSpawn spawn = getSpawn(SpawnType.Regular, pi.getTeamId());
		if (spawn != null) {
			Loc loc = new Loc(spawn.getLoc().getX(), spawn.getLoc().getY(), spawn.getLoc().getZ());
			loc.addRadius(spawn.getRadius());
			pi.teleport(loc, 0, true, instance);
			pi.sendMessage(LanguageEngine.getMsg("event_respawned"));
		} else {
			debug("Error on respawnPlayer - no spawn type REGULAR, team " + pi.getTeamId() + " has been found. Event aborted.");
		}
	}
	
	@Override
	protected void clockTick() {
		tick++;
		if ((tick % _towerCheckInterval) != 0) {
			return;
		}
		for (BattlefieldEventInstance instance : _matches.values()) {
			int instanceId = instance.getInstance().getId();
			if ((tick % 10) == 0) {
				for (EventTeam team : _teams.get(instanceId).values()) {
					startRadar(instanceId, team);
				}
			}
			TowerData towerData = getEventData(instanceId);
			Map<Integer, List<NpcData>> ownedTowers = new LinkedHashMap<Integer, List<NpcData>>();
			for (int i = 0; i < towerData._towers.length; i++) {
				Map<Integer, List<PlayerEventInfo>> players = new LinkedHashMap<Integer, List<PlayerEventInfo>>(_teamsCount);
				Tower tower = towerData._towers[i];
				NpcData towerNpc = towerData._towers[i].getNpc();
				int radius = towerData._towers[i].getRadius();
				int baseX = towerNpc.getLoc().getX();
				int baseY = towerNpc.getLoc().getY();
				int baseZ = towerNpc.getLoc().getZ();
				for (PlayerEventInfo player : getPlayers(instanceId)) {
					if ((player.getDistanceSq(baseX, baseY, baseZ) <= radius) && player.isVisible() && !player.isDead()) {
						if (!players.containsKey(player.getTeamId())) {
							players.put(player.getTeamId(), new LinkedList<PlayerEventInfo>());
						}
						players.get(player.getTeamId()).add(player);
					}
				}
				int highestCount = 0;
				int team = 0;
				boolean isThereMajorityTeam = true;
				for (Entry<Integer, List<PlayerEventInfo>> teamData : players.entrySet()) {
					if (teamData.getValue().size() > highestCount) {
						highestCount = teamData.getValue().size();
						team = teamData.getKey();
					} else {
						if ((highestCount != 0) && (teamData.getValue().size() == highestCount)) {
							isThereMajorityTeam = false;
							break;
						}
						continue;
					}
				}
				if (isThereMajorityTeam && (team != 0)) {
					final int majorityTeamPlayersCount = players.get(team).size();
					boolean dominatesBase = false;
					if (_percentMajorityToCapture == 0) {
						dominatesBase = true;
					} else if (_percentMajorityToCapture == 100) {
						boolean teamWithMorePlayers = false;
						for (Entry<Integer, List<PlayerEventInfo>> teamData : players.entrySet()) {
							if (teamData.getKey() == team) {
								continue;
							}
							if (teamData.getValue().size() > 0) {
								teamWithMorePlayers = true;
								break;
							}
						}
						if (!teamWithMorePlayers) {
							dominatesBase = true;
						}
					} else {
						boolean teamWithMorePlayers = false;
						for (Entry<Integer, List<PlayerEventInfo>> teamData : players.entrySet()) {
							if (teamData.getKey() == team) {
								continue;
							}
							final double d = teamData.getValue().size() / (double) majorityTeamPlayersCount;
							final int percent = 100 - (int) (d * 100.0);
							if (percent < _percentMajorityToCapture) {
								teamWithMorePlayers = true;
								break;
							}
						}
						if (!teamWithMorePlayers) {
							dominatesBase = true;
						}
					}
					if (dominatesBase) {
						int countInTeam = _teams.get(instanceId).get(team).getPlayers().size();
						int minCountOfPlayersNearTheBase;
						if (isMinPlayersToCaptureTheBaseInPercent) {
							minCountOfPlayersNearTheBase = (int) Math.round(countInTeam * (_minPlayersToCaptureTheBase * 0.01));
						} else {
							minCountOfPlayersNearTheBase = _minPlayersToCaptureTheBase;
						}
						if (minCountOfPlayersNearTheBase < 1) {
							minCountOfPlayersNearTheBase = 1;
						}
						if (majorityTeamPlayersCount < minCountOfPlayersNearTheBase) {
							if ((tick % 2) == 0) {
								for (final PlayerEventInfo player : players.get(team)) {
									if ((player != null) && player.isOnline()) {
										player.sendMessage("At least " + minCountOfPlayersNearTheBase + " players from your team are required to capture a base.");
									}
								}
							}
							dominatesBase = false;
						}
					}
					if (dominatesBase) {
						if (tower.getOwningTeam() == 0) {
							if (tower.setCapturingTime(tower.getCapturingTime() + _towerCheckInterval)) {
								announce(instanceId, _teams.get(instanceId).get(team).getTeamName() + " has gained the contol over base " + (i + 1));
								if (getBoolean("allowPlayerEffects") && (_teamsCount == 2)) {
									for (PlayerEventInfo player : tower.getEffectedPlayers()) {
										if (player != null) {
											tower.removeEffectedPlayer(player);
											player.stopAbnormalEffect((player.getTeamId() == 1) ? CallBack.getInstance().getValues().ABNORMAL_IMPRISIONING_1() : CallBack.getInstance().getValues().ABNORMAL_REDCIRCLE());
										}
									}
									tower.resetEffectedPlayers();
								}
								tower.setOwningTeam(team, true);
								setBaseEffects(team, towerNpc);
								towerNpc.setTitle("Owner: " + _teams.get(instanceId).get(team).getTeamName());
								towerNpc.broadcastNpcInfo();
								for (final PlayerEventInfo player : players.get(team)) {
									getPlayerData(player).raiseScore(_scoreForCapturingTower);
									setScoreStats(player, getPlayerData(player).getScore());
									if (player.isTitleUpdated()) {
										player.setTitle(getTitle(player), true);
										player.broadcastTitleInfo();
									}
									CallbackManager.getInstance().playerScores(getEventType(), player, _scoreForCapturingTower);
								}
							} else if (tower.getCapturingTime() == _towerCheckInterval) {
								announce(instanceId, _teams.get(instanceId).get(team).getTeamName() + " is now capturing base " + (i + 1));
							}
						} else if (tower.getOwningTeam() != team) {
							if (tower.setCapturingTime(tower.getCapturingTime() + _towerCheckInterval)) {
								announce(instanceId, _teams.get(instanceId).get(team).getTeamName() + " has gained the contol over base " + (i + 1));
								if (getBoolean("allowPlayerEffects") && (_teamsCount == 2)) {
									for (PlayerEventInfo player : tower.getEffectedPlayers()) {
										if (player != null) {
											tower.removeEffectedPlayer(player);
											player.stopAbnormalEffect((player.getTeamId() == 1) ? CallBack.getInstance().getValues().ABNORMAL_IMPRISIONING_1() : CallBack.getInstance().getValues().ABNORMAL_REDCIRCLE());
										}
									}
									tower.resetEffectedPlayers();
								}
								tower.setOwningTeam(team, true);
								setBaseEffects(team, towerNpc);
								towerNpc.setTitle("Owner: " + _teams.get(instanceId).get(team).getTeamName());
								towerNpc.broadcastNpcInfo();
								for (PlayerEventInfo player : players.get(team)) {
									getPlayerData(player).raiseScore(_scoreForCapturingTower);
									setScoreStats(player, getPlayerData(player).getScore());
									if (player.isTitleUpdated()) {
										player.setTitle(getTitle(player), true);
										player.broadcastTitleInfo();
									}
									CallbackManager.getInstance().playerScores(getEventType(), player, _scoreForCapturingTower);
								}
							} else if (tower.getCapturingTime() == _towerCheckInterval) {
								announce(instanceId, _teams.get(instanceId).get(team).getTeamName() + " is now capturing base " + (i + 1));
							}
						}
					} else if (tower.getCapturingTime() > 0) {
						tower.setCapturingTime(0);
					}
				} else if (tower.getCapturingTime() > 0) {
					tower.setCapturingTime(0);
				}
				if (tower.getOwningTeam() > 0) {
					if (getBoolean("allowPlayerEffects") && (_teamsCount == 2)) {
						if (players.containsKey(tower.getOwningTeam())) {
							for (PlayerEventInfo player : players.get(tower.getOwningTeam())) {
								if (!tower.containsEffectedPlayer(player)) {
									tower.addEffectedPlayer(player);
									player.startAbnormalEffect((player.getTeamId() == 1) ? CallBack.getInstance().getValues().ABNORMAL_IMPRISIONING_1() : CallBack.getInstance().getValues().ABNORMAL_REDCIRCLE());
								}
							}
						}
						for (PlayerEventInfo player : tower.getEffectedPlayers()) {
							if (!players.containsKey(tower.getOwningTeam()) || !players.get(tower.getOwningTeam()).contains(player)) {
								tower.removeEffectedPlayer(player);
								player.stopAbnormalEffect((player.getTeamId() == 1) ? CallBack.getInstance().getValues().ABNORMAL_IMPRISIONING_1() : CallBack.getInstance().getValues().ABNORMAL_REDCIRCLE());
							}
						}
					}
					tower.raiseOwnedTime(_towerCheckInterval);
					if (!ownedTowers.containsKey(tower.getOwningTeam())) {
						ownedTowers.put(tower.getOwningTeam(), new LinkedList<NpcData>());
					}
					ownedTowers.get(tower.getOwningTeam()).add(towerNpc);
				}
			}
			if (_scoreType.equals("AllTeams")) {
				_minTowersToOwnToScore = 1;
				for (Entry<Integer, List<NpcData>> e : ownedTowers.entrySet()) {
					int team = e.getKey();
					int countOfTowers = e.getValue().size();
					if ((countOfTowers >= _minTowersToOwnToScore) && (countOfTowers > 0)) {
						_teams.get(instanceId).get(team).raiseScore(countOfTowers);
					}
				}
			} else {
				if (!_scoreType.equals("DominatingTeam")) {
					continue;
				}
				boolean ownsRequiredCountOfBases = false;
				int teamWithMostBases = 0;
				int mostBasesCount = 0;
				for (Entry<Integer, List<NpcData>> e : ownedTowers.entrySet()) {
					if (e.getValue().size() > mostBasesCount) {
						teamWithMostBases = e.getKey();
						mostBasesCount = e.getValue().size();
						ownsRequiredCountOfBases = true;
					} else {
						if ((e.getValue().size() != 0) && (e.getValue().size() == mostBasesCount)) {
							ownsRequiredCountOfBases = false;
							break;
						}
						continue;
					}
				}
				if (ownsRequiredCountOfBases) {
					ownsRequiredCountOfBases = (mostBasesCount >= _minTowersToOwnToScore);
				}
				if (ownsRequiredCountOfBases) {
					if (teamWithMostBases != towerData._dominatingTeam) {
						announce(instanceId, "++ " + _teams.get(instanceId).get(teamWithMostBases).getFullName() + " is dominating " + mostBasesCount + " bases!");
						towerData.setDominatingTeam(teamWithMostBases);
						towerData.resetDominatingTime();
					} else {
						towerData.raiseDominatingTime(_towerCheckInterval);
					}
					if (towerData.getDominatingTime() >= _holdAllTowersFor) {
						_teams.get(instanceId).get(teamWithMostBases).raiseScore(_scoreForCapturingTower);
						towerData.resetDominatingTime();
						if (_holdAllTowersFor <= 5) {
							continue;
						}
						announce(instanceId, "*** " + _teams.get(instanceId).get(teamWithMostBases).getTeamName() + "s scored for owning " + mostBasesCount + " bases!");
						if (!getBoolean("allowFireworkOnScore")) {
							continue;
						}
						for (Tower tow : towerData._towers) {
							if ((tow.getNpc() != null) && (tow.getOwningTeam() == towerData.getDominatingTeam())) {
								tow.getNpc().broadcastSkillUse(tow.getNpc(), tow.getNpc(), 2024, 1);
							}
						}
					} else {
						int toHold = _holdAllTowersFor - towerData._holdingAllTowersFor;
						boolean announce = false;
						if (towerData._holdingAllTowersFor == 0) {
							announce = true;
						} else if ((toHold >= 60) && ((toHold % 60) == 0)) {
							announce = true;
						} else {
							switch (toHold) {
								case 5:
								case 10:
								case 20:
								case 30:
								case 45: {
									announce = true;
									break;
								}
							}
						}
						if (!announce) {
							continue;
						}
						announce(instanceId, "* " + LanguageEngine.getMsg("mDom_leftToScore", toHold, false ? "minutes" : "seconds", _teams.get(instanceId).get(teamWithMostBases).getFullName()));
					}
				} else {
					if ((towerData.getDominatingTeam() != 0) && (towerData.getDominatingTime() > 0)) {
						announce(instanceId, "-- " + _teams.get(instanceId).get(towerData._dominatingTeam).getFullName() + " has lost base domination.");
					}
					towerData.setDominatingTeam(0);
					towerData.resetDominatingTime();
				}
			}
		}
	}
	
	@Override
	public String getEstimatedTimeLeft() {
		if (_matches == null) {
			return "Starting";
		}
		for (BattlefieldEventInstance match : _matches.values()) {
			if (match.isActive()) {
				return match.getClock().getTime();
			}
		}
		return "N/A";
	}
	
	@Override
	public int getTeamsCount() {
		return getInt("teamsCount");
	}
	
	@Override
	public String getMissingSpawns(EventMap map) {
		StringBuilder tb = new StringBuilder();
		for (int i = 0; i < getTeamsCount(); i++) {
			if (!map.checkForSpawns(SpawnType.Regular, i + 1, 1)) {
				tb.append(addMissingSpawn(SpawnType.Regular, i + 1, 1));
			}
		}
		if (!map.checkForSpawns(SpawnType.Base, -1, 1)) {
			tb.append(addMissingSpawn(SpawnType.Base, 0, 1));
		}
		return tb.toString();
	}
	
	@Override
	protected String addExtraEventInfoCb(int instance) {
		int owningTeam = _matches.get(instance)._towerData._dominatingTeam;
		String status = "<font color=ac9887>Dominates:</font> <font color=" + EventManager.getInstance().getDarkColorForHtml(owningTeam) + ">" + EventManager.getInstance().getTeamName(owningTeam) + " team</font>";
		return "<table width=510 bgcolor=3E3E3E><tr><td width=510 align=center>" + status + "</td></tr></table>";
	}
	
	@Override
	public String getHtmlDescription() {
		if (_htmlDescription == null) {
			EventDescription desc = EventDescriptionSystem.getInstance().getDescription(getEventType());
			if (desc != null) {
				_htmlDescription = desc.getDescription(getConfigs());
			} else {
				_htmlDescription = "No information about this event yet.";
			}
		}
		return _htmlDescription;
	}
	
	@Override
	protected AbstractEventInstance getMatch(int instanceId) {
		return _matches.get(instanceId);
	}
	
	@Override
	protected TowerData createEventData(int instance) {
		return new TowerData(instance);
	}
	
	@Override
	protected BattlefieldEventInstance createEventInstance(InstanceData instance) {
		return new BattlefieldEventInstance(instance);
	}
	
	@Override
	protected TowerData getEventData(int instance) {
		return _matches.get(instance)._towerData;
	}
	
	protected enum EventState {
		START,
		FIGHT,
		END,
		TELEPORT,
		INACTIVE;
	}
	
	protected class BattlefieldEventInstance extends AbstractEventInstance {
		protected EventState _state;
		protected TowerData _towerData;
		
		protected BattlefieldEventInstance(InstanceData instance) {
			super(instance);
			_state = EventState.START;
			_towerData = createEventData(instance.getId());
		}
		
		protected void setNextState(EventState state) {
			_state = state;
		}
		
		@Override
		public boolean isActive() {
			return _state != EventState.INACTIVE;
		}
		
		@Override
		public void run() {
			try {
				if (GabrielEventsLoader.detailedDebug) {
					print("Event: running task of state " + _state.toString() + "...");
				}
				switch (_state) {
					case START: {
						if (checkPlayers(_instance.getId())) {
							teleportPlayers(_instance.getId(), SpawnType.Regular, false);
							setupTitles(_instance.getId());
							spawnTowers(_instance.getId());
							enableMarkers(_instance.getId(), true);
							forceSitAll(_instance.getId());
							setNextState(EventState.FIGHT);
							scheduleNextTask(10000);
							break;
						}
						break;
					}
					case FIGHT: {
						forceStandAll(_instance.getId());
						if (getBoolean("createParties")) {
							createParties(getInt("maxPartySize"));
						}
						setNextState(EventState.END);
						_clock.startClock(_manager.getRunTime());
						break;
					}
					case END: {
						_clock.setTime(0, true);
						unspawnTowers(_instance.getId());
						setNextState(EventState.INACTIVE);
						if (!instanceEnded() && _canBeAborted) {
							if (_canRewardIfAborted) {
								rewardAllTeams(_instance.getId(), getInt("scoreForReward"), getInt("killsForReward"));
							}
							clearEvent(_instance.getId());
							break;
						}
						break;
					}
				}
				if (GabrielEventsLoader.detailedDebug) {
					print("Event: ... finished running task. next state " + _state.toString());
				}
			} catch (Throwable e) {
				e.printStackTrace();
				_manager.endDueToError(LanguageEngine.getMsg("event_error"));
			}
		}
	}
	
	protected class TowerData extends AbstractEventData {
		protected Tower[] _towers;
		private int _order;
		protected int _dominatingTeam;
		protected int _holdingAllTowersFor;
		
		protected TowerData(int instance) {
			super(instance);
			_towers = new Tower[countOfTowers];
			_dominatingTeam = 0;
			_holdingAllTowersFor = 0;
			_order = 0;
		}
		
		protected void addTower(NpcData base, int radius, EventSpawn spawn) {
			if (_order < countOfTowers) {
				_towers[_order] = new Tower(spawn, base, (radius > 0) ? ((int) Math.pow(radius, 2.0)) : _towerRadius);
				_order++;
			} else {
				GabrielEventsLoader.debug("too many towers for TowerData (" + _order + "; " + countOfTowers + ")");
			}
		}
		
		protected void setDominatingTeam(int team) {
			_dominatingTeam = team;
		}
		
		protected int getDominatingTeam() {
			return _dominatingTeam;
		}
		
		protected int raiseDominatingTime(int time) {
			return _holdingAllTowersFor += time;
		}
		
		protected int getDominatingTime() {
			return _holdingAllTowersFor;
		}
		
		protected void resetDominatingTime() {
			_holdingAllTowersFor = 0;
		}
		
		protected Tower getTower(int index) {
			return _towers[index];
		}
	}
	
	protected class Tower {
		private NpcData _npc;
		private EventSpawn _spawn;
		private int _radius;
		private int _owningTeam;
		private int _ownedTime;
		private int _capturingTime;
		private final List<PlayerEventInfo> _effects;
		
		public Tower(EventSpawn spawn, NpcData npc, int radius) {
			_effects = new CopyOnWriteArrayList<PlayerEventInfo>();
			_spawn = spawn;
			_npc = npc;
			_radius = radius;
			_owningTeam = 0;
			_capturingTime = 0;
			_effects.clear();
		}
		
		public void setOwningTeam(int team, boolean updateTime) {
			_owningTeam = team;
			if (updateTime) {
				setOwnedTime(0);
			}
		}
		
		public boolean setCapturingTime(int i) {
			_capturingTime = i;
			return _capturingTime >= _timeToHoldTowerToCapture;
		}
		
		public int getCapturingTime() {
			return _capturingTime;
		}
		
		public void addEffectedPlayer(PlayerEventInfo player) {
			_effects.add(player);
		}
		
		public void removeEffectedPlayer(PlayerEventInfo player) {
			_effects.remove(player);
		}
		
		public boolean containsEffectedPlayer(PlayerEventInfo player) {
			return _effects.contains(player);
		}
		
		public List<PlayerEventInfo> getEffectedPlayers() {
			return _effects;
		}
		
		public void resetEffectedPlayers() {
			_effects.clear();
		}
		
		public int getOwningTeam() {
			return _owningTeam;
		}
		
		public int getOwnedTime() {
			return _ownedTime;
		}
		
		public void setOwnedTime(int i) {
			_ownedTime = i;
		}
		
		public void raiseOwnedTime(int count) {
			_ownedTime += count;
		}
		
		public NpcData getNpc() {
			return _npc;
		}
		
		public int getRadius() {
			return _radius;
		}
		
		public EventSpawn getSpawn() {
			return _spawn;
		}
		
		public Loc getLoc() {
			return _npc.getLoc();
		}
	}
}
