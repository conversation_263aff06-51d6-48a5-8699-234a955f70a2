package gabriel.eventEngine.events.engine.main.events;

import java.awt.Color;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.Set;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ScheduledFuture;

import club.projectessence.commons.concurrent.ThreadPool;
import club.projectessence.gameserver.enums.ItemLocation;
import club.projectessence.gameserver.geoengine.GeoEngine;
import club.projectessence.gameserver.model.Location;
import club.projectessence.gameserver.model.World;
import club.projectessence.gameserver.model.WorldObject;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.model.events.ListenerRegisterType;
import club.projectessence.gameserver.model.events.annotations.RegisterEvent;
import club.projectessence.gameserver.model.events.annotations.RegisterType;
import club.projectessence.gameserver.model.events.impl.creature.player.OnPlayerMoveRequest;
import club.projectessence.gameserver.model.events.returns.TerminateReturn;
import club.projectessence.gameserver.model.itemcontainer.Inventory;
import club.projectessence.gameserver.model.items.Item;
import club.projectessence.gameserver.model.items.instance.ItemInstance;
import club.projectessence.gameserver.model.skills.AbnormalVisualEffect;
import club.projectessence.gameserver.model.skills.Skill;
import club.projectessence.gameserver.network.SystemMessageId;

import club.projectessence.gameserver.network.serverpackets.ExServerPrimitive;
import club.projectessence.gameserver.network.serverpackets.ExShowTrace;
import club.projectessence.gameserver.network.serverpackets.InventoryUpdate;
import club.projectessence.gameserver.network.serverpackets.ServerPacket;
import club.projectessence.gameserver.network.serverpackets.SystemMessage;
import club.projectessence.gameserver.model.skills.AbnormalVisualEffect;
import gabriel.eventEngine.events.GabrielEventsLoader;
import gabriel.eventEngine.events.engine.EventManager;
import gabriel.eventEngine.events.engine.base.ConfigModel;
import gabriel.eventEngine.events.engine.base.EventMap;
import gabriel.eventEngine.events.engine.base.EventPlayerData;
import gabriel.eventEngine.events.engine.base.EventSpawn;
import gabriel.eventEngine.events.engine.base.EventType;
import gabriel.eventEngine.events.engine.base.Loc;
import gabriel.eventEngine.events.engine.base.PvPEventPlayerData;
import gabriel.eventEngine.events.engine.base.RewardPosition;
import gabriel.eventEngine.events.engine.base.SpawnType;
import gabriel.eventEngine.events.engine.EventRewardSystem;
import gabriel.eventEngine.events.engine.base.description.EventDescription;
import gabriel.eventEngine.events.engine.base.description.EventDescriptionSystem;
import gabriel.eventEngine.events.engine.lang.LanguageEngine;
import gabriel.eventEngine.events.engine.main.MainEventManager;
import gabriel.eventEngine.events.engine.main.base.MainEventInstanceType;
import gabriel.eventEngine.events.engine.stats.GlobalStatsModel;
import gabriel.eventEngine.interf.PlayerEventInfo;
import gabriel.eventEngine.interf.callback.CallbackManager;
import gabriel.eventEngine.interf.delegate.CharacterData;
import gabriel.eventEngine.interf.delegate.InstanceData;
import gabriel.eventEngine.interf.delegate.ItemData;
import gabriel.eventEngine.interf.delegate.NpcData;
import gabriel.eventEngine.interf.delegate.PartyData;
import gabriel.eventEngine.l2j.CallBack;

public class BattleRoyale extends AbstractMainEvent
{
	protected Map<Integer, BREventInstance>													_matches;
	protected boolean																		_antifeed;
	private int																				_weaponItemId;
	private int																				_ammoItemId;
	private int																				_airdropNpcId;
	private List<AirdropReward>																_airdropRewards;
	private int																				_airdropDespawnTime;
	private static final int																MAX_SPAWN_ATTEMPTS	= 100;
	private final List<Stage>																stages				= new ArrayList<>();
	private final ConcurrentHashMap<Integer, ConcurrentHashMap<Integer, PlayerEventInfo>>	activePlayers		= new ConcurrentHashMap<>();
	private final List<Loc>																	circleCenters		= new ArrayList<>();

	// Track circle rendering tasks for cleanup (similar to AutoPlayTaskManager)
	private final Set<ScheduledFuture<?>>													_circleRenderTasks	= ConcurrentHashMap.newKeySet();

	// Stuck command tracking
	private final Map<Integer, Long>														_playerStuckTimes	= new ConcurrentHashMap<>();
	private final Map<Integer, Loc>															_playerSpawnLocs	= new ConcurrentHashMap<>();

	// Safe zone status tracking for better UX
	private final Map<Integer, Boolean>														_playerSafeStatus	= new ConcurrentHashMap<>();

	// Throttling để tránh spam broadcastUserInfo
	private final Map<Integer, Long>														_lastBroadcastTimes	= new ConcurrentHashMap<>();
	private static final long																BROADCAST_THROTTLE	= 1000; // 1 second throttle

	// Throttling để tránh point display inconsistency
	private final Map<Integer, Long>														_lastTitleBroadcastTimes = new ConcurrentHashMap<>();
	private static final long																TITLE_BROADCAST_THROTTLE = 500; // 0.5 second throttle for titles

	// Circle rendering constants (optimized to prevent DirectX crashes)
	private static final int																POINTS_IN_CIRCLE = 12; // Reduced from 30 to 12 to prevent DirectX buffer overflow

	// Enhanced circle management for better stage transitions
	private final Map<Integer, Set<String>>													_activeCircleNames = new ConcurrentHashMap<>(); // Track active circle names per instance
	private final Map<Integer, Long>														_lastCircleClearTime = new ConcurrentHashMap<>(); // Track when circles were last cleared
	private static final long																CIRCLE_CLEAR_VERIFICATION_DELAY = 200; // 200ms to verify circle clearing
	
	private static class AirdropReward
	{
		int	itemId;
		int	minAmount;
		int	maxAmount;
		int	chance;
		
		AirdropReward(int itemId, int minAmount, int maxAmount, int chance)
		{
			this.itemId = itemId;
			this.minAmount = minAmount;
			this.maxAmount = maxAmount;
			this.chance = chance;
		}
	}
	
	private static class AirdropData
	{
		NpcData				airdrop;
		ScheduledFuture<?>	despawnTask;
		
		AirdropData(NpcData airdrop, ScheduledFuture<?> despawnTask)
		{
			this.airdrop = airdrop;
			this.despawnTask = despawnTask;
		}
	}
	
	public static class Stage
	{
		private final int	stageNumber;
		private int			safeTime;		// Bỏ final để có thể thay đổi giá trị
		private int			runTime;		// Bỏ final để có thể thay đổi giá trị
		private final int	radius;
		private final int	damageInitial;
		private final int	damageFinal;
		
		public Stage(int stageNumber, int radius, int damageInitial, int damageFinal, int safeTime, int runTime)
		{
			this.stageNumber = stageNumber;
			this.safeTime = safeTime;
			this.runTime = runTime;
			this.radius = radius;
			this.damageInitial = damageInitial;
			this.damageFinal = damageFinal;
		}
		
		public void setSafeTime(int safeTime)
		{
			this.safeTime = safeTime;
		}
		
		public void setRunTime(int runTime)
		{
			this.runTime = runTime;
		}
		
		public int getStageNumber()
		{
			return stageNumber;
		}
		
		public int getSafeTime()
		{
			return safeTime;
		}
		
		public int getRunTime()
		{
			return runTime;
		}
		
		public int getRadius()
		{
			return radius;
		}
		
		public int getDamageInitial()
		{
			return damageInitial;
		}
		
		public int getDamageFinal()
		{
			return damageFinal;
		}
	}
	
	public BattleRoyale(EventType type, MainEventManager manager)
	{
		super(type, manager);
		_matches = new ConcurrentHashMap<>();
		_airdropRewards = new ArrayList<>();
		setRewardTypes(new RewardPosition[]
		{
			RewardPosition.Winner,        // 1st place (last survivor)
			RewardPosition.Looser,        // 2nd+ place (eliminated players)
			RewardPosition.Numbered,      // Specific positions (1st, 2nd, 3rd, etc.)
			RewardPosition.Range,         // Position ranges (1-3, 4-10, etc.)
			RewardPosition.FirstBlood,    // First kill in the match
			RewardPosition.FirstRegistered, // First to register
			RewardPosition.OnKill,        // Per kill reward
			RewardPosition.KillingSpree   // Killing spree rewards
		});
	}
	
	@Override
	public void loadConfigs()
	{
		super.loadConfigs();
		addConfig(new ConfigModel("killsForReward", "0", "The minimum kills count required to get a reward."));
		addConfig(new ConfigModel("antifeedProtection", "true", "Enables anti-feed protection, changing player appearance.", ConfigModel.InputType.Boolean));
		addConfig(new ConfigModel("weaponItemId", "271", "The ID of the weapon given to players."));
		addConfig(new ConfigModel("ammoItemId", "17", "The ID of the ammo given to players."));
		addConfig(new ConfigModel("airdropNpcId", "40024", "The ID of the NPC representing the airdrop."));
		addConfig(new ConfigModel("airdropRewards", "94382,10,10,100|94380,1,1,100|94381,1,1,100", "List of airdrop rewards (format: itemId,minAmount,maxAmount,chance|...)."));
		addConfig(new ConfigModel("airdropDespawnTime", "120", "Time in seconds before an airdrop despawns if not claimed (default: 120 seconds)."));
		// PUBG-like stages: 2 minutes safe -> shrink phases with increasing damage, final battle 2 minutes
		addConfig(new ConfigModel("stages", "1,8000,100,200,150,0|" + "2,6000,200,400,150,0|" + "3,4000,400,800,120,0|" + "4,2000,800,1600,90,0|" + "5,1200,1200,2000,60,0|" + "6,800,2000,3000,45,0", "List of stages (format: stage,radius,damageInitial,damageFinal,safeTime,runTime|...). safeTime is the shrink duration; runTime is currently unused."));
		addConfig(new ConfigModel("battleRoyalItemHandlerId", "76000", "The ID of the BattleRoyalItemHandler item."));
		addConfig(new ConfigModel("allowedScrolls", "94380,94381", "List of scroll IDs that can be used in the event (format: itemId,itemId,...)"));
		addConfig(new ConfigModel("runTime", "900", "The total runtime of the event in seconds (15 minutes)."));
		addConfig(new ConfigModel("circleCenters", "-19830,144951,-3816|-14183,144127,-3604|-16390,140074,-3874|-20639,140774,-3884|-17970,142588,-3920", "List of circle center coordinates (format: x,y,z|x,y,z|...)."));
		addConfig(new ConfigModel("circleUpdateInterval", "15000", "Circle update interval in milliseconds (15s to prevent DirectX crashes)."));
		addConfig(new ConfigModel("damageInterval", "2000", "Damage check interval in milliseconds (2s for responsive damage)."));
		addConfig(new ConfigModel("disableCircleUpdates", "false", "Emergency setting: disable all circle updates to prevent crashes (true/false)."));

		// Bonus reward configs
		addConfig(new ConfigModel("survivalBonus15min", "300", "L-Coin bonus for surviving 15+ minutes."));
		addConfig(new ConfigModel("survivalBonus10min", "200", "L-Coin bonus for surviving 10+ minutes."));
		addConfig(new ConfigModel("survivalBonus5min", "100", "L-Coin bonus for surviving 5+ minutes."));
		addConfig(new ConfigModel("participationBonus", "50", "L-Coin bonus for participation."));
		addConfig(new ConfigModel("ramboBonus", "500", "L-Coin bonus for Rambo achievement (10+ kills)."));
		addConfig(new ConfigModel("ramboGVE", "5", "GVE Points bonus for Rambo achievement."));
		addConfig(new ConfigModel("pacifistBonus", "1000", "L-Coin bonus for Pacifist Winner achievement."));
		addConfig(new ConfigModel("pacifistGVE", "10", "GVE Points bonus for Pacifist Winner achievement."));
		addConfig(new ConfigModel("mostKillsBonus", "300", "L-Coin bonus for Most Kills achievement."));
		addConfig(new ConfigModel("mostKillsGVE", "3", "GVE Points bonus for Most Kills achievement."));
		addConfig(new ConfigModel("skilledFighterBonus", "200", "L-Coin bonus for Skilled Fighter achievement (5+ kills)."));
		addConfig(new ConfigModel("skilledFighterGVE", "2", "GVE Points bonus for Skilled Fighter achievement."));

		// Simple circle rendering - draw once with delay to prevent crash
		addConfig(new ConfigModel("circleUpdateDelay", "5000", "Delay between circle updates in milliseconds (5s to prevent crash)."));
	}
	
	// Phương thức tiện ích để xóa vật phẩm sự kiện từ inventory của người chơi
	public void removeEventItems(PlayerEventInfo player)
	{
		PlayerInstance playerInstance = player.getOwner();
		if (playerInstance == null)
		{
			if (GabrielEventsLoader.detailedDebug)
			{
				print("BattleRoyale: Player instance not found for " + player.getPlayersName() + " during item removal");
			}
			return;
		}
		InventoryUpdate iu = new InventoryUpdate();
		List<ItemInstance> currentItems = new ArrayList<>(playerInstance.getInventory().getItems());
		int handlerItemId = getInt("battleRoyalItemHandlerId");
		for (ItemInstance item : currentItems)
		{
			if (item.getCustomType1() == 9999 || item.getId() == handlerItemId)
			{
				if (item.isEquipped())
				{
					playerInstance.getInventory().unEquipItemInSlot(item.getLocationSlot());
					iu.addModifiedItem(item);
				}
				playerInstance.getInventory().destroyItem("EventItemRemoval", item, playerInstance, null);
				iu.addRemovedItem(item);
			}
		}
		if (player.isOnline())
		{
			playerInstance.sendInventoryUpdate(iu);
			playerInstance.sendItemList();
			playerInstance.broadcastUserInfo();
			// Reset điểm về 0 nếu dữ liệu người chơi còn tồn tại
			PvPEventPlayerData playerData = getPlayerData(player);
			if (playerData != null)
			{
				playerData.setEventPoints(0);
			}

			// Clear throttling cache để tránh memory leak
			_lastBroadcastTimes.remove(player.getPlayersId());
			_lastTitleBroadcastTimes.remove(player.getPlayersId());
			_playerSafeStatus.remove(player.getPlayersId());
			_playerStuckTimes.remove(player.getPlayersId());
			_playerSpawnLocs.remove(player.getPlayersId());

			// Clear radar markers and visual effects when leaving event
			if (player.getOwner() != null)
			{
				player.getOwner().getRadar().removeAllMarkers();
				player.getOwner().getEffectList().stopAbnormalVisualEffect(AbnormalVisualEffect.MAGIC_SQUARE);
			}
		}
	}
	
	@Override
	public void initEvent()
	{
		super.initEvent();
		_antifeed = getBoolean("antifeedProtection");
		_weaponItemId = getInt("weaponItemId");
		_ammoItemId = getInt("ammoItemId");
		_airdropNpcId = getInt("airdropNpcId");
		_airdropDespawnTime = getInt("airdropDespawnTime");
		_runningInstances = 0;
		// CHỈ xóa circles cho players trong event instances, KHÔNG gửi đến tất cả players
		// Comment out để tránh crash clients ngoài event
		/*
		for (PlayerEventInfo player : getPlayers(0)) // Lấy tất cả người chơi
		{
			if (player.isOnline())
			{
				PlayerInstance playerInstance = player.getOwner();
				if (playerInstance != null)
				{
					for (int stage = 1; stage <= stages.size(); stage++)
					{
						for (BRCircleColor color : BRCircleColor.values())
						{
							int circleId = -(110000 + stage * 10 + (color == BRCircleColor.WHITE ? 1 : 2));
							String circleName = "battleRoyaleCircle_" + circleId;
							ExServerPrimitive hidePacket = new ExServerPrimitive(circleName, 0, 0, 0);
							hidePacket.addPoint(0, 0, 0, 0);
							playerInstance.sendPacket(hidePacket);
						}
					}
				}
			}
		}
		*/
		// Xóa các vật phẩm sự kiện trên mặt đất khi khởi động sự kiện
		for (WorldObject obj : World.getInstance().getVisibleObjects())
		{
			if (obj instanceof ItemInstance)
			{
				ItemInstance item = (ItemInstance) obj;
				if (item.getCustomType1() == 9999)
				{
					item.decayMe();
					World.getInstance().removeObject(item);
					if (GabrielEventsLoader.detailedDebug)
					{
						print("BattleRoyale: Removed event item ID " + item.getId() + " from the ground at (" + item.getX() + ", " + item.getY() + ", " + item.getZ() + ") during initEvent");
					}
				}
			}
		}
		// Parse airdrop rewards - load directly from config without fallback
		_airdropRewards.clear();
		String rewardsConfig = getString("airdropRewards");
		if (GabrielEventsLoader.detailedDebug)
		{
			print("BattleRoyale: Raw airdropRewards config: '" + rewardsConfig + "'");
		}
		if (rewardsConfig != null && !rewardsConfig.trim().isEmpty())
		{
			rewardsConfig = rewardsConfig.trim().replaceAll("[\\s]+", "").replaceAll("[^0-9,|]", "").replaceAll("\\|\\|+", "|");
			String[] rewardEntries = rewardsConfig.split("\\|");
			for (String entry : rewardEntries)
			{
				try
				{
					String[] parts = entry.split(",");
					if (parts.length != 4)
					{
						print("BattleRoyale: Invalid airdrop reward format: " + entry + " (expected 4 parts, got " + parts.length + ")");
						continue;
					}
					int itemId = Integer.parseInt(parts[0].trim());
					int minAmount = Integer.parseInt(parts[1].trim());
					int maxAmount = Integer.parseInt(parts[2].trim());
					int chance = Integer.parseInt(parts[3].trim());
					_airdropRewards.add(new AirdropReward(itemId, minAmount, maxAmount, chance));
					if (GabrielEventsLoader.detailedDebug)
					{
						print("BattleRoyale: Loaded airdrop reward - Item: " + itemId + ", Amount: " + minAmount + "-" + maxAmount + ", Chance: " + chance + "%");
					}
				}
				catch (Exception e)
				{
					print("BattleRoyale: Error parsing airdrop reward: " + entry + " - " + e.getMessage());
				}
			}
		}
		// Parse stages - load directly from config without fallback
		stages.clear();
		String stagesConfig = getString("stages");
		if (GabrielEventsLoader.detailedDebug)
		{
			print("BattleRoyale: Raw stages config: '" + stagesConfig + "'");
		}
		if (stagesConfig != null && !stagesConfig.trim().isEmpty())
		{
			stagesConfig = stagesConfig.trim().replaceAll("\\s+", "");
			String[] stageEntries = stagesConfig.split("\\|");
			for (String entry : stageEntries)
			{
				try
				{
					String[] parts = entry.split(",");
					if (parts.length != 6)
					{
						print("BattleRoyale: Invalid stage format: " + entry + " (expected 6 parts, got " + parts.length + ")");
						continue;
					}
					int stageNumber = Integer.parseInt(parts[0].trim());
					int radius = Integer.parseInt(parts[1].trim());
					int damageInitial = Integer.parseInt(parts[2].trim());
					int damageFinal = Integer.parseInt(parts[3].trim());
					int safeTime = Integer.parseInt(parts[4].trim());
					int runTime = Integer.parseInt(parts[5].trim());
					stages.add(new Stage(stageNumber, radius, damageInitial, damageFinal, safeTime, runTime));
					if (GabrielEventsLoader.detailedDebug)
					{
						print("BattleRoyale: Loaded stage " + stageNumber + " - radius: " + radius + ", damage: " + damageInitial + "-" + damageFinal + ", times: " + safeTime + "/" + runTime);
					}
				}
				catch (Exception e)
				{
					print("BattleRoyale: Error parsing stage: " + entry + " - " + e.getMessage());
				}
			}
		}
		// Parse circle centers - load directly from config without fallback
		circleCenters.clear();
		String centersConfig = getString("circleCenters");
		if (centersConfig != null && !centersConfig.trim().isEmpty())
		{
			centersConfig = centersConfig.trim().replaceAll("\\s+", "");
			String[] centerEntries = centersConfig.split("\\|");
			for (String entry : centerEntries)
			{
				try
				{
					String[] parts = entry.split(",");
					if (parts.length != 3)
					{
						print("BattleRoyale: Invalid circle center format: " + entry + " (expected 3 parts, got " + parts.length + ")");
						continue;
					}
					int x = Integer.parseInt(parts[0].trim());
					int y = Integer.parseInt(parts[1].trim());
					int z = Integer.parseInt(parts[2].trim());
					circleCenters.add(new Loc(x, y, z));
					if (GabrielEventsLoader.detailedDebug)
					{
						print("BattleRoyale: Loaded circle center - X: " + x + ", Y: " + y + ", Z: " + z);
					}
				}
				catch (Exception e)
				{
					print("BattleRoyale: Error parsing circle center: " + entry + " - " + e.getMessage());
				}
			}
		}
		// Kiểm tra và cảnh báo nếu config rỗng (không dừng event)
		if (stages.isEmpty())
		{
			print("BattleRoyale: Warning: Stages configuration is empty! Event will use default behavior.");
		}
		else
		{
			print("BattleRoyale: Successfully loaded " + stages.size() + " stages from configuration.");
		}

		if (_airdropRewards.isEmpty())
		{
			print("BattleRoyale: Warning: Airdrop rewards configuration is empty! No airdrops will spawn.");
		}
		else
		{
			print("BattleRoyale: Successfully loaded " + _airdropRewards.size() + " airdrop rewards from configuration.");
		}

		if (circleCenters.isEmpty())
		{
			print("BattleRoyale: Warning: Circle centers configuration is empty! Circle positioning may not work properly.");
		}
		else
		{
			print("BattleRoyale: Successfully loaded " + circleCenters.size() + " circle centers from configuration.");
		}
	}
	
	@Override
	public boolean canUseItem(PlayerEventInfo player, ItemData item)
	{
		ItemInstance itemInstance = item.getOwner();
		if (itemInstance != null && itemInstance.getCustomType1() != 9999)
		{
			player.sendMessage("Only event items can be used in this event.");
			return false;
		}
		if ((notAllovedItems != null) && (Arrays.binarySearch(notAllovedItems, item.getItemId()) >= 0))
		{
			player.sendMessage(LanguageEngine.getMsg("event_itemNotAllowed"));
			return false;
		}
		if (item.isPotion() && !getBoolean("allowPotions"))
		{
			return false;
		}
		int handlerItemId = getInt("battleRoyalItemHandlerId");
		if (item.isScroll())
		{
			// Allow battleRoyalItemHandler
			if (item.getItemId() == handlerItemId)
			{
				return true;
			}

			// Check allowed scrolls from config
			String allowedScrollsConfig = getString("allowedScrolls");
			boolean isAllowed = false;
			if (allowedScrollsConfig != null && !allowedScrollsConfig.trim().isEmpty())
			{
				String[] allowedScrollIds = allowedScrollsConfig.split(",");
				for (String scrollIdStr : allowedScrollIds)
				{
					try
					{
						int allowedScrollId = Integer.parseInt(scrollIdStr.trim());
						if (item.getItemId() == allowedScrollId)
						{
							isAllowed = true;
							break;
						}
					}
					catch (NumberFormatException e)
					{
						// Skip invalid scroll ID
					}
				}
			}

			if (!isAllowed)
			{
				player.sendMessage("This scroll cannot be used in Battle Royale event.");
				return false;
			}
		}
		if (item.isPetCollar() && !_allowPets)
		{
			player.sendMessage(LanguageEngine.getMsg("event_petsNotAllowed"));
			return false;
		}
		return true;
	}
	
	@Override
	public int initInstanceTeams(MainEventInstanceType type)
	{
		createTeams(1, type.getInstance().getId());
		return 1;
	}
	
	@Override
	public void createTeams(int count, int instanceId)
	{
		if (GabrielEventsLoader.detailedDebug)
		{
			print("BattleRoyale: creating " + count + " teams for instanceId " + instanceId);
		}
		_teams.put(instanceId, new ConcurrentHashMap<>());
		createNewTeam(instanceId, 1, "BattleRoyale", "Battle Royale Players");
		updateActivePlayers(instanceId); // Initialize active players list
	}
	
	@Override
	public void runEvent()
	{
		if (GabrielEventsLoader.detailedDebug)
		{
			print("BattleRoyale: started runEvent()");
		}
		if (!dividePlayers())
		{
			clearEvent();
			return;
		}
		// Chuẩn hóa người chơi trước khi bắt đầu sự kiện
		for (PlayerEventInfo player : getPlayers(0)) // 0 để lấy tất cả người chơi
		{
			if (player.isOnline())
			{
				PlayerInstance playerInstance = player.getOwner();
				if (playerInstance != null)
				{
					// Vô hiệu hóa clan skill
					if (playerInstance.getClan() != null)
					{
						for (Skill clanSkill : playerInstance.getClan().getAllSkills())
						{
							playerInstance.disableSkill(clanSkill, -1);
						}
						playerInstance.sendSkillList();
					}
					// Cập nhật thông tin người chơi
					playerInstance.broadcastUserInfo();
					playerInstance.sendMessage("Your clan skills disabled for the Battle Royale event.");
				}
			}
		}
		_matches.clear();
		for (InstanceData instance : _instances)
		{
			BREventInstance match = createEventInstance(instance);
			_matches.put(instance.getId(), match);
			_runningInstances++;
			updateActivePlayers(instance.getId()); // Update active players for this instance
			match.scheduleNextTask(0);
		}
	}
	
	@Override
	public void onEventEnd()
	{
		int minKills = getInt("killsForReward");
		rewardAllPlayers(-1, minKills, minKills);
		if (_antifeed)
		{
			for (PlayerEventInfo player : getPlayers(0))
			{
				player.stopAntifeedProtection(false);
			}
		}
	}
	
	@Override
	public synchronized boolean instanceEnded()
	{
		_runningInstances--;
		if (_runningInstances == 0)
		{
			_manager.end();
			return true;
		}
		return false;
	}
	
	@Override
	public synchronized void endInstance(int instance, boolean canBeAborted, boolean canRewardIfAborted, boolean forceNotReward)
	{
		BREventInstance eventInstance = _matches.get(instance);
		if (eventInstance == null)
		{
			if (GabrielEventsLoader.detailedDebug)
			{
				print("BattleRoyale: Warning - endInstance called for non-existent instance " + instance);
			}
			return; // Prevent NullPointerException
		}

		if (forceNotReward)
		{
			eventInstance.forceNotRewardThisInstance();
		}
		eventInstance.setNextState(EventState.END);
		if (canBeAborted)
		{
			eventInstance.setCanBeAborted();
		}
		if (canRewardIfAborted)
		{
			_matches.get(instance).setCanRewardIfAborted();
		}
		_matches.get(instance).scheduleNextTask(0);
	}
	
	@Override
	protected String getScorebar(int instance)
	{
		StringBuilder tb = new StringBuilder();
		int alive = 0;
		ConcurrentHashMap<Integer, PlayerEventInfo> instancePlayers = activePlayers.get(instance);
		if (instancePlayers == null)
		{
			return "No players";
		}
		for (PlayerEventInfo player : instancePlayers.values())
		{
			if (player != null && !player.isDead())
			{
				alive++;
			}
		}
		tb.append("Alive: ").append(alive);
		BREventInstance match = _matches.get(instance);
		if (match == null)
		{
			return tb.toString();
		}
		long eventTimeRemaining = match._data.getShrinkTimeRemaining();
		long circleShrinkTimeRemaining = match._data.getCircleShrinkTimeRemaining();
		String clockTime;
		String shrinkTime;
		if (eventTimeRemaining <= 0)
		{
			clockTime = "0:00";
		}
		else
		{
			int minutes = (int) (eventTimeRemaining / 60);
			int seconds = (int) (eventTimeRemaining % 60);
			clockTime = String.format("%d:%02d", minutes, seconds);
		}
		if (circleShrinkTimeRemaining <= 0)
		{
			shrinkTime = "0:00";
		}
		else
		{
			int minutes = (int) (circleShrinkTimeRemaining / 60);
			int seconds = (int) (circleShrinkTimeRemaining % 60);
			shrinkTime = String.format("%d:%02d", minutes, seconds);
		}
		tb.append(" | Time: ").append(clockTime);
		if (circleShrinkTimeRemaining >= 0)
		{
			tb.append(" | Shrink: ").append(shrinkTime);
		}
		//int stageNumber = match._data.getCurrentStageIndex() >= 0 ? match._data.getStages().get(match._data.getCurrentStageIndex()).getStageNumber() : 0; // Hiển thị Stage 0 khi currentStageIndex = -1
		//tb.append(" | Stage: ").append(stageNumber);
		return tb.toString();
	}
	
	@Override
	public String getTitle(PlayerEventInfo pi)
	{
		if (_hideTitles)
		{
			return "";
		}
		if (pi.isAfk())
		{
			return "AFK";
		}
		// return "Kills: " + getPlayerData(pi).getScore() + " Deaths: " + getPlayerData(pi).getDeaths();
		// return "Points: " + getPlayerData(pi).getEventPoints();
		return "Points: " + getPlayerData(pi).getEventPoints() + " Kills: " + getPlayerData(pi).getScore();
	}
	
	@Override
	public void onKill(PlayerEventInfo player, CharacterData target)
	{
		if (target.getEventInfo() == null)
		{
			if (target.getNpc() != null)
			{
				if (target.getNpc().getNpcId() == _airdropNpcId)
				{
					handleAirdropKill(player, target.getNpc());
				}
			}
			return;
		}
		// Hồi 30% HP, MP, CP cho người chơi giết đối thủ
		PlayerInstance killer = player.getOwner();
		if (killer != null)
		{
			// Hồi 30% HP
			double maxHp = killer.getMaxHp();
			double currentHp = killer.getCurrentHp();
			double hpToRestore = maxHp * 0.3;
			killer.setCurrentHp(Math.min(maxHp, currentHp + hpToRestore));
			// Hồi 30% MP
			double maxMp = killer.getMaxMp();
			double currentMp = killer.getCurrentMp();
			double mpToRestore = maxMp * 0.3;
			killer.setCurrentMp(Math.min(maxMp, currentMp + mpToRestore));
			// Hồi 30% CP
			double maxCp = killer.getMaxCp();
			double currentCp = killer.getCurrentCp();
			double cpToRestore = maxCp * 0.3;
			killer.setCurrentCp(Math.min(maxCp, currentCp + cpToRestore));
		}
		// Tính số người chơi còn sống và điều chỉnh điểm
		ConcurrentHashMap<Integer, PlayerEventInfo> instancePlayers = activePlayers.getOrDefault(player.getInstanceId(), new ConcurrentHashMap<>());
		int aliveCount = (int) instancePlayers.values().stream().filter(p -> p.isOnline() && !p.isDead()).count();
		int points = aliveCount < 5 ? 15 : 10;
		getPlayerData(player).addEventPoints(points);
		tryFirstBlood(player);
		giveOnKillReward(player);
		getPlayerData(player).raiseScore(1);
		getPlayerData(player).raiseKills(1);
		getPlayerData(player).raiseSpree(1);
		giveKillingSpreeReward(getPlayerData(player));
		if (player.isTitleUpdated())
		{
			// Force title update with current points
			String newTitle = getTitle(player);
			player.setTitle(newTitle, true);
			safeBroadcastTitleInfo(player);
			safeBroadcastUserInfo(player);
		}
		CallbackManager.getInstance().playerKills(getEventType(), player, target.getEventInfo());
		setScoreStats(player, getPlayerData(player).getScore());
		setKillsStats(player, getPlayerData(player).getKills());
		checkForWinner(player.getInstanceId());
	}
	
	private void handleAirdropKill(PlayerEventInfo player, NpcData npc)
	{
		int instanceId = player.getInstanceId();
		if (getMatch(instanceId).isActive())
		{
			AirdropData airdropData = null;
			for (AirdropData data : getEventData(instanceId)._airdropSpawns)
			{
				if (data != null && data.airdrop != null && data.airdrop.getObjectId() == npc.getObjectId())
				{
					airdropData = data;
					break;
				}
			}
			if (airdropData == null)
			{
				if (GabrielEventsLoader.detailedDebug)
				{
					print("BattleRoyale: Airdrop not found in list for NPC ID " + npc.getNpcId());
				}
				return;
			}
			selectAirdropAction(player, npc);
			// Thêm 5 điểm (eventPoints) cho người chơi khi giết airdrop
			getPlayerData(player).addEventPoints(5);
			if (airdropData.despawnTask != null)
			{
				airdropData.despawnTask.cancel(false);
			}
			synchronized (getEventData(instanceId)._airdropSpawns)
			{
				getEventData(instanceId)._airdropSpawns.remove(airdropData);
			}
			airdropData.airdrop.deleteMe();
			for (PlayerEventInfo p : getPlayers(instanceId))
			{
				if (p.isOnline())
				{
					p.removeRadarAllMarkers();
					for (AirdropData remainingAirdrop : getEventData(instanceId)._airdropSpawns)
					{
						if (remainingAirdrop != null && remainingAirdrop.airdrop != null && !remainingAirdrop.airdrop.isDead())
						{
							p.addRadarMarker(remainingAirdrop.airdrop.getLoc().getX(), remainingAirdrop.airdrop.getLoc().getY(), remainingAirdrop.airdrop.getLoc().getZ());
						}
					}
				}
			}
		}
	}
	
	private void selectAirdropAction(PlayerEventInfo player, NpcData npc)
	{
		int instanceId = player.getInstanceId();
		if (instanceId <= 0)
		{
			if (GabrielEventsLoader.detailedDebug)
			{
				System.out.println("BattleRoyale: Warning: Invalid instance ID " + instanceId + " for player " + player.getPlayersName() + " in airdrop. Skipping airdrop rewards.");
			}
			return;
		}
		Random random = new Random();
		// Loại bỏ vòng lặp rewardCount, chỉ trao phần thưởng 1 lần duy nhất
		for (AirdropReward reward : _airdropRewards)
		{
			// Nếu chance = 100, luôn thưởng item này
			if (reward.chance == 100)
			{
				int amount = reward.minAmount + random.nextInt(reward.maxAmount - reward.minAmount + 1);
				ItemData itemData = player.addItem(reward.itemId, amount, true);
				if (itemData != null)
				{
					ItemInstance itemInstance = itemData.getOwner();
					if (itemInstance != null)
					{
						itemInstance.setCustomType1(9999);
						itemInstance.setInstanceById(instanceId);
						getEventData(instanceId).addPlayerItem(player, reward.itemId);
					}
				}
				if (GabrielEventsLoader.detailedDebug)
				{
					System.out.println("BattleRoyale: Player " + player.getPlayersName() + " received airdrop reward - Item ID: " + reward.itemId + ", Amount: " + amount + " in instance " + instanceId);
				}
			}
			else
			{
				// Với chance < 100, sử dụng logic ngẫu nhiên
				int totalChance = 0;
				for (AirdropReward r : _airdropRewards)
				{
					if (r.chance != 100) // Chỉ tính các item có chance < 100
					{
						totalChance += r.chance;
					}
				}
				if (totalChance <= 0)
				{
					if (GabrielEventsLoader.detailedDebug)
					{
						print("BattleRoyale: Warning: Total chance for airdrop rewards (excluding chance=100) is 0. Skipping random reward selection.");
					}
					continue;
				}
				int roll = random.nextInt(totalChance);
				int currentChance = 0;
				AirdropReward selectedReward = null;
				for (AirdropReward r : _airdropRewards)
				{
					if (r.chance == 100) // Bỏ qua các item có chance=100 vì đã xử lý ở trên
						continue;
					currentChance += r.chance;
					if (roll < currentChance)
					{
						selectedReward = r;
						break;
					}
				}
				if (selectedReward != null)
				{
					int amount = selectedReward.minAmount + random.nextInt(selectedReward.maxAmount - selectedReward.minAmount + 1);
					ItemData itemData = player.addItem(selectedReward.itemId, amount, true);
					if (itemData != null)
					{
						ItemInstance itemInstance = itemData.getOwner();
						if (itemInstance != null)
						{
							itemInstance.setCustomType1(9999);
							itemInstance.setInstanceById(instanceId);
							getEventData(instanceId).addPlayerItem(player, selectedReward.itemId);
						}
					}
				}
			}
		}
	}
	
	@Override
	public void onDie(PlayerEventInfo player, CharacterData killer)
	{
		getPlayerData(player).raiseDeaths(1);
		getPlayerData(player).setSpree(0);
		setDeathsStats(player, getPlayerData(player).getDeaths());
		if (player.isTitleUpdated())
		{
			// Force title update with current points
			String newTitle = getTitle(player);
			player.setTitle(newTitle, true);
			safeBroadcastTitleInfo(player);
			safeBroadcastUserInfo(player);
		}
		// Drop chỉ vật phẩm sự kiện khi người chơi chết (không drop items gốc)
		PlayerInstance playerInstance = player.getOwner();
		if (playerInstance != null)
		{
			InventoryUpdate iu = new InventoryUpdate();
			List<ItemInstance> itemsToDrop = new ArrayList<>();
			int handlerItemId = getInt("battleRoyalItemHandlerId");

			// Chỉ thu thập vật phẩm sự kiện (customType1 = 9999) để drop
			for (ItemInstance item : playerInstance.getInventory().getItems())
			{
				if (item != null && item.getCustomType1() == 9999 && item.getId() != handlerItemId)
				{
					itemsToDrop.add(item);
				}
			}

			// Drop chỉ vật phẩm sự kiện
			for (ItemInstance item : itemsToDrop)
			{
				if (item.isEquipped())
				{
					playerInstance.getInventory().unEquipItemInSlot(item.getLocationSlot());
					iu.addModifiedItem(item);
				}
				long itemCount = item.getCount();
				if (itemCount > Integer.MAX_VALUE)
				{
					itemCount = Integer.MAX_VALUE;
				}
				ItemInstance droppedItem = CallBack.getInstance().getOut().dropItem(playerInstance, item.getId(), (int) itemCount, playerInstance.getX(), playerInstance.getY(), playerInstance.getZ() + 50, item.getEnchantLevel(), player.getInstanceId());
				if (droppedItem != null)
				{
					droppedItem.setCustomType1(9999);
				}
				playerInstance.getInventory().destroyItem("DropOnDeath", item, playerInstance, null);
				iu.addRemovedItem(item);
			}

			if (!itemsToDrop.isEmpty())
			{
				playerInstance.sendInventoryUpdate(iu);
				playerInstance.sendItemList();
				playerInstance.broadcastUserInfo();
			}

			if (GabrielEventsLoader.detailedDebug)
			{
				print("BattleRoyale: Player " + player.getPlayersName() + " died, dropped " + itemsToDrop.size() + " event items");
			}
		}
		// Remove player from active list
		ConcurrentHashMap<Integer, PlayerEventInfo> instancePlayers = activePlayers.get(player.getInstanceId());
		if (instancePlayers != null)
		{
			instancePlayers.remove(player.getPlayersId());
		}
		checkForWinner(player.getInstanceId());
	}
	
	@Override
	public void onDisconnect(PlayerEventInfo player)
	{
		// Remove event items from player's inventory before calling super.onDisconnect
		removeEventItems(player);

		// Clear all Battle Royale circles for disconnecting player
		clearAllCirclesForPlayer(player);

		// Remove player from active list
		ConcurrentHashMap<Integer, PlayerEventInfo> instancePlayers = activePlayers.get(player.getInstanceId());
		if (instancePlayers != null)
		{
			instancePlayers.remove(player.getPlayersId());
		}
		// Call super.onDisconnect after handling player data
		super.onDisconnect(player);
		int instanceId = player.getInstanceId();
		if (!checkIfEventCanContinue(instanceId, player))
		{
			endEvent(instanceId);
			clearEvent(instanceId);
		}
	}
	
	private void updateActivePlayers(int instanceId)
	{
		Set<PlayerEventInfo> playerSet = getPlayers(instanceId);
		ConcurrentHashMap<Integer, PlayerEventInfo> active = new ConcurrentHashMap<>();
		if (playerSet != null)
		{
			for (PlayerEventInfo player : playerSet)
			{
				if (player.isOnline() && !player.isDead())
				{
					active.put(player.getPlayersId(), player);
				}
			}
		}
		activePlayers.put(instanceId, active);
	}
	
	private void checkForWinner(int instanceId)
	{
		ConcurrentHashMap<Integer, PlayerEventInfo> instancePlayers = activePlayers.getOrDefault(instanceId, new ConcurrentHashMap<>());
		int alive = 0;
		PlayerEventInfo lastPlayer = null;
		for (PlayerEventInfo player : instancePlayers.values())
		{
			if (player.isOnline() && !player.isDead())
			{
				alive++;
				lastPlayer = player;
			}
		}
		if (alive <= 1)
		{
			if (alive == 1 && lastPlayer != null)
			{
				announce(instanceId, LanguageEngine.getMsg("br_winner", lastPlayer.getPlayersName()));
				getPlayerData(lastPlayer).raiseScore(10);
				setScoreStats(lastPlayer, getPlayerData(lastPlayer).getScore());
				CallbackManager.getInstance().playerScores(getEventType(), lastPlayer, 10);
			}
			else if (alive == 0)
			{
				announce(instanceId, LanguageEngine.getMsg("br_no_winner"));
			}

			// Give rewards to all players based on their final position
			giveRewards(instanceId);

			endInstance(instanceId, true, true, false);
		}
	}

	/**
	 * Give rewards to all players based on their final position - same as Deathmatch
	 */
	private void giveRewards(int instanceId)
	{
		try
		{
			// Get all players from this instance
			Set<PlayerEventInfo> allPlayers = getPlayers(instanceId);
			if (allPlayers == null || allPlayers.isEmpty())
			{
				return;
			}

			// Create map of players and their scores for ranking - same as Deathmatch
			Map<PlayerEventInfo, Integer> playerScores = new LinkedHashMap<>();

			// Add all players to the score map and give survival bonuses
			for (PlayerEventInfo player : allPlayers)
			{
				if (player.isOnline())
				{
					playerScores.put(player, getPlayerData(player).getScore());

					// Give survival time bonuses (end-game rewards)
					giveSurvivalBonuses(player);
				}
			}

			// Show reward summary to all players first
		showRewardSummary(allPlayers);

		// Use EventRewardSystem to handle all rewards - exactly like Deathmatch
			EventRewardSystem.getInstance().rewardPlayers(playerScores, getEventType(), 1, getInt("killsForReward"), getInt("afkHalfReward"), getInt("afkNoReward"));

			// Give special achievement bonuses
			giveAchievementBonuses(allPlayers);

			// Show final reward notification
			CallBack.getInstance().getOut().scheduleGeneral(new Runnable() {
				@Override
				public void run() {
					for (PlayerEventInfo player : allPlayers) {
						if (player.isOnline()) {
							player.sendMessage("All Battle Royale rewards have been distributed! Check your inventory.");
							player.sendMessage("Thank you for participating! Join again for more rewards!");
						}
					}
				}
			}, 3000); // 3 second delay after all rewards

			if (GabrielEventsLoader.detailedDebug)
			{
				print("BattleRoyale: Finished giving rewards to " + playerScores.size() + " players using EventRewardSystem");
			}
		}
		catch (Exception e)
		{
			print("BattleRoyale: Error giving rewards: " + e.getMessage());
			e.printStackTrace();
		}
	}

	/**
	 * Find a safe spawn location around the given center point using GeoEngine
	 */
	private Loc findSafeSpawnLocation(Loc centerLoc, int radius)
	{
		try
		{
			Random random = new Random();

			// Try multiple attempts to find a safe location
			for (int attempt = 0; attempt < 20; attempt++) // Increased attempts
			{
				double angle = random.nextDouble() * 2 * Math.PI;
				double distance = random.nextDouble() * radius;
				int x = (int) (centerLoc.getX() + distance * Math.cos(angle));
				int y = (int) (centerLoc.getY() + distance * Math.sin(angle));

				// Use GeoEngine to get proper Z coordinate
				int geoZ = GeoEngine.getInstance().getHeight(x, y, centerLoc.getZ());
				Loc testLoc = new Loc(x, y, geoZ);

				// Enhanced validation using GeoEngine
				if (isValidSpawnLocation(testLoc))
				{
					if (GabrielEventsLoader.detailedDebug)
					{
						print("BattleRoyale: Found safe spawn location at (" + x + ", " + y + ", " + geoZ + ") after " + (attempt + 1) + " attempts");
					}
					return testLoc;
				}
			}

			// If all attempts fail, try closer to center with smaller radius
			for (int attempt = 0; attempt < 10; attempt++)
			{
				double angle = random.nextDouble() * 2 * Math.PI;
				double distance = random.nextDouble() * (radius / 2); // Half radius
				int x = (int) (centerLoc.getX() + distance * Math.cos(angle));
				int y = (int) (centerLoc.getY() + distance * Math.sin(angle));

				int geoZ = GeoEngine.getInstance().getHeight(x, y, centerLoc.getZ());
				Loc testLoc = new Loc(x, y, geoZ);

				if (isValidSpawnLocation(testLoc))
				{
					if (GabrielEventsLoader.detailedDebug)
					{
						print("BattleRoyale: Found safe spawn location (closer to center) at (" + x + ", " + y + ", " + geoZ + ") after " + (attempt + 1) + " fallback attempts");
					}
					return testLoc;
				}
			}

			// Last resort: return center location with proper geo Z
			int centerGeoZ = GeoEngine.getInstance().getHeight(centerLoc.getX(), centerLoc.getY(), centerLoc.getZ());
			Loc fallbackLoc = new Loc(centerLoc.getX(), centerLoc.getY(), centerGeoZ);

			if (GabrielEventsLoader.detailedDebug)
			{
				print("BattleRoyale: Using fallback center location at (" + centerLoc.getX() + ", " + centerLoc.getY() + ", " + centerGeoZ + ")");
			}

			return fallbackLoc;
		}
		catch (Exception e)
		{
			print("BattleRoyale: Error finding safe spawn location: " + e.getMessage());
			// Emergency fallback to original location
			return centerLoc;
		}
	}

	/**
	 * Enhanced validation for spawn location using GeoEngine
	 */
	private boolean isValidSpawnLocation(Loc loc)
	{
		try
		{
			// Basic checks to avoid obviously bad locations
			if (loc.getX() < -200000 || loc.getX() > 200000) return false;
			if (loc.getY() < -200000 || loc.getY() > 200000) return false;
			if (loc.getZ() < -10000 || loc.getZ() > 10000) return false;

			// Use GeoEngine to get proper Z and check if location is valid
			int geoZ = GeoEngine.getInstance().getHeight(loc.getX(), loc.getY(), loc.getZ());

			// Check if the difference between requested Z and geo Z is reasonable
			int zDiff = Math.abs(loc.getZ() - geoZ);
			if (zDiff > 500) // If Z difference is too large, location might be invalid
			{
				return false;
			}

			// Check if location is accessible (not blocked by geo)
			// Note: Using null instance for basic geo validation
			if (!GeoEngine.getInstance().canMoveToTarget(loc.getX(), loc.getY(), geoZ, loc.getX(), loc.getY(), geoZ, null))
			{
				return false;
			}

			return true;
		}
		catch (Exception e)
		{
			if (GabrielEventsLoader.detailedDebug)
			{
				print("BattleRoyale: Error validating spawn location: " + e.getMessage());
			}
			return false;
		}
	}

	/**
	 * Show detailed reward summary to all players
	 */
	private void showRewardSummary(Set<PlayerEventInfo> allPlayers)
	{
		try
		{
			// Create sorted list for position display
			List<PlayerEventInfo> sortedPlayers = new ArrayList<>(allPlayers);
			sortedPlayers.sort((p1, p2) -> {
				// Alive players first
				boolean p1Alive = !p1.isDead();
				boolean p2Alive = !p2.isDead();
				if (p1Alive != p2Alive) {
					return p1Alive ? -1 : 1;
				}
				// Then by score
				int scoreCompare = Integer.compare(getPlayerData(p2).getScore(), getPlayerData(p1).getScore());
				if (scoreCompare != 0) {
					return scoreCompare;
				}
				// Then by kills
				return Integer.compare(getPlayerData(p2).getKills(), getPlayerData(p1).getKills());
			});

			// Show reward summary to each player
			int position = 1;
			for (PlayerEventInfo player : sortedPlayers)
			{
				if (player.isOnline())
				{
					showPlayerRewardSummary(player, position, sortedPlayers.size());
				}
				position++;
			}

		}
		catch (Exception e)
		{
			print("BattleRoyale: Error showing reward summary: " + e.getMessage());
		}
	}

	/**
	 * Show detailed reward breakdown to individual player
	 */
	private void showPlayerRewardSummary(PlayerEventInfo player, int position, int totalPlayers)
	{
		try
		{
			int kills = getPlayerData(player).getKills();
			int score = getPlayerData(player).getScore();
			boolean isWinner = position == 1 && !player.isDead();
			int eventRunTimeMinutes = getInt("runTime") / 60; // Convert seconds to minutes

			// Build reward summary message
			StringBuilder summary = new StringBuilder();
			summary.append("=== BATTLE ROYALE REWARDS ===\n");
			summary.append("Position: ").append(position).append("/").append(totalPlayers).append("\n");
			summary.append("Kills: ").append(kills).append(" | Score: ").append(score).append("\n");
			summary.append("Status: ").append(player.isDead() ? "Eliminated" : "Survivor").append("\n\n");

			// Position-based rewards
			summary.append("POSITION REWARDS:\n");
			if (position == 1) {
				summary.append("- 1st Place: 1,500 L-Coin + 8 GVE\n");
			} else if (position == 2) {
				summary.append("- 2nd Place: 800 L-Coin + 4 GVE\n");
			} else if (position == 3) {
				summary.append("- 3rd Place: 600 L-Coin + 3 GVE\n");
			} else if (position <= 10) {
				summary.append("- Top 10: 400 L-Coin + 2 GVE\n");
			} else if (position <= 20) {
				summary.append("- Top 20: 200 L-Coin + 1 GVE\n");
			} else {
				summary.append("- Participation: 100 L-Coin + 1 GVE\n");
			}

			// Performance rewards
			summary.append("\nPERFORMANCE REWARDS:\n");
			if (kills > 0) {
				summary.append("- Kills (").append(kills).append("x): ").append(kills * 50).append(" L-Coin + ").append(kills).append(" GVE (30% chance each)\n");
			}

			// Killing spree rewards
			if (kills >= 15) {
				summary.append("- 15 Kill Streak: 1,000 L-Coin + 5 GVE\n");
			}
			if (kills >= 10) {
				summary.append("- 10 Kill Streak: 500 L-Coin + 3 GVE\n");
			}
			if (kills >= 5) {
				summary.append("- 5 Kill Streak: 200 L-Coin + 1 GVE\n");
			}

			// Survival bonuses
			summary.append("\nSURVIVAL BONUSES:\n");
			if (!player.isDead()) {
				if (eventRunTimeMinutes >= 15) {
					summary.append("- 15+ Min Survival: ").append(getInt("survivalBonus15min")).append(" L-Coin\n");
				} else if (eventRunTimeMinutes >= 10) {
					summary.append("- 10+ Min Survival: ").append(getInt("survivalBonus10min")).append(" L-Coin\n");
				} else if (eventRunTimeMinutes >= 5) {
					summary.append("- 5+ Min Survival: ").append(getInt("survivalBonus5min")).append(" L-Coin\n");
				}
			}
			summary.append("- Participation: ").append(getInt("participationBonus")).append(" L-Coin\n");

			// Achievement bonuses
			summary.append("\nACHIEVEMENT BONUSES:\n");
			if (kills >= 10) {
				summary.append("- Rambo (10+ kills): ").append(getInt("ramboBonus")).append(" L-Coin + ").append(getInt("ramboGVE")).append(" GVE\n");
			}
			if (kills == 0 && isWinner) {
				summary.append("- Pacifist Winner: ").append(getInt("pacifistBonus")).append(" L-Coin + ").append(getInt("pacifistGVE")).append(" GVE\n");
			}
			if (kills >= 5 && kills < 10) {
				summary.append("- Skilled Fighter (5+ kills): ").append(getInt("skilledFighterBonus")).append(" L-Coin + ").append(getInt("skilledFighterGVE")).append(" GVE\n");
			}

			summary.append("\n=== REWARDS WILL BE GIVEN SHORTLY ===");

			// Send the summary to player
			player.sendMessage(summary.toString());

		}
		catch (Exception e)
		{
			print("BattleRoyale: Error showing player reward summary: " + e.getMessage());
		}
	}

	/**
	 * Give survival time bonuses to encourage participation
	 */
	private void giveSurvivalBonuses(PlayerEventInfo player)
	{
		try
		{
			// Calculate survival time based on event duration
			int eventRunTimeMinutes = getInt("runTime"); // Event duration in minutes

			if (!player.isDead())
			{
				// Player survived to the end - give full survival bonuses
				if (eventRunTimeMinutes >= 15)
				{
					int bonus = getInt("survivalBonus15min");
					player.sendMessage("SURVIVAL BONUS: You received " + bonus + " L-Coin for surviving 15+ minutes!");
					player.addItem(91663, bonus, true); // L-Coin
				}
				else if (eventRunTimeMinutes >= 10)
				{
					int bonus = getInt("survivalBonus10min");
					player.sendMessage("SURVIVAL BONUS: You received " + bonus + " L-Coin for surviving 10+ minutes!");
					player.addItem(91663, bonus, true); // L-Coin
				}
				else if (eventRunTimeMinutes >= 5)
				{
					int bonus = getInt("survivalBonus5min");
					player.sendMessage("SURVIVAL BONUS: You received " + bonus + " L-Coin for surviving 5+ minutes!");
					player.addItem(91663, bonus, true); // L-Coin
				}
			}
			else
			{
				// Player died - give partial survival bonus
				int bonus = getInt("participationBonus");
				player.sendMessage("PARTICIPATION BONUS: You received " + bonus + " L-Coin for participating!");
				player.addItem(91663, bonus, true); // L-Coin
			}

			// Participation bonus for everyone
			int participationBonus = getInt("participationBonus");
			player.sendMessage("PARTICIPATION BONUS: You received " + participationBonus + " L-Coin for joining Battle Royale!");
			player.addItem(91663, participationBonus, true); // L-Coin

		}
		catch (Exception e)
		{
			print("BattleRoyale: Error giving survival bonuses: " + e.getMessage());
		}
	}

	/**
	 * Give special achievement bonuses
	 */
	private void giveAchievementBonuses(Set<PlayerEventInfo> allPlayers)
	{
		try
		{
			// Find special achievements
			PlayerEventInfo mostKills = null;
			int maxKills = 0;

			for (PlayerEventInfo player : allPlayers)
			{
				if (player.isOnline())
				{
					int kills = getPlayerData(player).getKills();

					// Track most kills
					if (kills > maxKills)
					{
						maxKills = kills;
						mostKills = player;
					}

					// Rambo achievement (10+ kills)
					if (kills >= 10)
					{
						int lCoinBonus = getInt("ramboBonus");
						int gveBonus = getInt("ramboGVE");
						player.sendMessage("RAMBO ACHIEVEMENT: You received " + lCoinBonus + " L-Coin + " + gveBonus + " GVE for 10+ kills!");
						player.addItem(91663, lCoinBonus, true); // L-Coin
						// GVE Points are handled by EventRewardSystem with item ID -4
						if (player.getOwner() != null) {
							player.getOwner().addGveSkillPoints(gveBonus, false);
						}
					}

					// Pacifist winner (0 kills but won)
					if (kills == 0 && !player.isDead())
					{
						int lCoinBonus = getInt("pacifistBonus");
						int gveBonus = getInt("pacifistGVE");
						player.sendMessage("PACIFIST WINNER: You received " + lCoinBonus + " L-Coin + " + gveBonus + " GVE for winning without kills!");
						player.addItem(91663, lCoinBonus, true); // L-Coin
						// GVE Points are handled by EventRewardSystem with item ID -4
						if (player.getOwner() != null) {
							player.getOwner().addGveSkillPoints(gveBonus, false);
						}
					}

					// High kill achievements
					if (kills >= 5 && kills < 10)
					{
						int lCoinBonus = getInt("skilledFighterBonus");
						int gveBonus = getInt("skilledFighterGVE");
						player.sendMessage("SKILLED FIGHTER: You received " + lCoinBonus + " L-Coin + " + gveBonus + " GVE for 5+ kills!");
						player.addItem(91663, lCoinBonus, true); // L-Coin
						// GVE Points are handled by EventRewardSystem with item ID -4
						if (player.getOwner() != null) {
							player.getOwner().addGveSkillPoints(gveBonus, false);
						}
					}
				}
			}

			// Most kills achievement
			if (mostKills != null && maxKills > 0)
			{
				int lCoinBonus = getInt("mostKillsBonus");
				int gveBonus = getInt("mostKillsGVE");
				mostKills.sendMessage("MOST KILLS CHAMPION: You received " + lCoinBonus + " L-Coin + " + gveBonus + " GVE for most kills (" + maxKills + ")!");
				mostKills.addItem(91663, lCoinBonus, true); // L-Coin
				// GVE Points are handled by EventRewardSystem with item ID -4
				if (mostKills.getOwner() != null) {
					mostKills.getOwner().addGveSkillPoints(gveBonus, false);
				}
			}

		}
		catch (Exception e)
		{
			print("BattleRoyale: Error giving achievement bonuses: " + e.getMessage());
		}
	}


	@Override
	public boolean canSupport(PlayerEventInfo player, CharacterData target)
	{
		return player.getPlayersId() == target.getObjectId();
	}
	
	@Override
	public boolean canAttack(PlayerEventInfo player, CharacterData target)
	{
		// Check if target is another player in the event
		if (target.getEventInfo() != null && target.getEventInfo().getEvent() == player.getEvent())
		{
			// Get current event state for this instance
			int instanceId = player.getInstanceId();
			BREventInstance match = _matches.get(instanceId);

			// DEBUG: Log attack attempt details
			if (GabrielEventsLoader.detailedDebug)
			{
				print("BattleRoyale: canAttack() - Player: " + player.getOwner().getName() +
						", Target: " + target.getName() + ", InstanceId: " + instanceId);
				if (match != null)
				{
					print("BattleRoyale: canAttack() - Match found, nextState: " + match._nextState);
				}
				else
				{
					print("BattleRoyale: canAttack() - No match found for instance " + instanceId);
				}
			}

			if (match != null)
			{
				// Block PvP during lobby phase
				if (match._nextState == EventState.LOBBY)
				{
					return false; // Block the attack
				}
				return true; // Block the attack
			}
		}
		return target.getEventInfo() == null || target.getEventInfo().getEvent() == player.getEvent();
	}
	
	@Override
	public boolean onSay(PlayerEventInfo player, String text, int channel)
	{
		if (text.equals(".scheme"))
		{
			EventManager.getInstance().getHtmlManager().showSelectSchemeForEventWindow(player, "none", getEventType().getAltTitle());
			return false;
		}

		// Handle .stuck command for Battle Royale
		if (text.equals(".stuck") || text.equals(".unstuck"))
		{
			handlePlayerStuck(player);
			return false;
		}

		if (_antifeed)
		{
			player.sendMessage(LanguageEngine.getMsg("dm_cantChat"));
			return false;
		}
		return true;
	}

	/**
	 * Handle .stuck command for players who are stuck in Battle Royale
	 */
	public void handlePlayerStuck(PlayerEventInfo player)
	{
		try
		{
			// Check if player is in Battle Royale event
			if (!player.isInEvent() || player.getEvent() != this)
			{
				player.sendMessage("You can only use .stuck command during Battle Royale event!");
				return;
			}

			// Check cooldown (prevent spam) - reduced cooldown for better user experience
			long currentTime = System.currentTimeMillis();
			Long lastStuckTime = _playerStuckTimes.get(player.getPlayersId());
			if (lastStuckTime != null && (currentTime - lastStuckTime) < 5000) // 5 second cooldown (reduced from 30)
			{
				int remainingSeconds = (int) ((5000 - (currentTime - lastStuckTime)) / 1000);
				player.sendMessage("You must wait " + remainingSeconds + " seconds before using .stuck again!");
				return;
			}

			// Find a safe location to teleport with retry logic
			Loc safeLocation = findStuckSafeLocation(player);

			if (safeLocation != null)
			{
				// Double-check the location is actually safe before teleporting
				if (isValidSpawnLocation(safeLocation))
				{
					// Teleport player to safe location
					player.teleport(safeLocation, 0, true, player.getInstanceId());
					player.sendMessage("You have been teleported to a safe location!");

					// Update stuck time
					_playerStuckTimes.put(player.getPlayersId(), currentTime);

					if (GabrielEventsLoader.detailedDebug)
					{
						print("BattleRoyale: Player " + player.getPlayersName() + " used .stuck command, teleported to (" +
							  safeLocation.getX() + ", " + safeLocation.getY() + ", " + safeLocation.getZ() + ")");
					}
				}
				else
				{
					// Location validation failed, try emergency teleport to spawn
					Loc emergencyLoc = getEmergencySpawnLocation();
					if (emergencyLoc != null)
					{
						player.teleport(emergencyLoc, 0, true, player.getInstanceId());
						player.sendMessage("Emergency teleport to spawn area!");
						_playerStuckTimes.put(player.getPlayersId(), currentTime);

						if (GabrielEventsLoader.detailedDebug)
						{
							print("BattleRoyale: Emergency teleport for " + player.getPlayersName() + " to (" +
								  emergencyLoc.getX() + ", " + emergencyLoc.getY() + ", " + emergencyLoc.getZ() + ")");
						}
					}
					else
					{
						player.sendMessage("Unable to find a safe location. Please try again in a few seconds.");
					}
				}
			}
			else
			{
				player.sendMessage("Unable to find a safe location. Please try again in a few seconds.");
			}
		}
		catch (Exception e)
		{
			print("BattleRoyale: Error handling stuck command for " + player.getPlayersName() + ": " + e.getMessage());
			player.sendMessage("Error processing .stuck command. Please try again.");
		}
	}

	/**
	 * Get emergency spawn location as last resort for stuck players
	 */
	private Loc getEmergencySpawnLocation()
	{
		try
		{
			// Try to get first regular spawn as emergency location
			EventSpawn emergencySpawn = getSpawn(SpawnType.Regular, 0);
			if (emergencySpawn != null)
			{
				Loc spawnLoc = emergencySpawn.getLoc();
				int geoZ = GeoEngine.getInstance().getHeight(spawnLoc.getX(), spawnLoc.getY(), spawnLoc.getZ());
				Loc emergencyLoc = new Loc(spawnLoc.getX(), spawnLoc.getY(), geoZ);

				if (isValidSpawnLocation(emergencyLoc))
				{
					return emergencyLoc;
				}
			}

			// Fallback to hardcoded safe location with proper geo Z
			int defaultX = -19830;
			int defaultY = 144951;
			int defaultZ = GeoEngine.getInstance().getHeight(defaultX, defaultY, -3816);
			return new Loc(defaultX, defaultY, defaultZ);
		}
		catch (Exception e)
		{
			print("BattleRoyale: Error getting emergency spawn location: " + e.getMessage());
			return new Loc(-19830, 144951, -3816); // Absolute fallback
		}
	}

	/**
	 * Find a safe location for stuck players with multiple fallback options
	 */
	private Loc findStuckSafeLocation(PlayerEventInfo player)
	{
		try
		{
			if (GabrielEventsLoader.detailedDebug)
			{
				print("BattleRoyale: Finding stuck safe location for " + player.getPlayersName() + " at current position (" + player.getX() + ", " + player.getY() + ", " + player.getZ() + ")");
			}

			// First priority: Try to teleport near other alive players (best solution for stuck issues)
			Loc nearPlayerLoc = findLocationNearOtherPlayers(player);
			if (nearPlayerLoc != null)
			{
				if (GabrielEventsLoader.detailedDebug)
				{
					print("BattleRoyale: Found safe location near other players for " + player.getPlayersName());
				}
				return nearPlayerLoc;
			}

			// Second priority: Try locations around current position (larger radius for better unstuck)
			Loc currentLoc = new Loc(player.getX(), player.getY(), player.getZ());
			Loc nearbyLoc = findSafeSpawnLocation(currentLoc, 2000); // Larger radius around current position (800 units)
			if (nearbyLoc != null && !nearbyLoc.equals(currentLoc))
			{
				if (GabrielEventsLoader.detailedDebug)
				{
					print("BattleRoyale: Found safe location near current position for " + player.getPlayersName());
				}
				return nearbyLoc;
			}

			// Third priority: Try to use original spawn location
			Loc originalSpawn = _playerSpawnLocs.get(player.getPlayersId());
			if (originalSpawn != null)
			{
				// Validate original spawn is still safe
				if (isValidSpawnLocation(originalSpawn))
				{
					if (GabrielEventsLoader.detailedDebug)
					{
						print("BattleRoyale: Using original spawn location for " + player.getPlayersName());
					}
					return originalSpawn;
				}
				else
				{
					// Try around original spawn with larger radius
					Loc aroundOriginal = findSafeSpawnLocation(originalSpawn, 2000);
					if (aroundOriginal != null)
					{
						if (GabrielEventsLoader.detailedDebug)
						{
							print("BattleRoyale: Found safe location around original spawn for " + player.getPlayersName());
						}
						return aroundOriginal;
					}
				}
			}

			// Third priority: Use Regular spawn locations from event configuration
			List<EventSpawn> regularSpawns = new ArrayList<>();
			int index = 0;
			while (true)
			{
				EventSpawn spawn = getSpawn(SpawnType.Regular, index);
				if (spawn == null)
				{
					break;
				}
				regularSpawns.add(spawn);
				index++;
			}

			if (!regularSpawns.isEmpty())
			{
				Random random = new Random();
				// Try multiple spawn points if first one fails
				for (int i = 0; i < Math.min(3, regularSpawns.size()); i++)
				{
					EventSpawn selectedSpawn = regularSpawns.get(random.nextInt(regularSpawns.size()));
					Loc spawnLoc = selectedSpawn.getLoc();

					// Use a larger radius for stuck teleport to ensure player gets unstuck
					int stuckRadius = Math.max(selectedSpawn.getRadius() / 2, 2000); // Larger radius, minimum 800
					Loc safeSpawn = findSafeSpawnLocation(spawnLoc, stuckRadius);
					if (safeSpawn != null)
					{
						if (GabrielEventsLoader.detailedDebug)
						{
							print("BattleRoyale: Found safe location at regular spawn for " + player.getPlayersName());
						}
						return safeSpawn;
					}
				}
			}

			// Fourth fallback: find a random safe location around circle center
			if (!circleCenters.isEmpty())
			{
				Random random = new Random();
				for (int i = 0; i < Math.min(2, circleCenters.size()); i++)
				{
					Loc randomCenter = circleCenters.get(random.nextInt(circleCenters.size()));
					Loc centerSafe = findSafeSpawnLocation(randomCenter, 2000); // Smaller radius
					if (centerSafe != null)
					{
						if (GabrielEventsLoader.detailedDebug)
						{
							print("BattleRoyale: Found safe location around circle center for " + player.getPlayersName());
						}
						return centerSafe;
					}
				}
			}

			// Last resort: use a default safe location in the map with proper geo Z
			int defaultX = -19830;
			int defaultY = 144951;
			int defaultZ = GeoEngine.getInstance().getHeight(defaultX, defaultY, -3816);
			Loc defaultLoc = new Loc(defaultX, defaultY, defaultZ);

			if (GabrielEventsLoader.detailedDebug)
			{
				print("BattleRoyale: Using default safe location for " + player.getPlayersName() + " at (" + defaultX + ", " + defaultY + ", " + defaultZ + ")");
			}

			return defaultLoc;
		}
		catch (Exception e)
		{
			print("BattleRoyale: Error finding stuck safe location for " + player.getPlayersName() + ": " + e.getMessage());
			return null;
		}
	}

	/**
	 * Find a safe location near other alive players for stuck command
	 */
	private Loc findLocationNearOtherPlayers(PlayerEventInfo stuckPlayer)
	{
		try
		{
			ConcurrentHashMap<Integer, PlayerEventInfo> players = activePlayers.getOrDefault(stuckPlayer.getInstanceId(), new ConcurrentHashMap<>());
			List<PlayerEventInfo> alivePlayers = new ArrayList<>();

			// Collect all alive players except the stuck player
			for (PlayerEventInfo player : players.values())
			{
				if (player != null && player.isOnline() &&
					player.getPlayersId() != stuckPlayer.getPlayersId() &&
					player.getInstanceId() == stuckPlayer.getInstanceId() &&
					!player.isDead())
				{
					alivePlayers.add(player);
				}
			}

			if (alivePlayers.isEmpty())
			{
				if (GabrielEventsLoader.detailedDebug)
				{
					print("BattleRoyale: No other alive players found for stuck teleport");
				}
				return null;
			}

			// Sort players by distance from stuck player (closest first)
			alivePlayers.sort((p1, p2) -> {
				double dist1 = Math.sqrt(Math.pow(p1.getX() - stuckPlayer.getX(), 2) + Math.pow(p1.getY() - stuckPlayer.getY(), 2));
				double dist2 = Math.sqrt(Math.pow(p2.getX() - stuckPlayer.getX(), 2) + Math.pow(p2.getY() - stuckPlayer.getY(), 2));
				return Double.compare(dist1, dist2);
			});

			// Try to find safe location near the closest players
			for (int i = 0; i < Math.min(3, alivePlayers.size()); i++) // Try up to 3 closest players
			{
				PlayerEventInfo targetPlayer = alivePlayers.get(i);
				Loc targetLoc = new Loc(targetPlayer.getX(), targetPlayer.getY(), targetPlayer.getZ());

				// Find safe location around target player (500-800 units away to avoid being too close)
				Loc safeLoc = findSafeLocationAroundTarget(targetLoc, 500, 800);
				if (safeLoc != null)
				{
					if (GabrielEventsLoader.detailedDebug)
					{
						print("BattleRoyale: Found safe location near player " + targetPlayer.getPlayersName() +
							  " at (" + safeLoc.getX() + ", " + safeLoc.getY() + ", " + safeLoc.getZ() + ")");
					}
					return safeLoc;
				}
			}

			if (GabrielEventsLoader.detailedDebug)
			{
				print("BattleRoyale: Could not find safe location near any other players");
			}
			return null;
		}
		catch (Exception e)
		{
			print("BattleRoyale: Error finding location near other players: " + e.getMessage());
			return null;
		}
	}

	/**
	 * Find safe location around target with specified distance range
	 */
	private Loc findSafeLocationAroundTarget(Loc targetLoc, int minDistance, int maxDistance)
	{
		try
		{
			Random random = new Random();

			// Try multiple attempts to find a good location
			for (int attempt = 0; attempt < 15; attempt++)
			{
				double angle = random.nextDouble() * 2 * Math.PI;
				double distance = minDistance + random.nextDouble() * (maxDistance - minDistance);

				int x = (int) (targetLoc.getX() + distance * Math.cos(angle));
				int y = (int) (targetLoc.getY() + distance * Math.sin(angle));

				// Use GeoEngine to get proper Z coordinate
				int geoZ = GeoEngine.getInstance().getHeight(x, y, targetLoc.getZ());
				Loc testLoc = new Loc(x, y, geoZ);

				// Validate location
				if (isValidSpawnLocation(testLoc))
				{
					if (GabrielEventsLoader.detailedDebug)
					{
						print("BattleRoyale: Found safe location around target after " + (attempt + 1) + " attempts");
					}
					return testLoc;
				}
			}

			return null;
		}
		catch (Exception e)
		{
			print("BattleRoyale: Error finding safe location around target: " + e.getMessage());
			return null;
		}
	}

	@Override
	public boolean canInviteToParty(PlayerEventInfo player, PlayerEventInfo target)
	{
		return false;
	}
	
	@Override
	public boolean checkIfEventCanContinue(int instanceId, PlayerEventInfo disconnectedPlayer)
	{
		int alive = 0;
		for (PlayerEventInfo pi : getPlayers(instanceId))
		{
			if (pi != null && pi.isOnline() && !pi.isDead())
			{
				alive++;
			}
		}
		return alive >= 2;
	}
	
	@Override
	public EventPlayerData createPlayerData(PlayerEventInfo player)
	{
		return new PvPEventPlayerData(player, this, new GlobalStatsModel(getEventType()));
	}
	
	@Override
	public PvPEventPlayerData getPlayerData(PlayerEventInfo player)
	{
		return (PvPEventPlayerData) player.getEventData();
	}

	/**
	 * Get BRData for instance (helper method for type safety)
	 */
	protected BRData getBRData(int instanceId)
	{
		return (BRData) getEventData(instanceId);
	}

	/**
	 * Throttled broadcast to prevent client crashes
	 */
	private void safeBroadcastUserInfo(PlayerEventInfo player)
	{
		if (player == null || !player.isOnline()) return;

		long currentTime = System.currentTimeMillis();
		Long lastBroadcast = _lastBroadcastTimes.get(player.getPlayersId());

		if (lastBroadcast == null || (currentTime - lastBroadcast) >= BROADCAST_THROTTLE)
		{
			player.broadcastUserInfo();
			_lastBroadcastTimes.put(player.getPlayersId(), currentTime);
		}
	}

	/**
	 * Throttled title broadcast to prevent sync issues
	 */
	private void safeBroadcastTitleInfo(PlayerEventInfo player)
	{
		if (player == null || !player.isOnline()) return;

		long currentTime = System.currentTimeMillis();
		Long lastTitleBroadcast = _lastTitleBroadcastTimes.get(player.getPlayersId());

		if (lastTitleBroadcast == null || (currentTime - lastTitleBroadcast) >= TITLE_BROADCAST_THROTTLE)
		{
			player.broadcastTitleInfo();
			_lastTitleBroadcastTimes.put(player.getPlayersId(), currentTime);
		}
	}

	/**
	 * Force refresh titles for all players to ensure point sync
	 */
	private void refreshAllPlayerTitles(int instanceId)
	{
		ConcurrentHashMap<Integer, PlayerEventInfo> players = activePlayers.get(instanceId);
		if (players == null) return;

		for (PlayerEventInfo player : players.values())
		{
			if (player != null && player.isOnline() && player.isTitleUpdated())
			{
				try
				{
					String newTitle = getTitle(player);
					player.setTitle(newTitle, true);
					safeBroadcastTitleInfo(player);
				}
				catch (Exception e)
				{
					// Ignore individual errors
				}
			}
		}
	}

	/**
	 * Safe packet sending - only to players in event
	 */
	private void safePacketSend(PlayerEventInfo player, ServerPacket packet)
	{
		if (player == null || !player.isOnline() || !player.isInEvent()) return;

		try
		{
			player.getOwner().sendPacket(packet);
		}
		catch (Exception e)
		{
			if (GabrielEventsLoader.detailedDebug)
			{
				print("BattleRoyale: Error sending packet to " + player.getPlayersName() + ": " + e.getMessage());
			}
		}
	}
	
	@Override
	public synchronized void clearEvent(int instanceId)
	{
		if (GabrielEventsLoader.detailedDebug)
		{
			print("BattleRoyale: called CLEAREVENT for instance " + instanceId);
		}
		try
		{
			// Stop any ongoing tasks in event instances
			if (_matches != null)
			{
				for (BREventInstance match : _matches.values())
				{
					if (instanceId == 0 || instanceId == match.getInstance().getId())
					{
						match.abort(); // Cancel all scheduled tasks in the instance
						match._data.stopCircleShrink();
						// handleWeapons(match.getInstance().getId(), false);
						// Clear circles for all players before restoring equipment
						for (PlayerEventInfo player : getPlayers(match.getInstance().getId()))
						{
							clearAllCirclesForPlayer(player);
						}

						// Restore equipment for all players (including offline players)
						for (PlayerEventInfo player : getPlayers(match.getInstance().getId()))
						{
							Map<Integer, ItemInstance> equippedItems = match._playerEquipmentBeforeEvent.get(player);
							if (equippedItems != null)
							{
								if (player.isOnline() && player.getOwner() != null)
								{
									// Restore equipment for online players
									for (Map.Entry<Integer, ItemInstance> itemEntry : equippedItems.entrySet())
									{
										int slot = itemEntry.getKey();
										ItemInstance itemToEquip = itemEntry.getValue();

										// Skip null items (empty slots)
										if (itemToEquip == null)
										{
											if (GabrielEventsLoader.detailedDebug)
											{
												print("BattleRoyale: Skipped empty slot " + slot + " (" + getSlotName(slot) + ") for player " + player.getPlayersName());
											}
											continue;
										}

										try
										{
											// Find the exact item by ObjectId - this should be unique
											ItemInstance currentItem = player.getOwner().getInventory().getItemByObjectId(itemToEquip.getObjectId());
											if (currentItem != null)
											{
												ItemInstance itemInSlot = player.getOwner().getInventory().getPaperdollItem(slot);
												if (itemInSlot != null && itemInSlot.getObjectId() != currentItem.getObjectId())
												{
													player.getOwner().getInventory().unEquipItemInSlot(slot);
													final InventoryUpdate iu = new InventoryUpdate();
													iu.addModifiedItem(itemInSlot);
													player.getOwner().sendInventoryUpdate(iu);
												}
												if (!currentItem.isEquipped())
												{
													// Check if this is a special slot that needs direct paperdoll setting
													boolean isSpecialSlot = (slot >= Inventory.PAPERDOLL_DECO1 && slot <= Inventory.PAPERDOLL_DECO6) || // Talisman slots 1-6
																		   (slot >= Inventory.PAPERDOLL_BROOCH_JEWEL1 && slot <= Inventory.PAPERDOLL_BROOCH_JEWEL6) || // Brooch jewel slots 1-6
																		   (slot >= Inventory.PAPERDOLL_AGATHION1 && slot <= Inventory.PAPERDOLL_AGATHION5) || // Agathion slots 1-5
																		   (slot >= Inventory.PAPERDOLL_ARTIFACT1 && slot <= Inventory.PAPERDOLL_ARTIFACT21) || // Artifact slots
																		   (slot == Inventory.PAPERDOLL_RFINGER || slot == Inventory.PAPERDOLL_LFINGER) || // Ring slots
																		   (slot == Inventory.PAPERDOLL_REAR || slot == Inventory.PAPERDOLL_LEAR) || // Earring slots
																		   (slot == Inventory.PAPERDOLL_BROOCH) || // Brooch slot
																		   (slot == Inventory.PAPERDOLL_RBRACELET || slot == Inventory.PAPERDOLL_LBRACELET); // Bracelet slots

													if (isSpecialSlot)
													{
														// Force equip to specific slot for special items
														try
														{
															player.getOwner().getInventory().setPaperdollItem(slot, currentItem);
															currentItem.setItemLocation(ItemLocation.PAPERDOLL, slot);
															currentItem.setLastChange(ItemInstance.MODIFIED);

															final InventoryUpdate iu2 = new InventoryUpdate();
															iu2.addModifiedItem(currentItem);
															player.getOwner().sendInventoryUpdate(iu2);

															if (GabrielEventsLoader.detailedDebug)
															{
																print("BattleRoyale: Force equipped special slot item " + currentItem.getId() + " to slot " + slot + " (" + getSlotName(slot) + ") for player " + player.getPlayersName());
															}
														}
														catch (Exception e)
														{
															print("BattleRoyale: Failed to force equip special slot item " + currentItem.getId() + " to slot " + slot + " (" + getSlotName(slot) + ") for player " + player.getPlayersName() + ": " + e.getMessage());
														}
													}
													else
													{
														// Use normal equip for regular slots
														try
														{
															player.getOwner().getInventory().equipItem(currentItem);
															if (GabrielEventsLoader.detailedDebug)
															{
																print("BattleRoyale: Normal equipped item " + currentItem.getId() + " to slot " + slot + " (" + getSlotName(slot) + ") for player " + player.getPlayersName());
															}
														}
														catch (Exception e)
														{
															print("BattleRoyale: Failed to normal equip item " + currentItem.getId() + " to slot " + slot + " (" + getSlotName(slot) + ") for player " + player.getPlayersName() + ": " + e.getMessage());
														}
													}
												}
											}
											else
											{
												if (GabrielEventsLoader.detailedDebug)
												{
													print("BattleRoyale: Could not find item with ObjectId " + itemToEquip.getObjectId() + " for player " + player.getPlayersName() + " in slot " + slot);
												}
											}
										}
										catch (Exception e)
										{
											print("BattleRoyale: Failed to re-equip item in slot " + slot + " for player " + player.getPlayersName() + " in instance " + match.getInstance().getId() + ": " + e.getMessage());
										}
									}
									player.getOwner().sendItemList();
									player.getOwner().broadcastUserInfo();

									// Additional verification for jewelry slots
									verifyJewelrySlots(player, equippedItems);

									// Force check all special slots that might not have been saved
									forceRestoreSpecialSlots(player);

									if (GabrielEventsLoader.detailedDebug)
									{
										print("BattleRoyale: Restored " + equippedItems.size() + " items for online player " + player.getPlayersName());
									}
								}
								else
								{
									if (GabrielEventsLoader.detailedDebug)
									{
										print("BattleRoyale: Player " + player.getPlayersName() + " is offline, equipment will be restored when they reconnect");
									}
								}
							}
							else
							{
								if (GabrielEventsLoader.detailedDebug)
								{
									print("BattleRoyale: No equipment data found for player " + player.getPlayersName());
								}
							}
						}
						match._playerEquipmentBeforeEvent.clear();
					}
				}
			}
		}
		catch (Exception e)
		{
			e.printStackTrace();
		}
		// Now process players: remove items, teleport, and ensure circle is hidden
		for (PlayerEventInfo player : getPlayers(instanceId))
		{
			removeEventItems(player);
			if (player.isOnline())
			{
				PlayerInstance playerInstance = player.getOwner();
				if (playerInstance != null)
				{
					if (player.hasAntifeedProtection())
					{
						player.stopAntifeedProtection(false);
					}
					if (player.isParalyzed())
					{
						player.setIsParalyzed(false);
					}
					if (player.isImmobilized())
					{
						player.unroot();
					}
					if (!player.isGM())
					{
						player.setIsInvul(false);
					}
					player.removeRadarAllMarkers();
					// Send hide circle packets before teleporting
					for (int stage = 1; stage <= stages.size(); stage++)
					{
						for (BRCircleColor color : BRCircleColor.values())
						{
							int circleId = -(110000 + stage * 10 + (color == BRCircleColor.WHITE ? 1 : 2));
							String circleName = "battleRoyaleCircle_" + circleId;
							ExServerPrimitive hidePacket = new ExServerPrimitive(circleName, 0, 0, 0);
							playerInstance.sendPacket(hidePacket);
						}
					}
					// Clear all circles for this player before teleporting
					clearAllCirclesForPlayer(player);

					// Teleport the player to instance 0
					player.setInstanceId(0);
					player.teleport(player.getOrigLoc(), 0, true, 0);
					player.sendMessage(LanguageEngine.getMsg("event_teleportBack"));

					// Additional circle clearing after teleport with delay to ensure client processes it
					CallBack.getInstance().getOut().scheduleGeneral(() -> {
						if (player.isOnline() && player.getOwner() != null)
						{
							clearAllCirclesForPlayer(player);
							if (GabrielEventsLoader.detailedDebug)
							{
								print("BattleRoyale: Cleared circles for " + player.getPlayersName() + " after teleport");
							}
						}
					}, 1000); // 1 second delay to ensure teleport is complete
					if (_removeBuffsOnEnd)
					{
						player.removeBuffs();
					}
					player.restoreData();
					if (player.getParty() != null)
					{
						PartyData party = player.getParty();
						party.removePartyMember(player);
					}
					if (playerInstance.getClan() != null)
					{
						for (Skill clanSkill : playerInstance.getClan().getAllSkills())
						{
							playerInstance.enableSkill(clanSkill);
						}
					}
					playerInstance.sendSkillList();
					playerInstance.broadcastUserInfo();

					// Auto-arrange inventory after event cleanup with longer delay to prevent client issues
					CallBack.getInstance().getOut().scheduleGeneral(() -> {
						if (player.isOnline() && player.getOwner() != null)
						{
							try
							{
								// Additional delay to ensure client is stable
								Thread.sleep(1500);
								autoArrangeInventory(player);
								//player.sendMessage("Hành trang của bạn đã được tự động sắp xếp lại sau event!");
							}
							catch (Exception e)
							{
								print("BattleRoyale: Error during inventory auto-arrange: " + e.getMessage());
							}
						}
					}, 6000); // 6 second delay to ensure all cleanup is complete and client is stable
				}
			}
		}
		clearPlayers(true, instanceId);
		if (instanceId != 0 && _matches != null)
		{
			_matches.remove(instanceId);
		}
		activePlayers.remove(instanceId);
		if (instanceId == 0)
		{
			_matches.clear();
			activePlayers.clear();
		}
	}
	
	@Override
	public synchronized void clearEvent()
	{
		clearEvent(0);
		if (_matches != null)
		{
			_matches.clear();
		}
		// Clear all active players
		activePlayers.clear();
	}
	
	public void handleWeapons(int instanceId, boolean equip)
	{
		for (PlayerEventInfo player : getPlayers(instanceId))
		{
			if (equip)
			{
				ItemData wpn = player.getPaperdollItem(CallBack.getInstance().getValues().PAPERDOLL_RHAND());
				if (wpn != null)
				{
					player.unEquipItemInBodySlotAndRecord(CallBack.getInstance().getValues().SLOT_R_HAND());
				}
				wpn = player.getPaperdollItem(CallBack.getInstance().getValues().PAPERDOLL_LHAND());
				if (wpn != null)
				{
					player.unEquipItemInBodySlotAndRecord(CallBack.getInstance().getValues().SLOT_L_HAND());
				}
				ItemData weapon = player.addItem(_weaponItemId, 1, false);
				if (weapon != null)
				{
					ItemInstance weaponInstance = weapon.getOwner();
					if (weaponInstance != null)
					{
						weaponInstance.setCustomType1(9999); // Mark as event item
					}
					player.equipItem(weapon);
				}
				if (_ammoItemId > 0)
				{
					ItemData ammo = player.addItem(_ammoItemId, 400, false);
					if (ammo != null)
					{
						ItemInstance ammoInstance = ammo.getOwner();
						if (ammoInstance != null)
						{
							ammoInstance.setCustomType1(9999); // Mark as event item
						}
					}
				}
			}
		}
	}
	
	@Override
	public String getEstimatedTimeLeft()
	{
		if (_matches == null)
		{
			return "Starting";
		}
		for (BREventInstance match : _matches.values())
		{
			if (match.isActive())
			{
				return match.getClock().getTime();
			}
		}
		return "N/A";
	}
	
	@Override
	public int getTeamsCount()
	{
		return 1;
	}
	
	@Override
	public String getMissingSpawns(EventMap map)
	{
		if (!map.checkForSpawns(SpawnType.CircleCenter, -1, 1))
		{
			return addMissingSpawn(SpawnType.CircleCenter, 0, 1);
		}
		return "";
	}
	
	@Override
	public String addExtraEventInfoCb(int instance)
	{
		int alive = 0;
		for (PlayerEventInfo player : getPlayers(instance))
		{
			if (!player.isDead())
			{
				alive++;
			}
		}
		String status = "<font color=ac9887>Players Alive: </font><font color=7f7f7f>" + alive + "</font>";
		return "<table width=510 bgcolor=3E3E3E><tr><td width=510 align=center>" + status + "</td></tr></table>";
	}
	
	@Override
	public String getHtmlDescription()
	{
		if (_htmlDescription == null)
		{
			EventDescription desc = EventDescriptionSystem.getInstance().getDescription(getEventType());
			if (desc != null)
			{
				_htmlDescription = desc.getDescription(getConfigs());
			}
			else
			{
				_htmlDescription = "This is a free-for-all Battle Royale event. Survive as the last player standing in a shrinking safe zone. ";
				_htmlDescription += "Kill opponents to gain score. The safe zone shrinks in multiple stages, and players outside take increasing damage over time. ";
				_htmlDescription += "Airdrops containing rare items will spawn periodically, marked on your map with a visual effect. They will despawn after " + _airdropDespawnTime + " seconds if not claimed. ";
				_htmlDescription += "When the circle reaches its final size, you have 60 seconds to eliminate others, or the event ends in a draw.";
				if (_antifeed)
				{
					_htmlDescription += "Anti-feed protection changes player appearance and disables chat. ";
				}
				if (getInt("killsForReward") > 0)
				{
					_htmlDescription += "You need at least " + getInt("killsForReward") + " kills to receive a reward.";
				}
			}
		}
		return _htmlDescription;
	}
	
	@Override
	public AbstractEventInstance getMatch(int instanceId)
	{
		return _matches.get(instanceId);
	}
	
	@Override
	public BRData createEventData(int instance)
	{
		return new BRData(instance, this);
	}
	
	@Override
	public BREventInstance createEventInstance(InstanceData instance)
	{
		return new BREventInstance(instance);
	}
	
	@Override
	public BRData getEventData(int instance)
	{
		BREventInstance match = _matches.get(instance);
		if (match == null)
		{
			if (GabrielEventsLoader.detailedDebug)
			{
				print("BattleRoyale: No match found for instance " + instance + " in getEventData");
			}
			return null;
		}
		return _matches.get(instance)._data;
	}
	
	public MainEventManager getManager()
	{
		return _manager;
	}
	
	protected enum EventState
	{
		LOBBY,
		START,
		FIGHT,
		END,
		INACTIVE;
	}
	
	protected class BREventInstance extends AbstractEventInstance
	{
		protected EventState											_nextState;
		protected BRData												_data;
		private final Map<PlayerEventInfo, Map<Integer, ItemInstance>>	_playerEquipmentBeforeEvent	= new HashMap<>();
		private ScheduledFuture<?>										_lobbyTask;
		
		public BREventInstance(InstanceData instance)
		{
			super(instance);
			_nextState = EventState.LOBBY;
			_data = createEventData(_instance.getId());
		}
		
		protected void setNextState(EventState state)
		{
			_nextState = state;
		}
		
		@Override
		public boolean isActive()
		{
			return _nextState != EventState.INACTIVE;
		}
		
		@Override
		public void run()
		{
			try
			{
				switch (_nextState)
				{
					case LOBBY:
					{
						// Teleport người chơi vào khu vực lobby
						Loc lobbyLoc = new Loc(18078, 145925, -3020); // Tọa độ lobby GM Misc
						for (PlayerEventInfo player : getPlayers(_instance.getId()))
						{
							if (player.isOnline())
							{
								player.teleport(lobbyLoc, 0, true, _instance.getId());
								player.setInstanceId(_instance.getId());
								// Kiểm tra giá trị trả về từ LanguageEngine.getMsg()
								String lobbyMessage = LanguageEngine.getMsg("br_lobby_message", "20");
								if (lobbyMessage == null)
								{
									lobbyMessage = "Battle Royale Lobby: Match starts in 20 seconds!"; // Thông báo mặc định
									if (GabrielEventsLoader.detailedDebug)
									{
										print("BattleRoyale: Warning: br_lobby_message not found in LanguageEngine, using default message.");
									}
								}
								player.sendMessage(lobbyMessage);
							}
						}
						// Tháo trang bị và xử lý vật phẩm ngay trong lobby
						for (PlayerEventInfo player : getPlayers(_instance.getId()))
						{
							if (player.isOnline())
							{
								// Tháo tất cả trang bị và lưu trữ
								Map<Integer, ItemInstance> equippedItems = new HashMap<>();
								Set<Integer> playerItemIds = _data.getPlayerItemIds(player);
								for (ItemInstance inventoryItem : player.getOwner().getInventory().getItems())
								{
									if (inventoryItem != null)
									{
										playerItemIds.add(inventoryItem.getId());
									}
								}

								// FIRST: Save ALL slots before unequipping anything to avoid listener interference
								for (int slot = 0; slot < Inventory.PAPERDOLL_TOTALSLOTS; slot++)
								{
									ItemInstance equippedItem = player.getOwner().getInventory().getPaperdollItem(slot);
									equippedItems.put(slot, equippedItem); // Save current state (null if empty)

									if (equippedItem != null)
									{
										playerItemIds.add(equippedItem.getId());
										if (GabrielEventsLoader.detailedDebug)
										{
											print("BattleRoyale: Saving slot " + slot + " (" + getSlotName(slot) + ") - Item ID: " + equippedItem.getId() + ", ObjectId: " + equippedItem.getObjectId() + " for player " + player.getPlayersName());
										}
									}
								}

								// SECOND: Unequip items in reverse dependency order to prevent listener cascade
								// Unequip dependent items first (JEWEL, TALISMAN, AGATHION, ARTIFACT)
								int[] dependentSlots = {
									// JEWEL slots (depend on BROOCH)
									Inventory.PAPERDOLL_BROOCH_JEWEL1, Inventory.PAPERDOLL_BROOCH_JEWEL2,
									Inventory.PAPERDOLL_BROOCH_JEWEL3, Inventory.PAPERDOLL_BROOCH_JEWEL4,
									Inventory.PAPERDOLL_BROOCH_JEWEL5, Inventory.PAPERDOLL_BROOCH_JEWEL6,
									// TALISMAN slots (depend on R_BRACELET)
									Inventory.PAPERDOLL_DECO1, Inventory.PAPERDOLL_DECO2, Inventory.PAPERDOLL_DECO3,
									Inventory.PAPERDOLL_DECO4, Inventory.PAPERDOLL_DECO5, Inventory.PAPERDOLL_DECO6,
									// AGATHION slots (depend on L_BRACELET)
									Inventory.PAPERDOLL_AGATHION1, Inventory.PAPERDOLL_AGATHION2, Inventory.PAPERDOLL_AGATHION3,
									Inventory.PAPERDOLL_AGATHION4, Inventory.PAPERDOLL_AGATHION5,
									// ARTIFACT slots (depend on ARTIFACT_BOOK)
									Inventory.PAPERDOLL_ARTIFACT1, Inventory.PAPERDOLL_ARTIFACT2, Inventory.PAPERDOLL_ARTIFACT3,
									Inventory.PAPERDOLL_ARTIFACT4, Inventory.PAPERDOLL_ARTIFACT5, Inventory.PAPERDOLL_ARTIFACT6,
									Inventory.PAPERDOLL_ARTIFACT7, Inventory.PAPERDOLL_ARTIFACT8, Inventory.PAPERDOLL_ARTIFACT9,
									Inventory.PAPERDOLL_ARTIFACT10, Inventory.PAPERDOLL_ARTIFACT11, Inventory.PAPERDOLL_ARTIFACT12,
									Inventory.PAPERDOLL_ARTIFACT13, Inventory.PAPERDOLL_ARTIFACT14, Inventory.PAPERDOLL_ARTIFACT15,
									Inventory.PAPERDOLL_ARTIFACT16, Inventory.PAPERDOLL_ARTIFACT17, Inventory.PAPERDOLL_ARTIFACT18,
									Inventory.PAPERDOLL_ARTIFACT19, Inventory.PAPERDOLL_ARTIFACT20, Inventory.PAPERDOLL_ARTIFACT21
								};

								// Unequip dependent slots first
								for (int slot : dependentSlots)
								{
									ItemInstance equippedItem = player.getOwner().getInventory().getPaperdollItem(slot);
									if (equippedItem != null)
									{
										try
										{
											player.getOwner().getInventory().unEquipItemInSlot(slot);
											final InventoryUpdate iu = new InventoryUpdate();
											iu.addModifiedItem(equippedItem);
											player.getOwner().sendInventoryUpdate(iu);
											if (GabrielEventsLoader.detailedDebug)
											{
												print("BattleRoyale: Unequipped dependent item in slot " + slot + " (" + getSlotName(slot) + ") for player " + player.getPlayersName());
											}
										}
										catch (Exception e)
										{
											print("BattleRoyale: Failed to unequip dependent item in slot " + slot + " for player " + player.getPlayersName() + ": " + e.getMessage());
										}
									}
								}

								// Then unequip all remaining slots
								for (int slot = 0; slot < Inventory.PAPERDOLL_TOTALSLOTS; slot++)
								{
									ItemInstance equippedItem = player.getOwner().getInventory().getPaperdollItem(slot);
									if (equippedItem != null)
									{
										try
										{
											player.getOwner().getInventory().unEquipItemInSlot(slot);
											final InventoryUpdate iu = new InventoryUpdate();
											iu.addModifiedItem(equippedItem);
											player.getOwner().sendInventoryUpdate(iu);
											if (GabrielEventsLoader.detailedDebug)
											{
												print("BattleRoyale: Unequipped item in slot " + slot + " (" + getSlotName(slot) + ") for player " + player.getPlayersName());
											}
										}
										catch (Exception e)
										{
											print("BattleRoyale: Failed to unequip item in slot " + slot + " for player " + player.getPlayersName() + ": " + e.getMessage());
										}
									}
								}
								// Save equipment state (including empty slots)
								_playerEquipmentBeforeEvent.put(player, equippedItems);
								if (GabrielEventsLoader.detailedDebug)
								{
									int itemCount = 0;
									for (ItemInstance item : equippedItems.values())
									{
										if (item != null) itemCount++;
									}
									print("BattleRoyale: Saved " + itemCount + " equipped items (" + equippedItems.size() + " total slots) for player " + player.getPlayersName());
									for (Map.Entry<Integer, ItemInstance> entry : equippedItems.entrySet())
									{
										if (entry.getValue() != null)
										{
											print("  - Slot " + entry.getKey() + " (" + getSlotName(entry.getKey()) + "): Item " + entry.getValue().getId());
										}
									}
								}
								player.getOwner().sendItemList();
								if (GabrielEventsLoader.detailedDebug)
								{
									print("BattleRoyale: Initialized item IDs for player " + player.getPlayersName() + ", total unique items: " + playerItemIds.size());
								}
								// Cấp item 76000 và điểm sự kiện
								getPlayerData(player).setEventPoints(2);
								int handlerItemId = getInt("battleRoyalItemHandlerId");
								ItemData handlerItem = player.addItem(handlerItemId, 1, true);
								if (handlerItem != null)
								{
									ItemInstance itemInstance = handlerItem.getOwner();
									if (itemInstance != null)
									{
										itemInstance.setCustomType1(9999);
										if (GabrielEventsLoader.detailedDebug)
										{
											print("BattleRoyale: Gave BattleRoyalItemHandler (ID: " + handlerItemId + ") to player " + player.getPlayersName() + " in lobby");
										}
									}
								}
							}
						}
						// Lên lịch chuyển sang trạng thái START sau 20 giây
						_lobbyTask = CallBack.getInstance().getOut().scheduleGeneral(() ->
						{
							setNextState(EventState.START);
							scheduleNextTask(0);
						}, 20 * 1000);
						break;
					}
					case START:
					{
						if (checkPlayers(_instance.getId()))
						{
							if (_antifeed)
							{
								for (PlayerEventInfo player : getPlayers(_instance.getId()))
								{
									// Skip antifeed protection for GM/Admin to prevent stat issues
									if (!player.isGM()) {
										player.startAntifeedProtection(false);
									}
								}
							}
							// Khởi tạo vòng tròn
							_data.initCircle();
							// Lấy danh sách tọa độ Regular để teleport người chơi
							Random random = new Random();
							List<EventSpawn> regularSpawns = new ArrayList<>();
							int index = 0;
							while (true)
							{
								EventSpawn spawn = getSpawn(SpawnType.Regular, index);
								if (spawn == null)
								{
									break;
								}
								regularSpawns.add(spawn);
								index++;
							}
							if (regularSpawns.isEmpty())
							{
								if (GabrielEventsLoader.detailedDebug)
								{
									print("BattleRoyale: Error: No Regular spawns found for teleporting players!");
								}
								break; // Thoát nếu không có tọa độ Regular nào
							}
							// Teleport người chơi ngẫu nhiên xung quanh tọa độ Regular
							int spawnIndex = 0; // Biến để phân bổ tọa độ Regular tuần tự
							for (PlayerEventInfo player : getPlayers(_instance.getId()))
							{
								if (player.isOnline())
								{
									// Chọn tọa độ Regular tuần tự để phân bố đều
									EventSpawn selectedSpawn = regularSpawns.get(spawnIndex % regularSpawns.size());
									spawnIndex++; // Tăng chỉ số để chọn tọa độ Regular tiếp theo cho người chơi tiếp theo
									Loc spawnLoc = selectedSpawn.getLoc();
									// Lấy bán kính từ EventSpawn, sử dụng giá trị mặc định 10000 nếu không hợp lệ
									int spawnRadius = selectedSpawn.getRadius();
									if (spawnRadius <= 0)
									{
										spawnRadius = 10000; // Giá trị mặc định nếu getRadius() trả về giá trị không hợp lệ
										if (GabrielEventsLoader.detailedDebug)
										{
											print("BattleRoyale: Invalid radius (" + selectedSpawn.getRadius() + ") for Regular spawn at (" + spawnLoc.getX() + ", " + spawnLoc.getY() + ", " + spawnLoc.getZ() + "), using default radius 10000");
										}
									}
									// Tính toán vị trí ngẫu nhiên trong bán kính spawnRadius xung quanh tọa độ Regular
									Loc finalSpawnLoc = findSafeSpawnLocation(spawnLoc, spawnRadius);

									// Dịch chuyển người chơi đến vị trí an toàn
									player.teleport(finalSpawnLoc, 0, true, _instance.getId());
									player.setInstanceId(_instance.getId());

									// Store spawn location for stuck command
									_playerSpawnLocs.put(player.getPlayersId(), finalSpawnLoc);

									// Notify player about stuck command
									player.sendMessage("You have been teleported to the Battle Royale arena!");
									player.sendMessage("If you get stuck, use .stuck command to teleport near other players!");

									if (GabrielEventsLoader.detailedDebug)
									{
										double distanceSquared = Math.pow(finalSpawnLoc.getX() - spawnLoc.getX(), 2) + Math.pow(finalSpawnLoc.getY() - spawnLoc.getY(), 2);
										print("BattleRoyale: Teleported player " + player.getPlayersName() + " to (" + finalSpawnLoc.getX() + ", " + finalSpawnLoc.getY() + ", " + finalSpawnLoc.getZ() + ") in instance " + _instance.getId() + ", distance squared from Regular spawn: " + distanceSquared + ", spawn radius: " + spawnRadius);
									}
								}
							}
							setupTitles(_instance.getId());
							enableMarkers(_instance.getId(), true);
							// Trang bị vũ khí và vật phẩm ngay lập tức
							forceStandAll(_instance.getId());
							// handleWeapons(_instance.getId(), true); // Cấp vũ khí mặc định sau khi rời lobby
							// announce(_instance.getId(), "Battle Royale has started! Find items and fight to survive!");
							// if (GabrielEventsLoader.detailedDebug)
							// {
							// print("BattleRoyale: Announced: Battle Royale has started! Find items and fight to survive!");
							// }
							setNextState(EventState.FIGHT);
							scheduleNextTask(0);
							if (GabrielEventsLoader.detailedDebug)
							{
								print("BattleRoyale: ... finished running task. next state FIGHT");
							}
							break;
						}
						break;
					}
					case FIGHT:
					{
						setNextState(EventState.END);
						_clock.startClock(_manager.getRunTime());
						_data.setEventStartTime(System.currentTimeMillis()); // Lưu thời gian bắt đầu FIGHT
						_data.startCircleShrink();
						break;
					}
					case END:
					{
						_data.stopCircleShrink();
						_clock.setTime(0, true);
						setNextState(EventState.INACTIVE);
						endEvent(_instance.getId());
						if (!instanceEnded() && _canBeAborted)
						{
							if (_canRewardIfAborted)
							{
								rewardAllPlayers(_instance.getId(), 0, getInt("killsForReward"));
							}
							clearEvent(_instance.getId());
							break;
						}
						break;
					}
					case INACTIVE:
					{
						break;
					}
				}
				if (GabrielEventsLoader.detailedDebug)
				{
					print("BattleRoyale: ... finished running task. next state " + _nextState.toString());
				}
			}
			catch (Throwable e)
			{
				e.printStackTrace();
				_manager.endDueToError(LanguageEngine.getMsg("event_error"));
			}
		}
		
		@Override
		public void abort()
		{
			if (_lobbyTask != null)
			{
				_lobbyTask.cancel(false);
			}
			if (_task != null)
			{
				_task.cancel(false);
				_task = null;
			}
			if (_clock != null)
			{
				_clock.abort();
			}
			if (_data != null)
			{
				_data.stopCircleShrink();
			}
		}
		
		@Override
		public ScheduledFuture<?> scheduleNextTask(int time)
		{
			if (_task != null)
			{
				_task.cancel(false);
			}
			if (_clock != null && _clock._task != null)
			{
				_clock._task.cancel(false);
			}
			if (time > 0)
			{
				_task = CallBack.getInstance().getOut().scheduleGeneral(this::run, time);
			}
			else
			{
				CallBack.getInstance().getOut().executeTask(this::run);
			}
			return _task;
		}
	}
	
	public class BRData extends AbstractEventData
	{
		public int														_circleRadius;
		public Loc														_circleCenter;
		private boolean													_circleActive;
		private ScheduledFuture<?>										_circleTask;
		private ScheduledFuture<?>										_countdownTask;
		private ScheduledFuture<?>										_damageTask;
		private ScheduledFuture<?>										_finalCircleTask;

		// Throttling để tránh spam ExServerPrimitive packets và DirectX crashes
		private long													_lastCircleUpdate = 0;
		private static final long										CIRCLE_UPDATE_THROTTLE = 15000; // 15 second throttle to prevent DirectX issues
		private int														_lastRenderedRadius = -1; // Track last rendered radius to prevent unnecessary updates
		private BRCircleColor											_lastRenderedColor = null; // Track last rendered color
		private int														_lastRenderedStage = -1; // Track last rendered stage
		private ScheduledFuture<?>										_airdropSpawnTask;
		private List<AirdropData>										_airdropSpawns;
		private List<Loc>												_usedAirdropLocations;


		private static final int										MIN_AIRDROP_DISTANCE	= 1000;
		private final Random											random					= new Random();
		private int														currentStageIndex		= -1;
		private long													eventStartTime;											// Thời gian bắt đầu sự kiện
		private long													currentStageStartTime;
		private final ConcurrentHashMap<Integer, Long>					lastDamageTimes			= new ConcurrentHashMap<>();
		private final ConcurrentHashMap<Integer, Boolean>				outsideCircleCache		= new ConcurrentHashMap<>();	// Cache trạng thái ngoài vòng bo
		private static final long										DAMAGE_COOLDOWN			= 500; // 0.5s for more responsive damage
		private final ConcurrentHashMap<PlayerEventInfo, Set<Integer>>	_playerItemIds			= new ConcurrentHashMap<>();
		private final ConcurrentHashMap<Integer, Boolean>				playerEffectStatus		= new ConcurrentHashMap<>();
		private BRCircle												safeZone;
		private final BattleRoyale										event;
		
		protected BRData(int instance, BattleRoyale event)
		{
			super(instance);
			this.event = event;
			_circleActive = false;
			_airdropSpawns = new ArrayList<>();
			_usedAirdropLocations = new ArrayList<>();
			currentStageStartTime = 0;
		}
		
		public void setEventStartTime(long time)
		{
			this.eventStartTime = time;
		}
		
		public Set<Integer> getPlayerItemIds(PlayerEventInfo player)
		{
			return _playerItemIds.computeIfAbsent(player, k -> new HashSet<>());
		}
		
		public void addPlayerItem(PlayerEventInfo player, int itemId)
		{
			Set<Integer> itemIds = getPlayerItemIds(player);
			itemIds.add(itemId);
		}
		
		public List<Stage> getStages()
		{
			return stages;
		}
		
		public int getCurrentStageIndex()
		{
			return currentStageIndex;
		}
		
		public void initCircle()
		{
			List<Loc> circleCenters = event.circleCenters;
			if (circleCenters.isEmpty())
			{
				List<EventSpawn> circleCenterSpawns = new ArrayList<>();
				int index = 0;
				while (true)
				{
					EventSpawn spawn = getSpawn(SpawnType.CircleCenter, index);
					if (spawn == null)
					{
						break;
					}
					circleCenterSpawns.add(spawn);
					index++;
				}
				if (circleCenterSpawns.isEmpty())
				{
					return;
				}
				EventSpawn selectedSpawn = circleCenterSpawns.get(random.nextInt(circleCenterSpawns.size()));
				circleCenters.add(new Loc(selectedSpawn.getLoc().getX(), selectedSpawn.getLoc().getY(), selectedSpawn.getLoc().getZ()));
			}
			_circleCenter = null;
			if (!stages.isEmpty())
			{
				currentStageIndex = -1; // Đặt stage là -1 (Stage 0) trong 10 phút đầu
				_circleRadius = stages.get(0).getRadius();
				_circleActive = true;
			}
			else
			{
				_circleRadius = 1000;
			}
		}
		
		public void startCircleShrink()
		{
			if (stages.isEmpty())
			{
				_circleActive = false;
				event.endInstance(_instanceId, true, true, false);
				return;
			}
			int initialRadius = stages.get(0).getRadius();
			int finalRadius = stages.get(stages.size() - 1).getRadius();
			MainEventManager manager = event.getManager();
			if (manager == null)
			{
				event.endInstance(_instanceId, true, true, false);
				return;
			}
			long shrinkStartTime = eventStartTime + (2 * 60 * 1000); // 2 phút safe time
			final long totalShrinkTime = stages.stream().mapToLong(stage -> stage.getSafeTime() * 1000L).sum();
			// Thêm task thưởng điểm sống sót
			CallBack.getInstance().getOut().scheduleGeneralAtFixedRate(() ->
			{
				if (!_circleActive)
					return;
				for (PlayerEventInfo player : activePlayers.getOrDefault(_instanceId, new ConcurrentHashMap<>()).values())
				{
					if (player != null && player.isOnline() && !player.isDead())
					{
						getPlayerData(player).addEventPoints(2);
						player.sendMessage("You get 2 points for surviving the event!");

						// Update title to show new points
						if (player.isTitleUpdated())
						{
							// Force title update with current points
							String newTitle = getTitle(player);
							player.setTitle(newTitle, true);
							safeBroadcastTitleInfo(player);
							safeBroadcastUserInfo(player);
						}
					}
				}
			}, 60 * 1000, 60 * 1000);
			_circleTask = CallBack.getInstance().getOut().scheduleGeneralAtFixedRate(() ->
			{
				if (!_circleActive)
				{
					return;
				}
				long currentTime = System.currentTimeMillis();
				if (currentTime < shrinkStartTime)
				{
					_circleCenter = null;
					safeZone = null;
					ConcurrentHashMap<Integer, PlayerEventInfo> players = activePlayers.getOrDefault(_instanceId, new ConcurrentHashMap<>());
					for (PlayerEventInfo player : players.values())
					{
						if (player != null && player.isOnline() && player.getInstanceId() == _instanceId)
						{
							for (int stage = 1; stage <= stages.size(); stage++)
							{
								for (BRCircleColor color : BRCircleColor.values())
								{
									int circleId = -(110000 + stage * 10 + (color == BRCircleColor.WHITE ? 1 : 2));
									String circleName = "battleRoyaleCircle_" + circleId;
									ExServerPrimitive hidePacket = new ExServerPrimitive(circleName, 0, 0, 0);
									player.getOwner().sendPacket(hidePacket);
								}
							}
						}
					}
					return;
				}
				if (_circleCenter == null)
				{
					List<Loc> circleCenters = event.circleCenters;
					Loc selectedCenter = null;
					if (!circleCenters.isEmpty())
					{
						selectedCenter = circleCenters.get(random.nextInt(circleCenters.size()));
					}
					else
					{
						List<EventSpawn> circleCenterSpawns = new ArrayList<>();
						int index = 0;
						while (true)
						{
							EventSpawn spawn = getSpawn(SpawnType.CircleCenter, index);
							if (spawn == null)
							{
								break;
							}
							circleCenterSpawns.add(spawn);
							index++;
						}
						if (circleCenterSpawns.isEmpty())
						{
							event.endInstance(_instanceId, true, true, false);
							return;
						}
						EventSpawn selectedSpawn = circleCenterSpawns.get(random.nextInt(circleCenterSpawns.size()));
						selectedCenter = new Loc(selectedSpawn.getLoc().getX(), selectedSpawn.getLoc().getY(), selectedSpawn.getLoc().getZ());
					}
					_circleCenter = selectedCenter;
					_circleRadius = initialRadius;
					// Determine circle color based on stage state (PUBG-like)
					BRCircleColor color = getCircleColorForStage(currentStageIndex, _circleRadius);
					currentStageIndex = 0; // Chuyển sang Stage 1 khi vòng tròn xuất hiện

					// Clear all existing circles first before creating new one
					clearAllCircles();

					// Create initial circle immediately (no delay to prevent multiple circles)
					safeZone = new BRCircle(_circleCenter.getX(), _circleCenter.getY(), _circleCenter.getZ(), _circleRadius, color, currentStageIndex + 1);
					showCircleWithTracking(safeZone);

					// Update tracking variables for initial circle
					_lastRenderedRadius = _circleRadius;
					_lastRenderedColor = color;
					_lastRenderedStage = currentStageIndex + 1;

					if (GabrielEventsLoader.detailedDebug)
					{
						print("BattleRoyale: Created initial circle with radius " + _circleRadius + " and color " + color);
					}
					ConcurrentHashMap<Integer, PlayerEventInfo> players = activePlayers.getOrDefault(_instanceId, new ConcurrentHashMap<>());
					for (PlayerEventInfo player : players.values())
					{
						if (player != null && player.isOnline() && player.getInstanceId() == _instanceId)
						{
							player.sendMessage(LanguageEngine.getMsg("br_safe_zone_appeared"));
						}
					}
					currentStageStartTime = currentTime;

					// Immediate damage check when circle first appears
					CallBack.getInstance().getOut().scheduleGeneral(() -> {
						if (_circleActive && _circleCenter != null)
						{
							checkPlayersOutsideCircle(_instanceId);
						}
					}, 500); // 500ms delay để đảm bảo circle đã được setup
				}
				// PUBG-like stage-based shrinking: each stage has its own radius
				if (currentStageIndex >= 0 && currentStageIndex < stages.size())
				{
					Stage currentStage = stages.get(currentStageIndex);
					_circleRadius = currentStage.getRadius(); // Use exact stage radius
				}
				else
				{
					// Fallback to old logic for safety
					long elapsedShrinkTime = currentTime - shrinkStartTime;
					double shrinkProgress = Math.min(1.0, (double) elapsedShrinkTime / totalShrinkTime);
					_circleRadius = (int) (initialRadius - (initialRadius - finalRadius) * shrinkProgress);
					_circleRadius = Math.max(_circleRadius, finalRadius);
				}
				// Update circle continuously for smooth PUBG-like shrinking effect
				boolean shouldUpdateCircle = false;

				// Handle stage transitions with proper circle clearing
				long elapsedTimeInStage = currentTime - currentStageStartTime;
				if (currentStageIndex >= 0 && currentStageIndex < stages.size())
				{
					Stage currentStage = stages.get(currentStageIndex);
					long stageDuration = currentStage.getSafeTime() * 1000;
					if (elapsedTimeInStage >= stageDuration)
					{
						if (currentStageIndex < stages.size() - 1)
						{
							// Clear old circle before moving to next stage
							clearAllCircles();

							// Move to next stage
							int oldStageIndex = currentStageIndex;
							currentStageIndex++;
							currentStageStartTime = currentTime;

							if (GabrielEventsLoader.detailedDebug)
							{
								print("BattleRoyale: Stage transition from " + (oldStageIndex + 1) + " to " + (currentStageIndex + 1));
							}

							// Force circle update for new stage
							shouldUpdateCircle = true;
						}
					}
				}
				// Determine circle color based on stage state (PUBG-like)
				BRCircleColor color = getCircleColorForStage(currentStageIndex, _circleRadius);

				// Check time-based throttling to prevent DirectX crashes
				long timeSinceLastUpdate = currentTime - _lastCircleUpdate;
				long updateInterval = Math.max(getInt("circleUpdateInterval"), 15000); // Minimum 15 seconds to prevent DirectX issues

				if (safeZone == null)
				{
					shouldUpdateCircle = true;
					if (GabrielEventsLoader.detailedDebug)
					{
						print("BattleRoyale: Creating circle because safeZone is null");
					}
				}
				else
				{
					int radiusDiff = Math.abs(_lastRenderedRadius - _circleRadius);
					boolean colorChanged = _lastRenderedColor != color;
					boolean stageChanged = _lastRenderedStage != (currentStageIndex + 1);

					// Always update for continuous shrinking effect, but respect time throttling
					if (stageChanged || colorChanged)
					{
						// Immediate update for stage/color changes - clear old stage circle first
						if (stageChanged)
						{
							clearSpecificStageCircle(_lastRenderedStage); // Clear old stage circle specifically
						}
						shouldUpdateCircle = true;
						if (GabrielEventsLoader.detailedDebug)
						{
							print("BattleRoyale: Updating circle - colorChanged: " + colorChanged + ", stageChanged: " + stageChanged);
						}
					}
					else if (timeSinceLastUpdate >= updateInterval)
					{
						// Regular update for smooth shrinking (always update at interval for continuous effect)
						shouldUpdateCircle = true;
						if (GabrielEventsLoader.detailedDebug)
						{
							print("BattleRoyale: Updating circle - continuous shrink, radiusDiff: " + radiusDiff + ", timeSinceLastUpdate: " + timeSinceLastUpdate + "ms");
						}
					}
					else
					{
						// Just update the radius without rendering new circle
						safeZone = new BRCircle(_circleCenter.getX(), _circleCenter.getY(), _circleCenter.getZ(), _circleRadius, safeZone.getColor(), safeZone.getStageNumber());
						if (GabrielEventsLoader.detailedDebug && radiusDiff > 0)
						{
							print("BattleRoyale: Skipped circle update - throttled, radiusDiff: " + radiusDiff + ", timeSinceLastUpdate: " + timeSinceLastUpdate + "ms");
						}
					}
				}

				// Emergency disable check
				if (getBoolean("disableCircleUpdates"))
				{
					if (GabrielEventsLoader.detailedDebug)
					{
						print("BattleRoyale: Circle updates disabled by config");
					}
					return; // Skip all circle operations
				}

				if (shouldUpdateCircle)
				{
					// Enhanced circle update with verification and synchronization
					final int finalStageIndex = currentStageIndex;
					final BRCircleColor finalColor = color;
					final int currentRadius = _circleRadius;

					// Clear all existing circles first - critical for preventing DirectX crashes
					clearAllCircles();

					// Verify circle clearing before proceeding with new circle
					ThreadPool.schedule(() -> {
						try
						{
							// Verify that enough time has passed since last clear
							Long lastClearTime = _lastCircleClearTime.get(_instanceId);
							if (lastClearTime != null && (System.currentTimeMillis() - lastClearTime) < CIRCLE_CLEAR_VERIFICATION_DELAY)
							{
								if (GabrielEventsLoader.detailedDebug)
								{
									print("BattleRoyale: Waiting for circle clear verification...");
								}
								// Wait a bit more for clearing to complete
								Thread.sleep(CIRCLE_CLEAR_VERIFICATION_DELAY);
							}

							// Create and show new circle with current stage data
							BRCircle newCircle = new BRCircle(_circleCenter.getX(), _circleCenter.getY(), _circleCenter.getZ(), currentRadius, finalColor, finalStageIndex + 1);
							safeZone = newCircle;
							showCircleWithTracking(newCircle);

							// Update tracking variables after successful render
							_lastRenderedRadius = currentRadius;
							_lastRenderedColor = finalColor;
							_lastRenderedStage = finalStageIndex + 1;

							if (GabrielEventsLoader.detailedDebug)
							{
								print("BattleRoyale: Enhanced circle update completed - radius: " + currentRadius + ", color: " + finalColor + ", stage: " + (finalStageIndex + 1));
							}
						}
						catch (Exception e)
						{
							print("BattleRoyale: Error in enhanced circle update: " + e.getMessage());
						}
					}, 500); // 500ms delay to ensure clear is processed and prevent packet spam
				}

				// Immediate damage check for all players when circle appears/changes
				CallBack.getInstance().getOut().scheduleGeneral(() -> {
					if (_circleActive && _circleCenter != null)
					{
						checkPlayersOutsideCircle(_instanceId);
					}
				}, 100); // 100ms delay để đảm bảo circle đã được render
				if (currentStageIndex == stages.size() - 1)
				{
					long elapsedTimeInFinalStage = currentTime - currentStageStartTime;
					long finalStageDuration = stages.get(currentStageIndex).getSafeTime() * 1000;
					if (elapsedTimeInFinalStage >= finalStageDuration)
					{
						String finalCircleMessage = LanguageEngine.getMsg("br_final_circle");
						for (PlayerEventInfo player : activePlayers.getOrDefault(_instanceId, new ConcurrentHashMap<>()).values())
						{
							if (player != null && player.isOnline() && player.getOwner() != null)
							{
								player.sendMessage(finalCircleMessage);
							}
						}
						_finalCircleTask = CallBack.getInstance().getOut().scheduleGeneral(() ->
						{
							if (!_circleActive)
								return;
							int alive = 0;
							for (PlayerEventInfo player : activePlayers.getOrDefault(_instanceId, new ConcurrentHashMap<>()).values())
							{
								if (player != null && player.isOnline() && !player.isDead())
								{
									alive++;
								}
							}
							if (alive > 1)
							{
								String drawMessage = LanguageEngine.getMsg("br_draw");
								for (PlayerEventInfo player : activePlayers.getOrDefault(_instanceId, new ConcurrentHashMap<>()).values())
								{
									if (player != null && player.isOnline() && player.getOwner() != null)
									{
										player.sendMessage(drawMessage);
									}
								}
								if (GabrielEventsLoader.detailedDebug)
								{
									print("BattleRoyale: Final circle countdown ended, " + alive + " players still alive, ending in a draw");
								}
								for (PlayerEventInfo player : activePlayers.getOrDefault(_instanceId, new ConcurrentHashMap<>()).values())
								{
									if (player != null && player.isOnline() && !player.isDead())
									{
										player.doDie(null);
									}
								}
								event.endInstance(_instanceId, true, true, true);
							}
						}, 60 * 1000);
						_circleTask.cancel(false);
					}
				}
			}, event.getInt("circleUpdateInterval"), event.getInt("circleUpdateInterval")); // Use configurable interval
			_damageTask = CallBack.getInstance().getOut().scheduleGeneralAtFixedRate(() ->
			{
				if (!_circleActive)
				{
					if (GabrielEventsLoader.detailedDebug)
					{
						print("BattleRoyale: Damage task skipped because _circleActive is false");
					}
					return;
				}
				if (_circleCenter != null)
				{
					checkPlayersOutsideCircle(_instanceId);
				}
			}, event.getInt("damageInterval"), event.getInt("damageInterval")); // Use configurable damage interval
			if (_airdropSpawnTask != null)
			{
				_airdropSpawnTask.cancel(false);
			}
			_airdropSpawnTask = CallBack.getInstance().getOut().scheduleGeneralAtFixedRate(() ->
			{
				if (!_circleActive)
					return;
				spawnAirdrop(_instanceId);
			}, 10 * 1000, 60 * 1000);

			// Removed automatic circle refresh task to prevent multiple circles
			// Circle will only be shown when it changes or appears
			if (GabrielEventsLoader.detailedDebug)
			{
				print("BattleRoyale: Circle visibility task disabled to prevent multiple circles");
			}
		}
		
		private void applyDamage(PlayerEventInfo player)
		{
			if (player == null || player.getOwner() == null)
			{
				if (GabrielEventsLoader.detailedDebug)
				{
					print("BattleRoyale: applyDamage - Player or PlayerInstance is null, skipping damage");
				}
				return;
			}

			long currentTime = System.currentTimeMillis();
			Long lastDamageTime = lastDamageTimes.getOrDefault(player.getPlayersId(), 0L);
			long timeSinceLastDamage = currentTime - lastDamageTime;

			if (timeSinceLastDamage < DAMAGE_COOLDOWN)
			{
				if (GabrielEventsLoader.detailedDebug)
				{
					print("BattleRoyale: applyDamage - Damage cooldown for " + player.getPlayersName() + " (remaining: " + (DAMAGE_COOLDOWN - timeSinceLastDamage) + "ms)");
				}
				return;
			}

			// Tính sát thương dựa trên stage hiện tại
			// Nếu circle đã active nhưng chưa có stage, sử dụng stage đầu tiên
			int damageStageIndex = currentStageIndex;
			if (damageStageIndex < 0 && _circleActive && _circleCenter != null)
			{
				damageStageIndex = 0; // Sử dụng stage đầu tiên cho damage
			}

			if (damageStageIndex < 0 || damageStageIndex >= stages.size())
			{
				if (GabrielEventsLoader.detailedDebug)
				{
					print("BattleRoyale: applyDamage - Invalid stage index: " + damageStageIndex + " (stages size: " + stages.size() + ", currentStageIndex: " + currentStageIndex + ", _circleActive: " + _circleActive + ")");
				}
				return;
			}

			Stage currentStage = stages.get(damageStageIndex);
			int initialDamage = currentStage.getDamageInitial();
			int finalDamage = currentStage.getDamageFinal();
			long elapsedTimeInStage = currentTime - currentStageStartTime;
			long stageDuration = currentStage.getSafeTime() * 1000; // Thời gian của stage tính bằng ms
			double progress = stageDuration > 0 ? Math.min(1.0, (double) elapsedTimeInStage / stageDuration) : 1.0;
			int damage = (int) (initialDamage + (finalDamage - initialDamage) * progress);
			if (damage <= 0)
			{
				damage = 1;
			}

			double currentHp = player.getOwner().getCurrentHp();
			double newHp = Math.max(0, currentHp - damage);

			if (GabrielEventsLoader.detailedDebug)
			{
				print("BattleRoyale: applyDamage - Player: " + player.getPlayersName() + ", Stage: " + (damageStageIndex + 1) + ", Damage: " + damage + " (" + initialDamage + "-" + finalDamage + "), Progress: " + String.format("%.2f", progress * 100) + "%, HP: " + (int)currentHp + " -> " + (int)newHp);
			}

			player.getOwner().setCurrentHp(newHp);
			player.getOwner().sendDamageMessage(player.getOwner(), null, damage, damage, false, false, false);

			SystemMessage sm = new SystemMessage(SystemMessageId.C1_HAS_DEALT_S3_DAMAGE_TO_C2);
			sm.addString("Circle");
			sm.addString(player.getOwner().getName());
			sm.addInt(damage);
			sm.addPopup(player.getOwner().getObjectId(), player.getOwner().getObjectId(), -damage);
			if (sm != null)
			{
				player.getOwner().sendPacket(sm);
			}

			if (newHp <= 0)
			{
				if (!player.isDead())
				{
					// Stop visual effect before death
					player.getOwner().getEffectList().stopAbnormalVisualEffect(AbnormalVisualEffect.WAR_ROAR_SCREEN_AVE);
					playerEffectStatus.remove(player.getPlayersId());

					// Call onDie with null killer (circle damage) - this will handle all death logic properly
					event.onDie(player, null);

					if (GabrielEventsLoader.detailedDebug)
					{
						print("BattleRoyale: Player " + player.getPlayersName() + " died from circle damage");
					}
				}
			}
			else
			{
				if (!playerEffectStatus.getOrDefault(player.getPlayersId(), false))
				{
					player.getOwner().getEffectList().startAbnormalVisualEffect(AbnormalVisualEffect.WAR_ROAR_SCREEN_AVE);
					playerEffectStatus.put(player.getPlayersId(), true);
				}
			}
			lastDamageTimes.put(player.getPlayersId(), currentTime);
		}
		
		private void checkPlayersOutsideCircle(int instanceId)
		{
			ConcurrentHashMap<Integer, PlayerEventInfo> players = activePlayers.getOrDefault(instanceId, new ConcurrentHashMap<>());
			if (players.isEmpty())
			{
				if (GabrielEventsLoader.detailedDebug)
				{
					print("BattleRoyale: checkPlayersOutsideCircle - No active players found for instance " + instanceId);
				}
				return;
			}

			// Enhanced debug info
			long currentTime = System.currentTimeMillis();
			long elapsedTime = currentTime - eventStartTime;
			long shrinkStartTime = 2 * 60 * 1000; // 2 minutes
			boolean circleActive = _circleCenter != null;

			if (GabrielEventsLoader.detailedDebug)
			{
				print("BattleRoyale: checkPlayersOutsideCircle - ElapsedTime: " + (elapsedTime/1000) + "s, ShrinkStartTime: " + (shrinkStartTime/1000) + "s, CircleActive: " + circleActive + ", CircleCenter: " + (_circleCenter != null ? _circleCenter.toString() : "null") + ", CircleRadius: " + _circleRadius);
			}

			if (_circleCenter == null)
			{
				if (GabrielEventsLoader.detailedDebug)
				{
					print("BattleRoyale: checkPlayersOutsideCircle - Skipping because safe zone has not yet appeared (_circleCenter is null). Time remaining: " + Math.max(0, (shrinkStartTime - elapsedTime)/1000) + "s");
				}
				return;
			}

			for (PlayerEventInfo player : players.values())
			{
				if (player == null || !player.isOnline() || player.isDead())
				{
					if (GabrielEventsLoader.detailedDebug)
					{
						print("BattleRoyale: checkPlayersOutsideCircle - Skipping player " + (player != null ? player.getPlayersName() : "null") + " (Online: " + (player != null && player.isOnline()) + ", Dead: " + (player != null && player.isDead()) + ")");
					}
					continue;
				}

				// Tính khoảng cách không bao gồm Z (chỉ dựa trên X, Y)
				double dx = player.getX() - _circleCenter.getX();
				double dy = player.getY() - _circleCenter.getY();
				double distanceSquared = dx * dx + dy * dy;
				double radiusSquared = _circleRadius * _circleRadius;
				double distance = Math.sqrt(distanceSquared);
				boolean isOutside = distanceSquared > radiusSquared;

				// Enhanced debug for each player
				if (GabrielEventsLoader.detailedDebug)
				{
					print("BattleRoyale: Player " + player.getPlayersName() + " - Distance: " + (int)distance + ", Radius: " + _circleRadius + ", IsOutside: " + isOutside + ", Position: (" + player.getX() + ", " + player.getY() + "), CircleCenter: (" + _circleCenter.getX() + ", " + _circleCenter.getY() + ")");
				}

				outsideCircleCache.put(player.getPlayersId(), isOutside);
				if (isOutside)
				{
					applyDamage(player);
				}
				else
				{
					if (playerEffectStatus.getOrDefault(player.getPlayersId(), false))
					{
						player.getOwner().getEffectList().stopAbnormalVisualEffect(AbnormalVisualEffect.WAR_ROAR_SCREEN_AVE);
						playerEffectStatus.put(player.getPlayersId(), false);
					}
				}
				updateRadar(player);
			}
		}
		
		private void updateRadar(PlayerEventInfo player)
		{
			if (!player.isOnline())
				return;
			player.removeRadarAllMarkers();
			if (safeZone != null)
			{
				double dx = player.getX() - safeZone.getX();
				double dy = player.getY() - safeZone.getY();
				double distanceSquared = dx * dx + dy * dy;
				double radiusSquared = safeZone.getRadius() * safeZone.getRadius();
				if (distanceSquared > radiusSquared)
				{
					player.addRadarMarker(safeZone.getX(), safeZone.getY(), safeZone.getZ());
				}
			}
		}
		
		private void showCircles()
		{
			if (safeZone != null)
			{
				// Hiển thị tất cả vòng tròn (tối ưu hóa)
				if (safeZone != null)
				{
					showCircle(safeZone);
				}
				else
				{
					if (GabrielEventsLoader.detailedDebug)
					{
						print("BattleRoyale: Skipped showing circle (ID: " + safeZone.getId() + ") because it is green and radius <= 2000");
					}
				}
			}
		}
		
		private void hideCircles()
		{
			// Use clearAllCircles() instead since we're using ExServerPrimitive now
			clearAllCircles();
		}

		private void clearAllCircles()
		{
			// Enhanced circle clearing with verification and tracking
			try
			{
				ConcurrentHashMap<Integer, PlayerEventInfo> players = activePlayers.getOrDefault(_instanceId, new ConcurrentHashMap<>());
				Set<String> activeCircles = _activeCircleNames.getOrDefault(_instanceId, ConcurrentHashMap.newKeySet());
				int clearedCount = 0;

				for (PlayerEventInfo player : players.values())
				{
					if (player != null && player.isOnline() && player.getInstanceId() == _instanceId)
					{
						PlayerInstance playerInstance = player.getOwner();
						if (playerInstance != null)
						{
							try
							{
								// Clear main BRCircle using AutoPlayTaskManager method
								ExServerPrimitive clearPacket = new ExServerPrimitive("BRCircle", playerInstance.getX(), playerInstance.getY(), playerInstance.getZ() + 65535);
								clearPacket.addPoint(0, 0, 0, 0); // AutoPlayTaskManager approach
								playerInstance.sendPacket(clearPacket);

								// Clear any tracked circle names for this instance
								for (String circleName : activeCircles)
								{
									ExServerPrimitive namedClearPacket = new ExServerPrimitive(circleName, 0, 0, -65535);
									namedClearPacket.addPoint(0, 0, 0, 0);
									playerInstance.sendPacket(namedClearPacket);
								}

								clearedCount++;
							}
							catch (Exception e)
							{
								if (GabrielEventsLoader.detailedDebug)
								{
									print("BattleRoyale: Error clearing circle for " + player.getPlayersName() + ": " + e.getMessage());
								}
							}
						}
					}
				}

				// Clear tracking data
				activeCircles.clear();
				_activeCircleNames.put(_instanceId, activeCircles);
				_lastCircleClearTime.put(_instanceId, System.currentTimeMillis());

				if (GabrielEventsLoader.detailedDebug)
				{
					print("BattleRoyale: Enhanced circle clear completed for " + clearedCount + " players, cleared " + activeCircles.size() + " tracked circles");
				}
			}
			catch (Exception e)
			{
				print("BattleRoyale: Error in enhanced circle clear: " + e.getMessage());
			}
		}

		/**
		 * Clear circle of specific stage to prevent multiple circles when stage changes
		 * Enhanced with verification and tracking
		 */
		private void clearSpecificStageCircle(int stage)
		{
			if (stage <= 0) return; // Invalid stage

			try
			{
				ConcurrentHashMap<Integer, PlayerEventInfo> players = activePlayers.getOrDefault(_instanceId, new ConcurrentHashMap<>());
				Set<String> activeCircles = _activeCircleNames.getOrDefault(_instanceId, ConcurrentHashMap.newKeySet());
				int clearedCount = 0;

				for (PlayerEventInfo player : players.values())
				{
					if (player != null && player.isOnline() && player.getInstanceId() == _instanceId)
					{
						PlayerInstance playerInstance = player.getOwner();
						if (playerInstance != null)
						{
							// Clear specific stage circle using correct naming convention
							// Clear both WHITE, BLUE and DANGER circles for this stage
							for (BRCircleColor color : BRCircleColor.values())
							{
								int circleId = -(110000 + stage * 10 + (color.ordinal() + 1));
								String circleName = "battleRoyaleCircle_" + circleId;

								// Enhanced clearing with addPoint(0,0,0,0) for reliability
								ExServerPrimitive clearPacket = new ExServerPrimitive(circleName, 0, 0, -65535);
								clearPacket.addPoint(0, 0, 0, 0); // AutoPlayTaskManager approach
								playerInstance.sendPacket(clearPacket);

								// Remove from tracking
								activeCircles.remove(circleName);
							}

							clearedCount++;
						}
					}
				}

				// Update tracking
				_activeCircleNames.put(_instanceId, activeCircles);
				_lastCircleClearTime.put(_instanceId, System.currentTimeMillis());

				if (GabrielEventsLoader.detailedDebug)
				{
					print("BattleRoyale: Enhanced clear of stage " + stage + " circles for " + clearedCount + " players");
				}
			}
			catch (Exception e)
			{
				print("BattleRoyale: Error clearing stage " + stage + " circle: " + e.getMessage());
			}
		}

		private void showCircle(BRCircle circle)
		{
			// Enhanced throttling to prevent DirectX crashes
			long currentTime = System.currentTimeMillis();
			_lastCircleUpdate = currentTime;

			// Use ThreadPool for safe circle rendering with DirectX optimization
			ScheduledFuture<?> task = ThreadPool.schedule(() -> {
				try
				{
					// Create simple circle packet once
					ExServerPrimitive circlePacket = createSimpleCirclePacket(circle);

					// Send to all players in instance
					ConcurrentHashMap<Integer, PlayerEventInfo> players = activePlayers.getOrDefault(_instanceId, new ConcurrentHashMap<>());
					int sentCount = 0;
					for (PlayerEventInfo player : players.values())
					{
						if (player != null && player.isOnline() && player.getInstanceId() == _instanceId)
						{
							try
							{
								PlayerInstance playerInstance = player.getOwner();
								if (playerInstance != null)
								{
									playerInstance.sendPacket(circlePacket);
									sentCount++;
								}
							}
							catch (Exception e)
							{
								// Log error but continue
								if (GabrielEventsLoader.detailedDebug)
								{
									print("BattleRoyale: Error sending circle packet to " + player.getPlayersName() + ": " + e.getMessage());
								}
							}
						}
					}

					if (GabrielEventsLoader.detailedDebug)
					{
						print("BattleRoyale: Sent circle packet (ID: " + circle.getId() + ", radius: " + circle.getRadius() + ") to " + sentCount + " players via ThreadPool");
					}
				}
				catch (Exception e)
				{
					print("BattleRoyale: Error in circle render thread: " + e.getMessage());
				}
			}, 0);

			// Track task for cleanup
			_circleRenderTasks.add(task);
		}

		/**
		 * Enhanced showCircle with tracking for better circle management
		 */
		private void showCircleWithTracking(BRCircle circle)
		{
			// Enhanced throttling to prevent DirectX crashes
			long currentTime = System.currentTimeMillis();
			_lastCircleUpdate = currentTime;

			// Use ThreadPool for safe circle rendering with DirectX optimization
			ScheduledFuture<?> task = ThreadPool.schedule(() -> {
				try
				{
					// Create simple circle packet once
					ExServerPrimitive circlePacket = createSimpleCirclePacket(circle);
					String circleName = circle.getName();

					// Send to all players in instance
					ConcurrentHashMap<Integer, PlayerEventInfo> players = activePlayers.getOrDefault(_instanceId, new ConcurrentHashMap<>());
					Set<String> activeCircles = _activeCircleNames.getOrDefault(_instanceId, ConcurrentHashMap.newKeySet());
					int sentCount = 0;

					for (PlayerEventInfo player : players.values())
					{
						if (player != null && player.isOnline() && player.getInstanceId() == _instanceId)
						{
							try
							{
								PlayerInstance playerInstance = player.getOwner();
								if (playerInstance != null)
								{
									playerInstance.sendPacket(circlePacket);
									sentCount++;
								}
							}
							catch (Exception e)
							{
								// Log error but continue
								if (GabrielEventsLoader.detailedDebug)
								{
									print("BattleRoyale: Error sending tracked circle packet to " + player.getPlayersName() + ": " + e.getMessage());
								}
							}
						}
					}

					// Track this circle name for future clearing
					activeCircles.add(circleName);
					_activeCircleNames.put(_instanceId, activeCircles);

					if (GabrielEventsLoader.detailedDebug)
					{
						print("BattleRoyale: Sent tracked circle packet (ID: " + circle.getId() + ", name: " + circleName + ", radius: " + circle.getRadius() + ") to " + sentCount + " players");
					}
				}
				catch (Exception e)
				{
					print("BattleRoyale: Error in tracked circle render thread: " + e.getMessage());
				}
			}, 0);

			// Track task for cleanup
			_circleRenderTasks.add(task);
		}



		private void updateRadarForAllPlayers()
		{
			ConcurrentHashMap<Integer, PlayerEventInfo> players = activePlayers.getOrDefault(_instanceId, new ConcurrentHashMap<>());
			for (PlayerEventInfo player : players.values())
			{
				if (player != null && player.isOnline() && player.getInstanceId() == _instanceId)
				{
					updateRadar(player);
				}
			}
		}
		
		private int getMaxAirdrops(int instanceId)
		{
			ConcurrentHashMap<Integer, PlayerEventInfo> players = activePlayers.getOrDefault(instanceId, new ConcurrentHashMap<>());
			int playerCount = players.size();
			int maxAirdrops = playerCount / 2 + 1;
			return maxAirdrops;
		}
		
		private void spawnAirdrop(int instanceId)
		{
			synchronized (_airdropSpawns)
			{
				int activeAirdrops = 0;
				for (AirdropData data : _airdropSpawns)
				{
					if (data != null && data.airdrop != null && !data.airdrop.isDead())
					{
						activeAirdrops++;
					}
				}
				int maxAirdrops = getMaxAirdrops(instanceId);
				if (activeAirdrops >= maxAirdrops)
				{
					return;
				}
				// Chọn ngẫu nhiên một người chơi còn sống
				ConcurrentHashMap<Integer, PlayerEventInfo> players = activePlayers.getOrDefault(instanceId, new ConcurrentHashMap<>());
				List<PlayerEventInfo> alivePlayers = new ArrayList<>();
				for (PlayerEventInfo player : players.values())
				{
					if (player != null && player.isOnline() && !player.isDead())
					{
						alivePlayers.add(player);
					}
				}
				if (alivePlayers.isEmpty())
				{
					return;
				}
				// Chọn ngẫu nhiên một người chơi từ danh sách người chơi còn sống
				PlayerEventInfo selectedPlayer = alivePlayers.get(random.nextInt(alivePlayers.size()));
				int playerX = selectedPlayer.getX();
				int playerY = selectedPlayer.getY();
				int playerZ = selectedPlayer.getZ();
				int attempts = 0;
				int x = 0, y = 0, z = 0;
				boolean validLocation = false;
				final int AIRDROP_DISTANCE = 500; // Thay đổi khoảng cách thành 500 đơn vị từ người chơi
				while (attempts < MAX_SPAWN_ATTEMPTS && !validLocation)
				{
					// Tạo vị trí ngẫu nhiên cách người chơi 500 đơn vị
					double angle = random.nextDouble() * 2 * Math.PI; // Góc ngẫu nhiên từ 0 đến 2π
					x = (int) (playerX + AIRDROP_DISTANCE * Math.cos(angle));
					y = (int) (playerY + AIRDROP_DISTANCE * Math.sin(angle));
					z = playerZ; // Giữ nguyên Z của người chơi
					// Kiểm tra khoảng cách so với các airdrop đã spawn
					Loc newLoc = new Loc(x, y, z);
					boolean tooClose = false;
					for (Loc usedLoc : _usedAirdropLocations)
					{
						double distance = Math.sqrt(Math.pow(newLoc.getX() - usedLoc.getX(), 2) + Math.pow(newLoc.getY() - usedLoc.getY(), 2));
						if (distance < MIN_AIRDROP_DISTANCE)
						{
							tooClose = true;
							break;
						}
					}
					if (!tooClose)
					{
						validLocation = true;
					}
					attempts++;
				}
				if (!validLocation)
				{
					return;
				}
				final int finalX = x;
				final int finalY = y;
				final int finalZ = z;
				try
				{
					NpcData airdrop = spawnNPC(finalX, finalY, finalZ, _airdropNpcId, instanceId, null, null);
					if (airdrop == null)
					{
						if (GabrielEventsLoader.detailedDebug)
						{
							print("BattleRoyale: Failed to spawn airdrop NPC (ID: " + _airdropNpcId + ") - spawnNPC returned null");
						}
						return;
					}
					ScheduledFuture<?> despawnTask = CallBack.getInstance().getOut().scheduleGeneral(() ->
					{
						synchronized (_airdropSpawns)
						{
							AirdropData airdropData = null;
							for (AirdropData data : _airdropSpawns)
							{
								if (data != null && data.airdrop != null && data.airdrop.getObjectId() == airdrop.getObjectId())
								{
									airdropData = data;
									break;
								}
							}
							if (airdropData != null)
							{
								_airdropSpawns.remove(airdropData);
								airdrop.deleteMe();
								activePlayers.getOrDefault(instanceId, new ConcurrentHashMap<>()).values().stream().filter(player -> player != null && player.isOnline() && player.getOwner() != null).filter(player ->
								{
									double dx = player.getX() - finalX;
									double dy = player.getY() - finalY;
									return (dx * dx + dy * dy) <= 1000000;
								}).forEach(player -> player.removeRadarMarker(finalX, finalY, finalZ));
								addUsedAirdropLocation(new Loc(finalX, finalY, finalZ));
							}
						}
					}, _airdropDespawnTime * 1000);
					_airdropSpawns.add(new AirdropData(airdrop, despawnTask));
					activePlayers.getOrDefault(instanceId, new ConcurrentHashMap<>()).values().stream().filter(player -> player != null && player.isOnline() && player.getOwner() != null).filter(player ->
					{
						double dx = player.getX() - finalX;
						double dy = player.getY() - finalY;
						return (dx * dx + dy * dy) <= 1000000;
					}).forEach(player -> player.addRadarMarker(finalX, finalY, finalZ));
					addUsedAirdropLocation(new Loc(finalX, finalY, finalZ));
				}
				catch (Exception e)
				{
					if (GabrielEventsLoader.detailedDebug)
					{
						print("BattleRoyale: Failed to spawn airdrop NPC (ID: " + _airdropNpcId + ") at (" + finalX + ", " + finalY + ", " + finalZ + "): " + e.getMessage());
						e.printStackTrace();
					}
				}
			}
		}
		
		private void addUsedAirdropLocation(Loc loc)
		{
			_usedAirdropLocations.add(loc);
		}
		
		public void stopCircleShrink()
		{
			_circleActive = false;
			if (_circleTask != null)
			{
				_circleTask.cancel(false);
			}
			if (_countdownTask != null)
			{
				_countdownTask.cancel(false);
			}
			if (_damageTask != null)
			{
				_damageTask.cancel(false);
			}
			if (_finalCircleTask != null)
			{
				_finalCircleTask.cancel(false);
			}
			if (_airdropSpawnTask != null)
			{
				_airdropSpawnTask.cancel(false);
			}
			List<AirdropData> airdropSpawnsCopy = new ArrayList<>(_airdropSpawns);
			for (AirdropData data : airdropSpawnsCopy)
			{
				if (data != null && data.airdrop != null)
				{
					if (data.despawnTask != null)
					{
						data.despawnTask.cancel(false);
					}
					data.airdrop.deleteMe();
				}
			}
			_airdropSpawns.clear();
			_usedAirdropLocations.clear();
			lastDamageTimes.clear();
			_playerItemIds.clear();
			for (PlayerEventInfo player : getPlayers(_instanceId))
			{
				if (player.isOnline() && playerEffectStatus.getOrDefault(player.getPlayersId(), false))
				{
					player.getOwner().getEffectList().stopAbnormalVisualEffect(AbnormalVisualEffect.WAR_ROAR_SCREEN_AVE);
					playerEffectStatus.put(player.getPlayersId(), false);
				}
			}
			playerEffectStatus.clear();
			for (WorldObject obj : World.getInstance().getVisibleObjects())
			{
				if (obj instanceof ItemInstance)
				{
					ItemInstance item = (ItemInstance) obj;
					if (item.getInstanceId() == _instanceId && (item.getCustomType1() == 9999 || item.getCustomType1() == 9998))
					{
						item.decayMe();
						World.getInstance().removeObject(item);
					}
				}
			}
			// Ẩn vòng tròn cho tất cả người chơi trong instance
			hideCircles();
			for (PlayerEventInfo player : getPlayers(_instanceId))
			{
				if (player.isOnline())
				{
					player.removeRadarAllMarkers();
				}
			}
			safeZone = null;
		}
		
		public void unspawnAirdrops()
		{
			for (AirdropData data : _airdropSpawns)
			{
				if (data != null && data.airdrop != null)
				{
					if (data.despawnTask != null)
					{
						data.despawnTask.cancel(false);
					}
					data.airdrop.deleteMe();
				}
			}
			_airdropSpawns.clear();
			_usedAirdropLocations.clear();
		}
		
		public long getShrinkTimeRemaining()
		{
			if (!_circleActive)
			{
				return -1;
			}
			MainEventManager manager = event.getManager();
			if (manager == null)
			{
				return -1;
			}
			long elapsedTime = System.currentTimeMillis() - eventStartTime;
			long totalRunTime = manager.getRunTime() * 1000;
			long timeRemaining = totalRunTime - elapsedTime;
			return Math.max(0, timeRemaining / 1000); // Trả về thời gian còn lại tính bằng giây
		}
		
		public long getCircleShrinkTimeRemaining()
		{
			if (!_circleActive)
			{
				return -1;
			}
			long currentTime = System.currentTimeMillis();
			long elapsedTime = currentTime - eventStartTime;
			long shrinkStartTime = 2 * 60 * 1000; // 2 phút
			if (elapsedTime < shrinkStartTime)
			{
				long timeUntilShrink = shrinkStartTime - elapsedTime;
				return Math.max(0, timeUntilShrink / 1000);
			}
			// Nếu đã qua thời gian chờ nhưng chưa đến stage 1, trả về 0
			if (currentStageIndex < 0)
			{
				return 0;
			}
			// Lấy stage hiện tại
			Stage currentStage = stages.get(currentStageIndex);
			// Tính thời gian đã trôi qua trong stage hiện tại
			long elapsedTimeInStage = currentTime - currentStageStartTime;
			// Tính thời gian còn lại của stage hiện tại
			long stageDuration = currentStage.getSafeTime() * 1000; // Thời gian của stage tính bằng ms
			long timeRemaining = Math.max(0, stageDuration - elapsedTimeInStage); // Thời gian còn lại của stage hiện tại
			return timeRemaining / 1000;
		}
		
		private void notifyPlayerPosition(PlayerEventInfo player, Location targetLoc)
		{
			if (!_circleActive || player == null || !player.isOnline() || player.isDead())
			{
				return;
			}
			if (_circleCenter == null)
			{
				return;
			}
			double dx = targetLoc.getX() - _circleCenter.getX();
			double dy = targetLoc.getY() - _circleCenter.getY();
			double dz = targetLoc.getZ() - _circleCenter.getZ();
			double distanceSquared = dx * dx + dy * dy + dz * dz;
			double radiusSquared = _circleRadius * _circleRadius;
			if (distanceSquared > radiusSquared)
			{
				applyDamage(player);
			}
		}









	// Check if slot requires special equip logic
	private boolean isSpecialSlot(int slot)
	{
		return (slot >= Inventory.PAPERDOLL_DECO1 && slot <= Inventory.PAPERDOLL_DECO6) || // Talisman slots
			   (slot >= Inventory.PAPERDOLL_BROOCH_JEWEL1 && slot <= Inventory.PAPERDOLL_BROOCH_JEWEL6) || // Brooch jewel slots
			   (slot >= Inventory.PAPERDOLL_AGATHION1 && slot <= Inventory.PAPERDOLL_AGATHION5) || // Agathion slots
			   (slot >= Inventory.PAPERDOLL_ARTIFACT1 && slot <= Inventory.PAPERDOLL_ARTIFACT21) || // Artifact slots
			   (slot == Inventory.PAPERDOLL_RFINGER || slot == Inventory.PAPERDOLL_LFINGER) || // Ring slots
			   (slot == Inventory.PAPERDOLL_REAR || slot == Inventory.PAPERDOLL_LEAR) || // Earring slots
			   (slot == Inventory.PAPERDOLL_BROOCH) || // Brooch slot
			   (slot == Inventory.PAPERDOLL_RBRACELET || slot == Inventory.PAPERDOLL_LBRACELET); // Bracelet slots
	}

	// Equip item to specific slot using proper logic
	private void equipItemToSpecificSlot(PlayerInstance player, ItemInstance item, int targetSlot)
	{
		try
		{
			// Force equip to specific slot by setting paperdoll directly
			player.getInventory().setPaperdollItem(targetSlot, item);
			item.setItemLocation(ItemLocation.PAPERDOLL, targetSlot);
			item.setLastChange(ItemInstance.MODIFIED);

			// Update inventory
			final InventoryUpdate iu = new InventoryUpdate();
			iu.addModifiedItem(item);
			player.sendInventoryUpdate(iu);

			if (GabrielEventsLoader.detailedDebug)
			{
				print("BattleRoyale: Force equipped item " + item.getId() + " to slot " + targetSlot + " (" + getSlotName(targetSlot) + ") for player " + player.getName());
			}
		}
		catch (Exception e)
		{
			print("BattleRoyale: Failed to force equip item " + item.getId() + " to slot " + targetSlot + " for player " + player.getName() + ": " + e.getMessage());
		}
	}

	// Enhanced circle packet with height variation and diagonal lines like AutoPlayTaskManager
	private ExServerPrimitive createSimpleCirclePacket(BRCircle circle)
	{
		// Use consistent naming like AutoPlayTaskManager
		String circleName = "BRCircle";
		ExServerPrimitive primitive = new ExServerPrimitive(circleName, circle.getX(), circle.getY(), circle.getZ() + 65535);

		// Determine color based on distance from center (PUBG-like + distance-based)
		int baseColor;
		switch (circle.getColor())
		{
			case WHITE:
				baseColor = 0xFFFFFF; // White for current safe zone
				break;
			case BLUE:
				baseColor = 0x0080FF; // Light blue for next safe zone
				break;
			case DANGER:
				baseColor = 0xFF0000; // Red for danger zone
				break;
			default:
				baseColor = 0xFFFFFF; // Default white
				break;
		}

		// Enhanced color coding based on radius (distance from center)
		int circleColor;
		if (circle.getRadius() > 4000) {
			circleColor = 0x00FF00; // Green for outer circles (safe)
		} else if (circle.getRadius() > 2000) {
			circleColor = 0xFFFF00; // Yellow for middle circles (caution)
		} else if (circle.getRadius() > 800) {
			circleColor = 0xFF8000; // Orange for inner circles (danger)
		} else {
			circleColor = 0xFF0000; // Red for final circles (extreme danger)
		}

		// Use enhanced color or base color based on preference
		int finalColor = baseColor; // Use PUBG colors for consistency

		// Multiple height levels for 3D effect like AutoPlayTaskManager
		int baseZ = circle.getZ();
		int[] heightLevels = {
			baseZ + 50,   // Ground level
			baseZ + 150,  // Mid level
			baseZ + 250   // High level
		};

		// Draw main circle at multiple heights
		for (int heightLevel : heightLevels)
		{
			for (int i = 0; i < POINTS_IN_CIRCLE; i++)
			{
				double angle1 = (i * 2 * Math.PI) / POINTS_IN_CIRCLE;
				double angle2 = ((i + 1) * 2 * Math.PI) / POINTS_IN_CIRCLE;

				int x1 = (int) (circle.getX() + (circle.getRadius() * Math.cos(angle1)));
				int y1 = (int) (circle.getY() + (circle.getRadius() * Math.sin(angle1)));
				int x2 = (int) (circle.getX() + (circle.getRadius() * Math.cos(angle2)));
				int y2 = (int) (circle.getY() + (circle.getRadius() * Math.sin(angle2)));

				// Simple height variation for 3D effect
				int heightColor = finalColor;

				primitive.addLine(heightColor, x1, y1, heightLevel, x2, y2, heightLevel);
			}
		}

		return primitive;
	}



	private String getSlotName(int slot)
	{
		switch (slot)
		{
			case Inventory.PAPERDOLL_UNDER: return "UNDER";
			case Inventory.PAPERDOLL_HEAD: return "HEAD";
			case Inventory.PAPERDOLL_HAIR: return "HAIR";
			case Inventory.PAPERDOLL_HAIR2: return "HAIR2";
			case Inventory.PAPERDOLL_NECK: return "NECK";
			case Inventory.PAPERDOLL_RHAND: return "RHAND";
			case Inventory.PAPERDOLL_CHEST: return "CHEST";
			case Inventory.PAPERDOLL_LHAND: return "LHAND";
			case Inventory.PAPERDOLL_REAR: return "REAR";
			case Inventory.PAPERDOLL_LEAR: return "LEAR";
			case Inventory.PAPERDOLL_GLOVES: return "GLOVES";
			case Inventory.PAPERDOLL_LEGS: return "LEGS";
			case Inventory.PAPERDOLL_FEET: return "FEET";
			case Inventory.PAPERDOLL_RFINGER: return "RFINGER";
			case Inventory.PAPERDOLL_LFINGER: return "LFINGER";
			case Inventory.PAPERDOLL_LBRACELET: return "LBRACELET";
			case Inventory.PAPERDOLL_RBRACELET: return "RBRACELET";
			case Inventory.PAPERDOLL_DECO1: return "TALISMAN1";
			case Inventory.PAPERDOLL_DECO2: return "TALISMAN2";
			case Inventory.PAPERDOLL_DECO3: return "TALISMAN3";
			case Inventory.PAPERDOLL_DECO4: return "TALISMAN4";
			case Inventory.PAPERDOLL_DECO5: return "TALISMAN5";
			case Inventory.PAPERDOLL_DECO6: return "TALISMAN6";
			case Inventory.PAPERDOLL_CLOAK: return "CLOAK";
			case Inventory.PAPERDOLL_BELT: return "BELT";
			case Inventory.PAPERDOLL_BROOCH: return "BROOCH";
			case Inventory.PAPERDOLL_BROOCH_JEWEL1: return "JEWEL1";
			case Inventory.PAPERDOLL_BROOCH_JEWEL2: return "JEWEL2";
			case Inventory.PAPERDOLL_BROOCH_JEWEL3: return "JEWEL3";
			case Inventory.PAPERDOLL_BROOCH_JEWEL4: return "JEWEL4";
			case Inventory.PAPERDOLL_BROOCH_JEWEL5: return "JEWEL5";
			case Inventory.PAPERDOLL_BROOCH_JEWEL6: return "JEWEL6";
			case Inventory.PAPERDOLL_AGATHION1: return "AGATHION1";
			case Inventory.PAPERDOLL_AGATHION2: return "AGATHION2";
			case Inventory.PAPERDOLL_AGATHION3: return "AGATHION3";
			case Inventory.PAPERDOLL_AGATHION4: return "AGATHION4";
			case Inventory.PAPERDOLL_AGATHION5: return "AGATHION5";
			case Inventory.PAPERDOLL_ARTIFACT_BOOK: return "ARTIFACT_BOOK";
			default:
				if (slot >= Inventory.PAPERDOLL_ARTIFACT1 && slot <= Inventory.PAPERDOLL_ARTIFACT21)
					return "ARTIFACT" + (slot - Inventory.PAPERDOLL_ARTIFACT1 + 1);
				return "UNKNOWN_SLOT_" + slot;
		}
	}


	}

	// Verify and restore jewelry slots that might not be properly equipped
	private void verifyJewelrySlots(PlayerEventInfo player, Map<Integer, ItemInstance> equippedItems)
	{
		if (!player.isOnline() || player.getOwner() == null) return;

		// Define jewelry slots that need special attention
		int[] jewelrySlots = {
			Inventory.PAPERDOLL_RFINGER, Inventory.PAPERDOLL_LFINGER, // Rings
			Inventory.PAPERDOLL_BROOCH, // Brooch
			Inventory.PAPERDOLL_BROOCH_JEWEL1, Inventory.PAPERDOLL_BROOCH_JEWEL2,
			Inventory.PAPERDOLL_BROOCH_JEWEL3, Inventory.PAPERDOLL_BROOCH_JEWEL4,
			Inventory.PAPERDOLL_BROOCH_JEWEL5, // Brooch Jewels 1-5
			Inventory.PAPERDOLL_AGATHION1, Inventory.PAPERDOLL_AGATHION2,
			Inventory.PAPERDOLL_AGATHION3, Inventory.PAPERDOLL_AGATHION4,
			Inventory.PAPERDOLL_AGATHION5, // Agathions 1-5
			Inventory.PAPERDOLL_DECO1, Inventory.PAPERDOLL_DECO2,
			Inventory.PAPERDOLL_DECO3, Inventory.PAPERDOLL_DECO4,
			Inventory.PAPERDOLL_DECO5, Inventory.PAPERDOLL_DECO6 // Talismans 1-6
		};

		for (int slot : jewelrySlots)
		{
			ItemInstance expectedItem = equippedItems.get(slot);
			if (expectedItem != null)
			{
				ItemInstance currentlyEquipped = player.getOwner().getInventory().getPaperdollItem(slot);
				if (currentlyEquipped == null || currentlyEquipped.getObjectId() != expectedItem.getObjectId())
				{
					// Item is not equipped in the correct slot, try to fix it
					ItemInstance itemInInventory = player.getOwner().getInventory().getItemByObjectId(expectedItem.getObjectId());
					if (itemInInventory != null && !itemInInventory.isEquipped())
					{
						try
						{
							// Force equip to the specific slot
							player.getOwner().getInventory().setPaperdollItem(slot, itemInInventory);
							itemInInventory.setItemLocation(ItemLocation.PAPERDOLL, slot);
							itemInInventory.setLastChange(ItemInstance.MODIFIED);

							final InventoryUpdate iu = new InventoryUpdate();
							iu.addModifiedItem(itemInInventory);
							player.getOwner().sendInventoryUpdate(iu);

							if (GabrielEventsLoader.detailedDebug)
							{
								print("BattleRoyale: Verified and restored jewelry item " + itemInInventory.getId() + " to slot " + slot + " (" + getSlotName(slot) + ") for player " + player.getPlayersName());
							}
						}
						catch (Exception e)
						{
							print("BattleRoyale: Failed to verify/restore jewelry item " + expectedItem.getId() + " to slot " + slot + " (" + getSlotName(slot) + ") for player " + player.getPlayersName() + ": " + e.getMessage());
						}
					}
				}
			}
		}
	}

	// Force restore special slots that might not have been saved initially
	private void forceRestoreSpecialSlots(PlayerEventInfo player)
	{
		if (!player.isOnline() || player.getOwner() == null) return;

		// Define all special slots that need to be checked
		int[] specialSlots = {
			// Agathion slots 17-21
			Inventory.PAPERDOLL_AGATHION1, Inventory.PAPERDOLL_AGATHION2,
			Inventory.PAPERDOLL_AGATHION3, Inventory.PAPERDOLL_AGATHION4,
			Inventory.PAPERDOLL_AGATHION5,
			// Talisman slots 22-27
			Inventory.PAPERDOLL_DECO1, Inventory.PAPERDOLL_DECO2,
			Inventory.PAPERDOLL_DECO3, Inventory.PAPERDOLL_DECO4,
			Inventory.PAPERDOLL_DECO5, Inventory.PAPERDOLL_DECO6,
			// Brooch jewel slots 31-36
			Inventory.PAPERDOLL_BROOCH_JEWEL1, Inventory.PAPERDOLL_BROOCH_JEWEL2,
			Inventory.PAPERDOLL_BROOCH_JEWEL3, Inventory.PAPERDOLL_BROOCH_JEWEL4,
			Inventory.PAPERDOLL_BROOCH_JEWEL5, Inventory.PAPERDOLL_BROOCH_JEWEL6
		};

		for (int slot : specialSlots)
		{
			// Check if this slot currently has an item equipped
			ItemInstance currentlyEquipped = player.getOwner().getInventory().getPaperdollItem(slot);
			if (currentlyEquipped != null)
			{
				// Item is equipped but shouldn't be (since we're in event)
				// This means it wasn't properly unequipped or was re-equipped incorrectly
				try
				{
					// Find the item in inventory that should be in this slot
					// Look for items that match the equipped item's ID and are not equipped
					for (ItemInstance inventoryItem : player.getOwner().getInventory().getItems())
					{
						if (inventoryItem != null &&
							inventoryItem.getId() == currentlyEquipped.getId() &&
							!inventoryItem.isEquipped() &&
							inventoryItem.getObjectId() != currentlyEquipped.getObjectId())
						{
							// Found a matching item that should be equipped instead
							// Unequip current and equip the correct one
							player.getOwner().getInventory().unEquipItemInSlot(slot);

							// Force equip the correct item
							player.getOwner().getInventory().setPaperdollItem(slot, inventoryItem);
							inventoryItem.setItemLocation(ItemLocation.PAPERDOLL, slot);
							inventoryItem.setLastChange(ItemInstance.MODIFIED);

							final InventoryUpdate iu = new InventoryUpdate();
							iu.addModifiedItem(inventoryItem);
							player.getOwner().sendInventoryUpdate(iu);

							if (GabrielEventsLoader.detailedDebug)
							{
								print("BattleRoyale: Force restored special slot " + slot + " (" + getSlotName(slot) + ") with item " + inventoryItem.getId() + " for player " + player.getPlayersName());
							}
							break;
						}
					}
				}
				catch (Exception e)
				{
					print("BattleRoyale: Failed to force restore special slot " + slot + " (" + getSlotName(slot) + ") for player " + player.getPlayersName() + ": " + e.getMessage());
				}
			}
		}
	}

	// Helper method for slot names (used in both main class and BREventInstance)
	private String getSlotName(int slot)
	{
		switch (slot)
		{
			case Inventory.PAPERDOLL_UNDER: return "UNDER";
			case Inventory.PAPERDOLL_HEAD: return "HEAD";
			case Inventory.PAPERDOLL_HAIR: return "HAIR";
			case Inventory.PAPERDOLL_HAIR2: return "HAIR2";
			case Inventory.PAPERDOLL_NECK: return "NECK";
			case Inventory.PAPERDOLL_RHAND: return "RHAND";
			case Inventory.PAPERDOLL_CHEST: return "CHEST";
			case Inventory.PAPERDOLL_LHAND: return "LHAND";
			case Inventory.PAPERDOLL_REAR: return "REAR";
			case Inventory.PAPERDOLL_LEAR: return "LEAR";
			case Inventory.PAPERDOLL_GLOVES: return "GLOVES";
			case Inventory.PAPERDOLL_LEGS: return "LEGS";
			case Inventory.PAPERDOLL_FEET: return "FEET";
			case Inventory.PAPERDOLL_RFINGER: return "RFINGER";
			case Inventory.PAPERDOLL_LFINGER: return "LFINGER";
			case Inventory.PAPERDOLL_LBRACELET: return "LBRACELET";
			case Inventory.PAPERDOLL_RBRACELET: return "RBRACELET";
			case Inventory.PAPERDOLL_DECO1: return "TALISMAN1";
			case Inventory.PAPERDOLL_DECO2: return "TALISMAN2";
			case Inventory.PAPERDOLL_DECO3: return "TALISMAN3";
			case Inventory.PAPERDOLL_DECO4: return "TALISMAN4";
			case Inventory.PAPERDOLL_DECO5: return "TALISMAN5";
			case Inventory.PAPERDOLL_DECO6: return "TALISMAN6";
			case Inventory.PAPERDOLL_CLOAK: return "CLOAK";
			case Inventory.PAPERDOLL_BELT: return "BELT";
			case Inventory.PAPERDOLL_BROOCH: return "BROOCH";
			case Inventory.PAPERDOLL_BROOCH_JEWEL1: return "JEWEL1";
			case Inventory.PAPERDOLL_BROOCH_JEWEL2: return "JEWEL2";
			case Inventory.PAPERDOLL_BROOCH_JEWEL3: return "JEWEL3";
			case Inventory.PAPERDOLL_BROOCH_JEWEL4: return "JEWEL4";
			case Inventory.PAPERDOLL_BROOCH_JEWEL5: return "JEWEL5";
			case Inventory.PAPERDOLL_BROOCH_JEWEL6: return "JEWEL6";
			case Inventory.PAPERDOLL_AGATHION1: return "AGATHION1";
			case Inventory.PAPERDOLL_AGATHION2: return "AGATHION2";
			case Inventory.PAPERDOLL_AGATHION3: return "AGATHION3";
			case Inventory.PAPERDOLL_AGATHION4: return "AGATHION4";
			case Inventory.PAPERDOLL_AGATHION5: return "AGATHION5";
			case Inventory.PAPERDOLL_ARTIFACT_BOOK: return "ARTIFACT_BOOK";
			default:
				if (slot >= Inventory.PAPERDOLL_ARTIFACT1 && slot <= Inventory.PAPERDOLL_ARTIFACT21)
					return "ARTIFACT" + (slot - Inventory.PAPERDOLL_ARTIFACT1 + 1);
				return "UNKNOWN_SLOT_" + slot;
		}
	}

	public void endEvent(int instanceId)
	{
		// Cleanup all circle render tasks first
		cleanupCircleRenderTasks();

		for (PlayerEventInfo player : getPlayers(instanceId))
		{
			// Remove event items from player's inventory
			removeEventItems(player);

			// Clear all Battle Royale circles for this player
			clearAllCirclesForPlayer(player);

			// Auto-arrange inventory after event cleanup with longer delay to prevent client issues
			if (player.isOnline() && player.getOwner() != null)
			{
				CallBack.getInstance().getOut().scheduleGeneral(() -> {
					if (player.isOnline() && player.getOwner() != null)
					{
						try
						{
							// Additional delay to ensure client is stable
							Thread.sleep(1000);
							autoArrangeInventory(player);
							//player.sendMessage("Hành trang của bạn đã được tự động sắp xếp lại sau event!");
						}
						catch (Exception e)
						{
							print("BattleRoyale: Error during inventory auto-arrange: " + e.getMessage());
						}
					}
				}, 5000); // 5 second delay to ensure all cleanup is complete and client is stable
			}
		}
	}

	// Enhanced cleanup all circle render tasks and tracking data (called when event ends)
	private void cleanupCircleRenderTasks()
	{
		int cancelledCount = 0;
		for (ScheduledFuture<?> task : _circleRenderTasks)
		{
			if (task != null && !task.isDone())
			{
				task.cancel(true);
				cancelledCount++;
			}
		}
		_circleRenderTasks.clear();

		// Clean up circle tracking data
		int clearedInstances = _activeCircleNames.size();
		_activeCircleNames.clear();
		_lastCircleClearTime.clear();

		if (GabrielEventsLoader.detailedDebug)
		{
			print("BattleRoyale: Enhanced cleanup completed - cancelled " + cancelledCount + " render tasks, cleared " + clearedInstances + " instance tracking data");
		}
	}

	/**
	 * Force start circle damage for testing (bypasses 2-minute wait)
	 */
	public void forceStartCircleDamage(int instanceId)
	{
		BREventInstance match = _matches.get(instanceId);
		if (match == null)
		{
			print("BattleRoyale: No match found for instance " + instanceId);
			return;
		}

		BRData data = match._data;
		if (data == null)
		{
			print("BattleRoyale: No data found for instance " + instanceId);
			return;
		}

		// Force set circle center and radius for immediate damage testing
		if (data._circleCenter == null)
		{
			// Use first circle center or create a default one
			if (!circleCenters.isEmpty())
			{
				data._circleCenter = circleCenters.get(0);
			}
			else
			{
				// Create a default circle center at spawn location
				List<EventSpawn> spawns = new ArrayList<>();
				int index = 0;
				while (true)
				{
					EventSpawn spawn = getSpawn(SpawnType.Regular, index);
					if (spawn == null) break;
					spawns.add(spawn);
					index++;
				}
				if (!spawns.isEmpty())
				{
					EventSpawn spawn = spawns.get(0);
					data._circleCenter = new Loc(spawn.getLoc().getX(), spawn.getLoc().getY(), spawn.getLoc().getZ());
				}
				else
				{
					print("BattleRoyale: Cannot force start - no spawn locations found");
					return;
				}
			}
		}

		// Set initial radius from first stage
		if (!stages.isEmpty())
		{
			data._circleRadius = stages.get(0).getRadius();
			data.currentStageIndex = 0;
		}
		else
		{
			data._circleRadius = 5000; // Default radius
		}

		data._circleActive = true;
		data.currentStageStartTime = System.currentTimeMillis();

		// Create and show circle
		BRCircleColor color = getCircleColorForStage(data.currentStageIndex, data._circleRadius);
		data.safeZone = new BRCircle(data._circleCenter.getX(), data._circleCenter.getY(), data._circleCenter.getZ(), data._circleRadius, color, data.currentStageIndex + 1);
		data.showCircleWithTracking(data.safeZone);

		// Notify players
		for (PlayerEventInfo player : getPlayers(instanceId))
		{
			if (player.isOnline())
			{
				player.sendMessage("FORCE START: Circle damage is now active! Circle center: (" + data._circleCenter.getX() + ", " + data._circleCenter.getY() + "), radius: " + data._circleRadius);
			}
		}

		// Immediate damage check
		data.checkPlayersOutsideCircle(instanceId);

		print("BattleRoyale: Force started circle damage for instance " + instanceId + " - Center: " + data._circleCenter + ", Radius: " + data._circleRadius);
	}

	/**
	 * Verify that circles have been properly cleared for an instance
	 */
	private boolean verifyCircleCleared(int instanceId)
	{
		try
		{
			Set<String> activeCircles = _activeCircleNames.getOrDefault(instanceId, ConcurrentHashMap.newKeySet());
			Long lastClearTime = _lastCircleClearTime.get(instanceId);

			// Check if we have any tracked circles
			boolean hasTrackedCircles = !activeCircles.isEmpty();

			// Check if enough time has passed since last clear
			boolean enoughTimePassed = lastClearTime == null ||
				(System.currentTimeMillis() - lastClearTime) >= CIRCLE_CLEAR_VERIFICATION_DELAY;

			boolean isCleared = !hasTrackedCircles && enoughTimePassed;

			if (GabrielEventsLoader.detailedDebug)
			{
				print("BattleRoyale: Circle clear verification for instance " + instanceId +
					" - hasTrackedCircles: " + hasTrackedCircles +
					", enoughTimePassed: " + enoughTimePassed +
					", isCleared: " + isCleared);
			}

			return isCleared;
		}
		catch (Exception e)
		{
			print("BattleRoyale: Error in circle clear verification: " + e.getMessage());
			return true; // Assume cleared on error to prevent blocking
		}
	}

	/**
	 * Force clear all circle tracking data for an instance
	 */
	private void forceCleanCircleTracking(int instanceId)
	{
		try
		{
			Set<String> activeCircles = _activeCircleNames.getOrDefault(instanceId, ConcurrentHashMap.newKeySet());
			int clearedCount = activeCircles.size();

			activeCircles.clear();
			_activeCircleNames.put(instanceId, activeCircles);
			_lastCircleClearTime.put(instanceId, System.currentTimeMillis());

			if (GabrielEventsLoader.detailedDebug)
			{
				print("BattleRoyale: Force cleaned " + clearedCount + " circle tracking entries for instance " + instanceId);
			}
		}
		catch (Exception e)
		{
			print("BattleRoyale: Error in force clean circle tracking: " + e.getMessage());
		}
	}

	// Get circle color based on stage state (PUBG-like logic)
	private BRCircleColor getCircleColorForStage(int stageIndex, int radius)
	{
		// PUBG-like logic:
		// - Early stages (large circles): WHITE (current safe zone)
		// - Mid stages (medium circles): BLUE (next safe zone preview)
		// - Final stages (small circles): WHITE (final safe zone)

		if (stageIndex < 0)
		{
			return BRCircleColor.WHITE; // Initial circle
		}

		if (stageIndex >= stages.size() - 2)
		{
			return BRCircleColor.WHITE; // Final stages - white for visibility
		}

		if (radius <= 1500)
		{
			return BRCircleColor.WHITE; // Small circles - white for final battles
		}

		if (radius <= 4000)
		{
			return BRCircleColor.BLUE; // Medium circles - blue for next zone preview
		}

		return BRCircleColor.WHITE; // Large circles - white for current safe zone
	}

	// Enhanced clear all Battle Royale circles for a specific player with comprehensive tracking
	private void clearAllCirclesForPlayer(PlayerEventInfo player)
	{
		if (!player.isOnline() || player.getOwner() == null) return;

		try
		{
			PlayerInstance playerInstance = player.getOwner();
			int instanceId = player.getInstanceId();
			Set<String> activeCircles = _activeCircleNames.getOrDefault(instanceId, ConcurrentHashMap.newKeySet());
			int clearedCount = 0;

			// Clear main BRCircle using AutoPlayTaskManager method
			ExServerPrimitive clearPacket = new ExServerPrimitive("BRCircle", playerInstance.getX(), playerInstance.getY(), playerInstance.getZ() + 65535);
			clearPacket.addPoint(0, 0, 0, 0); // AutoPlayTaskManager approach
			playerInstance.sendPacket(clearPacket);
			clearedCount++;

			// Clear all tracked circle names for this player
			for (String circleName : activeCircles)
			{
				ExServerPrimitive namedClearPacket = new ExServerPrimitive(circleName, 0, 0, -65535);
				namedClearPacket.addPoint(0, 0, 0, 0);
				playerInstance.sendPacket(namedClearPacket);
				clearedCount++;
			}

			// Clear legacy circle names (for backward compatibility)
			for (int stage = 1; stage <= stages.size(); stage++)
			{
				for (BRCircleColor color : BRCircleColor.values())
				{
					int circleId = -(110000 + stage * 10 + (color.ordinal() + 1));
					String circleName = "battleRoyaleCircle_" + circleId;
					ExServerPrimitive legacyClearPacket = new ExServerPrimitive(circleName, 0, 0, -65535);
					legacyClearPacket.addPoint(0, 0, 0, 0);
					playerInstance.sendPacket(legacyClearPacket);
					clearedCount++;
				}
			}

			if (GabrielEventsLoader.detailedDebug)
			{
				print("BattleRoyale: Enhanced clear completed for player " + player.getPlayersName() + " - cleared " + clearedCount + " circle packets");
			}
		}
		catch (Exception e)
		{
			print("BattleRoyale: Error in enhanced circle clear for player " + player.getPlayersName() + ": " + e.getMessage());
		}
	}

	// Test method to verify circle clearing works properly
	private void testCircleClearForPlayer(PlayerEventInfo player)
	{
		if (!player.isOnline() || player.getOwner() == null) return;

		PlayerInstance playerInstance = player.getOwner();

		// Send a test circle first
		ExServerPrimitive testCircle = new ExServerPrimitive("BRCircle", playerInstance.getX(), playerInstance.getY(), playerInstance.getZ() + 65535);
		testCircle.addLine(0xFF0000, playerInstance.getX() + 100, playerInstance.getY(), playerInstance.getZ(),
						   playerInstance.getX() - 100, playerInstance.getY(), playerInstance.getZ());
		playerInstance.sendPacket(testCircle);

		// Wait 2 seconds then clear it
		ThreadPool.schedule(() -> {
			clearAllCirclesForPlayer(player);
			if (GabrielEventsLoader.detailedDebug)
			{
				print("BattleRoyale: Test circle cleared for " + player.getPlayersName());
			}
		}, 2000);
	}

	// Comprehensive test method for circle functionality - call this to test
	public void testCircleFunctionality(PlayerEventInfo testPlayer)
	{
		if (!testPlayer.isOnline() || testPlayer.getOwner() == null) return;

		PlayerInstance player = testPlayer.getOwner();
		print("BattleRoyale: Starting circle functionality test for " + testPlayer.getPlayersName());

		// Test 1: Create and clear basic circle
		ThreadPool.schedule(() -> {
			print("BattleRoyale: Test 1 - Basic circle create/clear");
			ExServerPrimitive testCircle1 = new ExServerPrimitive("BRCircle", player.getX(), player.getY(), player.getZ() + 65535);
			for (int i = 0; i < 8; i++)
			{
				double angle1 = (i * 2 * Math.PI) / 8;
				double angle2 = ((i + 1) * 2 * Math.PI) / 8;
				int x1 = (int) (player.getX() + (500 * Math.cos(angle1)));
				int y1 = (int) (player.getY() + (500 * Math.sin(angle1)));
				int x2 = (int) (player.getX() + (500 * Math.cos(angle2)));
				int y2 = (int) (player.getY() + (500 * Math.sin(angle2)));
				testCircle1.addLine(0xFF0000, x1, y1, player.getZ(), x2, y2, player.getZ());
			}
			player.sendPacket(testCircle1);
			print("BattleRoyale: Test 1 - Circle created (red, radius 500)");
		}, 1000);

		// Test 2: Clear using AutoPlayTaskManager method
		ThreadPool.schedule(() -> {
			print("BattleRoyale: Test 2 - Clearing circle using AutoPlayTaskManager method");
			ExServerPrimitive clearPacket = new ExServerPrimitive("BRCircle", player.getX(), player.getY(), player.getZ() + 65535);
			clearPacket.addPoint(0, 0, 0, 0);
			player.sendPacket(clearPacket);
			print("BattleRoyale: Test 2 - Circle cleared");
		}, 3000);

		// Test 3: Create new circle with different color
		ThreadPool.schedule(() -> {
			print("BattleRoyale: Test 3 - New circle with different color");
			ExServerPrimitive testCircle2 = new ExServerPrimitive("BRCircle", player.getX(), player.getY(), player.getZ() + 65535);
			for (int i = 0; i < 12; i++)
			{
				double angle1 = (i * 2 * Math.PI) / 12;
				double angle2 = ((i + 1) * 2 * Math.PI) / 12;
				int x1 = (int) (player.getX() + (800 * Math.cos(angle1)));
				int y1 = (int) (player.getY() + (800 * Math.sin(angle1)));
				int x2 = (int) (player.getX() + (800 * Math.cos(angle2)));
				int y2 = (int) (player.getY() + (800 * Math.sin(angle2)));
				testCircle2.addLine(0x00FF00, x1, y1, player.getZ(), x2, y2, player.getZ());
			}
			player.sendPacket(testCircle2);
			print("BattleRoyale: Test 3 - New circle created (green, radius 800)");
		}, 5000);

		// Test 4: Final clear
		ThreadPool.schedule(() -> {
			print("BattleRoyale: Test 4 - Final clear");
			clearAllCirclesForPlayer(testPlayer);
			print("BattleRoyale: Circle functionality test completed for " + testPlayer.getPlayersName());
		}, 7000);
	}

	@RegisterEvent(club.projectessence.gameserver.model.events.EventType.ON_PLAYER_MOVE_REQUEST)
	@RegisterType(ListenerRegisterType.GLOBAL_PLAYERS)
	public TerminateReturn onPlayerMoveRequest(OnPlayerMoveRequest event)
	{
		PlayerEventInfo player = new PlayerEventInfo(event.getPlayer());
		if (!_matches.containsKey(player.getInstanceId()))
		{
			// Nếu người chơi không còn trong instance sự kiện, xóa vòng tròn
			if (player.getInstanceId() == 0)
			{
				clearAllCirclesForPlayer(player);
			}
			return null;
		}
		BREventInstance match = _matches.get(player.getInstanceId());
		if (match != null && match._data != null)
		{
			match._data.notifyPlayerPosition(player, event.getLocation());
		}
		return null; // Cho phép di chuyển
	}
	
	@Override
	protected void respawnPlayer(PlayerEventInfo pi, int instance)
	{}
	
	// Class hỗ trợ cho vòng tròn - sử dụng ExServerPrimitive approach từ AutoPlayTaskManager
	public class BRCircle
	{
		private final int			id;
		private final BRCircleColor	color;
		private final int			stageNumber;
		private final int			x, y, z, radius;
		private final String		name;

		public BRCircle(int x, int y, int z, int radius, BRCircleColor color, int stageNumber)
		{
			this.x = x;
			this.y = y;
			this.z = z; // Sử dụng Z thực tế từ CircleCenter
			this.radius = radius;
			this.color = color;
			this.stageNumber = stageNumber;
			this.id = -(110000 + stageNumber * 10 + (color.ordinal() + 1));
			this.name = "battleRoyaleCircle_" + id;
		}
		
		public BRCircle(BRCircle circle, BRCircleColor color, int stageNumber)
		{
			this(circle.x, circle.y, circle.z, circle.radius, color, stageNumber);
		}

		public int getRadius()
		{
			return radius;
		}

		public String getName()
		{
			return name;
		}
		
		public int getId()
		{
			return id;
		}
		
		public BRCircleColor getColor()
		{
			return color;
		}
		
		public int getStageNumber()
		{
			return stageNumber;
		}
		
		public int getX()
		{
			return x;
		}
		
		public int getY()
		{
			return y;
		}
		
		public int getZ()
		{
			return z;
		}
		


		// Alternative: ExShowTrace approach (works without cursed weapons)
		public ExShowTrace createTraceCircle()
		{
			ExShowTrace trace = new ExShowTrace();

			// Create circle points using trace markers
			int points = 16; // Fewer points for better performance
			for (int i = 0; i < points; i++)
			{
				double angle = (i * 2 * Math.PI) / points;
				int circleX = (int) (x + (radius * Math.cos(angle)));
				int circleY = (int) (y + (radius * Math.sin(angle)));
				trace.addLocation(circleX, circleY, z);
			}

			return trace;
		}

		// Enhanced ExServerPrimitive approach with height variation like AutoPlayTaskManager
		public ExServerPrimitive createSimpleCircle()
		{
			ExServerPrimitive primitive = new ExServerPrimitive(name, x, y, z + 65535);

			// Determine color based on circle type and radius
			int baseColor;
			switch (color)
			{
				case WHITE:
					baseColor = 0xFFFFFF; // White for current safe zone
					break;
				case BLUE:
					baseColor = 0x0080FF; // Light blue for next safe zone
					break;
				case DANGER:
					baseColor = 0xFF0000; // Red for danger zone
					break;
				default:
					baseColor = 0xFFFFFF; // Default white
					break;
			}

			// Enhanced color coding based on radius (distance from center)
			int circleColor;
			if (radius > 4000) {
				circleColor = 0x00FF00; // Green for outer circles (safe)
			} else if (radius > 2000) {
				circleColor = 0xFFFF00; // Yellow for middle circles (caution)
			} else if (radius > 800) {
				circleColor = 0xFF8000; // Orange for inner circles (danger)
			} else {
				circleColor = 0xFF0000; // Red for final circles (extreme danger)
			}

			// Use base color for consistency with PUBG style
			int finalColor = baseColor;

			// Multiple height levels for 3D effect
			int[] heightLevels = {
				z + 100,  // Ground level
				z + 200,  // Mid level
				z + 300   // High level
			};

			// Draw circle at multiple heights for 3D effect
			for (int heightLevel : heightLevels)
			{
				for (int i = 0; i < POINTS_IN_CIRCLE; i++)
				{
					double angle1 = (i * 2 * Math.PI) / POINTS_IN_CIRCLE;
					double angle2 = ((i + 1) * 2 * Math.PI) / POINTS_IN_CIRCLE;

					int x1 = (int) (x + (radius * Math.cos(angle1)));
					int y1 = (int) (y + (radius * Math.sin(angle1)));
					int x2 = (int) (x + (radius * Math.cos(angle2)));
					int y2 = (int) (y + (radius * Math.sin(angle2)));

					// Simple color variation by height for 3D effect
					int heightColor = finalColor;

					primitive.addLine(heightColor, x1, y1, heightLevel, x2, y2, heightLevel);
				}
			}

			// Enhanced with multiple height levels for better visibility

			return primitive;
		}





		// Visual Effects approach - using AbnormalVisualEffect (most stable!)
		public void applyVisualEffectCircle(PlayerEventInfo player)
		{
			if (player == null || !player.isOnline()) return;

			PlayerInstance playerInstance = player.getOwner();
			if (playerInstance == null) return;

			// Apply MAGIC_SQUARE effect to players near circle edge
			double distance = Math.sqrt(player.getPlanDistanceSq(x, y));
			boolean isNearEdge = Math.abs(distance - radius) < 500; // Within 500 units of edge

			if (isNearEdge)
			{
				// Apply visual effect to show circle boundary
				playerInstance.getEffectList().startAbnormalVisualEffect(AbnormalVisualEffect.MAGIC_SQUARE);
			}
			else
			{
				// Remove effect when away from edge
				playerInstance.getEffectList().stopAbnormalVisualEffect(AbnormalVisualEffect.MAGIC_SQUARE);
			}
		}

		// Item Drop Circle approach - like zone visualization
		public void createItemDropCircle(int instanceId)
		{
			final int STEP = 300; // Distance between items (increased for better performance)
			final int count = Math.min(50, (int) ((2 * Math.PI * radius) / STEP)); // Max 50 items to prevent lag
			final double angle = (2 * Math.PI) / count;

			for (int i = 0; i < count; i++)
			{
				int dropX = x + (int) (Math.cos(angle * i) * radius);
				int dropY = y + (int) (Math.sin(angle * i) * radius);

				// Drop temporary visual items (like adena) to mark circle
				ItemInstance droppedItem = CallBack.getInstance().getOut().dropItem(null, 57, 1, dropX, dropY, z + 50, 0, instanceId);
				if (droppedItem != null)
				{
					// Mark as temporary visual item
					droppedItem.setCustomType1(9998); // Different from event items (9999)
				}
			}
		}

		// Check if player is inside this circle
		public boolean isInside(PlayerEventInfo player)
		{
			if (player == null) return false;
			double distance = Math.sqrt(player.getPlanDistanceSq(x, y));
			return distance <= radius;
		}

		// Get distance from player to circle center
		public double getDistanceToCenter(PlayerEventInfo player)
		{
			if (player == null) return Double.MAX_VALUE;
			return Math.sqrt(player.getPlanDistanceSq(x, y));
		}
	}

	public enum BRCircleColor
	{
		WHITE,    // Current safe zone (like PUBG white circle)
		BLUE,     // Next safe zone (smaller white circle in PUBG)
		DANGER    // Blue zone (damage area outside circle)
	}

	public ConcurrentHashMap<Integer, ConcurrentHashMap<Integer, PlayerEventInfo>> getActivePlayers()
	{
		return activePlayers;
	}


}