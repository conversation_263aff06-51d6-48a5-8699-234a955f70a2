package gabriel.eventEngine.events.engine.main.events;

import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ScheduledFuture;

import club.projectessence.gameserver.model.skills.Skill;
import gabriel.eventEngine.events.GabrielEventsLoader;
import gabriel.eventEngine.events.engine.EventBuffer;
import gabriel.eventEngine.events.engine.EventManager;
import gabriel.eventEngine.events.engine.base.ConfigModel;
import gabriel.eventEngine.events.engine.base.EventMap;
import gabriel.eventEngine.events.engine.base.EventPlayerData;
import gabriel.eventEngine.events.engine.base.EventSpawn;
import gabriel.eventEngine.events.engine.base.EventType;
import gabriel.eventEngine.events.engine.base.Loc;
import gabriel.eventEngine.events.engine.base.PvPEventPlayerData;
import gabriel.eventEngine.events.engine.base.RewardPosition;
import gabriel.eventEngine.events.engine.base.SpawnType;
import gabriel.eventEngine.events.engine.lang.LanguageEngine;
import gabriel.eventEngine.events.engine.main.MainEventManager;
import gabriel.eventEngine.events.engine.main.base.MainEventInstanceType;
import gabriel.eventEngine.events.engine.main.features.ArtifactCaptureFeature;
import gabriel.eventEngine.events.engine.stats.GlobalStats;
import gabriel.eventEngine.events.engine.stats.GlobalStatsModel;
import gabriel.eventEngine.events.engine.team.EventTeam;
import gabriel.eventEngine.interf.PlayerEventInfo;
import gabriel.eventEngine.interf.callback.CallbackManager;
import gabriel.eventEngine.interf.delegate.CharacterData;
import gabriel.eventEngine.interf.delegate.InstanceData;
import gabriel.eventEngine.interf.delegate.NpcData;
import gabriel.eventEngine.interf.delegate.PartyData;
import gabriel.eventEngine.interf.delegate.SkillData;
import gabriel.eventEngine.l2j.CallBack;

/**
 * UpgradingEvent class implements a team-based event where two factions (Fire and Water)
 * compete to capture an Artifact using the TakeFortress skill.
 */
public class UpgradingEvent extends AbstractMainEvent
{
	protected Map<Integer, UpgradingEventInstance>	_matches;
	private static final int						ARTIFACT_ID				= 528;							// Artifact NPC ID
	private static final int						TAKE_FORTRESS_SKILL_ID	= 90024;						// TakeFortress skill ID
	private final Map<Integer, NpcData>				_artifacts				= new ConcurrentHashMap<>();	// Stores Artifact NPCs per instance
	private final Map<Integer, Integer>				_captureCounts			= new ConcurrentHashMap<>();	// Tracks capture counts per instance
	private final Map<Integer, ScheduledFuture<?>>	_despawnTasks			= new ConcurrentHashMap<>();	// Manages Artifact despawn tasks
	private final ArtifactCaptureFeature			artifactFeature;										// Manages Artifact capture configurations
	protected boolean								_waweRespawn;
	protected int									_teamsCount;
	
	/**
	 * Constructor for UpgradingEvent.
	 * Initializes basic event settings and ArtifactCaptureFeature.
	 */
	public UpgradingEvent(EventType type, MainEventManager manager)
	{
		super(type, manager);
		_matches = new ConcurrentHashMap<Integer, UpgradingEventInstance>();
		setRewardTypes(new RewardPosition[]
		{
			RewardPosition.Winner,
			RewardPosition.Looser,
			RewardPosition.Tie,
			RewardPosition.FirstBlood,
			RewardPosition.FirstRegistered,
			RewardPosition.OnKill,
			RewardPosition.KillingSpree
		});
		artifactFeature = new ArtifactCaptureFeature(type, "1,5"); // Initialize with 1 point per capture, 5 max captures
		_htmlDescription = "<font color=LEVEL>Upgrading Event:</font><br>" + "Two factions (Fire and Water) compete to capture the Artifact. The faction with the most captures wins.<br>" + "Use the TakeFortress skill to capture the Artifact.";
	}
	
	/**
	 * Loads event configurations.
	 */
	@Override
	public void loadConfigs()
	{
		super.loadConfigs();
		addConfig(new ConfigModel("killsForReward", "0", "The minimum kills count required to get a reward (includes all possible rewards)."));
		addConfig(new ConfigModel("resDelay", "15", "The delay after which the player is resurrected. In seconds."));
		addConfig(new ConfigModel("waweRespawn", "true", "Enables the wave-style respawn system.", ConfigModel.InputType.Boolean));
		addConfig(new ConfigModel("createParties", "true", "Put 'True' if you want this event to automatically create parties for players in each team.", ConfigModel.InputType.Boolean));
		addConfig(new ConfigModel("maxPartySize", "9", "The maximum size of party, that can be created. Works only if <font color=LEVEL>createParties</font> is true."));
		addConfig(new ConfigModel("teamsCount", "2", "The count of teams in the event. Fixed to 2 for Fire and Water factions."));
		addConfig(new ConfigModel("firstBloodMessage", "true", "You can turn off/on the first blood announce in the event (first kill made in the event). This is also rewardable - check out reward type FirstBlood.", ConfigModel.InputType.Boolean));
		addConfig(new ConfigModel("individualCapturePoints", "1", "Points awarded to the individual player for capturing the Artifact."));
		addInstanceTypeConfig(new ConfigModel("teamsCount", "2", "Fixed to 2 for Fire and Water factions."));
	}
	
	/**
	 * Getter for _artifacts map.
	 */
	public Map<Integer, NpcData> getArtifacts()
	{
		return _artifacts;
	}
	
	@Override
	public void initEvent()
	{
		super.initEvent();
		_waweRespawn = getBoolean("waweRespawn");
		if (_waweRespawn)
		{
			initWaweRespawns(getInt("resDelay"));
		}
		_runningInstances = 0;
	}
	
	public String getTeamName(int id)
	{
		for (Map<Integer, EventTeam> i : _teams.values())
		{
			for (EventTeam team : i.values())
			{
				if (team.getTeamId() == id)
				{
					return team.getTeamName();
				}
			}
		}
		return "Unknown";
	}
	
	@Override
	public int getTeamsCount()
	{
		return getInt("teamsCount");
	}
	
	/**
	 * Creates player data for the given player.
	 */
	@Override
	public EventPlayerData createPlayerData(PlayerEventInfo player)
	{
		return new PvPEventPlayerData(player, this, new GlobalStatsModel(getEventType()));
	}
	
	/**
	 * Retrieves player data for the given player.
	 */
	@Override
	public PvPEventPlayerData getPlayerData(PlayerEventInfo player)
	{
		return (PvPEventPlayerData) player.getEventData();
	}
	
	/**
	 * Clears all event data, including all instances and player data.
	 */
	@Override
	public synchronized void clearEvent()
	{
		if (GabrielEventsLoader.detailedDebug)
		{
			print("Event: called global clearEvent()");
		}
		clearEvent(0);
	}
	
	/**
	 * Clears event data for the specified instance or all instances.
	 */
	@Override
	public synchronized void clearEvent(int instanceId)
	{
		if (GabrielEventsLoader.detailedDebug)
		{
			print("UpgradingEvent: Clearing event for instance " + instanceId);
		}
		try
		{
			if (_matches != null)
			{
				for (UpgradingEventInstance match : _matches.values())
				{
					if (instanceId == 0 || instanceId == match.getInstance().getId())
					{
						match.abort();
					}
				}
			}
		}
		catch (Exception e)
		{
			e.printStackTrace();
		}
		for (PlayerEventInfo player : getPlayers(instanceId))
		{
			if (player.isOnline())
			{
				if (player.isParalyzed())
				{
					player.setIsParalyzed(false);
				}
				if (player.isImmobilized())
				{
					player.unroot();
				}
				if (!player.isGM())
				{
					player.setIsInvul(false);
				}
				player.removeRadarAllMarkers();
				player.setInstanceId(0);
				// Remove the TakeFortress skill
				player.removeSkill(TAKE_FORTRESS_SKILL_ID);
				// System.out.println("UpgradingEvent: Removed skill " + TAKE_FORTRESS_SKILL_ID + " from player " + player.getPlayersName());
				// Remove buffs if configured
				if (_removeBuffsOnEnd)
				{
					player.removeBuffs();
				}
				player.restoreData();
				// Teleport player back to their original location (set at registration)
				Loc origLoc = player.getOrigLoc();
				// System.out.println("UpgradingEvent: Player " + player.getPlayersName() + " original location before teleport: (" + origLoc.getX() + ", " + origLoc.getY() + ", " + origLoc.getZ() + ")");
				player.teleToLocation(origLoc.getX(), origLoc.getY(), origLoc.getZ(), true);
				player.sendMessage(LanguageEngine.getMsg("event_teleportBack"));
				// System.out.println("UpgradingEvent: Teleported player " + player.getPlayersName() + " to original location (" + origLoc.getX() + ", " + origLoc.getY() + ", " + origLoc.getZ() + ")");
				if (player.getParty() != null)
				{
					PartyData party = player.getParty();
					party.removePartyMember(player);
				}
				player.broadcastUserInfo();
			}
		}
		clearPlayers(true, instanceId);
	}
	
	/**
	 * Handles a kill event.
	 */
	@Override
	public void onKill(PlayerEventInfo player, CharacterData target)
	{
		if (target.getEventInfo() == null)
		{
			return;
		}
		PvPEventPlayerData playerData = getPlayerData(player);
		if (playerData != null)
		{
			tryFirstBlood(player);
			giveOnKillReward(player);
			player.getEventTeam().raiseScore(1);
			player.getEventTeam().raiseKills(1);
			getPlayerData(player).raiseScore(1);
			getPlayerData(player).raiseKills(1);
			getPlayerData(player).raiseSpree(1);
			giveKillingSpreeReward(getPlayerData(player));
			player.sendMessage(LanguageEngine.getMsg("event_player_killed", target.getEventInfo().getPlayersName()));
			if (player.isTitleUpdated())
			{
				player.setTitle(getTitle(player), true);
				player.broadcastTitleInfo();
			}
			CallbackManager.getInstance().playerKills(getEventType(), player, target.getEventInfo());
			setScoreStats(player, getPlayerData(player).getScore());
			setKillsStats(player, getPlayerData(player).getKills());
		}
	}
	
	/**
	 * Handles a death event.
	 */
	@Override
	public void onDie(PlayerEventInfo player, CharacterData killer)
	{
		PvPEventPlayerData playerData = getPlayerData(player);
		if (playerData != null)
		{
			playerData.raiseDeaths(1);
			playerData.setSpree(0);
			setDeathsStats(player, playerData.getDeaths());
			if (player.isTitleUpdated())
			{
				player.setTitle(getTitle(player), true);
				player.broadcastTitleInfo();
			}
			if (_waweRespawn)
			{
				_waweScheduler.addPlayer(player);
			}
			else
			{
				scheduleRevive(player, getInt("resDelay") * 1000);
			}
		}
	}
	
	/**
	 * Handles NPC interaction.
	 */
	@Override
	public boolean onNpcAction(PlayerEventInfo player, NpcData npc)
	{
		if (npc.getNpcId() == ARTIFACT_ID)
		{
			// System.out.println("UpgradingEvent: Player " + player.getPlayersName() + " interacted with Artifact NPC ID: " + ARTIFACT_ID);
			player.sendMessage(LanguageEngine.getMsg("event_use_takeFortress"));
			return true;
		}
		return false;
	}
	
	/**
	 * Validates if the player can use a skill (specifically TakeFortress).
	 */
	@Override
	public boolean canUseSkill(PlayerEventInfo player, SkillData skill)
	{
		if (skill.getId() == TAKE_FORTRESS_SKILL_ID)
		{
			// System.out.println("UpgradingEvent: canUseSkill called for player " + player.getPlayersName() + ", skill ID: " + skill.getId());
			if (player.getEventTeam() == null || _artifacts.get(player.getInstanceId()) == null)
			{
				// System.out.println("UpgradingEvent: canUseSkill failed - No team or no artifact in instance " + player.getInstanceId());
				player.sendMessage("This skill cannot be used here!");
				return false;
			}
			NpcData artifact = _artifacts.get(player.getInstanceId());
			if (!isInRange(player, artifact, 185))
			{
				// System.out.println("UpgradingEvent: canUseSkill failed - Player out of range from artifact");
				player.sendMessage("You are too far from the target!");
				return false;
			}
			// System.out.println("UpgradingEvent: canUseSkill passed for player " + player.getPlayersName());
			return true;
		}
		return super.canUseSkill(player, skill);
	}
	
	/**
	 * Handles skill usage, triggering Artifact capture for TakeFortress skill.
	 */
	@Override
	public void onSkillUse(PlayerEventInfo player, SkillData skill)
	{
		if (skill.getId() == TAKE_FORTRESS_SKILL_ID)
		{
			// System.out.println("UpgradingEvent: onSkillUse triggered for player " + player.getPlayersName() + ", skill ID: " + skill.getId());
			UpgradingEventInstance instance = (UpgradingEventInstance) getMatch(player.getInstanceId());
			if (instance != null)
			{
				// System.out.println("UpgradingEvent: Calling onArtifactCapture for instance " + player.getInstanceId());
				instance.onArtifactCapture(player);
			}
			else
			{
				// System.out.println("UpgradingEvent: Instance is null for ID " + player.getInstanceId());
			}
		}
		super.onSkillUse(player, skill);
	}
	
	/**
	 * Checks if the player is within range of a location.
	 */
	private boolean isInRange(PlayerEventInfo player, NpcData npc, int radius)
	{
		double dx = player.getX() - npc.getLoc().getX();
		double dy = player.getY() - npc.getLoc().getY();
		double dz = player.getZ() - npc.getLoc().getZ();
		double distance = Math.sqrt(dx * dx + dy * dy + dz * dz);
		// System.out.println("UpgradingEvent: Distance to artifact: " + distance + ", required radius: " + radius);
		return distance <= radius;
	}
	
	/**
	 * Returns the number of Artifact captures for an instance.
	 */
	public int getCaptureCount(int instanceId)
	{
		return _captureCounts.getOrDefault(instanceId, 0);
	}
	
	/**
	 * Starts the event by creating instances and scheduling tasks.
	 */
	@Override
	public void runEvent()
	{
		if (GabrielEventsLoader.detailedDebug)
		{
			print("Event: started runEvent()");
		}
		if (!dividePlayers())
		{
			clearEvent();
			return;
		}
		_matches.clear();
		for (InstanceData instance : _instances)
		{
			if (GabrielEventsLoader.detailedDebug)
			{
				print("Event: creating eventinstance for instance " + instance.getId());
			}
			UpgradingEventInstance match = createEventInstance(instance);
			_matches.put(instance.getId(), match);
			_runningInstances++;
			match.scheduleNextTask(0);
			if (GabrielEventsLoader.detailedDebug)
			{
				print("Event: event instance started");
			}
		}
		if (GabrielEventsLoader.detailedDebug)
		{
			print("Event: finished runEvent()");
		}
	}
	
	/**
	 * Handles event termination, ending all instances.
	 */
	@Override
	public void onEventEnd()
	{
		if (GabrielEventsLoader.detailedDebug)
		{
			print("Event: onEventEnd()");
		}
		int minKills = getInt("killsForReward");
		rewardAllTeams(-1, minKills, minKills);
	}
	
	/**
	 * Clears instance-specific data (Artifacts, despawn tasks, capture counts).
	 */
	private void clearInstance(int instanceId)
	{
		if (_artifacts.containsKey(instanceId))
		{
			_artifacts.get(instanceId).deleteMe();
			_artifacts.remove(instanceId);
		}
		if (_despawnTasks.containsKey(instanceId))
		{
			_despawnTasks.get(instanceId).cancel(false);
			_despawnTasks.remove(instanceId);
		}
		_captureCounts.remove(instanceId);
	}
	
	/**
	 * Checks if all instances have ended.
	 */
	@Override
	protected boolean instanceEnded()
	{
		_runningInstances--;
		if (GabrielEventsLoader.detailedDebug)
		{
			print("Event: notifying instance ended: runningInstances = " + _runningInstances);
		}
		if (_runningInstances == 0)
		{
			_manager.end();
			return true;
		}
		return false;
	}
	
	/**
	 * Ends an instance, optionally rewarding players.
	 */
	@Override
	protected synchronized void endInstance(int instance, boolean canBeAborted, boolean canRewardIfAborted, boolean forceNotReward)
	{
		if (GabrielEventsLoader.detailedDebug)
		{
			print("Event: endInstance() " + instance + ", canBeAborted " + canBeAborted + ", canReward.. " + canRewardIfAborted + " forceNotReward " + forceNotReward);
		}
		if (forceNotReward)
		{
			_matches.get(instance).forceNotRewardThisInstance();
		}
		_matches.get(instance).setNextState(EventState.END);
		if (canBeAborted)
		{
			_matches.get(instance).setCanBeAborted();
		}
		if (canRewardIfAborted)
		{
			_matches.get(instance).setCanRewardIfAborted();
		}
		_matches.get(instance).scheduleNextTask(0);
	}
	
	/**
	 * Respawns a player to their faction's spawn point.
	 */
	@Override
	protected void respawnPlayer(PlayerEventInfo player, int instanceId)
	{
		if (GabrielEventsLoader.detailedDebug)
		{
			print("/// Event: respawning player " + player.getPlayersName() + ", instance " + instanceId);
		}
		EventSpawn spawn = getSpawn(SpawnType.Regular, player.getTeamId());
		if (spawn != null)
		{
			Loc loc = new Loc(spawn.getLoc().getX(), spawn.getLoc().getY(), spawn.getLoc().getZ());
			loc.addRadius(spawn.getRadius());
			player.teleport(loc, 0, true, instanceId);
			player.sendMessage(LanguageEngine.getMsg("event_respawned"));
		}
		else
		{
			debug("Error on respawnPlayer - no spawn type REGULAR, team " + player.getTeamId() + " has been found. Event aborted.");
		}
	}
	
	@Override
	public String getEstimatedTimeLeft()
	{
		if (_matches == null)
		{
			return "Starting";
		}
		for (UpgradingEventInstance match : _matches.values())
		{
			if (match.isActive())
			{
				return match.getClock().getTime();
			}
		}
		return "N/A";
	}
	
	/**
	 * Generates the scorebar HTML for the event.
	 */
	@Override
	protected String getScorebar(int instance)
	{
		final int count = _teams.get(instance).size();
		StringBuilder tb = new StringBuilder();
		for (EventTeam team : _teams.get(instance).values())
		{
			if (count <= 4)
			{
				tb.append(team.getTeamName() + ": " + team.getScore() + "  ");
			}
			else
			{
				tb.append(team.getTeamName().substring(0, 1) + ": " + team.getScore() + "  ");
			}
		}
		if (count <= 3)
		{
			tb.append(LanguageEngine.getMsg("event_scorebar_time", _matches.get(instance).getClock().getTime()));
		}
		return tb.toString();
	}
	
	/**
	 * Generates event information for Community Board display.
	 */
	@Override
	public String getEventInfoCb(int instanceId, Object param)
	{
		// System.out.println("UpgradingEvent: Generating event info for Community Board, instance " + instanceId);
		StringBuilder tb = new StringBuilder();
		tb.append("<table width=510 bgcolor=2E2E2E><tr><td width=510 align=center><font color=ac9887>Faction Scores:</font></td></tr></table>");
		for (EventTeam team : _teams.get(instanceId).values())
		{
			String color = EventManager.getInstance().getTeamColorForHtml(team.getTeamId());
			tb.append("<table width=510><tr><td width=510><font color=").append(color).append(">").append(team.getTeamName()).append("</font>: ").append(team.getScore()).append(" points</td></tr></table>");
			// System.out.println("UpgradingEvent: Community Board - Team " + team.getTeamName() + ": " + team.getScore() + " points, color: " + color);
		}
		tb.append("<table width=510><tr><td width=510><font color=9f9f9f>Captures: ").append(_captureCounts.getOrDefault(instanceId, 0)).append("</font></td></tr></table>");
		String cbContent = tb.toString();
		// System.out.println("UpgradingEvent: Community Board content: " + cbContent);
		return cbContent;
	}
	
	/**
	 * Generates player title with their individual score.
	 */
	@Override
	protected String getTitle(PlayerEventInfo pi)
	{
		if (pi.isAfk())
		{
			return "AFK";
		}
		PvPEventPlayerData playerData = getPlayerData(pi);
		if (playerData == null)
		{
			return "Score: 0";
		}
		return "Score: " + playerData.getScore();
	}
	
	/**
	 * Sets up player titles and adds the TakeFortress skill.
	 */
	@Override
	public void setupTitles(int instanceId)
	{
		if (GabrielEventsLoader.detailedDebug)
		{
			print("UpgradingEvent: SETUPING TITLES");
		}
		for (PlayerEventInfo player : getPlayers(instanceId))
		{
			if (_allowSchemeBuffer)
			{
				EventBuffer.getInstance().buffPlayer(player, true);
			}
			if (_allowNoblesOnRess)
			{
				Skill noblesse = club.projectessence.gameserver.data.xml.SkillData.getInstance().getSkill(19119, 1);
				if (noblesse != null)
				{
					noblesse.applyEffects(player.getOwner(), player.getOwner());
				}
			}
			if (_removePartiesOnStart)
			{
				PartyData pt = player.getParty();
				if (pt != null)
				{
					player.getParty().removePartyMember(player);
				}
			}
			// Add the TakeFortress skill to the player
			Skill skill = club.projectessence.gameserver.data.xml.SkillData.getInstance().getSkill(TAKE_FORTRESS_SKILL_ID, 1);
			if (skill != null)
			{
				SkillData skillData = new SkillData(skill);
				player.addSkill(skillData, true);
				// System.out.println("UpgradingEvent: Added skill " + TAKE_FORTRESS_SKILL_ID + " to player " + player.getPlayersName() + " during setupTitles");
			}
			else
			{
				// System.out.println("UpgradingEvent: Failed to add skill " + TAKE_FORTRESS_SKILL_ID + " to player " + player.getPlayersName() + " - Skill not found");
			}
			if (player.isTitleUpdated())
			{
				player.setTitle(getTitle(player), true);
			}
		}
	}
	
	/**
	 * Returns the HTML description of the event.
	 */
	@Override
	public String getHtmlDescription()
	{
		return _htmlDescription;
	}
	
	/**
	 * Creates an event instance for the given instance data.
	 */
	@Override
	protected UpgradingEventInstance createEventInstance(InstanceData instance)
	{
		return new UpgradingEventInstance(instance);
	}
	
	/**
	 * Retrieves the event instance for the given instance ID.
	 */
	@Override
	public AbstractEventInstance getMatch(int instanceId)
	{
		return _matches.get(instanceId);
	}
	
	@Override
	protected int initInstanceTeams(MainEventInstanceType type)
	{
		_teamsCount = type.getConfigInt("teamsCount");
		if (_teamsCount < 2 || _teamsCount > 5)
		{
			_teamsCount = getInt("teamsCount");
		}
		if (_teamsCount < 2 || _teamsCount > 5)
		{
			_teamsCount = 2;
		}
		createTeams(_teamsCount, type.getInstance().getId());
		return _teamsCount;
	}
	
	/**
	 * Creates teams for the event instance.
	 */
	@Override
	protected void createTeams(int count, int instanceId)
	{
		if (GabrielEventsLoader.detailedDebug)
		{
			print("UpgradingEvent: creating " + count + " teams for instanceId " + instanceId);
		}
		if (count != 2)
		{
			// System.out.println("UpgradingEvent: Invalid team count " + count + ", forcing 2 teams for Fire and Water");
			count = 2;
		}
		_teams.put(instanceId, new ConcurrentHashMap<>());
		createNewTeam(instanceId, 1, "Fire", "Fire Faction");
		createNewTeam(instanceId, 2, "Water", "Water Faction");
		// System.out.println("UpgradingEvent: Created teams for instance " + instanceId + ": Fire (teamId=1), Water (teamId=2)");
	}
	
	/**
	 * Checks for missing spawns required for the event.
	 */
	@Override
	public String getMissingSpawns(EventMap map)
	{
		StringBuilder tb = new StringBuilder();
		for (int i = 0; i < getTeamsCount(); i++)
		{
			if (!map.checkForSpawns(SpawnType.Regular, i + 1, 1))
			{
				tb.append(addMissingSpawn(SpawnType.Regular, i + 1, 1));
			}
			if (!map.checkForSpawns(SpawnType.Artifact, -1, 1))
			{
				tb.append(addMissingSpawn(SpawnType.Artifact, -1, 1));
			}
		}
		return tb.toString();
	}
	
	protected enum EventState
	{
		START,
		FIGHT,
		END,
		TELEPORT,
		INACTIVE;
	}
	
	/**
	 * Creates event data for the given instance.
	 */
	@Override
	protected UpgradingEventData createEventData(int instanceId)
	{
		return new UpgradingEventData(instanceId);
	}
	
	/**
	 * Retrieves event data for the given instance.
	 */
	@Override
	protected UpgradingEventData getEventData(int instanceId)
	{
		return _matches.get(instanceId)._data;
	}
	
	/**
	 * Inner class representing an instance of the UpgradingEvent.
	 */
	public class UpgradingEventInstance extends AbstractEventInstance
	{
		protected EventState			_state;
		protected UpgradingEventData	_data;
		
		public UpgradingEventInstance(InstanceData instance)
		{
			super(instance);
			_state = EventState.START;
			_data = createEventData(instance.getId());
		}
		
		protected void setNextState(EventState state)
		{
			_state = state;
		}
		
		@Override
		public boolean isActive()
		{
			return _state != EventState.INACTIVE;
		}
		
		@Override
		public void run()
		{
			if (GabrielEventsLoader.detailedDebug)
			{
				print("UpgradingEventInstance: Starting instance " + _instance.getId());
			}
			switch (_state)
			{
				case START:
				{
					if (checkPlayers(_instance.getId()))
					{
						teleportPlayers(_instance.getId(), SpawnType.Regular, false);
						// System.out.println("UpgradingEvent: After teleportPlayers, teams status for instance " + _instance.getId() + ":");
						for (Map.Entry<Integer, EventTeam> entry : _teams.getOrDefault(_instance.getId(), new ConcurrentHashMap<>()).entrySet())
						{
							EventTeam team = entry.getValue();
							// System.out.println("UpgradingEvent: Team " + team.getTeamName() + " has " + team.getPlayers().size() + " players");
							for (PlayerEventInfo player : team.getPlayers())
							{
								// System.out.println("UpgradingEvent: Team " + team.getTeamName() + " contains player " + player.getPlayersName());
							}
						}
						setupTitles(_instance.getId());
						enableMarkers(_instance.getId(), true);
						spawnArtifact(_instance.getId());
						forceSitAll(_instance.getId());
						setNextState(EventState.FIGHT);
						scheduleNextTask(10000);
					}
					break;
				}
				case FIGHT:
				{
					forceStandAll(_instance.getId());
					if (getBoolean("createParties"))
					{
						createParties(getInt("maxPartySize"));
					}
					setNextState(EventState.END);
					_clock.startClock(_manager.getRunTime());
					break;
				}
				case END:
				{
					_clock.setTime(0, true);
					setNextState(EventState.INACTIVE);
					if (!instanceEnded() && _canBeAborted)
					{
						if (_canRewardIfAborted)
						{
							rewardAllTeams(_instance.getId(), getInt("killsForReward"), getInt("killsForReward"));
						}
						clearEvent(_instance.getId());
					}
					break;
				}
			}
		}
		
		/**
		 * Spawns the Artifact NPC for the instance.
		 */
		private void spawnArtifact(int instanceId)
		{
			EventSpawn spawn = getSpawn(SpawnType.Artifact, -1);
			if (spawn == null)
			{
				// System.out.println("UpgradingEvent: No Artifact spawn found for instance " + instanceId);
				endInstance(instanceId, true, false, true);
				return;
			}
			// System.out.println("UpgradingEvent: Attempting to spawn Artifact at instance " + instanceId + ", NPC ID: " + ARTIFACT_ID + ", Map ID: " + spawn.getMapId() + ", Location: (" + spawn.getLoc().getX() + ", " + spawn.getLoc().getY() + ", " + spawn.getLoc().getZ() + ")");
			NpcData artifact = spawnNPC(spawn.getLoc().getX(), spawn.getLoc().getY(), spawn.getLoc().getZ(), ARTIFACT_ID, instanceId, "Artifact", "Upgrading Event");
			if (artifact != null)
			{
				// System.out.println("UpgradingEvent: Artifact spawned at instance " + instanceId + ", NPC ID: " + ARTIFACT_ID);
				_artifacts.put(instanceId, artifact);
			}
			else
			{
				// System.out.println("UpgradingEvent: Failed to spawn Artifact for instance " + instanceId + ". Possible reasons: Invalid NPC ID, invalid coordinates, or instance issue.");
			}
		}
		
		/**
		 * Handles Artifact capture by a player.
		 */
		public void onArtifactCapture(PlayerEventInfo player)
		{
			// System.out.println("UpgradingEvent: onArtifactCapture called for player " + player.getPlayersName() + ", instance ID: " + _instance.getId());
			EventTeam capturingTeam = player.getEventTeam();
			if (capturingTeam == null)
			{
				// System.out.println("UpgradingEvent: No team for player " + player.getPlayersName());
				return;
			}
			// Ensure player data exists
			PvPEventPlayerData capturerData = getPlayerData(player);
			if (capturerData == null)
			{
				// System.out.println("UpgradingEvent: Capturer data not found for " + player.getPlayersName() + ", creating new data");
				capturerData = (PvPEventPlayerData) createPlayerData(player);
			}
			// Add individual points for capturing the Artifact
			int individualPoints = getInt("individualCapturePoints");
			capturerData.raiseScore(individualPoints);
			// System.out.println("UpgradingEvent: Player " + player.getPlayersName() + " received " + individualPoints + " individual points for capturing the Artifact, new score: " + capturerData.getScore());
			player.sendMessage("You received " + individualPoints + " points for capturing the Artifact!");
			// Add team points
			int teamPoints = artifactFeature.getCapturePoints();
			capturingTeam.raiseScore(teamPoints);
			// System.out.println("UpgradingEvent: Team " + capturingTeam.getTeamName() + " score increased by " + teamPoints + ", new score: " + capturingTeam.getScore());
			_captureCounts.compute(_instance.getId(), (k, v) -> v == null ? 1 : v + 1);
			announce(_instance.getId(), LanguageEngine.getMsg("event_team_announceWinner1", capturingTeam.getTeamName()) + " Score: " + capturingTeam.getScore());
			player.sendMessage(LanguageEngine.getMsg("event_artifact_captured"));
			// Update stats for the capturer only
			capturerData.getGlobalStats().raise(GlobalStats.GlobalStatType.ARTIFACT_CAPTURES, 1);
			// Update titles for all team members
			for (PlayerEventInfo pi : capturingTeam.getPlayers())
			{
				if (pi.isOnline() && pi.isTitleUpdated())
				{
					pi.setTitle(getTitle(pi), true);
					pi.broadcastTitleInfo();
				}
			}
			// Teleport all players from both teams back to their initial spawn points, avoiding duplicates
			Set<PlayerEventInfo> uniquePlayers = new HashSet<>();
			for (EventTeam team : _teams.get(_instance.getId()).values())
			{
				for (PlayerEventInfo teamPlayer : team.getPlayers())
				{
					if (teamPlayer.isOnline() && uniquePlayers.add(teamPlayer))
					{
						respawnPlayer(teamPlayer, _instance.getId());
						// System.out.println("UpgradingEvent: Teleported player " + teamPlayer.getPlayersName() + " from team " + team.getTeamName() + " back to initial spawn point.");
					}
				}
			}
			announce(_instance.getId(), "All players have been teleported back to their starting positions!");
			// Clear the current Artifact and schedule a new spawn
			clearInstance(_instance.getId());
			_despawnTasks.put(_instance.getId(), CallBack.getInstance().getOut().scheduleGeneral(() -> spawnArtifact(_instance.getId()), 5000L));
			if (_captureCounts.getOrDefault(_instance.getId(), 0) >= artifactFeature.getMaxCaptures())
			{
				endInstance(_instance.getId(), false, true, false);
			}
		}
	}
	
	/**
	 * Inner class representing event data for an instance.
	 */
	protected class UpgradingEventData extends AbstractEventData
	{
		public UpgradingEventData(int instance)
		{
			super(instance);
		}
	}
}