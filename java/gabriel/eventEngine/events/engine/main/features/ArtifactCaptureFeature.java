package gabriel.eventEngine.events.engine.main.features;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import gabriel.eventEngine.events.engine.base.ConfigModel;
import gabriel.eventEngine.events.engine.base.EventType;

public class ArtifactCaptureFeature
{
	private int								capturePoints;
	private int								maxCaptures;
	private final Map<String, ConfigModel>	configs	= new ConcurrentHashMap<>();
	
	public ArtifactCaptureFeature(EventType event, String parametersString)
	{
		capturePoints = 1;
		maxCaptures = 5;
		configs.put("CapturePoints", new ConfigModel("CapturePoints", "100", "Points awarded to the team for capturing the Artifact.", ConfigModel.InputType.TextEdit));
		configs.put("MaxCaptures", new ConfigModel("MaxCaptures", "5", "Maximum number of Artifact captures before the event ends.", ConfigModel.InputType.TextEdit));
		if (parametersString != null && !parametersString.isEmpty())
		{
			initValues(parametersString);
		}
	}
	
	private void initValues(String parametersString)
	{
		String[] params = parametersString.split(",");
		try
		{
			capturePoints = Integer.parseInt(params[0]);
			maxCaptures = Integer.parseInt(params[1]);
			configs.get("CapturePoints").setValue(params[0]);
			configs.get("MaxCaptures").setValue(params[1]);
		}
		catch (NumberFormatException e)
		{
			e.printStackTrace();
		}
	}
	
	public int getCapturePoints()
	{
		return capturePoints;
	}
	
	public int getMaxCaptures()
	{
		return maxCaptures;
	}
	
	public Map<String, ConfigModel> getConfigs()
	{
		return configs;
	}
}