package gabriel.eventEngine.events.engine.stats;

import gabriel.eventEngine.interf.PlayerEventInfo;

public class EventSpecificStats extends EventStats
{
    @Override
    public void load() {
    }
    
    @Override
    public void onLogin(final PlayerEventInfo player) {
    }
    
    @Override
    public void onDisconnect(final PlayerEventInfo player) {
    }
    
    @Override
    public void onCommand(final PlayerEventInfo player, final String command) {
    }
    
    @Override
    public void statsChanged(final PlayerEventInfo player) {
    }
}
