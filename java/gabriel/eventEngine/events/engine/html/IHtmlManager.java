package gabriel.eventEngine.events.engine.html;

import gabriel.eventEngine.interf.PlayerEventInfo;
import gabriel.eventEngine.interf.delegate.NpcData;

public interface IHtmlManager {
    boolean showNpcHtml(PlayerEventInfo paramPlayerEventInfo, NpcData paramNpcData);

    boolean onBypass(PlayerEventInfo paramPlayerEventInfo, String paramString);

    boolean onCbBypass(PlayerEventInfo paramPlayerEventInfo, String paramString);
}


