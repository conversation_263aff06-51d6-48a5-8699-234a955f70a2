package gabriel.eventEngine.events.engine.base;

import gabriel.eventEngine.events.EventGame;
import gabriel.eventEngine.events.engine.stats.GlobalStats;
import gabriel.eventEngine.events.engine.stats.GlobalStatsModel;
import gabriel.eventEngine.interf.PlayerEventInfo;

public class EventPlayerData {
	private final PlayerEventInfo _owner;
	protected GlobalStatsModel _globalStats;
	private int _score;
	
	public EventPlayerData(PlayerEventInfo owner, EventGame event, GlobalStatsModel stats) {
		_owner = owner;
		_globalStats = stats;
	}
	
	public PlayerEventInfo getOwner() {
		return _owner;
	}
	
	public int getScore() {
		return _score;
	}
	
	public int raiseScore(int i) {
		_score += i;
		_globalStats.raise(GlobalStats.GlobalStatType.SCORE, i);
		return _score;
	}
	
	public void setScore(int i) {
		_score = i;
		_globalStats.set(GlobalStats.GlobalStatType.SCORE, i);
	}
	
	public GlobalStatsModel getGlobalStats() {
		return _globalStats;
	}
}
