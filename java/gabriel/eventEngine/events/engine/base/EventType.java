package gabriel.eventEngine.events.engine.base;

import java.lang.reflect.Constructor;
import java.util.LinkedList;
import java.util.List;
import java.util.logging.Level;

import gabriel.eventEngine.events.GabrielEventsLoader;
import gabriel.eventEngine.events.engine.EventConfig;
import gabriel.eventEngine.events.engine.EventManager;
import gabriel.eventEngine.events.engine.EventMapSystem;
import gabriel.eventEngine.events.engine.main.MainEventManager;
import gabriel.eventEngine.events.engine.main.events.BattleRoyale;
import gabriel.eventEngine.events.engine.main.events.Battlefield;
import gabriel.eventEngine.events.engine.main.events.CaptureTheFlag;
import gabriel.eventEngine.events.engine.main.events.Deathmatch;
import gabriel.eventEngine.events.engine.main.events.Domination;
import gabriel.eventEngine.events.engine.main.events.HuntingGrounds;
import gabriel.eventEngine.events.engine.main.events.LastManStanding;
import gabriel.eventEngine.events.engine.main.events.LuckyChests;
import gabriel.eventEngine.events.engine.main.events.MassDomination;
import gabriel.eventEngine.events.engine.main.events.Mutant;
import gabriel.eventEngine.events.engine.main.events.TeamVsTeam;
import gabriel.eventEngine.events.engine.main.events.TreasureHunt;
import gabriel.eventEngine.events.engine.main.events.TreasureHuntPvp;
import gabriel.eventEngine.events.engine.main.events.UpgradingEvent;
import gabriel.eventEngine.events.engine.main.events.VIPTeamVsTeam;
import gabriel.eventEngine.events.engine.main.events.Zombies;
import gabriel.eventEngine.events.engine.mini.events.KoreanManager;
import gabriel.eventEngine.events.engine.mini.events.MiniTvTManager;
import gabriel.eventEngine.events.engine.mini.events.OnevsOneManager;
import gabriel.eventEngine.events.engine.mini.events.PartyvsPartyManager;

public enum EventType
{
	Unassigned(0, "", "", Category.MainTeam, true, false),
	TvT(1, "TvT", "Team vs Team", Category.MainTeam, true, false, TeamVsTeam.class),
	CTF(2, "CTF", "Capture the Flag", Category.MainTeam, true, false, CaptureTheFlag.class),
	Domination(3, "Domination", "Domination", Category.MainTeam, true, false, Domination.class),
	MassDomination(4, "MassDom", "Mass Domination", Category.MainTeam, true, false, MassDomination.class),
	DM(5, "DM", "Deathmatch", Category.MainFFA, true, false, Deathmatch.class),
	LastMan(6, "LastMan", "Last Man Standing", Category.MainFFA, true, false, LastManStanding.class),
	BattleRoyales(7, "BattleRoyale", "Battle Royale", Category.MainFFA, true, false, BattleRoyale.class),
	LuckyChests(8, "Chests", "Lucky Chests", Category.MainFFA, true, false, LuckyChests.class),
	Zombies(9, "Zombies", "Zombies", Category.MainTeam, true, false, Zombies.class),
	Mutant(10, "Mutant", "Mutant", Category.MainTeam, true, false, Mutant.class),
	TreasureHunt(11, "THunt", "Treasure Hunt", Category.MainTeam, true, false, TreasureHunt.class),
	TreasureHuntPvp(12, "THuntPvP", "Treasure Hunt PvP", Category.MainTeam, true, false, TreasureHuntPvp.class),
	HuntingGround(13, "HuntGround", "Hunting Grounds", Category.MainTeam, true, false, HuntingGrounds.class),
	Upgrading(14, "Upgrading", "Upgrading Event", Category.MainTeam, true, false, UpgradingEvent.class),
	Battlefields(15, "Battlefields", "Battlefields", Category.MainTeam, true, false, Battlefield.class),
	TvTAdv(16, "TvTAdv", "TvT Advanced", Category.MainTeam, true, false, VIPTeamVsTeam.class),
	Commanders(17, "Commanders", "Commanders", Category.MainTeam, true, false),
	BombFight(18, "Bomb", "Bomb Fight", Category.MainTeam, true, false),
	RussianRoulette(19, "Russian", "Russian Roulette", Category.MainTeam, true, false),
	Simon(20, "Simon", "Simon Says", Category.MainTeam, true, false),
	Classic_1v1(50, "1v1", "Single players fights", Category.Mini, true, false, OnevsOneManager.class),
	PartyvsParty(51, "PTvsPT", "Party fights", Category.Mini, true, false, PartyvsPartyManager.class),
	Korean(52, "Korean", "Korean Style", Category.Mini, true, false, KoreanManager.class),
	MiniTvT(53, "MiniTvT", "Mini TvT", Category.Mini, true, true, MiniTvTManager.class),
	LMS(54, "LMS", "Last Man", Category.Mini, true, false),
	LTS(55, "LTS", "Last Team", Category.Mini, true, false),
	Classic_2v2(56, "2v2", "2v2 event", Category.Mini, true, false),
	Tournament(57, "Tournament", "Tournament", Category.Mini, false, false),
	Underground_Coliseum(58, "UC", "Tower Crush", Category.Mini, true, false),
	Hitman(59, "Hitman", "Hitman", Category.Mini, false, false),
	RBHunt(60, "RBH", "Raid Hunt", Category.Mini, true, false),
	SurvivalArena(61, "Survival", "Survival Arena", Category.Mini, true, true);
	
	private int						_order;
	private Category				_category;
	private String					_shortName;
	private String					_longName;
	private boolean					_allowEdits;
	private boolean					_allowConfig;
	private Class<? extends Event>	eventClass;
	public static int				lastGivenEvent	= 0;
	
	private EventType(int order, String shortName, String longName, Category category, boolean allowEdits, boolean allowConfig, Class<? extends Event> eventClass)
	{
		this._order = order;
		this._category = category;
		this._shortName = shortName;
		this._longName = longName;
		this._allowEdits = allowEdits;
		this._allowConfig = allowConfig;
		this.eventClass = eventClass;
	}
	
	private EventType(int order, String shortName, String longName, Category category, boolean allowEdits, boolean allowConfig)
	{
		this(order, shortName, longName, category, allowEdits, allowConfig, (Class) null);
	}
	
	public Event loadEvent(MainEventManager manager)
	{
		if (this.eventClass != null)
		{
			try
			{
				if (this.isRegularEvent())
				{
					Constructor<? extends Event> constructor = this.eventClass.getConstructor(EventType.class, MainEventManager.class);
					if (constructor == null)
					{
						GabrielEventsLoader.debug("Wrong constructor for event " + this.getAltTitle() + ".", Level.SEVERE);
						return null;
					}
					return constructor.newInstance(this, manager);
				}
			}
			catch (Exception var3)
			{
				System.out.println(this.getAltTitle() + " event load error");
				var3.printStackTrace();
				throw new RuntimeException(var3);
			}
		}
		return null;
	}
	
	public int getId()
	{
		return this.getOrder();
	}
	
	public int getOrder()
	{
		return this._order;
	}
	
	public int getMainEventId()
	{
		return 0;
	}
	
	public boolean isRegularEvent()
	{
		return (this._category == Category.MainTeam) || (this._category == Category.MainFFA);
	}
	
	public boolean isMiniEvent()
	{
		return this._category == Category.Mini;
	}
	
	public boolean isGlobalEvent()
	{
		return this._category == Category.Global;
	}
	
	public boolean isFFAEvent()
	{
		return this._category == Category.MainFFA;
	}
	
	public Category getCategory()
	{
		return this._category;
	}
	
	public boolean allowConfig()
	{
		return this._allowConfig;
	}
	
	public boolean allowEdits()
	{
		return this._allowEdits;
	}
	
	public String getAltTitle()
	{
		return this._shortName;
	}
	
	public String getHtmlTitle()
	{
		return this._longName;
	}
	
	public static EventType getById(int id)
	{
		EventType[] var1 = values();
		int var2 = var1.length;
		for (int var3 = 0; var3 < var2; ++var3)
		{
			EventType t = var1[var3];
			if (t.getId() == id)
			{
				return t;
			}
		}
		return Unassigned;
	}
	
	public static EventType getType(String value)
	{
		EventType[] var1 = values();
		int var2 = var1.length;
		for (int var3 = 0; var3 < var2; ++var3)
		{
			EventType t = var1[var3];
			if (t.toString().equalsIgnoreCase(value) || t.getAltTitle().equalsIgnoreCase(value) || t.getHtmlTitle().equalsIgnoreCase(value) || String.valueOf(t.getId()).equals(value))
			{
				return t;
			}
		}
		return null;
	}
	
	public static EventType[] getMiniEvents()
	{
		List<EventType> types = new LinkedList();
		EventType[] var1 = values();
		int var2 = var1.length;
		for (int var3 = 0; var3 < var2; ++var3)
		{
			EventType t = var1[var3];
			types.add(t);
		}
		return types.toArray(new EventType[types.size()]);
	}
	
	public static EventType getEventByMainId(int id)
	{
		EventType[] var1 = values();
		int var2 = var1.length;
		for (int var3 = 0; var3 < var2; ++var3)
		{
			EventType t = var1[var3];
			if (t.getMainEventId() == id)
			{
				return t;
			}
		}
		return null;
	}
	
	public static EventType getNextRegularEvent()
	{
		EventType t = EventManager.getInstance().getMainEventManager().nextAvailableEvent(false);
		if (t == null)
		{
			return null;
		}
		else
		{
			lastGivenEvent = t.getId();
			return t;
		}
	}
	
	public static EventType getNextRegularEvent(int lastId)
	{
		int i = 0;
		EventType[] var3;
		int var4;
		int var5;
		EventType t;
		for (int next = lastId + 1; i < values().length; ++i)
		{
			var3 = values();
			var4 = var3.length;
			for (var5 = 0; var5 < var4; ++var5)
			{
				t = var3[var5];
				if ((t.getId() == next) && t.isRegularEvent() && EventConfig.getInstance().isEventAllowed(t) && (EventManager.getInstance().getMainEvent(t) != null) && (EventMapSystem.getInstance().getMapsCount(t) > 0))
				{
					return t;
				}
			}
			++next;
		}
		var3 = values();
		var4 = var3.length;
		for (var5 = 0; var5 < var4; ++var5)
		{
			t = var3[var5];
			if (t.isRegularEvent() && EventConfig.getInstance().isEventAllowed(t) && (EventManager.getInstance().getMainEvent(t) != null) && (EventMapSystem.getInstance().getMapsCount(t) > 0))
			{
				return t;
			}
		}
		return null;
	}
	
	public static enum Category
	{
		MainTeam,
		MainFFA,
		Mini,
		Global;
		
		private Category()
		{}
	}
}
