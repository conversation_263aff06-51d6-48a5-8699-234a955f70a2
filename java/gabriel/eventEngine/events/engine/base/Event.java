package gabriel.eventEngine.events.engine.base;

import gabriel.eventEngine.events.GabrielEventsLoader;
import gabriel.eventEngine.l2j.CallBack;

public abstract class Event {
	protected EventType _type;
	
	public Event(EventType type) {
		_type = type;
	}
	
	public final EventType getEventType() {
		return _type;
	}
	
	public String getEventName() {
		return _type.getAltTitle();
	}
	
	public void announce(String text) {
		CallBack.getInstance().getOut().announceToAllScreenMessage(text, getEventType().getAltTitle());
	}
	
	public void debug(String text) {
		GabrielEventsLoader.debug(text);
	}
	
	public void print(String msg) {
		GabrielEventsLoader.detailedDebug(msg);
	}
}
