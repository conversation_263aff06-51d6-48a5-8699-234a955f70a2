package gabriel.eventEngine.events.engine.base;

import gabriel.eventEngine.events.EventGame;
import gabriel.eventEngine.events.engine.stats.GlobalStats;
import gabriel.eventEngine.events.engine.stats.GlobalStatsModel;
import gabriel.eventEngine.interf.PlayerEventInfo;

public class PvPEventPlayerData extends EventPlayerData
{
	private int	_kills			= 0;
	private int	_deaths			= 0;
	private int	_spree			= 0;
	private int	_eventPoints	= 0;
	
	public PvPEventPlayerData(PlayerEventInfo owner, EventGame event, GlobalStatsModel stats)
	{
		super(owner, event, stats);
	}
	
	public int getKills()
	{
		return _kills;
	}
	
	public int raiseKills(int i)
	{
		_kills += i;
		_globalStats.raise(GlobalStats.GlobalStatType.KILLS, i);
		return _kills;
	}
	
	public void setKills(int i)
	{
		_kills = i;
		_globalStats.set(GlobalStats.GlobalStatType.KILLS, i);
	}
	
	public int getDeaths()
	{
		return _deaths;
	}
	
	public int raiseDeaths(int i)
	{
		_deaths += i;
		_globalStats.raise(GlobalStats.GlobalStatType.DEATHS, i);
		return _deaths;
	}
	
	public void setDeaths(int i)
	{
		_deaths = i;
		_globalStats.set(GlobalStats.GlobalStatType.DEATHS, i);
	}
	
	public int getSpree()
	{
		return _spree;
	}
	
	public int raiseSpree(int i)
	{
		_spree += i;
		return _spree;
	}
	
	public void setSpree(int i)
	{
		_spree = i;
	}
	
	public int getEventPoints()
	{
		return _eventPoints;
	}
	
	public void setEventPoints(int points)
	{
		_eventPoints = points;
	}
	
	public void addEventPoints(int points)
	{
		_eventPoints += points;
	}
	
	public void subtractEventPoints(int points)
	{
		_eventPoints = Math.max(0, _eventPoints - points);
	}
}
