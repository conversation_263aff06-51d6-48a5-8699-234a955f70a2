package gabriel.eventEngine.events.engine;

import java.util.Comparator;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.StringTokenizer;
import java.util.logging.Level;

import club.projectessence.Config;
import club.projectessence.gameserver.handler.AdminCommandHandler;
import gabriel.eventEngine.events.Configurable;
import gabriel.eventEngine.events.EventGame;
import gabriel.eventEngine.events.GabrielEventsLoader;
import gabriel.eventEngine.events.engine.base.Event;
import gabriel.eventEngine.events.engine.base.EventPlayerData;
import gabriel.eventEngine.events.engine.base.EventType;
import gabriel.eventEngine.events.engine.html.EventHtmlManager;
import gabriel.eventEngine.events.engine.lang.LanguageEngine;
import gabriel.eventEngine.events.engine.main.MainEventManager;
import gabriel.eventEngine.events.engine.main.events.AbstractMainEvent;
import gabriel.eventEngine.events.engine.mini.MiniEventGame;
import gabriel.eventEngine.events.engine.mini.MiniEventManager;
import gabriel.eventEngine.events.engine.mini.events.KoreanManager;
import gabriel.eventEngine.events.engine.mini.events.MiniTvTManager;
import gabriel.eventEngine.events.engine.mini.events.OnevsOneManager;
import gabriel.eventEngine.events.engine.mini.events.PartyvsPartyManager;
import gabriel.eventEngine.events.engine.stats.EventStatsManager;
import gabriel.eventEngine.events.engine.team.EventTeam;
import gabriel.eventEngine.interf.GabrielEvents;
import gabriel.eventEngine.interf.PlayerEventInfo;
import gabriel.eventEngine.interf.delegate.CharacterData;
import gabriel.eventEngine.interf.delegate.NpcData;
import gabriel.eventEngine.interf.delegate.SkillData;
import gabriel.eventEngine.interf.handlers.AdminCommandHandlerInstance;
import gabriel.eventEngine.l2j.CallBack;

public class EventManager
{
	private final Map<EventType, Map<Integer, MiniEventManager>>	_miniEvents;
	private final Map<EventType, AbstractMainEvent>					_mainEvents;
	private Map<DisconnectedPlayerData, Long>						_disconnectedPlayers;
	private MainEventManager										_mainManager;
	private EventHtmlManager										_html;
	public static boolean											ALLOW_VOICE_COMMANDS		= EventConfig.getInstance().getGlobalConfigBoolean("allowVoicedCommands");
	public static String											REGISTER_VOICE_COMMAND		= EventConfig.getInstance().getGlobalConfigValue("registerVoicedCommand");
	public static String											UNREGISTER_VOICE_COMMAND	= EventConfig.getInstance().getGlobalConfigValue("unregisterVoicedCommand");
	// Mã màu từ config
	private static final String										FIRE_NAME_COLOR;
	private static final String										WATER_NAME_COLOR;
	private static final int										FIRE_COLOR_VALUE;
	private static final int										WATER_COLOR_VALUE;
	static
	{
		EventConfig config = EventConfig.getInstance();
		if (config == null)
		{
			System.err.println("EventManager: EventConfig.getInstance() returned null. Unable to load configuration.");
		}
		ALLOW_VOICE_COMMANDS = config != null && config.getGlobalConfigBoolean("allowVoicedCommands");
		REGISTER_VOICE_COMMAND = config != null ? config.getGlobalConfigValue("registerVoicedCommand") : "";
		UNREGISTER_VOICE_COMMAND = config != null ? config.getGlobalConfigValue("unregisterVoicedCommand") : "";
		FIRE_COLOR_VALUE = Config.FACTION_FIRE_NAME_COLOR; // 255 (0x0000FF)
		WATER_COLOR_VALUE = Config.FACTION_WATER_NAME_COLOR; // 65280 (0x00FF00)
		FIRE_NAME_COLOR = String.format("%06X", FIRE_COLOR_VALUE & 0xFFFFFF);
		WATER_NAME_COLOR = String.format("%06X", WATER_COLOR_VALUE & 0xFFFFFF);
		// System.out.println("EventManager: FireNameColor loaded from Config: " + FIRE_NAME_COLOR + " (value: " + FIRE_COLOR_VALUE + ")");
		// System.out.println("EventManager: WaterNameColor loaded from Config: " + WATER_NAME_COLOR + " (value: " + WATER_COLOR_VALUE + ")");
	}
	
	public EventManager()
	{
		AdminCommandHandler.getInstance().registerHandler(new AdminSunrise());
		this._miniEvents = new LinkedHashMap<>((EventType.values()).length);
		this._mainEvents = new LinkedHashMap<>((EventType.values()).length);
		GabrielEventsLoader.debug("Loading events...");
		loadEvents();
	}
	
	/** compares PlayerEventInfo collection by their levels - descending */
	public Comparator<PlayerEventInfo>	compareByLevels		= new Comparator<PlayerEventInfo>()
															{
																@Override
																public int compare(PlayerEventInfo o1, PlayerEventInfo o2)
																{
																	int level1 = o1.getLevel();
																	int level2 = o2.getLevel();
																	return level1 == level2 ? 0 : level1 < level2 ? 1 : -1;
																}
															};
	/** compares PlayerEventInfo collection by their pvp kills - descending */
	public Comparator<PlayerEventInfo>	compareByPvps		= new Comparator<PlayerEventInfo>()
															{
																@Override
																public int compare(PlayerEventInfo o1, PlayerEventInfo o2)
																{
																	int pvp1 = o1.getPvpKills();
																	int pvp2 = o2.getPvpKills();
																	return pvp1 == pvp2 ? 0 : pvp1 < pvp2 ? 1 : -1;
																}
															};
	/** sorts event teams by their kills amount */
	public Comparator<EventTeam>		compareTeamKills	= new Comparator<EventTeam>()
															{
																@Override
																public int compare(EventTeam t1, EventTeam t2)
																{
																	int kills1 = t1.getKills();
																	int kills2 = t2.getKills();
																	return kills1 == kills2 ? 0 : kills1 < kills2 ? 1 : -1;
																}
															};
	public Comparator<PlayerEventInfo>	comparePlayersKills	= new Comparator<PlayerEventInfo>()
															{
																@Override
																public int compare(PlayerEventInfo p1, PlayerEventInfo p2)
																{
																	int kills1 = p1.getKills();
																	int kills2 = p2.getKills();
																	return kills1 == kills2 ? 0 : kills1 < kills2 ? 1 : -1;
																}
															};
	public Comparator<PlayerEventInfo>	comparePlayersScore	= new Comparator<PlayerEventInfo>()
															{
																@Override
																public int compare(PlayerEventInfo p1, PlayerEventInfo p2)
																{
																	int score1 = p1.getScore();
																	int score2 = p2.getScore();
																	if (score1 == score2)
																	{
																		int deaths1 = p1.getDeaths();
																		int deaths2 = p2.getDeaths();
																		return deaths1 == deaths2 ? 0 : deaths1 < deaths2 ? -1 : 1;
																	}
																	else
																		return score1 < score2 ? 1 : -1;
																}
															};
	public Comparator<EventTeam>		compareTeamScore	= new Comparator<EventTeam>()
															{
																@Override
																public int compare(EventTeam t1, EventTeam t2)
																{
																	int score1 = t1.getScore();
																	int score2 = t2.getScore();
																	return score1 == score2 ? 0 : score1 < score2 ? 1 : -1;
																}
															};
	
	private void loadEvents()
	{
		int count = 0;
		this._disconnectedPlayers = new LinkedHashMap<>();
		this._mainManager = new MainEventManager();
		for (EventType event : EventType.values())
		{
			if (event != EventType.Unassigned)
			{
				Event eventInstance = event.loadEvent(this._mainManager);
				if (eventInstance != null)
				{
					if (event.isRegularEvent())
					{
						this._mainEvents.put(eventInstance.getEventType(), (AbstractMainEvent) eventInstance);
						count++;
					}
				}
			}
		}
		GabrielEventsLoader.debug("Loaded " + count + " main events.");
		this._miniEvents.put(EventType.Unassigned, new LinkedHashMap<>());
		this._miniEvents.put(EventType.Classic_1v1, new LinkedHashMap<>());
		this._miniEvents.put(EventType.PartyvsParty, new LinkedHashMap<>());
		this._miniEvents.put(EventType.Korean, new LinkedHashMap<>());
		this._miniEvents.put(EventType.MiniTvT, new LinkedHashMap<>());
		GabrielEventsLoader.debug("Loaded " + this._miniEvents.size() + " mini event types.");
	}
	
	public void setHtmlManager(EventHtmlManager manager)
	{
		this._html = manager;
	}
	
	public MiniEventManager createManager(EventType type, int modeId, String name, String visibleName)
	{
		OnevsOneManager onevsOneManager;
		PartyvsPartyManager partyvsPartyManager;
		KoreanManager koreanManager;
		MiniTvTManager miniTvTManager;
		MiniEventManager manager = null;
		switch (type)
		{
			case Classic_1v1:
				onevsOneManager = new OnevsOneManager(type);
				onevsOneManager.getMode().setModeName(name);
				onevsOneManager.getMode().setVisibleName(visibleName);
				(this._miniEvents.get(type)).put(Integer.valueOf(modeId), onevsOneManager);
				return onevsOneManager;
			case PartyvsParty:
				partyvsPartyManager = new PartyvsPartyManager(type);
				partyvsPartyManager.getMode().setModeName(name);
				partyvsPartyManager.getMode().setVisibleName(visibleName);
				(this._miniEvents.get(type)).put(Integer.valueOf(modeId), partyvsPartyManager);
				return partyvsPartyManager;
			case Korean:
				koreanManager = new KoreanManager(type);
				koreanManager.getMode().setModeName(name);
				koreanManager.getMode().setVisibleName(visibleName);
				(this._miniEvents.get(type)).put(Integer.valueOf(modeId), koreanManager);
				return koreanManager;
			case MiniTvT:
				miniTvTManager = new MiniTvTManager(type);
				miniTvTManager.getMode().setModeName(name);
				miniTvTManager.getMode().setVisibleName(visibleName);
				(this._miniEvents.get(type)).put(Integer.valueOf(modeId), miniTvTManager);
				return miniTvTManager;
		}
		GabrielEventsLoader.debug("Event " + type.getAltTitle() + " isn't implemented yet.", Level.WARNING);
		return null;
	}
	
	public static final EventManager getInstance()
	{
		return SingletonHolder._instance;
	}
	
	public Map<EventType, Map<Integer, MiniEventManager>> getMiniEvents()
	{
		return this._miniEvents;
	}
	
	public Map<EventType, AbstractMainEvent> getMainEvents()
	{
		return this._mainEvents;
	}
	
	public Configurable getEvent(EventType type)
	{
		return getEvent(type, 1);
	}
	
	public Configurable getEvent(EventType type, int modeId)
	{
		if (type.isRegularEvent())
		{
			return getMainEvent(type);
		}
		return getMiniEvent(type, modeId);
	}
	
	public MiniEventManager getMiniEvent(EventType type, int id)
	{
		if (this._miniEvents.get(type) == null)
		{
			return null;
		}
		return this._miniEvents.get(type).get(id);
	}
	
	public AbstractMainEvent getMainEvent(EventType type)
	{
		if (!this._mainEvents.containsKey(type))
		{
			return null;
		}
		return this._mainEvents.get(type);
	}
	
	public AbstractMainEvent getCurrentMainEvent()
	{
		return this._mainManager.getCurrent();
	}
	
	public boolean onBypass(PlayerEventInfo player, String bypass)
	{
		return this._html.onBypass(player, bypass);
	}
	
	public boolean showNpcHtml(PlayerEventInfo player, NpcData npc)
	{
		return this._html.showNpcHtml(player, npc);
	}
	
	public EventHtmlManager getHtmlManager()
	{
		return this._html;
	}
	
	public boolean canRegister(PlayerEventInfo player)
	{
		if (player.isInJail())
		{
			player.sendMessage(LanguageEngine.getMsg("registering_jail"));
			return false;
		}
		if (player.isInSiege())
		{
			player.sendMessage(LanguageEngine.getMsg("registering_siege"));
			return false;
		}
		if (player.isInDuel())
		{
			player.sendMessage(LanguageEngine.getMsg("registering_duel"));
			return false;
		}
		if (player.isOlympiadRegistered() || player.isInOlympiadMode() || player.isInOlympiad())
		{
			player.sendMessage(LanguageEngine.getMsg("registering_olympiad"));
			return false;
		}
		if (player.getKarma() > 0)
		{
			player.sendMessage(LanguageEngine.getMsg("registering_karma"));
			return false;
		}
		if (player.isCursedWeaponEquipped())
		{
			player.sendMessage(LanguageEngine.getMsg("registering_cursedWeapon"));
			return false;
		}
		if (player.isInStoreMode())
		{
			player.sendMessage(LanguageEngine.getMsg("registering_storemode"));
			return false;
		}
		return true;
	}
	
	public boolean isInEvent(CharacterData cha)
	{
		if (getCurrentMainEvent() != null)
		{
			return getCurrentMainEvent().isInEvent(cha);
		}
		return false;
	}
	
	public boolean allowDie(CharacterData cha, CharacterData killer)
	{
		if (getCurrentMainEvent() != null)
		{
			return getCurrentMainEvent().allowKill(cha, killer);
		}
		return true;
	}
	
	public void onDamageGive(CharacterData cha, CharacterData attacker, int damage, boolean isDOT)
	{
		if (getCurrentMainEvent() != null)
		{
			getCurrentMainEvent().onDamageGive(attacker, cha, damage, isDOT);
		}
	}
	
	public boolean onAttack(CharacterData cha, CharacterData target)
	{
		if (getCurrentMainEvent() != null)
		{
			return getCurrentMainEvent().onAttack(cha, target);
		}
		return true;
	}
	
	public boolean tryVoicedCommand(PlayerEventInfo player, String text)
	{
		if (player != null && ALLOW_VOICE_COMMANDS)
		{
			if (text.equalsIgnoreCase(REGISTER_VOICE_COMMAND))
			{
				getInstance().getMainEventManager().registerPlayer(player);
				return true;
			}
			if (text.equalsIgnoreCase(UNREGISTER_VOICE_COMMAND))
			{
				getInstance().getMainEventManager().unregisterPlayer(player, false);
				return true;
			}
			if (text.equalsIgnoreCase(".suicide"))
			{
				if (player.isInEvent())
				{
					player.sendMessage("You are being suicided.");
					player.doDie();
					return true;
				}
			}
		}
		return false;
	}
	
	public void removeEventSkills(PlayerEventInfo player)
	{
		for (SkillData sk : player.getSkills())
		{
			if (sk.getId() >= 35000 && sk.getId() <= 35099)
			{
				player.removeBuff(sk.getId());
				player.removeSkill(sk.getId());
			}
		}
	}
	
	public void onPlayerLogin(PlayerEventInfo player)
	{
		removeEventSkills(player);
		EventStatsManager.getInstance().onLogin(player);
		DisconnectedPlayerData data = null;
		for (Map.Entry<DisconnectedPlayerData, Long> e : this._disconnectedPlayers.entrySet())
		{
			if (e.getKey()._player.getPlayersId() == player.getPlayersId())
			{
				data = e.getKey();
				this._disconnectedPlayers.remove(e.getKey());
				break;
			}
		}
		if (data != null)
		{
			DisconnectedPlayerData fData = data;
			EventGame event = data._event;
			if (event != null)
			{
				CallBack.getInstance().getOut().scheduleGeneral(() -> event.addDisconnectedPlayer(player, fData), 1500L);
			}
		}
	}
	
	public void addDisconnectedPlayer(PlayerEventInfo player, EventTeam team, EventPlayerData d, EventGame event)
	{
		long time = System.currentTimeMillis();
		DisconnectedPlayerData data = new DisconnectedPlayerData(player, event, d, team, time, player.getInstanceId());
		this._disconnectedPlayers.put(data, time);
	}
	
	public void clearDisconnectedPlayers()
	{
		this._disconnectedPlayers.clear();
	}
	
	public void spectateGame(PlayerEventInfo player, EventType event, int modeId, int gameId)
	{
		MiniEventManager manager = getMiniEvent(event, modeId);
		if (manager == null)
		{
			player.sendStaticPacket();
			return;
		}
		MiniEventGame game = null;
		for (MiniEventGame g : manager.getActiveGames())
		{
			if (g.getGameId() == gameId)
			{
				game = g;
				break;
			}
		}
		if (game == null)
		{
			player.sendMessage(LanguageEngine.getMsg("observing_gameEnded"));
			return;
		}
		if (!canRegister(player))
		{
			player.sendMessage(LanguageEngine.getMsg("observing_cant"));
			return;
		}
		if (player.isRegistered())
		{
			player.sendMessage(LanguageEngine.getMsg("observing_alreadyRegistered"));
			return;
		}
		CallBack.getInstance().getPlayerBase().addInfo(player);
		player.initOrigInfo();
		game.addSpectator(player);
	}
	
	public void removePlayerFromObserverMode(PlayerEventInfo pi)
	{
		MiniEventGame game = pi.getActiveGame();
		if (game == null)
		{
			return;
		}
		game.removeSpectator(pi, false);
	}
	
	public String getDarkColorForHtml(int teamId)
	{
		switch (teamId)
		{
			case 1:
				return FIRE_NAME_COLOR; // Fire: từ config hoặc mặc định
			case 2:
				return WATER_NAME_COLOR; // Water: từ config hoặc mặc định
			case 3:
				return "868F81";
			case 4:
				return "937D8D";
			case 5:
				return "93937D";
			case 6:
				return "D2934D";
			case 7:
				return "3EC1C1";
			case 8:
				return "D696D1";
			case 9:
				return "9B7957";
			case 10:
				return "949494";
		}
		return "8f8f8f";
	}
	
	public String getTeamColorForHtml(int teamId)
	{
		switch (teamId)
		{
			case 1:
				return FIRE_NAME_COLOR; // Fire: từ config hoặc mặc định
			case 2:
				return WATER_NAME_COLOR; // Water: từ config hoặc mặc định
			case 3:
				return "56C965";
			case 4:
				return "9F52CD";
			case 5:
				return "DAC73D";
			case 6:
				return "D2934D";
			case 7:
				return "3EC1C1";
			case 8:
				return "D696D1";
			case 9:
				return "9B7957";
			case 10:
				return "949494";
		}
		return "FFFFFF";
	}
	
	public int getTeamColorForName(int teamId)
	{
		switch (teamId)
		{
			case 1:
				return FIRE_COLOR_VALUE; // Fire: từ config hoặc mặc định
			case 2:
				return WATER_COLOR_VALUE; // Water: từ config hoặc mặc định
			case 3:
				return 5687653; // 0x56C965
			case 4:
				return 10435533; // 0x9F52CD
			case 5:
				return 14330557; // 0xDAC73D
			case 6:
				return 13798157; // 0xD2934D
			case 7:
				return 4109057; // 0x3EC1C1
			case 8:
				return 14049681; // 0xD696D1
			case 9:
				return 10187863; // 0x9B7957
			case 10:
				return 9737364; // 0x949494
		}
		return 0;
	}
	
	public String getTeamName(int teamId)
	{
		switch (teamId)
		{
			case 1:
				return "Fire";
			case 2:
				return "Water";
			case 3:
				return "Green";
			case 4:
				return "Purple";
			case 5:
				return "Yellow";
			case 6:
				return "Orange";
			case 7:
				return "Teal";
			case 8:
				return "Pink";
			case 9:
				return "Brown";
			case 10:
				return "Grey";
		}
		return "No";
	}
	
	public void debug(String message)
	{
		GabrielEventsLoader.debug(message);
	}
	
	public void debug(Exception e)
	{
		e.printStackTrace();
	}
	
	public MainEventManager getMainEventManager()
	{
		return this._mainManager;
	}
	
	private static class SingletonHolder
	{
		protected static final EventManager _instance = new EventManager();
	}
	
	public class AdminSunrise extends AdminCommandHandlerInstance
	{
		private final String[] ADMIN_COMMANDS = new String[]
		{
			"admin_event_manage"
		};
		
		public boolean useAdminCommand(String command, PlayerEventInfo activeChar)
		{
			if (command.startsWith("admin_event_manage"))
			{
				StringTokenizer st = new StringTokenizer(command);
				st.nextToken();
				if (!st.hasMoreTokens())
				{
					GabrielEvents.onAdminBypass(activeChar, "menu");
				}
				else
				{
					GabrielEvents.onAdminBypass(activeChar, command.substring(19));
				}
			}
			return true;
		}
		
		public String[] getAdminCommandList()
		{
			return this.ADMIN_COMMANDS;
		}
	}
	
	public class DisconnectedPlayerData
	{
		protected final PlayerEventInfo	_player;
		protected final EventGame		_event;
		private final EventPlayerData	_data;
		private final EventTeam			_team;
		private final long				_time;
		private final int				_instance;
		
		public DisconnectedPlayerData(PlayerEventInfo player, EventGame event, EventPlayerData data, EventTeam team, long time, int instance)
		{
			this._time = time;
			this._player = player;
			this._data = data;
			this._team = team;
			this._event = event;
			this._instance = instance;
		}
		
		public PlayerEventInfo getPlayer()
		{
			return this._player;
		}
		
		public EventGame getEvent()
		{
			return this._event;
		}
		
		public EventTeam getTeam()
		{
			return this._team;
		}
		
		public EventPlayerData getPlayerData()
		{
			return this._data;
		}
		
		public long getTime()
		{
			return this._time;
		}
		
		public int getInstance()
		{
			return this._instance;
		}
	}
}