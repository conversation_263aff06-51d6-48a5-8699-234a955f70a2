package gabriel.eventEngine.util;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import gabriel.eventEngine.events.engine.EventConfig;

/**
 * Utility class to prevent screen message spam that can cause client crashes
 * <AUTHOR> Team
 */
public class ScreenMessageThrottle {
    private static final long MESSAGE_COOLDOWN_MS = 500; // 0.5 second cooldown between messages (giảm từ 1s)
    private static final int MAX_MESSAGES_PER_MINUTE = 30; // Maximum messages per minute (tăng từ 10)

    private static final ConcurrentHashMap<Integer, Long> lastMessageTime = new ConcurrentHashMap<>();
    private static final ConcurrentHashMap<Integer, Integer> messageCount = new ConcurrentHashMap<>();
    private static final ConcurrentHashMap<Integer, Long> minuteStartTime = new ConcurrentHashMap<>();
    private static final ConcurrentHashMap<Integer, Long> lastScorebarTime = new ConcurrentHashMap<>();
    
    /**
     * Check if a player can receive a screen message without causing spam
     * @param player The player to check
     * @return true if the message can be sent, false if it should be throttled
     */
    public static boolean canSendMessage(PlayerInstance player) {
        if (player == null) {
            return false;
        }

        // For GM players, apply less strict throttling (cải thiện cho GM)
        if (player.isGM()) {
            int playerId = player.getObjectId();
            long currentTime = System.currentTimeMillis();

            // Check cooldown - same as normal players now
            Long lastTime = lastMessageTime.get(playerId);
            if (lastTime != null && (currentTime - lastTime) < MESSAGE_COOLDOWN_MS) {
                return false;
            }

            // Check rate limiting - same limit as normal players
            Long minuteStart = minuteStartTime.get(playerId);
            if (minuteStart == null || (currentTime - minuteStart) >= TimeUnit.MINUTES.toMillis(1)) {
                // Reset counter for new minute
                minuteStartTime.put(playerId, currentTime);
                messageCount.put(playerId, 1);
            } else {
                int count = messageCount.getOrDefault(playerId, 0);
                if (count >= MAX_MESSAGES_PER_MINUTE) { // Same limit as normal players
                    return false;
                }
                messageCount.put(playerId, count + 1);
            }

            lastMessageTime.put(playerId, currentTime);
            return true;
        }

        // For normal players, allow all messages (they handle it better)
        return true;
    }

    /**
     * Check if a score bar message can be sent (less strict than regular messages)
     * @param player The player to check
     * @return true if the score bar can be sent
     */
    public static boolean canSendScoreBar(PlayerInstance player) {
        if (player == null) {
            return false;
        }

        // Check if throttling is disabled globally
        boolean throttlingDisabled = EventConfig.getInstance().getGlobalConfigBoolean("Features", "disableScorebarThrottling");
        if (throttlingDisabled) {
            return true;
        }

        // Get scorebar display interval from config (in seconds)
        int intervalSeconds = EventConfig.getInstance().getGlobalConfigInt("Features", "scorebarDisplayInterval");
        if (intervalSeconds <= 0) {
            intervalSeconds = 1; // Default to 1 second if invalid
        }
        long intervalMs = intervalSeconds * 1000L;

        int playerId = player.getObjectId();
        long currentTime = System.currentTimeMillis();

        // Check if enough time has passed since last scorebar
        Long lastTime = lastScorebarTime.get(playerId);
        if (lastTime != null && (currentTime - lastTime) < intervalMs) {
            return false;
        }

        lastScorebarTime.put(playerId, currentTime);
        return true;
    }
    
    /**
     * Clean up old entries to prevent memory leaks
     */
    public static void cleanup() {
        long currentTime = System.currentTimeMillis();
        long cleanupThreshold = TimeUnit.MINUTES.toMillis(5);
        
        lastMessageTime.entrySet().removeIf(entry -> 
            (currentTime - entry.getValue()) > cleanupThreshold);
        
        minuteStartTime.entrySet().removeIf(entry -> 
            (currentTime - entry.getValue()) > cleanupThreshold);
        
        messageCount.entrySet().removeIf(entry -> {
            Long minuteStart = minuteStartTime.get(entry.getKey());
            return minuteStart == null || (currentTime - minuteStart) > cleanupThreshold;
        });
    }
}
