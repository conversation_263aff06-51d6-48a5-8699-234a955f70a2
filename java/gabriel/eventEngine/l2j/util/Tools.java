package gabriel.eventEngine.l2j.util;

import java.util.logging.Logger;

import gabriel.eventEngine.interf.PlayerEventInfo;

public final class Tools {
	protected static final Logger _log = Logger.getLogger(Tools.class.getName());

	public static boolean isDualBox(PlayerEventInfo player1, PlayerEventInfo player2) {
		try {
			String ip_net1 = player1.getOwner().getClient().getHostAddress();
			String ip_net2 = player2.getOwner().getClient().getHostAddress();
			String ip_pc1 = "";
			String ip_pc2 = "";
			int[][] trace1 = player1.getOwner().getClient().getTrace();
			for (int o = 0; o < (trace1[0]).length; o++) {
				ip_pc1 = ip_pc1 + trace1[0][o];
				if (o != ((trace1[0]).length - 1)) {
					ip_pc1 = ip_pc1 + ".";
				}
			}
			int[][] trace2 = player2.getOwner().getClient().getTrace();
			for (int u = 0; u < (trace2[0]).length; u++) {
				ip_pc2 = ip_pc2 + trace2[0][u];
				if (u != ((trace2[0]).length - 1)) {
					ip_pc2 = ip_pc2 + ".";
				}
			}
			if (ip_net1.equals(ip_net2) && ip_pc1.equals(ip_pc2)) {
				return true;
			}
		} catch (Exception e) {
			_log.warning(Tools.class.getSimpleName() + ": Exception while trying to get external IP.");
			return false;
		}
		return false;
	}
}
