package gabriel.eventEngine.l2j.delegate;

import gabriel.eventEngine.events.engine.base.Loc;
import gabriel.eventEngine.interf.PlayerEventInfo;
import gabriel.eventEngine.interf.delegate.CharacterData;
import gabriel.eventEngine.interf.delegate.DoorData;

public interface ICharacterData {
    String getName();

    int getObjectId();

    Loc getLoc();

    double getPlanDistanceSq(int paramInt1, int paramInt2);

    boolean isDoor();

    DoorData getDoorData();

    void startAbnormalEffect(int paramInt);

    void stopAbnormalEffect(int paramInt);

    void creatureSay(int paramInt, String paramString1, String paramString2);

    void doDie(CharacterData paramCharacterData);

    boolean isDead();

    PlayerEventInfo getEventInfo();
}


