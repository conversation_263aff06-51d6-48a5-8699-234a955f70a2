package gabriel.eventEngine.l2j;

import club.projectessence.gameserver.enums.ShortcutType;

public interface IValues
{
	int PAPERDOLL_UNDER();
	
	int PAPERDOLL_HEAD();
	
	int PAPERDOLL_HAIR();
	
	int PAPERDOLL_HAIR2();
	
	int PAPERDOLL_NECK();
	
	int PAPERDOLL_RHAND();
	
	int PAPERDOLL_CHEST();
	
	int PAPERDOLL_LHAND();
	
	int PAPERDOLL_REAR();
	
	int PAPERDOLL_LEAR();
	
	int PAPERDOLL_GLOVES();
	
	int PAPERDOLL_LEGS();
	
	int PAPERDOLL_FEET();
	
	int PAPERDOLL_RFINGER();
	
	int PAPERDOLL_LFINGER();
	
	int PAPERDOLL_LBRACELET();
	
	int PAPERDOLL_RBRACELET();
	
	int PAPERDOLL_DECO1();
	
	int PAPERDOLL_DECO2();
	
	int PAPERDOLL_DECO3();
	
	int PAPERDOLL_DECO4();
	
	int PAPERDOLL_DECO5();
	
	int PAPERDOLL_DECO6();
	
	int PAPERDOLL_CLOAK();
	
	int PAPERDOLL_BELT();
	
	int PAPERDOLL_TOTALSLOTS();
	
	int SLOT_NONE();
	
	int SLOT_UNDERWEAR();
	
	int SLOT_R_EAR();
	
	int SLOT_L_EAR();
	
	int SLOT_LR_EAR();
	
	int SLOT_NECK();
	
	int SLOT_R_FINGER();
	
	int SLOT_L_FINGER();
	
	int SLOT_LR_FINGER();
	
	int SLOT_HEAD();
	
	int SLOT_R_HAND();
	
	int SLOT_L_HAND();
	
	int SLOT_GLOVES();
	
	int SLOT_CHEST();
	
	int SLOT_LEGS();
	
	int SLOT_FEET();
	
	int SLOT_BACK();
	
	int SLOT_LR_HAND();
	
	int SLOT_FULL_ARMOR();
	
	int SLOT_HAIR();
	
	int SLOT_ALLDRESS();
	
	int SLOT_HAIR2();
	
	int SLOT_HAIRALL();
	
	int SLOT_R_BRACELET();
	
	int SLOT_L_BRACELET();
	
	int SLOT_DECO();
	
	int SLOT_BELT();
	
	int SLOT_WOLF();
	
	int SLOT_HATCHLING();
	
	int SLOT_STRIDER();
	
	int SLOT_BABYPET();
	
	int SLOT_GREATWOLF();
	
	int CRYSTAL_NONE();
	
	int CRYSTAL_D();
	
	int CRYSTAL_C();
	
	int CRYSTAL_B();
	
	int CRYSTAL_A();
	
	int CRYSTAL_S();
	
	int CRYSTAL_S80();
	
	int CRYSTAL_S84();
	
	ShortcutType TYPE_ITEM();
	
	ShortcutType TYPE_SKILL();
	
	ShortcutType TYPE_ACTION();
	
	ShortcutType TYPE_MACRO();
	
	ShortcutType TYPE_RECIPE();
	
	ShortcutType TYPE_TPBOOKMARK();
	
	int ABNORMAL_NULL();
	
	int ABNORMAL_BLEEDING();
	
	int ABNORMAL_POISON();
	
	int ABNORMAL_REDCIRCLE();
	
	int ABNORMAL_ICE();
	
	int ABNORMAL_WIND();
	
	int ABNORMAL_FEAR();
	
	int ABNORMAL_STUN();
	
	int ABNORMAL_SLEEP();
	
	int ABNORMAL_MUTED();
	
	int ABNORMAL_ROOT();
	
	int ABNORMAL_HOLD_1();
	
	int ABNORMAL_HOLD_2();
	
	int ABNORMAL_UNKNOWN_13();
	
	int ABNORMAL_BIG_HEAD();
	
	int ABNORMAL_FLAME();
	
	int ABNORMAL_UNKNOWN_16();
	
	int ABNORMAL_GROW();
	
	int ABNORMAL_FLOATING_ROOT();
	
	int ABNORMAL_DANCE_STUNNED();
	
	int ABNORMAL_FIREROOT_STUN();
	
	int ABNORMAL_STEALTH();
	
	int ABNORMAL_IMPRISIONING_1();
	
	int ABNORMAL_IMPRISIONING_2();
	
	int ABNORMAL_MAGIC_CIRCLE();
	
	int ABNORMAL_ICE2();
	
	int ABNORMAL_EARTHQUAKE();
	
	int ABNORMAL_UNKNOWN_27();
	
	int ABNORMAL_INVULNERABLE();
	
	int ABNORMAL_VITALITY();
	
	int ABNORMAL_REAL_TARGET();
	
	int ABNORMAL_DEATH_MARK();
	
	int ABNORMAL_SKULL_FEAR();
	
	int ABNORMAL_S_INVINCIBLE();
	
	int ABNORMAL_S_AIR_STUN();
	
	int ABNORMAL_S_AIR_ROOT();
	
	int ABNORMAL_S_BAGUETTE_SWORD();
	
	int ABNORMAL_S_YELLOW_AFFRO();
	
	int ABNORMAL_S_PINK_AFFRO();
	
	int ABNORMAL_S_BLACK_AFFRO();
	
	int ABNORMAL_S_UNKNOWN8();
	
	int ABNORMAL_S_STIGMA_SHILIEN();
	
	int ABNORMAL_S_STAKATOROOT();
	
	int ABNORMAL_S_FREEZING();
	
	int ABNORMAL_S_VESPER();
	
	int ABNORMAL_E_AFRO_1();
	
	int ABNORMAL_E_AFRO_2();
	
	int ABNORMAL_E_AFRO_3();
	
	int ABNORMAL_E_EVASWRATH();
	
	int ABNORMAL_E_HEADPHONE();
	
	int ABNORMAL_E_VESPER_1();
	
	int ABNORMAL_E_VESPER_2();
	
	int ABNORMAL_E_VESPER_3();
}
