package custom.gve.service;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.logging.Level;
import java.util.logging.Logger;

import club.projectessence.Config;
import club.projectessence.commons.database.DatabaseFactory;
import club.projectessence.commons.util.Rnd;
import club.projectessence.gameserver.data.sql.ClanTable;
import club.projectessence.gameserver.enums.CastleSide;
import club.projectessence.gameserver.enums.Faction;
import club.projectessence.gameserver.instancemanager.CastleManager;
import club.projectessence.gameserver.instancemanager.FactionLeaderManager;
import club.projectessence.gameserver.instancemanager.FortManager;
import club.projectessence.gameserver.model.World;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.model.clan.Clan;
import club.projectessence.gameserver.model.clan.ClanMember;
import club.projectessence.gameserver.model.clan.ClanWar;
import club.projectessence.gameserver.model.events.Containers;
import club.projectessence.gameserver.model.events.EventType;
import club.projectessence.gameserver.model.events.ListenersContainer;
import club.projectessence.gameserver.model.events.impl.creature.player.OnPlayerLogin;
import club.projectessence.gameserver.model.events.impl.creature.player.OnPlayerLogout;
import club.projectessence.gameserver.model.events.listeners.ConsumerEventListener;
import club.projectessence.gameserver.model.itemcontainer.Inventory;
import club.projectessence.gameserver.model.olympiad.OlympiadManager;
import club.projectessence.gameserver.model.siege.Castle;
import club.projectessence.gameserver.model.siege.Fort;
import club.projectessence.gameserver.network.SystemMessageId;
import club.projectessence.gameserver.network.serverpackets.SystemMessage;

public class FactionBalanceService
{
	private static final Logger									LOGGER					= Logger.getLogger(FactionBalanceService.class.getName());
	private static final ConcurrentHashMap<Faction, Integer>	totalFactionCounts		= new ConcurrentHashMap<>();
	private static final ConcurrentHashMap<Faction, Integer>	onlineFactionCounts		= new ConcurrentHashMap<>();
	private final ListenersContainer							_listenerContainer		= Containers.Players();
	private static final String									LAST_CHANGE_VAR			= "faction_last_change";
	private static final String									CLAN_LAST_CHANGE_VAR	= "clan_fraction_last_change";
	
	private static class SingletonHolder
	{
		protected static final FactionBalanceService INSTANCE = new FactionBalanceService();
	}
	
	public static FactionBalanceService getInstance()
	{
		return SingletonHolder.INSTANCE;
	}
	
	private FactionBalanceService()
	{
		loadFromDatabase();
		loadFactionCounts(false);
		registerListeners();
	}
	
	private void registerListeners()
	{
		_listenerContainer.addListener(new ConsumerEventListener(_listenerContainer, EventType.ON_PLAYER_LOGIN, (event) ->
		{
			if (event instanceof OnPlayerLogin)
			{
				onPlayerLogin((OnPlayerLogin) event);
			}
		}, this));
		_listenerContainer.addListener(new ConsumerEventListener(_listenerContainer, EventType.ON_PLAYER_LOGOUT, (event) ->
		{
			if (event instanceof OnPlayerLogout)
			{
				onPlayerLogout((OnPlayerLogout) event);
			}
		}, this));
	}
	
	public void loadFromDatabase()
	{
		totalFactionCounts.clear();
		try (Connection con = DatabaseFactory.getConnection(); PreparedStatement ps = con.prepareStatement("SELECT faction, total_count FROM faction_balance"))
		{
			try (ResultSet rs = ps.executeQuery())
			{
				while (rs.next())
				{
					String factionName = rs.getString("faction");
					int count = rs.getInt("total_count");
					try
					{
						Faction faction = Faction.valueOf(factionName);
						totalFactionCounts.put(faction, count);
					}
					catch (IllegalArgumentException e)
					{
						LOGGER.warning("Invalid faction name " + factionName + " found in faction_balance. Skipping...");
					}
				}
				if (totalFactionCounts.isEmpty())
				{
					LOGGER.info("No faction counts loaded from database. Initializing with 0.");
					totalFactionCounts.put(Faction.FIRE, 0);
					totalFactionCounts.put(Faction.WATER, 0);
				}
			}
		}
		catch (SQLException e)
		{
			LOGGER.log(Level.SEVERE, "Failed to load faction counts from database", e);
		}
	}
	
	public void loadFactionCounts(boolean log)
	{
		onlineFactionCounts.clear();
		for (PlayerInstance player : World.getInstance().getPlayers())
		{
			if (player == null || !player.isOnline() || player.getClient() == null || player.getClient().isDetached())
			{
				continue;
			}
			Faction faction = player.getFaction();
			if (faction == null || faction == Faction.NONE)
			{
				continue;
			}
			onlineFactionCounts.compute(faction, (k, v) -> (v == null) ? 1 : v + 1);
		}
		if (log && Config.LOG_FACTION_DETAILS)
		{
			LOGGER.info("Online faction counts updated: FIRE=" + onlineFactionCounts.getOrDefault(Faction.FIRE, 0) + ", WATER=" + onlineFactionCounts.getOrDefault(Faction.WATER, 0));
		}
	}
	
	public void updateFactionCounts()
	{
		loadFactionCounts(false);
	}
	
	public int getTotalFactionCount(Faction faction)
	{
		return totalFactionCounts.getOrDefault(faction, 0);
	}
	
	public int getOnlineFactionCount(Faction faction)
	{
		return onlineFactionCounts.getOrDefault(faction, 0);
	}
	
	public double getFactionPlayerPercentage(Faction faction)
	{
		int totalFire = getTotalFactionCount(Faction.FIRE);
		int totalWater = getTotalFactionCount(Faction.WATER);
		int total = totalFire + totalWater;
		// LOGGER.info("Calculating faction percentage - Total FIRE: " + totalFire + ", Total WATER: " + totalWater + ", Total: " + total);
		if (total == 0)
		{
			return 0.0; // Return 0% if no data in database
		}
		double percent = (faction == Faction.FIRE ? totalFire : totalWater) * 100.0 / total;
		// LOGGER.info("Using total counts - " + faction + " percent: " + percent + "%");
		return percent;
	}
	
	public Faction assignFaction(PlayerInstance player, Faction selectedFaction)
	{
		if (player == null)
		{
			return null;
		}
		Faction assignedFaction = null;
		if (Config.MANUAL_FACTION_SELECTION && selectedFaction != null)
		{
			double firePercent = getFactionPlayerPercentage(Faction.FIRE);
			double waterPercent = getFactionPlayerPercentage(Faction.WATER);
			// Simulate the new percentage after adding the player
			double newPercent = selectedFaction == Faction.FIRE ? firePercent + (100.0 / (firePercent + waterPercent + 100.0)) : waterPercent + (100.0 / (firePercent + waterPercent + 100.0));
			if (newPercent > Config.FACTION_BALANCE_MAX_PERCENT)
			{
				player.sendMessage("Cannot join " + selectedFaction + ": Faction is too crowded (" + String.format("%.1f", newPercent) + "% > " + Config.FACTION_BALANCE_MAX_PERCENT + "%). Please choose another faction.");
				return null;
			}
			assignedFaction = selectedFaction;
		}
		else if (Config.AUTO_FACTION_SELECTION)
		{
			double firePercent = getFactionPlayerPercentage(Faction.FIRE);
			double waterPercent = getFactionPlayerPercentage(Faction.WATER);
			LOGGER.info("Auto-assigning faction - FIRE%: " + firePercent + "%, WATER%: " + waterPercent);
			if (firePercent >= Config.FACTION_BALANCE_MAX_PERCENT && waterPercent < Config.FACTION_BALANCE_MAX_PERCENT)
			{
				assignedFaction = Faction.WATER;
			}
			else if (waterPercent >= Config.FACTION_BALANCE_MAX_PERCENT && firePercent < Config.FACTION_BALANCE_MAX_PERCENT)
			{
				assignedFaction = Faction.FIRE;
			}
			else if (firePercent < waterPercent)
			{
				assignedFaction = Faction.FIRE;
			}
			else if (firePercent > waterPercent)
			{
				assignedFaction = Faction.WATER;
			}
			else
			{
				assignedFaction = Rnd.nextBoolean() ? Faction.FIRE : Faction.WATER;
			}
		}
		else
		{
			// LOGGER.warning("No faction selection mode enabled! Ensure ManualFactionSelection or AutoFactionSelection is enabled in FactionSystem.ini.");
			return null;
		}
		if (assignedFaction != null)
		{
			totalFactionCounts.compute(assignedFaction, (k, v) -> (v == null) ? 1 : v + 1);
			onlineFactionCounts.compute(assignedFaction, (k, v) -> (v == null) ? 1 : v + 1);
			player.setFaction(assignedFaction);
			if (Config.LOG_FACTION_DETAILS)
			{
				LOGGER.info("Assigned faction " + assignedFaction + " to player " + player.getName() + " (Total in database - FIRE: " + getTotalFactionCount(Faction.FIRE) + ", WATER: " + getTotalFactionCount(Faction.WATER) + ")");
			}
			saveToDatabase();
		}
		return assignedFaction;
	}
	
	public void saveToDatabase()
	{
		try (Connection con = DatabaseFactory.getConnection(); PreparedStatement ps = con.prepareStatement("INSERT INTO faction_balance (faction, total_count, last_updated) VALUES (?, ?, NOW()) " + "ON DUPLICATE KEY UPDATE total_count = ?, last_updated = NOW()"))
		{
			for (Faction faction : Faction.values())
			{
				if (faction == Faction.NONE)
					continue;
				ps.setString(1, faction.name());
				ps.setInt(2, totalFactionCounts.getOrDefault(faction, 0));
				ps.setInt(3, totalFactionCounts.getOrDefault(faction, 0));
				ps.executeUpdate();
			}
			if (Config.LOG_FACTION_DETAILS)
			{
				LOGGER.fine("Saved faction balance data to database.");
			}
		}
		catch (SQLException e)
		{
			LOGGER.log(Level.SEVERE, "Failed to save faction balance data to database: " + e.getMessage(), e);
		}
	}
	
	public void updateAndSaveFactionCounts()
	{
		loadFactionCounts(false);
		saveToDatabase();
	}
	
	private void onPlayerLogin(OnPlayerLogin event)
	{
		PlayerInstance player = event.getPlayer();
		Faction faction = player.getFaction();
		if (faction != null && faction != Faction.NONE)
		{
			onlineFactionCounts.compute(faction, (k, v) -> (v == null) ? 1 : v + 1);
			if (Config.LOG_FACTION_DETAILS)
			{
				LOGGER.fine("Updated online faction counts on login for player " + player.getName() + ": " + faction);
			}
		}
	}
	
	private void onPlayerLogout(OnPlayerLogout event)
	{
		PlayerInstance player = event.getPlayer();
		Faction faction = player.getFaction();
		if (faction != null && faction != Faction.NONE)
		{
			onlineFactionCounts.computeIfPresent(faction, (k, v) -> (v <= 1) ? null : v - 1);
			if (Config.LOG_FACTION_DETAILS)
			{
				LOGGER.fine("Updated online faction counts on logout for player " + player.getName() + ": " + faction);
			}
		}
	}
	
	private boolean isFactionLeader(PlayerInstance player)
	{
		return FactionLeaderManager.getInstance().isFactionLeader(player);
	}
	
	private boolean isMercenary(PlayerInstance player)
	{
		return false; // TODO: Implement mercenary check
	}
	
	private boolean isRegisteredInEvent(PlayerInstance player)
	{
		return player.isInEvents(); // TODO: Verify if this is correct
	}
	
	private boolean isInOlympiadMode(PlayerInstance player)
	{
		return player.isInOlympiadMode();
	}
	
	private boolean isRegisteredInOlympiad(PlayerInstance player)
	{
		return OlympiadManager.getInstance().isRegisteredInComp(player);
	}
	
	private List<ClanWar> getClanWars(Clan clan)
	{
		List<ClanWar> wars = new ArrayList<>();
		for (ClanWar war : ClanTable.getInstance().getClanWars())
		{
			if (war.getAttackerClanId() == clan.getId() || war.getAttackedClanId() == clan.getId())
			{
				wars.add(war);
			}
		}
		return wars;
	}
	
	public boolean changePersonalFaction(PlayerInstance player)
	{
		if (player == null)
			return false;
		if (isMercenary(player))
		{
			player.sendMessage("Mercenaries cannot change faction.");
			return false;
		}
		if (isFactionLeader(player))
		{
			player.sendMessage("Faction leaders cannot change faction.");
			return false;
		}
		Clan clan = player.getClan();
		if (clan != null)
		{
			player.sendMessage("You must leave your clan before changing your faction.");
			return false;
		}
		if (player.isInParty())
		{
			player.sendMessage("Please leave your party before changing faction.");
			return false;
		}
		if (isRegisteredInEvent(player))
		{
			player.sendMessage("Cannot change faction while registered in an event.");
			return false;
		}
		if (isInOlympiadMode(player) || isRegisteredInOlympiad(player))
		{
			player.sendMessage("Cannot change faction while in Olympiad mode or registered for Olympiad.");
			return false;
		}
		Faction currentFaction = player.getFaction();
		if (currentFaction == null || currentFaction == Faction.NONE)
		{
			player.sendMessage("You do not belong to any faction.");
			return false;
		}
		Faction newFaction = currentFaction == Faction.FIRE ? Faction.WATER : Faction.FIRE;
		double currentPercent = getFactionPlayerPercentage(currentFaction);
		double newPercent = getFactionPlayerPercentage(newFaction) + (100.0 / (getFactionPlayerPercentage(Faction.FIRE) + getFactionPlayerPercentage(Faction.WATER) + 100.0));
		if (newPercent > Config.FACTION_BALANCE_MAX_PERCENT)
		{
			player.sendMessage("Cannot change to " + newFaction + ": Faction is too crowded (" + String.format("%.1f", newPercent) + "% > " + Config.FACTION_BALANCE_MAX_PERCENT + "%).");
			return false;
		}
		long reuseTime = player.getVariables().getLong(LAST_CHANGE_VAR, 0);
		long currentTime = System.currentTimeMillis() / 1000;
		if (reuseTime > currentTime)
		{
			long timeLeft = reuseTime - currentTime;
			int hours = (int) (timeLeft / 3600);
			int minutes = (int) ((timeLeft - hours * 3600) / 60);
			player.sendMessage("You must wait " + hours + " hours and " + minutes + " minutes before changing faction again.");
			return false;
		}
		int price = Config.GVE_FRACTION_CHANGE_PERSONAL;
		if (currentPercent >= 60)
		{
			price = 100;
		}
		else if (currentPercent <= 40)
		{
			price = 2000;
		}
		if (!player.destroyItemByItemId("Advanced Clan Donation", Inventory.LCOIN_ID, price, player, true))
		{
			player.sendMessage("You don't have enough L-Coins to change faction.");
			return false;
		}
		player.setFaction(newFaction);
		long time = (System.currentTimeMillis() + TimeUnit.HOURS.toMillis(12)) / 1000;
		player.getVariables().set(LAST_CHANGE_VAR, time);
		updateFactionCounts();
		saveToDatabase();
		player.sendMessage("You have successfully changed your faction to " + newFaction + "!");
		if (Config.LOG_FACTION_DETAILS)
		{
			LOGGER.info("Player " + player.getName() + " changed faction to " + newFaction);
		}
		player.applyFactionColors();
		return true;
	}
	
	public boolean changeClanFaction(PlayerInstance player)
	{
		if (player == null || !player.isClanLeader())
		{
			player.sendMessage("Only clan leaders can change clan faction.");
			return false;
		}
		Clan clan = player.getClan();
		Faction faction = player.getFaction();
		if (faction == null || faction == Faction.NONE)
		{
			player.sendMessage("Your clan does not belong to any faction.");
			return false;
		}
		if (isMercenary(player))
		{
			player.sendMessage("Mercenaries cannot change clan faction.");
			return false;
		}
		if (isFactionLeader(player))
		{
			player.sendMessage("Faction leaders cannot change clan faction.");
			return false;
		}
		double currentPercent = getFactionPlayerPercentage(faction);
		Faction newFaction = faction == Faction.FIRE ? Faction.WATER : Faction.FIRE;
		double newPercent = getFactionPlayerPercentage(newFaction) + (100.0 * clan.getOnlineMembersCount() / (getFactionPlayerPercentage(Faction.FIRE) + getFactionPlayerPercentage(Faction.WATER) + 100.0 * (clan.getOnlineMembersCount() + 1)));
		if (newPercent > Config.FACTION_BALANCE_MAX_PERCENT)
		{
			player.sendMessage("Cannot change to " + newFaction + ": Faction is too crowded (" + String.format("%.1f", newPercent) + "% > " + Config.FACTION_BALANCE_MAX_PERCENT + "%).");
			return false;
		}
		long reuseTime = player.getVariables().getLong(CLAN_LAST_CHANGE_VAR, 0);
		long currentTime = System.currentTimeMillis() / 1000;
		if (reuseTime > currentTime)
		{
			long timeLeft = reuseTime - currentTime;
			int hours = (int) (timeLeft / 3600);
			int minutes = (int) ((timeLeft - hours * 3600) / 60);
			player.sendMessage("Your clan must wait " + hours + " hours and " + minutes + " minutes before changing faction again.");
			return false;
		}
		if (isRegisteredInEvent(player))
		{
			player.sendMessage("Cannot change clan faction while registered in an event.");
			return false;
		}
		if (isInOlympiadMode(player) || isRegisteredInOlympiad(player))
		{
			player.sendMessage("Cannot change clan faction while in Olympiad mode or registered for Olympiad.");
			return false;
		}
		if (player.isInParty())
		{
			player.sendMessage("Please leave your party before changing clan faction.");
			return false;
		}
		int price = Config.GVE_FRACTION_CHANGE_CLAN;
		if (currentPercent >= 60)
		{
			price = 2000;
		}
		else if (currentPercent <= 40)
		{
			price = 10000;
		}
		if (!player.destroyItemByItemId("ClanFactionChange", Inventory.LCOIN_ID, price, player, true))
		{
			player.sendMessage("You don't have enough L-Coins to change clan faction.");
			return false;
		}
		StringBuilder invalidMembers = new StringBuilder();
		for (ClanMember member : clan.getMembers())
		{
			if (member.getObjectId() == player.getObjectId())
				continue;
			if (isFactionLeader(member.getPlayerInstance()))
			{
				invalidMembers.append(member.getName()).append("\n");
				continue;
			}
			if (member.isOnline())
			{
				PlayerInstance clanMember = member.getPlayerInstance();
				if (clanMember.isInParty())
				{
					invalidMembers.append(clanMember.getName()).append("\n");
					continue;
				}
				if (isRegisteredInEvent(clanMember))
				{
					invalidMembers.append(clanMember.getName()).append("\n");
					continue;
				}
				if (isInOlympiadMode(clanMember) || isRegisteredInOlympiad(clanMember))
				{
					invalidMembers.append(clanMember.getName()).append("\n");
					continue;
				}
				if (isMercenary(clanMember))
				{
					invalidMembers.append(clanMember.getName()).append("\n");
					continue;
				}
			}
		}
		if (invalidMembers.length() > 0)
		{
			player.sendMessage("The following clan members do not meet the requirements:\n" + invalidMembers);
			return false;
		}
		new ArrayList<>(getClanWars(clan)).forEach(cw ->
		{
			Clan attackerClan = ClanTable.getInstance().getClan(cw.getAttackerClanId());
			Clan opposingClan = ClanTable.getInstance().getClan(cw.getAttackedClanId());
			ClanTable.getInstance().deleteClanWars(attackerClan.getId(), opposingClan.getId());
		});
		if (Config.LOG_FACTION_DETAILS)
		{
			LOGGER.info("Cleared all clan wars for clan " + clan.getName() + " before faction change.");
		}
		if (clan.getFortId() != 0)
		{
			final Fort fortress = FortManager.getInstance().getFortById(clan.getFortId());
			if (fortress != null)
			{
				fortress.setOwner(null, false);
				clan.setFortId(0);
				LOGGER.info("Removed fortress ownership for clan " + clan.getName());
			}
		}
		if (clan.getCastleId() != 0)
		{
			final Castle castle = CastleManager.getInstance().getCastleById(clan.getCastleId());
			if (castle != null)
			{
				castle.setOwner(null);
				clan.setCastleId(0);
				LOGGER.info("Removed castle ownership for clan " + clan.getName());
			}
		}
		for (ClanMember member : clan.getMembers())
		{
			if (member.isOnline())
			{
				PlayerInstance clanMember = member.getPlayerInstance();
				if (clanMember.isInParty())
				{
					clanMember.leaveParty();
				}
				clanMember.setFaction(newFaction);
				if (clanMember.getObjectId() == player.getObjectId())
				{
					long time = (System.currentTimeMillis() + TimeUnit.HOURS.toMillis(24)) / 1000;
					clanMember.getVariables().set(CLAN_LAST_CHANGE_VAR, time);
				}
				clanMember.applyFactionColors();
			}
			else
			{
				try (Connection con = DatabaseFactory.getConnection(); PreparedStatement ps = con.prepareStatement("UPDATE characters SET faction=? WHERE charId=?"))
				{
					ps.setByte(1, (byte) newFaction.getId());
					ps.setInt(2, member.getObjectId());
					ps.executeUpdate();
					if (Config.LOG_FACTION_DETAILS)
					{
						LOGGER.fine("Updated faction to " + newFaction + " (ID: " + newFaction.getId() + ") for offline clan member " + member.getName());
					}
				}
				catch (SQLException e)
				{
					LOGGER.log(Level.SEVERE, "Failed to update offline clan member's faction", e);
				}
			}
		}
		clan.setFaction(newFaction);
		if (clan.getCastleId() != 0)
		{
			final Castle castle = CastleManager.getInstance().getCastleById(clan.getCastleId());
			if (castle != null)
			{
				CastleSide newSide = newFaction == Faction.FIRE ? CastleSide.DARK : newFaction == Faction.WATER ? CastleSide.LIGHT : CastleSide.NEUTRAL;
				castle.setSide(newSide);
				if (Config.LOG_FACTION_DETAILS)
				{
					LOGGER.info("Updated castle " + castle.getName() + " side to " + newSide + " for clan " + clan.getName());
				}
			}
		}
		updateFactionCounts();
		saveToDatabase();
		SystemMessage sm = new SystemMessage(SystemMessageId.S1_S2);
		sm.addString("The faction of your clan has been changed to");
		sm.addString(newFaction.name());
		clan.broadcastToOnlineMembers(sm);
		if (Config.LOG_FACTION_DETAILS)
		{
			LOGGER.info("Clan " + clan.getName() + " changed faction to " + newFaction);
		}
		return true;
	}
}