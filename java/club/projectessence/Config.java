/*
 * This file is part of the L2J Mobius project.
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.math.BigInteger;
import java.net.Inet6Address;
import java.net.InetAddress;
import java.net.InterfaceAddress;
import java.net.NetworkInterface;
import java.net.SocketException;
import java.net.URL;
import java.net.UnknownHostException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.time.Duration;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.Set;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.regex.Pattern;
import java.util.regex.PatternSyntaxException;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import org.w3c.dom.Document;
import org.w3c.dom.NamedNodeMap;
import org.w3c.dom.Node;

import club.projectessence.commons.enums.ServerMode;
import club.projectessence.commons.util.IXmlReader;
import club.projectessence.commons.util.PropertiesParser;
import club.projectessence.commons.util.StringUtil;
import club.projectessence.gameserver.enums.ChatType;
import club.projectessence.gameserver.enums.ClassId;
import club.projectessence.gameserver.enums.IllegalActionPunishmentType;
import club.projectessence.gameserver.model.Location;
import club.projectessence.gameserver.model.holders.ItemHolder;
import club.projectessence.gameserver.model.holders.SkillHolder;
import club.projectessence.gameserver.model.skills.AbnormalVisualEffect;
import club.projectessence.gameserver.util.FloodProtectorConfig;
import club.projectessence.gameserver.util.Util;

/**
 * This class loads all the game server related configurations from files.<br>
 * The files are usually located in config folder in server root folder.<br>
 * Each configuration has a default value (that should reflect retail behavior).
 */
public class Config
{
	// --------------------------------------------------
	// Constants
	// --------------------------------------------------
	public static final String					EOL												= System.lineSeparator();
	// --------------------------------------------------
	// Config File Definitions
	// --------------------------------------------------
	public static final String					GEOENGINE_CONFIG_FILE							= "./config/GeoEngine.ini";
	public static final String					OLYMPIAD_CONFIG_FILE							= "./config/Olympiad.ini";
	public static final String					SIEGE_CONFIG_FILE								= "./config/Siege.ini";
	public static final String					FORTSIEGE_CONFIG_FILE							= "./config/FortSiege.ini";
	private static final Logger					LOGGER											= Logger.getLogger(Config.class.getName());
	private static final String					ATTENDANCE_CONFIG_FILE							= "./config/AttendanceRewards.ini";
	private static final String					ATTRIBUTE_SYSTEM_FILE							= "./config/AttributeSystem.ini";
	private static final String					CHARACTER_CONFIG_FILE							= "./config/Character.ini";
	private static final String					FEATURE_CONFIG_FILE								= "./config/Feature.ini";
	private static final String					FLOOD_PROTECTOR_CONFIG_FILE						= "./config/FloodProtector.ini";
	private static final String					GENERAL_CONFIG_FILE								= "./config/General.ini";
	private static final String					LOGIN_CONFIG_FILE								= "./config/LoginServer.ini";
	private static final String					NPC_CONFIG_FILE									= "./config/NPC.ini";
	private static final String					PVP_CONFIG_FILE									= "./config/PVP.ini";
	private static final String					RATES_CONFIG_FILE								= "./config/Rates.ini";
	private static final String					SERVER_CONFIG_FILE								= "./config/Server.ini";
	private static final String					TRAINING_CAMP_CONFIG_FILE						= "./config/TrainingCamp.ini";
	private static final String					CHAT_FILTER_FILE								= "./config/chatfilter.txt";
	private static final String					HEXID_FILE										= "./config/hexid.txt";
	private static final String					IPCONFIG_FILE									= "./config/ipconfig.xml";
	private static final String					DISCORD_CONFIG_FILE								= "./config/Discord.ini";
	private static final String					TELEGRAM_CONFIG_FILE							= "./config/Telegram.ini";
	private static final String					WORLD_EXCHANGE_FILE								= "./config/WorldExchange.ini";
	// --------------------------------------------------
	// Custom Config File Definitions
	// --------------------------------------------------
	private static final String					CUSTOM_ALLOWED_PLAYER_RACES_CONFIG_FILE			= "./config/Custom/AllowedPlayerRaces.ini";
	private static final String					CUSTOM_CHAMPION_MONSTERS_CONFIG_FILE			= "./config/Custom/ChampionMonsters.ini";
	private static final String					CUSTOM_CLASS_BALANCE_CONFIG_FILE				= "./config/Custom/ClassBalance.ini";
	private static final String					CUSTOM_COMMUNITY_BOARD_CONFIG_FILE				= "./config/Custom/CommunityBoard.ini";
	private static final String					CUSTOM_CUSTOM_DEPOSITABLE_ITEMS_CONFIG_FILE		= "./config/Custom/CustomDepositableItems.ini";
	private static final String					CUSTOM_CUSTOM_MAIL_MANAGER_CONFIG_FILE			= "./config/Custom/CustomMailManager.ini";
	private static final String					CUSTOM_DELEVEL_MANAGER_CONFIG_FILE				= "./config/Custom/DelevelManager.ini";
	private static final String					CUSTOM_DUALBOX_CHECK_CONFIG_FILE				= "./config/Custom/DualboxCheck.ini";
	private static final String					CUSTOM_FIND_PVP_CONFIG_FILE						= "./config/Custom/FindPvP.ini";
	private static final String					CUSTOM_MERCHANT_ZERO_SELL_PRICE_CONFIG_FILE		= "./config/Custom/MerchantZeroSellPrice.ini";
	private static final String					CUSTOM_NOBLESS_MASTER_CONFIG_FILE				= "./config/Custom/NoblessMaster.ini";
	private static final String					CUSTOM_NPC_STAT_MULTIPLIERS_CONFIG_FILE			= "./config/Custom/NpcStatMultipliers.ini";
	private static final String					CUSTOM_OFFLINE_TRADE_CONFIG_FILE				= "./config/Custom/OfflineTrade.ini";
	private static final String					CUSTOM_PC_CAFE_CONFIG_FILE						= "./config/Custom/PcCafe.ini";
	private static final String					CUSTOM_PREMIUM_SYSTEM_CONFIG_FILE				= "./config/Custom/PremiumSystem.ini";
	private static final String					CUSTOM_PRIVATE_STORE_RANGE_CONFIG_FILE			= "./config/Custom/PrivateStoreRange.ini";
	private static final String					CUSTOM_PVP_ANNOUNCE_CONFIG_FILE					= "./config/Custom/PvpAnnounce.ini";
	private static final String					CUSTOM_PVP_REWARD_ITEM_CONFIG_FILE				= "./config/Custom/PvpRewardItem.ini";
	private static final String					CUSTOM_PVP_TITLE_CONFIG_FILE					= "./config/Custom/PvpTitleColor.ini";
	private static final String					CUSTOM_RANDOM_SPAWNS_CONFIG_FILE				= "./config/Custom/RandomSpawns.ini";
	private static final String					CUSTOM_SAYUNE_FOR_ALL_CONFIG_FILE				= "./config/Custom/SayuneForAll.ini";
	private static final String					CUSTOM_SCREEN_WELCOME_MESSAGE_CONFIG_FILE		= "./config/Custom/ScreenWelcomeMessage.ini";
	private static final String					CUSTOM_SELL_BUFFS_CONFIG_FILE					= "./config/Custom/SellBuffs.ini";
	private static final String					CUSTOM_SERVER_TIME_CONFIG_FILE					= "./config/Custom/ServerTime.ini";
	private static final String					CUSTOM_SCHEME_BUFFER_CONFIG_FILE				= "./config/Custom/ShemeBuffer.ini";
	private static final String					CUSTOM_STARTING_LOCATION_CONFIG_FILE			= "./config/Custom/StartingLocation.ini";
	private static final String					CUSTOM_VOTE_REWARD_CONFIG_FILE					= "./config/Custom/VoteReward.ini";
	private static final String					CUSTOM_INTERFACE_CONFIG_FILE					= "./config/Custom/Interface.ini";
	public static int							GM_AVE_STEP										= 1;
	private static final String					CUSTOM_BONUS_CONFIG_FILE						= "./config/Custom/CustomBonus.ini";
	private static final String					CUSTOM_EVENT_CONFIG_FILE						= "./config/Custom/CustomEvent.ini";
	private static final String					CUSTOM_REWARD_CTV_CONFIG_FILE					= "./config/Custom/RewardCTV.ini";
	private static final String					CUSTOM_CTV_KARMA_CONFIG_FILE					= "./config/Custom/CustomCTVKarma.ini";
	private static final String					CUSTOM_BATTLE_WITH_BALOK_CONFIG_FILE			= "./config/Custom/CustomBattleWithBalok.ini";
	private static final String					CUSTOM_LCOIN_DROP_CONFIG_FILE					= "./config/Custom/CustomLcoinDrop.ini";
	private static final String					CUSTOM_OFFLINE_PLAY_CONFIG_FILE					= "./config/Custom/OfflinePlay.ini";
	private static final String					CUSTOM_FACTION_SYSTEM_CONFIG_FILE				= "./config/Custom/FactionSystem.ini";
	private static final String					CUSTOM_FACTION_LEADER_CONFIG_FILE				= "./config/Custom/FactionLeader.ini";
	private static final String					CUSTOM_FORTRESS_SIEGE_EVENT_CONFIG_FILE			= "./config/Custom/FortressSiegeEvent.ini";
	private static final String					MORALE_BOOST_CONFIG_FILE						= "./config/Custom/MoraleBoost.ini";
	private static final String					CUSTOM_DROP_CALC_CONFIG_FILE					= "./config/Custom/DropCalculator.ini";
	// --------------------------------------------------
	// Variable Definitions
	// --------------------------------------------------
	public static ServerMode					SERVER_MODE										= ServerMode.NONE;
	public static boolean						ENABLE_ATTENDANCE_REWARDS;
	public static boolean						PREMIUM_ONLY_ATTENDANCE_REWARDS;
	public static boolean						ATTENDANCE_REWARDS_SHARE_ACCOUNT;
	public static int							ATTENDANCE_REWARD_DELAY;
	public static boolean						ATTENDANCE_POPUP_START;
	public static boolean						ATTENDANCE_POPUP_WINDOW;
	public static boolean						PLAYER_DELEVEL;
	public static int							DELEVEL_MINIMUM;
	public static boolean						DECREASE_SKILL_LEVEL;
	public static double						ALT_WEIGHT_LIMIT;
	public static int							RUN_SPD_BOOST;
	public static double						RESPAWN_RESTORE_CP;
	public static double						RESPAWN_RESTORE_HP;
	public static double						RESPAWN_RESTORE_MP;
	public static boolean						ENABLE_MODIFY_SKILL_DURATION;
	public static Map<Integer, Integer>			SKILL_DURATION_LIST;
	public static boolean						ENABLE_MODIFY_SKILL_REUSE;
	public static Map<Integer, Integer>			SKILL_REUSE_LIST;
	public static boolean						AUTO_LEARN_SKILLS;
	public static boolean						AUTO_LEARN_FS_SKILLS;
	public static boolean						AUTO_LOOT_HERBS;
	public static byte							BUFFS_MAX_AMOUNT;
	public static byte							DEBUFFS_MAX_AMOUNT;
	public static byte							TRIGGERED_BUFFS_MAX_AMOUNT;
	public static byte							DANCES_MAX_AMOUNT;
	public static boolean						DANCE_CANCEL_BUFF;
	public static boolean						DANCE_CONSUME_ADDITIONAL_MP;
	public static boolean						ALT_STORE_DANCES;
	public static boolean						AUTO_LEARN_DIVINE_INSPIRATION;
	public static boolean						ALT_GAME_CANCEL_BOW;
	public static boolean						ALT_GAME_CANCEL_CAST;
	public static boolean						ALT_GAME_MAGICFAILURES;
	public static boolean						ALT_GAME_STUN_BREAK;
	public static int							PLAYER_FAKEDEATH_UP_PROTECTION;
	public static boolean						STORE_SKILL_COOLTIME;
	public static boolean						SUBCLASS_STORE_SKILL_COOLTIME;
	public static boolean						SUMMON_STORE_SKILL_COOLTIME;
	public static long							EFFECT_TICK_RATIO;
	public static boolean						FAKE_DEATH_UNTARGET;
	public static boolean						FAKE_DEATH_DAMAGE_STAND;
	public static boolean						LIFE_CRYSTAL_NEEDED;
	public static boolean						DIVINE_SP_BOOK_NEEDED;
	public static boolean						ALT_GAME_SUBCLASS_WITHOUT_QUESTS;
	public static boolean						ALT_GAME_SUBCLASS_EVERYWHERE;
	public static boolean						ALLOW_TRANSFORM_WITHOUT_QUEST;
	public static int							FEE_DELETE_TRANSFER_SKILLS;
	public static int							FEE_DELETE_SUBCLASS_SKILLS;
	public static int							FEE_DELETE_DUALCLASS_SKILLS;
	public static boolean						RESTORE_SERVITOR_ON_RECONNECT;
	public static boolean						RESTORE_PET_ON_RECONNECT;
	public static double						MAX_BONUS_EXP;
	public static double						MAX_BONUS_SP;
	public static int							MAX_RUN_SPEED;
	public static int							MAX_PATK;
	public static int							MAX_MATK;
	public static int							MAX_PCRIT_RATE;
	public static int							MAX_MCRIT_RATE;
	public static int							MAX_PATK_SPEED;
	public static int							MAX_MATK_SPEED;
	public static int							MAX_EVASION;
	public static int							MIN_ABNORMAL_STATE_SUCCESS_RATE;
	public static int							MAX_ABNORMAL_STATE_SUCCESS_RATE;
	public static long							MAX_SP;
	public static int							PLAYER_MAXIMUM_LEVEL;
	public static int							MAX_SUBCLASS;
	public static int							BASE_SUBCLASS_LEVEL;
	public static int							BASE_DUALCLASS_LEVEL;
	public static int							MAX_SUBCLASS_LEVEL;
	public static int							MAX_PVTSTORESELL_SLOTS_DWARF;
	public static int							MAX_PVTSTORESELL_SLOTS_OTHER;
	public static int							MAX_PVTSTOREBUY_SLOTS_DWARF;
	public static int							MAX_PVTSTOREBUY_SLOTS_OTHER;
	public static int							INVENTORY_MAXIMUM_NO_DWARF;
	public static int							INVENTORY_MAXIMUM_DWARF;
	public static int							INVENTORY_MAXIMUM_GM;
	public static int							INVENTORY_MAXIMUM_QUEST_ITEMS;
	public static int							WAREHOUSE_SLOTS_DWARF;
	public static int							WAREHOUSE_SLOTS_NO_DWARF;
	public static int							WAREHOUSE_SLOTS_CLAN;
	public static int							DKP_WAREHOUSE_SLOTS_CLAN;
	public static int							DKP_AUCTION_WAREHOUSE_SLOTS_CLAN;
	public static int							ALT_FREIGHT_SLOTS;
	public static int							ALT_FREIGHT_PRICE;
	public static long							MENTOR_PENALTY_FOR_MENTEE_COMPLETE;
	public static long							MENTOR_PENALTY_FOR_MENTEE_LEAVE;
	public static boolean						ALT_GAME_KARMA_PLAYER_CAN_BE_KILLED_IN_PEACEZONE;
	public static boolean						ALT_GAME_KARMA_PLAYER_CAN_SHOP;
	public static boolean						ALT_GAME_KARMA_PLAYER_CAN_TELEPORT;
	public static boolean						ALT_GAME_KARMA_PLAYER_CAN_USE_GK;
	public static boolean						ALT_GAME_KARMA_PLAYER_CAN_TRADE;
	public static boolean						ALT_GAME_KARMA_PLAYER_CAN_USE_WAREHOUSE;
	public static int							MAX_PERSONAL_FAME_POINTS;
	public static int							FORTRESS_ZONE_FAME_TASK_FREQUENCY;
	public static int							FORTRESS_ZONE_FAME_AQUIRE_POINTS;
	public static int							CASTLE_ZONE_FAME_TASK_FREQUENCY;
	public static int							CASTLE_ZONE_FAME_AQUIRE_POINTS;
	public static boolean						FAME_FOR_DEAD_PLAYERS;
	public static boolean						IS_CRAFTING_ENABLED;
	public static boolean						CRAFT_MASTERWORK;
	public static int							DWARF_RECIPE_LIMIT;
	public static int							COMMON_RECIPE_LIMIT;
	public static boolean						ALT_GAME_CREATION;
	public static double						ALT_GAME_CREATION_SPEED;
	public static double						ALT_GAME_CREATION_XP_RATE;
	public static double						ALT_GAME_CREATION_RARE_XPSP_RATE;
	public static double						ALT_GAME_CREATION_SP_RATE;
	public static boolean						ALT_CLAN_LEADER_INSTANT_ACTIVATION;
	public static int							ALT_CLAN_JOIN_MINS;
	public static int							ALT_CLAN_CREATE_MINS;
	public static int							ALT_CLAN_DISSOLVE_MINS;
	public static int							ALT_ALLY_JOIN_MINS_WHEN_LEAVED;
	public static int							ALT_ALLY_JOIN_MINS_WHEN_DISMISSED;
	public static int							ALT_ACCEPT_CLAN_MINS_WHEN_DISMISSED;
	public static int							ALT_CREATE_ALLY_MINS_WHEN_DISSOLVED;
	public static int							ALT_MAX_NUM_OF_CLANS_IN_ALLY;
	public static int							ALT_CLAN_MEMBERS_FOR_WAR;
	public static boolean						ALT_MEMBERS_CAN_WITHDRAW_FROM_CLANWH;
	public static long							ALT_CLAN_MEMBERS_TIME_FOR_BONUS;
	public static boolean						REMOVE_CASTLE_CIRCLETS;
	public static int							ALT_PARTY_MAX_MEMBERS;
	public static int							ALT_PARTY_RANGE;
	public static boolean						ALT_LEAVE_PARTY_LEADER;
	public static boolean						ALT_COMMAND_CHANNEL_FRIENDS;
	public static int							MAX_CC_MEMBERS;
	public static boolean						INITIAL_EQUIPMENT_EVENT;
	public static long							STARTING_ADENA;
	public static byte							STARTING_LEVEL;
	public static int							STARTING_SP;
	public static String						NEWBIE_TITLE;
	public static long							MAX_ADENA;
	public static boolean						AUTO_LOOT;
	public static boolean						AUTO_LOOT_RAIDS;
	public static boolean						AUTO_LOOT_SLOT_LIMIT;
	public static int							LOOT_RAIDS_PRIVILEGE_INTERVAL;
	public static int							LOOT_RAIDS_PRIVILEGE_CC_SIZE;
	public static List<Integer>					AUTO_LOOT_ITEM_IDS;
	public static boolean						ENABLE_KEYBOARD_MOVEMENT;
	public static int							UNSTUCK_INTERVAL;
	public static int							TELEPORT_WATCHDOG_TIMEOUT;
	public static int							PLAYER_SPAWN_PROTECTION;
	public static int							PLAYER_TELEPORT_PROTECTION;
	public static boolean						RANDOM_RESPAWN_IN_TOWN_ENABLED;
	public static boolean						OFFSET_ON_TELEPORT_ENABLED;
	public static int							MAX_OFFSET_ON_TELEPORT;
	public static boolean						TELEPORT_WHILE_SIEGE_IN_PROGRESS;
	public static boolean						PETITIONING_ALLOWED;
	public static int							MAX_PETITIONS_PER_PLAYER;
	public static int							MAX_PETITIONS_PENDING;
	public static int							MAX_FREE_TELEPORT_LEVEL;
	public static int							MAX_NEWBIE_BUFF_LEVEL;
	public static int							DELETE_HOURS;
	public static String						PARTY_XP_CUTOFF_METHOD;
	public static double						PARTY_XP_CUTOFF_PERCENT;
	public static int							PARTY_XP_CUTOFF_LEVEL;
	public static int[][]						PARTY_XP_CUTOFF_GAPS;
	public static int[]							PARTY_XP_CUTOFF_GAP_PERCENTS;
	public static boolean						DISABLE_TUTORIAL;
	public static boolean						STORE_RECIPE_SHOPLIST;
	public static boolean						STORE_UI_SETTINGS;
	public static String[]						FORBIDDEN_NAMES;
	public static boolean						SILENCE_MODE_EXCLUDE;
	// --------------------------------------------------
	// Castle Settings
	// --------------------------------------------------
	public static long							CS_TELE_FEE_RATIO;
	public static int							CS_TELE1_FEE;
	public static int							CS_TELE2_FEE;
	public static long							CS_MPREG_FEE_RATIO;
	public static int							CS_MPREG1_FEE;
	public static int							CS_MPREG2_FEE;
	public static long							CS_HPREG_FEE_RATIO;
	public static int							CS_HPREG1_FEE;
	public static int							CS_HPREG2_FEE;
	public static long							CS_EXPREG_FEE_RATIO;
	public static int							CS_EXPREG1_FEE;
	public static int							CS_EXPREG2_FEE;
	public static long							CS_SUPPORT_FEE_RATIO;
	public static int							CS_SUPPORT1_FEE;
	public static int							CS_SUPPORT2_FEE;
	public static boolean						ENABLE_DATABASE_LOGGER;
	public static boolean						BALOK_ENABLED;
	public static boolean						BALOK_LOGGER_ENABLED;
	public static boolean						ENCHANT_CHALLENGE_POINTS_ENABLED;
	public static boolean						HELLBOUND_ENABLE;
	public static boolean						HELLBOUND_LOGGER_ENABLE;
	public static boolean						HELLBOUND_GM_FORCE_OPEN;
	public static boolean						HELLBOUND_GM_FORCE_CLOSE;
	public static int							HELLBOUND_GM_FORCE_OPEN_TIME;
	public static boolean						ATINGO_ENABLE;
	public static boolean						ATINGO_LOGGER_ENABLE;
	public static long							ATINGO_RESPAWN_TIME;
	public static long							ATINGO_MOVE_TIME;
	public static long							PET_DELETE_TIMEOUT;
	public static int							SIN_EATER_CHANCE;
	public static boolean						PURGE_ENABLE;
	public static boolean						FRINTEZZA_ENABLE;
	public static int							MIN_LEVEL_ENTER_FRINTEZZA;
	public static boolean						DWELLING_OF_SPIRITS_ENABLE;
	public static boolean						DWELLING_OF_SPIRITS_LOGGER_ENABLE;
	public static int							DWELLING_OF_SPIRITS_PORTAL_OPEN_CHANCE;
	public static boolean						DREAM_DUNGEON_ENABLE;
	public static boolean						TOI_REINFORCED_MOBS_ENABLED;
	public static int							TOI_REINFORCED_MOBS_KILL_COUNT;
	public static int							TOI_REINFORCED_MOBS_DURATION;
	public static List<Integer>					SIEGE_HOUR_LIST;
	public static int							CASTLE_BUY_TAX_NEUTRAL;
	public static int							CASTLE_BUY_TAX_LIGHT;
	public static int							CASTLE_BUY_TAX_DARK;
	public static int							CASTLE_SELL_TAX_NEUTRAL;
	public static int							CASTLE_SELL_TAX_LIGHT;
	public static int							CASTLE_SELL_TAX_DARK;
	public static int							OUTER_DOOR_UPGRADE_PRICE2;
	public static int							OUTER_DOOR_UPGRADE_PRICE3;
	public static int							OUTER_DOOR_UPGRADE_PRICE5;
	public static int							INNER_DOOR_UPGRADE_PRICE2;
	public static int							INNER_DOOR_UPGRADE_PRICE3;
	public static int							INNER_DOOR_UPGRADE_PRICE5;
	public static int							WALL_UPGRADE_PRICE2;
	public static int							WALL_UPGRADE_PRICE3;
	public static int							WALL_UPGRADE_PRICE5;
	public static int							TRAP_UPGRADE_PRICE1;
	public static int							TRAP_UPGRADE_PRICE2;
	public static int							TRAP_UPGRADE_PRICE3;
	public static int							TRAP_UPGRADE_PRICE4;
	// --------------------------------------------------
	// Fortress Settings
	// --------------------------------------------------
	public static long							FS_TELE_FEE_RATIO;
	public static int							FS_TELE1_FEE;
	public static int							FS_TELE2_FEE;
	public static long							FS_MPREG_FEE_RATIO;
	public static int							FS_MPREG1_FEE;
	public static int							FS_MPREG2_FEE;
	public static long							FS_HPREG_FEE_RATIO;
	public static int							FS_HPREG1_FEE;
	public static int							FS_HPREG2_FEE;
	public static long							FS_EXPREG_FEE_RATIO;
	public static int							FS_EXPREG1_FEE;
	public static int							FS_EXPREG2_FEE;
	public static long							FS_SUPPORT_FEE_RATIO;
	public static int							FS_SUPPORT1_FEE;
	public static int							FS_SUPPORT2_FEE;
	public static int							FS_BLOOD_OATH_COUNT;
	public static int							FS_UPDATE_FRQ;
	public static int							FS_MAX_SUPPLY_LEVEL;
	public static int							FS_FEE_FOR_CASTLE;
	public static int							FS_MAX_OWN_TIME;
	// --------------------------------------------------
	// Feature Settings
	// --------------------------------------------------
	public static int							TAKE_FORT_POINTS;
	public static int							LOOSE_FORT_POINTS;
	public static int							TAKE_CASTLE_POINTS;
	public static int							LOOSE_CASTLE_POINTS;
	public static int							CASTLE_DEFENDED_POINTS;
	public static int							FESTIVAL_WIN_POINTS;
	public static int							HERO_POINTS;
	public static int							ROYAL_GUARD_COST;
	public static int							KNIGHT_UNIT_COST;
	public static int							KNIGHT_REINFORCE_COST;
	public static int							BALLISTA_POINTS;
	public static int							BLOODALLIANCE_POINTS;
	public static int							BLOODOATH_POINTS;
	public static int							KNIGHTSEPAULETTE_POINTS;
	public static int							REPUTATION_SCORE_PER_KILL;
	public static int							JOIN_ACADEMY_MIN_REP_SCORE;
	public static int							JOIN_ACADEMY_MAX_REP_SCORE;
	public static int							CLAN_LEVEL_6_COST;
	public static int							CLAN_LEVEL_7_COST;
	public static int							CLAN_LEVEL_8_COST;
	public static int							CLAN_LEVEL_9_COST;
	public static int							CLAN_LEVEL_10_COST;
	public static int							CLAN_LEVEL_11_COST;
	public static int							CLAN_LEVEL_6_REQUIREMENT;
	public static int							CLAN_LEVEL_7_REQUIREMENT;
	public static int							CLAN_LEVEL_8_REQUIREMENT;
	public static int							CLAN_LEVEL_9_REQUIREMENT;
	public static int							CLAN_LEVEL_10_REQUIREMENT;
	public static int							CLAN_LEVEL_11_REQUIREMENT;
	public static boolean						ALLOW_WYVERN_ALWAYS;
	public static boolean						ALLOW_WYVERN_DURING_SIEGE;
	public static boolean						ALLOW_MOUNTS_DURING_SIEGE;
	public static int							EXP_FOR_MAGIC_LAMP;
	public static int							MAX_MAGIC_LAMP_AMOUNT;
	public static int							SHARE_POSITION_CREATE_PRICE;
	public static int							SHARE_POSITION_TELEPORT_PRICE;
	public static boolean						ORC_FORTRESS_ENABLE;
	public static boolean						COLLECTIONS_ENABLED;
	public static boolean						KEBER_ENABLED;
	public static int							HIT_COUNT;
	public static int							TIME_DESTROY_BARRIER;
	// --------------------------------------------------
	// General Settings
	// --------------------------------------------------
	public static int							CACHE_TIME_PRIVATE_STORE;
	public static int							DEFAULT_ACCESS_LEVEL;
	public static boolean						SERVER_GMONLY;
	public static boolean						GM_HERO_AURA;
	public static boolean						GM_STARTUP_BUILDER_HIDE;
	public static boolean						GM_STARTUP_INVULNERABLE;
	public static boolean						GM_STARTUP_INVISIBLE;
	public static boolean						GM_STARTUP_SILENCE;
	public static boolean						GM_STARTUP_AUTO_LIST;
	public static boolean						GM_STARTUP_DIET_MODE;
	public static boolean						GM_ITEM_RESTRICTION;
	public static boolean						GM_SKILL_RESTRICTION;
	public static boolean						GM_TRADE_RESTRICTED_ITEMS;
	public static boolean						GM_RESTART_FIGHTING;
	public static boolean						GM_ANNOUNCER_NAME;
	public static boolean						GM_GIVE_SPECIAL_SKILLS;
	public static boolean						GM_GIVE_SPECIAL_AURA_SKILLS;
	public static boolean						GM_DEBUG_HTML_PATHS;
	public static boolean						USE_SUPER_HASTE_AS_GM_SPEED;
	public static boolean						LOG_CHAT;
	public static boolean						LOG_AUTO_ANNOUNCEMENTS;
	public static boolean						LOG_ITEMS;
	public static boolean						LOG_ITEMS_SMALL_LOG;
	public static boolean						LOG_ITEM_ENCHANTS;
	public static boolean						LOG_SKILL_ENCHANTS;
	public static boolean						GMAUDIT;
	public static boolean						SKILL_CHECK_ENABLE;
	public static boolean						SKILL_CHECK_REMOVE;
	public static boolean						SKILL_CHECK_GM;
	public static boolean						HTML_ACTION_CACHE_DEBUG;
	public static boolean						DEVELOPER;
	public static boolean						ALT_DEV_NO_QUESTS;
	public static boolean						ALT_DEV_NO_SPAWNS;
	public static boolean						ALT_DEV_SHOW_QUESTS_LOAD_IN_LOGS;
	public static boolean						ALT_DEV_SHOW_SCRIPTS_LOAD_IN_LOGS;
	public static int							SCHEDULED_THREAD_POOL_COUNT;
	public static int							INSTANT_THREAD_POOL_COUNT;
	public static int							SCHEDULED_POOL_COUNT;
	public static int							INSTANT_POOL_COUNT;
	public static int							IO_PACKET_THREAD_CORE_SIZE;
	public static boolean						THREADS_FOR_LOADING;
	public static int							AUTO_PLAY_THREAD_COUNT;
	public static int							AUTO_USE_THREAD_COUNT;
	public static int							ATTACKABLE_AI_THREAD_COUNT;
	public static int							RESPAWN_MANAGER_THREAD_COUNT;
	public static boolean						DEADLOCK_DETECTOR;
	public static int							DEADLOCK_CHECK_INTERVAL;
	public static boolean						RESTART_ON_DEADLOCK;
	public static boolean						ALLOW_DISCARDITEM;
	public static int							AUTODESTROY_ITEM_AFTER;
	public static int							HERB_AUTO_DESTROY_TIME;
	public static List<Integer>					LIST_PROTECTED_ITEMS;
	public static boolean						DATABASE_CLEAN_UP;
	public static int							CHAR_DATA_STORE_INTERVAL;
	public static int							CLAN_VARIABLES_STORE_INTERVAL;
	public static boolean						LAZY_ITEMS_UPDATE;
	public static boolean						UPDATE_ITEMS_ON_CHAR_STORE;
	public static boolean						DESTROY_DROPPED_PLAYER_ITEM;
	public static boolean						DESTROY_EQUIPABLE_PLAYER_ITEM;
	public static boolean						DESTROY_ALL_ITEMS;
	public static boolean						SAVE_DROPPED_ITEM;
	public static boolean						EMPTY_DROPPED_ITEM_TABLE_AFTER_LOAD;
	public static int							SAVE_DROPPED_ITEM_INTERVAL;
	public static boolean						CLEAR_DROPPED_ITEM_TABLE;
	public static boolean						ORDER_QUEST_LIST_BY_QUESTID;
	public static boolean						AUTODELETE_INVALID_QUEST_DATA;
	public static boolean						ENABLE_STORY_QUEST_BUFF_REWARD;
	public static boolean						MULTIPLE_ITEM_DROP;
	public static boolean						FORCE_INVENTORY_UPDATE;
	public static boolean						LAZY_CACHE;
	public static boolean						CHECK_HTML_ENCODING;
	public static boolean						CACHE_CHAR_NAMES;
	public static int							MIN_NPC_ANIMATION;
	public static int							MAX_NPC_ANIMATION;
	public static int							MIN_MONSTER_ANIMATION;
	public static int							MAX_MONSTER_ANIMATION;
	public static boolean						ENABLE_FALLING_DAMAGE;
	public static boolean						GRIDS_ALWAYS_ON;
	public static int							GRID_NEIGHBOR_TURNON_TIME;
	public static int							GRID_NEIGHBOR_TURNOFF_TIME;
	public static int							PEACE_ZONE_MODE;
	public static String						DEFAULT_GLOBAL_CHAT;
	public static String						DEFAULT_TRADE_CHAT;
	public static boolean						ENABLE_WORLD_CHAT;
	public static int							MINIMUM_CHAT_LEVEL;
	public static boolean						ALLOW_WAREHOUSE;
	public static boolean						WAREHOUSE_CACHE;
	public static int							WAREHOUSE_CACHE_TIME;
	public static boolean						ALLOW_REFUND;
	public static boolean						ALLOW_MAIL;
	public static boolean						ALLOW_ATTACHMENTS;
	public static boolean						ALLOW_WEAR;
	public static int							WEAR_DELAY;
	public static int							WEAR_PRICE;
	public static int							INSTANCE_FINISH_TIME;
	public static boolean						RESTORE_PLAYER_INSTANCE;
	public static int							EJECT_DEAD_PLAYER_TIME;
	public static boolean						ALLOW_RACE;
	public static boolean						ALLOW_WATER;
	public static boolean						ALLOW_FISHING;
	public static boolean						ALLOW_BOAT;
	public static int							BOAT_BROADCAST_RADIUS;
	public static boolean						ALLOW_CURSED_WEAPONS;
	public static boolean						ALLOW_MANOR;
	public static boolean						SERVER_NEWS;
	public static boolean						ENABLE_COMMUNITY_BOARD;
	public static String						BBS_DEFAULT;
	public static boolean						USE_SAY_FILTER;
	public static String						CHAT_FILTER_CHARS;
	public static Set<ChatType>					BAN_CHAT_CHANNELS;
	public static int							WORLD_CHAT_MIN_LEVEL;
	public static int							WORLD_CHAT_POINTS_PER_DAY;
	public static Duration						WORLD_CHAT_INTERVAL;
	public static int							ALT_MANOR_REFRESH_TIME;
	public static int							ALT_MANOR_REFRESH_MIN;
	public static int							ALT_MANOR_APPROVE_TIME;
	public static int							ALT_MANOR_APPROVE_MIN;
	public static int							ALT_MANOR_MAINTENANCE_MIN;
	public static boolean						ALT_MANOR_SAVE_ALL_ACTIONS;
	public static int							ALT_MANOR_SAVE_PERIOD_RATE;
	public static boolean						ALT_ITEM_AUCTION_ENABLED;
	public static int							ALT_ITEM_AUCTION_EXPIRED_AFTER;
	public static long							ALT_ITEM_AUCTION_TIME_EXTENDS_ON_BID;
	public static IllegalActionPunishmentType	DEFAULT_PUNISH;
	public static int							DEFAULT_PUNISH_PARAM;
	public static boolean						ONLY_GM_ITEMS_FREE;
	public static boolean						JAIL_IS_PVP;
	public static boolean						JAIL_DISABLE_CHAT;
	public static boolean						JAIL_DISABLE_TRANSACTION;
	public static boolean						CUSTOM_NPC_DATA;
	public static boolean						CUSTOM_TELEPORT_TABLE;
	public static boolean						CUSTOM_SKILLS_LOAD;
	public static boolean						CUSTOM_ITEMS_LOAD;
	public static boolean						CUSTOM_MULTISELL_LOAD;
	public static boolean						CUSTOM_BUYLIST_LOAD;
	public static int							ALT_BIRTHDAY_GIFT;
	public static String						ALT_BIRTHDAY_MAIL_SUBJECT;
	public static String						ALT_BIRTHDAY_MAIL_TEXT;
	public static boolean						ENABLE_BLOCK_CHECKER_EVENT;
	public static int							MIN_BLOCK_CHECKER_TEAM_MEMBERS;
	public static boolean						HBCE_FAIR_PLAY;
	public static int							STORE_HISTORY_LIMIT;
	public static int							PLAYER_MOVEMENT_BLOCK_TIME;
	public static int							ABILITY_MAX_POINTS;
	public static long							ABILITY_POINTS_RESET_ADENA;
	public static boolean						TOI_MAX_Z_ENABLED;
	public static int							TOI_MAX_Z;
	// --------------------------------------------------
	// FloodProtector Settings
	// --------------------------------------------------
	public static FloodProtectorConfig			FLOOD_PROTECTOR_USE_ITEM;
	public static FloodProtectorConfig			FLOOD_PROTECTOR_ROLL_DICE;
	public static FloodProtectorConfig			FLOOD_PROTECTOR_FIREWORK;
	public static FloodProtectorConfig			FLOOD_PROTECTOR_ITEM_PET_SUMMON;
	public static FloodProtectorConfig			FLOOD_PROTECTOR_HERO_VOICE;
	public static FloodProtectorConfig			FLOOD_PROTECTOR_GLOBAL_CHAT;
	public static FloodProtectorConfig			FLOOD_PROTECTOR_SUBCLASS;
	public static FloodProtectorConfig			FLOOD_PROTECTOR_DROP_ITEM;
	public static FloodProtectorConfig			FLOOD_PROTECTOR_SERVER_BYPASS;
	public static FloodProtectorConfig			FLOOD_PROTECTOR_MULTISELL;
	public static FloodProtectorConfig			FLOOD_PROTECTOR_TRANSACTION;
	public static FloodProtectorConfig			FLOOD_PROTECTOR_MANUFACTURE;
	public static FloodProtectorConfig			FLOOD_PROTECTOR_MANOR;
	public static FloodProtectorConfig			FLOOD_PROTECTOR_SENDMAIL;
	public static FloodProtectorConfig			FLOOD_PROTECTOR_CHARACTER_SELECT;
	public static FloodProtectorConfig			FLOOD_PROTECTOR_ITEM_AUCTION;
	// --------------------------------------------------
	// NPC Settings
	// --------------------------------------------------
	public static boolean						ANNOUNCE_MAMMON_SPAWN;
	public static boolean						ALT_MOB_AGRO_IN_PEACEZONE;
	public static boolean						ALT_ATTACKABLE_NPCS;
	public static boolean						ALT_GAME_VIEWNPC;
	public static boolean						SHOW_NPC_LEVEL;
	public static boolean						SHOW_NPC_AGGRESSION;
	public static boolean						SHOW_CREST_WITHOUT_QUEST;
	public static boolean						ENABLE_RANDOM_ENCHANT_EFFECT;
	public static int							MIN_NPC_LEVEL_DMG_PENALTY;
	public static float[]						NPC_DMG_PENALTY;
	public static float[]						NPC_CRIT_DMG_PENALTY;
	public static float[]						NPC_SKILL_DMG_PENALTY;
	public static int							MIN_NPC_LEVEL_MAGIC_PENALTY;
	public static float[]						NPC_SKILL_CHANCE_PENALTY;
	public static int							DEFAULT_CORPSE_TIME;
	public static int							SPOILED_CORPSE_EXTEND_TIME;
	public static int							CORPSE_CONSUME_SKILL_ALLOWED_TIME_BEFORE_DECAY;
	public static int							MAX_DRIFT_RANGE;
	public static boolean						AGGRO_DISTANCE_CHECK_ENABLED;
	public static boolean						AGGRO_DISTANCE_CHECK_RAIDS;
	public static boolean						AGGRO_DISTANCE_CHECK_INSTANCES;
	public static boolean						AGGRO_DISTANCE_CHECK_RESTORE_LIFE;
	public static boolean						AGGRO_DISTANCE_CHECK_RAID_RESTORE_LIFE;
	public static boolean						GUARD_ATTACK_AGGRO_MOB;
	public static double						RAID_HP_REGEN_MULTIPLIER;
	public static double						RAID_MP_REGEN_MULTIPLIER;
	public static double						RAID_PDEFENCE_MULTIPLIER;
	public static double						RAID_MDEFENCE_MULTIPLIER;
	public static double						RAID_PATTACK_MULTIPLIER;
	public static double						RAID_MATTACK_MULTIPLIER;
	public static double						RAID_MINION_RESPAWN_TIMER;
	public static Map<Integer, Integer>			MINIONS_RESPAWN_TIME;
	public static float							RAID_MIN_RESPAWN_MULTIPLIER;
	public static float							RAID_MAX_RESPAWN_MULTIPLIER;
	public static boolean						RAID_DISABLE_CURSE;
	public static boolean						FORCE_DELETE_MINIONS;
	public static int							RAID_CHAOS_TIME;
	public static int							GRAND_CHAOS_TIME;
	public static int							MINION_CHAOS_TIME;
	public static int							INVENTORY_MAXIMUM_PET;
	public static double						PET_HP_REGEN_MULTIPLIER;
	public static double						PET_MP_REGEN_MULTIPLIER;
	public static int							SAYHA_GRACE_CONSUME_BY_MOB;
	public static int							SAYHA_GRACE_CONSUME_BY_BOSS;
	public static boolean						NPC_RANDOM_WALKING_ENABLED;
	public static boolean						NPC_RANDOM_ANIMATION_ENABLED;
	// --------------------------------------------------
	// PvP Settings
	// --------------------------------------------------
	public static boolean						KARMA_DROP_GM;
	public static int							KARMA_PK_LIMIT;
	public static String						KARMA_NONDROPPABLE_PET_ITEMS;
	public static String						KARMA_NONDROPPABLE_ITEMS;
	public static int[]							KARMA_LIST_NONDROPPABLE_PET_ITEMS;
	public static int[]							KARMA_LIST_NONDROPPABLE_ITEMS;
	public static boolean						ANTIFEED_ENABLE;
	public static boolean						ANTIFEED_DUALBOX;
	public static boolean						ANTIFEED_DISCONNECTED_AS_DUALBOX;
	public static int							ANTIFEED_INTERVAL;
	public static boolean						VAMPIRIC_ATTACK_AFFECTS_PVP;
	public static boolean						MP_VAMPIRIC_ATTACK_AFFECTS_PVP;
	public static boolean						ANNOUNCE_GAINAK_SIEGE;
	// --------------------------------------------------
	// Rate Settings
	// --------------------------------------------------
	public static boolean						EXP_EVENT_ENABLED;
	public static float							RATE_XP;
	public static float							RATE_SP;
	public static float							RATE_PARTY_XP;
	public static float							RATE_PARTY_SP;
	public static float							RATE_INSTANCE_XP;
	public static float							RATE_INSTANCE_SP;
	public static float							RATE_INSTANCE_PARTY_XP;
	public static float							RATE_INSTANCE_PARTY_SP;
	public static float							RATE_RAIDBOSS_POINTS;
	public static float							RATE_EXTRACTABLE;
	public static int							RATE_LCOIN_MIN;
	public static int							RATE_LCOIN_MAX;
	public static int							RATE_LCOIN_MIN_RMT_ZONES;
	public static int							RATE_LCOIN_MAX_RMT_ZONES;
	public static int							RATE_LCOIN_MIN_HB;
	public static int							RATE_LCOIN_MAX_HB;
	public static int							LCOIN_DROP_DELAY;
	public static boolean						EVENT_FIXED_DROP_ENABLED;
	public static int							EVENT_FIXED_DROP_DELAY;
	public static int							RATE_DROP_MANOR;
	public static float							RATE_QUEST_DROP;
	public static float							RATE_QUEST_REWARD;
	public static float							RATE_QUEST_REWARD_XP;
	public static float							RATE_QUEST_REWARD_SP;
	public static float							RATE_QUEST_REWARD_ADENA;
	public static boolean						RATE_QUEST_REWARD_USE_MULTIPLIERS;
	public static float							RATE_QUEST_REWARD_POTION;
	public static float							RATE_QUEST_REWARD_SCROLL;
	public static float							RATE_QUEST_REWARD_RECIPE;
	public static float							RATE_QUEST_REWARD_MATERIAL;
	public static float							RATE_DEATH_DROP_AMOUNT_MULTIPLIER;
	public static float							RATE_SPOIL_DROP_AMOUNT_MULTIPLIER;
	public static float							RATE_HERB_DROP_AMOUNT_MULTIPLIER;
	public static float							RATE_RAID_DROP_AMOUNT_MULTIPLIER;
	public static float							RATE_DEATH_DROP_CHANCE_MULTIPLIER;
	public static float							RATE_SPOIL_DROP_CHANCE_MULTIPLIER;
	public static float							RATE_HERB_DROP_CHANCE_MULTIPLIER;
	public static float							RATE_RAID_DROP_CHANCE_MULTIPLIER;
	public static Map<Integer, Float>			RATE_DROP_AMOUNT_BY_ID;
	public static Map<Integer, Float>			RATE_DROP_CHANCE_BY_ID;
	public static int							DROP_MAX_OCCURRENCES_NORMAL;
	public static int							DROP_MAX_OCCURRENCES_RAIDBOSS;
	public static int							DROP_ADENA_MIN_LEVEL_DIFFERENCE;
	public static int							DROP_ADENA_MAX_LEVEL_DIFFERENCE;
	public static double						DROP_ADENA_MIN_LEVEL_GAP_CHANCE;
	public static int							DROP_ITEM_MIN_LEVEL_DIFFERENCE;
	public static int							DROP_ITEM_MAX_LEVEL_DIFFERENCE;
	public static double						DROP_ITEM_MIN_LEVEL_GAP_CHANCE;
	public static float							RATE_KARMA_LOST;
	public static float							RATE_KARMA_EXP_LOST;
	public static float							RATE_SIEGE_GUARDS_PRICE;
	public static int							PLAYER_DROP_LIMIT;
	public static int							PLAYER_RATE_DROP;
	public static int							PLAYER_RATE_DROP_ITEM;
	public static int							PLAYER_RATE_DROP_EQUIP;
	public static int							PLAYER_RATE_DROP_EQUIP_WEAPON;
	public static float							PET_XP_RATE;
	public static int							PET_FOOD_RATE;
	public static float							SINEATER_XP_RATE;
	public static int							KARMA_DROP_LIMIT;
	public static int							KARMA_RATE_DROP;
	public static int							KARMA_RATE_DROP_ITEM;
	public static int							KARMA_RATE_DROP_EQUIP;
	public static int							KARMA_RATE_DROP_EQUIP_WEAPON;
	// --------------------------------------------------
	// Server Settings
	// --------------------------------------------------
	public static boolean						ASYNC_MMOCORE_AUTO_READ;
	public static int							ASYNC_MMOCORE_FAIRNESS_BUCKETS;
	public static int							PORT_GAME;
	public static int							PORT_LOGIN;
	public static String						LOGIN_BIND_ADDRESS;
	public static int							LOGIN_TRY_BEFORE_BAN;
	public static int							LOGIN_BLOCK_AFTER_BAN;
	public static boolean						ONLY_CHARACTER_CREATE;
	public static boolean						BROADCAST_SHOTS_SELF_ONLY						= false;
	public static boolean						BROADCAST_POTIONS_SELF_ONLY						= false;
	public static boolean						PACKET_TRACK_ENABLED							= false;
	public static String						PACKET_TRACK_NICK								= null;
	public static boolean						PACKET_TRACK_CLIENT								= false;
	public static boolean						PACKET_TRACK_SERVER								= false;
	public static int							MOB_RESPAWN_TIME_1_10							= 2;
	public static int							MOB_RESPAWN_TIME_11_20							= 3;
	public static int							MOB_RESPAWN_TIME_21_30							= 4;
	public static int							MOB_RESPAWN_TIME_31_40							= 5;
	public static int							MOB_RESPAWN_TIME_41_50							= 6;
	public static int							MOB_RESPAWN_TIME_51_60							= 7;
	public static int							MOB_RESPAWN_TIME_61_70							= 8;
	public static int							MOB_RESPAWN_TIME_71_80							= 9;
	public static int							MOB_RESPAWN_TIME_81								= 13;
	public static boolean						TEST											= false;
	public static boolean						DEBUG											= false;
	public static String						DEBUG_CHAR_NAME									= null;
	public static boolean						NO_TRIGGER_EFFECTS								= false;
	public static boolean						COUNT_LOOT_BOXES								= true;
	public static boolean						COUNT_LCOIN_FARM								= true;
	// Event Sibi Stats
	public static boolean						COUNT_BALTHUS_KNIGHT_MARK						= true;
	public static boolean						COUNT_LIFE_CONTROL_TOWER						= true;
	public static boolean						COUNT_MID_GRADE_HP_POTION						= true;
	public static boolean						COUNT_SCROLL_BOOST_ATTACK						= true;
	public static boolean						COUNT_SCROLL_BOOST_DEFENSE						= true;
	public static boolean						COUNT_SAYHA_COOKIE								= true;
	public static boolean						COUNT_SAYHA_BLESSING							= true;
	public static boolean						CLEAR_PACKETS_TO_WRITE_ENABLED					= true;
	public static boolean						CLEAR_PACKETS_TO_WRITE_LOG_ENABLED				= false;
	public static int							CLEAR_PACKETS_TO_WRITE_FROM						= 100_000;
	public static int							COMMON_PACKETS_BC_RANGE							= 850;
	public static int							SKILL_BC_RANGE									= 1200;
	public static int							NOT_CRITICAL_BROADCASTS_DELAY					= 0;
	public static int							SKILL_LIST_DELAY								= 100;
	public static int							CLIENT_CLOSE_DELAY								= 2000;
	public static List<String>					NO_BOX_LIMIT_HWIDS								= new ArrayList<>();
	public static String						GAMESERVER_HOSTNAME;
	public static String						DATABASE_DRIVER;
	public static String						DATABASE_URL;
	public static String						DATABASE_LOGIN;
	public static String						DATABASE_PASSWORD;
	public static int							DATABASE_MAX_CONNECTIONS;
	public static boolean						BACKUP_DATABASE;
	public static String						MYSQL_BIN_PATH;
	public static String						BACKUP_PATH;
	public static int							BACKUP_DAYS;
	public static int							MAXIMUM_ONLINE_USERS;
	public static int							MAXIMUM_ONLINE_USERS_QUEUE;
	public static boolean						HARDWARE_INFO_ENABLED;
	public static boolean						KICK_MISSING_HWID;
	public static int							MAX_PLAYERS_PER_HWID;
	public static boolean						ENABLE_PREMIUM_HWID_SLOTS;
	public static int							PREMIUM_HWID_SLOTS;
	public static Pattern						CHARNAME_TEMPLATE_PATTERN;
	public static String						PET_NAME_TEMPLATE;
	public static String						CLAN_NAME_TEMPLATE;
	public static int							MAX_CHARACTERS_NUMBER_PER_ACCOUNT;
	public static File							DATAPACK_ROOT;
	public static File							SCRIPT_ROOT;
	public static boolean						ACCEPT_ALTERNATE_ID;
	public static int							REQUEST_ID;
	public static boolean						RESERVE_HOST_ON_LOGIN							= false;
	public static List<Integer>					PROTOCOL_LIST;
	public static int							SERVER_LIST_TYPE;
	public static int							SERVER_LIST_AGE;
	public static boolean						SERVER_LIST_BRACKET;
	public static boolean						LOGIN_SERVER_SCHEDULE_RESTART;
	public static long							LOGIN_SERVER_SCHEDULE_RESTART_TIME;
	public static boolean						SERVER_RESTART_SCHEDULE_ENABLED;
	public static boolean						SERVER_RESTART_SCHEDULE_MESSAGE;
	public static int							SERVER_RESTART_SCHEDULE_COUNTDOWN;
	public static String[]						SERVER_RESTART_SCHEDULE;
	public static List<Integer>					SERVER_RESTART_DAYS;
	public static boolean						IS_WINDOWS;
	public static boolean						PRECAUTIONARY_RESTART_ENABLED;
	public static boolean						PRECAUTIONARY_RESTART_CPU;
	public static boolean						PRECAUTIONARY_RESTART_MEMORY;
	public static boolean						PRECAUTIONARY_RESTART_CHECKS;
	public static int							PRECAUTIONARY_RESTART_PERCENTAGE;
	public static int							PRECAUTIONARY_RESTART_DELAY;
	// --------------------------------------------------
	// Sayha's Grace Settings
	// --------------------------------------------------
	public static boolean						ENABLE_SAYHA_GRACE;
	public static int							STARTING_SAYHA_GRACE_POINTS;
	public static boolean						RAIDBOSS_USE_SAYHA_GRACE;
	public static float							RATE_SAYHA_GRACE_EXP_MULTIPLIER;
	public static float							RATE_LIMITED_SAYHA_GRACE_EXP_MULTIPLIER;
	public static int							SAYHA_GRACE_MAX_ITEMS_ALLOWED;
	public static float							RATE_SAYHA_GRACE_LOST;
	public static float							RATE_SAYHA_GRACE_LOST_KAMAEL_TRANSFORM;
	public static float							RATE_SAYHA_GRACE_GAIN;
	// --------------------------------------------------
	// Custom Interface
	// --------------------------------------------------
	public static boolean						CUSTOM_INTERFACE_ENABLED;
	public static long							AUTO_RANDOM_CRAFT_CHECK_DELAY;
	public static boolean						AUTO_RANDOM_CRAFT_ENABLED;
	// --------------------------------------------------
	// No classification assigned to the following yet
	// --------------------------------------------------
	public static int							MAX_ITEM_IN_PACKET;
	public static int							GAME_SERVER_LOGIN_PORT;
	public static String						GAME_SERVER_LOGIN_HOST;
	public static List<String>					GAME_SERVER_SUBNETS;
	public static List<String>					GAME_SERVER_HOSTS;
	public static int							PVP_NORMAL_TIME;
	public static int							PVP_PVP_TIME;
	public static int							MAX_REPUTATION;
	public static int							REPUTATION_INCREASE;
	public static int[]							ENCHANT_BLACKLIST;
	public static boolean						DISABLE_OVER_ENCHANTING;
	public static int							MIN_ARMOR_ENCHANT_ANNOUNCE_DCB;
	public static int							MIN_WEAPON_ENCHANT_ANNOUNCE_DCB;
	public static int							MIN_ARMOR_ENCHANT_ANNOUNCE_A;
	public static int							MIN_WEAPON_ENCHANT_ANNOUNCE_A;
	public static int[]							AUGMENTATION_BLACKLIST;
	public static double						HP_REGEN_MULTIPLIER;
	public static double						MP_REGEN_MULTIPLIER;
	public static double						CP_REGEN_MULTIPLIER;
	public static boolean						TRAINING_CAMP_ENABLE;
	public static boolean						TRAINING_CAMP_PREMIUM_ONLY;
	public static int							TRAINING_CAMP_MAX_DURATION;
	public static int							TRAINING_CAMP_MIN_LEVEL;
	public static int							TRAINING_CAMP_MAX_LEVEL;
	public static double						TRAINING_CAMP_EXP_MULTIPLIER;
	public static double						TRAINING_CAMP_SP_MULTIPLIER;
	public static boolean						SHOW_LICENCE;
	public static boolean						SHOW_PI_AGREEMENT;
	public static boolean						ACCEPT_NEW_GAMESERVER;
	public static int							SERVER_ID;
	public static byte[]						HEX_ID;
	public static boolean						AUTO_CREATE_ACCOUNTS;
	public static boolean						FLOOD_PROTECTION;
	public static int							FAST_CONNECTION_LIMIT;
	public static int							NORMAL_CONNECTION_TIME;
	public static int							FAST_CONNECTION_TIME;
	public static int							MAX_CONNECTION_PER_IP;
	public static boolean						ENABLE_CMD_LINE_LOGIN;
	public static boolean						ONLY_CMD_LINE_LOGIN;
	// chatfilter
	public static List<String>					FILTER_LIST;
	// --------------------------------------------------
	// GeoEngine
	// --------------------------------------------------
	public static String						GEODATA_PATH;
	public static int							COORD_SYNCHRONIZE;
	public static int							PART_OF_CHARACTER_HEIGHT;
	public static int							MAX_OBSTACLE_HEIGHT;
	public static boolean						PATHFINDING;
	public static String						PATHFIND_BUFFERS;
	public static int							BASE_WEIGHT;
	public static int							DIAGONAL_WEIGHT;
	public static int							HEURISTIC_WEIGHT;
	public static int							OBSTACLE_MULTIPLIER;
	public static int							MAX_ITERATIONS;
	public static boolean						CORRECT_PLAYER_Z;
	/**
	 * Attribute System
	 */
	public static int							S_WEAPON_STONE;
	public static int							S80_WEAPON_STONE;
	public static int							S84_WEAPON_STONE;
	public static int							R_WEAPON_STONE;
	public static int							R95_WEAPON_STONE;
	public static int							R99_WEAPON_STONE;
	public static int							S_ARMOR_STONE;
	public static int							S80_ARMOR_STONE;
	public static int							S84_ARMOR_STONE;
	public static int							R_ARMOR_STONE;
	public static int							R95_ARMOR_STONE;
	public static int							R99_ARMOR_STONE;
	public static int							S_WEAPON_CRYSTAL;
	public static int							S80_WEAPON_CRYSTAL;
	public static int							S84_WEAPON_CRYSTAL;
	public static int							R_WEAPON_CRYSTAL;
	public static int							R95_WEAPON_CRYSTAL;
	public static int							R99_WEAPON_CRYSTAL;
	public static int							S_ARMOR_CRYSTAL;
	public static int							S80_ARMOR_CRYSTAL;
	public static int							S84_ARMOR_CRYSTAL;
	public static int							R_ARMOR_CRYSTAL;
	public static int							R95_ARMOR_CRYSTAL;
	public static int							R99_ARMOR_CRYSTAL;
	public static int							S_WEAPON_STONE_SUPER;
	public static int							S80_WEAPON_STONE_SUPER;
	public static int							S84_WEAPON_STONE_SUPER;
	public static int							R_WEAPON_STONE_SUPER;
	public static int							R95_WEAPON_STONE_SUPER;
	public static int							R99_WEAPON_STONE_SUPER;
	public static int							S_ARMOR_STONE_SUPER;
	public static int							S80_ARMOR_STONE_SUPER;
	public static int							S84_ARMOR_STONE_SUPER;
	public static int							R_ARMOR_STONE_SUPER;
	public static int							R95_ARMOR_STONE_SUPER;
	public static int							R99_ARMOR_STONE_SUPER;
	public static int							S_WEAPON_CRYSTAL_SUPER;
	public static int							S80_WEAPON_CRYSTAL_SUPER;
	public static int							S84_WEAPON_CRYSTAL_SUPER;
	public static int							R_WEAPON_CRYSTAL_SUPER;
	public static int							R95_WEAPON_CRYSTAL_SUPER;
	public static int							R99_WEAPON_CRYSTAL_SUPER;
	public static int							S_ARMOR_CRYSTAL_SUPER;
	public static int							S80_ARMOR_CRYSTAL_SUPER;
	public static int							S84_ARMOR_CRYSTAL_SUPER;
	public static int							R_ARMOR_CRYSTAL_SUPER;
	public static int							R95_ARMOR_CRYSTAL_SUPER;
	public static int							R99_ARMOR_CRYSTAL_SUPER;
	public static int							S_WEAPON_JEWEL;
	public static int							S80_WEAPON_JEWEL;
	public static int							S84_WEAPON_JEWEL;
	public static int							R_WEAPON_JEWEL;
	public static int							R95_WEAPON_JEWEL;
	public static int							R99_WEAPON_JEWEL;
	public static int							S_ARMOR_JEWEL;
	public static int							S80_ARMOR_JEWEL;
	public static int							S84_ARMOR_JEWEL;
	public static int							R_ARMOR_JEWEL;
	public static int							R95_ARMOR_JEWEL;
	public static int							R99_ARMOR_JEWEL;
	// --------------------------------------------------
	// Custom Settings
	// --------------------------------------------------
	public static boolean						CHAMPION_ENABLE;
	public static boolean						CHAMPION_PASSIVE;
	public static int							CHAMPION_FREQUENCY;
	public static String						CHAMP_TITLE;
	public static boolean						SHOW_CHAMPION_AURA;
	public static int							CHAMP_MIN_LEVEL;
	public static int							CHAMP_MAX_LEVEL;
	public static int							CHAMPION_HP;
	public static float							CHAMPION_REWARDS_EXP_SP;
	public static float							CHAMPION_REWARDS_CHANCE;
	public static float							CHAMPION_REWARDS_AMOUNT;
	public static float							CHAMPION_ADENAS_REWARDS_CHANCE;
	public static float							CHAMPION_ADENAS_REWARDS_AMOUNT;
	public static float							CHAMPION_HP_REGEN;
	public static float							CHAMPION_ATK;
	public static float							CHAMPION_SPD_ATK;
	public static int							CHAMPION_REWARD_LOWER_LEVEL_ITEM_CHANCE;
	public static int							CHAMPION_REWARD_HIGHER_LEVEL_ITEM_CHANCE;
	public static int							CHAMPION_REWARD_ID;
	public static int							CHAMPION_REWARD_QTY;
	public static boolean						CHAMPION_ENABLE_SAYHA_GRACE;
	public static boolean						CHAMPION_ENABLE_IN_INSTANCES;
	public static boolean						ENABLE_NPC_STAT_MULTIPLIERS;
	public static double						MONSTER_HP_MULTIPLIER;
	public static double						MONSTER_MP_MULTIPLIER;
	public static double						MONSTER_PATK_MULTIPLIER;
	public static double						MONSTER_MATK_MULTIPLIER;
	public static double						MONSTER_PDEF_MULTIPLIER;
	public static double						MONSTER_MDEF_MULTIPLIER;
	public static double						MONSTER_AGRRO_RANGE_MULTIPLIER;
	public static double						MONSTER_CLAN_HELP_RANGE_MULTIPLIER;
	public static double						RAIDBOSS_HP_MULTIPLIER;
	public static double						RAIDBOSS_MP_MULTIPLIER;
	public static double						RAIDBOSS_PATK_MULTIPLIER;
	public static double						RAIDBOSS_MATK_MULTIPLIER;
	public static double						RAIDBOSS_PDEF_MULTIPLIER;
	public static double						RAIDBOSS_MDEF_MULTIPLIER;
	public static double						RAIDBOSS_AGRRO_RANGE_MULTIPLIER;
	public static double						RAIDBOSS_CLAN_HELP_RANGE_MULTIPLIER;
	public static double						GUARD_HP_MULTIPLIER;
	public static double						GUARD_MP_MULTIPLIER;
	public static double						GUARD_PATK_MULTIPLIER;
	public static double						GUARD_MATK_MULTIPLIER;
	public static double						GUARD_PDEF_MULTIPLIER;
	public static double						GUARD_MDEF_MULTIPLIER;
	public static double						GUARD_AGRRO_RANGE_MULTIPLIER;
	public static double						GUARD_CLAN_HELP_RANGE_MULTIPLIER;
	public static double						DEFENDER_HP_MULTIPLIER;
	public static double						DEFENDER_MP_MULTIPLIER;
	public static double						DEFENDER_PATK_MULTIPLIER;
	public static double						DEFENDER_MATK_MULTIPLIER;
	public static double						DEFENDER_PDEF_MULTIPLIER;
	public static double						DEFENDER_MDEF_MULTIPLIER;
	public static double						DEFENDER_AGRRO_RANGE_MULTIPLIER;
	public static double						DEFENDER_CLAN_HELP_RANGE_MULTIPLIER;
	public static boolean						ENABLE_OFFLINE_PLAY_COMMAND;
	public static boolean						OFFLINE_PLAY_PREMIUM;
	public static boolean						OFFLINE_PLAY_LOGOUT_ON_DEATH;
	public static String						OFFLINE_PLAY_LOGIN_MESSAGE;
	public static boolean						OFFLINE_PLAY_SET_NAME_COLOR;
	public static int							OFFLINE_PLAY_NAME_COLOR;
	public static List<AbnormalVisualEffect>	OFFLINE_PLAY_ABNORMAL_EFFECTS					= new ArrayList<>();
	public static boolean						OFFLINE_PLAY_AUTO_RESURRECTION_ENABLED;
	public static boolean						OFFLINE_PLAY_LOGOUT_ON_INSUFFICIENT_LCOIN;
	public static boolean						OFFLINE_PLAY_STORE_HUNTING_LOCATION;
	public static boolean						OFFLINE_TRADE_ENABLE;
	public static boolean						OFFLINE_CRAFT_ENABLE;
	public static boolean						OFFLINE_MODE_IN_PEACE_ZONE;
	public static boolean						OFFLINE_MODE_NO_DAMAGE;
	public static boolean						RESTORE_OFFLINERS;
	public static int							OFFLINE_MAX_DAYS;
	public static boolean						OFFLINE_DISCONNECT_FINISHED;
	public static boolean						OFFLINE_SET_NAME_COLOR;
	public static int							OFFLINE_NAME_COLOR;
	public static boolean						OFFLINE_FAME;
	public static boolean						STORE_OFFLINE_TRADE_IN_REALTIME;
	public static boolean						DISPLAY_SERVER_TIME;
	public static int							BUFFER_MAX_SCHEMES;
	public static int							BUFFER_STATIC_BUFF_COST;
	public static boolean						WELCOME_MESSAGE_ENABLED;
	public static String						WELCOME_MESSAGE_TEXT;
	public static int							WELCOME_MESSAGE_TIME;
	public static boolean						ANNOUNCE_PK_PVP;
	public static boolean						ANNOUNCE_PK_PVP_NORMAL_MESSAGE;
	public static String						ANNOUNCE_PK_MSG;
	public static String						ANNOUNCE_PVP_MSG;
	public static boolean						REWARD_PVP_ITEM;
	public static int							REWARD_PVP_ITEM_ID;
	public static int							REWARD_PVP_ITEM_AMOUNT;
	public static boolean						REWARD_PVP_ITEM_MESSAGE;
	public static boolean						REWARD_PK_ITEM;
	public static int							REWARD_PK_ITEM_ID;
	public static int							REWARD_PK_ITEM_AMOUNT;
	public static boolean						REWARD_PK_ITEM_MESSAGE;
	public static boolean						DISABLE_REWARDS_IN_INSTANCES;
	public static boolean						DISABLE_REWARDS_IN_PVP_ZONES;
	public static boolean						PVP_COLOR_SYSTEM_ENABLED;
	public static int							PVP_AMOUNT1;
	public static int							PVP_AMOUNT2;
	public static int							PVP_AMOUNT3;
	public static int							PVP_AMOUNT4;
	public static int							PVP_AMOUNT5;
	public static int							NAME_COLOR_FOR_PVP_AMOUNT1;
	public static int							NAME_COLOR_FOR_PVP_AMOUNT2;
	public static int							NAME_COLOR_FOR_PVP_AMOUNT3;
	public static int							NAME_COLOR_FOR_PVP_AMOUNT4;
	public static int							NAME_COLOR_FOR_PVP_AMOUNT5;
	public static String						TITLE_FOR_PVP_AMOUNT1;
	public static String						TITLE_FOR_PVP_AMOUNT2;
	public static String						TITLE_FOR_PVP_AMOUNT3;
	public static String						TITLE_FOR_PVP_AMOUNT4;
	public static String						TITLE_FOR_PVP_AMOUNT5;
	public static float[]						PVE_MAGICAL_SKILL_DAMAGE_MULTIPLIERS			= new float[221];
	public static float[]						PVP_MAGICAL_SKILL_DAMAGE_MULTIPLIERS			= new float[221];
	public static float[]						PVE_MAGICAL_SKILL_DEFENCE_MULTIPLIERS			= new float[221];
	public static float[]						PVP_MAGICAL_SKILL_DEFENCE_MULTIPLIERS			= new float[221];
	public static float[]						PVE_MAGICAL_SKILL_CRITICAL_CHANCE_MULTIPLIERS	= new float[221];
	public static float[]						PVP_MAGICAL_SKILL_CRITICAL_CHANCE_MULTIPLIERS	= new float[221];
	public static float[]						PVE_MAGICAL_SKILL_CRITICAL_DAMAGE_MULTIPLIERS	= new float[221];
	public static float[]						PVP_MAGICAL_SKILL_CRITICAL_DAMAGE_MULTIPLIERS	= new float[221];
	public static float[]						PVE_PHYSICAL_SKILL_DAMAGE_MULTIPLIERS			= new float[221];
	public static float[]						PVP_PHYSICAL_SKILL_DAMAGE_MULTIPLIERS			= new float[221];
	public static float[]						PVE_PHYSICAL_SKILL_DEFENCE_MULTIPLIERS			= new float[221];
	public static float[]						PVP_PHYSICAL_SKILL_DEFENCE_MULTIPLIERS			= new float[221];
	public static float[]						PVE_PHYSICAL_SKILL_CRITICAL_CHANCE_MULTIPLIERS	= new float[221];
	public static float[]						PVP_PHYSICAL_SKILL_CRITICAL_CHANCE_MULTIPLIERS	= new float[221];
	public static float[]						PVE_PHYSICAL_SKILL_CRITICAL_DAMAGE_MULTIPLIERS	= new float[221];
	public static float[]						PVP_PHYSICAL_SKILL_CRITICAL_DAMAGE_MULTIPLIERS	= new float[221];
	public static float[]						PVE_PHYSICAL_ATTACK_DAMAGE_MULTIPLIERS			= new float[221];
	public static float[]						PVP_PHYSICAL_ATTACK_DAMAGE_MULTIPLIERS			= new float[221];
	public static float[]						PVE_PHYSICAL_ATTACK_DEFENCE_MULTIPLIERS			= new float[221];
	public static float[]						PVP_PHYSICAL_ATTACK_DEFENCE_MULTIPLIERS			= new float[221];
	public static float[]						PVE_PHYSICAL_ATTACK_CRITICAL_CHANCE_MULTIPLIERS	= new float[221];
	public static float[]						PVP_PHYSICAL_ATTACK_CRITICAL_CHANCE_MULTIPLIERS	= new float[221];
	public static float[]						PVE_PHYSICAL_ATTACK_CRITICAL_DAMAGE_MULTIPLIERS	= new float[221];
	public static float[]						PVP_PHYSICAL_ATTACK_CRITICAL_DAMAGE_MULTIPLIERS	= new float[221];
	public static float[]						PVE_BLOW_SKILL_DAMAGE_MULTIPLIERS				= new float[221];
	public static float[]						PVP_BLOW_SKILL_DAMAGE_MULTIPLIERS				= new float[221];
	public static float[]						PVE_BLOW_SKILL_DEFENCE_MULTIPLIERS				= new float[221];
	public static float[]						PVP_BLOW_SKILL_DEFENCE_MULTIPLIERS				= new float[221];
	public static float[]						PVE_ENERGY_SKILL_DAMAGE_MULTIPLIERS				= new float[221];
	public static float[]						PVP_ENERGY_SKILL_DAMAGE_MULTIPLIERS				= new float[221];
	public static float[]						PVE_ENERGY_SKILL_DEFENCE_MULTIPLIERS			= new float[221];
	public static float[]						PVP_ENERGY_SKILL_DEFENCE_MULTIPLIERS			= new float[221];
	public static float[]						PLAYER_HEALING_SKILL_MULTIPLIERS				= new float[221];
	public static float[]						SKILL_MASTERY_CHANCE_MULTIPLIERS				= new float[221];
	public static float[]						EXP_AMOUNT_MULTIPLIERS							= new float[221];
	public static float[]						SP_AMOUNT_MULTIPLIERS							= new float[221];
	public static boolean						NOBLESS_MASTER_ENABLED;
	public static int							NOBLESS_MASTER_NPCID;
	public static int							NOBLESS_MASTER_LEVEL_REQUIREMENT;
	public static boolean						NOBLESS_MASTER_REWARD_TIARA;
	public static int							DUALBOX_CHECK_MAX_PLAYERS_PER_IP;
	public static int							DUALBOX_CHECK_MAX_OLYMPIAD_PARTICIPANTS_PER_IP;
	public static int							DUALBOX_CHECK_MAX_L2EVENT_PARTICIPANTS_PER_IP;
	public static boolean						DUALBOX_COUNT_OFFLINE_TRADERS;
	public static Map<Integer, Integer>			DUALBOX_CHECK_WHITELIST;
	public static Map<Integer, Integer>			DUALBOX_CHECK_WHITELIST_AAC;
	public static List<String>					IGNORED_HWIDS;
	// Custom Drop All Bosses
	public static boolean						ENABLE_CUSTOM_DROP_ALL_BOSSES;
	// Custom Drop GrandBosses
	public static boolean						ENABLE_CUSTOM_DROP_GRANDBOSSES;
	public static List<String>					LIST_CHARNAME_DROP_BONUS_GRANDBOSSES;
	public static Map<Integer, Float>			RATE_BONUS_DROP_CHANCE_BY_ID_GRANDBOSSES;
	public static boolean						ENABLE_CUSTOM_BAIUM_NO_DROP_ITEMS;
	// Custom Drop Chaotic Boss
	public static boolean						ENABLE_CUSTOM_CHAOTIC_NO_DROP_ITEMS;
	public static boolean						ENABLE_CUSTOM_DROP_CHAOTIC_BOSSES;
	public static List<String>					LIST_CHARNAME_DROP_BONUS_CHAOTIC;
	public static Map<Integer, Float>			RATE_BONUS_DROP_CHANCE_BY_ID_CHAOTIC;
	// Custom Enchant Rate CTV
	public static boolean						ENABLE_CUSTOM_ENCHANT_RATE_CTV;
	public static Map<String, Integer>			RATE_ENCHANT_LIST_CTV;
	// Custom Combine Rate CTV
	public static boolean						ENABLE_CUSTOM_COMBINE_RATE_CTV;
	public static Map<String, Integer>			RATE_COMBINE_LIST_CTV;
	// Custom Char Name Bonus Drop
	public static boolean						ENABLE_CUSTOM_DROP_BONUS;
	public static List<String>					LIST_CHARNAME_DROP_BONUS;
	public static Map<Integer, Float>			RATE_BONUS_DROP_CHANCE_BY_ID;
	// Custom Reward CTV
	public static boolean						ENABLE_CUSTOM_REWARD_CTV;
	public static List<String>					LIST_CHARNAME_REWARD_CTV;
	public static int							REWARD_ITEM_BY_ID;
	public static int							REWARD_AMOUNT;
	public static List<String>					LIST_CHARNAME_LEADER_REWARD_CTV;
	public static int							REWARD_LEADER_ITEM_BY_ID;
	public static int							REWARD_LEADER_AMOUNT;
	// Custom Reward CTV Clan WM
	public static List<String>					LIST_CHARNAME_REWARD_CTV_WM;
	public static int							REWARD_ITEM_BY_ID_WM;
	public static int							REWARD_AMOUNT_WM;
	// Custom Reward CTV VIP
	public static boolean						ENABLE_CUSTOM_REWARD_CTV_VIP;
	public static List<String>					LIST_CHARNAME_REWARD_CTV_VIP;
	public static int							REWARD_LCOIN_AMOUNT_CTV_VIP;
	public static int							REWARD_ADENA_AMOUNT_CTV_VIP;
	// Custom Special Craft Pet Training Guide
	public static int							SPECIAL_CRAFT_PET_TRAINING_GUIDE_TAKE_GIRAN_SEALED;
	public static int							SPECIAL_CRAFT_ADVANCED_PET_TRAINING_GUIDE_TAKE_GIRAN_SEALED;
	// Custom Char Name Bonus Enchant
	public static boolean						ENABLE_CUSTOM_ENCHANT_BONUS;
	public static List<String>					LIST_CHARNAME_ENCHANT_BONUS;
	// Custom Char Name Bonus Auto PvP
	public static boolean						ENABLE_CUSTOM_AUTO_PVP_BONUS;
	public static List<String>					LIST_CHARNAME_BONUS_AUTO_PVP;
	// Custom Amount PvP Enemies
	public static int							MAX_PVP_ENEMIES_FOR_BONUS_CHAR;
	public static int							MAX_PVP_ENEMIES_FOR_NORMAL_CHAR;
	// Custom Rate Bonus Effect Success
	public static boolean						ENABLE_CUSTOM_EFFECT_SUCCESS_BONUS;
	public static List<String>					LIST_CHARNAME_BONUS_EFFECT_SUCCESS;
	public static double						RATE_BONUS_EFFECT_SUCCESS;
	// Custom Rate Bonus Skill Reuse Delay
	public static boolean						ENABLE_CUSTOM_DELAY_SKILL_BONUS;
	public static List<String>					LIST_CHARNAME_BONUS_DELAY_SKILLS;
	public static Map<Integer, Integer>			SKILL_REUSE_BONUS_LIST;
	public static List<String>					LIST_CHARNAME_IGNORE;
	// Custom Event Enable/Disable
	public static boolean						ENABLE_EVENT_SYLPH_BLESSING;
	public static int							EVENT_START_HOUR;
	public static int							EVENT_END_HOUR;
	// Orcs Invasion Event
	public static boolean						ENABLE_ORC_INVASION_EVENT;
	// Custom Drop Watermelon Event Boss
	public static boolean						ENABLE_CUSTOM_DROP_WATERMELON_EVENT_BOSSES;
	public static int							WATERMELON_SEED_DROP_MAX_DAILY;
	public static int							WATERMELON_SEED_DROP_MIN;
	public static int							WATERMELON_SEED_DROP_MAX;
	public static double						WATERMELON_SEED_DROP_RATE;
	public static int							WATERMELON_LARGE_PRIME_BUFF_CHANCE;
	public static int							WATERMELON_PRIME_BUFF_CHANCE;
	public static long							WATERMELON_LCOIN_FOR_GET_DOLL_PACK_LEVEL1_VIP_BELOW_11_FROM_RAID;
	public static long							WATERMELON_LCOIN_FOR_GET_DOLL_PACK_LEVEL1_VIP_OVER_11_FROM_RAID;
	// Custom Drop Pink Rabbit Chasing Event
	public static int							PINK_ORB_DROP_MAX_DAILY;
	public static int							PINK_ORB_DROP_MIN;
	public static int							PINK_ORB_DROP_MAX;
	public static double						PINK_ORB_DROP_RATE;
	// Custom Char Name Bonus Karma
	public static boolean						ENABLE_CUSTOM_CTV_KARMA_BONUS;
	public static List<String>					LIST_CHARNAME_CTV_KARMA_BONUS;
	// Custom Battle With Balok
	public static int							BALOK_HOUR;
	public static int							BALOK_MINUTE;
	public static int							BALOK_POINTS_PER_MONSTER;
	// Custom Drop LCoin Daily
	public static int							LCOIN_DROP_DAILY_MAX_PREMIUM_CHAR;
	public static int							LCOIN_DROP_DAILY_NORMAL_CHAR;
	public static int							LCOIN_DROP_AMOUNT_NORMAL_CHAR_LEVEL_75;
	public static int							LCOIN_DROP_AMOUNT_PREMIUM_CHAR_LEVEL_75;
	public static int							LCOIN_DROP_AMOUNT_NORMAL_CHAR_LEVEL_76;
	public static int							LCOIN_DROP_AMOUNT_PREMIUM_CHAR_LEVEL_76;
	public static int							LCOIN_DROP_AMOUNT_NORMAL_CHAR_LEVEL_77;
	public static int							LCOIN_DROP_AMOUNT_PREMIUM_CHAR_LEVEL_77;
	public static int							LCOIN_DROP_AMOUNT_NORMAL_CHAR_LEVEL_78;
	public static int							LCOIN_DROP_AMOUNT_PREMIUM_CHAR_LEVEL_78;
	public static int							LCOIN_DROP_AMOUNT_NORMAL_CHAR_LEVEL_79;
	public static int							LCOIN_DROP_AMOUNT_PREMIUM_CHAR_LEVEL_79;
	public static int							LCOIN_DROP_AMOUNT_NORMAL_CHAR_LEVEL_80;
	public static int							LCOIN_DROP_AMOUNT_PREMIUM_CHAR_LEVEL_80;
	public static int							LCOIN_DROP_AMOUNT_NORMAL_CHAR_LEVEL_81;
	public static int							LCOIN_DROP_AMOUNT_PREMIUM_CHAR_LEVEL_81;
	public static int							LCOIN_DROP_AMOUNT_NORMAL_CHAR_LEVEL_82;
	public static int							LCOIN_DROP_AMOUNT_PREMIUM_CHAR_LEVEL_82;
	public static int							LCOIN_DROP_AMOUNT_NORMAL_CHAR_LEVEL_83;
	public static int							LCOIN_DROP_AMOUNT_PREMIUM_CHAR_LEVEL_83;
	public static int							LCOIN_DROP_AMOUNT_NORMAL_CHAR_LEVEL_84;
	public static int							LCOIN_DROP_AMOUNT_PREMIUM_CHAR_LEVEL_84;
	public static int							LCOIN_DROP_AMOUNT_NORMAL_CHAR_LEVEL_85;
	public static int							LCOIN_DROP_AMOUNT_PREMIUM_CHAR_LEVEL_85;
	public static int							LCOIN_DROP_AMOUNT_NORMAL_CHAR_LEVEL_86;
	public static int							LCOIN_DROP_AMOUNT_PREMIUM_CHAR_LEVEL_86;
	public static int							LCOIN_DROP_AMOUNT_NORMAL_CHAR_LEVEL_87;
	public static int							LCOIN_DROP_AMOUNT_PREMIUM_CHAR_LEVEL_87;
	public static int							LCOIN_DROP_AMOUNT_NORMAL_CHAR_LEVEL_88;
	public static int							LCOIN_DROP_AMOUNT_PREMIUM_CHAR_LEVEL_88;
	public static int							LCOIN_DROP_AMOUNT_NORMAL_CHAR_LEVEL_89;
	public static int							LCOIN_DROP_AMOUNT_PREMIUM_CHAR_LEVEL_89;
	public static int							LCOIN_DROP_AMOUNT_NORMAL_CHAR_LEVEL_90_99;
	public static int							LCOIN_DROP_AMOUNT_PREMIUM_CHAR_LEVEL_90_99;
	// Custom Faction
	public static boolean						LOG_FACTION_DETAILS;
	public static boolean						FACTION_SYSTEM_ENABLED;
	public static boolean						AUTO_FACTION_SELECTION;
	public static boolean						MANUAL_FACTION_SELECTION;
	public static int							FACTION_BALANCE_MAX_PERCENT;
	public static int							ACTIVE_ZONE_COUNT;
	public static Location						FACTION_STARTING_LOCATION;
	public static Location						FACTION_MANAGER_LOCATION;
	public static Location						FACTION_FIRE_BASE_LOCATION;
	public static Location						FACTION_WATER_BASE_LOCATION;
	public static String						FACTION_FIRE_TEAM_NAME;
	public static String						FACTION_WATER_TEAM_NAME;
	public static int							FACTION_FIRE_NAME_COLOR;
	public static int							FACTION_WATER_NAME_COLOR;
	public static boolean						FACTION_GUARDS_ENABLED;
	public static boolean						FACTION_RESPAWN_AT_BASE;
	public static boolean						FACTION_AUTO_NOBLESS;
	public static boolean						FACTION_SPECIFIC_CHAT;
	public static boolean						FACTION_BALANCE_ONLINE_PLAYERS;
	public static int							FACTION_BALANCE_PLAYER_EXCEED_LIMIT;
	// Faction Leader Settings
	public static boolean						FACTION_LEADER_ENABLED;
	public static int							FACTION_LEADER_MIN_PERSONAL_EFFICIENCY;
	public static List<SkillHolder>				FACTION_LEADER_SKILL_SET;
	public static List<ItemHolder>				FACTION_LEADER_ITEM_SET;
	public static int							FACTION_LEADER_BROADCAST_RADIUS;
	public static boolean						FACTION_LEADER_SELF_VOTE_AVAILABLE;
	public static boolean						FACTION_LEADER_CHECK_HWID;
	public static int							FACTION_LEADER_MIN_LEVEL_FOR_VOTING;
	public static boolean						FACTION_LEADER_AVAILABLE_INTERVALS_ENABLED;
	public static List<int[]>					FACTION_LEADER_AVAILABLE_INTERVALS;
	public static int							FACTION_LEADER_START_VOTE_HOUR;
	public static int							FACTION_LEADER_START_VOTE_MINUTE;
	public static int							FACTION_LEADER_START_REQUEST_HOUR;
	public static int							FACTION_LEADER_START_REQUEST_MINUTE;
	public static int							FACTION_LEADER_END_CYCLE_HOUR;
	public static int							FACTION_LEADER_END_CYCLE_MINUTE;
	public static int							FACTION_LEADER_NEXT_CYCLE_HOUR;
	public static int							FACTION_LEADER_NEXT_CYCLE_MINUTE;
	public static String						FACTION_LEADER_ANNOUNCE_CYCLE_START;
	// Faction System - GvE Settings
	public static int							ZONE_CHANGE_INTERVAL;																			// in minutes
	public static int							PROTECTION_DURATION;																			// in minutes
	public static int[]							ZONE_CHANGE_ANNOUNCE_TIMES;																		// in minutes
	public static int							INITIAL_ACTIVATION_DELAY_MINUTES;																// in minutes
	public static boolean						ENABLE_ACTIVATION_COUNTDOWN;
	public static int							ACTIVATION_COUNTDOWN_SECONDS;
	public static int							MAX_PLAYERS_PER_ZONE;
	public static int							DEFEND_REWARD_INTERVAL;																			// in seconds
	public static int							DEFEND_REWARD_LCOIN_AMOUNT;
	public static int							DEFEND_REWARD_MIN_TIME;																			// in seconds
	public static int							DEFEND_ZONE_RADIUS;
	public static int							OUTPOST_DESTROYER_REWARD_ADENA_AMOUNT;
	public static int							OUTPOST_DESTROYER_REWARD_LCOIN_AMOUNT;
	public static int							OUTPOST_DESTROYER_REWARD_LCOIN_CHANCE;															// in percentage
	public static int							OUTPOST_CAPTURE_REWARD_LCOIN_AMOUNT;
	public static int							OUTPOST_DEFEND_SUCCESS_REWARD_LCOIN_AMOUNT;
	public static int							TELEPORT_CONFIRM_TIME;																			// in seconds
	public static int							MAX_PLAYERS_FOR_NOTIFICATIONS;
	public static int							OUTPOST_LOW_HP_ANNOUNCE_THRESHOLD;																// in percentage
	public static int							TELEPORT_RANDOM_RADIUS;
	// Guard settings for outposts
	public static int							OUTPOST_GUARD_ARCHER_COUNT;
	public static int							OUTPOST_GUARD_MAGE_COUNT;
	public static int							OUTPOST_GUARD_WARRIOR_COUNT;
	// Guard NPC IDs for FIRE faction
	public static int							NEUTRAL_GUARD_ARCHER_NPC_ID;
	public static int							NEUTRAL_GUARD_MAGE_NPC_ID;
	public static int							NEUTRAL_GUARD_WARRIOR_NPC_ID;
	// Guard NPC IDs for FIRE faction
	public static int							FIRE_GUARD_ARCHER_NPC_ID;
	public static int							FIRE_GUARD_MAGE_NPC_ID;
	public static int							FIRE_GUARD_WARRIOR_NPC_ID;
	// Guard NPC IDs for WATER faction
	public static int							WATER_GUARD_ARCHER_NPC_ID;
	public static int							WATER_GUARD_MAGE_NPC_ID;
	public static int							WATER_GUARD_WARRIOR_NPC_ID;
	public static int							OUTPOST_GUARD_SPAWN_RADIUS;
	public static int							OUTPOST_GUARD_RESPAWN_TIME;																		// in seconds
	// Artifact settings
	public static int							ARTIFACT_DEFEND_DURATION;
	public static int							ARTIFACT_DEFEND_SUCCESS_REWARD_LCOIN_AMOUNT;
	public static int							ARTIFACT_DEFEND_POINTS;
	public static int							ARTIFACT_DEFEND_FACTION_POINTS;
	// Faction War
	public static boolean						FACTION_WAR_ENABLED;
	// public static boolean FACTION_WAR_PVP_HWID_ENABLE;
	public static int							FACTION_WAR_START_POINTS;
	// public static int FACTION_WAR_START_HOUR;
	// public static int FACTION_WAR_START_MINUTE;
	// public static int FACTION_WAR_END_HOUR;
	// public static int FACTION_WAR_END_MINUTE;
	public static String						FACTION_WAR_SCHEDULE;
	public static List<int[]>					FACTION_WAR_TIME_SLOTS;
	public static int							FACTION_WAR_MIN_LEVEL;
	public static int							FACTION_WAR_PVP_KILL_POINTS;
	public static int							FACTION_WAR_PVP_DEATH_POINTS;
	public static int							FACTION_WAR_OUTPOST_KILL_POINTS;
	public static int							FACTION_WAR_FORTRESS_SIEGE_ZONE_POINTS;
	public static int							FACTION_WAR_CASTLE_SIEGE_ZONE_POINTS;
	public static int							FACTION_WAR_ARTIFACT_CAPTURE_POINTS;
	public static int							FACTION_WAR_ELITE_BOSS_KILL_POINTS;
	public static int[]							FACTION_WAR_TRUST_LEVEL_POINTS;
	public static List<ItemHolder>				FACTION_WAR_TOP_1_REWARD;
	public static List<ItemHolder>				FACTION_WAR_TOP_10_REWARD;
	public static List<ItemHolder>				FACTION_WAR_TOP_50_REWARD;
	public static List<ItemHolder>				FACTION_WAR_TOP_100_REWARD;
	public static List<ItemHolder>				FACTION_WAR_TOP_300_REWARD;
	public static List<ItemHolder>				FACTION_WAR_NOT_REWARD;
	public static int							GVE_FRACTION_CHANGE_PERSONAL;
	public static int							GVE_FRACTION_CHANGE_CLAN;
	public static boolean						MAX_LOAD_LOCATIONS_SYSTEM;
	public static boolean						FACTION_BALANCE_IN_LOCATIONS;
	public static int							FACTION_BALANCE_IN_LOCATION_MIN_COUNT;
	public static int							FACTION_BALANCE_IN_LOCATION_MAX_PERCENT;
	public static int							FACTION_BALANCE_MIN_PERCENT_TO_CHANGE_PLAYER;
	public static int							FACTION_BALANCE_MIN_PERCENT_TO_CHANGE_CLAN;
	public static Map<String, Integer>			BALANCE_LOCATIONS								= new HashMap<>();
	public static Map<Integer, String>			GVE_COMBO_KILL_MESSAGES							= new HashMap<>();
	public static double						DROP_RATE_PENALTY_OPPOSING_FACTION;

	// Faction Zone Performance Settings
	public static int							CIRCUIT_BREAKER_MAX_FAILURES;
	public static int							CIRCUIT_BREAKER_TIMEOUT_MINUTES;
	public static int							PERIODIC_CLEANUP_INTERVAL_MINUTES;
	public static int							HEALTH_CHECK_INTERVAL_MINUTES;
	public static int							DATABASE_RETRY_ATTEMPTS;
	public static int							DATABASE_RETRY_DELAY_MS;
	// Morale Boost Configurations
	public static boolean						BOOST_MORALE_ENABLED;
	public static int[][]						MORALE_EFFECT;
	public static boolean						CASTLE_SUCCESS_ATTACK_BOOST;
	public static boolean						CASTLE_SUCCESS_DEFENSE_BOOST;
	public static boolean						FORTRESS_SUCCESS_ATTACK_BOOST;
	public static boolean						OUTPOST_DESTROY_BOOST;
	public static boolean						ARTIFACT_CAPTURE_BOOST;
	public static boolean						BOSS_KILL_BOOST;
	// GvE Reward Settings
	public static long							GIVE_ASSIST_ATTACK_DELAY;
	public static double						FACTION_REWARD_LCOIN_PENALTY;
	public static long							GVE_KILL_PENALTY_REMOVE_TIME;
	public static int							GVE_KILL_PENALTY_COUNT;
	public static long							GVE_KILL_PENALTY_TIME;
	public static long							GVE_DEATH_PENALTY_REMOVE_TIME;
	public static int							GVE_DEATH_PENALTY_COUNT;
	public static long							GVE_DEATH_PENALTY_TIME;
	public static long							GVE_SERIES_KILL_PENALTY_REMOVE_TIME;
	public static int							GVE_SERIES_KILL_COUNT;
	public static long							GVE_SERIES_KILL_PENALTY_TIME;
	public static double						GVE_RATE_DROP_LCOIN;

	// GvE Skill Points Daily Limits Configuration
	public static int							GVE_SKILL_DAILY_LIMIT_BASE;
	public static int							GVE_SKILL_DAILY_LIMIT_1_STAR;
	public static int							GVE_SKILL_DAILY_LIMIT_2_STAR;
	public static int							GVE_SKILL_DAILY_LIMIT_3_STAR;
	public static int							GVE_SKILL_DAILY_LIMIT_4_STAR;
	public static int							GVE_SKILL_RESET_HOUR;
	public static int							GVE_SKILL_RESET_MINUTE;

	// Zone Dominance System Configuration
	public static boolean						ENABLE_ZONE_DOMINANCE_SYSTEM;
	public static int							ZONE_DOMINANCE_MINOR_GVE_BONUS;
	public static int							ZONE_DOMINANCE_MINOR_ADENA_BONUS;
	public static int							ZONE_DOMINANCE_MINOR_LCOIN_BONUS;
	public static int							ZONE_DOMINANCE_MINOR_EXP_SP_BONUS;
	public static int							ZONE_DOMINANCE_MAJOR_GVE_BONUS;
	public static int							ZONE_DOMINANCE_MAJOR_ADENA_BONUS;
	public static int							ZONE_DOMINANCE_MAJOR_LCOIN_BONUS;
	public static int							ZONE_DOMINANCE_MAJOR_EXP_SP_BONUS;
	public static int							ZONE_DOMINANCE_TOTAL_GVE_BONUS;
	public static int							ZONE_DOMINANCE_TOTAL_ADENA_BONUS;
	public static int							ZONE_DOMINANCE_TOTAL_LCOIN_BONUS;
	public static int							ZONE_DOMINANCE_TOTAL_EXP_SP_BONUS;
	public static boolean						ZONE_DOMINANCE_ANNOUNCEMENTS;
	// GvE Fortress Siege configurations
	public static boolean						GVE_FORTRESS_SIEGE_ENABLED;
	public static Duration						GVE_FORTRESS_SIEGE_DURATION;
	public static Duration						GVE_FORTRESS_SIEGE_INTERVAL;
	public static int							GVE_FORTRESS_ZONE_ENEMY_COUNT;
	public static long							GVE_FORTRESS_ZONE_ENEMY_INTERVAL;
	public static int							GVE_FORTRESS_REWARD_MAX;
	public static long							GVE_FORTRESS_REWARD_INTERVAL;
	public static int							GVE_FORTRESS_REWARD_INCREASE;
	public static Duration						GVE_FORTRESS_REWARD_TIME_IN_ZONE;
	public static Map<Integer, Integer>			GVE_FORTRESS_REWARD_WINNER						= new HashMap<>();
	public static Map<Integer, Integer>			GVE_FORTRESS_REWARD_LOSER						= new HashMap<>();
	public static long							GVE_FORTRESS_GREG_SENTRY_SPAWN_DELAY;
	// End Custom Config
	public static boolean						ALLOW_HUMAN;
	public static boolean						ALLOW_ELF;
	public static boolean						ALLOW_DARKELF;
	public static boolean						ALLOW_ORC;
	public static boolean						ALLOW_DWARF;
	public static boolean						ALLOW_KAMAEL;
	public static boolean						ALLOW_ERTHEIA;
	public static boolean						CUSTOM_STARTING_LOC;
	public static int							CUSTOM_STARTING_LOC_X;
	public static int							CUSTOM_STARTING_LOC_Y;
	public static int							CUSTOM_STARTING_LOC_Z;
	public static int							SHOP_MIN_RANGE_FROM_NPC;
	public static int							SHOP_MIN_RANGE_FROM_PLAYER;
	public static boolean						ENABLE_RANDOM_MONSTER_SPAWNS;
	public static int							MOB_MIN_SPAWN_RANGE;
	public static int							MOB_MAX_SPAWN_RANGE;
	public static List<Integer>					MOBS_LIST_NOT_RANDOM;
	public static boolean						FREE_JUMPS_FOR_ALL;
	public static boolean						CUSTOM_CB_ENABLED;
	public static int							COMMUNITYBOARD_CURRENCY;
	public static boolean						COMMUNITYBOARD_ENABLE_MULTISELLS;
	public static boolean						COMMUNITYBOARD_ENABLE_TELEPORTS;
	public static boolean						COMMUNITYBOARD_ENABLE_BUFFS;
	public static boolean						COMMUNITYBOARD_ENABLE_HEAL;
	public static boolean						COMMUNITYBOARD_ENABLE_DELEVEL;
	public static int							COMMUNITYBOARD_TELEPORT_PRICE;
	public static int							COMMUNITYBOARD_BUFF_PRICE;
	public static int							COMMUNITYBOARD_HEAL_PRICE;
	public static int							COMMUNITYBOARD_DELEVEL_PRICE;
	public static boolean						COMMUNITYBOARD_COMBAT_DISABLED;
	public static boolean						COMMUNITYBOARD_KARMA_DISABLED;
	public static boolean						COMMUNITYBOARD_CAST_ANIMATIONS;
	public static boolean						COMMUNITY_PREMIUM_SYSTEM_ENABLED;
	public static int							COMMUNITY_PREMIUM_COIN_ID;
	public static int							COMMUNITY_PREMIUM_PRICE_PER_DAY;
	public static List<Integer>					COMMUNITY_AVAILABLE_BUFFS;
	public static Map<String, Location>			COMMUNITY_AVAILABLE_TELEPORTS;
	public static boolean						CUSTOM_DEPOSITABLE_ENABLED;
	public static boolean						CUSTOM_DEPOSITABLE_QUEST_ITEMS;
	public static boolean						CUSTOM_MAIL_MANAGER_ENABLED;
	public static int							CUSTOM_MAIL_MANAGER_DELAY;
	public static boolean						DELEVEL_MANAGER_ENABLED;
	public static int							DELEVEL_MANAGER_NPCID;
	public static int							DELEVEL_MANAGER_ITEMID;
	public static int							DELEVEL_MANAGER_ITEMCOUNT;
	public static int							DELEVEL_MANAGER_MINIMUM_DELEVEL;
	public static boolean						ENABLE_FIND_PVP;
	public static boolean						MERCHANT_ZERO_SELL_PRICE;
	public static boolean						PREMIUM_SYSTEM_ENABLED;
	public static boolean						ALL_PREMIUM_EVENT;
	public static boolean						ALL_RANDOM_CRAFT_PREMIUM_EVENT;
	public static float							PREMIUM_RATE_XP;
	public static float							PREMIUM_RATE_SP;
	public static float							PREMIUM_RATE_SAYHA_GRACE_XP;
	public static float							PREMIUM_RATE_LIMITED_SAYHA_GRACE_XP;
	public static Map<Integer, Float>			PREMIUM_RATE_DROP_ITEMS_ID;
	public static float							PREMIUM_RATE_DROP_CHANCE;
	public static float							PREMIUM_RATE_DROP_AMOUNT;
	public static int							PREMIUM_RATE_LCOIN_DROP_AMOUNT_ADD;
	public static float							PREMIUM_RATE_SPOIL_CHANCE;
	public static float							PREMIUM_RATE_SPOIL_AMOUNT;
	public static Map<Integer, Float>			PREMIUM_RATE_DROP_CHANCE_BY_ID;
	public static Map<Integer, Float>			PREMIUM_RATE_DROP_AMOUNT_BY_ID;
	public static float							PREMIUM_RATE_DEATH_LOST_XP;
	public static float							PREMIUM_RATE_NO_PENALTY_RESURRECTION_COST;
	public static float							PREMIUM_RATE_RANDOM_CRAFT_HERB_POINT_AMOUNT;
	public static boolean						PREMIUM_ONLY_FISHING;
	public static boolean						PC_CAFE_ENABLED;
	public static boolean						PC_CAFE_ONLY_PREMIUM;
	public static int							PC_CAFE_MAX_POINTS;
	public static boolean						PC_CAFE_ENABLE_DOUBLE_POINTS;
	public static int							PC_CAFE_DOUBLE_POINTS_CHANCE;
	public static double						PC_CAFE_POINT_RATE;
	public static boolean						PC_CAFE_RANDOM_POINT;
	public static boolean						PC_CAFE_REWARD_LOW_EXP_KILLS;
	public static int							PC_CAFE_LOW_EXP_KILLS_CHANCE;
	public static boolean						SELLBUFF_ENABLED;
	public static int							SELLBUFF_MP_MULTIPLER;
	public static int							SELLBUFF_PAYMENT_ID;
	public static long							SELLBUFF_MIN_PRICE;
	public static long							SELLBUFF_MAX_PRICE;
	public static int							SELLBUFF_MAX_BUFFS;
	public static boolean						ALLOW_NETWORK_VOTE_REWARD;
	public static String						NETWORK_SERVER_LINK;
	public static int							NETWORK_VOTES_DIFFERENCE;
	public static int							NETWORK_REWARD_CHECK_TIME;
	public static Map<Integer, Integer>			NETWORK_REWARD									= new HashMap<>();
	public static int							NETWORK_DUALBOXES_ALLOWED;
	public static boolean						ALLOW_NETWORK_GAME_SERVER_REPORT;
	public static boolean						ALLOW_TOPZONE_VOTE_REWARD;
	public static String						TOPZONE_SERVER_LINK;
	public static int							TOPZONE_VOTES_DIFFERENCE;
	public static int							TOPZONE_REWARD_CHECK_TIME;
	public static Map<Integer, Integer>			TOPZONE_REWARD									= new HashMap<>();
	public static int							TOPZONE_DUALBOXES_ALLOWED;
	public static boolean						ALLOW_TOPZONE_GAME_SERVER_REPORT;
	public static boolean						ALLOW_HOPZONE_VOTE_REWARD;
	public static String						HOPZONE_SERVER_LINK;
	public static int							HOPZONE_VOTES_DIFFERENCE;
	public static int							HOPZONE_REWARD_CHECK_TIME;
	public static Map<Integer, Integer>			HOPZONE_REWARD									= new HashMap<>();
	public static int							HOPZONE_DUALBOXES_ALLOWED;
	public static boolean						ALLOW_HOPZONE_GAME_SERVER_REPORT;
	public static boolean						ALLOW_L2TOP_VOTE_REWARD;
	public static String						L2TOP_SERVER_LINK;
	public static int							L2TOP_VOTES_DIFFERENCE;
	public static int							L2TOP_REWARD_CHECK_TIME;
	public static Map<Integer, Integer>			L2TOP_REWARD									= new HashMap<>();
	public static int							L2TOP_DUALBOXES_ALLOWED;
	public static boolean						ALLOW_L2TOP_GAME_SERVER_REPORT;
	public static boolean						ALLOW_L2JBRASIL_VOTE_REWARD;
	public static String						L2JBRASIL_SERVER_LINK;
	public static int							L2JBRASIL_VOTES_DIFFERENCE;
	public static int							L2JBRASIL_REWARD_CHECK_TIME;
	public static Map<Integer, Integer>			L2JBRASIL_REWARD								= new HashMap<>();
	public static int							L2JBRASIL_DUALBOXES_ALLOWED;
	public static boolean						ALLOW_L2JBRASIL_GAME_SERVER_REPORT;
	// Discord Settings
	public static boolean						DISCORD_ENABLED;
	public static boolean						DISCORD_WEBHOOK_ENABLED;
	public static String						DISCORD_BOT_TOKEN;
	public static String						DISCORD_GUILD_ID;
	public static String						DISCORD_EPIC_BOSS_CHANNEL;
	public static String						DISCORD_FACTION_WAR_WEBHOOK;
	public static boolean						DISCORD_FACTION_NOTIFICATIONS_ENABLED;
	public static String						DISCORD_FACTION_WAR_CHANNEL_ID;
	public static String						DISCORD_FACTION_STATS_CHANNEL_ID;
	// Telegram Settings
	public static boolean						TELEGRAM_ENABLED;
	public static String						TELEGRAM_BOT_USERNAME;
	public static String						TELEGRAM_BOT_TOKEN;
	public static String						TELEGRAM_ADMIN_CHAT_IDS;
	public static int							TELEGRAM_MAX_PRIME_POINTS_PER_COMMAND;
	public static long							TELEGRAM_COMMAND_COOLDOWN;
	// Drop Search Settings
	public static boolean						ENABLE_DROP_CALCULATOR;
	public static boolean						ENABLE_TELEPORT_FUNCTION;
	public static boolean						ALLOW_TELEPORT_FROM_PEACE_ZONE_ONLY;
	public static boolean						ALLOW_FREE_TELEPORT;
	public static int[]							TELEPORT_PRICE;
	public static List<Integer>					RESTRICTED_TELEPORT_IDS;
	public static List<Integer>					RESTRICTED_MOB_DROPLIST_IDS;
	// World Exchange
	public static boolean						ENABLE_WORLD_EXCHANGE;
	public static String						WORLD_EXCHANGE_DEFAULT_LANG;
	public static long							WORLD_EXCHANGE_SAVE_INTERVAL;
	public static double						WORLD_EXCHANGE_LCOIN_TAX;
	public static long							WORLD_EXCHANGE_MAX_LCOIN_TAX;
	public static double						WORLD_EXCHANGE_ADENA_FEE;
	public static long							WORLD_EXCHANGE_MAX_ADENA_FEE;
	public static boolean						WORLD_EXCHANGE_LAZY_UPDATE;
	public static int							WORLD_EXCHANGE_ITEM_SELL_PERIOD;
	public static int							WORLD_EXCHANGE_ITEM_BACK_PERIOD;
	public static int							WORLD_EXCHANGE_PAYMENT_TAKE_PERIOD;

	// Auto-resurrection configs
	public static boolean						AUTOPLAY_AUTO_RESURRECTION_ENABLED;
	public static int							AUTOPLAY_AUTO_RESURRECTION_COOLDOWN;
	public static int							AUTOPLAY_AUTO_RESURRECTION_MAX_USES_PER_DAY;
	public static int							AUTOPLAY_AUTO_RESURRECTION_MAX_USES_PER_DAY_PREMIUM;
	public static int							AUTOPLAY_AUTO_RESURRECTION_RETURN_DELAY;
	public static float							AUTOPLAY_AUTO_RESURRECTION_COST_MULTIPLIER_STEP;
	public static float							AUTOPLAY_AUTO_RESURRECTION_COST_MULTIPLIER_CAP;

	/**
	 * This class initializes all global variables for configuration.<br>
	 * If the key doesn't appear in properties file, a default value is set by this class. {@link #SERVER_CONFIG_FILE} (properties file) for configuring your server.
	 *
	 * @param serverMode
	 */
	public static void load(ServerMode serverMode)
	{
		SERVER_MODE = serverMode;
		if (SERVER_MODE == ServerMode.GAME)
		{
			FLOOD_PROTECTOR_USE_ITEM = new FloodProtectorConfig("UseItemFloodProtector");
			FLOOD_PROTECTOR_ROLL_DICE = new FloodProtectorConfig("RollDiceFloodProtector");
			FLOOD_PROTECTOR_FIREWORK = new FloodProtectorConfig("FireworkFloodProtector");
			FLOOD_PROTECTOR_ITEM_PET_SUMMON = new FloodProtectorConfig("ItemPetSummonFloodProtector");
			FLOOD_PROTECTOR_HERO_VOICE = new FloodProtectorConfig("HeroVoiceFloodProtector");
			FLOOD_PROTECTOR_GLOBAL_CHAT = new FloodProtectorConfig("GlobalChatFloodProtector");
			FLOOD_PROTECTOR_SUBCLASS = new FloodProtectorConfig("SubclassFloodProtector");
			FLOOD_PROTECTOR_DROP_ITEM = new FloodProtectorConfig("DropItemFloodProtector");
			FLOOD_PROTECTOR_SERVER_BYPASS = new FloodProtectorConfig("ServerBypassFloodProtector");
			FLOOD_PROTECTOR_MULTISELL = new FloodProtectorConfig("MultiSellFloodProtector");
			FLOOD_PROTECTOR_TRANSACTION = new FloodProtectorConfig("TransactionFloodProtector");
			FLOOD_PROTECTOR_MANUFACTURE = new FloodProtectorConfig("ManufactureFloodProtector");
			FLOOD_PROTECTOR_MANOR = new FloodProtectorConfig("ManorFloodProtector");
			FLOOD_PROTECTOR_SENDMAIL = new FloodProtectorConfig("SendMailFloodProtector");
			FLOOD_PROTECTOR_CHARACTER_SELECT = new FloodProtectorConfig("CharacterSelectFloodProtector");
			FLOOD_PROTECTOR_ITEM_AUCTION = new FloodProtectorConfig("ItemAuctionFloodProtector");
			final PropertiesParser serverSettings = new PropertiesParser(SERVER_CONFIG_FILE);
			NO_BOX_LIMIT_HWIDS.clear();
			String hwids = serverSettings.getString("NoBoxLimitHwids", "");
			for (String hwid : hwids.split(";"))
			{
				NO_BOX_LIMIT_HWIDS.add(hwid);
			}
			ONLY_CHARACTER_CREATE = serverSettings.getBoolean("OnlyCharacterCreate", false);
			GAMESERVER_HOSTNAME = serverSettings.getString("GameserverHostname", "0.0.0.0");
			PORT_GAME = serverSettings.getInt("GameserverPort", 7777);
			ASYNC_MMOCORE_AUTO_READ = serverSettings.getBoolean("AsyncMMOcoreAutoRead", false);
			ASYNC_MMOCORE_FAIRNESS_BUCKETS = serverSettings.getInt("AsyncMMOcoreFairnessBuckets", 1);
			GAME_SERVER_LOGIN_PORT = serverSettings.getInt("LoginPort", 9014);
			GAME_SERVER_LOGIN_HOST = serverSettings.getString("LoginHost", "127.0.0.1");
			REQUEST_ID = serverSettings.getInt("RequestServerID", 0);
			ACCEPT_ALTERNATE_ID = serverSettings.getBoolean("AcceptAlternateID", true);
			DATABASE_DRIVER = serverSettings.getString("Driver", "org.mariadb.jdbc.Driver");
			DATABASE_URL = serverSettings.getString("URL", "******************************");
			DATABASE_LOGIN = serverSettings.getString("Login", "root");
			DATABASE_PASSWORD = serverSettings.getString("Password", "");
			DATABASE_MAX_CONNECTIONS = serverSettings.getInt("MaximumDbConnections", 10);
			BACKUP_DATABASE = serverSettings.getBoolean("BackupDatabase", false);
			MYSQL_BIN_PATH = serverSettings.getString("MySqlBinLocation", "C:/xampp/mysql/bin/");
			BACKUP_PATH = serverSettings.getString("BackupPath", "../backup/");
			BACKUP_DAYS = serverSettings.getInt("BackupDays", 30);
			try
			{
				DATAPACK_ROOT = new File(serverSettings.getString("DatapackRoot", ".").replaceAll("\\\\", "/")).getCanonicalFile();
			}
			catch (IOException e)
			{
				LOGGER.log(Level.WARNING, "Error setting datapack root!", e);
				DATAPACK_ROOT = new File(".");
			}
			try
			{
				SCRIPT_ROOT = new File(serverSettings.getString("ScriptRoot", "./data/scripts").replaceAll("\\\\", "/")).getCanonicalFile();
			}
			catch (Exception e)
			{
				LOGGER.log(Level.WARNING, "Error setting script root!", e);
				SCRIPT_ROOT = new File(".");
			}
			Pattern charNamePattern;
			try
			{
				charNamePattern = Pattern.compile(serverSettings.getString("CnameTemplate", ".*"));
			}
			catch (PatternSyntaxException e)
			{
				LOGGER.log(Level.WARNING, "Character name pattern is invalid!", e);
				charNamePattern = Pattern.compile(".*");
			}
			CHARNAME_TEMPLATE_PATTERN = charNamePattern;
			PET_NAME_TEMPLATE = serverSettings.getString("PetNameTemplate", ".*");
			CLAN_NAME_TEMPLATE = serverSettings.getString("ClanNameTemplate", ".*");
			MAX_CHARACTERS_NUMBER_PER_ACCOUNT = serverSettings.getInt("CharMaxNumber", 7);
			MAXIMUM_ONLINE_USERS = serverSettings.getInt("MaximumOnlineUsers", 6500);
			MAXIMUM_ONLINE_USERS_QUEUE = serverSettings.getInt("MaximumOnlineUsersQueue", 4000);
			HARDWARE_INFO_ENABLED = serverSettings.getBoolean("EnableHardwareInfo", false);
			KICK_MISSING_HWID = serverSettings.getBoolean("KickMissingHWID", false);
			MAX_PLAYERS_PER_HWID = serverSettings.getInt("MaxPlayersPerHWID", 0);
			ENABLE_PREMIUM_HWID_SLOTS = serverSettings.getBoolean("EnablePremiumHwidSlots", false);
			PREMIUM_HWID_SLOTS = serverSettings.getInt("PremiumHwidSlots", 2);
			if (MAX_PLAYERS_PER_HWID > 0)
			{
				KICK_MISSING_HWID = true;
			}
			final String[] protocols = serverSettings.getString("AllowedProtocolRevisions", "603;606;607").split(";");
			PROTOCOL_LIST = new ArrayList<>(protocols.length);
			for (String protocol : protocols)
			{
				try
				{
					PROTOCOL_LIST.add(Integer.parseInt(protocol.trim()));
				}
				catch (NumberFormatException e)
				{
					LOGGER.warning("Wrong config protocol version: " + protocol + ". Skipped.");
				}
			}
			SERVER_LIST_TYPE = getServerTypeId(serverSettings.getString("ServerListType", "Free").split(","));
			SERVER_LIST_AGE = serverSettings.getInt("ServerListAge", 0);
			SERVER_LIST_BRACKET = serverSettings.getBoolean("ServerListBrackets", false);
			SCHEDULED_POOL_COUNT = serverSettings.getInt("ScheduledPoolCount", 3);
			INSTANT_POOL_COUNT = serverSettings.getInt("InstantPoolCount", 3);
			SCHEDULED_THREAD_POOL_COUNT = serverSettings.getInt("ScheduledThreadPoolCount", 40);
			INSTANT_THREAD_POOL_COUNT = serverSettings.getInt("InstantThreadPoolCount", 20);
			IO_PACKET_THREAD_CORE_SIZE = serverSettings.getInt("UrgentPacketThreadCoreSize", 20);
			THREADS_FOR_LOADING = serverSettings.getBoolean("ThreadsForLoading", false);
			AUTO_PLAY_THREAD_COUNT = serverSettings.getInt("AutoPlayThreadCount", 6);
			AUTO_USE_THREAD_COUNT = serverSettings.getInt("AutoUseThreadCount", 2);
			ATTACKABLE_AI_THREAD_COUNT = serverSettings.getInt("AttackableAIThreadCount", 2);
			RESPAWN_MANAGER_THREAD_COUNT = serverSettings.getInt("RespawnManagerThreadCount", 1);
			DEADLOCK_DETECTOR = serverSettings.getBoolean("DeadLockDetector", true);
			DEADLOCK_CHECK_INTERVAL = serverSettings.getInt("DeadLockCheckInterval", 20);
			RESTART_ON_DEADLOCK = serverSettings.getBoolean("RestartOnDeadlock", false);
			SERVER_RESTART_SCHEDULE_ENABLED = serverSettings.getBoolean("ServerRestartScheduleEnabled", false);
			SERVER_RESTART_SCHEDULE_MESSAGE = serverSettings.getBoolean("ServerRestartScheduleMessage", false);
			SERVER_RESTART_SCHEDULE_COUNTDOWN = serverSettings.getInt("ServerRestartScheduleCountdown", 600);
			SERVER_RESTART_SCHEDULE = serverSettings.getString("ServerRestartSchedule", "08:00").split(",");
			SERVER_RESTART_DAYS = new ArrayList<>();
			for (String day : serverSettings.getString("ServerRestartDays", "").trim().split(","))
			{
				if (Util.isDigit(day))
				{
					SERVER_RESTART_DAYS.add(Integer.parseInt(day));
				}
			}
			PRECAUTIONARY_RESTART_ENABLED = serverSettings.getBoolean("PrecautionaryRestartEnabled", false);
			PRECAUTIONARY_RESTART_CPU = serverSettings.getBoolean("PrecautionaryRestartCpu", true);
			PRECAUTIONARY_RESTART_MEMORY = serverSettings.getBoolean("PrecautionaryRestartMemory", false);
			PRECAUTIONARY_RESTART_CHECKS = serverSettings.getBoolean("PrecautionaryRestartChecks", true);
			PRECAUTIONARY_RESTART_PERCENTAGE = serverSettings.getInt("PrecautionaryRestartPercentage", 95);
			PRECAUTIONARY_RESTART_DELAY = serverSettings.getInt("PrecautionaryRestartDelay", 60) * 1000;
			IS_WINDOWS = System.getProperty("os.name").toLowerCase().startsWith("windows");
			// Hosts and Subnets
			final IPConfigData ipcd = new IPConfigData();
			GAME_SERVER_SUBNETS = ipcd.getSubnets();
			GAME_SERVER_HOSTS = ipcd.getHosts();
			// Load Feature config file (if exists)
			final PropertiesParser Feature = new PropertiesParser(FEATURE_CONFIG_FILE);
			ENABLE_DATABASE_LOGGER = Feature.getBoolean("EnableDatabaseLogger", false);
			BALOK_ENABLED = Feature.getBoolean("BalokEnabled", false);
			BALOK_LOGGER_ENABLED = Feature.getBoolean("BalokLoggerEnabled", false);
			ENCHANT_CHALLENGE_POINTS_ENABLED = Feature.getBoolean("EnchantChallengePointsEnabled", false);
			HELLBOUND_ENABLE = Feature.getBoolean("HellBoundEnable", false);
			HELLBOUND_LOGGER_ENABLE = Feature.getBoolean("HellBoundLoggerEnable", false);
			HELLBOUND_GM_FORCE_OPEN = false;
			HELLBOUND_GM_FORCE_CLOSE = false;
			HELLBOUND_GM_FORCE_OPEN_TIME = -1;
			ATINGO_ENABLE = Feature.getBoolean("AtingoEnable", false);
			ATINGO_LOGGER_ENABLE = Feature.getBoolean("AtingoLoggerEnable", false);
			ATINGO_RESPAWN_TIME = Feature.getLong("AtingoRespawnTime", 60 * 60 * 24 * 1000L);
			ATINGO_MOVE_TIME = Feature.getLong("AtingoMoveTime", 60 * 60 * 24 * 1000L);
			PET_DELETE_TIMEOUT = Feature.getLong("AtingoPetTimeOut", 60 * 60 * 1000L);
			SIN_EATER_CHANCE = Feature.getInt("AtingoSinEaterChance", 70);
			FRINTEZZA_ENABLE = Feature.getBoolean("FrintezzaEnable", false);
			MIN_LEVEL_ENTER_FRINTEZZA = Feature.getInt("MinLevelEnterFrintezza", 76);
			DWELLING_OF_SPIRITS_ENABLE = Feature.getBoolean("DwellingOfSpiritsEnable", false);
			DWELLING_OF_SPIRITS_LOGGER_ENABLE = Feature.getBoolean("DwellingOfSpiritsLoggerEnable", false);
			DWELLING_OF_SPIRITS_PORTAL_OPEN_CHANCE = Feature.getInt("DwellingOfSpiritsPortalOpenChance", 1);
			DREAM_DUNGEON_ENABLE = Feature.getBoolean("DreamDungeonEnable", false);
			PURGE_ENABLE = Feature.getBoolean("PurgeEnable", true);
			TOI_REINFORCED_MOBS_ENABLED = Feature.getBoolean("ToiReinforcedMobsEnabled", true);
			TOI_REINFORCED_MOBS_KILL_COUNT = Feature.getInt("ToiReinforcedMobsKillCount", 10000);
			TOI_REINFORCED_MOBS_DURATION = Feature.getInt("ToiReinforcedMobsDuration", 60);
			SIEGE_HOUR_LIST = new ArrayList<>();
			for (String hour : Feature.getString("SiegeHourList", "").split(","))
			{
				if (Util.isDigit(hour))
				{
					SIEGE_HOUR_LIST.add(Integer.parseInt(hour));
				}
			}
			CASTLE_BUY_TAX_NEUTRAL = Feature.getInt("BuyTaxForNeutralSide", 15);
			CASTLE_BUY_TAX_LIGHT = Feature.getInt("BuyTaxForLightSide", 0);
			CASTLE_BUY_TAX_DARK = Feature.getInt("BuyTaxForDarkSide", 30);
			CASTLE_SELL_TAX_NEUTRAL = Feature.getInt("SellTaxForNeutralSide", 0);
			CASTLE_SELL_TAX_LIGHT = Feature.getInt("SellTaxForLightSide", 0);
			CASTLE_SELL_TAX_DARK = Feature.getInt("SellTaxForDarkSide", 20);
			CS_TELE_FEE_RATIO = Feature.getLong("CastleTeleportFunctionFeeRatio", *********);
			CS_TELE1_FEE = Feature.getInt("CastleTeleportFunctionFeeLvl1", 1000);
			CS_TELE2_FEE = Feature.getInt("CastleTeleportFunctionFeeLvl2", 10000);
			CS_SUPPORT_FEE_RATIO = Feature.getLong("CastleSupportFunctionFeeRatio", *********);
			CS_SUPPORT1_FEE = Feature.getInt("CastleSupportFeeLvl1", 49000);
			CS_SUPPORT2_FEE = Feature.getInt("CastleSupportFeeLvl2", 120000);
			CS_MPREG_FEE_RATIO = Feature.getLong("CastleMpRegenerationFunctionFeeRatio", *********);
			CS_MPREG1_FEE = Feature.getInt("CastleMpRegenerationFeeLvl1", 45000);
			CS_MPREG2_FEE = Feature.getInt("CastleMpRegenerationFeeLvl2", 65000);
			CS_HPREG_FEE_RATIO = Feature.getLong("CastleHpRegenerationFunctionFeeRatio", *********);
			CS_HPREG1_FEE = Feature.getInt("CastleHpRegenerationFeeLvl1", 12000);
			CS_HPREG2_FEE = Feature.getInt("CastleHpRegenerationFeeLvl2", 20000);
			CS_EXPREG_FEE_RATIO = Feature.getLong("CastleExpRegenerationFunctionFeeRatio", *********);
			CS_EXPREG1_FEE = Feature.getInt("CastleExpRegenerationFeeLvl1", 63000);
			CS_EXPREG2_FEE = Feature.getInt("CastleExpRegenerationFeeLvl2", 70000);
			OUTER_DOOR_UPGRADE_PRICE2 = Feature.getInt("OuterDoorUpgradePriceLvl2", 3000000);
			OUTER_DOOR_UPGRADE_PRICE3 = Feature.getInt("OuterDoorUpgradePriceLvl3", 4000000);
			OUTER_DOOR_UPGRADE_PRICE5 = Feature.getInt("OuterDoorUpgradePriceLvl5", 5000000);
			INNER_DOOR_UPGRADE_PRICE2 = Feature.getInt("InnerDoorUpgradePriceLvl2", 750000);
			INNER_DOOR_UPGRADE_PRICE3 = Feature.getInt("InnerDoorUpgradePriceLvl3", 900000);
			INNER_DOOR_UPGRADE_PRICE5 = Feature.getInt("InnerDoorUpgradePriceLvl5", 1000000);
			WALL_UPGRADE_PRICE2 = Feature.getInt("WallUpgradePriceLvl2", 1600000);
			WALL_UPGRADE_PRICE3 = Feature.getInt("WallUpgradePriceLvl3", 1800000);
			WALL_UPGRADE_PRICE5 = Feature.getInt("WallUpgradePriceLvl5", 2000000);
			TRAP_UPGRADE_PRICE1 = Feature.getInt("TrapUpgradePriceLvl1", 3000000);
			TRAP_UPGRADE_PRICE2 = Feature.getInt("TrapUpgradePriceLvl2", 4000000);
			TRAP_UPGRADE_PRICE3 = Feature.getInt("TrapUpgradePriceLvl3", 5000000);
			TRAP_UPGRADE_PRICE4 = Feature.getInt("TrapUpgradePriceLvl4", 6000000);
			FS_TELE_FEE_RATIO = Feature.getLong("FortressTeleportFunctionFeeRatio", *********);
			FS_TELE1_FEE = Feature.getInt("FortressTeleportFunctionFeeLvl1", 1000);
			FS_TELE2_FEE = Feature.getInt("FortressTeleportFunctionFeeLvl2", 10000);
			FS_SUPPORT_FEE_RATIO = Feature.getLong("FortressSupportFunctionFeeRatio", 86400000);
			FS_SUPPORT1_FEE = Feature.getInt("FortressSupportFeeLvl1", 7000);
			FS_SUPPORT2_FEE = Feature.getInt("FortressSupportFeeLvl2", 17000);
			FS_MPREG_FEE_RATIO = Feature.getLong("FortressMpRegenerationFunctionFeeRatio", 86400000);
			FS_MPREG1_FEE = Feature.getInt("FortressMpRegenerationFeeLvl1", 6500);
			FS_MPREG2_FEE = Feature.getInt("FortressMpRegenerationFeeLvl2", 9300);
			FS_HPREG_FEE_RATIO = Feature.getLong("FortressHpRegenerationFunctionFeeRatio", 86400000);
			FS_HPREG1_FEE = Feature.getInt("FortressHpRegenerationFeeLvl1", 2000);
			FS_HPREG2_FEE = Feature.getInt("FortressHpRegenerationFeeLvl2", 3500);
			FS_EXPREG_FEE_RATIO = Feature.getLong("FortressExpRegenerationFunctionFeeRatio", 86400000);
			FS_EXPREG1_FEE = Feature.getInt("FortressExpRegenerationFeeLvl1", 9000);
			FS_EXPREG2_FEE = Feature.getInt("FortressExpRegenerationFeeLvl2", 10000);
			FS_UPDATE_FRQ = Feature.getInt("FortressPeriodicUpdateFrequency", 360);
			FS_BLOOD_OATH_COUNT = Feature.getInt("FortressBloodOathCount", 1);
			FS_MAX_SUPPLY_LEVEL = Feature.getInt("FortressMaxSupplyLevel", 6);
			FS_FEE_FOR_CASTLE = Feature.getInt("FortressFeeForCastle", 25000);
			FS_MAX_OWN_TIME = Feature.getInt("FortressMaximumOwnTime", 168);
			TAKE_FORT_POINTS = Feature.getInt("TakeFortPoints", 200);
			LOOSE_FORT_POINTS = Feature.getInt("LooseFortPoints", 0);
			TAKE_CASTLE_POINTS = Feature.getInt("TakeCastlePoints", 1500);
			LOOSE_CASTLE_POINTS = Feature.getInt("LooseCastlePoints", 3000);
			CASTLE_DEFENDED_POINTS = Feature.getInt("CastleDefendedPoints", 750);
			FESTIVAL_WIN_POINTS = Feature.getInt("FestivalOfDarknessWin", 200);
			HERO_POINTS = Feature.getInt("HeroPoints", 1000);
			ROYAL_GUARD_COST = Feature.getInt("CreateRoyalGuardCost", 5000);
			KNIGHT_UNIT_COST = Feature.getInt("CreateKnightUnitCost", 10000);
			KNIGHT_REINFORCE_COST = Feature.getInt("ReinforceKnightUnitCost", 5000);
			BALLISTA_POINTS = Feature.getInt("KillBallistaPoints", 500);
			BLOODALLIANCE_POINTS = Feature.getInt("BloodAlliancePoints", 500);
			BLOODOATH_POINTS = Feature.getInt("BloodOathPoints", 200);
			KNIGHTSEPAULETTE_POINTS = Feature.getInt("KnightsEpaulettePoints", 20);
			REPUTATION_SCORE_PER_KILL = Feature.getInt("ReputationScorePerKill", 1);
			JOIN_ACADEMY_MIN_REP_SCORE = Feature.getInt("CompleteAcademyMinPoints", 190);
			JOIN_ACADEMY_MAX_REP_SCORE = Feature.getInt("CompleteAcademyMaxPoints", 650);
			CLAN_LEVEL_6_COST = Feature.getInt("ClanLevel6Cost", 5000);
			CLAN_LEVEL_7_COST = Feature.getInt("ClanLevel7Cost", 10000);
			CLAN_LEVEL_8_COST = Feature.getInt("ClanLevel8Cost", 20000);
			CLAN_LEVEL_9_COST = Feature.getInt("ClanLevel9Cost", 40000);
			CLAN_LEVEL_10_COST = Feature.getInt("ClanLevel10Cost", 40000);
			CLAN_LEVEL_11_COST = Feature.getInt("ClanLevel11Cost", 75000);
			CLAN_LEVEL_6_REQUIREMENT = Feature.getInt("ClanLevel6Requirement", 30);
			CLAN_LEVEL_7_REQUIREMENT = Feature.getInt("ClanLevel7Requirement", 50);
			CLAN_LEVEL_8_REQUIREMENT = Feature.getInt("ClanLevel8Requirement", 80);
			CLAN_LEVEL_9_REQUIREMENT = Feature.getInt("ClanLevel9Requirement", 120);
			CLAN_LEVEL_10_REQUIREMENT = Feature.getInt("ClanLevel10Requirement", 140);
			CLAN_LEVEL_11_REQUIREMENT = Feature.getInt("ClanLevel11Requirement", 170);
			ALLOW_WYVERN_ALWAYS = Feature.getBoolean("AllowRideWyvernAlways", false);
			ALLOW_WYVERN_DURING_SIEGE = Feature.getBoolean("AllowRideWyvernDuringSiege", true);
			ALLOW_MOUNTS_DURING_SIEGE = Feature.getBoolean("AllowRideMountsDuringSiege", false);
			EXP_FOR_MAGIC_LAMP = Feature.getInt("ExpForMagicLamp", 10000000);
			MAX_MAGIC_LAMP_AMOUNT = Feature.getInt("MaxMagicLampAmount", 300);
			SHARE_POSITION_CREATE_PRICE = Feature.getInt("SharePositionCreatePrice", 50);
			SHARE_POSITION_TELEPORT_PRICE = Feature.getInt("SharePositionTeleportPrice", 400);
			ORC_FORTRESS_ENABLE = Feature.getBoolean("OrcFortressEnable", false);
			COLLECTIONS_ENABLED = Feature.getBoolean("CollectionsEnabled", true);
			KEBER_ENABLED = Feature.getBoolean("KeberEnabled", false);
			// Custom Limit Barrier
			HIT_COUNT = Feature.getInt("HitCound", 500);
			TIME_DESTROY_BARRIER = Feature.getInt("TimeToDestroyBarrier", 20);
			// Load Attendance config file (if exists)
			final PropertiesParser Attandance = new PropertiesParser(ATTENDANCE_CONFIG_FILE);
			ENABLE_ATTENDANCE_REWARDS = Attandance.getBoolean("EnableAttendanceRewards", false);
			PREMIUM_ONLY_ATTENDANCE_REWARDS = Attandance.getBoolean("PremiumOnlyAttendanceRewards", false);
			ATTENDANCE_REWARDS_SHARE_ACCOUNT = Attandance.getBoolean("AttendanceRewardsShareAccount", false);
			ATTENDANCE_REWARD_DELAY = Attandance.getInt("AttendanceRewardDelay", 30);
			ATTENDANCE_POPUP_START = Attandance.getBoolean("AttendancePopupStart", true);
			ATTENDANCE_POPUP_WINDOW = Attandance.getBoolean("AttendancePopupWindow", false);
			// Load AttributeSystem config file (if exists)
			final PropertiesParser AttributeSystem = new PropertiesParser(ATTRIBUTE_SYSTEM_FILE);
			S_WEAPON_STONE = AttributeSystem.getInt("SWeaponStone", 50);
			S80_WEAPON_STONE = AttributeSystem.getInt("S80WeaponStone", 50);
			S84_WEAPON_STONE = AttributeSystem.getInt("S84WeaponStone", 50);
			R_WEAPON_STONE = AttributeSystem.getInt("RWeaponStone", 50);
			R95_WEAPON_STONE = AttributeSystem.getInt("R95WeaponStone", 50);
			R99_WEAPON_STONE = AttributeSystem.getInt("R99WeaponStone", 50);
			S_ARMOR_STONE = AttributeSystem.getInt("SArmorStone", 60);
			S80_ARMOR_STONE = AttributeSystem.getInt("S80ArmorStone", 80);
			S84_ARMOR_STONE = AttributeSystem.getInt("S84ArmorStone", 80);
			R_ARMOR_STONE = AttributeSystem.getInt("RArmorStone", 100);
			R95_ARMOR_STONE = AttributeSystem.getInt("R95ArmorStone", 100);
			R99_ARMOR_STONE = AttributeSystem.getInt("R99ArmorStone", 100);
			S_WEAPON_CRYSTAL = AttributeSystem.getInt("SWeaponCrystal", 30);
			S80_WEAPON_CRYSTAL = AttributeSystem.getInt("S80WeaponCrystal", 40);
			S84_WEAPON_CRYSTAL = AttributeSystem.getInt("S84WeaponCrystal", 50);
			R_WEAPON_CRYSTAL = AttributeSystem.getInt("RWeaponCrystal", 60);
			R95_WEAPON_CRYSTAL = AttributeSystem.getInt("R95WeaponCrystal", 60);
			R99_WEAPON_CRYSTAL = AttributeSystem.getInt("R99WeaponCrystal", 60);
			S_ARMOR_CRYSTAL = AttributeSystem.getInt("SArmorCrystal", 50);
			S80_ARMOR_CRYSTAL = AttributeSystem.getInt("S80ArmorCrystal", 70);
			S84_ARMOR_CRYSTAL = AttributeSystem.getInt("S84ArmorCrystal", 80);
			R_ARMOR_CRYSTAL = AttributeSystem.getInt("RArmorCrystal", 80);
			R95_ARMOR_CRYSTAL = AttributeSystem.getInt("R95ArmorCrystal", 100);
			R99_ARMOR_CRYSTAL = AttributeSystem.getInt("R99ArmorCrystal", 100);
			S_WEAPON_STONE_SUPER = AttributeSystem.getInt("SWeaponStoneSuper", 100);
			S80_WEAPON_STONE_SUPER = AttributeSystem.getInt("S80WeaponStoneSuper", 100);
			S84_WEAPON_STONE_SUPER = AttributeSystem.getInt("S84WeaponStoneSuper", 100);
			R_WEAPON_STONE_SUPER = AttributeSystem.getInt("RWeaponStoneSuper", 100);
			R95_WEAPON_STONE_SUPER = AttributeSystem.getInt("R95WeaponStoneSuper", 100);
			R99_WEAPON_STONE_SUPER = AttributeSystem.getInt("R99WeaponStoneSuper", 100);
			S_ARMOR_STONE_SUPER = AttributeSystem.getInt("SArmorStoneSuper", 100);
			S80_ARMOR_STONE_SUPER = AttributeSystem.getInt("S80ArmorStoneSuper", 100);
			S84_ARMOR_STONE_SUPER = AttributeSystem.getInt("S84ArmorStoneSuper", 100);
			R_ARMOR_STONE_SUPER = AttributeSystem.getInt("RArmorStoneSuper", 100);
			R95_ARMOR_STONE_SUPER = AttributeSystem.getInt("R95ArmorStoneSuper", 100);
			R99_ARMOR_STONE_SUPER = AttributeSystem.getInt("R99ArmorStoneSuper", 100);
			S_WEAPON_CRYSTAL_SUPER = AttributeSystem.getInt("SWeaponCrystalSuper", 80);
			S80_WEAPON_CRYSTAL_SUPER = AttributeSystem.getInt("S80WeaponCrystalSuper", 90);
			S84_WEAPON_CRYSTAL_SUPER = AttributeSystem.getInt("S84WeaponCrystalSuper", 100);
			R_WEAPON_CRYSTAL_SUPER = AttributeSystem.getInt("RWeaponCrystalSuper", 100);
			R95_WEAPON_CRYSTAL_SUPER = AttributeSystem.getInt("R95WeaponCrystalSuper", 100);
			R99_WEAPON_CRYSTAL_SUPER = AttributeSystem.getInt("R99WeaponCrystalSuper", 100);
			S_ARMOR_CRYSTAL_SUPER = AttributeSystem.getInt("SArmorCrystalSuper", 100);
			S80_ARMOR_CRYSTAL_SUPER = AttributeSystem.getInt("S80ArmorCrystalSuper", 100);
			S84_ARMOR_CRYSTAL_SUPER = AttributeSystem.getInt("S84ArmorCrystalSuper", 100);
			R_ARMOR_CRYSTAL_SUPER = AttributeSystem.getInt("RArmorCrystalSuper", 100);
			R95_ARMOR_CRYSTAL_SUPER = AttributeSystem.getInt("R95ArmorCrystalSuper", 100);
			R99_ARMOR_CRYSTAL_SUPER = AttributeSystem.getInt("R99ArmorCrystalSuper", 100);
			S_WEAPON_JEWEL = AttributeSystem.getInt("SWeaponJewel", 100);
			S80_WEAPON_JEWEL = AttributeSystem.getInt("S80WeaponJewel", 100);
			S84_WEAPON_JEWEL = AttributeSystem.getInt("S84WeaponJewel", 100);
			R_WEAPON_JEWEL = AttributeSystem.getInt("RWeaponJewel", 100);
			R95_WEAPON_JEWEL = AttributeSystem.getInt("R95WeaponJewel", 100);
			R99_WEAPON_JEWEL = AttributeSystem.getInt("R99WeaponJewel", 100);
			S_ARMOR_JEWEL = AttributeSystem.getInt("SArmorJewel", 100);
			S80_ARMOR_JEWEL = AttributeSystem.getInt("S80ArmorJewel", 100);
			S84_ARMOR_JEWEL = AttributeSystem.getInt("S84ArmorJewel", 100);
			R_ARMOR_JEWEL = AttributeSystem.getInt("RArmorJewel", 100);
			R95_ARMOR_JEWEL = AttributeSystem.getInt("R95ArmorJewel", 100);
			R99_ARMOR_JEWEL = AttributeSystem.getInt("R99ArmorJewel", 100);
			// Load Character config file (if exists)
			final PropertiesParser Character = new PropertiesParser(CHARACTER_CONFIG_FILE);
			PLAYER_DELEVEL = Character.getBoolean("Delevel", true);
			DELEVEL_MINIMUM = Character.getInt("DelevelMinimum", 85);
			DECREASE_SKILL_LEVEL = Character.getBoolean("DecreaseSkillOnDelevel", true);
			ALT_WEIGHT_LIMIT = Character.getDouble("AltWeightLimit", 1);
			RUN_SPD_BOOST = Character.getInt("RunSpeedBoost", 0);
			RESPAWN_RESTORE_CP = Character.getDouble("RespawnRestoreCP", 0) / 100;
			RESPAWN_RESTORE_HP = Character.getDouble("RespawnRestoreHP", 65) / 100;
			RESPAWN_RESTORE_MP = Character.getDouble("RespawnRestoreMP", 0) / 100;
			HP_REGEN_MULTIPLIER = Character.getDouble("HpRegenMultiplier", 100) / 100;
			MP_REGEN_MULTIPLIER = Character.getDouble("MpRegenMultiplier", 100) / 100;
			CP_REGEN_MULTIPLIER = Character.getDouble("CpRegenMultiplier", 100) / 100;
			ENABLE_MODIFY_SKILL_DURATION = Character.getBoolean("EnableModifySkillDuration", false);
			if (ENABLE_MODIFY_SKILL_DURATION)
			{
				final String[] propertySplit = Character.getString("SkillDurationList", "").split(";");
				SKILL_DURATION_LIST = new HashMap<>(propertySplit.length);
				for (String skill : propertySplit)
				{
					final String[] skillSplit = skill.split(",");
					if (skillSplit.length != 2)
					{
						LOGGER.warning("[SkillDurationList]: invalid config property -> SkillDurationList " + skill);
					}
					else
					{
						try
						{
							SKILL_DURATION_LIST.put(Integer.parseInt(skillSplit[0]), Integer.parseInt(skillSplit[1]));
						}
						catch (NumberFormatException nfe)
						{
							if (!skill.isEmpty())
							{
								LOGGER.warning(StringUtil.concat("[SkillDurationList]: invalid config property -> SkillList \"", skillSplit[0], "\"", skillSplit[1]));
							}
						}
					}
				}
			}
			ENABLE_MODIFY_SKILL_REUSE = Character.getBoolean("EnableModifySkillReuse", false);
			if (ENABLE_MODIFY_SKILL_REUSE)
			{
				final String[] propertySplit = Character.getString("SkillReuseList", "").split(";");
				SKILL_REUSE_LIST = new HashMap<>(propertySplit.length);
				for (String skill : propertySplit)
				{
					final String[] skillSplit = skill.split(",");
					if (skillSplit.length != 2)
					{
						LOGGER.warning(StringUtil.concat("[SkillReuseList]: invalid config property -> SkillReuseList \"", skill, "\""));
					}
					else
					{
						try
						{
							SKILL_REUSE_LIST.put(Integer.parseInt(skillSplit[0]), Integer.parseInt(skillSplit[1]));
						}
						catch (NumberFormatException nfe)
						{
							if (!skill.isEmpty())
							{
								LOGGER.warning(StringUtil.concat("[SkillReuseList]: invalid config property -> SkillList \"", skillSplit[0], "\"", skillSplit[1]));
							}
						}
					}
				}
			}
			AUTO_LEARN_SKILLS = Character.getBoolean("AutoLearnSkills", false);
			AUTO_LEARN_FS_SKILLS = Character.getBoolean("AutoLearnForgottenScrollSkills", false);
			AUTO_LOOT_HERBS = Character.getBoolean("AutoLootHerbs", false);
			BUFFS_MAX_AMOUNT = Character.getByte("MaxBuffAmount", (byte) 20);
			DEBUFFS_MAX_AMOUNT = Character.getByte("MaxDebuffAmount", (byte) 10);
			TRIGGERED_BUFFS_MAX_AMOUNT = Character.getByte("MaxTriggeredBuffAmount", (byte) 12);
			DANCES_MAX_AMOUNT = Character.getByte("MaxDanceAmount", (byte) 12);
			DANCE_CANCEL_BUFF = Character.getBoolean("DanceCancelBuff", false);
			DANCE_CONSUME_ADDITIONAL_MP = Character.getBoolean("DanceConsumeAdditionalMP", true);
			ALT_STORE_DANCES = Character.getBoolean("AltStoreDances", false);
			AUTO_LEARN_DIVINE_INSPIRATION = Character.getBoolean("AutoLearnDivineInspiration", false);
			ALT_GAME_CANCEL_BOW = Character.getString("AltGameCancelByHit", "Cast").equalsIgnoreCase("bow") || Character.getString("AltGameCancelByHit", "Cast").equalsIgnoreCase("all");
			ALT_GAME_CANCEL_CAST = Character.getString("AltGameCancelByHit", "Cast").equalsIgnoreCase("cast") || Character.getString("AltGameCancelByHit", "Cast").equalsIgnoreCase("all");
			ALT_GAME_MAGICFAILURES = Character.getBoolean("MagicFailures", true);
			ALT_GAME_STUN_BREAK = Character.getBoolean("BreakStun", false);
			PLAYER_FAKEDEATH_UP_PROTECTION = Character.getInt("PlayerFakeDeathUpProtection", 0);
			STORE_SKILL_COOLTIME = Character.getBoolean("StoreSkillCooltime", true);
			SUBCLASS_STORE_SKILL_COOLTIME = Character.getBoolean("SubclassStoreSkillCooltime", false);
			SUMMON_STORE_SKILL_COOLTIME = Character.getBoolean("SummonStoreSkillCooltime", true);
			EFFECT_TICK_RATIO = Character.getLong("EffectTickRatio", 666);
			FAKE_DEATH_UNTARGET = Character.getBoolean("FakeDeathUntarget", true);
			FAKE_DEATH_DAMAGE_STAND = Character.getBoolean("FakeDeathDamageStand", false);
			LIFE_CRYSTAL_NEEDED = Character.getBoolean("LifeCrystalNeeded", true);
			DIVINE_SP_BOOK_NEEDED = Character.getBoolean("DivineInspirationSpBookNeeded", true);
			ALT_GAME_SUBCLASS_WITHOUT_QUESTS = Character.getBoolean("AltSubClassWithoutQuests", false);
			ALT_GAME_SUBCLASS_EVERYWHERE = Character.getBoolean("AltSubclassEverywhere", false);
			RESTORE_SERVITOR_ON_RECONNECT = Character.getBoolean("RestoreServitorOnReconnect", true);
			RESTORE_PET_ON_RECONNECT = Character.getBoolean("RestorePetOnReconnect", true);
			ALLOW_TRANSFORM_WITHOUT_QUEST = Character.getBoolean("AltTransformationWithoutQuest", false);
			FEE_DELETE_TRANSFER_SKILLS = Character.getInt("FeeDeleteTransferSkills", 10000000);
			FEE_DELETE_SUBCLASS_SKILLS = Character.getInt("FeeDeleteSubClassSkills", 10000000);
			FEE_DELETE_DUALCLASS_SKILLS = Character.getInt("FeeDeleteDualClassSkills", 20000000);
			ENABLE_SAYHA_GRACE = Character.getBoolean("EnableSayhaGrace", true);
			STARTING_SAYHA_GRACE_POINTS = Character.getInt("StartingSayhaGracePoints", 140000);
			RAIDBOSS_USE_SAYHA_GRACE = Character.getBoolean("RaidbossUseSayhaGrace", true);
			MAX_BONUS_EXP = Character.getDouble("MaxExpBonus", 0);
			MAX_BONUS_SP = Character.getDouble("MaxSpBonus", 0);
			MAX_RUN_SPEED = Character.getInt("MaxRunSpeed", 300);
			MAX_PATK = Character.getInt("MaxPAtk", 999999);
			MAX_MATK = Character.getInt("MaxMAtk", 999999);
			MAX_PCRIT_RATE = Character.getInt("MaxPCritRate", 500);
			MAX_MCRIT_RATE = Character.getInt("MaxMCritRate", 200);
			MAX_PATK_SPEED = Character.getInt("MaxPAtkSpeed", 1500);
			MAX_MATK_SPEED = Character.getInt("MaxMAtkSpeed", 1999);
			MAX_EVASION = Character.getInt("MaxEvasion", 250);
			MIN_ABNORMAL_STATE_SUCCESS_RATE = Character.getInt("MinAbnormalStateSuccessRate", 10);
			MAX_ABNORMAL_STATE_SUCCESS_RATE = Character.getInt("MaxAbnormalStateSuccessRate", 90);
			MAX_SP = Character.getLong("MaxSp", 50000000000L) >= 0 ? Character.getLong("MaxSp", 50000000000L) : Long.MAX_VALUE;
			PLAYER_MAXIMUM_LEVEL = Character.getInt("MaximumPlayerLevel", 99);
			PLAYER_MAXIMUM_LEVEL++;
			MAX_SUBCLASS = Math.min(3, Character.getInt("MaxSubclass", 3));
			BASE_SUBCLASS_LEVEL = Character.getInt("BaseSubclassLevel", 40);
			BASE_DUALCLASS_LEVEL = Character.getInt("BaseDualclassLevel", 85);
			MAX_SUBCLASS_LEVEL = Character.getInt("MaxSubclassLevel", 80);
			MAX_PVTSTORESELL_SLOTS_DWARF = Character.getInt("MaxPvtStoreSellSlotsDwarf", 4);
			MAX_PVTSTORESELL_SLOTS_OTHER = Character.getInt("MaxPvtStoreSellSlotsOther", 3);
			MAX_PVTSTOREBUY_SLOTS_DWARF = Character.getInt("MaxPvtStoreBuySlotsDwarf", 5);
			MAX_PVTSTOREBUY_SLOTS_OTHER = Character.getInt("MaxPvtStoreBuySlotsOther", 4);
			INVENTORY_MAXIMUM_NO_DWARF = Character.getInt("MaximumSlotsForNoDwarf", 80);
			INVENTORY_MAXIMUM_DWARF = Character.getInt("MaximumSlotsForDwarf", 100);
			INVENTORY_MAXIMUM_GM = Character.getInt("MaximumSlotsForGMPlayer", 250);
			INVENTORY_MAXIMUM_QUEST_ITEMS = Character.getInt("MaximumSlotsForQuestItems", 100);
			MAX_ITEM_IN_PACKET = Math.max(INVENTORY_MAXIMUM_NO_DWARF, Math.max(INVENTORY_MAXIMUM_DWARF, INVENTORY_MAXIMUM_GM));
			WAREHOUSE_SLOTS_DWARF = Character.getInt("MaximumWarehouseSlotsForDwarf", 120);
			WAREHOUSE_SLOTS_NO_DWARF = Character.getInt("MaximumWarehouseSlotsForNoDwarf", 100);
			WAREHOUSE_SLOTS_CLAN = Character.getInt("MaximumWarehouseSlotsForClan", 150);
			DKP_WAREHOUSE_SLOTS_CLAN = Character.getInt("MaximumDkpWarehouseSlotsForClan", 150);
			DKP_AUCTION_WAREHOUSE_SLOTS_CLAN = Character.getInt("MaximumDkpAuctionWarehouseSlotsForClan", 8);
			ALT_FREIGHT_SLOTS = Character.getInt("MaximumFreightSlots", 200);
			ALT_FREIGHT_PRICE = Character.getInt("FreightPrice", 1000);
			MENTOR_PENALTY_FOR_MENTEE_COMPLETE = Character.getInt("MentorPenaltyForMenteeComplete", 1) * 24 * 60 * 60 * 1000;
			MENTOR_PENALTY_FOR_MENTEE_COMPLETE = Character.getInt("MentorPenaltyForMenteeLeave", 2) * 24 * 60 * 60 * 1000;
			final String[] notenchantable = Character.getString("EnchantBlackList", "7816,7817,7818,7819,7820,7821,7822,7823,7824,7825,7826,7827,7828,7829,7830,7831,13293,13294,13296").split(",");
			ENCHANT_BLACKLIST = new int[notenchantable.length];
			for (int i = 0; i < notenchantable.length; i++)
			{
				ENCHANT_BLACKLIST[i] = Integer.parseInt(notenchantable[i]);
			}
			Arrays.sort(ENCHANT_BLACKLIST);
			DISABLE_OVER_ENCHANTING = Character.getBoolean("DisableOverEnchanting", true);
			MIN_ARMOR_ENCHANT_ANNOUNCE_DCB = Character.getInt("MinimumArmorEnchantAnnounceDCB", 6);
			MIN_WEAPON_ENCHANT_ANNOUNCE_DCB = Character.getInt("MinimumWeaponEnchantAnnounceDCB", 7);
			MIN_ARMOR_ENCHANT_ANNOUNCE_A = Character.getInt("MinimumArmorEnchantAnnounceA", 6);
			MIN_WEAPON_ENCHANT_ANNOUNCE_A = Character.getInt("MinimumWeaponEnchantAnnounceA", 7);
			final String[] array = Character.getString("AugmentationBlackList", "6656,6657,6658,6659,6660,6661,6662,8191,10170,10314,13740,13741,13742,13743,13744,13745,13746,13747,13748,14592,14593,14594,14595,14596,14597,14598,14599,14600,14664,14665,14666,14667,14668,14669,14670,14671,14672,14801,14802,14803,14804,14805,14806,14807,14808,14809,15282,15283,15284,15285,15286,15287,15288,15289,15290,15291,15292,15293,15294,15295,15296,15297,15298,15299,16025,16026,21712,22173,22174,22175").split(",");
			AUGMENTATION_BLACKLIST = new int[array.length];
			for (int i = 0; i < array.length; i++)
			{
				AUGMENTATION_BLACKLIST[i] = Integer.parseInt(array[i]);
			}
			Arrays.sort(AUGMENTATION_BLACKLIST);
			ALT_GAME_KARMA_PLAYER_CAN_BE_KILLED_IN_PEACEZONE = Character.getBoolean("AltKarmaPlayerCanBeKilledInPeaceZone", false);
			ALT_GAME_KARMA_PLAYER_CAN_SHOP = Character.getBoolean("AltKarmaPlayerCanShop", true);
			ALT_GAME_KARMA_PLAYER_CAN_TELEPORT = Character.getBoolean("AltKarmaPlayerCanTeleport", true);
			ALT_GAME_KARMA_PLAYER_CAN_USE_GK = Character.getBoolean("AltKarmaPlayerCanUseGK", false);
			ALT_GAME_KARMA_PLAYER_CAN_TRADE = Character.getBoolean("AltKarmaPlayerCanTrade", true);
			ALT_GAME_KARMA_PLAYER_CAN_USE_WAREHOUSE = Character.getBoolean("AltKarmaPlayerCanUseWareHouse", true);
			MAX_PERSONAL_FAME_POINTS = Character.getInt("MaxPersonalFamePoints", 100000);
			FORTRESS_ZONE_FAME_TASK_FREQUENCY = Character.getInt("FortressZoneFameTaskFrequency", 300);
			FORTRESS_ZONE_FAME_AQUIRE_POINTS = Character.getInt("FortressZoneFameAquirePoints", 0);
			CASTLE_ZONE_FAME_TASK_FREQUENCY = Character.getInt("CastleZoneFameTaskFrequency", 0);
			CASTLE_ZONE_FAME_AQUIRE_POINTS = Character.getInt("CastleZoneFameAquirePoints", 125);
			FAME_FOR_DEAD_PLAYERS = Character.getBoolean("FameForDeadPlayers", true);
			IS_CRAFTING_ENABLED = Character.getBoolean("CraftingEnabled", true);
			CRAFT_MASTERWORK = Character.getBoolean("CraftMasterwork", true);
			DWARF_RECIPE_LIMIT = Character.getInt("DwarfRecipeLimit", 50);
			COMMON_RECIPE_LIMIT = Character.getInt("CommonRecipeLimit", 50);
			ALT_GAME_CREATION = Character.getBoolean("AltGameCreation", false);
			ALT_GAME_CREATION_SPEED = Character.getDouble("AltGameCreationSpeed", 1);
			ALT_GAME_CREATION_XP_RATE = Character.getDouble("AltGameCreationXpRate", 1);
			ALT_GAME_CREATION_SP_RATE = Character.getDouble("AltGameCreationSpRate", 1);
			ALT_GAME_CREATION_RARE_XPSP_RATE = Character.getDouble("AltGameCreationRareXpSpRate", 2);
			ALT_CLAN_LEADER_INSTANT_ACTIVATION = Character.getBoolean("AltClanLeaderInstantActivation", false);
			ALT_CLAN_JOIN_MINS = Character.getInt("MinsBeforeJoinAClan", 1);
			ALT_CLAN_CREATE_MINS = Character.getInt("MinsBeforeCreateAClan", 10);
			ALT_CLAN_DISSOLVE_MINS = Character.getInt("MinsToPassToDissolveAClan", 7);
			ALT_ALLY_JOIN_MINS_WHEN_LEAVED = Character.getInt("MinsBeforeJoinAllyWhenLeaved", 1);
			ALT_ALLY_JOIN_MINS_WHEN_DISMISSED = Character.getInt("MinsBeforeJoinAllyWhenDismissed", 1);
			ALT_ACCEPT_CLAN_MINS_WHEN_DISMISSED = Character.getInt("MinsBeforeAcceptNewClanWhenDismissed", 1);
			ALT_CREATE_ALLY_MINS_WHEN_DISSOLVED = Character.getInt("MinsBeforeCreateNewAllyWhenDissolved", 1);
			ALT_MAX_NUM_OF_CLANS_IN_ALLY = Character.getInt("AltMaxNumOfClansInAlly", 3);
			ALT_CLAN_MEMBERS_FOR_WAR = Character.getInt("AltClanMembersForWar", 15);
			ALT_MEMBERS_CAN_WITHDRAW_FROM_CLANWH = Character.getBoolean("AltMembersCanWithdrawFromClanWH", false);
			ALT_CLAN_MEMBERS_TIME_FOR_BONUS = Character.getDuration("AltClanMembersTimeForBonus", "30mins").toMillis();
			REMOVE_CASTLE_CIRCLETS = Character.getBoolean("RemoveCastleCirclets", true);
			ALT_PARTY_MAX_MEMBERS = Character.getInt("AltPartyMaxMembers", 7);
			ALT_PARTY_RANGE = Character.getInt("AltPartyRange", 1500);
			ALT_LEAVE_PARTY_LEADER = Character.getBoolean("AltLeavePartyLeader", false);
			ALT_COMMAND_CHANNEL_FRIENDS = Character.getBoolean("AltCommandChannelFriends", false);
			MAX_CC_MEMBERS = Character.getInt("MaxCCMember", 65);
			INITIAL_EQUIPMENT_EVENT = Character.getBoolean("InitialEquipmentEvent", false);
			STARTING_ADENA = Character.getLong("StartingAdena", 0);
			STARTING_LEVEL = Character.getByte("StartingLevel", (byte) 1);
			STARTING_SP = Character.getInt("StartingSP", 0);
			NEWBIE_TITLE = Character.getString("NewbieTitle", "");
			MAX_ADENA = Character.getLong("MaxAdena", 99900000000L);
			if (MAX_ADENA < 0)
			{
				MAX_ADENA = Long.MAX_VALUE;
			}
			AUTO_LOOT = Character.getBoolean("AutoLoot", false);
			AUTO_LOOT_RAIDS = Character.getBoolean("AutoLootRaids", false);
			AUTO_LOOT_SLOT_LIMIT = Character.getBoolean("AutoLootSlotLimit", false);
			LOOT_RAIDS_PRIVILEGE_INTERVAL = Character.getInt("RaidLootRightsInterval", 900) * 1000;
			LOOT_RAIDS_PRIVILEGE_CC_SIZE = Character.getInt("RaidLootRightsCCSize", 45);
			final String[] autoLootItemIds = Character.getString("AutoLootItemIds", "0").split(",");
			AUTO_LOOT_ITEM_IDS = new ArrayList<>(autoLootItemIds.length);
			for (String item : autoLootItemIds)
			{
				Integer itm = 0;
				try
				{
					itm = Integer.parseInt(item);
				}
				catch (NumberFormatException nfe)
				{
					LOGGER.warning("Auto loot item ids: Wrong ItemId passed: " + item);
					LOGGER.warning(nfe.getMessage());
				}
				if (itm != 0)
				{
					AUTO_LOOT_ITEM_IDS.add(itm);
				}
			}
			ENABLE_KEYBOARD_MOVEMENT = Character.getBoolean("KeyboardMovement", true);
			UNSTUCK_INTERVAL = Character.getInt("UnstuckInterval", 300);
			TELEPORT_WATCHDOG_TIMEOUT = Character.getInt("TeleportWatchdogTimeout", 0);
			PLAYER_SPAWN_PROTECTION = Character.getInt("PlayerSpawnProtection", 0);
			PLAYER_TELEPORT_PROTECTION = Character.getInt("PlayerTeleportProtection", 0);
			RANDOM_RESPAWN_IN_TOWN_ENABLED = Character.getBoolean("RandomRespawnInTownEnabled", true);
			OFFSET_ON_TELEPORT_ENABLED = Character.getBoolean("OffsetOnTeleportEnabled", true);
			MAX_OFFSET_ON_TELEPORT = Character.getInt("MaxOffsetOnTeleport", 50);
			TELEPORT_WHILE_SIEGE_IN_PROGRESS = Character.getBoolean("TeleportWhileSiegeInProgress", true);
			PETITIONING_ALLOWED = Character.getBoolean("PetitioningAllowed", true);
			MAX_PETITIONS_PER_PLAYER = Character.getInt("MaxPetitionsPerPlayer", 5);
			MAX_PETITIONS_PENDING = Character.getInt("MaxPetitionsPending", 25);
			MAX_FREE_TELEPORT_LEVEL = Character.getInt("MaxFreeTeleportLevel", 0);
			MAX_NEWBIE_BUFF_LEVEL = Character.getInt("MaxNewbieBuffLevel", 0);
			DELETE_HOURS = Character.getInt("DeleteCharAfterHours", 24);
			PARTY_XP_CUTOFF_METHOD = Character.getString("PartyXpCutoffMethod", "level").toLowerCase();
			PARTY_XP_CUTOFF_PERCENT = Character.getDouble("PartyXpCutoffPercent", 3);
			PARTY_XP_CUTOFF_LEVEL = Character.getInt("PartyXpCutoffLevel", 20);
			final String[] gaps = Character.getString("PartyXpCutoffGaps", "0,9;10,14;15,99").split(";");
			PARTY_XP_CUTOFF_GAPS = new int[gaps.length][2];
			for (int i = 0; i < gaps.length; i++)
			{
				PARTY_XP_CUTOFF_GAPS[i] = new int[]
				{
					Integer.parseInt(gaps[i].split(",")[0]),
					Integer.parseInt(gaps[i].split(",")[1])
				};
			}
			final String[] percents = Character.getString("PartyXpCutoffGapPercent", "100;30;0").split(";");
			PARTY_XP_CUTOFF_GAP_PERCENTS = new int[percents.length];
			for (int i = 0; i < percents.length; i++)
			{
				PARTY_XP_CUTOFF_GAP_PERCENTS[i] = Integer.parseInt(percents[i]);
			}
			DISABLE_TUTORIAL = Character.getBoolean("DisableTutorial", false);
			STORE_RECIPE_SHOPLIST = Character.getBoolean("StoreRecipeShopList", false);
			STORE_UI_SETTINGS = Character.getBoolean("StoreCharUiSettings", true);
			FORBIDDEN_NAMES = Character.getString("ForbiddenNames", "").split(",");
			SILENCE_MODE_EXCLUDE = Character.getBoolean("SilenceModeExclude", false);
			PLAYER_MOVEMENT_BLOCK_TIME = Character.getInt("NpcTalkBlockingTime", 0) * 1000;
			ABILITY_MAX_POINTS = Character.getInt("AbilityMaxPoints", 16);
			ABILITY_POINTS_RESET_ADENA = Character.getLong("AbilityPointsResetAdena", 10_000_000);
			// Load Training Camp config file (if exists)
			final PropertiesParser trainingCampSettings = new PropertiesParser(TRAINING_CAMP_CONFIG_FILE);
			TRAINING_CAMP_ENABLE = trainingCampSettings.getBoolean("TrainingCampEnable", false);
			TRAINING_CAMP_PREMIUM_ONLY = trainingCampSettings.getBoolean("TrainingCampPremiumOnly", false);
			TRAINING_CAMP_MAX_DURATION = trainingCampSettings.getInt("TrainingCampDuration", 18000);
			TRAINING_CAMP_MIN_LEVEL = trainingCampSettings.getInt("TrainingCampMinLevel", 18);
			TRAINING_CAMP_MAX_LEVEL = trainingCampSettings.getInt("TrainingCampMaxLevel", 127);
			TRAINING_CAMP_EXP_MULTIPLIER = trainingCampSettings.getDouble("TrainingCampExpMultiplier", 1.0);
			TRAINING_CAMP_SP_MULTIPLIER = trainingCampSettings.getDouble("TrainingCampSpMultiplier", 1.0);
			// Load Discord config file (if exists)
			final PropertiesParser Discord = new PropertiesParser(DISCORD_CONFIG_FILE);
			DISCORD_ENABLED = Discord.getBoolean("DiscordEnabled", false);
			DISCORD_WEBHOOK_ENABLED = Discord.getBoolean("DiscordWebhookEnabled", false);
			DISCORD_BOT_TOKEN = Discord.getString("DiscordBotToken", "");
			DISCORD_GUILD_ID = Discord.getString("DiscordGuildId", "");
			DISCORD_EPIC_BOSS_CHANNEL = Discord.getString("DiscordEpicBossChannel", "");
			DISCORD_FACTION_WAR_WEBHOOK = Discord.getString("DiscordFactionWarWebhook", "");
			DISCORD_FACTION_NOTIFICATIONS_ENABLED = Discord.getBoolean("DiscordFactionNotificationsEnabled", false);
			DISCORD_FACTION_WAR_CHANNEL_ID = Discord.getString("DiscordFactionWarChannelId", "");
			DISCORD_FACTION_STATS_CHANNEL_ID = Discord.getString("DiscordFactionStatsChannelId", "");
			// Load Telegram config file (if exists)
			final PropertiesParser Telegram = new PropertiesParser(TELEGRAM_CONFIG_FILE);
			TELEGRAM_ENABLED = Telegram.getBoolean("TelegramEnabled", false);
			TELEGRAM_BOT_TOKEN = Telegram.getString("TelegramBotToken", "");
			TELEGRAM_BOT_USERNAME = Telegram.getString("TelegramBotUsername", "");
			TELEGRAM_ADMIN_CHAT_IDS = Telegram.getString("TelegramAdminChatIds", "");
			TELEGRAM_MAX_PRIME_POINTS_PER_COMMAND = Telegram.getInt("TelegramMaxPrimePointsPerCommand", 100000);
			TELEGRAM_COMMAND_COOLDOWN = Telegram.getLong("TelegramCommandCooldown", 5000);
			// Load Custom Drop Calc L2Properties file (if exists)
			final PropertiesParser DropCalcProperties = new PropertiesParser(CUSTOM_DROP_CALC_CONFIG_FILE);
			ENABLE_DROP_CALCULATOR = DropCalcProperties.getBoolean("EnableDropCalculator", false);
			ENABLE_TELEPORT_FUNCTION = DropCalcProperties.getBoolean("EnableTeleportFunction", false);
			ALLOW_TELEPORT_FROM_PEACE_ZONE_ONLY = DropCalcProperties.getBoolean("AllowTeleportFromPeaceOnly", false);
			ALLOW_FREE_TELEPORT = DropCalcProperties.getBoolean("AllowFreeTeleport", false);
			final String[] prices = DropCalcProperties.getString("TeleportPrice", "57,1000").split(",");
			TELEPORT_PRICE = new int[prices.length];
			for (int i = 0; i < prices.length; i++)
			{
				TELEPORT_PRICE[i] = Integer.parseInt(prices[i]);
			}
			String[] modIds = DropCalcProperties.getString("RestrictedMobIdsForTeleport", "25188;29163").trim().split(";");
			RESTRICTED_TELEPORT_IDS = new ArrayList<>(modIds.length);
			for (String modId : modIds)
			{
				try
				{
					RESTRICTED_TELEPORT_IDS.add(Integer.parseInt(modId));
				}
				catch (NumberFormatException nfe)
				{
					LOGGER.warning(Config.class.getSimpleName() + ": Wrong mob Id RestrictedMobIdsForTeleport passed: " + modId);
					LOGGER.warning(nfe.getMessage());
				}
			}
			String[] dropIds = DropCalcProperties.getString("RestrictedMobIdsInDropCalc", "25188;29163").trim().split(";");
			RESTRICTED_MOB_DROPLIST_IDS = new ArrayList<>(dropIds.length);
			for (String dropId : dropIds)
			{
				try
				{
					RESTRICTED_MOB_DROPLIST_IDS.add(Integer.parseInt(dropId));
				}
				catch (NumberFormatException nfe)
				{
					LOGGER.warning(Config.class.getSimpleName() + ": Wrong mob Id RestrictedMobIdsInDropCalc passed: " + dropId);
					LOGGER.warning(nfe.getMessage());
				}
			}
			// Load World Exchange config file (if exists)
			final PropertiesParser worldExchangeconfig = new PropertiesParser(WORLD_EXCHANGE_FILE);
			ENABLE_WORLD_EXCHANGE = worldExchangeconfig.getBoolean("EnableWorldExchange", true);
			WORLD_EXCHANGE_DEFAULT_LANG = worldExchangeconfig.getString("WorldExchangeDefaultLanguage", "en");
			WORLD_EXCHANGE_SAVE_INTERVAL = worldExchangeconfig.getLong("BidItemsIntervalStatusCheck", 30000);
			WORLD_EXCHANGE_LCOIN_TAX = worldExchangeconfig.getDouble("LCoinFee", 0.05);
			WORLD_EXCHANGE_MAX_LCOIN_TAX = worldExchangeconfig.getLong("MaxLCoinFee", 20000);
			WORLD_EXCHANGE_ADENA_FEE = worldExchangeconfig.getDouble("AdenaFee", 100.0);
			WORLD_EXCHANGE_MAX_ADENA_FEE = worldExchangeconfig.getLong("MaxAdenaFee", -1);
			WORLD_EXCHANGE_LAZY_UPDATE = worldExchangeconfig.getBoolean("DBLazy", false);
			WORLD_EXCHANGE_ITEM_SELL_PERIOD = worldExchangeconfig.getInt("ItemSellPeriod", 14);
			WORLD_EXCHANGE_ITEM_BACK_PERIOD = worldExchangeconfig.getInt("ItemBackPeriod", 120);
			WORLD_EXCHANGE_PAYMENT_TAKE_PERIOD = worldExchangeconfig.getInt("PaymentTakePeriod", 120);
			// Load General config file (if exists)
			final PropertiesParser General = new PropertiesParser(GENERAL_CONFIG_FILE);
			CACHE_TIME_PRIVATE_STORE = General.getInt("CacheTimePrivateStore", 5000); // 5 seconds
			DEFAULT_ACCESS_LEVEL = General.getInt("DefaultAccessLevel", 0);
			SERVER_GMONLY = General.getBoolean("ServerGMOnly", false);
			GM_HERO_AURA = General.getBoolean("GMHeroAura", false);
			GM_STARTUP_BUILDER_HIDE = General.getBoolean("GMStartupBuilderHide", false);
			GM_STARTUP_INVULNERABLE = General.getBoolean("GMStartupInvulnerable", false);
			GM_STARTUP_INVISIBLE = General.getBoolean("GMStartupInvisible", false);
			GM_STARTUP_SILENCE = General.getBoolean("GMStartupSilence", false);
			GM_STARTUP_AUTO_LIST = General.getBoolean("GMStartupAutoList", false);
			GM_STARTUP_DIET_MODE = General.getBoolean("GMStartupDietMode", false);
			GM_ITEM_RESTRICTION = General.getBoolean("GMItemRestriction", true);
			GM_SKILL_RESTRICTION = General.getBoolean("GMSkillRestriction", true);
			GM_TRADE_RESTRICTED_ITEMS = General.getBoolean("GMTradeRestrictedItems", false);
			GM_RESTART_FIGHTING = General.getBoolean("GMRestartFighting", true);
			GM_ANNOUNCER_NAME = General.getBoolean("GMShowAnnouncerName", false);
			GM_GIVE_SPECIAL_SKILLS = General.getBoolean("GMGiveSpecialSkills", false);
			GM_GIVE_SPECIAL_AURA_SKILLS = General.getBoolean("GMGiveSpecialAuraSkills", false);
			GM_DEBUG_HTML_PATHS = General.getBoolean("GMDebugHtmlPaths", true);
			USE_SUPER_HASTE_AS_GM_SPEED = General.getBoolean("UseSuperHasteAsGMSpeed", false);
			LOG_CHAT = General.getBoolean("LogChat", false);
			LOG_AUTO_ANNOUNCEMENTS = General.getBoolean("LogAutoAnnouncements", false);
			LOG_ITEMS = General.getBoolean("LogItems", false);
			LOG_ITEMS_SMALL_LOG = General.getBoolean("LogItemsSmallLog", false);
			LOG_ITEM_ENCHANTS = General.getBoolean("LogItemEnchants", false);
			LOG_SKILL_ENCHANTS = General.getBoolean("LogSkillEnchants", false);
			GMAUDIT = General.getBoolean("GMAudit", false);
			SKILL_CHECK_ENABLE = General.getBoolean("SkillCheckEnable", false);
			SKILL_CHECK_REMOVE = General.getBoolean("SkillCheckRemove", false);
			SKILL_CHECK_GM = General.getBoolean("SkillCheckGM", true);
			HTML_ACTION_CACHE_DEBUG = General.getBoolean("HtmlActionCacheDebug", false);
			DEVELOPER = General.getBoolean("Developer", false);
			ALT_DEV_NO_QUESTS = General.getBoolean("AltDevNoQuests", false) || Boolean.getBoolean("noquests");
			ALT_DEV_NO_SPAWNS = General.getBoolean("AltDevNoSpawns", false) || Boolean.getBoolean("nospawns");
			ALT_DEV_SHOW_QUESTS_LOAD_IN_LOGS = General.getBoolean("AltDevShowQuestsLoadInLogs", false);
			ALT_DEV_SHOW_SCRIPTS_LOAD_IN_LOGS = General.getBoolean("AltDevShowScriptsLoadInLogs", false);
			ALLOW_DISCARDITEM = General.getBoolean("AllowDiscardItem", true);
			AUTODESTROY_ITEM_AFTER = General.getInt("AutoDestroyDroppedItemAfter", 600);
			HERB_AUTO_DESTROY_TIME = General.getInt("AutoDestroyHerbTime", 60) * 1000;
			final String[] split = General.getString("ListOfProtectedItems", "0").split(",");
			LIST_PROTECTED_ITEMS = new ArrayList<>(split.length);
			for (String id : split)
			{
				LIST_PROTECTED_ITEMS.add(Integer.parseInt(id));
			}
			DATABASE_CLEAN_UP = General.getBoolean("DatabaseCleanUp", true);
			CHAR_DATA_STORE_INTERVAL = General.getInt("CharacterDataStoreInterval", 15) * 60 * 1000;
			CLAN_VARIABLES_STORE_INTERVAL = General.getInt("ClanVariablesStoreInterval", 15) * 60 * 1000;
			LAZY_ITEMS_UPDATE = General.getBoolean("LazyItemsUpdate", false);
			UPDATE_ITEMS_ON_CHAR_STORE = General.getBoolean("UpdateItemsOnCharStore", false);
			DESTROY_DROPPED_PLAYER_ITEM = General.getBoolean("DestroyPlayerDroppedItem", false);
			DESTROY_EQUIPABLE_PLAYER_ITEM = General.getBoolean("DestroyEquipableItem", false);
			DESTROY_ALL_ITEMS = General.getBoolean("DestroyAllItems", false);
			SAVE_DROPPED_ITEM = General.getBoolean("SaveDroppedItem", false);
			EMPTY_DROPPED_ITEM_TABLE_AFTER_LOAD = General.getBoolean("EmptyDroppedItemTableAfterLoad", false);
			SAVE_DROPPED_ITEM_INTERVAL = General.getInt("SaveDroppedItemInterval", 60) * 60000;
			CLEAR_DROPPED_ITEM_TABLE = General.getBoolean("ClearDroppedItemTable", false);
			ORDER_QUEST_LIST_BY_QUESTID = General.getBoolean("OrderQuestListByQuestId", true);
			AUTODELETE_INVALID_QUEST_DATA = General.getBoolean("AutoDeleteInvalidQuestData", false);
			ENABLE_STORY_QUEST_BUFF_REWARD = General.getBoolean("StoryQuestRewardBuff", true);
			MULTIPLE_ITEM_DROP = General.getBoolean("MultipleItemDrop", true);
			FORCE_INVENTORY_UPDATE = General.getBoolean("ForceInventoryUpdate", false);
			LAZY_CACHE = General.getBoolean("LazyCache", true);
			CHECK_HTML_ENCODING = General.getBoolean("CheckHtmlEncoding", true);
			CACHE_CHAR_NAMES = General.getBoolean("CacheCharNames", true);
			MIN_NPC_ANIMATION = General.getInt("MinNpcAnimation", 5);
			MAX_NPC_ANIMATION = General.getInt("MaxNpcAnimation", 60);
			MIN_MONSTER_ANIMATION = General.getInt("MinMonsterAnimation", 5);
			MAX_MONSTER_ANIMATION = General.getInt("MaxMonsterAnimation", 60);
			GRIDS_ALWAYS_ON = General.getBoolean("GridsAlwaysOn", false);
			GRID_NEIGHBOR_TURNON_TIME = General.getInt("GridNeighborTurnOnTime", 1);
			GRID_NEIGHBOR_TURNOFF_TIME = General.getInt("GridNeighborTurnOffTime", 90);
			ENABLE_FALLING_DAMAGE = General.getBoolean("EnableFallingDamage", true);
			PEACE_ZONE_MODE = General.getInt("PeaceZoneMode", 0);
			DEFAULT_GLOBAL_CHAT = General.getString("GlobalChat", "ON");
			DEFAULT_TRADE_CHAT = General.getString("TradeChat", "ON");
			ENABLE_WORLD_CHAT = General.getBoolean("WorldChatEnabled", false);
			MINIMUM_CHAT_LEVEL = General.getInt("MinimumChatLevel", 20);
			ALLOW_WAREHOUSE = General.getBoolean("AllowWarehouse", true);
			WAREHOUSE_CACHE = General.getBoolean("WarehouseCache", false);
			WAREHOUSE_CACHE_TIME = General.getInt("WarehouseCacheTime", 15);
			ALLOW_REFUND = General.getBoolean("AllowRefund", true);
			ALLOW_MAIL = General.getBoolean("AllowMail", true);
			ALLOW_ATTACHMENTS = General.getBoolean("AllowAttachments", true);
			ALLOW_WEAR = General.getBoolean("AllowWear", true);
			WEAR_DELAY = General.getInt("WearDelay", 5);
			WEAR_PRICE = General.getInt("WearPrice", 10);
			INSTANCE_FINISH_TIME = General.getInt("DefaultFinishTime", 5);
			RESTORE_PLAYER_INSTANCE = General.getBoolean("RestorePlayerInstance", false);
			EJECT_DEAD_PLAYER_TIME = General.getInt("EjectDeadPlayerTime", 1);
			ALLOW_RACE = General.getBoolean("AllowRace", true);
			ALLOW_WATER = General.getBoolean("AllowWater", true);
			ALLOW_FISHING = General.getBoolean("AllowFishing", true);
			ALLOW_MANOR = General.getBoolean("AllowManor", true);
			ALLOW_BOAT = General.getBoolean("AllowBoat", true);
			BOAT_BROADCAST_RADIUS = General.getInt("BoatBroadcastRadius", 20000);
			ALLOW_CURSED_WEAPONS = General.getBoolean("AllowCursedWeapons", true);
			SERVER_NEWS = General.getBoolean("ShowServerNews", false);
			ENABLE_COMMUNITY_BOARD = General.getBoolean("EnableCommunityBoard", true);
			BBS_DEFAULT = General.getString("BBSDefault", "_bbshome");
			USE_SAY_FILTER = General.getBoolean("UseChatFilter", false);
			CHAT_FILTER_CHARS = General.getString("ChatFilterChars", "^_^");
			final String[] propertySplit4 = General.getString("BanChatChannels", "GENERAL;SHOUT;WORLD;TRADE;HERO_VOICE").trim().split(";");
			BAN_CHAT_CHANNELS = new HashSet<>();
			try
			{
				for (String chatId : propertySplit4)
				{
					BAN_CHAT_CHANNELS.add(Enum.valueOf(ChatType.class, chatId));
				}
			}
			catch (NumberFormatException nfe)
			{
				LOGGER.log(Level.WARNING, "There was an error while parsing ban chat channels: ", nfe);
			}
			WORLD_CHAT_MIN_LEVEL = General.getInt("WorldChatMinLevel", 95);
			WORLD_CHAT_POINTS_PER_DAY = General.getInt("WorldChatPointsPerDay", 10);
			WORLD_CHAT_INTERVAL = General.getDuration("WorldChatInterval", "20secs", Duration.ofSeconds(20));
			ALT_MANOR_REFRESH_TIME = General.getInt("AltManorRefreshTime", 20);
			ALT_MANOR_REFRESH_MIN = General.getInt("AltManorRefreshMin", 0);
			ALT_MANOR_APPROVE_TIME = General.getInt("AltManorApproveTime", 4);
			ALT_MANOR_APPROVE_MIN = General.getInt("AltManorApproveMin", 30);
			ALT_MANOR_MAINTENANCE_MIN = General.getInt("AltManorMaintenanceMin", 6);
			ALT_MANOR_SAVE_ALL_ACTIONS = General.getBoolean("AltManorSaveAllActions", false);
			ALT_MANOR_SAVE_PERIOD_RATE = General.getInt("AltManorSavePeriodRate", 2);
			ALT_ITEM_AUCTION_ENABLED = General.getBoolean("AltItemAuctionEnabled", true);
			ALT_ITEM_AUCTION_EXPIRED_AFTER = General.getInt("AltItemAuctionExpiredAfter", 14);
			ALT_ITEM_AUCTION_TIME_EXTENDS_ON_BID = General.getInt("AltItemAuctionTimeExtendsOnBid", 0) * 1000;
			DEFAULT_PUNISH = IllegalActionPunishmentType.findByName(General.getString("DefaultPunish", "KICK"));
			DEFAULT_PUNISH_PARAM = General.getInt("DefaultPunishParam", 0);
			ONLY_GM_ITEMS_FREE = General.getBoolean("OnlyGMItemsFree", true);
			JAIL_IS_PVP = General.getBoolean("JailIsPvp", false);
			JAIL_DISABLE_CHAT = General.getBoolean("JailDisableChat", true);
			JAIL_DISABLE_TRANSACTION = General.getBoolean("JailDisableTransaction", false);
			CUSTOM_NPC_DATA = General.getBoolean("CustomNpcData", false);
			CUSTOM_TELEPORT_TABLE = General.getBoolean("CustomTeleportTable", false);
			CUSTOM_SKILLS_LOAD = General.getBoolean("CustomSkillsLoad", false);
			CUSTOM_ITEMS_LOAD = General.getBoolean("CustomItemsLoad", false);
			CUSTOM_MULTISELL_LOAD = General.getBoolean("CustomMultisellLoad", false);
			CUSTOM_BUYLIST_LOAD = General.getBoolean("CustomBuyListLoad", false);
			ALT_BIRTHDAY_GIFT = General.getInt("AltBirthdayGift", 22187);
			ALT_BIRTHDAY_MAIL_SUBJECT = General.getString("AltBirthdayMailSubject", "Happy Birthday!");
			ALT_BIRTHDAY_MAIL_TEXT = General.getString("AltBirthdayMailText", "Hello Adventurer!! Seeing as you're one year older now, I thought I would send you some birthday cheer :) Please find your birthday pack attached. May these gifts bring you joy and happiness on this very special day." + EOL + EOL + "Sincerely, Alegria");
			ENABLE_BLOCK_CHECKER_EVENT = General.getBoolean("EnableBlockCheckerEvent", false);
			MIN_BLOCK_CHECKER_TEAM_MEMBERS = General.getInt("BlockCheckerMinTeamMembers", 2);
			if (MIN_BLOCK_CHECKER_TEAM_MEMBERS < 1)
			{
				MIN_BLOCK_CHECKER_TEAM_MEMBERS = 1;
			}
			else if (MIN_BLOCK_CHECKER_TEAM_MEMBERS > 6)
			{
				MIN_BLOCK_CHECKER_TEAM_MEMBERS = 6;
			}
			HBCE_FAIR_PLAY = General.getBoolean("HBCEFairPlay", false);
			TOI_MAX_Z_ENABLED = General.getBoolean("TOIMaxZEnabled", false);
			TOI_MAX_Z = General.getInt("TOIMaxZ", 2000);
			STORE_HISTORY_LIMIT = General.getInt("StoreHistoryLimit", 30);
			// Load FloodProtector config file
			final PropertiesParser FloodProtectors = new PropertiesParser(FLOOD_PROTECTOR_CONFIG_FILE);
			loadFloodProtectorConfigs(FloodProtectors);
			// Load NPC config file (if exists)
			final PropertiesParser NPC = new PropertiesParser(NPC_CONFIG_FILE);
			ANNOUNCE_MAMMON_SPAWN = NPC.getBoolean("AnnounceMammonSpawn", false);
			ALT_MOB_AGRO_IN_PEACEZONE = NPC.getBoolean("AltMobAgroInPeaceZone", true);
			ALT_ATTACKABLE_NPCS = NPC.getBoolean("AltAttackableNpcs", true);
			ALT_GAME_VIEWNPC = NPC.getBoolean("AltGameViewNpc", false);
			SHOW_NPC_LEVEL = NPC.getBoolean("ShowNpcLevel", false);
			SHOW_NPC_AGGRESSION = NPC.getBoolean("ShowNpcAggression", false);
			SHOW_CREST_WITHOUT_QUEST = NPC.getBoolean("ShowCrestWithoutQuest", false);
			ENABLE_RANDOM_ENCHANT_EFFECT = NPC.getBoolean("EnableRandomEnchantEffect", false);
			MIN_NPC_LEVEL_DMG_PENALTY = NPC.getInt("MinNPCLevelForDmgPenalty", 78);
			NPC_DMG_PENALTY = parseConfigLine(NPC.getString("DmgPenaltyForLvLDifferences", "0.7, 0.6, 0.6, 0.55"));
			NPC_CRIT_DMG_PENALTY = parseConfigLine(NPC.getString("CritDmgPenaltyForLvLDifferences", "0.75, 0.65, 0.6, 0.58"));
			NPC_SKILL_DMG_PENALTY = parseConfigLine(NPC.getString("SkillDmgPenaltyForLvLDifferences", "0.8, 0.7, 0.65, 0.62"));
			MIN_NPC_LEVEL_MAGIC_PENALTY = NPC.getInt("MinNPCLevelForMagicPenalty", 78);
			NPC_SKILL_CHANCE_PENALTY = parseConfigLine(NPC.getString("SkillChancePenaltyForLvLDifferences", "2.5, 3.0, 3.25, 3.5"));
			DEFAULT_CORPSE_TIME = NPC.getInt("DefaultCorpseTime", 7);
			SPOILED_CORPSE_EXTEND_TIME = NPC.getInt("SpoiledCorpseExtendTime", 10);
			CORPSE_CONSUME_SKILL_ALLOWED_TIME_BEFORE_DECAY = NPC.getInt("CorpseConsumeSkillAllowedTimeBeforeDecay", 2000);
			MAX_DRIFT_RANGE = NPC.getInt("MaxDriftRange", 300);
			AGGRO_DISTANCE_CHECK_ENABLED = NPC.getBoolean("AggroDistanceCheckEnabled", true);
			AGGRO_DISTANCE_CHECK_RAIDS = NPC.getBoolean("AggroDistanceCheckRaids", false);
			AGGRO_DISTANCE_CHECK_INSTANCES = NPC.getBoolean("AggroDistanceCheckInstances", false);
			AGGRO_DISTANCE_CHECK_RESTORE_LIFE = NPC.getBoolean("AggroDistanceCheckRestoreLife", true);
			AGGRO_DISTANCE_CHECK_RAID_RESTORE_LIFE = NPC.getBoolean("AggroDistanceCheckRaidRestoreLife", false);
			GUARD_ATTACK_AGGRO_MOB = NPC.getBoolean("GuardAttackAggroMob", false);
			RAID_HP_REGEN_MULTIPLIER = NPC.getDouble("RaidHpRegenMultiplier", 100) / 100;
			RAID_MP_REGEN_MULTIPLIER = NPC.getDouble("RaidMpRegenMultiplier", 100) / 100;
			RAID_PDEFENCE_MULTIPLIER = NPC.getDouble("RaidPDefenceMultiplier", 100) / 100;
			RAID_MDEFENCE_MULTIPLIER = NPC.getDouble("RaidMDefenceMultiplier", 100) / 100;
			RAID_PATTACK_MULTIPLIER = NPC.getDouble("RaidPAttackMultiplier", 100) / 100;
			RAID_MATTACK_MULTIPLIER = NPC.getDouble("RaidMAttackMultiplier", 100) / 100;
			RAID_MIN_RESPAWN_MULTIPLIER = NPC.getFloat("RaidMinRespawnMultiplier", 1.0f);
			RAID_MAX_RESPAWN_MULTIPLIER = NPC.getFloat("RaidMaxRespawnMultiplier", 1.0f);
			RAID_MINION_RESPAWN_TIMER = NPC.getInt("RaidMinionRespawnTime", 300000);
			final String[] propertySplit = NPC.getString("CustomMinionsRespawnTime", "").split(";");
			MINIONS_RESPAWN_TIME = new HashMap<>(propertySplit.length);
			for (String prop : propertySplit)
			{
				final String[] propSplit = prop.split(",");
				if (propSplit.length != 2)
				{
					LOGGER.warning(StringUtil.concat("[CustomMinionsRespawnTime]: invalid config property -> CustomMinionsRespawnTime \"", prop, "\""));
				}
				try
				{
					MINIONS_RESPAWN_TIME.put(Integer.parseInt(propSplit[0]), Integer.parseInt(propSplit[1]));
				}
				catch (NumberFormatException nfe)
				{
					if (!prop.isEmpty())
					{
						LOGGER.warning(StringUtil.concat("[CustomMinionsRespawnTime]: invalid config property -> CustomMinionsRespawnTime \"", propSplit[0], "\"", propSplit[1]));
					}
				}
			}
			FORCE_DELETE_MINIONS = NPC.getBoolean("ForceDeleteMinions", false);
			RAID_DISABLE_CURSE = NPC.getBoolean("DisableRaidCurse", true);
			RAID_CHAOS_TIME = NPC.getInt("RaidChaosTime", 10);
			GRAND_CHAOS_TIME = NPC.getInt("GrandChaosTime", 10);
			MINION_CHAOS_TIME = NPC.getInt("MinionChaosTime", 10);
			INVENTORY_MAXIMUM_PET = NPC.getInt("MaximumSlotsForPet", 12);
			PET_HP_REGEN_MULTIPLIER = NPC.getDouble("PetHpRegenMultiplier", 100) / 100;
			PET_MP_REGEN_MULTIPLIER = NPC.getDouble("PetMpRegenMultiplier", 100) / 100;
			SAYHA_GRACE_CONSUME_BY_MOB = NPC.getInt("SayhaGraceConsumeByMob", 2250);
			SAYHA_GRACE_CONSUME_BY_BOSS = NPC.getInt("SayhaGraceConsumeByBoss", 1125);
			NPC_RANDOM_WALKING_ENABLED = NPC.getBoolean("NpcRandomWalkingEnabled", true);
			NPC_RANDOM_ANIMATION_ENABLED = NPC.getBoolean("NpcRandomAnimationEnabled", true);
			// Load Rates config file (if exists)
			final PropertiesParser RatesSettings = new PropertiesParser(RATES_CONFIG_FILE);
			EXP_EVENT_ENABLED = RatesSettings.getBoolean("ExpEventEnabled", false);
			RATE_XP = RatesSettings.getFloat("RateXp", 1);
			RATE_SP = RatesSettings.getFloat("RateSp", 1);
			RATE_PARTY_XP = RatesSettings.getFloat("RatePartyXp", 1);
			RATE_PARTY_SP = RatesSettings.getFloat("RatePartySp", 1);
			RATE_INSTANCE_XP = RatesSettings.getFloat("RateInstanceXp", -1);
			if (RATE_INSTANCE_XP < 0)
			{
				RATE_INSTANCE_XP = RATE_XP;
			}
			RATE_INSTANCE_SP = RatesSettings.getFloat("RateInstanceSp", -1);
			if (RATE_INSTANCE_SP < 0)
			{
				RATE_INSTANCE_SP = RATE_SP;
			}
			RATE_INSTANCE_PARTY_XP = RatesSettings.getFloat("RateInstancePartyXp", -1);
			if (RATE_INSTANCE_PARTY_XP < 0)
			{
				RATE_INSTANCE_PARTY_XP = RATE_PARTY_XP;
			}
			RATE_INSTANCE_PARTY_SP = RatesSettings.getFloat("RateInstancePartyXp", -1);
			if (RATE_INSTANCE_PARTY_SP < 0)
			{
				RATE_INSTANCE_PARTY_SP = RATE_PARTY_SP;
			}
			RATE_EXTRACTABLE = RatesSettings.getFloat("RateExtractable", 1);
			RATE_LCOIN_MIN = RatesSettings.getInt("RateLcoinMin", 2);
			RATE_LCOIN_MAX = RatesSettings.getInt("RateLcoinMax", 2);
			RATE_LCOIN_MIN_RMT_ZONES = RatesSettings.getInt("RateLcoinMinRmtZones", 2);
			RATE_LCOIN_MAX_RMT_ZONES = RatesSettings.getInt("RateLcoinMaxRmtZones", 2);
			RATE_LCOIN_MIN_HB = RatesSettings.getInt("RateLcoinMinHB", 2);
			RATE_LCOIN_MAX_HB = RatesSettings.getInt("RateLcoinMaxHB", 2);
			LCOIN_DROP_DELAY = RatesSettings.getInt("LCoinDropDelay", 20_000);
			EVENT_FIXED_DROP_ENABLED = RatesSettings.getBoolean("EventFixedDropEnabled", false);
			EVENT_FIXED_DROP_DELAY = RatesSettings.getInt("EventFixedDropDelay", 10800000);
			RATE_DROP_MANOR = RatesSettings.getInt("RateDropManor", 1);
			RATE_QUEST_DROP = RatesSettings.getFloat("RateQuestDrop", 1);
			RATE_QUEST_REWARD = RatesSettings.getFloat("RateQuestReward", 1);
			RATE_QUEST_REWARD_XP = RatesSettings.getFloat("RateQuestRewardXP", 1);
			RATE_QUEST_REWARD_SP = RatesSettings.getFloat("RateQuestRewardSP", 1);
			RATE_QUEST_REWARD_ADENA = RatesSettings.getFloat("RateQuestRewardAdena", 1);
			RATE_QUEST_REWARD_USE_MULTIPLIERS = RatesSettings.getBoolean("UseQuestRewardMultipliers", false);
			RATE_QUEST_REWARD_POTION = RatesSettings.getFloat("RateQuestRewardPotion", 1);
			RATE_QUEST_REWARD_SCROLL = RatesSettings.getFloat("RateQuestRewardScroll", 1);
			RATE_QUEST_REWARD_RECIPE = RatesSettings.getFloat("RateQuestRewardRecipe", 1);
			RATE_QUEST_REWARD_MATERIAL = RatesSettings.getFloat("RateQuestRewardMaterial", 1);
			RATE_RAIDBOSS_POINTS = RatesSettings.getFloat("RateRaidbossPointsReward", 1);
			RATE_SAYHA_GRACE_EXP_MULTIPLIER = RatesSettings.getFloat("RateSayhaGraceExpMultiplier", 3);
			RATE_LIMITED_SAYHA_GRACE_EXP_MULTIPLIER = RatesSettings.getFloat("RateLimitedSayhaGraceExpMultiplier", 2);
			SAYHA_GRACE_MAX_ITEMS_ALLOWED = RatesSettings.getInt("SayhaGraceMaxItemsAllowed", 0);
			if (SAYHA_GRACE_MAX_ITEMS_ALLOWED == 0)
			{
				SAYHA_GRACE_MAX_ITEMS_ALLOWED = Integer.MAX_VALUE;
			}
			RATE_SAYHA_GRACE_LOST = RatesSettings.getFloat("RateSayhaGraceLost", 1);
			RATE_SAYHA_GRACE_LOST_KAMAEL_TRANSFORM = RatesSettings.getFloat("RateSayhaGraceLostKamaelTransform", 1);
			RATE_SAYHA_GRACE_GAIN = RatesSettings.getFloat("RateSayhaGraceGain", 1);
			RATE_KARMA_LOST = RatesSettings.getFloat("RateKarmaLost", -1);
			if (RATE_KARMA_LOST == -1)
			{
				RATE_KARMA_LOST = RATE_XP;
			}
			RATE_KARMA_EXP_LOST = RatesSettings.getFloat("RateKarmaExpLost", 1);
			RATE_SIEGE_GUARDS_PRICE = RatesSettings.getFloat("RateSiegeGuardsPrice", 1);
			PLAYER_DROP_LIMIT = RatesSettings.getInt("PlayerDropLimit", 1);
			PLAYER_RATE_DROP = RatesSettings.getInt("PlayerRateDrop", 5);
			PLAYER_RATE_DROP_ITEM = RatesSettings.getInt("PlayerRateDropItem", 70);
			PLAYER_RATE_DROP_EQUIP = RatesSettings.getInt("PlayerRateDropEquip", 25);
			PLAYER_RATE_DROP_EQUIP_WEAPON = RatesSettings.getInt("PlayerRateDropEquipWeapon", 5);
			PET_XP_RATE = RatesSettings.getFloat("PetXpRate", 1);
			PET_FOOD_RATE = RatesSettings.getInt("PetFoodRate", 1);
			SINEATER_XP_RATE = RatesSettings.getFloat("SinEaterXpRate", 1);
			KARMA_DROP_LIMIT = RatesSettings.getInt("KarmaDropLimit", 10);
			KARMA_RATE_DROP = RatesSettings.getInt("KarmaRateDrop", 70);
			KARMA_RATE_DROP_ITEM = RatesSettings.getInt("KarmaRateDropItem", 50);
			KARMA_RATE_DROP_EQUIP = RatesSettings.getInt("KarmaRateDropEquip", 40);
			KARMA_RATE_DROP_EQUIP_WEAPON = RatesSettings.getInt("KarmaRateDropEquipWeapon", 10);
			RATE_DEATH_DROP_AMOUNT_MULTIPLIER = RatesSettings.getFloat("DeathDropAmountMultiplier", 1);
			RATE_SPOIL_DROP_AMOUNT_MULTIPLIER = RatesSettings.getFloat("SpoilDropAmountMultiplier", 1);
			RATE_HERB_DROP_AMOUNT_MULTIPLIER = RatesSettings.getFloat("HerbDropAmountMultiplier", 1);
			RATE_RAID_DROP_AMOUNT_MULTIPLIER = RatesSettings.getFloat("RaidDropAmountMultiplier", 1);
			RATE_DEATH_DROP_CHANCE_MULTIPLIER = RatesSettings.getFloat("DeathDropChanceMultiplier", 1);
			RATE_SPOIL_DROP_CHANCE_MULTIPLIER = RatesSettings.getFloat("SpoilDropChanceMultiplier", 1);
			RATE_HERB_DROP_CHANCE_MULTIPLIER = RatesSettings.getFloat("HerbDropChanceMultiplier", 1);
			RATE_RAID_DROP_CHANCE_MULTIPLIER = RatesSettings.getFloat("RaidDropChanceMultiplier", 1);
			final String[] dropAmountMultiplier = RatesSettings.getString("DropAmountMultiplierByItemId", "").split(";");
			RATE_DROP_AMOUNT_BY_ID = new HashMap<>(dropAmountMultiplier.length);
			if (!dropAmountMultiplier[0].isEmpty())
			{
				for (String item : dropAmountMultiplier)
				{
					final String[] itemSplit = item.split(",");
					if (itemSplit.length != 2)
					{
						LOGGER.warning(StringUtil.concat("Config.load(): invalid config property -> RateDropItemsById \"", item, "\""));
					}
					else
					{
						try
						{
							RATE_DROP_AMOUNT_BY_ID.put(Integer.parseInt(itemSplit[0]), Float.parseFloat(itemSplit[1]));
						}
						catch (NumberFormatException nfe)
						{
							if (!item.isEmpty())
							{
								LOGGER.warning(StringUtil.concat("Config.load(): invalid config property -> RateDropItemsById \"", item, "\""));
							}
						}
					}
				}
			}
			final String[] dropChanceMultiplier = RatesSettings.getString("DropChanceMultiplierByItemId", "").split(";");
			RATE_DROP_CHANCE_BY_ID = new HashMap<>(dropChanceMultiplier.length);
			if (!dropChanceMultiplier[0].isEmpty())
			{
				for (String item : dropChanceMultiplier)
				{
					final String[] itemSplit = item.split(",");
					if (itemSplit.length != 2)
					{
						LOGGER.warning(StringUtil.concat("Config.load(): invalid config property -> RateDropItemsById \"", item, "\""));
					}
					else
					{
						try
						{
							RATE_DROP_CHANCE_BY_ID.put(Integer.parseInt(itemSplit[0]), Float.parseFloat(itemSplit[1]));
						}
						catch (NumberFormatException nfe)
						{
							if (!item.isEmpty())
							{
								LOGGER.warning(StringUtil.concat("Config.load(): invalid config property -> RateDropItemsById \"", item, "\""));
							}
						}
					}
				}
			}
			DROP_MAX_OCCURRENCES_NORMAL = RatesSettings.getInt("DropMaxOccurrencesNormal", 2);
			DROP_MAX_OCCURRENCES_RAIDBOSS = RatesSettings.getInt("DropMaxOccurrencesRaidboss", 7);
			DROP_ADENA_MIN_LEVEL_DIFFERENCE = RatesSettings.getInt("DropAdenaMinLevelDifference", 8);
			DROP_ADENA_MAX_LEVEL_DIFFERENCE = RatesSettings.getInt("DropAdenaMaxLevelDifference", 15);
			DROP_ADENA_MIN_LEVEL_GAP_CHANCE = RatesSettings.getDouble("DropAdenaMinLevelGapChance", 10);
			DROP_ITEM_MIN_LEVEL_DIFFERENCE = RatesSettings.getInt("DropItemMinLevelDifference", 5);
			DROP_ITEM_MAX_LEVEL_DIFFERENCE = RatesSettings.getInt("DropItemMaxLevelDifference", 10);
			DROP_ITEM_MIN_LEVEL_GAP_CHANCE = RatesSettings.getDouble("DropItemMinLevelGapChance", 10);
			// Load PvP config file (if exists)
			final PropertiesParser PVPSettings = new PropertiesParser(PVP_CONFIG_FILE);
			KARMA_DROP_GM = PVPSettings.getBoolean("CanGMDropEquipment", false);
			KARMA_PK_LIMIT = PVPSettings.getInt("MinimumPKRequiredToDrop", 4);
			KARMA_NONDROPPABLE_PET_ITEMS = PVPSettings.getString("ListOfPetItems", "2375,3500,3501,3502,4422,4423,4424,4425,6648,6649,6650,9882");
			KARMA_NONDROPPABLE_ITEMS = PVPSettings.getString("ListOfNonDroppableItems", "57,1147,425,1146,461,10,2368,7,6,2370,2369,6842,6611,6612,6613,6614,6615,6616,6617,6618,6619,6620,6621,7694,8181,5575,7694,9388,9389,9390");
			String[] karma = KARMA_NONDROPPABLE_PET_ITEMS.split(",");
			KARMA_LIST_NONDROPPABLE_PET_ITEMS = new int[karma.length];
			for (int i = 0; i < karma.length; i++)
			{
				KARMA_LIST_NONDROPPABLE_PET_ITEMS[i] = Integer.parseInt(karma[i]);
			}
			Arrays.sort(KARMA_LIST_NONDROPPABLE_PET_ITEMS);
			karma = KARMA_NONDROPPABLE_ITEMS.split(",");
			KARMA_LIST_NONDROPPABLE_ITEMS = new int[karma.length];
			for (int i = 0; i < karma.length; i++)
			{
				KARMA_LIST_NONDROPPABLE_ITEMS[i] = Integer.parseInt(karma[i]);
			}
			Arrays.sort(KARMA_LIST_NONDROPPABLE_ITEMS);
			ANTIFEED_ENABLE = PVPSettings.getBoolean("AntiFeedEnable", false);
			ANTIFEED_DUALBOX = PVPSettings.getBoolean("AntiFeedDualbox", true);
			ANTIFEED_DISCONNECTED_AS_DUALBOX = PVPSettings.getBoolean("AntiFeedDisconnectedAsDualbox", true);
			ANTIFEED_INTERVAL = PVPSettings.getInt("AntiFeedInterval", 120) * 1000;
			VAMPIRIC_ATTACK_AFFECTS_PVP = PVPSettings.getBoolean("VampiricAttackAffectsPvP", false);
			MP_VAMPIRIC_ATTACK_AFFECTS_PVP = PVPSettings.getBoolean("MpVampiricAttackAffectsPvP", false);
			PVP_NORMAL_TIME = PVPSettings.getInt("PvPVsNormalTime", 120000);
			PVP_PVP_TIME = PVPSettings.getInt("PvPVsPvPTime", 60000);
			MAX_REPUTATION = PVPSettings.getInt("MaxReputation", 500);
			REPUTATION_INCREASE = PVPSettings.getInt("ReputationIncrease", 100);
			ANNOUNCE_GAINAK_SIEGE = PVPSettings.getBoolean("AnnounceGainakSiege", false);
			// Load Olympiad config file (if exists)
			loadOlympiadConfigs(OLYMPIAD_CONFIG_FILE);
			final File hexIdFile = new File(HEXID_FILE);
			if (hexIdFile.exists())
			{
				final PropertiesParser hexId = new PropertiesParser(hexIdFile);
				if (hexId.containskey("ServerID") && hexId.containskey("HexID"))
				{
					SERVER_ID = hexId.getInt("ServerID", 1);
					try
					{
						HEX_ID = new BigInteger(hexId.getString("HexID", null), 16).toByteArray();
					}
					catch (Exception e)
					{
						LOGGER.warning("Could not load HexID file (" + HEXID_FILE + "). Hopefully login will give us one.");
					}
				}
			}
			if (HEX_ID == null)
			{
				LOGGER.warning("Could not load HexID file (" + HEXID_FILE + "). Hopefully login will give us one.");
			}
			try
			{
				// @formatter:off
				FILTER_LIST = Files.lines(Paths.get(CHAT_FILTER_FILE), StandardCharsets.UTF_8).map(String::trim).filter(line -> (!line.isEmpty() && (line.charAt(0) != '#'))).collect(Collectors.toList());
				// @formatter:on
				LOGGER.info("Loaded " + FILTER_LIST.size() + " Filter Words.");
			}
			catch (IOException e)
			{
				LOGGER.log(Level.WARNING, "Error while loading chat filter words!", e);
			}
			// Load GeoEngine config file (if exists)
			final PropertiesParser GeoEngine = new PropertiesParser(GEOENGINE_CONFIG_FILE);
			GEODATA_PATH = GeoEngine.getString("GeoDataPath", "./data/geodata/");
			COORD_SYNCHRONIZE = GeoEngine.getInt("CoordSynchronize", -1);
			PART_OF_CHARACTER_HEIGHT = GeoEngine.getInt("PartOfCharacterHeight", 75);
			MAX_OBSTACLE_HEIGHT = GeoEngine.getInt("MaxObstacleHeight", 32);
			PATHFINDING = GeoEngine.getBoolean("PathFinding", true);
			PATHFIND_BUFFERS = GeoEngine.getString("PathFindBuffers", "100x6;128x6;192x6;256x4;320x4;384x4;500x2");
			BASE_WEIGHT = GeoEngine.getInt("BaseWeight", 10);
			DIAGONAL_WEIGHT = GeoEngine.getInt("DiagonalWeight", 14);
			OBSTACLE_MULTIPLIER = GeoEngine.getInt("ObstacleMultiplier", 10);
			HEURISTIC_WEIGHT = GeoEngine.getInt("HeuristicWeight", 20);
			MAX_ITERATIONS = GeoEngine.getInt("MaxIterations", 3500);
			CORRECT_PLAYER_Z = GeoEngine.getBoolean("CorrectPlayerZ", false);
			// Load AllowedPlayerRaces config file (if exists)
			final PropertiesParser AllowedPlayerRaces = new PropertiesParser(CUSTOM_ALLOWED_PLAYER_RACES_CONFIG_FILE);
			ALLOW_HUMAN = AllowedPlayerRaces.getBoolean("AllowHuman", true);
			ALLOW_ELF = AllowedPlayerRaces.getBoolean("AllowElf", true);
			ALLOW_DARKELF = AllowedPlayerRaces.getBoolean("AllowDarkElf", true);
			ALLOW_ORC = AllowedPlayerRaces.getBoolean("AllowOrc", true);
			ALLOW_DWARF = AllowedPlayerRaces.getBoolean("AllowDwarf", true);
			ALLOW_KAMAEL = AllowedPlayerRaces.getBoolean("AllowKamael", true);
			ALLOW_ERTHEIA = AllowedPlayerRaces.getBoolean("AllowErtheia", true);
			// Load NpcStatMultipliers config file (if exists)
			final PropertiesParser BoostNpcStats = new PropertiesParser(CUSTOM_NPC_STAT_MULTIPLIERS_CONFIG_FILE);
			ENABLE_NPC_STAT_MULTIPLIERS = BoostNpcStats.getBoolean("EnableNpcStatMultipliers", false);
			MONSTER_HP_MULTIPLIER = BoostNpcStats.getDouble("MonsterHP", 1.0);
			MONSTER_MP_MULTIPLIER = BoostNpcStats.getDouble("MonsterMP", 1.0);
			MONSTER_PATK_MULTIPLIER = BoostNpcStats.getDouble("MonsterPAtk", 1.0);
			MONSTER_MATK_MULTIPLIER = BoostNpcStats.getDouble("MonsterMAtk", 1.0);
			MONSTER_PDEF_MULTIPLIER = BoostNpcStats.getDouble("MonsterPDef", 1.0);
			MONSTER_MDEF_MULTIPLIER = BoostNpcStats.getDouble("MonsterMDef", 1.0);
			MONSTER_AGRRO_RANGE_MULTIPLIER = BoostNpcStats.getDouble("MonsterAggroRange", 1.0);
			MONSTER_CLAN_HELP_RANGE_MULTIPLIER = BoostNpcStats.getDouble("MonsterClanHelpRange", 1.0);
			RAIDBOSS_HP_MULTIPLIER = BoostNpcStats.getDouble("RaidbossHP", 1.0);
			RAIDBOSS_MP_MULTIPLIER = BoostNpcStats.getDouble("RaidbossMP", 1.0);
			RAIDBOSS_PATK_MULTIPLIER = BoostNpcStats.getDouble("RaidbossPAtk", 1.0);
			RAIDBOSS_MATK_MULTIPLIER = BoostNpcStats.getDouble("RaidbossMAtk", 1.0);
			RAIDBOSS_PDEF_MULTIPLIER = BoostNpcStats.getDouble("RaidbossPDef", 1.0);
			RAIDBOSS_MDEF_MULTIPLIER = BoostNpcStats.getDouble("RaidbossMDef", 1.0);
			RAIDBOSS_AGRRO_RANGE_MULTIPLIER = BoostNpcStats.getDouble("RaidbossAggroRange", 1.0);
			RAIDBOSS_CLAN_HELP_RANGE_MULTIPLIER = BoostNpcStats.getDouble("RaidbossClanHelpRange", 1.0);
			GUARD_HP_MULTIPLIER = BoostNpcStats.getDouble("GuardHP", 1.0);
			GUARD_MP_MULTIPLIER = BoostNpcStats.getDouble("GuardMP", 1.0);
			GUARD_PATK_MULTIPLIER = BoostNpcStats.getDouble("GuardPAtk", 1.0);
			GUARD_MATK_MULTIPLIER = BoostNpcStats.getDouble("GuardMAtk", 1.0);
			GUARD_PDEF_MULTIPLIER = BoostNpcStats.getDouble("GuardPDef", 1.0);
			GUARD_MDEF_MULTIPLIER = BoostNpcStats.getDouble("GuardMDef", 1.0);
			GUARD_AGRRO_RANGE_MULTIPLIER = BoostNpcStats.getDouble("GuardAggroRange", 1.0);
			GUARD_CLAN_HELP_RANGE_MULTIPLIER = BoostNpcStats.getDouble("GuardClanHelpRange", 1.0);
			DEFENDER_HP_MULTIPLIER = BoostNpcStats.getDouble("DefenderHP", 1.0);
			DEFENDER_MP_MULTIPLIER = BoostNpcStats.getDouble("DefenderMP", 1.0);
			DEFENDER_PATK_MULTIPLIER = BoostNpcStats.getDouble("DefenderPAtk", 1.0);
			DEFENDER_MATK_MULTIPLIER = BoostNpcStats.getDouble("DefenderMAtk", 1.0);
			DEFENDER_PDEF_MULTIPLIER = BoostNpcStats.getDouble("DefenderPDef", 1.0);
			DEFENDER_MDEF_MULTIPLIER = BoostNpcStats.getDouble("DefenderMDef", 1.0);
			DEFENDER_AGRRO_RANGE_MULTIPLIER = BoostNpcStats.getDouble("DefenderAggroRange", 1.0);
			DEFENDER_CLAN_HELP_RANGE_MULTIPLIER = BoostNpcStats.getDouble("DefenderClanHelpRange", 1.0);
			// Load ChampionMonster config file (if exists)
			final PropertiesParser ChampionMonster = new PropertiesParser(CUSTOM_CHAMPION_MONSTERS_CONFIG_FILE);
			CHAMPION_ENABLE = ChampionMonster.getBoolean("ChampionEnable", false);
			CHAMPION_PASSIVE = ChampionMonster.getBoolean("ChampionPassive", false);
			CHAMPION_FREQUENCY = ChampionMonster.getInt("ChampionFrequency", 0);
			CHAMP_TITLE = ChampionMonster.getString("ChampionTitle", "Champion");
			SHOW_CHAMPION_AURA = ChampionMonster.getBoolean("ChampionAura", true);
			CHAMP_MIN_LEVEL = ChampionMonster.getInt("ChampionMinLevel", 20);
			CHAMP_MAX_LEVEL = ChampionMonster.getInt("ChampionMaxLevel", 60);
			CHAMPION_HP = ChampionMonster.getInt("ChampionHp", 7);
			CHAMPION_HP_REGEN = ChampionMonster.getFloat("ChampionHpRegen", 1);
			CHAMPION_REWARDS_EXP_SP = ChampionMonster.getFloat("ChampionRewardsExpSp", 8);
			CHAMPION_REWARDS_CHANCE = ChampionMonster.getFloat("ChampionRewardsChance", 8);
			CHAMPION_REWARDS_AMOUNT = ChampionMonster.getFloat("ChampionRewardsAmount", 1);
			CHAMPION_ADENAS_REWARDS_CHANCE = ChampionMonster.getFloat("ChampionAdenasRewardsChance", 1);
			CHAMPION_ADENAS_REWARDS_AMOUNT = ChampionMonster.getFloat("ChampionAdenasRewardsAmount", 1);
			CHAMPION_ATK = ChampionMonster.getFloat("ChampionAtk", 1);
			CHAMPION_SPD_ATK = ChampionMonster.getFloat("ChampionSpdAtk", 1);
			CHAMPION_REWARD_LOWER_LEVEL_ITEM_CHANCE = ChampionMonster.getInt("ChampionRewardLowerLvlItemChance", 0);
			CHAMPION_REWARD_HIGHER_LEVEL_ITEM_CHANCE = ChampionMonster.getInt("ChampionRewardHigherLvlItemChance", 0);
			CHAMPION_REWARD_ID = ChampionMonster.getInt("ChampionRewardItemID", 6393);
			CHAMPION_REWARD_QTY = ChampionMonster.getInt("ChampionRewardItemQty", 1);
			CHAMPION_ENABLE_SAYHA_GRACE = ChampionMonster.getBoolean("ChampionEnableSayhaGrace", false);
			CHAMPION_ENABLE_IN_INSTANCES = ChampionMonster.getBoolean("ChampionEnableInInstances", false);
			// Load ClassBalance config file (if exists)
			final PropertiesParser ClassBalance = new PropertiesParser(CUSTOM_CLASS_BALANCE_CONFIG_FILE);
			Arrays.fill(PVE_MAGICAL_SKILL_DAMAGE_MULTIPLIERS, 1f);
			final String[] pveMagicalSkillDamageMultipliers = ClassBalance.getString("PveMagicalSkillDamageMultipliers", "").trim().split(";");
			if (pveMagicalSkillDamageMultipliers.length > 0)
			{
				for (String info : pveMagicalSkillDamageMultipliers)
				{
					final String[] classInfo = info.trim().split("[*]");
					if (classInfo.length == 2)
					{
						final String id = classInfo[0].trim();
						PVE_MAGICAL_SKILL_DAMAGE_MULTIPLIERS[Util.isDigit(id) ? Integer.parseInt(id) : Enum.valueOf(ClassId.class, id).getId()] = Float.parseFloat(classInfo[1].trim());
					}
				}
			}
			Arrays.fill(PVP_MAGICAL_SKILL_DAMAGE_MULTIPLIERS, 1f);
			final String[] pvpMagicalSkillDamageMultipliers = ClassBalance.getString("PvpMagicalSkillDamageMultipliers", "").trim().split(";");
			if (pvpMagicalSkillDamageMultipliers.length > 0)
			{
				for (String info : pvpMagicalSkillDamageMultipliers)
				{
					final String[] classInfo = info.trim().split("[*]");
					if (classInfo.length == 2)
					{
						final String id = classInfo[0].trim();
						PVP_MAGICAL_SKILL_DAMAGE_MULTIPLIERS[Util.isDigit(id) ? Integer.parseInt(id) : Enum.valueOf(ClassId.class, id).getId()] = Float.parseFloat(classInfo[1].trim());
					}
				}
			}
			Arrays.fill(PVE_MAGICAL_SKILL_DEFENCE_MULTIPLIERS, 1f);
			final String[] pveMagicalSkillDefenceMultipliers = ClassBalance.getString("PveMagicalSkillDefenceMultipliers", "").trim().split(";");
			if (pveMagicalSkillDefenceMultipliers.length > 0)
			{
				for (String info : pveMagicalSkillDefenceMultipliers)
				{
					final String[] classInfo = info.trim().split("[*]");
					if (classInfo.length == 2)
					{
						final String id = classInfo[0].trim();
						PVE_MAGICAL_SKILL_DEFENCE_MULTIPLIERS[Util.isDigit(id) ? Integer.parseInt(id) : Enum.valueOf(ClassId.class, id).getId()] = Float.parseFloat(classInfo[1].trim());
					}
				}
			}
			Arrays.fill(PVP_MAGICAL_SKILL_DEFENCE_MULTIPLIERS, 1f);
			final String[] pvpMagicalSkillDefenceMultipliers = ClassBalance.getString("PvpMagicalSkillDefenceMultipliers", "").trim().split(";");
			if (pvpMagicalSkillDefenceMultipliers.length > 0)
			{
				for (String info : pvpMagicalSkillDefenceMultipliers)
				{
					final String[] classInfo = info.trim().split("[*]");
					if (classInfo.length == 2)
					{
						final String id = classInfo[0].trim();
						PVP_MAGICAL_SKILL_DEFENCE_MULTIPLIERS[Util.isDigit(id) ? Integer.parseInt(id) : Enum.valueOf(ClassId.class, id).getId()] = Float.parseFloat(classInfo[1].trim());
					}
				}
			}
			Arrays.fill(PVE_MAGICAL_SKILL_CRITICAL_CHANCE_MULTIPLIERS, 1f);
			final String[] pveMagicalSkillCriticalChanceMultipliers = ClassBalance.getString("PveMagicalSkillCriticalChanceMultipliers", "").trim().split(";");
			if (pveMagicalSkillCriticalChanceMultipliers.length > 0)
			{
				for (String info : pveMagicalSkillCriticalChanceMultipliers)
				{
					final String[] classInfo = info.trim().split("[*]");
					if (classInfo.length == 2)
					{
						final String id = classInfo[0].trim();
						PVE_MAGICAL_SKILL_CRITICAL_CHANCE_MULTIPLIERS[Util.isDigit(id) ? Integer.parseInt(id) : Enum.valueOf(ClassId.class, id).getId()] = Float.parseFloat(classInfo[1].trim());
					}
				}
			}
			Arrays.fill(PVP_MAGICAL_SKILL_CRITICAL_CHANCE_MULTIPLIERS, 1f);
			final String[] pvpMagicalSkillCriticalChanceMultipliers = ClassBalance.getString("PvpMagicalSkillCriticalChanceMultipliers", "").trim().split(";");
			if (pvpMagicalSkillCriticalChanceMultipliers.length > 0)
			{
				for (String info : pvpMagicalSkillCriticalChanceMultipliers)
				{
					final String[] classInfo = info.trim().split("[*]");
					if (classInfo.length == 2)
					{
						final String id = classInfo[0].trim();
						PVP_MAGICAL_SKILL_CRITICAL_CHANCE_MULTIPLIERS[Util.isDigit(id) ? Integer.parseInt(id) : Enum.valueOf(ClassId.class, id).getId()] = Float.parseFloat(classInfo[1].trim());
					}
				}
			}
			Arrays.fill(PVE_MAGICAL_SKILL_CRITICAL_DAMAGE_MULTIPLIERS, 1f);
			final String[] pveMagicalSkillCriticalDamageMultipliers = ClassBalance.getString("PveMagicalSkillCriticalDamageMultipliers", "").trim().split(";");
			if (pveMagicalSkillCriticalDamageMultipliers.length > 0)
			{
				for (String info : pveMagicalSkillCriticalDamageMultipliers)
				{
					final String[] classInfo = info.trim().split("[*]");
					if (classInfo.length == 2)
					{
						final String id = classInfo[0].trim();
						PVE_MAGICAL_SKILL_CRITICAL_DAMAGE_MULTIPLIERS[Util.isDigit(id) ? Integer.parseInt(id) : Enum.valueOf(ClassId.class, id).getId()] = Float.parseFloat(classInfo[1].trim());
					}
				}
			}
			Arrays.fill(PVP_MAGICAL_SKILL_CRITICAL_DAMAGE_MULTIPLIERS, 1f);
			final String[] pvpMagicalSkillCriticalDamageMultipliers = ClassBalance.getString("PvpMagicalSkillCriticalDamageMultipliers", "").trim().split(";");
			if (pvpMagicalSkillCriticalDamageMultipliers.length > 0)
			{
				for (String info : pvpMagicalSkillCriticalDamageMultipliers)
				{
					final String[] classInfo = info.trim().split("[*]");
					if (classInfo.length == 2)
					{
						final String id = classInfo[0].trim();
						PVP_MAGICAL_SKILL_CRITICAL_DAMAGE_MULTIPLIERS[Util.isDigit(id) ? Integer.parseInt(id) : Enum.valueOf(ClassId.class, id).getId()] = Float.parseFloat(classInfo[1].trim());
					}
				}
			}
			Arrays.fill(PVE_PHYSICAL_SKILL_DAMAGE_MULTIPLIERS, 1f);
			final String[] pvePhysicalSkillDamageMultipliers = ClassBalance.getString("PvePhysicalSkillDamageMultipliers", "").trim().split(";");
			if (pvePhysicalSkillDamageMultipliers.length > 0)
			{
				for (String info : pvePhysicalSkillDamageMultipliers)
				{
					final String[] classInfo = info.trim().split("[*]");
					if (classInfo.length == 2)
					{
						final String id = classInfo[0].trim();
						PVE_PHYSICAL_SKILL_DAMAGE_MULTIPLIERS[Util.isDigit(id) ? Integer.parseInt(id) : Enum.valueOf(ClassId.class, id).getId()] = Float.parseFloat(classInfo[1].trim());
					}
				}
			}
			Arrays.fill(PVP_PHYSICAL_SKILL_DAMAGE_MULTIPLIERS, 1f);
			final String[] pvpPhysicalSkillDamageMultipliers = ClassBalance.getString("PvpPhysicalSkillDamageMultipliers", "").trim().split(";");
			if (pvpPhysicalSkillDamageMultipliers.length > 0)
			{
				for (String info : pvpPhysicalSkillDamageMultipliers)
				{
					final String[] classInfo = info.trim().split("[*]");
					if (classInfo.length == 2)
					{
						final String id = classInfo[0].trim();
						PVP_PHYSICAL_SKILL_DAMAGE_MULTIPLIERS[Util.isDigit(id) ? Integer.parseInt(id) : Enum.valueOf(ClassId.class, id).getId()] = Float.parseFloat(classInfo[1].trim());
					}
				}
			}
			Arrays.fill(PVE_PHYSICAL_SKILL_DEFENCE_MULTIPLIERS, 1f);
			final String[] pvePhysicalSkillDefenceMultipliers = ClassBalance.getString("PvePhysicalSkillDefenceMultipliers", "").trim().split(";");
			if (pvePhysicalSkillDefenceMultipliers.length > 0)
			{
				for (String info : pvePhysicalSkillDefenceMultipliers)
				{
					final String[] classInfo = info.trim().split("[*]");
					if (classInfo.length == 2)
					{
						final String id = classInfo[0].trim();
						PVE_PHYSICAL_SKILL_DEFENCE_MULTIPLIERS[Util.isDigit(id) ? Integer.parseInt(id) : Enum.valueOf(ClassId.class, id).getId()] = Float.parseFloat(classInfo[1].trim());
					}
				}
			}
			Arrays.fill(PVP_PHYSICAL_SKILL_DEFENCE_MULTIPLIERS, 1f);
			final String[] pvpPhysicalSkillDefenceMultipliers = ClassBalance.getString("PvpPhysicalSkillDefenceMultipliers", "").trim().split(";");
			if (pvpPhysicalSkillDefenceMultipliers.length > 0)
			{
				for (String info : pvpPhysicalSkillDefenceMultipliers)
				{
					final String[] classInfo = info.trim().split("[*]");
					if (classInfo.length == 2)
					{
						final String id = classInfo[0].trim();
						PVP_PHYSICAL_SKILL_DEFENCE_MULTIPLIERS[Util.isDigit(id) ? Integer.parseInt(id) : Enum.valueOf(ClassId.class, id).getId()] = Float.parseFloat(classInfo[1].trim());
					}
				}
			}
			Arrays.fill(PVE_PHYSICAL_SKILL_CRITICAL_CHANCE_MULTIPLIERS, 1f);
			final String[] pvePhysicalSkillCriticalChanceMultipliers = ClassBalance.getString("PvePhysicalSkillCriticalChanceMultipliers", "").trim().split(";");
			if (pvePhysicalSkillCriticalChanceMultipliers.length > 0)
			{
				for (String info : pvePhysicalSkillCriticalChanceMultipliers)
				{
					final String[] classInfo = info.trim().split("[*]");
					if (classInfo.length == 2)
					{
						final String id = classInfo[0].trim();
						PVE_PHYSICAL_SKILL_CRITICAL_CHANCE_MULTIPLIERS[Util.isDigit(id) ? Integer.parseInt(id) : Enum.valueOf(ClassId.class, id).getId()] = Float.parseFloat(classInfo[1].trim());
					}
				}
			}
			Arrays.fill(PVP_PHYSICAL_SKILL_CRITICAL_CHANCE_MULTIPLIERS, 1f);
			final String[] pvpPhysicalSkillCriticalChanceMultipliers = ClassBalance.getString("PvpPhysicalSkillCriticalChanceMultipliers", "").trim().split(";");
			if (pvpPhysicalSkillCriticalChanceMultipliers.length > 0)
			{
				for (String info : pvpPhysicalSkillCriticalChanceMultipliers)
				{
					final String[] classInfo = info.trim().split("[*]");
					if (classInfo.length == 2)
					{
						final String id = classInfo[0].trim();
						PVP_PHYSICAL_SKILL_CRITICAL_CHANCE_MULTIPLIERS[Util.isDigit(id) ? Integer.parseInt(id) : Enum.valueOf(ClassId.class, id).getId()] = Float.parseFloat(classInfo[1].trim());
					}
				}
			}
			Arrays.fill(PVE_PHYSICAL_SKILL_CRITICAL_DAMAGE_MULTIPLIERS, 1f);
			final String[] pvePhysicalSkillCriticalDamageMultipliers = ClassBalance.getString("PvePhysicalSkillCriticalDamageMultipliers", "").trim().split(";");
			if (pvePhysicalSkillCriticalDamageMultipliers.length > 0)
			{
				for (String info : pvePhysicalSkillCriticalDamageMultipliers)
				{
					final String[] classInfo = info.trim().split("[*]");
					if (classInfo.length == 2)
					{
						final String id = classInfo[0].trim();
						PVE_PHYSICAL_SKILL_CRITICAL_DAMAGE_MULTIPLIERS[Util.isDigit(id) ? Integer.parseInt(id) : Enum.valueOf(ClassId.class, id).getId()] = Float.parseFloat(classInfo[1].trim());
					}
				}
			}
			Arrays.fill(PVP_PHYSICAL_SKILL_CRITICAL_DAMAGE_MULTIPLIERS, 1f);
			final String[] pvpPhysicalSkillCriticalDamageMultipliers = ClassBalance.getString("PvpPhysicalSkillCriticalDamageMultipliers", "").trim().split(";");
			if (pvpPhysicalSkillCriticalDamageMultipliers.length > 0)
			{
				for (String info : pvpPhysicalSkillCriticalDamageMultipliers)
				{
					final String[] classInfo = info.trim().split("[*]");
					if (classInfo.length == 2)
					{
						final String id = classInfo[0].trim();
						PVP_PHYSICAL_SKILL_CRITICAL_DAMAGE_MULTIPLIERS[Util.isDigit(id) ? Integer.parseInt(id) : Enum.valueOf(ClassId.class, id).getId()] = Float.parseFloat(classInfo[1].trim());
					}
				}
			}
			Arrays.fill(PVE_PHYSICAL_ATTACK_DAMAGE_MULTIPLIERS, 1f);
			final String[] pvePhysicalAttackDamageMultipliers = ClassBalance.getString("PvePhysicalAttackDamageMultipliers", "").trim().split(";");
			if (pvePhysicalAttackDamageMultipliers.length > 0)
			{
				for (String info : pvePhysicalAttackDamageMultipliers)
				{
					final String[] classInfo = info.trim().split("[*]");
					if (classInfo.length == 2)
					{
						final String id = classInfo[0].trim();
						PVE_PHYSICAL_ATTACK_DAMAGE_MULTIPLIERS[Util.isDigit(id) ? Integer.parseInt(id) : Enum.valueOf(ClassId.class, id).getId()] = Float.parseFloat(classInfo[1].trim());
					}
				}
			}
			Arrays.fill(PVP_PHYSICAL_ATTACK_DAMAGE_MULTIPLIERS, 1f);
			final String[] pvpPhysicalAttackDamageMultipliers = ClassBalance.getString("PvpPhysicalAttackDamageMultipliers", "").trim().split(";");
			if (pvpPhysicalAttackDamageMultipliers.length > 0)
			{
				for (String info : pvpPhysicalAttackDamageMultipliers)
				{
					final String[] classInfo = info.trim().split("[*]");
					if (classInfo.length == 2)
					{
						final String id = classInfo[0].trim();
						PVP_PHYSICAL_ATTACK_DAMAGE_MULTIPLIERS[Util.isDigit(id) ? Integer.parseInt(id) : Enum.valueOf(ClassId.class, id).getId()] = Float.parseFloat(classInfo[1].trim());
					}
				}
			}
			Arrays.fill(PVE_PHYSICAL_ATTACK_DEFENCE_MULTIPLIERS, 1f);
			final String[] pvePhysicalAttackDefenceMultipliers = ClassBalance.getString("PvePhysicalAttackDefenceMultipliers", "").trim().split(";");
			if (pvePhysicalAttackDefenceMultipliers.length > 0)
			{
				for (String info : pvePhysicalAttackDefenceMultipliers)
				{
					final String[] classInfo = info.trim().split("[*]");
					if (classInfo.length == 2)
					{
						final String id = classInfo[0].trim();
						PVE_PHYSICAL_ATTACK_DEFENCE_MULTIPLIERS[Util.isDigit(id) ? Integer.parseInt(id) : Enum.valueOf(ClassId.class, id).getId()] = Float.parseFloat(classInfo[1].trim());
					}
				}
			}
			Arrays.fill(PVP_PHYSICAL_ATTACK_DEFENCE_MULTIPLIERS, 1f);
			final String[] pvpPhysicalAttackDefenceMultipliers = ClassBalance.getString("PvpPhysicalAttackDefenceMultipliers", "").trim().split(";");
			if (pvpPhysicalAttackDefenceMultipliers.length > 0)
			{
				for (String info : pvpPhysicalAttackDefenceMultipliers)
				{
					final String[] classInfo = info.trim().split("[*]");
					if (classInfo.length == 2)
					{
						final String id = classInfo[0].trim();
						PVP_PHYSICAL_ATTACK_DEFENCE_MULTIPLIERS[Util.isDigit(id) ? Integer.parseInt(id) : Enum.valueOf(ClassId.class, id).getId()] = Float.parseFloat(classInfo[1].trim());
					}
				}
			}
			Arrays.fill(PVE_PHYSICAL_ATTACK_CRITICAL_CHANCE_MULTIPLIERS, 1f);
			final String[] pvePhysicalAttackCriticalChanceMultipliers = ClassBalance.getString("PvePhysicalAttackCriticalChanceMultipliers", "").trim().split(";");
			if (pvePhysicalAttackCriticalChanceMultipliers.length > 0)
			{
				for (String info : pvePhysicalAttackCriticalChanceMultipliers)
				{
					final String[] classInfo = info.trim().split("[*]");
					if (classInfo.length == 2)
					{
						final String id = classInfo[0].trim();
						PVE_PHYSICAL_ATTACK_CRITICAL_CHANCE_MULTIPLIERS[Util.isDigit(id) ? Integer.parseInt(id) : Enum.valueOf(ClassId.class, id).getId()] = Float.parseFloat(classInfo[1].trim());
					}
				}
			}
			Arrays.fill(PVP_PHYSICAL_ATTACK_CRITICAL_CHANCE_MULTIPLIERS, 1f);
			final String[] pvpPhysicalAttackCriticalChanceMultipliers = ClassBalance.getString("PvpPhysicalAttackCriticalChanceMultipliers", "").trim().split(";");
			if (pvpPhysicalAttackCriticalChanceMultipliers.length > 0)
			{
				for (String info : pvpPhysicalAttackCriticalChanceMultipliers)
				{
					final String[] classInfo = info.trim().split("[*]");
					if (classInfo.length == 2)
					{
						final String id = classInfo[0].trim();
						PVP_PHYSICAL_ATTACK_CRITICAL_CHANCE_MULTIPLIERS[Util.isDigit(id) ? Integer.parseInt(id) : Enum.valueOf(ClassId.class, id).getId()] = Float.parseFloat(classInfo[1].trim());
					}
				}
			}
			Arrays.fill(PVE_PHYSICAL_ATTACK_CRITICAL_DAMAGE_MULTIPLIERS, 1f);
			final String[] pvePhysicalAttackCriticalDamageMultipliers = ClassBalance.getString("PvePhysicalAttackCriticalDamageMultipliers", "").trim().split(";");
			if (pvePhysicalAttackCriticalDamageMultipliers.length > 0)
			{
				for (String info : pvePhysicalAttackCriticalDamageMultipliers)
				{
					final String[] classInfo = info.trim().split("[*]");
					if (classInfo.length == 2)
					{
						final String id = classInfo[0].trim();
						PVE_PHYSICAL_ATTACK_CRITICAL_DAMAGE_MULTIPLIERS[Util.isDigit(id) ? Integer.parseInt(id) : Enum.valueOf(ClassId.class, id).getId()] = Float.parseFloat(classInfo[1].trim());
					}
				}
			}
			Arrays.fill(PVP_PHYSICAL_ATTACK_CRITICAL_DAMAGE_MULTIPLIERS, 1f);
			final String[] pvpPhysicalAttackCriticalDamageMultipliers = ClassBalance.getString("PvpPhysicalAttackCriticalDamageMultipliers", "").trim().split(";");
			if (pvpPhysicalAttackCriticalDamageMultipliers.length > 0)
			{
				for (String info : pvpPhysicalAttackCriticalDamageMultipliers)
				{
					final String[] classInfo = info.trim().split("[*]");
					if (classInfo.length == 2)
					{
						final String id = classInfo[0].trim();
						PVP_PHYSICAL_ATTACK_CRITICAL_DAMAGE_MULTIPLIERS[Util.isDigit(id) ? Integer.parseInt(id) : Enum.valueOf(ClassId.class, id).getId()] = Float.parseFloat(classInfo[1].trim());
					}
				}
			}
			Arrays.fill(PVE_BLOW_SKILL_DAMAGE_MULTIPLIERS, 1f);
			final String[] pveBlowSkillDamageMultipliers = ClassBalance.getString("PveBlowSkillDamageMultipliers", "").trim().split(";");
			if (pveBlowSkillDamageMultipliers.length > 0)
			{
				for (String info : pveBlowSkillDamageMultipliers)
				{
					final String[] classInfo = info.trim().split("[*]");
					if (classInfo.length == 2)
					{
						final String id = classInfo[0].trim();
						PVE_BLOW_SKILL_DAMAGE_MULTIPLIERS[Util.isDigit(id) ? Integer.parseInt(id) : Enum.valueOf(ClassId.class, id).getId()] = Float.parseFloat(classInfo[1].trim());
					}
				}
			}
			Arrays.fill(PVP_BLOW_SKILL_DAMAGE_MULTIPLIERS, 1f);
			final String[] pvpBlowSkillDamageMultipliers = ClassBalance.getString("PvpBlowSkillDamageMultipliers", "").trim().split(";");
			if (pvpBlowSkillDamageMultipliers.length > 0)
			{
				for (String info : pvpBlowSkillDamageMultipliers)
				{
					final String[] classInfo = info.trim().split("[*]");
					if (classInfo.length == 2)
					{
						final String id = classInfo[0].trim();
						PVP_BLOW_SKILL_DAMAGE_MULTIPLIERS[Util.isDigit(id) ? Integer.parseInt(id) : Enum.valueOf(ClassId.class, id).getId()] = Float.parseFloat(classInfo[1].trim());
					}
				}
			}
			Arrays.fill(PVE_BLOW_SKILL_DEFENCE_MULTIPLIERS, 1f);
			final String[] pveBlowSkillDefenceMultipliers = ClassBalance.getString("PveBlowSkillDefenceMultipliers", "").trim().split(";");
			if (pveBlowSkillDefenceMultipliers.length > 0)
			{
				for (String info : pveBlowSkillDefenceMultipliers)
				{
					final String[] classInfo = info.trim().split("[*]");
					if (classInfo.length == 2)
					{
						final String id = classInfo[0].trim();
						PVE_BLOW_SKILL_DEFENCE_MULTIPLIERS[Util.isDigit(id) ? Integer.parseInt(id) : Enum.valueOf(ClassId.class, id).getId()] = Float.parseFloat(classInfo[1].trim());
					}
				}
			}
			Arrays.fill(PVP_BLOW_SKILL_DEFENCE_MULTIPLIERS, 1f);
			final String[] pvpBlowSkillDefenceMultipliers = ClassBalance.getString("PvpBlowSkillDefenceMultipliers", "").trim().split(";");
			if (pvpBlowSkillDefenceMultipliers.length > 0)
			{
				for (String info : pvpBlowSkillDefenceMultipliers)
				{
					final String[] classInfo = info.trim().split("[*]");
					if (classInfo.length == 2)
					{
						final String id = classInfo[0].trim();
						PVP_BLOW_SKILL_DEFENCE_MULTIPLIERS[Util.isDigit(id) ? Integer.parseInt(id) : Enum.valueOf(ClassId.class, id).getId()] = Float.parseFloat(classInfo[1].trim());
					}
				}
			}
			Arrays.fill(PVE_ENERGY_SKILL_DAMAGE_MULTIPLIERS, 1f);
			final String[] pveEnergySkillDamageMultipliers = ClassBalance.getString("PveEnergySkillDamageMultipliers", "").trim().split(";");
			if (pveEnergySkillDamageMultipliers.length > 0)
			{
				for (String info : pveEnergySkillDamageMultipliers)
				{
					final String[] classInfo = info.trim().split("[*]");
					if (classInfo.length == 2)
					{
						final String id = classInfo[0].trim();
						PVE_ENERGY_SKILL_DAMAGE_MULTIPLIERS[Util.isDigit(id) ? Integer.parseInt(id) : Enum.valueOf(ClassId.class, id).getId()] = Float.parseFloat(classInfo[1].trim());
					}
				}
			}
			Arrays.fill(PVP_ENERGY_SKILL_DAMAGE_MULTIPLIERS, 1f);
			final String[] pvpEnergySkillDamageMultipliers = ClassBalance.getString("PvpEnergySkillDamageMultipliers", "").trim().split(";");
			if (pvpEnergySkillDamageMultipliers.length > 0)
			{
				for (String info : pvpEnergySkillDamageMultipliers)
				{
					final String[] classInfo = info.trim().split("[*]");
					if (classInfo.length == 2)
					{
						final String id = classInfo[0].trim();
						PVP_ENERGY_SKILL_DAMAGE_MULTIPLIERS[Util.isDigit(id) ? Integer.parseInt(id) : Enum.valueOf(ClassId.class, id).getId()] = Float.parseFloat(classInfo[1].trim());
					}
				}
			}
			Arrays.fill(PVE_ENERGY_SKILL_DEFENCE_MULTIPLIERS, 1f);
			final String[] pveEnergySkillDefenceMultipliers = ClassBalance.getString("PveEnergySkillDefenceMultipliers", "").trim().split(";");
			if (pveEnergySkillDefenceMultipliers.length > 0)
			{
				for (String info : pveEnergySkillDefenceMultipliers)
				{
					final String[] classInfo = info.trim().split("[*]");
					if (classInfo.length == 2)
					{
						final String id = classInfo[0].trim();
						PVE_ENERGY_SKILL_DEFENCE_MULTIPLIERS[Util.isDigit(id) ? Integer.parseInt(id) : Enum.valueOf(ClassId.class, id).getId()] = Float.parseFloat(classInfo[1].trim());
					}
				}
			}
			Arrays.fill(PVP_ENERGY_SKILL_DEFENCE_MULTIPLIERS, 1f);
			final String[] pvpEnergySkillDefenceMultipliers = ClassBalance.getString("PvpEnergySkillDefenceMultipliers", "").trim().split(";");
			if (pvpEnergySkillDefenceMultipliers.length > 0)
			{
				for (String info : pvpEnergySkillDefenceMultipliers)
				{
					final String[] classInfo = info.trim().split("[*]");
					if (classInfo.length == 2)
					{
						final String id = classInfo[0].trim();
						PVP_ENERGY_SKILL_DEFENCE_MULTIPLIERS[Util.isDigit(id) ? Integer.parseInt(id) : Enum.valueOf(ClassId.class, id).getId()] = Float.parseFloat(classInfo[1].trim());
					}
				}
			}
			Arrays.fill(PLAYER_HEALING_SKILL_MULTIPLIERS, 1f);
			final String[] playerHealingSkillMultipliers = ClassBalance.getString("PlayerHealingSkillMultipliers", "").trim().split(";");
			if (playerHealingSkillMultipliers.length > 0)
			{
				for (String info : playerHealingSkillMultipliers)
				{
					final String[] classInfo = info.trim().split("[*]");
					if (classInfo.length == 2)
					{
						final String id = classInfo[0].trim();
						PLAYER_HEALING_SKILL_MULTIPLIERS[Util.isDigit(id) ? Integer.parseInt(id) : Enum.valueOf(ClassId.class, id).getId()] = Float.parseFloat(classInfo[1].trim());
					}
				}
			}
			Arrays.fill(SKILL_MASTERY_CHANCE_MULTIPLIERS, 1f);
			final String[] skillMasteryChanceMultipliers = ClassBalance.getString("SkillMasteryChanceMultipliers", "").trim().split(";");
			if (skillMasteryChanceMultipliers.length > 0)
			{
				for (String info : skillMasteryChanceMultipliers)
				{
					final String[] classInfo = info.trim().split("[*]");
					if (classInfo.length == 2)
					{
						final String id = classInfo[0].trim();
						SKILL_MASTERY_CHANCE_MULTIPLIERS[Util.isDigit(id) ? Integer.parseInt(id) : Enum.valueOf(ClassId.class, id).getId()] = Float.parseFloat(classInfo[1].trim());
					}
				}
			}
			Arrays.fill(EXP_AMOUNT_MULTIPLIERS, 1f);
			final String[] expAmountMultipliers = ClassBalance.getString("ExpAmountMultipliers", "").trim().split(";");
			if (expAmountMultipliers.length > 0)
			{
				for (String info : expAmountMultipliers)
				{
					final String[] classInfo = info.trim().split("[*]");
					if (classInfo.length == 2)
					{
						final String id = classInfo[0].trim();
						EXP_AMOUNT_MULTIPLIERS[Util.isDigit(id) ? Integer.parseInt(id) : Enum.valueOf(ClassId.class, id).getId()] = Float.parseFloat(classInfo[1].trim());
					}
				}
			}
			Arrays.fill(SP_AMOUNT_MULTIPLIERS, 1f);
			final String[] spAmountMultipliers = ClassBalance.getString("SpAmountMultipliers", "").trim().split(";");
			if (spAmountMultipliers.length > 0)
			{
				for (String info : spAmountMultipliers)
				{
					final String[] classInfo = info.trim().split("[*]");
					if (classInfo.length == 2)
					{
						final String id = classInfo[0].trim();
						SP_AMOUNT_MULTIPLIERS[Util.isDigit(id) ? Integer.parseInt(id) : Enum.valueOf(ClassId.class, id).getId()] = Float.parseFloat(classInfo[1].trim());
					}
				}
			}
			// Load CommunityBoard config file (if exists)
			final PropertiesParser CommunityBoard = new PropertiesParser(CUSTOM_COMMUNITY_BOARD_CONFIG_FILE);
			CUSTOM_CB_ENABLED = CommunityBoard.getBoolean("CustomCommunityBoard", false);
			COMMUNITYBOARD_CURRENCY = CommunityBoard.getInt("CommunityCurrencyId", 57);
			COMMUNITYBOARD_ENABLE_MULTISELLS = CommunityBoard.getBoolean("CommunityEnableMultisells", true);
			COMMUNITYBOARD_ENABLE_TELEPORTS = CommunityBoard.getBoolean("CommunityEnableTeleports", true);
			COMMUNITYBOARD_ENABLE_BUFFS = CommunityBoard.getBoolean("CommunityEnableBuffs", true);
			COMMUNITYBOARD_ENABLE_HEAL = CommunityBoard.getBoolean("CommunityEnableHeal", true);
			COMMUNITYBOARD_ENABLE_DELEVEL = CommunityBoard.getBoolean("CommunityEnableDelevel", false);
			COMMUNITYBOARD_TELEPORT_PRICE = CommunityBoard.getInt("CommunityTeleportPrice", 0);
			COMMUNITYBOARD_BUFF_PRICE = CommunityBoard.getInt("CommunityBuffPrice", 0);
			COMMUNITYBOARD_HEAL_PRICE = CommunityBoard.getInt("CommunityHealPrice", 0);
			COMMUNITYBOARD_DELEVEL_PRICE = CommunityBoard.getInt("CommunityDelevelPrice", 0);
			COMMUNITYBOARD_KARMA_DISABLED = CommunityBoard.getBoolean("CommunityKarmaDisabled", true);
			COMMUNITYBOARD_CAST_ANIMATIONS = CommunityBoard.getBoolean("CommunityCastAnimations", false);
			COMMUNITY_PREMIUM_SYSTEM_ENABLED = CommunityBoard.getBoolean("CommunityPremiumSystem", false);
			COMMUNITY_PREMIUM_COIN_ID = CommunityBoard.getInt("CommunityPremiumBuyCoinId", 57);
			COMMUNITY_PREMIUM_PRICE_PER_DAY = CommunityBoard.getInt("CommunityPremiumPricePerDay", 1000000);
			final String[] allowedBuffs = CommunityBoard.getString("CommunityAvailableBuffs", "").split(",");
			COMMUNITY_AVAILABLE_BUFFS = new ArrayList<>(allowedBuffs.length);
			for (String s : allowedBuffs)
			{
				COMMUNITY_AVAILABLE_BUFFS.add(Integer.parseInt(s));
			}
			final String[] availableTeleports = CommunityBoard.getString("CommunityTeleportList", "").split(";");
			COMMUNITY_AVAILABLE_TELEPORTS = new HashMap<>(availableTeleports.length);
			for (String s : availableTeleports)
			{
				final String splitInfo[] = s.split(",");
				COMMUNITY_AVAILABLE_TELEPORTS.put(splitInfo[0], new Location(Integer.parseInt(splitInfo[1]), Integer.parseInt(splitInfo[2]), Integer.parseInt(splitInfo[3])));
			}
			// Load CustomDepositableItems config file (if exists)
			final PropertiesParser CustomDepositableItems = new PropertiesParser(CUSTOM_CUSTOM_DEPOSITABLE_ITEMS_CONFIG_FILE);
			CUSTOM_DEPOSITABLE_ENABLED = CustomDepositableItems.getBoolean("CustomDepositableEnabled", false);
			CUSTOM_DEPOSITABLE_QUEST_ITEMS = CustomDepositableItems.getBoolean("DepositableQuestItems", false);
			// Load CustomMailManager config file (if exists)
			final PropertiesParser CustomMailManager = new PropertiesParser(CUSTOM_CUSTOM_MAIL_MANAGER_CONFIG_FILE);
			CUSTOM_MAIL_MANAGER_ENABLED = CustomMailManager.getBoolean("CustomMailManagerEnabled", false);
			CUSTOM_MAIL_MANAGER_DELAY = CustomMailManager.getInt("DatabaseQueryDelay", 30) * 1000;
			// Load DelevelManager config file (if exists)
			final PropertiesParser DelevelManager = new PropertiesParser(CUSTOM_DELEVEL_MANAGER_CONFIG_FILE);
			DELEVEL_MANAGER_ENABLED = DelevelManager.getBoolean("Enabled", false);
			DELEVEL_MANAGER_NPCID = DelevelManager.getInt("NpcId", 1002000);
			DELEVEL_MANAGER_ITEMID = DelevelManager.getInt("RequiredItemId", 4356);
			DELEVEL_MANAGER_ITEMCOUNT = DelevelManager.getInt("RequiredItemCount", 2);
			DELEVEL_MANAGER_MINIMUM_DELEVEL = DelevelManager.getInt("MimimumDelevel", 20);
			// Load DualboxCheck config file (if exists)
			final PropertiesParser DualboxCheck = new PropertiesParser(CUSTOM_DUALBOX_CHECK_CONFIG_FILE);
			DUALBOX_CHECK_MAX_PLAYERS_PER_IP = DualboxCheck.getInt("DualboxCheckMaxPlayersPerIP", 0);
			DUALBOX_CHECK_MAX_OLYMPIAD_PARTICIPANTS_PER_IP = DualboxCheck.getInt("DualboxCheckMaxOlympiadParticipantsPerIP", 0);
			DUALBOX_CHECK_MAX_L2EVENT_PARTICIPANTS_PER_IP = DualboxCheck.getInt("DualboxCheckMaxL2EventParticipantsPerIP", 0);
			DUALBOX_COUNT_OFFLINE_TRADERS = DualboxCheck.getBoolean("DualboxCountOfflineTraders", false);
			final String[] dualboxCheckWhiteList = DualboxCheck.getString("DualboxCheckWhitelist", "127.0.0.1,0").split(";");
			DUALBOX_CHECK_WHITELIST = new HashMap<>(dualboxCheckWhiteList.length);
			for (String entry : dualboxCheckWhiteList)
			{
				final String[] entrySplit = entry.split(",");
				if (entrySplit.length != 2)
				{
					LOGGER.warning(StringUtil.concat("DualboxCheck[Config.load()]: invalid config property -> DualboxCheckWhitelist \"", entry, "\""));
				}
				else
				{
					try
					{
						int num = Integer.parseInt(entrySplit[1]);
						num = num == 0 ? -1 : num;
						DUALBOX_CHECK_WHITELIST.put(InetAddress.getByName(entrySplit[0]).getHostAddress().hashCode(), num);
					}
					catch (UnknownHostException e)
					{
						LOGGER.warning(StringUtil.concat("DualboxCheck[Config.load()]: invalid address -> DualboxCheckWhitelist \"", entrySplit[0], "\""));
					}
					catch (NumberFormatException e)
					{
						LOGGER.warning(StringUtil.concat("DualboxCheck[Config.load()]: invalid number -> DualboxCheckWhitelist \"", entrySplit[1], "\""));
					}
				}
			}
			final String[] dualboxCheckWhiteListAAC = DualboxCheck.getString("DualboxCheckWhitelistAAC", "127.0.0.1,0").split(";");
			DUALBOX_CHECK_WHITELIST_AAC = new HashMap<>(dualboxCheckWhiteListAAC.length);
			for (String entry : dualboxCheckWhiteListAAC)
			{
				final String[] entrySplit = entry.split(",");
				if (entrySplit.length != 2)
				{
					LOGGER.warning(StringUtil.concat("DualboxCheckAAC[Config.load()]: invalid config property -> DualboxCheckWhitelistAAC \"", entry, "\""));
				}
				else
				{
					try
					{
						int num = Integer.parseInt(entrySplit[1]);
						DUALBOX_CHECK_WHITELIST_AAC.put(InetAddress.getByName(entrySplit[0]).hashCode(), num);
					}
					catch (UnknownHostException e)
					{
						LOGGER.warning(StringUtil.concat("DualboxCheckAAC[Config.load()]: invalid address -> DualboxCheckWhitelistAAC \"", entrySplit[0], "\""));
					}
					catch (NumberFormatException e)
					{
						LOGGER.warning(StringUtil.concat("DualboxCheckAAC[Config.load()]: invalid number -> DualboxCheckWhitelistAAC \"", entrySplit[1], "\""));
					}
				}
			}
			IGNORED_HWIDS = new ArrayList<>();
			final String[] splt = DualboxCheck.getString("IgnoredHWIDS", "1,2").split(",");
			for (String s : splt)
			{
				IGNORED_HWIDS.add(s);
			}
			// Load Custom Reward CTV config file (if exists)
			final PropertiesParser CustomRewardCTV = new PropertiesParser(CUSTOM_REWARD_CTV_CONFIG_FILE);
			ENABLE_CUSTOM_REWARD_CTV = CustomRewardCTV.getBoolean("EnableCustomRewardCTV", false);
			final String[] ListCharRewardCTV = CustomRewardCTV.getString("ListCharNameRewardCTV", "").split(",");
			LIST_CHARNAME_REWARD_CTV = new ArrayList<>(ListCharRewardCTV.length);
			for (String s : ListCharRewardCTV)
			{
				LIST_CHARNAME_REWARD_CTV.add(s);
			}
			REWARD_ITEM_BY_ID = CustomRewardCTV.getInt("RewardItemId", 0);
			REWARD_AMOUNT = CustomRewardCTV.getInt("RewardAmount", 0);
			final String[] ListCharLeaderRewardCTV = CustomRewardCTV.getString("ListCharNameLeaderRewardCTV", "").split(",");
			LIST_CHARNAME_LEADER_REWARD_CTV = new ArrayList<>(ListCharLeaderRewardCTV.length);
			for (String s : ListCharLeaderRewardCTV)
			{
				LIST_CHARNAME_LEADER_REWARD_CTV.add(s);
			}
			REWARD_LEADER_ITEM_BY_ID = CustomRewardCTV.getInt("RewardLeaderItemId", 0);
			REWARD_LEADER_AMOUNT = CustomRewardCTV.getInt("RewardLeaderAmount", 0);
			// Reward CTV Clan WM
			final String[] ListCharRewardCTVWM = CustomRewardCTV.getString("ListCharNameRewardCTV_WM", "").split(",");
			LIST_CHARNAME_REWARD_CTV_WM = new ArrayList<>(ListCharRewardCTVWM.length);
			for (String s : ListCharRewardCTVWM)
			{
				LIST_CHARNAME_REWARD_CTV_WM.add(s);
			}
			REWARD_ITEM_BY_ID_WM = CustomRewardCTV.getInt("RewardItemId_WM", 0);
			REWARD_AMOUNT_WM = CustomRewardCTV.getInt("RewardAmountWM", 0);
			// Reward CTV VIP
			ENABLE_CUSTOM_REWARD_CTV_VIP = CustomRewardCTV.getBoolean("EnableCustomRewardCTV_VIP", false);
			final String[] ListCharRewardCTVVIP = CustomRewardCTV.getString("ListCharNameRewardCTV_VIP", "").split(",");
			LIST_CHARNAME_REWARD_CTV_VIP = new ArrayList<>(ListCharRewardCTVVIP.length);
			for (String s : ListCharRewardCTVVIP)
			{
				LIST_CHARNAME_REWARD_CTV_VIP.add(s);
			}
			REWARD_LCOIN_AMOUNT_CTV_VIP = CustomRewardCTV.getInt("RewardLCoinAmoutCTV_VIP", 0);
			REWARD_ADENA_AMOUNT_CTV_VIP = CustomRewardCTV.getInt("RewardAdenaAmoutCTV_VIP", 0);
			// Load Custom Bonus CTRV config file (if exists)
			final PropertiesParser CustomBonusKarma = new PropertiesParser(CUSTOM_CTV_KARMA_CONFIG_FILE);
			ENABLE_CUSTOM_CTV_KARMA_BONUS = CustomBonusKarma.getBoolean("EnableCustomCTVKarmaBonus", false);
			LIST_CHARNAME_CTV_KARMA_BONUS = new ArrayList<>();
			final String[] ListCharsCTVIgnore = CustomBonusKarma.getString("ListCharNameIgnoreKarma", "baLien,Gunner").split(",");
			for (String s : ListCharsCTVIgnore)
			{
				LIST_CHARNAME_CTV_KARMA_BONUS.add(s);
			}
			// Load Custom Bonus Drop config file (if exists)
			final PropertiesParser CustomBonus = new PropertiesParser(CUSTOM_BONUS_CONFIG_FILE);
			SPECIAL_CRAFT_PET_TRAINING_GUIDE_TAKE_GIRAN_SEALED = CustomBonus.getInt("SpecialCraft_PetTraningGuide_GiranSealed", 0);
			SPECIAL_CRAFT_ADVANCED_PET_TRAINING_GUIDE_TAKE_GIRAN_SEALED = CustomBonus.getInt("SpecialCraft_AdvancedPetTraningGuide_GiranSealed", 0);
			ENABLE_CUSTOM_DROP_ALL_BOSSES = CustomBonus.getBoolean("EnableCustomDropAllBosses", false);
			ENABLE_CUSTOM_DROP_GRANDBOSSES = CustomBonus.getBoolean("EnableCustomDropGrandBosses", false);
			ENABLE_CUSTOM_BAIUM_NO_DROP_ITEMS = CustomBonus.getBoolean("EnableCustomBaiumNoDropItems", false);
			final String[] ListCharBonusGrandBosses = CustomBonus.getString("ListCharNameDropBonusGrandBosses", "").split(",");
			LIST_CHARNAME_DROP_BONUS_GRANDBOSSES = new ArrayList<>(ListCharBonusGrandBosses.length);
			for (String s : ListCharBonusGrandBosses)
			{
				LIST_CHARNAME_DROP_BONUS_GRANDBOSSES.add(s);
			}
			final String[] dropChanceMultiplierBonusGrandbosses = CustomBonus.getString("DropChanceBonusGrandBossesMultiplierByItemId", "").split(";");
			RATE_BONUS_DROP_CHANCE_BY_ID_GRANDBOSSES = new HashMap<>(dropChanceMultiplierBonusGrandbosses.length);
			if (!dropChanceMultiplierBonusGrandbosses[0].isEmpty())
			{
				for (String item : dropChanceMultiplierBonusGrandbosses)
				{
					final String[] itemSplit = item.split(",");
					if (itemSplit.length != 2)
					{
						LOGGER.warning(StringUtil.concat("Config.load(): invalid config property -> DropChanceBonusGrandBossesMultiplierByItemId \"", item, "\""));
					}
					else
					{
						try
						{
							RATE_BONUS_DROP_CHANCE_BY_ID_GRANDBOSSES.put(Integer.parseInt(itemSplit[0]), Float.parseFloat(itemSplit[1]));
						}
						catch (NumberFormatException nfe)
						{
							if (!item.isEmpty())
							{
								LOGGER.warning(StringUtil.concat("Config.load(): invalid config property -> DropChanceBonusGrandBossesMultiplierByItemId \"", item, "\""));
							}
						}
					}
				}
			}
			ENABLE_CUSTOM_CHAOTIC_NO_DROP_ITEMS = CustomBonus.getBoolean("EnableCustomChaoticNoDropItems", false);
			ENABLE_CUSTOM_DROP_CHAOTIC_BOSSES = CustomBonus.getBoolean("EnableCustomDropChaoticBosses", false);
			final String[] ListCharBonusChaoticBosses = CustomBonus.getString("ListCharNameDropBonusChaoticBosses", "").split(",");
			LIST_CHARNAME_DROP_BONUS_CHAOTIC = new ArrayList<>(ListCharBonusChaoticBosses.length);
			for (String s : ListCharBonusChaoticBosses)
			{
				LIST_CHARNAME_DROP_BONUS_CHAOTIC.add(s);
			}
			final String[] dropChanceMultiplierBonusChaoticBosses = CustomBonus.getString("DropChanceBonusChaoticBossesMultiplierByItemId", "").split(";");
			RATE_BONUS_DROP_CHANCE_BY_ID_CHAOTIC = new HashMap<>(dropChanceMultiplierBonusChaoticBosses.length);
			if (!dropChanceMultiplierBonusChaoticBosses[0].isEmpty())
			{
				for (String item : dropChanceMultiplierBonusChaoticBosses)
				{
					final String[] itemSplit = item.split(",");
					if (itemSplit.length != 2)
					{
						LOGGER.warning(StringUtil.concat("Config.load(): invalid config property -> DropChanceBonusChaoticBossesMultiplierByItemId \"", item, "\""));
					}
					else
					{
						try
						{
							RATE_BONUS_DROP_CHANCE_BY_ID_CHAOTIC.put(Integer.parseInt(itemSplit[0]), Float.parseFloat(itemSplit[1]));
						}
						catch (NumberFormatException nfe)
						{
							if (!item.isEmpty())
							{
								LOGGER.warning(StringUtil.concat("Config.load(): invalid config property -> DropChanceBonusChaoticBossesMultiplierByItemId \"", item, "\""));
							}
						}
					}
				}
			}
			ENABLE_CUSTOM_ENCHANT_RATE_CTV = CustomBonus.getBoolean("EnableCustomEnchantRateCTV", false);
			if (ENABLE_CUSTOM_ENCHANT_RATE_CTV)
			{
				final String[] enchantRateListCTV = CustomBonus.getString("EnchantRateListCTV", "").split(";");
				RATE_ENCHANT_LIST_CTV = new HashMap<>(enchantRateListCTV.length);
				for (String enchantRate : enchantRateListCTV)
				{
					final String[] enchantRateSplit = enchantRate.split(",");
					if (enchantRateSplit.length != 2)
					{
						LOGGER.warning(StringUtil.concat("[enchantRateList]: invalid config property -> enchantRateList \"", enchantRate, "\""));
					}
					else
					{
						try
						{
							RATE_ENCHANT_LIST_CTV.put(String.valueOf(enchantRateSplit[0]), Integer.parseInt(enchantRateSplit[1]));
						}
						catch (NumberFormatException nfe)
						{
							if (!enchantRate.isEmpty())
							{
								LOGGER.warning(StringUtil.concat("[enchantRateList]: invalid config property -> enchantRate \"", enchantRateSplit[0], "\"", enchantRateSplit[1]));
							}
						}
					}
				}
			}
			ENABLE_CUSTOM_COMBINE_RATE_CTV = CustomBonus.getBoolean("EnableCustomCombineRateCTV", false);
			if (ENABLE_CUSTOM_ENCHANT_RATE_CTV)
			{
				final String[] combineRateListCTV = CustomBonus.getString("CombineRateListCTV", "").split(";");
				RATE_COMBINE_LIST_CTV = new HashMap<>(combineRateListCTV.length);
				for (String combineRate : combineRateListCTV)
				{
					final String[] combineRateSplit = combineRate.split(",");
					if (combineRateSplit.length != 2)
					{
						LOGGER.warning(StringUtil.concat("[combineRateList]: invalid config property -> combineRateList \"", combineRate, "\""));
					}
					else
					{
						try
						{
							RATE_COMBINE_LIST_CTV.put(String.valueOf(combineRateSplit[0]), Integer.parseInt(combineRateSplit[1]));
						}
						catch (NumberFormatException nfe)
						{
							if (!combineRate.isEmpty())
							{
								LOGGER.warning(StringUtil.concat("[combineRateList]: invalid config property -> combineRate \"", combineRateSplit[0], "\"", combineRateSplit[1]));
							}
						}
					}
				}
			}
			ENABLE_CUSTOM_DROP_BONUS = CustomBonus.getBoolean("EnableCustomDropBonus", false);
			final String[] ListCharBonus = CustomBonus.getString("ListCharNameDropBonus", "").split(",");
			LIST_CHARNAME_DROP_BONUS = new ArrayList<>(ListCharBonus.length);
			for (String s : ListCharBonus)
			{
				LIST_CHARNAME_DROP_BONUS.add(s);
			}
			final String[] dropChanceMultiplierBonus = CustomBonus.getString("DropChanceBonusMultiplierByItemId", "").split(";");
			RATE_BONUS_DROP_CHANCE_BY_ID = new HashMap<>(dropChanceMultiplierBonus.length);
			if (!dropChanceMultiplierBonus[0].isEmpty())
			{
				for (String item : dropChanceMultiplierBonus)
				{
					final String[] itemSplit = item.split(",");
					if (itemSplit.length != 2)
					{
						LOGGER.warning(StringUtil.concat("Config.load(): invalid config property -> RateDropItemsById \"", item, "\""));
					}
					else
					{
						try
						{
							RATE_BONUS_DROP_CHANCE_BY_ID.put(Integer.parseInt(itemSplit[0]), Float.parseFloat(itemSplit[1]));
						}
						catch (NumberFormatException nfe)
						{
							if (!item.isEmpty())
							{
								LOGGER.warning(StringUtil.concat("Config.load(): invalid config property -> RateDropItemsById \"", item, "\""));
							}
						}
					}
				}
			}
			ENABLE_CUSTOM_ENCHANT_BONUS = CustomBonus.getBoolean("EnableCustomEnchantBonus", false);
			final String[] listCharNameEnchantBonus = CustomBonus.getString("ListCharNameEnchantBonus", "").split(",");
			LIST_CHARNAME_ENCHANT_BONUS = new ArrayList<>(listCharNameEnchantBonus.length);
			for (String s : listCharNameEnchantBonus)
			{
				LIST_CHARNAME_ENCHANT_BONUS.add(s);
			}
			ENABLE_CUSTOM_AUTO_PVP_BONUS = CustomBonus.getBoolean("EnableCustomAutoPvPBonus", false);
			final String[] ListCharBonusAutoPvP = CustomBonus.getString("ListCharBonusAutoPvP", "").split(",");
			LIST_CHARNAME_BONUS_AUTO_PVP = new ArrayList<>(ListCharBonusAutoPvP.length);
			for (String s : ListCharBonusAutoPvP)
			{
				LIST_CHARNAME_BONUS_AUTO_PVP.add(s);
			}
			MAX_PVP_ENEMIES_FOR_BONUS_CHAR = CustomBonus.getInt("MaxPvPEnemiesForBonusChar", 5);
			MAX_PVP_ENEMIES_FOR_NORMAL_CHAR = CustomBonus.getInt("MaxPvPEnemiesNormalChar", 5);
			ENABLE_CUSTOM_EFFECT_SUCCESS_BONUS = CustomBonus.getBoolean("EnableCustomEffectSuccessBonus", false);
			final String[] ListCharBonusEffectSuccess = CustomBonus.getString("ListCharBonusEffectSuccess", "").split(",");
			LIST_CHARNAME_BONUS_EFFECT_SUCCESS = new ArrayList<>(ListCharBonusEffectSuccess.length);
			for (String s : ListCharBonusEffectSuccess)
			{
				LIST_CHARNAME_BONUS_EFFECT_SUCCESS.add(s);
			}
			RATE_BONUS_EFFECT_SUCCESS = CustomBonus.getDouble("Rate_Bonus_Effect_Success", 50.0);
			ENABLE_CUSTOM_DELAY_SKILL_BONUS = CustomBonus.getBoolean("EnableCustomDelaySkillsBonus", false);
			final String[] ListCharBonusDelaySkills = CustomBonus.getString("ListCharBonusDelaySkills", "").split(",");
			LIST_CHARNAME_BONUS_DELAY_SKILLS = new ArrayList<>(ListCharBonusDelaySkills.length);
			for (String s : ListCharBonusDelaySkills)
			{
				LIST_CHARNAME_BONUS_DELAY_SKILLS.add(s);
			}
			if (ENABLE_CUSTOM_DELAY_SKILL_BONUS)
			{
				final String[] skillReuseBonusList = CustomBonus.getString("SkillReuseBonusList", "").split(";");
				SKILL_REUSE_BONUS_LIST = new HashMap<>(skillReuseBonusList.length);
				for (String skill : skillReuseBonusList)
				{
					final String[] skillSplit = skill.split(",");
					if (skillSplit.length != 2)
					{
						LOGGER.warning(StringUtil.concat("[SkillReuseBonusList]: invalid config property -> SkillReuseBonusList \"", skill, "\""));
					}
					else
					{
						try
						{
							SKILL_REUSE_BONUS_LIST.put(Integer.parseInt(skillSplit[0]), Integer.parseInt(skillSplit[1]));
						}
						catch (NumberFormatException nfe)
						{
							if (!skill.isEmpty())
							{
								LOGGER.warning(StringUtil.concat("[SkillReuseBonusList]: invalid config property -> SkillListBonus \"", skillSplit[0], "\"", skillSplit[1]));
							}
						}
					}
				}
			}
			LIST_CHARNAME_IGNORE = new ArrayList<>();
			final String[] ListCharsIgnore = CommunityBoard.getString("ListCharIgnore", "OneHitDie,Jet,ShopGianThuong").split(",");
			for (String s : ListCharsIgnore)
			{
				LIST_CHARNAME_IGNORE.add(s);
			}
			// Load Custom Event config file (if exists)
			final PropertiesParser CustomEvent = new PropertiesParser(CUSTOM_EVENT_CONFIG_FILE);
			ENABLE_EVENT_SYLPH_BLESSING = CustomEvent.getBoolean("EnableEventSylhBlessing", false);
			EVENT_START_HOUR = CustomEvent.getInt("EventStartHour", 19);
			EVENT_END_HOUR = CustomEvent.getInt("EventEndHour", 20);
			ENABLE_CUSTOM_DROP_WATERMELON_EVENT_BOSSES = CustomEvent.getBoolean("EnableCustomDropWatermelonEventBosses", false);
			WATERMELON_SEED_DROP_MAX_DAILY = CustomEvent.getInt("WatermelonSeedDropMaxDaily", 300);
			WATERMELON_SEED_DROP_MIN = CustomEvent.getInt("WatermelonSeedDropMin", 1);
			WATERMELON_SEED_DROP_MAX = CustomEvent.getInt("WatermelonSeedDropMax", 1);
			WATERMELON_SEED_DROP_RATE = CustomEvent.getDouble("WatermelonSeedDropRate", 1.0);
			WATERMELON_LARGE_PRIME_BUFF_CHANCE = CustomEvent.getInt("WatermelonLargePrimeBuffChance", 80);
			WATERMELON_PRIME_BUFF_CHANCE = CustomEvent.getInt("WatermelonPrimeBuffChance", 60);
			WATERMELON_LCOIN_FOR_GET_DOLL_PACK_LEVEL1_VIP_BELOW_11_FROM_RAID = CustomEvent.getInt("LcoinForGetDollPackLevel1VIPBelow11FromRaid", 1000000000);
			WATERMELON_LCOIN_FOR_GET_DOLL_PACK_LEVEL1_VIP_OVER_11_FROM_RAID = CustomEvent.getInt("LcoinForGetDollPackLevel1VIPOver11FromRaid", 1000000);
			// Orcs Invasion Event
			ENABLE_ORC_INVASION_EVENT = CustomEvent.getBoolean("EnableOrcsInvasionEvent", false);
			// Pink Rabbit Chasing Event
			PINK_ORB_DROP_MAX_DAILY = CustomEvent.getInt("PinkOrbDropMaxDaily", 300);
			PINK_ORB_DROP_MIN = CustomEvent.getInt("PinkOrbDropMin", 1);
			PINK_ORB_DROP_MAX = CustomEvent.getInt("PinkOrbDropMax", 1);
			PINK_ORB_DROP_RATE = CustomEvent.getDouble("PinkOrbDropRate", 1.0);
			// Load FindPvP config file (if exists)
			final PropertiesParser FindPvP = new PropertiesParser(CUSTOM_FIND_PVP_CONFIG_FILE);
			ENABLE_FIND_PVP = FindPvP.getBoolean("EnableFindPvP", false);
			// Load MerchantZeroSellPrice config file (if exists)
			final PropertiesParser MerchantZeroSellPrice = new PropertiesParser(CUSTOM_MERCHANT_ZERO_SELL_PRICE_CONFIG_FILE);
			MERCHANT_ZERO_SELL_PRICE = MerchantZeroSellPrice.getBoolean("MerchantZeroSellPrice", false);
			// Load NoblessMaster config file (if exists)
			final PropertiesParser NoblessMaster = new PropertiesParser(CUSTOM_NOBLESS_MASTER_CONFIG_FILE);
			NOBLESS_MASTER_ENABLED = NoblessMaster.getBoolean("Enabled", false);
			NOBLESS_MASTER_NPCID = NoblessMaster.getInt("NpcId", 1003000);
			NOBLESS_MASTER_LEVEL_REQUIREMENT = NoblessMaster.getInt("LevelRequirement", 80);
			NOBLESS_MASTER_REWARD_TIARA = NoblessMaster.getBoolean("RewardTiara", false);
			// Load Custom Battle With Balok config file (if exists)
			final PropertiesParser CustomBattleWithBalok = new PropertiesParser(CUSTOM_BATTLE_WITH_BALOK_CONFIG_FILE);
			final String[] balokTime = CustomBattleWithBalok.getString("BalokTime", "20:30").trim().split(":");
			BALOK_HOUR = Integer.parseInt(balokTime[0]);
			BALOK_MINUTE = Integer.parseInt(balokTime[1]);
			BALOK_POINTS_PER_MONSTER = CustomBattleWithBalok.getInt("BalokPointsPerMonster", 10);
			// Load Custom L-Coin drop config file (if exists)
			final PropertiesParser CustomLCoinDrop = new PropertiesParser(CUSTOM_LCOIN_DROP_CONFIG_FILE);
			LCOIN_DROP_DAILY_MAX_PREMIUM_CHAR = CustomLCoinDrop.getInt("LcoinDropDailyMaxPremiumChar", 60000);
			LCOIN_DROP_DAILY_NORMAL_CHAR = CustomLCoinDrop.getInt("LcoinDropDailyMaxNormalChar", 10000);
			LCOIN_DROP_AMOUNT_NORMAL_CHAR_LEVEL_75 = CustomLCoinDrop.getInt("LcoinDropAmountNormalCharLevel_75", 10);
			LCOIN_DROP_AMOUNT_PREMIUM_CHAR_LEVEL_75 = CustomLCoinDrop.getInt("LcoinDropAmountPremiumCharLevel_75", 10);
			LCOIN_DROP_AMOUNT_NORMAL_CHAR_LEVEL_76 = CustomLCoinDrop.getInt("LcoinDropAmountNormalCharLevel_76", 11);
			LCOIN_DROP_AMOUNT_PREMIUM_CHAR_LEVEL_76 = CustomLCoinDrop.getInt("LcoinDropAmountPremiumCharLevel_76", 11);
			LCOIN_DROP_AMOUNT_NORMAL_CHAR_LEVEL_77 = CustomLCoinDrop.getInt("LcoinDropAmountNormalCharLevel_77", 12);
			LCOIN_DROP_AMOUNT_PREMIUM_CHAR_LEVEL_77 = CustomLCoinDrop.getInt("LcoinDropAmountPremiumCharLevel_77", 12);
			LCOIN_DROP_AMOUNT_NORMAL_CHAR_LEVEL_78 = CustomLCoinDrop.getInt("LcoinDropAmountNormalCharLevel_78", 13);
			LCOIN_DROP_AMOUNT_PREMIUM_CHAR_LEVEL_78 = CustomLCoinDrop.getInt("LcoinDropAmountPremiumCharLevel_78", 13);
			LCOIN_DROP_AMOUNT_NORMAL_CHAR_LEVEL_79 = CustomLCoinDrop.getInt("LcoinDropAmountNormalCharLevel_79", 14);
			LCOIN_DROP_AMOUNT_PREMIUM_CHAR_LEVEL_79 = CustomLCoinDrop.getInt("LcoinDropAmountPremiumCharLevel_79", 14);
			LCOIN_DROP_AMOUNT_NORMAL_CHAR_LEVEL_80 = CustomLCoinDrop.getInt("LcoinDropAmountNormalCharLevel_80", 15);
			LCOIN_DROP_AMOUNT_PREMIUM_CHAR_LEVEL_80 = CustomLCoinDrop.getInt("LcoinDropAmountPremiumCharLevel_80", 15);
			LCOIN_DROP_AMOUNT_NORMAL_CHAR_LEVEL_81 = CustomLCoinDrop.getInt("LcoinDropAmountNormalCharLevel_81", 16);
			LCOIN_DROP_AMOUNT_PREMIUM_CHAR_LEVEL_81 = CustomLCoinDrop.getInt("LcoinDropAmountPremiumCharLevel_81", 16);
			LCOIN_DROP_AMOUNT_NORMAL_CHAR_LEVEL_82 = CustomLCoinDrop.getInt("LcoinDropAmountNormalCharLevel_82", 17);
			LCOIN_DROP_AMOUNT_PREMIUM_CHAR_LEVEL_82 = CustomLCoinDrop.getInt("LcoinDropAmountPremiumCharLevel_82", 17);
			LCOIN_DROP_AMOUNT_NORMAL_CHAR_LEVEL_83 = CustomLCoinDrop.getInt("LcoinDropAmountNormalCharLevel_83", 18);
			LCOIN_DROP_AMOUNT_PREMIUM_CHAR_LEVEL_83 = CustomLCoinDrop.getInt("LcoinDropAmountPremiumCharLevel_83", 18);
			LCOIN_DROP_AMOUNT_NORMAL_CHAR_LEVEL_84 = CustomLCoinDrop.getInt("LcoinDropAmountNormalCharLevel_84", 19);
			LCOIN_DROP_AMOUNT_PREMIUM_CHAR_LEVEL_84 = CustomLCoinDrop.getInt("LcoinDropAmountPremiumCharLevel_84", 19);
			LCOIN_DROP_AMOUNT_NORMAL_CHAR_LEVEL_85 = CustomLCoinDrop.getInt("LcoinDropAmountNormalCharLevel_85", 20);
			LCOIN_DROP_AMOUNT_PREMIUM_CHAR_LEVEL_85 = CustomLCoinDrop.getInt("LcoinDropAmountPremiumCharLevel_85", 20);
			LCOIN_DROP_AMOUNT_NORMAL_CHAR_LEVEL_86 = CustomLCoinDrop.getInt("LcoinDropAmountNormalCharLevel_86", 21);
			LCOIN_DROP_AMOUNT_PREMIUM_CHAR_LEVEL_86 = CustomLCoinDrop.getInt("LcoinDropAmountPremiumCharLevel_86", 21);
			LCOIN_DROP_AMOUNT_NORMAL_CHAR_LEVEL_87 = CustomLCoinDrop.getInt("LcoinDropAmountNormalCharLevel_87", 22);
			LCOIN_DROP_AMOUNT_PREMIUM_CHAR_LEVEL_87 = CustomLCoinDrop.getInt("LcoinDropAmountPremiumCharLevel_87", 22);
			LCOIN_DROP_AMOUNT_NORMAL_CHAR_LEVEL_88 = CustomLCoinDrop.getInt("LcoinDropAmountNormalCharLevel_88", 23);
			LCOIN_DROP_AMOUNT_PREMIUM_CHAR_LEVEL_88 = CustomLCoinDrop.getInt("LcoinDropAmountPremiumCharLevel_88", 23);
			LCOIN_DROP_AMOUNT_NORMAL_CHAR_LEVEL_89 = CustomLCoinDrop.getInt("LcoinDropAmountNormalCharLevel_89", 24);
			LCOIN_DROP_AMOUNT_PREMIUM_CHAR_LEVEL_89 = CustomLCoinDrop.getInt("LcoinDropAmountPremiumCharLevel_89", 24);
			LCOIN_DROP_AMOUNT_NORMAL_CHAR_LEVEL_90_99 = CustomLCoinDrop.getInt("LcoinDropAmountNormalCharLevel_90_99", 25);
			LCOIN_DROP_AMOUNT_PREMIUM_CHAR_LEVEL_90_99 = CustomLCoinDrop.getInt("LcoinDropAmountPremiumCharLevel_90_99", 25);
			// Load FactionSystem config file (if exists)
			final PropertiesParser factionSystemConfig = new PropertiesParser(CUSTOM_FACTION_SYSTEM_CONFIG_FILE);
			String[] tempString;
			LOG_FACTION_DETAILS = factionSystemConfig.getBoolean("LogFactionDetails", false);
			FACTION_SYSTEM_ENABLED = factionSystemConfig.getBoolean("EnableFactionSystem", false);
			AUTO_FACTION_SELECTION = factionSystemConfig.getBoolean("AutoFactionSelection", true);
			MANUAL_FACTION_SELECTION = factionSystemConfig.getBoolean("ManualFactionSelection", false);
			FACTION_BALANCE_MAX_PERCENT = factionSystemConfig.getInt("FactionBalanceMaxPercent", 55);
			if (AUTO_FACTION_SELECTION && MANUAL_FACTION_SELECTION)
			{
				LOGGER.warning("Both AUTO_FACTION_SELECTION and MANUAL_FACTION_SELECTION are enabled. Defaulting to AUTO_FACTION_SELECTION.");
				MANUAL_FACTION_SELECTION = false;
			}
			ACTIVE_ZONE_COUNT = factionSystemConfig.getInt("ActiveZoneCount", 1);
			tempString = factionSystemConfig.getString("StartingLocation", "85332,16199,-1252").split(",");
			FACTION_STARTING_LOCATION = new Location(Integer.parseInt(tempString[0]), Integer.parseInt(tempString[1]), Integer.parseInt(tempString[2]));
			tempString = factionSystemConfig.getString("ManagerSpawnLocation", "85712,15974,-1260,26808").split(",");
			FACTION_MANAGER_LOCATION = new Location(Integer.parseInt(tempString[0]), Integer.parseInt(tempString[1]), Integer.parseInt(tempString[2]), tempString[3] != null ? Integer.parseInt(tempString[3]) : 0);
			tempString = factionSystemConfig.getString("FireBaseLocation", "45306,48878,-3058").split(",");
			FACTION_FIRE_BASE_LOCATION = new Location(Integer.parseInt(tempString[0]), Integer.parseInt(tempString[1]), Integer.parseInt(tempString[2]));
			tempString = factionSystemConfig.getString("WaterBaseLocation", "-44037,-113283,-237").split(",");
			FACTION_WATER_BASE_LOCATION = new Location(Integer.parseInt(tempString[0]), Integer.parseInt(tempString[1]), Integer.parseInt(tempString[2]));
			FACTION_FIRE_TEAM_NAME = factionSystemConfig.getString("FireTeamName", "Fire");
			FACTION_WATER_TEAM_NAME = factionSystemConfig.getString("WaterTeamName", "Water");
			FACTION_FIRE_NAME_COLOR = Integer.decode("0x" + factionSystemConfig.getString("FireNameColor", "00FF00"));
			FACTION_WATER_NAME_COLOR = Integer.decode("0x" + factionSystemConfig.getString("WaterNameColor", "0000FF"));
			FACTION_GUARDS_ENABLED = factionSystemConfig.getBoolean("EnableFactionGuards", true);
			FACTION_RESPAWN_AT_BASE = factionSystemConfig.getBoolean("RespawnAtFactionBase", true);
			FACTION_AUTO_NOBLESS = factionSystemConfig.getBoolean("FactionAutoNobless", false);
			FACTION_SPECIFIC_CHAT = factionSystemConfig.getBoolean("EnableFactionChat", true);
			FACTION_BALANCE_ONLINE_PLAYERS = factionSystemConfig.getBoolean("BalanceOnlinePlayers", true);
			FACTION_BALANCE_PLAYER_EXCEED_LIMIT = factionSystemConfig.getInt("BalancePlayerExceedLimit", 20);
			ZONE_CHANGE_INTERVAL = factionSystemConfig.getInt("ZoneChangeInterval", 30);
			PROTECTION_DURATION = factionSystemConfig.getInt("ProtectionDuration", 5);
			tempString = factionSystemConfig.getString("ZoneChangeAnnounceTimes", "5,1").split(",");
			ZONE_CHANGE_ANNOUNCE_TIMES = new int[]
			{
				Integer.parseInt(tempString[0]), Integer.parseInt(tempString[1])
			};
			INITIAL_ACTIVATION_DELAY_MINUTES = factionSystemConfig.getInt("InitialActivationDelayMinutes", 10);
			ENABLE_ACTIVATION_COUNTDOWN = factionSystemConfig.getBoolean("EnableActivationCountdown", false);
			ACTIVATION_COUNTDOWN_SECONDS = factionSystemConfig.getInt("ActivationCountdownSeconds", 10);
			MAX_PLAYERS_PER_ZONE = factionSystemConfig.getInt("MaxPlayersPerZone", 50);
			DEFEND_REWARD_INTERVAL = factionSystemConfig.getInt("DefendRewardInterval", 60);
			DEFEND_REWARD_LCOIN_AMOUNT = factionSystemConfig.getInt("DefendRewardLCoinAmount", 10);
			DEFEND_REWARD_MIN_TIME = factionSystemConfig.getInt("DefendRewardMinTime", 60);
			DEFEND_ZONE_RADIUS = factionSystemConfig.getInt("DefendZoneRadius", 5000);
			tempString = factionSystemConfig.getString("OutpostDestroyerReward", "50,50,20").split(",");
			OUTPOST_DESTROYER_REWARD_ADENA_AMOUNT = Integer.parseInt(tempString[0]);
			OUTPOST_DESTROYER_REWARD_LCOIN_AMOUNT = Integer.parseInt(tempString[1]);
			OUTPOST_DESTROYER_REWARD_LCOIN_CHANCE = Integer.parseInt(tempString[2]);
			OUTPOST_CAPTURE_REWARD_LCOIN_AMOUNT = factionSystemConfig.getInt("OutpostCaptureReward", 100);
			OUTPOST_DEFEND_SUCCESS_REWARD_LCOIN_AMOUNT = factionSystemConfig.getInt("OutpostDefendSuccessReward", 100);
			TELEPORT_CONFIRM_TIME = factionSystemConfig.getInt("TeleportConfirmTime", 15);
			MAX_PLAYERS_FOR_NOTIFICATIONS = factionSystemConfig.getInt("MaxPlayersForNotifications", 50);
			OUTPOST_LOW_HP_ANNOUNCE_THRESHOLD = factionSystemConfig.getInt("OutpostLowHPAnnounceThreshold", 40);
			TELEPORT_RANDOM_RADIUS = factionSystemConfig.getInt("TeleportRandomRadius", 50);
			// Guard settings for outposts
			OUTPOST_GUARD_ARCHER_COUNT = factionSystemConfig.getInt("OutpostGuardArcherCount", 3);
			OUTPOST_GUARD_MAGE_COUNT = factionSystemConfig.getInt("OutpostGuardMageCount", 3);
			OUTPOST_GUARD_WARRIOR_COUNT = factionSystemConfig.getInt("OutpostGuardWarriorCount", 3);
			NEUTRAL_GUARD_ARCHER_NPC_ID = factionSystemConfig.getInt("NeutralGuardArcherNpcId", 513);
			NEUTRAL_GUARD_MAGE_NPC_ID = factionSystemConfig.getInt("NeutralGuardMageNpcId", 515);
			NEUTRAL_GUARD_WARRIOR_NPC_ID = factionSystemConfig.getInt("NeutralGuardWarriorNpcId", 514);
			FIRE_GUARD_ARCHER_NPC_ID = factionSystemConfig.getInt("FireGuardArcherNpcId", 502);
			FIRE_GUARD_MAGE_NPC_ID = factionSystemConfig.getInt("FireGuardMageNpcId", 505);
			FIRE_GUARD_WARRIOR_NPC_ID = factionSystemConfig.getInt("FireGuardWarriorNpcId", 506);
			WATER_GUARD_ARCHER_NPC_ID = factionSystemConfig.getInt("WaterGuardArcherNpcId", 501);
			WATER_GUARD_MAGE_NPC_ID = factionSystemConfig.getInt("WaterGuardMageNpcId", 503);
			WATER_GUARD_WARRIOR_NPC_ID = factionSystemConfig.getInt("WaterGuardWarriorNpcId", 504);
			OUTPOST_GUARD_SPAWN_RADIUS = factionSystemConfig.getInt("OutpostGuardSpawnRadius", 200);
			OUTPOST_GUARD_RESPAWN_TIME = factionSystemConfig.getInt("OutpostGuardRespawnTime", 60);
			// Artifact settings
			ARTIFACT_DEFEND_DURATION = factionSystemConfig.getInt("ArtifactDefendDuration", 5); // Thời gian bảo vệ Artifact (phút)
			ARTIFACT_DEFEND_SUCCESS_REWARD_LCOIN_AMOUNT = factionSystemConfig.getInt("ArtifactDefendSuccessRewardLCoinAmount", 100); // Số lượng L-Coin nhận được khi bảo vệ Artifact thành công
			ARTIFACT_DEFEND_POINTS = factionSystemConfig.getInt("ArtifactDefendPoints", 50); // Điểm cá nhân nhận được khi bảo vệ Artifact thành công
			ARTIFACT_DEFEND_FACTION_POINTS = factionSystemConfig.getInt("ArtifactDefendFactionPoints", 30); // Điểm danh tiếng faction nhận được khi bảo vệ Artifact thành công
			// Load Faction War
			FACTION_WAR_ENABLED = factionSystemConfig.getBoolean("FactionWarEnabled", true);
			// FACTION_WAR_PVP_HWID_ENABLE = factionSystemConfig.getBoolean("FactionWarPvPHwidEnable", true);
			FACTION_WAR_START_POINTS = factionSystemConfig.getInt("FactionWarStartPoints", 0);
			FACTION_WAR_SCHEDULE = factionSystemConfig.getString("FactionWarSchedule", "08:00-10:00,14:00-18:00");
			FACTION_WAR_TIME_SLOTS = parseFactionWarSchedule(FACTION_WAR_SCHEDULE);
			// FACTION_WAR_START_HOUR = factionSystemConfig.getInt("FactionWarStartHour", 20);
			// FACTION_WAR_START_MINUTE = factionSystemConfig.getInt("FactionWarStartMinute", 0);
			// FACTION_WAR_END_HOUR = factionSystemConfig.getInt("FactionWarEndHour", 22);
			// FACTION_WAR_END_MINUTE = factionSystemConfig.getInt("FactionWarEndMinute", 0);
			FACTION_WAR_MIN_LEVEL = factionSystemConfig.getInt("FactionWarMinLevel", 80);
			FACTION_WAR_PVP_KILL_POINTS = factionSystemConfig.getInt("FactionWarPvPKillPoints", 1);
			FACTION_WAR_PVP_DEATH_POINTS = factionSystemConfig.getInt("FactionWarPvPDeathPoints", 1);
			FACTION_WAR_OUTPOST_KILL_POINTS = factionSystemConfig.getInt("FactionWarOutpostKillPoints", 25);
			FACTION_WAR_FORTRESS_SIEGE_ZONE_POINTS = factionSystemConfig.getInt("FactionWarFortressSiegeZonePoints", 25);
			FACTION_WAR_CASTLE_SIEGE_ZONE_POINTS = factionSystemConfig.getInt("FactionWarCastleSiegeZonePoints", 25);
			FACTION_WAR_ARTIFACT_CAPTURE_POINTS = factionSystemConfig.getInt("FactionWarCaptureArtifactPoints", 20);
			FACTION_WAR_ELITE_BOSS_KILL_POINTS = factionSystemConfig.getInt("FactionWarEliteBossKillPoints", 25);
			FACTION_WAR_TRUST_LEVEL_POINTS = factionSystemConfig.getIntArray("FactionWarTrustLevelPoints", ",", new int[]
			{
				1000, 2000, 3000, 4000, 5000, 6000
			});
			FACTION_WAR_TOP_1_REWARD = parseItemsList(factionSystemConfig.getString("FactionWarTop1Reward", "57,1000;57,1000"));
			FACTION_WAR_TOP_10_REWARD = parseItemsList(factionSystemConfig.getString("FactionWarTop10Reward", "57,1000;57,1000"));
			FACTION_WAR_TOP_50_REWARD = parseItemsList(factionSystemConfig.getString("FactionWarTop50Reward", "57,1000;57,1000"));
			FACTION_WAR_TOP_100_REWARD = parseItemsList(factionSystemConfig.getString("FactionWarTop100Reward", "57,1000;57,1000"));
			FACTION_WAR_TOP_300_REWARD = parseItemsList(factionSystemConfig.getString("FactionWarTop300Reward", "57,1000;57,1000"));
			FACTION_WAR_NOT_REWARD = parseItemsList(factionSystemConfig.getString("FactionWarNotTopReward", "57,1000;57,1000"));
			GVE_FRACTION_CHANGE_PERSONAL = factionSystemConfig.getInt("FractionChangePersonal", 100);
			GVE_FRACTION_CHANGE_CLAN = factionSystemConfig.getInt("FractionChangeClan", 1000);
			MAX_LOAD_LOCATIONS_SYSTEM = factionSystemConfig.getBoolean("MaxLoadLocationsSystem", false);
			FACTION_BALANCE_IN_LOCATIONS = factionSystemConfig.getBoolean("FactionBalanceInLocations", false);
			FACTION_BALANCE_IN_LOCATION_MIN_COUNT = factionSystemConfig.getInt("FactionBalanceInLocationMinCount", 10);
			FACTION_BALANCE_IN_LOCATION_MAX_PERCENT = factionSystemConfig.getInt("FactionBalanceInLocationMaxPercent", 55);
			FACTION_BALANCE_MIN_PERCENT_TO_CHANGE_PLAYER = factionSystemConfig.getInt("FactionBalanceMinPercentToChangePlayer", 49);
			FACTION_BALANCE_MIN_PERCENT_TO_CHANGE_CLAN = factionSystemConfig.getInt("FactionBalanceMinPercentToChangeClan", 55);
			String balanceLocations = factionSystemConfig.getString("BalanceLocations", "elf_village_zone:30;dark_elf_village_zone:30;xilenos_fortress_base:50");
			for (String entry : balanceLocations.split(";"))
			{
				String[] parts = entry.split(":");
				if (parts.length == 2)
				{
					BALANCE_LOCATIONS.put(parts[0].trim(), Integer.parseInt(parts[1].trim()));
					LOGGER.info("Loaded BALANCE_LOCATIONS: " + BALANCE_LOCATIONS);
				}
			}
			String comboKillMessages = factionSystemConfig.getString("ComboKillSystem", "3,<?winner?> is on MULTI KILL! <?count?> kills!;5,<?winner?> is on a Rampage with <?count?> kills!;7,<?winner?> is on a KILLING SPREE! <?count?> kills!;9,<?winner?> made a MEGA KILL! <?count?> kills!;12,HOLY SHIT! <?winner?> GOT ANOTHER ONE! <?count?> kills!;15,<?winner?> made a LUDICROUS KILL! <?count?> kills!;19,<?winner?> is a MAY THE FORCE! <?count?> kills!;24,<?winner?> is UNSTOPPABLE! <?count?> kills!;30,<?winner?> made a MONSTER KILL! <?count?> kills!;40,<?winner?> is ONE AND ONLY!<?count?> kills!;50,<?winner?> IS G O D L I K E !!!<?count?>");
			if (!comboKillMessages.isEmpty())
			{
				String[] messages = comboKillMessages.split(";");
				for (String message : messages)
				{
					String[] messageParts = message.split(",", 2);
					if (messageParts.length == 2)
					{
						try
						{
							int comboCount = Integer.parseInt(messageParts[0]);
							String messageText = messageParts[1];
							GVE_COMBO_KILL_MESSAGES.put(comboCount, messageText);
						}
						catch (NumberFormatException e)
						{
							LOGGER.warning("Format GvE Combo Kill invalid message: " + message);
						}
					}
				}
			}
			DROP_RATE_PENALTY_OPPOSING_FACTION = factionSystemConfig.getDouble("DropRatePenaltyOpposingFaction", 0.5);

			// Load performance settings
			CIRCUIT_BREAKER_MAX_FAILURES = factionSystemConfig.getInt("CircuitBreakerMaxFailures", 3);
			CIRCUIT_BREAKER_TIMEOUT_MINUTES = factionSystemConfig.getInt("CircuitBreakerTimeoutMinutes", 5);
			PERIODIC_CLEANUP_INTERVAL_MINUTES = factionSystemConfig.getInt("PeriodicCleanupIntervalMinutes", 5);
			HEALTH_CHECK_INTERVAL_MINUTES = factionSystemConfig.getInt("HealthCheckIntervalMinutes", 10);
			DATABASE_RETRY_ATTEMPTS = factionSystemConfig.getInt("DatabaseRetryAttempts", 3);
			DATABASE_RETRY_DELAY_MS = factionSystemConfig.getInt("DatabaseRetryDelayMs", 1000);
			// Load Morale Boost config file (if exists)
			final PropertiesParser moraleBoostConfig = new PropertiesParser(MORALE_BOOST_CONFIG_FILE);
			BOOST_MORALE_ENABLED = moraleBoostConfig.getBoolean("Enable", true);
			// Load Morale Effects (skillId, skillLevel)
			String[] moraleEffects = moraleBoostConfig.getString("MoraleEffect", "90017,1").split(";");
			MORALE_EFFECT = new int[moraleEffects.length][2];
			for (int i = 0; i < moraleEffects.length; i++)
			{
				String[] effect = moraleEffects[i].split(",");
				MORALE_EFFECT[i][0] = Integer.parseInt(effect[0]); // skillId
				MORALE_EFFECT[i][1] = Integer.parseInt(effect[1]); // skillLevel
			}
			CASTLE_SUCCESS_ATTACK_BOOST = moraleBoostConfig.getBoolean("CastleSuccessAttackBoost", true);
			CASTLE_SUCCESS_DEFENSE_BOOST = moraleBoostConfig.getBoolean("CastleSuccessDefenseBoost", true);
			FORTRESS_SUCCESS_ATTACK_BOOST = moraleBoostConfig.getBoolean("FortressSuccessAttackBoost", true);
			OUTPOST_DESTROY_BOOST = moraleBoostConfig.getBoolean("OutpostDestroyBoost", true);
			ARTIFACT_CAPTURE_BOOST = moraleBoostConfig.getBoolean("ArtifactCaptureBoost", true);
			BOSS_KILL_BOOST = moraleBoostConfig.getBoolean("BossKillBoost", true);
			// GVE Reward
			GIVE_ASSIST_ATTACK_DELAY = factionSystemConfig.getLong("GiveAssistAttackDelay", 300);
			FACTION_REWARD_LCOIN_PENALTY = factionSystemConfig.getDouble("FactionRewardLCoinPenalty", 0.0);
			GVE_KILL_PENALTY_REMOVE_TIME = factionSystemConfig.getLong("GveKillPenaltyRemoveTime", 86400); // 24 giờ
			GVE_KILL_PENALTY_COUNT = factionSystemConfig.getInt("GveKillPenaltyCount", 5);
			GVE_KILL_PENALTY_TIME = factionSystemConfig.getLong("GveKillPenaltyTime", 3600); // 1 giờ
			GVE_DEATH_PENALTY_REMOVE_TIME = factionSystemConfig.getLong("GveDeathPenaltyRemoveTime", 86400); // 24 giờ
			GVE_DEATH_PENALTY_COUNT = factionSystemConfig.getInt("GveDeathPenaltyCount", 5);
			GVE_DEATH_PENALTY_TIME = factionSystemConfig.getLong("GveDeathPenaltyTime", 3600); // 1 giờ
			GVE_SERIES_KILL_PENALTY_REMOVE_TIME = factionSystemConfig.getLong("GveSeriesKillPenaltyRemoveTime", 86400); // 24 giờ
			GVE_SERIES_KILL_COUNT = factionSystemConfig.getInt("GveSeriesKillCount", 3);
			GVE_SERIES_KILL_PENALTY_TIME = factionSystemConfig.getLong("GveSeriesKillPenaltyTime", 3600); // 1 giờ
			GVE_RATE_DROP_LCOIN = factionSystemConfig.getDouble("RateDropLCoin", 1.0);

			// GvE Skill Points Daily Limits
			GVE_SKILL_DAILY_LIMIT_BASE = factionSystemConfig.getInt("GveSkillDailyLimitBase", 150);
			GVE_SKILL_DAILY_LIMIT_1_STAR = factionSystemConfig.getInt("GveSkillDailyLimit1Star", 200);
			GVE_SKILL_DAILY_LIMIT_2_STAR = factionSystemConfig.getInt("GveSkillDailyLimit2Star", 250);
			GVE_SKILL_DAILY_LIMIT_3_STAR = factionSystemConfig.getInt("GveSkillDailyLimit3Star", 300);
			GVE_SKILL_DAILY_LIMIT_4_STAR = factionSystemConfig.getInt("GveSkillDailyLimit4Star", 400);
			GVE_SKILL_RESET_HOUR = factionSystemConfig.getInt("GveSkillResetHour", 6);
			GVE_SKILL_RESET_MINUTE = factionSystemConfig.getInt("GveSkillResetMinute", 30);

			// Zone Dominance System
			ENABLE_ZONE_DOMINANCE_SYSTEM = factionSystemConfig.getBoolean("EnableZoneDominanceSystem", true);
			ZONE_DOMINANCE_MINOR_GVE_BONUS = factionSystemConfig.getInt("ZoneDominanceMinorGveBonus", 5);
			ZONE_DOMINANCE_MINOR_ADENA_BONUS = factionSystemConfig.getInt("ZoneDominanceMinorAdenaBonus", 10);
			ZONE_DOMINANCE_MINOR_LCOIN_BONUS = factionSystemConfig.getInt("ZoneDominanceMinorLCoinBonus", 5);
			ZONE_DOMINANCE_MINOR_EXP_SP_BONUS = factionSystemConfig.getInt("ZoneDominanceMinorExpSpBonus", 5);
			ZONE_DOMINANCE_MAJOR_GVE_BONUS = factionSystemConfig.getInt("ZoneDominanceMajorGveBonus", 10);
			ZONE_DOMINANCE_MAJOR_ADENA_BONUS = factionSystemConfig.getInt("ZoneDominanceMajorAdenaBonus", 20);
			ZONE_DOMINANCE_MAJOR_LCOIN_BONUS = factionSystemConfig.getInt("ZoneDominanceMajorLCoinBonus", 10);
			ZONE_DOMINANCE_MAJOR_EXP_SP_BONUS = factionSystemConfig.getInt("ZoneDominanceMajorExpSpBonus", 10);
			ZONE_DOMINANCE_TOTAL_GVE_BONUS = factionSystemConfig.getInt("ZoneDominanceTotalGveBonus", 20);
			ZONE_DOMINANCE_TOTAL_ADENA_BONUS = factionSystemConfig.getInt("ZoneDominanceTotalAdenaBonus", 30);
			ZONE_DOMINANCE_TOTAL_LCOIN_BONUS = factionSystemConfig.getInt("ZoneDominanceTotalLCoinBonus", 15);
			ZONE_DOMINANCE_TOTAL_EXP_SP_BONUS = factionSystemConfig.getInt("ZoneDominanceTotalExpSpBonus", 15);
			ZONE_DOMINANCE_ANNOUNCEMENTS = factionSystemConfig.getBoolean("ZoneDominanceAnnouncements", true);
			// Load Faction Leader config file (if exists)
			final PropertiesParser factionLeaderConfig = new PropertiesParser(CUSTOM_FACTION_LEADER_CONFIG_FILE);
			FACTION_LEADER_ENABLED = factionLeaderConfig.getBoolean("Enabled", true);
			FACTION_LEADER_MIN_PERSONAL_EFFICIENCY = factionLeaderConfig.getInt("MinPersonalFactionEfficiency", 700);
			FACTION_LEADER_BROADCAST_RADIUS = factionLeaderConfig.getInt("BroadcastCommandRadius", 15000);
			FACTION_LEADER_SELF_VOTE_AVAILABLE = factionLeaderConfig.getBoolean("SelfVoteAvailable", false);
			FACTION_LEADER_CHECK_HWID = factionLeaderConfig.getBoolean("CheckHWID", true);
			FACTION_LEADER_MIN_LEVEL_FOR_VOTING = factionLeaderConfig.getInt("MinLevelForVoting", 76);
			FACTION_LEADER_AVAILABLE_INTERVALS_ENABLED = factionLeaderConfig.getBoolean("AvailableIntervalsEnabled", false);
			// Load skill và item sets
			FACTION_LEADER_SKILL_SET = new ArrayList<>();
			String[] skillSet = factionLeaderConfig.getString("LeaderSkillSet", "56048,1;56058,1;56059,1").split(";");
			for (String skill : skillSet)
			{
				String[] skillData = skill.split(",");
				FACTION_LEADER_SKILL_SET.add(new SkillHolder(Integer.parseInt(skillData[0]), Integer.parseInt(skillData[1])));
			}
			FACTION_LEADER_ITEM_SET = new ArrayList<>();
			String[] itemSet = factionLeaderConfig.getString("LeaderItemSet", "76020,1;75044,1000;75047,5;57,3000").split(";");
			for (String item : itemSet)
			{
				String[] itemData = item.split(",");
				FACTION_LEADER_ITEM_SET.add(new ItemHolder(Integer.parseInt(itemData[0]), Integer.parseInt(itemData[1])));
			}
			// Load available intervals
			FACTION_LEADER_AVAILABLE_INTERVALS = new ArrayList<>();
			String[] intervals = factionLeaderConfig.getString("AvailableIntervals", "19:00-20:00;21:00-22:00").split(";");
			for (String interval : intervals)
			{
				String[] times = interval.split("-");
				if (times.length != 2)
				{
					LOGGER.warning("Invalid time interval format: " + interval + ". Expected format: HH:mm-HH:mm. Skipping.");
					continue;
				}
				String[] fromTime = times[0].split(":");
				String[] toTime = times[1].split(":");
				if (fromTime.length != 2 || toTime.length != 2)
				{
					LOGGER.warning("Invalid time format in interval: " + interval + ". Expected format: HH:mm-HH:mm. Skipping.");
					continue;
				}
				try
				{
					int fromHour = Integer.parseInt(fromTime[0]);
					int fromMinute = Integer.parseInt(fromTime[1]);
					int toHour = Integer.parseInt(toTime[0]);
					int toMinute = Integer.parseInt(toTime[1]);
					// Kiểm tra giá trị hợp lệ
					if (fromHour < 0 || fromHour > 23 || fromMinute < 0 || fromMinute > 59 || toHour < 0 || toHour > 23 || toMinute < 0 || toMinute > 59)
					{
						LOGGER.warning("Invalid time interval: " + interval + ". Hours must be 0-23, minutes 0-59. Skipping.");
						continue;
					}
					FACTION_LEADER_AVAILABLE_INTERVALS.add(new int[]
					{
						fromHour, fromMinute, toHour, toMinute
					});
				}
				catch (NumberFormatException e)
				{
					LOGGER.warning("Failed to parse time interval: " + interval + ". " + e.getMessage() + ". Skipping.");
				}
			}
			// Parse giờ và phút trực tiếp từ cấu hình
			String[] startVoteTime = factionLeaderConfig.getString("StartVoteLocalTime", "14:00").split(":");
			FACTION_LEADER_START_VOTE_HOUR = Integer.parseInt(startVoteTime[0]);
			FACTION_LEADER_START_VOTE_MINUTE = Integer.parseInt(startVoteTime[1]);
			String[] startRequestTime = factionLeaderConfig.getString("StartRequestLocalTime", "14:00").split(":");
			FACTION_LEADER_START_REQUEST_HOUR = Integer.parseInt(startRequestTime[0]);
			FACTION_LEADER_START_REQUEST_MINUTE = Integer.parseInt(startRequestTime[1]);
			String[] endCycleTime = factionLeaderConfig.getString("EndCycleLocalTime", "19:00").split(":");
			FACTION_LEADER_END_CYCLE_HOUR = Integer.parseInt(endCycleTime[0]);
			FACTION_LEADER_END_CYCLE_MINUTE = Integer.parseInt(endCycleTime[1]);
			String[] nextCycleTime = factionLeaderConfig.getString("NextCycleLocalTime", "19:00").split(":");
			FACTION_LEADER_NEXT_CYCLE_HOUR = Integer.parseInt(nextCycleTime[0]);
			FACTION_LEADER_NEXT_CYCLE_MINUTE = Integer.parseInt(nextCycleTime[1]);
			FACTION_LEADER_ANNOUNCE_CYCLE_START = factionLeaderConfig.getString("AnnounceCycleStart", "The period of registration for Faction Leader elections has started!");
			// Load FortressSiegeEvent config file (if exists)
			final PropertiesParser FortressSiegeEventConfig = new PropertiesParser(CUSTOM_FORTRESS_SIEGE_EVENT_CONFIG_FILE);
			GVE_FORTRESS_SIEGE_ENABLED = FortressSiegeEventConfig.getBoolean("GveFortressSiegeEnabled", true);
			GVE_FORTRESS_SIEGE_DURATION = Duration.ofSeconds(Long.parseLong(FortressSiegeEventConfig.getString("GveFortressSiegeDuration", "3600"))); // 1 hour
			GVE_FORTRESS_SIEGE_INTERVAL = Duration.parse(convertToISO8601Duration(FortressSiegeEventConfig.getString("GveFortressSiegeInterval", "2h")));
			GVE_FORTRESS_ZONE_ENEMY_COUNT = FortressSiegeEventConfig.getInt("GveFortressZoneEnemyCount", 5);
			GVE_FORTRESS_ZONE_ENEMY_INTERVAL = Duration.parse(convertToISO8601Duration(FortressSiegeEventConfig.getString("GveFortressZoneEnemyInterval", "5m"))).toMillis();
			GVE_FORTRESS_REWARD_MAX = FortressSiegeEventConfig.getInt("GveFortressRewardMax", 450);
			GVE_FORTRESS_REWARD_INCREASE = FortressSiegeEventConfig.getInt("GveFortressRewardIncrease", 5000);
			GVE_FORTRESS_REWARD_INTERVAL = Duration.parse(convertToISO8601Duration(FortressSiegeEventConfig.getString("GveFortressRewardInterval", "10m"))).toMillis();
			GVE_FORTRESS_REWARD_TIME_IN_ZONE = Duration.parse(convertToISO8601Duration(FortressSiegeEventConfig.getString("GveFortressRewardTimeInZone", "10m")));
			parseRewardItems(FortressSiegeEventConfig, "GveFortressRewardWinner", GVE_FORTRESS_REWARD_WINNER, "57,100");
			parseRewardItems(FortressSiegeEventConfig, "GveFortressRewardLoser", GVE_FORTRESS_REWARD_LOSER, "57,50");
			GVE_FORTRESS_GREG_SENTRY_SPAWN_DELAY = Duration.parse(convertToISO8601Duration(FortressSiegeEventConfig.getString("GveFortressGregSentrySpawnDelay", "20m"))).toMillis();
			// Load OfflinePlay config file (if exists)
			final PropertiesParser offlinePlayConfig = new PropertiesParser(CUSTOM_OFFLINE_PLAY_CONFIG_FILE);
			ENABLE_OFFLINE_PLAY_COMMAND = offlinePlayConfig.getBoolean("EnableOfflinePlayCommand", false);
			OFFLINE_PLAY_PREMIUM = offlinePlayConfig.getBoolean("OfflinePlayPremium", false);
			OFFLINE_PLAY_LOGOUT_ON_DEATH = offlinePlayConfig.getBoolean("OfflinePlayLogoutOnDeath", true);
			OFFLINE_PLAY_LOGIN_MESSAGE = offlinePlayConfig.getString("OfflinePlayLoginMessage", "");
			OFFLINE_PLAY_SET_NAME_COLOR = offlinePlayConfig.getBoolean("OfflinePlaySetNameColor", false);
			OFFLINE_PLAY_NAME_COLOR = Integer.decode("0x" + offlinePlayConfig.getString("OfflinePlayNameColor", "808080"));
			OFFLINE_PLAY_ABNORMAL_EFFECTS.clear();
			final String offlinePlayAbnormalEffects = offlinePlayConfig.getString("OfflinePlayAbnormalEffect", "").trim();
			if (!offlinePlayAbnormalEffects.isEmpty())
			{
				for (String ave : offlinePlayAbnormalEffects.split(","))
				{
					OFFLINE_PLAY_ABNORMAL_EFFECTS.add(Enum.valueOf(AbnormalVisualEffect.class, ave.trim()));
				}
			}
			OFFLINE_PLAY_AUTO_RESURRECTION_ENABLED = offlinePlayConfig.getBoolean("OfflinePlayAutoResurrectionEnabled", true);
			OFFLINE_PLAY_LOGOUT_ON_INSUFFICIENT_LCOIN = offlinePlayConfig.getBoolean("OfflinePlayLogoutOnInsufficientLCoin", true);
			OFFLINE_PLAY_STORE_HUNTING_LOCATION = offlinePlayConfig.getBoolean("OfflinePlayStoreHuntingLocation", true);
			// Load OfflineTrade config file (if exists)
			final PropertiesParser OfflineTrade = new PropertiesParser(CUSTOM_OFFLINE_TRADE_CONFIG_FILE);
			OFFLINE_TRADE_ENABLE = OfflineTrade.getBoolean("OfflineTradeEnable", false);
			OFFLINE_CRAFT_ENABLE = OfflineTrade.getBoolean("OfflineCraftEnable", false);
			OFFLINE_MODE_IN_PEACE_ZONE = OfflineTrade.getBoolean("OfflineModeInPeaceZone", false);
			OFFLINE_MODE_NO_DAMAGE = OfflineTrade.getBoolean("OfflineModeNoDamage", false);
			OFFLINE_SET_NAME_COLOR = OfflineTrade.getBoolean("OfflineSetNameColor", false);
			OFFLINE_NAME_COLOR = Integer.decode("0x" + OfflineTrade.getString("OfflineNameColor", "808080"));
			OFFLINE_FAME = OfflineTrade.getBoolean("OfflineFame", true);
			RESTORE_OFFLINERS = OfflineTrade.getBoolean("RestoreOffliners", false);
			OFFLINE_MAX_DAYS = OfflineTrade.getInt("OfflineMaxDays", 10);
			OFFLINE_DISCONNECT_FINISHED = OfflineTrade.getBoolean("OfflineDisconnectFinished", true);
			STORE_OFFLINE_TRADE_IN_REALTIME = OfflineTrade.getBoolean("StoreOfflineTradeInRealtime", true);
			// Load PcCafe config file (if exists)
			final PropertiesParser PcCafe = new PropertiesParser(CUSTOM_PC_CAFE_CONFIG_FILE);
			PC_CAFE_ENABLED = PcCafe.getBoolean("PcCafeEnabled", false);
			PC_CAFE_ONLY_PREMIUM = PcCafe.getBoolean("PcCafeOnlyPremium", false);
			PC_CAFE_MAX_POINTS = PcCafe.getInt("MaxPcCafePoints", 200000);
			if (PC_CAFE_MAX_POINTS < 0)
			{
				PC_CAFE_MAX_POINTS = 0;
			}
			PC_CAFE_ENABLE_DOUBLE_POINTS = PcCafe.getBoolean("DoublingAcquisitionPoints", false);
			PC_CAFE_DOUBLE_POINTS_CHANCE = PcCafe.getInt("DoublingAcquisitionPointsChance", 1);
			if ((PC_CAFE_DOUBLE_POINTS_CHANCE < 0) || (PC_CAFE_DOUBLE_POINTS_CHANCE > 100))
			{
				PC_CAFE_DOUBLE_POINTS_CHANCE = 1;
			}
			PC_CAFE_POINT_RATE = PcCafe.getDouble("AcquisitionPointsRate", 1.0);
			PC_CAFE_RANDOM_POINT = PcCafe.getBoolean("AcquisitionPointsRandom", false);
			if (PC_CAFE_POINT_RATE < 0)
			{
				PC_CAFE_POINT_RATE = 1;
			}
			PC_CAFE_REWARD_LOW_EXP_KILLS = PcCafe.getBoolean("RewardLowExpKills", true);
			PC_CAFE_LOW_EXP_KILLS_CHANCE = PcCafe.getInt("RewardLowExpKillsChance", 50);
			if (PC_CAFE_LOW_EXP_KILLS_CHANCE < 0)
			{
				PC_CAFE_LOW_EXP_KILLS_CHANCE = 0;
			}
			if (PC_CAFE_LOW_EXP_KILLS_CHANCE > 100)
			{
				PC_CAFE_LOW_EXP_KILLS_CHANCE = 100;
			}
			// Load PremiumSystem config file (if exists)
			final PropertiesParser PremiumSystem = new PropertiesParser(CUSTOM_PREMIUM_SYSTEM_CONFIG_FILE);
			PREMIUM_SYSTEM_ENABLED = PremiumSystem.getBoolean("EnablePremiumSystem", false);
			ALL_PREMIUM_EVENT = PremiumSystem.getBoolean("AllPremiumEvent", false);
			ALL_RANDOM_CRAFT_PREMIUM_EVENT = PremiumSystem.getBoolean("AllRandomCraftPremiumEvent", false);
			PREMIUM_RATE_XP = PremiumSystem.getFloat("PremiumRateXp", 2);
			PREMIUM_RATE_SP = PremiumSystem.getFloat("PremiumRateSp", 2);
			PREMIUM_RATE_SAYHA_GRACE_XP = PremiumSystem.getFloat("PremiumRateSayhaGraceXp", 0);
			PREMIUM_RATE_LIMITED_SAYHA_GRACE_XP = PremiumSystem.getFloat("PremiumRateLimitedSayhaGraceXp", 0);
			PREMIUM_RATE_DROP_CHANCE = PremiumSystem.getFloat("PremiumRateDropChance", 2);
			PREMIUM_RATE_DROP_AMOUNT = PremiumSystem.getFloat("PremiumRateDropAmount", 1);
			PREMIUM_RATE_LCOIN_DROP_AMOUNT_ADD = PremiumSystem.getInt("PremiumRateLcoinDropAmountAdd", 3);
			PREMIUM_RATE_SPOIL_CHANCE = PremiumSystem.getFloat("PremiumRateSpoilChance", 2);
			PREMIUM_RATE_SPOIL_AMOUNT = PremiumSystem.getFloat("PremiumRateSpoilAmount", 1);
			final String[] premiumDropChanceMultiplier = PremiumSystem.getString("PremiumRateDropChanceByItemId", "").split(";");
			PREMIUM_RATE_DROP_CHANCE_BY_ID = new HashMap<>(premiumDropChanceMultiplier.length);
			if (!premiumDropChanceMultiplier[0].isEmpty())
			{
				for (String item : premiumDropChanceMultiplier)
				{
					final String[] itemSplit = item.split(",");
					if (itemSplit.length != 2)
					{
						LOGGER.warning(StringUtil.concat("Config.load(): invalid config property -> PremiumRateDropChanceByItemId \"", item, "\""));
					}
					else
					{
						try
						{
							PREMIUM_RATE_DROP_CHANCE_BY_ID.put(Integer.parseInt(itemSplit[0]), Float.parseFloat(itemSplit[1]));
						}
						catch (NumberFormatException nfe)
						{
							if (!item.isEmpty())
							{
								LOGGER.warning(StringUtil.concat("Config.load(): invalid config property -> PremiumRateDropChanceByItemId \"", item, "\""));
							}
						}
					}
				}
			}
			final String[] premiumDropAmountMultiplier = PremiumSystem.getString("PremiumRateDropAmountByItemId", "").split(";");
			PREMIUM_RATE_DROP_AMOUNT_BY_ID = new HashMap<>(premiumDropAmountMultiplier.length);
			if (!premiumDropAmountMultiplier[0].isEmpty())
			{
				for (String item : premiumDropAmountMultiplier)
				{
					final String[] itemSplit = item.split(",");
					if (itemSplit.length != 2)
					{
						LOGGER.warning(StringUtil.concat("Config.load(): invalid config property -> PremiumRateDropAmountByItemId \"", item, "\""));
					}
					else
					{
						try
						{
							PREMIUM_RATE_DROP_AMOUNT_BY_ID.put(Integer.parseInt(itemSplit[0]), Float.parseFloat(itemSplit[1]));
						}
						catch (NumberFormatException nfe)
						{
							if (!item.isEmpty())
							{
								LOGGER.warning(StringUtil.concat("Config.load(): invalid config property -> PremiumRateDropAmountByItemId \"", item, "\""));
							}
						}
					}
				}
			}
			PREMIUM_RATE_DEATH_LOST_XP = PremiumSystem.getFloat("PremiumRateDeathLostXp", 1);
			PREMIUM_RATE_NO_PENALTY_RESURRECTION_COST = PremiumSystem.getFloat("PremiumRateNoPenaltyResurrectionCost", 1);
			PREMIUM_RATE_RANDOM_CRAFT_HERB_POINT_AMOUNT = PremiumSystem.getFloat("PremiumRateRandomCraftHerbPointAmount", 1);
			PREMIUM_ONLY_FISHING = PremiumSystem.getBoolean("PremiumOnlyFishing", true);
			// Load PrivateStoreRange config file (if exists)
			final PropertiesParser PrivateStoreRange = new PropertiesParser(CUSTOM_PRIVATE_STORE_RANGE_CONFIG_FILE);
			SHOP_MIN_RANGE_FROM_PLAYER = PrivateStoreRange.getInt("ShopMinRangeFromPlayer", 50);
			SHOP_MIN_RANGE_FROM_NPC = PrivateStoreRange.getInt("ShopMinRangeFromNpc", 100);
			// Load PvpAnnounce config file (if exists)
			final PropertiesParser PvpAnnounce = new PropertiesParser(CUSTOM_PVP_ANNOUNCE_CONFIG_FILE);
			ANNOUNCE_PK_PVP = PvpAnnounce.getBoolean("AnnouncePkPvP", false);
			ANNOUNCE_PK_PVP_NORMAL_MESSAGE = PvpAnnounce.getBoolean("AnnouncePkPvPNormalMessage", true);
			ANNOUNCE_PK_MSG = PvpAnnounce.getString("AnnouncePkMsg", "$killer has slaughtered $target");
			ANNOUNCE_PVP_MSG = PvpAnnounce.getString("AnnouncePvpMsg", "$killer has defeated $target");
			// Load PvpRewardItem config file (if exists)
			final PropertiesParser PvpRewardItem = new PropertiesParser(CUSTOM_PVP_REWARD_ITEM_CONFIG_FILE);
			REWARD_PVP_ITEM = PvpRewardItem.getBoolean("RewardPvpItem", false);
			REWARD_PVP_ITEM_ID = PvpRewardItem.getInt("RewardPvpItemId", 57);
			REWARD_PVP_ITEM_AMOUNT = PvpRewardItem.getInt("RewardPvpItemAmount", 1000);
			REWARD_PVP_ITEM_MESSAGE = PvpRewardItem.getBoolean("RewardPvpItemMessage", true);
			REWARD_PK_ITEM = PvpRewardItem.getBoolean("RewardPkItem", false);
			REWARD_PK_ITEM_ID = PvpRewardItem.getInt("RewardPkItemId", 57);
			REWARD_PK_ITEM_AMOUNT = PvpRewardItem.getInt("RewardPkItemAmount", 500);
			REWARD_PK_ITEM_MESSAGE = PvpRewardItem.getBoolean("RewardPkItemMessage", true);
			DISABLE_REWARDS_IN_INSTANCES = PvpRewardItem.getBoolean("DisableRewardsInInstances", true);
			DISABLE_REWARDS_IN_PVP_ZONES = PvpRewardItem.getBoolean("DisableRewardsInPvpZones", true);
			// Load PvpRewardItem config file (if exists)
			final PropertiesParser PvpTitleColor = new PropertiesParser(CUSTOM_PVP_TITLE_CONFIG_FILE);
			PVP_COLOR_SYSTEM_ENABLED = PvpTitleColor.getBoolean("EnablePvPColorSystem", false);
			PVP_AMOUNT1 = PvpTitleColor.getInt("PvpAmount1", 500);
			PVP_AMOUNT2 = PvpTitleColor.getInt("PvpAmount2", 1000);
			PVP_AMOUNT3 = PvpTitleColor.getInt("PvpAmount3", 1500);
			PVP_AMOUNT4 = PvpTitleColor.getInt("PvpAmount4", 2500);
			PVP_AMOUNT5 = PvpTitleColor.getInt("PvpAmount5", 5000);
			NAME_COLOR_FOR_PVP_AMOUNT1 = Integer.decode("0x" + PvpTitleColor.getString("ColorForAmount1", "00FF00"));
			NAME_COLOR_FOR_PVP_AMOUNT2 = Integer.decode("0x" + PvpTitleColor.getString("ColorForAmount2", "00FF00"));
			NAME_COLOR_FOR_PVP_AMOUNT3 = Integer.decode("0x" + PvpTitleColor.getString("ColorForAmount3", "00FF00"));
			NAME_COLOR_FOR_PVP_AMOUNT4 = Integer.decode("0x" + PvpTitleColor.getString("ColorForAmount4", "00FF00"));
			NAME_COLOR_FOR_PVP_AMOUNT5 = Integer.decode("0x" + PvpTitleColor.getString("ColorForAmount5", "00FF00"));
			TITLE_FOR_PVP_AMOUNT1 = PvpTitleColor.getString("PvPTitleForAmount1", "Title");
			TITLE_FOR_PVP_AMOUNT2 = PvpTitleColor.getString("PvPTitleForAmount2", "Title");
			TITLE_FOR_PVP_AMOUNT3 = PvpTitleColor.getString("PvPTitleForAmount3", "Title");
			TITLE_FOR_PVP_AMOUNT4 = PvpTitleColor.getString("PvPTitleForAmount4", "Title");
			TITLE_FOR_PVP_AMOUNT5 = PvpTitleColor.getString("PvPTitleForAmount5", "Title");
			// Load RandomSpawns config file (if exists)
			final PropertiesParser RandomSpawns = new PropertiesParser(CUSTOM_RANDOM_SPAWNS_CONFIG_FILE);
			ENABLE_RANDOM_MONSTER_SPAWNS = RandomSpawns.getBoolean("EnableRandomMonsterSpawns", false);
			MOB_MAX_SPAWN_RANGE = RandomSpawns.getInt("MaxSpawnMobRange", 150);
			MOB_MIN_SPAWN_RANGE = MOB_MAX_SPAWN_RANGE * -1;
			if (ENABLE_RANDOM_MONSTER_SPAWNS)
			{
				final String[] mobsIds = RandomSpawns.getString("MobsSpawnNotRandom", "18812,18813,18814,22138").split(",");
				MOBS_LIST_NOT_RANDOM = new ArrayList<>(mobsIds.length);
				for (String id : mobsIds)
				{
					MOBS_LIST_NOT_RANDOM.add(Integer.parseInt(id));
				}
			}
			// Load SayuneForAll config file (if exists)
			final PropertiesParser SayuneForAll = new PropertiesParser(CUSTOM_SAYUNE_FOR_ALL_CONFIG_FILE);
			FREE_JUMPS_FOR_ALL = SayuneForAll.getBoolean("FreeJumpsForAll", false);
			// Load ScreenWelcomeMessage config file (if exists)
			final PropertiesParser ScreenWelcomeMessage = new PropertiesParser(CUSTOM_SCREEN_WELCOME_MESSAGE_CONFIG_FILE);
			WELCOME_MESSAGE_ENABLED = ScreenWelcomeMessage.getBoolean("ScreenWelcomeMessageEnable", false);
			WELCOME_MESSAGE_TEXT = ScreenWelcomeMessage.getString("ScreenWelcomeMessageText", "Welcome to our server!");
			WELCOME_MESSAGE_TIME = ScreenWelcomeMessage.getInt("ScreenWelcomeMessageTime", 10) * 1000;
			// Load SellBuffs config file (if exists)
			final PropertiesParser SellBuffs = new PropertiesParser(CUSTOM_SELL_BUFFS_CONFIG_FILE);
			SELLBUFF_ENABLED = SellBuffs.getBoolean("SellBuffEnable", false);
			SELLBUFF_MP_MULTIPLER = SellBuffs.getInt("MpCostMultipler", 1);
			SELLBUFF_PAYMENT_ID = SellBuffs.getInt("PaymentID", 57);
			SELLBUFF_MIN_PRICE = SellBuffs.getLong("MinimalPrice", 100000);
			SELLBUFF_MAX_PRICE = SellBuffs.getLong("MaximalPrice", 100000000);
			SELLBUFF_MAX_BUFFS = SellBuffs.getInt("MaxBuffs", 15);
			// Load ServerTime config file (if exists)
			final PropertiesParser ServerTime = new PropertiesParser(CUSTOM_SERVER_TIME_CONFIG_FILE);
			DISPLAY_SERVER_TIME = ServerTime.getBoolean("DisplayServerTime", false);
			// Load SchemeBuffer config file (if exists)
			final PropertiesParser SchemeBuffer = new PropertiesParser(CUSTOM_SCHEME_BUFFER_CONFIG_FILE);
			BUFFER_MAX_SCHEMES = SchemeBuffer.getInt("BufferMaxSchemesPerChar", 4);
			BUFFER_STATIC_BUFF_COST = SchemeBuffer.getInt("BufferStaticCostPerBuff", -1);
			// Load StartingLocation config file (if exists)
			final PropertiesParser StartingLocation = new PropertiesParser(CUSTOM_STARTING_LOCATION_CONFIG_FILE);
			CUSTOM_STARTING_LOC = StartingLocation.getBoolean("CustomStartingLocation", false);
			CUSTOM_STARTING_LOC_X = StartingLocation.getInt("CustomStartingLocX", 50821);
			CUSTOM_STARTING_LOC_Y = StartingLocation.getInt("CustomStartingLocY", 186527);
			CUSTOM_STARTING_LOC_Z = StartingLocation.getInt("CustomStartingLocZ", -3625);
			// Load VoteReward config file (if exists)
			final PropertiesParser VoteReward = new PropertiesParser(CUSTOM_VOTE_REWARD_CONFIG_FILE);
			// L2network.eu
			ALLOW_NETWORK_VOTE_REWARD = VoteReward.getBoolean("AllowNetworkVoteReward", false);
			NETWORK_SERVER_LINK = VoteReward.getString("NetworkServerLink", "");
			NETWORK_VOTES_DIFFERENCE = VoteReward.getInt("NetworkVotesDifference", 5);
			NETWORK_REWARD_CHECK_TIME = VoteReward.getInt("NetworkRewardCheckTime", 5);
			final String NETWORK_SMALL_REWARD_VALUE = VoteReward.getString("NetworkReward", "57,100000000;");
			final String[] NETWORK_small_reward_splitted_1 = NETWORK_SMALL_REWARD_VALUE.split(";");
			for (String i : NETWORK_small_reward_splitted_1)
			{
				final String[] NETWORK_small_reward_splitted_2 = i.split(",");
				NETWORK_REWARD.put(Integer.parseInt(NETWORK_small_reward_splitted_2[0]), Integer.parseInt(NETWORK_small_reward_splitted_2[1]));
			}
			NETWORK_DUALBOXES_ALLOWED = VoteReward.getInt("NetworkDualboxesAllowed", 1);
			ALLOW_NETWORK_GAME_SERVER_REPORT = VoteReward.getBoolean("AllowNetworkGameServerReport", false);
			// Topzone.com
			ALLOW_TOPZONE_VOTE_REWARD = VoteReward.getBoolean("AllowTopzoneVoteReward", false);
			TOPZONE_SERVER_LINK = VoteReward.getString("TopzoneServerLink", "");
			TOPZONE_VOTES_DIFFERENCE = VoteReward.getInt("TopzoneVotesDifference", 5);
			TOPZONE_REWARD_CHECK_TIME = VoteReward.getInt("TopzoneRewardCheckTime", 5);
			final String TOPZONE_SMALL_REWARD_VALUE = VoteReward.getString("TopzoneReward", "57,100000000;");
			final String[] topzone_small_reward_splitted_1 = TOPZONE_SMALL_REWARD_VALUE.split(";");
			for (String i : topzone_small_reward_splitted_1)
			{
				final String[] topzone_small_reward_splitted_2 = i.split(",");
				TOPZONE_REWARD.put(Integer.parseInt(topzone_small_reward_splitted_2[0]), Integer.parseInt(topzone_small_reward_splitted_2[1]));
			}
			TOPZONE_DUALBOXES_ALLOWED = VoteReward.getInt("TopzoneDualboxesAllowed", 1);
			ALLOW_TOPZONE_GAME_SERVER_REPORT = VoteReward.getBoolean("AllowTopzoneGameServerReport", false);
			// Hopzone.net
			ALLOW_HOPZONE_VOTE_REWARD = VoteReward.getBoolean("AllowHopzoneVoteReward", false);
			HOPZONE_SERVER_LINK = VoteReward.getString("HopzoneServerLink", "");
			HOPZONE_VOTES_DIFFERENCE = VoteReward.getInt("HopzoneVotesDifference", 5);
			HOPZONE_REWARD_CHECK_TIME = VoteReward.getInt("HopzoneRewardCheckTime", 5);
			final String HOPZONE_SMALL_REWARD_VALUE = VoteReward.getString("HopzoneReward", "57,100000000;");
			final String[] hopzone_small_reward_splitted_1 = HOPZONE_SMALL_REWARD_VALUE.split(";");
			for (String i : hopzone_small_reward_splitted_1)
			{
				final String[] hopzone_small_reward_splitted_2 = i.split(",");
				HOPZONE_REWARD.put(Integer.parseInt(hopzone_small_reward_splitted_2[0]), Integer.parseInt(hopzone_small_reward_splitted_2[1]));
			}
			HOPZONE_DUALBOXES_ALLOWED = VoteReward.getInt("HopzoneDualboxesAllowed", 1);
			ALLOW_HOPZONE_GAME_SERVER_REPORT = VoteReward.getBoolean("AllowHopzoneGameServerReport", false);
			// L2top.co
			ALLOW_L2TOP_VOTE_REWARD = VoteReward.getBoolean("AllowL2topVoteReward", false);
			L2TOP_SERVER_LINK = VoteReward.getString("L2topServerLink", "");
			L2TOP_VOTES_DIFFERENCE = VoteReward.getInt("L2topVotesDifference", 5);
			L2TOP_REWARD_CHECK_TIME = VoteReward.getInt("L2topRewardCheckTime", 5);
			final String L2TOP_SMALL_REWARD_VALUE = VoteReward.getString("L2topReward", "57,100000000;");
			final String[] l2top_small_reward_splitted_1 = L2TOP_SMALL_REWARD_VALUE.split(";");
			for (String i : l2top_small_reward_splitted_1)
			{
				final String[] l2top_small_reward_splitted_2 = i.split(",");
				L2TOP_REWARD.put(Integer.parseInt(l2top_small_reward_splitted_2[0]), Integer.parseInt(l2top_small_reward_splitted_2[1]));
			}
			L2TOP_DUALBOXES_ALLOWED = VoteReward.getInt("L2topDualboxesAllowed", 1);
			ALLOW_L2TOP_GAME_SERVER_REPORT = VoteReward.getBoolean("AllowL2topGameServerReport", false);
			ALLOW_L2JBRASIL_VOTE_REWARD = VoteReward.getBoolean("AllowL2JBrasilVoteReward", false);
			L2JBRASIL_SERVER_LINK = VoteReward.getString("L2JBrasilServerLink", "");
			L2JBRASIL_VOTES_DIFFERENCE = VoteReward.getInt("L2JBrasilVotesDifference", 5);
			L2JBRASIL_REWARD_CHECK_TIME = VoteReward.getInt("L2JBrasilRewardCheckTime", 5);
			String L2JBRASIL_SMALL_REWARD_VALUE = VoteReward.getString("L2JBrasilReward", "57,100000000;");
			String[] l2jbrasil_small_reward_splitted_1 = L2JBRASIL_SMALL_REWARD_VALUE.split(";");
			for (String i : l2jbrasil_small_reward_splitted_1)
			{
				String[] l2jbrasil_small_reward_splitted_2 = i.split(",");
				HOPZONE_REWARD.put(Integer.parseInt(l2jbrasil_small_reward_splitted_2[0]), Integer.parseInt(l2jbrasil_small_reward_splitted_2[1]));
			}
			L2JBRASIL_DUALBOXES_ALLOWED = VoteReward.getInt("L2JBrasilDualboxesAllowed", 1);
			ALLOW_L2JBRASIL_GAME_SERVER_REPORT = VoteReward.getBoolean("AllowL2JBrasilGameServerReport", false);
			// Load Custom Interface config file (if exists)
			final PropertiesParser customInterface = new PropertiesParser(CUSTOM_INTERFACE_CONFIG_FILE);
			CUSTOM_INTERFACE_ENABLED = customInterface.getBoolean("CustomInterfaceEnabled", false);
			AUTO_RANDOM_CRAFT_CHECK_DELAY = customInterface.getLong("AutoRandomCraftCheckDelay", 300);
			AUTO_RANDOM_CRAFT_ENABLED = customInterface.getBoolean("AutoRandomCraftEnabled", false);

			// Auto-resurrection configs
			AUTOPLAY_AUTO_RESURRECTION_ENABLED = customInterface.getBoolean("AutoPlayAutoResurrectionEnabled", true);
			AUTOPLAY_AUTO_RESURRECTION_COOLDOWN = customInterface.getInt("AutoPlayAutoResurrectionCooldown", 300); // 5 minutes
			AUTOPLAY_AUTO_RESURRECTION_MAX_USES_PER_DAY = customInterface.getInt("AutoPlayAutoResurrectionMaxUsesPerDay", 50);
			AUTOPLAY_AUTO_RESURRECTION_MAX_USES_PER_DAY_PREMIUM = customInterface.getInt("AutoPlayAutoResurrectionMaxUsesPerDayPremium", 200);
			AUTOPLAY_AUTO_RESURRECTION_RETURN_DELAY = customInterface.getInt("AutoPlayAutoResurrectionReturnDelay", 300); // 5 minutes
			AUTOPLAY_AUTO_RESURRECTION_COST_MULTIPLIER_STEP = customInterface.getFloat("AutoPlayAutoResurrectionCostMultiplierStep", 0.1f);
			AUTOPLAY_AUTO_RESURRECTION_COST_MULTIPLIER_CAP = customInterface.getFloat("AutoPlayAutoResurrectionCostMultiplierCap", 3.0f);
		}
		else if (SERVER_MODE == ServerMode.LOGIN)
		{
			final PropertiesParser ServerSettings = new PropertiesParser(LOGIN_CONFIG_FILE);
			GAME_SERVER_LOGIN_HOST = ServerSettings.getString("LoginHostname", "127.0.0.1");
			GAME_SERVER_LOGIN_PORT = ServerSettings.getInt("LoginPort", 9013);
			LOGIN_BIND_ADDRESS = ServerSettings.getString("LoginserverHostname", "0.0.0.0");
			PORT_LOGIN = ServerSettings.getInt("LoginserverPort", 2106);
			try
			{
				DATAPACK_ROOT = new File(ServerSettings.getString("DatapackRoot", ".").replaceAll("\\\\", "/")).getCanonicalFile();
			}
			catch (IOException e)
			{
				LOGGER.log(Level.WARNING, "Error setting datapack root!", e);
				DATAPACK_ROOT = new File(".");
			}
			ACCEPT_NEW_GAMESERVER = ServerSettings.getBoolean("AcceptNewGameServer", true);
			LOGIN_TRY_BEFORE_BAN = ServerSettings.getInt("LoginTryBeforeBan", 5);
			LOGIN_BLOCK_AFTER_BAN = ServerSettings.getInt("LoginBlockAfterBan", 900);
			LOGIN_SERVER_SCHEDULE_RESTART = ServerSettings.getBoolean("LoginRestartSchedule", false);
			LOGIN_SERVER_SCHEDULE_RESTART_TIME = ServerSettings.getLong("LoginRestartTime", 24);
			DATABASE_DRIVER = ServerSettings.getString("Driver", "org.mariadb.jdbc.Driver");
			DATABASE_URL = ServerSettings.getString("URL", "******************************");
			DATABASE_LOGIN = ServerSettings.getString("Login", "root");
			DATABASE_PASSWORD = ServerSettings.getString("Password", "");
			DATABASE_MAX_CONNECTIONS = ServerSettings.getInt("MaximumDbConnections", 10);
			BACKUP_DATABASE = ServerSettings.getBoolean("BackupDatabase", false);
			MYSQL_BIN_PATH = ServerSettings.getString("MySqlBinLocation", "C:/xampp/mysql/bin/");
			BACKUP_PATH = ServerSettings.getString("BackupPath", "../backup/");
			BACKUP_DAYS = ServerSettings.getInt("BackupDays", 30);
			SHOW_LICENCE = ServerSettings.getBoolean("ShowLicence", true);
			SHOW_PI_AGREEMENT = ServerSettings.getBoolean("ShowPIAgreement", false);
			AUTO_CREATE_ACCOUNTS = ServerSettings.getBoolean("AutoCreateAccounts", true);
			FLOOD_PROTECTION = ServerSettings.getBoolean("EnableFloodProtection", true);
			FAST_CONNECTION_LIMIT = ServerSettings.getInt("FastConnectionLimit", 15);
			NORMAL_CONNECTION_TIME = ServerSettings.getInt("NormalConnectionTime", 700);
			FAST_CONNECTION_TIME = ServerSettings.getInt("FastConnectionTime", 350);
			MAX_CONNECTION_PER_IP = ServerSettings.getInt("MaxConnectionPerIP", 50);
			ENABLE_CMD_LINE_LOGIN = ServerSettings.getBoolean("EnableCmdLineLogin", false);
			ONLY_CMD_LINE_LOGIN = ServerSettings.getBoolean("OnlyCmdLineLogin", false);
		}
		else
		{
			LOGGER.severe("Could not Load Config: server mode was not set!");
		}
	}
	
	public static boolean			OLY_ENABLED;
	public static int				OLY_MIN_LEVEL;
	public static String			OLY_PERIOD;
	public static int				OLY_PERIOD_MULTIPLIER;
	public static List<Integer>		OLY_COMPETITION_DAYS;
	public static int[]				OLY_CYCLE_END;
	public static int[]				OLY_COMPETITION_TIME;
	public static long				OLY_COMPETITION_PERIOD;
	public static long				OLY_BATTLE;
	public static int				OLY_CLASSED;
	public static int				OLY_NONCLASSED;
	public static List<ItemHolder>	OLY_WINNER_REWARD;
	public static List<ItemHolder>	OLY_LOSER_REWARD;
	public static int				OLY_COMP_RITEM;
	public static int				OLY_MIN_MATCHES;
	public static int				OLY_POINTS_WIN;
	public static int				OLY_POINTS_LOSE;
	public static int				OLY_POINTS_TIE;
	public static int				OLY_MAX_DAILY_MATCHES;
	public static boolean			OLY_LOG_FIGHTS;
	public static boolean			OLY_DEBUG_ENABLED;
	public static boolean			OLY_SHOW_MONTHLY_WINNERS;
	public static boolean			OLY_ANNOUNCE_GAMES;
	public static Set<Integer>		OLY_RESTRICTED_ITEMS_LIST	= new HashSet<>();
	public static int				OLY_WEAPON_ENCHANT_LIMIT;
	public static int				OLY_ARMOR_ENCHANT_LIMIT;
	public static int				OLY_WAIT_TIME;
	public static boolean			OLY_OFFLINE_ENDING_RULE;
	
	private static void loadOlympiadConfigs(String configFile)
	{
		final PropertiesParser properties = new PropertiesParser(configFile);
		String[] split;
		OLY_ENABLED = properties.getBoolean("OlyEnabled", true);
		OLY_MIN_LEVEL = properties.getInt("OlyMinLevel", 55);
		OLY_COMPETITION_DAYS = new ArrayList<>();
		for (String s : properties.getString("OlyCompetitionDays", "2,3,4,5,6").split(","))
		{
			OLY_COMPETITION_DAYS.add(Integer.parseInt(s));
		}
		OLY_PERIOD = properties.getString("OlyPeriod", "WEEK");
		OLY_PERIOD_MULTIPLIER = properties.getInt("OlyPeriodMultiplier", 1);
		split = properties.getString("OlyCycleEnd", "12:00").split(":");
		OLY_CYCLE_END = new int[]
		{
			Integer.parseInt(split[0]),
			Integer.parseInt(split[1]),
		};
		split = properties.getString("OlyCompetitionTime", "21:30").split(":");
		OLY_COMPETITION_TIME = new int[]
		{
			Integer.parseInt(split[0]),
			Integer.parseInt(split[1]),
		};
		//
		OLY_COMPETITION_PERIOD = properties.getLong("OlyCompetitionPeriod", 30) * 60000;
		OLY_BATTLE = properties.getLong("OlyBattle", 5) * 60000;
		OLY_CLASSED = properties.getInt("OlyClassedParticipants", 10);
		OLY_NONCLASSED = properties.getInt("OlyNonClassedParticipants", 20);
		OLY_WINNER_REWARD = parseItemsList(properties.getString("OlyWinReward", "none"));
		OLY_LOSER_REWARD = parseItemsList(properties.getString("OlyLoserReward", "none"));
		OLY_COMP_RITEM = properties.getInt("OlyCompRewItem", 45584);
		OLY_MIN_MATCHES = properties.getInt("OlyMinMatchesForHero", 10);
		OLY_POINTS_WIN = properties.getInt("OlyPointsWin", 30);
		OLY_POINTS_LOSE = properties.getInt("OlyPointsLose", 10);
		OLY_POINTS_TIE = properties.getInt("OlyPointsTie", 20);
		OLY_MAX_DAILY_MATCHES = properties.getInt("OlyMaxDailyMatches", 30);
		OLY_LOG_FIGHTS = properties.getBoolean("OlyLogFights", false);
		OLY_DEBUG_ENABLED = properties.getBoolean("OlyDebugEnabled", false);
		OLY_SHOW_MONTHLY_WINNERS = properties.getBoolean("OlyShowMonthlyWinners", true);
		OLY_ANNOUNCE_GAMES = properties.getBoolean("OlyAnnounceGames", true);
		final String olyRestrictedItems = properties.getString("OlyRestrictedItems", "").trim();
		if (!olyRestrictedItems.isEmpty())
		{
			final String[] olyRestrictedItemsSplit = olyRestrictedItems.split(",");
			OLY_RESTRICTED_ITEMS_LIST = new HashSet<>(olyRestrictedItemsSplit.length);
			for (String id : olyRestrictedItemsSplit)
			{
				OLY_RESTRICTED_ITEMS_LIST.add(Integer.parseInt(id));
			}
		}
		else // In case of reload with removal of all items ids.
		{
			OLY_RESTRICTED_ITEMS_LIST.clear();
		}
		OLY_WEAPON_ENCHANT_LIMIT = properties.getInt("OlyWeaponEnchantLimit", -1);
		OLY_ARMOR_ENCHANT_LIMIT = properties.getInt("OlyArmorEnchantLimit", -1);
		OLY_WAIT_TIME = properties.getInt("OlyWaitTime", 60);
		OLY_OFFLINE_ENDING_RULE = properties.getBoolean("OlyOfflineEndingRule", false);
	}
	
	/**
	 * Save hexadecimal ID of the server in the config file.<br>
	 * Check {@link #HEXID_FILE}.
	 *
	 * @param serverId
	 *            the ID of the server whose hexId to save
	 * @param hexId
	 *            the hexadecimal ID to store
	 */
	public static void saveHexid(int serverId, String hexId)
	{
		saveHexid(serverId, hexId, HEXID_FILE);
	}
	
	/**
	 * Save hexadecimal ID of the server in the config file.
	 *
	 * @param serverId
	 *            the ID of the server whose hexId to save
	 * @param hexId
	 *            the hexadecimal ID to store
	 * @param fileName
	 *            name of the config file
	 */
	public static void saveHexid(int serverId, String hexId, String fileName)
	{
		try
		{
			final Properties hexSetting = new Properties();
			final File file = new File(fileName);
			// Create a new empty file only if it doesn't exist
			if (!file.exists())
			{
				try (OutputStream out = new FileOutputStream(file))
				{
					hexSetting.setProperty("ServerID", String.valueOf(serverId));
					hexSetting.setProperty("HexID", hexId);
					hexSetting.store(out, "The HexId to Auth into LoginServer");
					LOGGER.log(Level.INFO, "Gameserver: Generated new HexID file for server id " + serverId + ".");
				}
			}
		}
		catch (Exception e)
		{
			LOGGER.warning(StringUtil.concat("Failed to save hex id to ", fileName, " File."));
			LOGGER.warning("Config: " + e.getMessage());
		}
	}
	
	/**
	 * Loads flood protector configurations.
	 *
	 * @param properties
	 *            the properties object containing the actual values of the flood protector configs
	 */
	private static void loadFloodProtectorConfigs(PropertiesParser properties)
	{
		loadFloodProtectorConfig(properties, FLOOD_PROTECTOR_USE_ITEM, "UseItem", 4);
		loadFloodProtectorConfig(properties, FLOOD_PROTECTOR_ROLL_DICE, "RollDice", 42);
		loadFloodProtectorConfig(properties, FLOOD_PROTECTOR_FIREWORK, "Firework", 42);
		loadFloodProtectorConfig(properties, FLOOD_PROTECTOR_ITEM_PET_SUMMON, "ItemPetSummon", 16);
		loadFloodProtectorConfig(properties, FLOOD_PROTECTOR_HERO_VOICE, "HeroVoice", 100);
		loadFloodProtectorConfig(properties, FLOOD_PROTECTOR_GLOBAL_CHAT, "GlobalChat", 5);
		loadFloodProtectorConfig(properties, FLOOD_PROTECTOR_SUBCLASS, "Subclass", 20);
		loadFloodProtectorConfig(properties, FLOOD_PROTECTOR_DROP_ITEM, "DropItem", 10);
		loadFloodProtectorConfig(properties, FLOOD_PROTECTOR_SERVER_BYPASS, "ServerBypass", 5);
		loadFloodProtectorConfig(properties, FLOOD_PROTECTOR_MULTISELL, "MultiSell", 1);
		loadFloodProtectorConfig(properties, FLOOD_PROTECTOR_TRANSACTION, "Transaction", 10);
		loadFloodProtectorConfig(properties, FLOOD_PROTECTOR_MANUFACTURE, "Manufacture", 3);
		loadFloodProtectorConfig(properties, FLOOD_PROTECTOR_MANOR, "Manor", 30);
		loadFloodProtectorConfig(properties, FLOOD_PROTECTOR_SENDMAIL, "SendMail", 100);
		loadFloodProtectorConfig(properties, FLOOD_PROTECTOR_CHARACTER_SELECT, "CharacterSelect", 30);
		loadFloodProtectorConfig(properties, FLOOD_PROTECTOR_ITEM_AUCTION, "ItemAuction", 9);
	}
	
	/**
	 * Loads single flood protector configuration.
	 *
	 * @param properties
	 *            properties file reader
	 * @param config
	 *            flood protector configuration instance
	 * @param configString
	 *            flood protector configuration string that determines for which flood protector configuration should be read
	 * @param defaultInterval
	 *            default flood protector interval
	 */
	private static void loadFloodProtectorConfig(PropertiesParser properties, FloodProtectorConfig config, String configString, int defaultInterval)
	{
		config.FLOOD_PROTECTION_INTERVAL = properties.getInt("FloodProtector" + configString + "Interval", defaultInterval);
		config.LOG_FLOODING = properties.getBoolean("FloodProtector" + configString + "LogFlooding", false);
		config.PUNISHMENT_LIMIT = properties.getInt("FloodProtector" + configString + "PunishmentLimit", 0);
		config.PUNISHMENT_TYPE = properties.getString("FloodProtector" + configString + "PunishmentType", "none");
		config.PUNISHMENT_TIME = properties.getInt("FloodProtector" + configString + "PunishmentTime", 0) * 60000;
	}
	
	public static int getServerTypeId(String[] serverTypes)
	{
		int serverType = 0;
		for (String cType : serverTypes)
		{
			switch (cType.trim().toLowerCase())
			{
				case "normal":
				{
					serverType |= 0x01;
					break;
				}
				case "relax":
				{
					serverType |= 0x02;
					break;
				}
				case "test":
				{
					serverType |= 0x04;
					break;
				}
				case "broad":
				{
					serverType |= 0x08;
					break;
				}
				case "restricted":
				{
					serverType |= 0x10;
					break;
				}
				case "event":
				{
					serverType |= 0x20;
					break;
				}
				case "free":
				{
					serverType |= 0x40;
					break;
				}
				case "world":
				{
					serverType |= 0x100;
					break;
				}
				case "new":
				{
					serverType |= 0x200;
					break;
				}
				case "essence":
				{
					serverType |= 0x400;
					break;
				}
			}
		}
		return serverType;
	}
	
	/**
	 * @param line
	 *            the string line to parse
	 * @return a parsed float array
	 */
	private static float[] parseConfigLine(String line)
	{
		final String[] propertySplit = line.split(",");
		final float[] ret = new float[propertySplit.length];
		int i = 0;
		for (String value : propertySplit)
		{
			ret[i++] = Float.parseFloat(value);
		}
		return ret;
	}
	
	/**
	 * Parse a config value from its string representation to a two-dimensional int array.<br>
	 * The format of the value to be parsed should be as follows: "item1Id,item1Amount;item2Id,item2Amount;...itemNId,itemNAmount".
	 *
	 * @param line
	 *            the value of the parameter to parse
	 * @return the parsed list or {@code null} if nothing was parsed
	 */
	private static List<ItemHolder> parseItemsList(String line)
	{
		final String[] propertySplit = line.split(";");
		if (line.equalsIgnoreCase("none") || (propertySplit.length == 0))
		{
			// nothing to do here
			return null;
		}
		String[] valueSplit;
		final List<ItemHolder> result = new ArrayList<>(propertySplit.length);
		for (String value : propertySplit)
		{
			valueSplit = value.split(",");
			if (valueSplit.length != 2)
			{
				LOGGER.warning("parseItemsList[Config.load()]: invalid entry -> " + valueSplit[0] + ", should be itemId,itemNumber. Skipping to the next entry in the list.");
				continue;
			}
			int itemId = -1;
			try
			{
				itemId = Integer.parseInt(valueSplit[0]);
			}
			catch (NumberFormatException e)
			{
				LOGGER.warning("parseItemsList[Config.load()]: invalid itemId -> " + valueSplit[0] + ", value must be an integer. Skipping to the next entry in the list.");
				continue;
			}
			int count = -1;
			try
			{
				count = Integer.parseInt(valueSplit[1]);
			}
			catch (NumberFormatException e)
			{
				LOGGER.warning("parseItemsList[Config.load()]: invalid item number -> " + valueSplit[1] + ", value must be an integer. Skipping to the next entry in the list.");
				continue;
			}
			if ((itemId > 0) && (count > 0))
			{
				result.add(new ItemHolder(itemId, count));
			}
		}
		return result;
	}
	
	private static class IPConfigData implements IXmlReader
	{
		private static final List<String>	_subnets	= new ArrayList<>(5);
		private static final List<String>	_hosts		= new ArrayList<>(5);
		
		public IPConfigData()
		{
			load();
		}
		
		@Override
		public void load()
		{
			final File f = new File(IPCONFIG_FILE);
			if (f.exists())
			{
				LOGGER.info("Network Config: ipconfig.xml exists using manual configuration...");
				parseFile(new File(IPCONFIG_FILE));
			}
			else // Auto configuration...
			{
				LOGGER.info("Network Config: ipconfig.xml doesn't exists using automatic configuration...");
				autoIpConfig();
			}
		}
		
		@Override
		public void parseDocument(Document doc, File f)
		{
			NamedNodeMap attrs;
			for (Node n = doc.getFirstChild(); n != null; n = n.getNextSibling())
			{
				if ("gameserver".equalsIgnoreCase(n.getNodeName()))
				{
					for (Node d = n.getFirstChild(); d != null; d = d.getNextSibling())
					{
						if ("define".equalsIgnoreCase(d.getNodeName()))
						{
							attrs = d.getAttributes();
							_subnets.add(attrs.getNamedItem("subnet").getNodeValue());
							_hosts.add(attrs.getNamedItem("address").getNodeValue());
							if (_hosts.size() != _subnets.size())
							{
								LOGGER.warning("Failed to Load " + IPCONFIG_FILE + " File - subnets does not match server addresses.");
							}
						}
					}
					final Node att = n.getAttributes().getNamedItem("address");
					if (att == null)
					{
						LOGGER.warning("Failed to load " + IPCONFIG_FILE + " file - default server address is missing.");
						_hosts.add("127.0.0.1");
					}
					else
					{
						_hosts.add(att.getNodeValue());
					}
					_subnets.add("0.0.0.0/0");
				}
			}
		}
		
		protected void autoIpConfig()
		{
			String externalIp = "127.0.0.1";
			try
			{
				final URL autoIp = new URL("http://ip1.dynupdate.no-ip.com:8245/");
				try (BufferedReader in = new BufferedReader(new InputStreamReader(autoIp.openStream())))
				{
					externalIp = in.readLine();
				}
			}
			catch (IOException e)
			{
				LOGGER.log(Level.INFO, "Failed to connect to api.externalip.net please check your internet connection using 127.0.0.1!");
				externalIp = "127.0.0.1";
			}
			try
			{
				final Enumeration<NetworkInterface> niList = NetworkInterface.getNetworkInterfaces();
				while (niList.hasMoreElements())
				{
					final NetworkInterface ni = niList.nextElement();
					if (!ni.isUp() || ni.isVirtual())
					{
						continue;
					}
					if (!ni.isLoopback() && ((ni.getHardwareAddress() == null) || (ni.getHardwareAddress().length != 6)))
					{
						continue;
					}
					for (InterfaceAddress ia : ni.getInterfaceAddresses())
					{
						if (ia.getAddress() instanceof Inet6Address)
						{
							continue;
						}
						final String hostAddress = ia.getAddress().getHostAddress();
						final int subnetPrefixLength = ia.getNetworkPrefixLength();
						final int subnetMaskInt = IntStream.rangeClosed(1, subnetPrefixLength).reduce((r, e) -> (r << 1) + 1).orElse(0) << (32 - subnetPrefixLength);
						final int hostAddressInt = Arrays.stream(hostAddress.split("\\.")).mapToInt(Integer::parseInt).reduce((r, e) -> (r << 8) + e).orElse(0);
						final int subnetAddressInt = hostAddressInt & subnetMaskInt;
						final String subnetAddress = ((subnetAddressInt >> 24) & 0xFF) + "." + ((subnetAddressInt >> 16) & 0xFF) + "." + ((subnetAddressInt >> 8) & 0xFF) + "." + (subnetAddressInt & 0xFF);
						final String subnet = subnetAddress + '/' + subnetPrefixLength;
						if (!_subnets.contains(subnet) && !subnet.equals("0.0.0.0/0"))
						{
							_subnets.add(subnet);
							_hosts.add(hostAddress);
							LOGGER.info("Network Config: Adding new subnet: " + subnet + " address: " + hostAddress);
						}
					}
				}
				// External host and subnet
				_hosts.add(externalIp);
				_subnets.add("0.0.0.0/0");
				LOGGER.info("Network Config: Adding new subnet: 0.0.0.0/0 address: " + externalIp);
			}
			catch (SocketException e)
			{
				LOGGER.log(Level.INFO, "Network Config: Configuration failed please configure manually using ipconfig.xml", e);
				System.exit(0);
			}
		}
		
		protected List<String> getSubnets()
		{
			if (_subnets.isEmpty())
			{
				return Arrays.asList("0.0.0.0/0");
			}
			return _subnets;
		}
		
		protected List<String> getHosts()
		{
			if (_hosts.isEmpty())
			{
				return Arrays.asList("127.0.0.1");
			}
			return _hosts;
		}
	}
	
	private static List<int[]> parseFactionWarSchedule(String schedule)
	{
		List<int[]> timeSlots = new ArrayList<>();
		if (schedule == null || schedule.trim().isEmpty())
		{
			// Mặc định nếu không có cấu hình
			timeSlots.add(new int[]
			{
				8, 0, 10, 0
			}); // 08:00-10:00
			timeSlots.add(new int[]
			{
				14, 0, 18, 0
			}); // 14:00-18:00
			return timeSlots;
		}
		String[] slots = schedule.split(",");
		for (String slot : slots)
		{
			String[] times = slot.trim().split("-");
			if (times.length != 2)
			{
				continue; // Bỏ qua nếu định dạng không đúng
			}
			String[] startTime = times[0].split(":");
			String[] endTime = times[1].split(":");
			if (startTime.length != 2 || endTime.length != 2)
			{
				continue; // Bỏ qua nếu định dạng không đúng
			}
			try
			{
				int startHour = Integer.parseInt(startTime[0]);
				int startMinute = Integer.parseInt(startTime[1]);
				int endHour = Integer.parseInt(endTime[0]);
				int endMinute = Integer.parseInt(endTime[1]);
				timeSlots.add(new int[]
				{
					startHour, startMinute, endHour, endMinute
				});
			}
			catch (NumberFormatException e)
			{
				LOGGER.warning("Invalid Faction War schedule format: " + slot);
			}
		}
		return timeSlots;
	}
	
	private static void parseRewardItems(PropertiesParser properties, String key, Map<Integer, Integer> targetMap, String defaultValue)
	{
		String rewardString = properties.getString(key, defaultValue);
		String[] rewardItems = rewardString.split(";");
		for (String item : rewardItems)
		{
			String[] itemSplit = item.split(",");
			if (itemSplit.length != 2)
			{
				LOGGER.warning("Invalid reward entry for " + key + ": " + item + ". Expected format: itemId,amount");
				continue;
			}
			try
			{
				int itemId = Integer.parseInt(itemSplit[0]);
				int amount = Integer.parseInt(itemSplit[1]);
				targetMap.put(itemId, amount);
			}
			catch (NumberFormatException e)
			{
				LOGGER.warning("Invalid number format in " + key + ": " + item);
			}
		}
	}
	
	private static String convertToISO8601Duration(String durationStr)
	{
		if (durationStr == null || durationStr.isEmpty())
		{
			return "PT0S"; // Default to 0 seconds if empty
		}
		durationStr = durationStr.trim().toLowerCase();
		if (durationStr.endsWith("h"))
		{
			String hours = durationStr.replace("h", "");
			return "PT" + hours + "H";
		}
		else if (durationStr.endsWith("m"))
		{
			String minutes = durationStr.replace("m", "");
			return "PT" + minutes + "M";
		}
		else if (durationStr.endsWith("s"))
		{
			String seconds = durationStr.replace("s", "");
			return "PT" + seconds + "S";
		}
		else
		{
			// If the format is already in ISO-8601 (e.g., PT2H), return as is
			if (durationStr.startsWith("PT"))
			{
				return durationStr;
			}
			// Default to seconds if no unit is specified
			return "PT" + durationStr + "S";
		}
	}
}
