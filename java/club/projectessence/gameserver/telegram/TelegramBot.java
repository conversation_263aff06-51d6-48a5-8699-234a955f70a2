package club.projectessence.gameserver.telegram;

import club.projectessence.Config;
import club.projectessence.commons.concurrent.ThreadPool;
import club.projectessence.gameserver.data.ItemTable;
import club.projectessence.gameserver.enums.PrivateStoreType;
import club.projectessence.gameserver.model.World;
import club.projectessence.gameserver.model.actor.Creature;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.model.items.Item;
import club.projectessence.gameserver.model.variables.PlayerVariables;
import club.projectessence.gameserver.network.serverpackets.primeshop.ExBRGamePoint;
import club.projectessence.gameserver.util.Util;
import club.projectessence.commons.database.DatabaseFactory;
import org.telegram.telegrambots.bots.TelegramLongPollingBot;
import org.telegram.telegrambots.meta.api.methods.send.SendMessage;
import org.telegram.telegrambots.meta.api.objects.Message;
import org.telegram.telegrambots.meta.api.objects.Update;
import org.telegram.telegrambots.meta.exceptions.TelegramApiException;
import org.telegram.telegrambots.meta.exceptions.TelegramApiRequestException;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.logging.Logger;
import java.util.logging.Level;
import java.util.regex.Pattern;
import java.util.regex.Matcher;

/**
 * <AUTHOR>
 */

public final class TelegramBot extends TelegramLongPollingBot {
	private static final Logger LOGGER = Logger.getLogger(TelegramBot.class.getName());
	private static final Set<String> _pendingChatIds = ConcurrentHashMap.newKeySet();
	// ChatId, PlayerName
	private static final Map<String, String> _pendingNames = new ConcurrentHashMap<>();
	// ChatId, Code
	private static final Map<String, String> _pendingCodes = new ConcurrentHashMap<>();

	private static final Set<TelegramMessage> _pendingMessages = ConcurrentHashMap.newKeySet();

	// Prime Points Management
	private static final Set<String> _authorizedAdmins = ConcurrentHashMap.newKeySet();
	private static final Map<String, Long> _lastCommandTime = new ConcurrentHashMap<>();
	private static final Pattern ADDPRIME_PATTERN = Pattern.compile("^/addprime\\s+(\\w+)\\s+(\\d+)$", Pattern.CASE_INSENSITIVE);
	private static final Pattern ADDPRIME_ACC_PATTERN = Pattern.compile("^/addprime_acc\\s+(\\w+)\\s+(\\d+)$", Pattern.CASE_INSENSITIVE);

	static {
		// Initialize authorized admins from config
		initializeAuthorizedAdmins();
	}

	public TelegramBot() {
		if (Config.TELEGRAM_ENABLED) {
			ThreadPool.get().scheduleAtFixedRate(new SendMessagesTask(), 1500, 1500);
		}
	}

	/**
	 * Initialize authorized admins from config
	 */
	private static void initializeAuthorizedAdmins() {
		_authorizedAdmins.clear();

		if (!Config.TELEGRAM_ADMIN_CHAT_IDS.isEmpty()) {
			String[] chatIds = Config.TELEGRAM_ADMIN_CHAT_IDS.split(",");
			for (String chatId : chatIds) {
				String trimmedChatId = chatId.trim();
				if (!trimmedChatId.isEmpty()) {
					_authorizedAdmins.add(trimmedChatId);
				}
			}
		}

		LOGGER.info("TelegramBot: Authorized admins initialized. Count: " + _authorizedAdmins.size());
		if (_authorizedAdmins.isEmpty()) {
			LOGGER.warning("TelegramBot: No authorized admins configured! Prime Points commands will not work.");
			LOGGER.warning("Add admin chat IDs to TelegramAdminChatIds in Telegram.ini config file.");
		}
	}

	public void sendDeathNotification(PlayerInstance player, Creature killer) {
		String message = killer.getName() + " [Lv. " + killer.getLevel() + "]";
		if (killer.isPlayer() && (killer.getClan() != null)) {
			message += " [Clan: " + killer.getClan().getName() + "]";
		}
		message = player.getName() + " died. Killed by: " + message;
		addMessageToQueue(player, message);
	}

	public void sendDisconnectNotification(PlayerInstance player) {
		addMessageToQueue(player, player.getName() + " disconnected.");
	}

	public void sendPrivateStoreNotification(PlayerInstance player, PlayerInstance otherPlayer, PrivateStoreType storeType, int itemId, long itemCount, int enchantLevel, boolean isAugmented) {
		String message = otherPlayer.getName() + " ";
		boolean isSellStore;
		switch (storeType) {
			case SELL:
			case SELL_MANAGE:
			case PACKAGE_SELL: {
				isSellStore = true;
				break;
			}
			case BUY:
			case BUY_MANAGE: {
				isSellStore = false;
				break;
			}
			default: {
				return;
			}
		}
		final Item itemTemplate = ItemTable.getInstance().getTemplate(itemId);
		message += (isSellStore ? "bought" : "sold");
		if (itemCount > 1) {
			message += " x" + itemCount;
		}
		if (isAugmented) {
			message += " Aug.";
		}
		if (enchantLevel > 0) {
			message += " +" + enchantLevel;
		}
		message += " " + itemTemplate.getName();
		if (itemTemplate.getAdditionalName() != null) {
			message += " (" + itemTemplate.getAdditionalName() + ")";
		}
		message += " " + (isSellStore ? "from " : "to ") + player.getName();
		addMessageToQueue(player, message);
	}

	private void addMessageToQueue(PlayerInstance player, String message) {
		final PlayerVariables vars = player.getVariables();
		String chatId = vars.getString(PlayerVariables.TELEGRAM_CHAT_ID, "");
		if (!chatId.isEmpty()) {
			addMessageToQueue(chatId, message);
		}
	}

	private void addMessageToQueue(String chatId, String message) {
		if (!Config.TELEGRAM_ENABLED) {
			return;
		}
		_pendingMessages.add(new TelegramMessage(chatId, message));
	}

	@Override
	public void onUpdateReceived(Update update) {
		if (!Config.TELEGRAM_ENABLED) {
			return;
		}
		final Message message = update.getMessage();
		if (!message.hasText()) {
			return;
		}
		// Handle admin commands first
		final String chatId = message.getChatId().toString();
		final String messageText = message.getText();

		// Check for Prime Points admin commands
		if (isAuthorizedAdmin(chatId)) {
			if (handleAdminCommand(chatId, messageText)) {
				return; // Command was handled
			}
		}

		// start and ask for name
		if (messageText.equals("/start")) {
			if (!_pendingChatIds.contains(chatId)) {
				_pendingChatIds.add(chatId);
				addMessageToQueue(chatId, "Enter your character name (case sensitive):\nUse /abort to cancel verification.");
			}
			return;
		}
		// abort binding that is in process
		else if (messageText.equals("/abort")) {
			boolean chatIdRemoved = _pendingChatIds.remove(chatId);
			boolean codeRemoved = _pendingCodes.remove(chatId) != null;
			boolean nameRemoved = _pendingNames.remove(chatId) != null;
			if (chatIdRemoved || codeRemoved || nameRemoved) {
				addMessageToQueue(chatId, "Successful.\nUse /start to begin character verification.");
				return;
			}
		} else // Char name
		{
			if (_pendingChatIds.contains(chatId) && !_pendingNames.containsKey(chatId) && !_pendingCodes.containsKey(chatId)) {
				// Set name
				final String pendingName = message.getText();
				if (pendingName != null) {
					final PlayerInstance player = World.getInstance().getPlayer(pendingName);
					if (player != null) {
						if (player.getVariables().getString(PlayerVariables.TELEGRAM_CHAT_ID, "").isEmpty()) {
							final String code = Util.generateRandomString(6).toUpperCase();
							_pendingCodes.put(chatId, code);
							_pendingNames.put(chatId, pendingName);
							addMessageToQueue(chatId, "Enter verification code In Game (ALT+B):    " + code + "\nThe code is case sensitive.\nUse /abort to cancel verification.");
						} else {
							_pendingChatIds.remove(chatId);
							_pendingCodes.remove(chatId);
							_pendingNames.remove(chatId);
							addMessageToQueue(chatId, "This character is already linked to telegram. You can reset it in ALT+B In game.\nUse /start to begin verification");
						}
						return;
					}
					addMessageToQueue(chatId, "Character is not online. Please enter your character name (case sensitive):\nUse /abort to cancel verification.");
					return;
				}
			}
		}
	}

	@Override
	public String getBotUsername() {
		return Config.TELEGRAM_BOT_USERNAME;
	}

	@Override
	public String getBotToken() {
		return Config.TELEGRAM_BOT_TOKEN;
	}

	public boolean tryVerifyPlayer(PlayerInstance player, String inputCode) {
		String chatId = null;
		for (Entry<String, String> entry : _pendingNames.entrySet()) {
			if (entry.getValue().equals(player.getName())) {
				chatId = entry.getKey();
				break;
			}
		}
		if (chatId == null) {
			return false;
		}
		if (!_pendingChatIds.contains(chatId)) {
			return false;
		}
		String correctCode = _pendingCodes.get(chatId);
		if (correctCode == null) {
			return false;
		}
		if (correctCode.equals(inputCode)) {
			player.getVariables().set(PlayerVariables.TELEGRAM_CHAT_ID, chatId);
			_pendingChatIds.remove(chatId);
			_pendingNames.remove(chatId);
			_pendingCodes.remove(chatId);
			addMessageToQueue(chatId, "Character " + player.getName() + " successfuly verified.");
			return true;
		}
		return false;
	}

	/**
	 * Check if chat ID is authorized admin
	 */
	private boolean isAuthorizedAdmin(String chatId) {
		return _authorizedAdmins.contains(chatId);
	}

	/**
	 * Handle admin commands for Prime Points management
	 */
	private boolean handleAdminCommand(String chatId, String messageText) {
		try {
			// Check rate limiting
			if (!checkRateLimit(chatId)) {
				addMessageToQueue(chatId, "⏰ Vui lòng đợi " + (Config.TELEGRAM_COMMAND_COOLDOWN/1000) + " giây trước khi sử dụng lệnh tiếp theo.");
				return true;
			}

			// Handle /addprime command
			if (messageText.startsWith("/addprime ")) {
				return handleAddPrimeCommand(chatId, messageText);
			}

			// Handle /addprime_acc command
			if (messageText.startsWith("/addprime_acc ")) {
				return handleAddPrimeAccountCommand(chatId, messageText);
			}

			// Handle /help command
			if (messageText.equals("/help")) {
				sendHelpMessage(chatId);
				return true;
			}

			// Not an admin command
			return false;

		} catch (Exception e) {
			LOGGER.log(Level.SEVERE, "Error handling admin command", e);
			addMessageToQueue(chatId, "❌ Đã xảy ra lỗi khi xử lý lệnh.");
			return true;
		}
	}

	/**
	 * Check rate limiting for commands
	 */
	private boolean checkRateLimit(String chatId) {
		long currentTime = System.currentTimeMillis();
		Long lastTime = _lastCommandTime.get(chatId);

		if (lastTime != null && (currentTime - lastTime) < Config.TELEGRAM_COMMAND_COOLDOWN) {
			return false;
		}

		_lastCommandTime.put(chatId, currentTime);
		return true;
	}

	/**
	 * Handle /addprime command
	 */
	private boolean handleAddPrimeCommand(String chatId, String messageText) {
		Matcher matcher = ADDPRIME_PATTERN.matcher(messageText.trim());
		if (!matcher.matches()) {
			addMessageToQueue(chatId, "❌ Cú pháp sai. Sử dụng: /addprime <tên_nhân_vật> <số_prime_points>");
			return true;
		}

		String characterName = matcher.group(1);
		int primePoints;

		try {
			primePoints = Integer.parseInt(matcher.group(2));
			if (primePoints <= 0 || primePoints > Config.TELEGRAM_MAX_PRIME_POINTS_PER_COMMAND) {
				addMessageToQueue(chatId, "❌ Số Prime Points phải từ 1 đến " + Config.TELEGRAM_MAX_PRIME_POINTS_PER_COMMAND + ".");
				return true;
			}
		} catch (NumberFormatException e) {
			addMessageToQueue(chatId, "❌ Số Prime Points không hợp lệ.");
			return true;
		}

		// Find player online
		PlayerInstance player = World.getInstance().getPlayer(characterName);
		if (player != null && player.isOnline()) {
			// Player is online - direct update
			addPrimePointsToOnlinePlayer(chatId, player, primePoints);
		} else {
			// Player is offline - update via database
			addPrimePointsToOfflinePlayer(chatId, characterName, primePoints);
		}

		return true;
	}

	/**
	 * Handle /addprime_acc command
	 */
	private boolean handleAddPrimeAccountCommand(String chatId, String messageText) {
		Matcher matcher = ADDPRIME_ACC_PATTERN.matcher(messageText.trim());
		if (!matcher.matches()) {
			addMessageToQueue(chatId, "❌ Cú pháp sai. Sử dụng: /addprime_acc <tên_account> <số_prime_points>");
			return true;
		}

		String accountName = matcher.group(1);
		int primePoints;

		try {
			primePoints = Integer.parseInt(matcher.group(2));
			if (primePoints <= 0 || primePoints > Config.TELEGRAM_MAX_PRIME_POINTS_PER_COMMAND) {
				addMessageToQueue(chatId, "❌ Số Prime Points phải từ 1 đến " + Config.TELEGRAM_MAX_PRIME_POINTS_PER_COMMAND + ".");
				return true;
			}
		} catch (NumberFormatException e) {
			addMessageToQueue(chatId, "❌ Số Prime Points không hợp lệ.");
			return true;
		}

		addPrimePointsToAccount(chatId, accountName, primePoints);
		return true;
	}

	/**
	 * Add Prime Points to online player
	 */
	private void addPrimePointsToOnlinePlayer(String chatId, PlayerInstance player, int primePoints) {
		try {
			int currentPoints = player.getPrimePoints();
			int newPoints = Math.min(currentPoints + primePoints, Integer.MAX_VALUE);

			player.setPrimePoints(newPoints);

			// Send real-time update packet to player
			player.sendPacket(new ExBRGamePoint(player));

			// Send notification to player
			player.sendMessage("🎁 Bạn đã nhận được " + primePoints + " Prime Points từ Admin qua Telegram Bot!");

			// Log the action
			LOGGER.info("TelegramBot: Admin added " + primePoints + " Prime Points to online player " + player.getName());

			// Send success message to Telegram
			addMessageToQueue(chatId,
				"✅ Đã thêm " + primePoints + " Prime Points cho nhân vật " + player.getName() + " (đang online)\n" +
				"📊 Prime Points hiện tại: " + newPoints);

		} catch (Exception e) {
			LOGGER.log(Level.SEVERE, "Error adding Prime Points to online player", e);
			addMessageToQueue(chatId, "❌ Lỗi khi thêm Prime Points cho nhân vật đang online.");
		}
	}

	/**
	 * Add Prime Points to offline player (by character name)
	 */
	private void addPrimePointsToOfflinePlayer(String chatId, String characterName, int primePoints) {
		try {
			// Get account name from character name
			String accountName = getAccountNameByCharacter(characterName);
			if (accountName == null) {
				addMessageToQueue(chatId, "❌ Không tìm thấy nhân vật: " + characterName);
				return;
			}

			// Update Prime Points in database
			boolean success = updateOfflinePlayerPrimePoints(accountName, primePoints, true);
			if (success) {
				LOGGER.info("TelegramBot: Admin added " + primePoints + " Prime Points to offline player " + characterName + " (account: " + accountName + ")");
				addMessageToQueue(chatId,
					"✅ Đã thêm " + primePoints + " Prime Points cho nhân vật " + characterName + " (offline)\n" +
					"ℹ️ Người chơi sẽ thấy Prime Points khi đăng nhập lại.");
			} else {
				addMessageToQueue(chatId, "❌ Lỗi khi cập nhật Prime Points trong database.");
			}

		} catch (Exception e) {
			LOGGER.log(Level.SEVERE, "Error adding Prime Points to offline player", e);
			addMessageToQueue(chatId, "❌ Lỗi khi thêm Prime Points cho nhân vật offline.");
		}
	}

	/**
	 * Add Prime Points directly to account
	 */
	private void addPrimePointsToAccount(String chatId, String accountName, int primePoints) {
		try {
			// Check if any character from this account is online
			PlayerInstance onlinePlayer = findOnlinePlayerByAccount(accountName);

			if (onlinePlayer != null) {
				// Account has online player - direct update
				addPrimePointsToOnlinePlayer(chatId, onlinePlayer, primePoints);
				return;
			}

			// Account is offline - update via database
			boolean success = updateOfflinePlayerPrimePoints(accountName, primePoints, true);
			if (success) {
				LOGGER.info("TelegramBot: Admin added " + primePoints + " Prime Points to account " + accountName);
				addMessageToQueue(chatId,
					"✅ Đã thêm " + primePoints + " Prime Points cho account " + accountName + "\n" +
					"ℹ️ Người chơi sẽ thấy Prime Points khi đăng nhập lại.");
			} else {
				addMessageToQueue(chatId, "❌ Lỗi khi cập nhật Prime Points trong database.");
			}

		} catch (Exception e) {
			LOGGER.log(Level.SEVERE, "Error adding Prime Points to account", e);
			addMessageToQueue(chatId, "❌ Lỗi khi thêm Prime Points cho account.");
		}
	}

	/**
	 * Get account name by character name
	 */
	private String getAccountNameByCharacter(String characterName) {
		try (Connection con = DatabaseFactory.getConnection();
			 PreparedStatement ps = con.prepareStatement("SELECT account_name FROM characters WHERE char_name = ?")) {

			ps.setString(1, characterName);
			try (ResultSet rs = ps.executeQuery()) {
				if (rs.next()) {
					return rs.getString("account_name");
				}
			}
		} catch (SQLException e) {
			LOGGER.log(Level.SEVERE, "Error getting account name for character: " + characterName, e);
		}
		return null;
	}

	/**
	 * Find online player by account name
	 */
	private PlayerInstance findOnlinePlayerByAccount(String accountName) {
		for (PlayerInstance player : World.getInstance().getPlayers()) {
			if (player != null && player.isOnline() && accountName.equals(player.getAccountName())) {
				return player;
			}
		}
		return null;
	}

	/**
	 * Update Prime Points for offline player (reuse existing method from AdminPrimePoints)
	 */
	private boolean updateOfflinePlayerPrimePoints(String accountName, int points, boolean isIncrease) {
		try (Connection con = DatabaseFactory.getConnection();
			 PreparedStatement checkExistence = con.prepareStatement("SELECT COUNT(*) FROM account_gsdata WHERE var = 'PRIME_POINTS' AND account_name = ?");
			 PreparedStatement updatePrimePoints = con.prepareStatement("UPDATE account_gsdata SET value = GREATEST(value + ?, 0) WHERE var = 'PRIME_POINTS' AND account_name = ?");
			 PreparedStatement insertPrimePoints = con.prepareStatement("INSERT INTO account_gsdata (account_name, var, value) VALUES (?, 'PRIME_POINTS', ?)")) {

			// Check if record exists
			checkExistence.setString(1, accountName);
			try (ResultSet rs = checkExistence.executeQuery()) {
				if (rs.next() && rs.getInt(1) == 0) {
					// Record doesn't exist, insert new one
					insertPrimePoints.setString(1, accountName);
					insertPrimePoints.setInt(2, isIncrease ? points : Math.max(points, 0));
					insertPrimePoints.executeUpdate();
					return true;
				}
			}

			// Record exists, update it
			updatePrimePoints.setInt(1, isIncrease ? points : points);
			updatePrimePoints.setString(2, accountName);
			int rowsUpdated = updatePrimePoints.executeUpdate();
			return rowsUpdated > 0;

		} catch (SQLException e) {
			LOGGER.log(Level.SEVERE, "Error updating offline player Prime Points", e);
			return false;
		}
	}

	/**
	 * Send help message
	 */
	private void sendHelpMessage(String chatId) {
		String helpText =
			"🤖 **GVE Prime Points Bot**\n\n" +
			"📋 **Danh sách lệnh:**\n" +
			"• `/addprime <tên_nhân_vật> <số_points>` - Thêm Prime Points cho nhân vật\n" +
			"• `/addprime_acc <tên_account> <số_points>` - Thêm Prime Points cho account\n" +
			"• `/help` - Hiển thị trợ giúp\n\n" +
			"⚠️ **Lưu ý:**\n" +
			"• Số Prime Points: 1-" + Config.TELEGRAM_MAX_PRIME_POINTS_PER_COMMAND + "\n" +
			"• Cooldown: " + (Config.TELEGRAM_COMMAND_COOLDOWN/1000) + " giây giữa các lệnh\n" +
			"• Chỉ Admin được phép sử dụng\n\n" +
			"✅ **Ví dụ:**\n" +
			"`/addprime PlayerName 1000`\n" +
			"`/addprime_acc account123 5000`";

		addMessageToQueue(chatId, helpText);
	}

	/**
	 * Add authorized admin (for runtime management)
	 */
	public static void addAuthorizedAdmin(String chatId) {
		_authorizedAdmins.add(chatId);
		LOGGER.info("Added authorized admin: " + chatId);
	}

	/**
	 * Remove authorized admin (for runtime management)
	 */
	public static void removeAuthorizedAdmin(String chatId) {
		boolean removed = _authorizedAdmins.remove(chatId);
		if (removed) {
			LOGGER.info("Removed authorized admin: " + chatId);
		}
	}

	private final class SendMessagesTask implements Runnable {
		@Override
		public void run() {
			try {
				final List<TelegramMessage> toDelete = new ArrayList<>();
				int i = 0;
				for (TelegramMessage message : _pendingMessages) {
					i++;
					if (i >= 25) // Telegram API 30 msgs / second
					{
						break;
					}
					try {
						final SendMessage response = new SendMessage();
						response.setChatId(message.getChatId());
						response.setText(message.getMessage());
						execute(response);
					} catch (TelegramApiRequestException e) {
						LOGGER.warning(getClass().getSimpleName() + ": Error response: " + e.getApiResponse() + " Text: " + message.getMessage() + "");
					} catch (TelegramApiException e) {
						LOGGER.warning(getClass().getSimpleName() + ": Unhandled exception: " + e.getMessage());
						e.printStackTrace();
					}
					toDelete.add(message);
				}
				for (TelegramMessage message : toDelete) {
					_pendingMessages.remove(message);
				}
			} catch (Exception e) {
				e.printStackTrace();
			}
		}
	}
}
