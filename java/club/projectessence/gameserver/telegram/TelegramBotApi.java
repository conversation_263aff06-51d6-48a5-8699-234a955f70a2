package club.projectessence.gameserver.telegram;

import club.projectessence.Config;
import org.telegram.telegrambots.meta.TelegramBotsApi;
import org.telegram.telegrambots.meta.exceptions.TelegramApiException;
import org.telegram.telegrambots.updatesreceivers.DefaultBotSession;

import java.util.logging.Logger;

/**
 * <AUTHOR>
 */

public final class TelegramBotApi {
	private static final Logger LOGGER = Logger.getLogger(TelegramBotApi.class.getName());

	private static TelegramBotsApi _api;
	private static TelegramBot _bot;

	private TelegramBotApi() {
		_bot = new TelegramBot();
		if (Config.TELEGRAM_ENABLED) {
			try {
				_api = new TelegramBotsApi(DefaultBotSession.class);
				_api.registerBot(_bot);
			} catch (TelegramApiException e) {
				LOGGER.warning(getClass().getSimpleName() + ": Failed to initialize.");
				e.printStackTrace();
			}
		}
	}

	public static TelegramBotApi getInstance() {
		return Singleton.INSTANCE;
	}

	public TelegramBotsApi getApi() {
		return _api;
	}

	public TelegramBot getBot() {
		return _bot;
	}

	private static final class Singleton {
		private static final TelegramBotApi INSTANCE = new TelegramBotApi();
	}
}
