/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.taskmanager;

import club.projectessence.Config;
import club.projectessence.commons.concurrent.ThreadPool;
import club.projectessence.gameserver.Shutdown;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;

import java.util.Map;
import java.util.Map.Entry;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 */
public class PlayerAutoSaveTaskManager {
	private static final Map<PlayerInstance, Long> PLAYER_TIMES = new ConcurrentHashMap<>();
	private static boolean _working = false;

	public PlayerAutoSaveTaskManager() {
		ThreadPool.get().scheduleAtFixedRate(() ->
		{
			if (_working) {
				return;
			}
			if (Shutdown.IS_SHUTDOWNING) {
				return;
			}

			_working = true;

			final long time = System.currentTimeMillis();
			for (Entry<PlayerInstance, Long> entry : PLAYER_TIMES.entrySet()) {
				if (Shutdown.IS_SHUTDOWNING) {
					return;
				}
				if (time > entry.getValue().longValue()) {
					final PlayerInstance player = entry.getKey();
					if ((player != null) && player.isOnline()) {
						player.autoSave();
						PLAYER_TIMES.put(entry.getKey(), time + Config.CHAR_DATA_STORE_INTERVAL);
					}
				}
			}

			_working = false;
		}, 1000, 1000);
	}

	public static PlayerAutoSaveTaskManager getInstance() {
		return SingletonHolder.INSTANCE;
	}

	public void add(PlayerInstance player) {
		PLAYER_TIMES.put(player, System.currentTimeMillis() + Config.CHAR_DATA_STORE_INTERVAL);
	}

	public void remove(PlayerInstance player) {
		PLAYER_TIMES.remove(player);
	}

	private static class SingletonHolder {
		protected static final PlayerAutoSaveTaskManager INSTANCE = new PlayerAutoSaveTaskManager();
	}
}