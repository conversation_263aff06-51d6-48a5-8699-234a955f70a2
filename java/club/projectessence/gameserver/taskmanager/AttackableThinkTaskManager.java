/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.taskmanager;

import club.projectessence.Config;
import club.projectessence.commons.concurrent.ThreadPool;
import club.projectessence.gameserver.ai.CreatureAI;
import club.projectessence.gameserver.model.actor.Attackable;

import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR> Benetis
 */
public class AttackableThinkTaskManager {
	private final SetWrapper _attackables[];
	private int _nextPool = 0;

	private final int THREAD_COUNT = Config.ATTACKABLE_AI_THREAD_COUNT;

	public AttackableThinkTaskManager() {
		_attackables = new SetWrapper[THREAD_COUNT];
		for (int i = 0; i < THREAD_COUNT; i++) {
			_attackables[i] = new SetWrapper();
		}
		for (int i = 0; i < THREAD_COUNT; i++) {
			ThreadPool.get().scheduleAtFixedRate(new AttackableTask(i), 1000, 1000);
		}
	}

	private class AttackableTask implements Runnable {
		private final int _i;

		protected AttackableTask(int index) {
			_i = index;
		}

		@Override
		public void run() {
			CreatureAI ai;
			for (Attackable attackable : _attackables[_i].attackables) {
				if (attackable.hasAI()) {
					ai = attackable.getAI();
					if (ai != null) {
						ai.onEvtThink();
					} else {
						remove(attackable);
					}
				} else {
					remove(attackable);
				}
			}
		}
	}

	public static AttackableThinkTaskManager getInstance() {
		return SingletonHolder.INSTANCE;
	}

	public void add(Attackable attackable) {
		int next = Math.abs(_nextPool++ % THREAD_COUNT);
		_attackables[next].attackables.add(attackable);
	}

	public void remove(Attackable attackable) {
		for (int i = 0; i < THREAD_COUNT; i++) {
			_attackables[i].attackables.remove(attackable);
		}
	}

	private static class SingletonHolder {
		protected static final AttackableThinkTaskManager INSTANCE = new AttackableThinkTaskManager();
	}

	public void printDebug() {
		System.out.println("--- " + getClass().getSimpleName() + " ---");
		for (int i = 0; i < THREAD_COUNT; i++) {
			System.out.println("List " + i + ": " + _attackables[i].attackables.size() + " players.");
		}
	}

	private class SetWrapper {
		protected final Set<Attackable> attackables;

		protected SetWrapper() {
			attackables = ConcurrentHashMap.newKeySet();
		}
	}
}
