/*
 * This file is part of the L2J Mobius project.
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.taskmanager.autoplay;

import java.awt.Color;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ScheduledFuture;
import java.util.logging.Logger;
import java.util.stream.Collectors;

import club.projectessence.Config;
import club.projectessence.commons.concurrent.ThreadPool;
import club.projectessence.gameserver.data.xml.ActionData;
import club.projectessence.gameserver.enums.NextActionType;
import club.projectessence.gameserver.geoengine.GeoEngine;
import club.projectessence.gameserver.handler.IPlayerActionHandler;
import club.projectessence.gameserver.handler.PlayerActionHandler;
import club.projectessence.gameserver.model.ActionDataHolder;
import club.projectessence.gameserver.model.World;
import club.projectessence.gameserver.model.WorldObject;
import club.projectessence.gameserver.model.actor.Creature;
import club.projectessence.gameserver.model.actor.Npc;
import club.projectessence.gameserver.model.actor.Summon;
import club.projectessence.gameserver.model.actor.instance.MonsterInstance;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.model.holders.AutoPlaySettingsHolder;
import club.projectessence.gameserver.model.interfaces.ILocational;
import club.projectessence.gameserver.model.items.instance.ItemInstance;
import club.projectessence.gameserver.model.skills.Skill;
import club.projectessence.gameserver.model.zone.ZoneId;
import club.projectessence.gameserver.network.clientpackets.autoplay.ExAutoPlaySetting.NextTargetMode;
import club.projectessence.gameserver.network.serverpackets.ExServerPrimitive;
import club.projectessence.gameserver.network.serverpackets.ExShowScreenMessage;
import club.projectessence.gameserver.network.serverpackets.autoplay.ExAutoPlaySettingSend;

/**
 * <AUTHOR>
 */
public class AutoPlayTaskManager
{
	private static final Logger					LOGGER				= Logger.getLogger(AutoPlayTaskManager.class.getName());
	// cast skill id | debuff skill id
	private static final Map<Integer, Integer>	NO_SPAM_DEBUFFS		= new HashMap<>();
	private static int							MAX_PVP_ENEMIES		= 0;
	private static final int					POINTS_IN_CIRCLE	= 30;
	private static final int					LINE_LENGTH			= 30;
	private static final int					MP_RECHARGE_MAX		= 50;
	public static int							INTERVAL			= 30;
	private static ScheduledFuture<?>			_tasks[];
	private static AutoPlayPlayerList			_players[];
	private static boolean						_debug				= false;
	private static long							_debugStartTime[];
	private static PlayerInstance				_debugLongestTimePlayer[];
	private static long							_debugLongestTime[];
	private static long							_timeElapsedForPlayer[];
	static
	{
		NO_SPAM_DEBUFFS.put(122, 122); // Hex
		NO_SPAM_DEBUFFS.put(1539, 1539);// Stigma of Shillien
		NO_SPAM_DEBUFFS.put(531, 531);// Critical Wound
		NO_SPAM_DEBUFFS.put(1164, 1164);// Weakness
		NO_SPAM_DEBUFFS.put(1396, 1396); // Magical Backfire
		NO_SPAM_DEBUFFS.put(115, 115);// Power Break
		NO_SPAM_DEBUFFS.put(1206, 1206); // Wind Shackle
		NO_SPAM_DEBUFFS.put(412, 412);// Sand Bomb
		NO_SPAM_DEBUFFS.put(47337, 47337); // Critical Wound: Focus
		NO_SPAM_DEBUFFS.put(1263, 1263); // Curse Gloom
		NO_SPAM_DEBUFFS.put(45477, 45522); // Flying dagger
		NO_SPAM_DEBUFFS.put(47369, 47370); // Flying dagger
		NO_SPAM_DEBUFFS.put(1358, 1358); // Block Defense
		NO_SPAM_DEBUFFS.put(1359, 1359); // Block Wind Walk
		NO_SPAM_DEBUFFS.put(1351, 1351); // Mage Bane
		NO_SPAM_DEBUFFS.put(1350, 1350); // Warrior Bane
		NO_SPAM_DEBUFFS.put(87134, 87134); // Break
	}
	private final int THREAD_COUNT = Config.AUTO_PLAY_THREAD_COUNT;
	
	private AutoPlayTaskManager()
	{
		_players = new AutoPlayPlayerList[THREAD_COUNT];
		for (int i = 0; i < THREAD_COUNT; i++)
		{
			_players[i] = new AutoPlayPlayerList();
		}
		_debugStartTime = new long[THREAD_COUNT];
		_debugLongestTimePlayer = new PlayerInstance[THREAD_COUNT];
		_debugLongestTime = new long[THREAD_COUNT];
		_timeElapsedForPlayer = new long[THREAD_COUNT];
		startTasks();
	}
	
	public static AutoPlayTaskManager getInstance()
	{
		return SingletonHolder.INSTANCE;
	}
	
	public void startTasks()
	{
		if ((_tasks == null) || (_tasks.length == 0))
		{
			_tasks = new ScheduledFuture[THREAD_COUNT];
		}
		for (int i = 0; i < THREAD_COUNT; i++)
		{
			if (_tasks[i] != null)
			{
				_tasks[i].cancel(true);
				_tasks[i] = null;
			}
		}
		for (int i = 0; i < THREAD_COUNT; i++)
		{
			_tasks[i] = ThreadPool.get().scheduleAtFixedRate(new AutoPlayTask(i), INTERVAL, INTERVAL);
		}
	}
	
	private boolean verifyTarget(PlayerInstance player, WorldObject target, boolean existingTarget)
	{
		if ((target != null) //
		&& (target.getId() != 25914) // Atingo
		&& (target.getId() != 18554) // Otherworldly Shard
		&& (target.getId() != 22430) // Nepenthes
		&& (target.getId() != 22438) // Antelope
		&& (target.getId() != 22444) // Bandersnatch
		&& (target.getId() != 18725) // Morgos Military Base
		&& (target.getId() != 18726) // Morgos Military Base
		&& (target.getId() != 18722) // Xillenos Fortress
		&& (target.getId() != 18723) // Xillenos Fortress
		&& target.isAutoAttackable(player) //
		&& target.isVisibleFor(player) //
		&& GeoEngine.getInstance().canSeeTarget(player, target)//
		&& (target.isPlayer() ? player.canAttackSameFaction(target.getActingPlayer()) : true) // Faction check
		&& GeoEngine.getInstance().canMoveToTarget(player.getX(), player.getY(), player.getZ(), target.getX(), target.getY(), target.getZ(), player.getInstanceWorld()))
		{
			if (existingTarget)
			{
				switch (player.getAutoPlaySettings().getNextTargetMode())
				{
					case MONSTER:
					{
						if (!target.isMonster())
						{
							return false;
						}
						break;
					}
					case CHARACTERS:
					{
						if (existingTarget ? !target.isPlayable() : !target.isPlayer())
						{
							return false;
						}
						break;
					}
					case NPC:
					{
						if (!target.isNpc() || target.isMonster())
						{
							return false;
						}
						break;
					}
					case PAYBACK_ATTACK:
					{
						if (target.isPlayable())
						{
							if (existingTarget ? !target.isPlayable() : !target.isPlayer())
							{
								return false;
							}
							if (target.getActingPlayer().getEinhasadOverseeingLevel() < 0)
							{
								return false;
							}
						}
						else if (!target.isMonster())
						{
							return false;
						}
						break;
					}
					case ANY_TARGET:
					{
						break;
					}
					default:
						break;
				}
			}
			ILocational rangeCheckPoint;
			if (player.getAutoPlaySettings().isFixedCombatZone())
			{
				rangeCheckPoint = player.getAutoPlaySettings().getFixedCombatZoneLoc();
				if (rangeCheckPoint == null)
				{
					rangeCheckPoint = player.getLocation();
					player.getAutoPlaySettings().setFixedCombatZoneLoc(rangeCheckPoint);
				}
			}
			else
			{
				rangeCheckPoint = player.getLocation();
			}
			if (target.calculateDistance2D(rangeCheckPoint) > (player.getAutoPlaySettings().isLongRange() ? player.getAutoPlaySettings().getRange2() : player.getAutoPlaySettings().getRange1()))
			{
				return false;
			}
			// if (!player.getAutoPlaySettings().isTargetRaid())
			// {
			// final Creature creature = (Creature) target;
			// if (creature.isRaid() || creature.isRaidMinion())
			// {
			// return false;
			// }
			// }
			// else
			// {
			// final Creature creature = (Creature) target;
			// if (creature.isRaid())
			// {
			// switch (creature.getId())
			// {
			// // 60 rb solo
			// case 25751:
			// case 25749:
			// case 25226:
			// case 25122:
			// case 25102:
			// // 70 rb
			// case 25004:
			// case 25431:
			// case 25230:
			// case 25256:
			// case 25463:
			// case 25747:
			// case 25748:
			// case 25754:
			// // 75 rb
			// case 25095:
			// case 25152:
			// case 25217:
			// case 25026:
			// case 25155:
			// case 25146:
			// case 25369:
			// case 25044:
			// case 25051:
			// case 25099:
			// case 25746:
			// // 80 rb
			// case 25398:
			// case 25176:
			// case 25441:
			// case 25057:
			// case 25159:
			// case 25255:
			// case 25163:
			// case 25757:
			// case 23862:
			// case 25911:
			// // Custom Eigis
			// case 10000:
			// case 10001:
			// case 24115, 24122, 24123, 24267:
			// {
			// return false;
			// }
			// }
			// }
			// }
			if (!player.getAutoPlaySettings().isTargetRaid())
			{
				final Creature creature = (Creature) target;
				if (creature.isRaid() || creature.isRaidMinion())
				{
					return false;
				}
			}
			if (player.getAutoPlaySettings().getNextTargetMode() == NextTargetMode.PAYBACK_ATTACK)
			{
				if (target.isPlayable() && (target.getActingPlayer().getEinhasadOverseeingLevel() > 0))
				{
					return true;
				}
				return target.isMonster();
			}
			return true;
		}
		return false;
	}
	
	private boolean respectfulHunting(PlayerInstance player, Creature creature)
	{
		if (creature == null)
		{
			return true;
		}
		if (creature.isMonster() && player.getAutoPlaySettings().isRespectfulHunting())
		{
			/*
			 * TODO: uncomment when fake players in use List<WorldObject> fakePlayerIgnore = new ArrayList<>(); if (player.isFakePlayer()) { for (Playable playable : World.getInstance().getVisibleObjectsInRange(player, Playable.class, 1100)) { if (playable.getActingPlayer().isFakePlayer() &&
			 * (playable.getTarget() != null)) { fakePlayerIgnore.add(playable.getTarget()); } } } if (player.isFakePlayer() && player.isInsideZone(ZoneId.FAKE_PLAYER)) { if (fakePlayerIgnore.contains(existingTarget)) { monster = null; break recheckRespectfulHunt; } } if (!player.isFakePlayer() ||
			 * !player.isInsideZone(ZoneId.FAKE_PLAYER)) {
			 */
			if (creature.isMonster())
			{
				MonsterInstance mob = (MonsterInstance) creature;
				Creature mobTarget = mob.getMostHated();
				Creature mostHated = (Creature) mob.getTarget();
				Creature mySummon = null;
				boolean mobTargetMyParty = (mobTarget != null) && ((mobTarget.getParty() != null) && (player.getParty() != null) && (mobTarget.getParty() == player.getParty()));
				boolean mostHatedMyParty = (mostHated != null) && ((mostHated.getParty() != null) && (player.getParty() != null) && (mostHated.getParty() == player.getParty()));
				for (Creature summ : player.getServitors().values())
				{
					mySummon = summ;
				}
				if (((player.getPet() != null) && (mobTarget == player.getPet())) || (mostHated == player.getPet()))
				{
					return true;
				}
				if ((mostHated != null) && ((mostHated != player) && (mostHated != mySummon) && (!mostHatedMyParty)) && (mobTarget != null) && ((mobTarget != player) && (mobTarget != mySummon) && (!mobTargetMyParty)))
				{
					return false;
				}
			}
		}
		return true;
	}
	
	public void doAutoPlay(PlayerInstance player, boolean pickup, boolean longRange, boolean respectfulHunting, NextTargetMode nextTargetMode)
	{
		player.getAutoPlaySettings().setPickup(pickup);
		player.getAutoPlaySettings().setLongRange(longRange);
		player.getAutoPlaySettings().setRespectfulHunting(respectfulHunting);
		player.getAutoPlaySettings().setNextTargetMode(nextTargetMode);
		int lowest = -1;
		int lowestCount = Integer.MAX_VALUE;
		for (int i = 0; i < THREAD_COUNT; i++)
		{
			final int size = _players[i].players.size();
			if (size < lowestCount)
			{
				lowestCount = size;
				lowest = i;
			}
		}
		if (!isPlayerAutoHunting(player))
		{
			player.getAutoPlaySettings().setFixedCombatZoneLoc(player.getLocation());
			player._autoFarmSkillIndex = 0;
			player._autoFarmAutoAttacksDone = 0;
			player.onActionRequest();
			_players[lowest].players.add(player);
		}
		AutoPlaySettingsHolder settings = player.getAutoPlaySettings();
		player.sendPacket(new ExAutoPlaySettingSend(16, true, settings.doPickup(), settings.getNextTargetMode(), settings.isLongRange(), Config.CUSTOM_INTERFACE_ENABLED ? ((settings.getAutoHp2PotionPercent() << 8) + (settings.getAutoMpPotionPercent() << 16) + (settings.getAutoCpPotionPercent() << 24) + settings.getAutoHpPotionPercent()) : settings.getAutoHpPotionPercent(), settings.getPetAutoPotionPercent(), settings.isRespectfulHunting()));
		trySendAutoPlayFixedZoneLoc(player);
		trySendAutoPlayRange(player, false);
		// DropManager.getInstance().startPassiveDrop(player);
	}
	
	public void addAutoSkill(PlayerInstance player, int skillId)
	{
		player.getAutoPlaySettings().getAutoSkills().add(skillId);
	}
	
	public void removeAutoSkill(PlayerInstance player, int skillId)
	{
		player.getAutoPlaySettings().getAutoSkills().remove((Object) skillId);
	}
	
	public void addPetAutoSkill(PlayerInstance player, int skillId)
	{
		player.getAutoPlaySettings().getPetAutoSkills().add(skillId);
	}
	
	public void removePetAutoSkill(PlayerInstance player, int skillId)
	{
		player.getAutoPlaySettings().getPetAutoSkills().remove((Object) skillId);
	}
	
	public void addAutoAction(PlayerInstance player, Integer actionId)
	{
		player.getAutoPlaySettings().getAutoActions().add(actionId);
	}
	
	public void removeAutoAction(PlayerInstance player, int actionId)
	{
		player.getAutoPlaySettings().getAutoActions().remove((Object) actionId);
	}
	
	public void stopAutoPlay(PlayerInstance player)
	{
		if (isPlayerAutoHunting(player))
		{
			// TODO: uncomment when fake players in use
			// if (player.isFakePlayer())
			// {
			// player.getFakePlayerInfo().DONT_SELECT_MORE_TARGETS = false;
			// }
			// PLAYERS.remove(player);
			for (int i = 0; i < THREAD_COUNT; i++)
			{
				_players[i].players.remove(player);
			}
			AutoPlaySettingsHolder settings = player.getAutoPlaySettings();
			player.sendPacket(new ExAutoPlaySettingSend(16, false, settings.doPickup(), settings.getNextTargetMode(), settings.isLongRange(), Config.CUSTOM_INTERFACE_ENABLED ? ((settings.getAutoHp2PotionPercent() << 8) + (settings.getAutoMpPotionPercent() << 16) + (settings.getAutoCpPotionPercent() << 24) + settings.getAutoHpPotionPercent()) : settings.getAutoHpPotionPercent(), settings.getPetAutoPotionPercent(), settings.isRespectfulHunting()));
			removeAutoPlayRange(player);
		}
		// DropManager.getInstance().stopPassiveDrop(player);
	}
	
	public boolean isPlayerAutoHunting(PlayerInstance player)
	{
		for (int i = 0; i < THREAD_COUNT; i++)
		{
			if (_players[i].players.contains(player))
			{
				return true;
			}
		}
		return false;
	}

	/**
	 * Alias for isPlayerAutoHunting for better readability in auto-resurrection context
	 * @param player the player to check
	 * @return true if player is in autoplay mode
	 */
	public boolean isAutoPlay(PlayerInstance player)
	{
		return isPlayerAutoHunting(player);
	}

	/**
	 * Enables or disables auto-resurrection for the player
	 * @param player the player
	 * @param enabled true to enable, false to disable
	 */
	public void setAutoResurrection(PlayerInstance player, boolean enabled)
	{
		player.getAutoPlaySettings().setAutoResurrectionEnabled(player, enabled);
		if (enabled)
		{
			String message = "Auto-resurrection enabled. You will automatically resurrect and return to your hunting location when you die during autoplay.";
			player.sendMessage(message);
			player.sendPacket(new ExShowScreenMessage(message, 5000));
		}
		else
		{
			// If player is currently waiting for return, cancel it
			if (player.getAutoPlaySettings().isWaitingForReturn())
			{
				player.getAutoPlaySettings().setWaitingForReturn(false);
				player.getAutoPlaySettings().setReturnScheduledTime(0);
				String message = "Auto-resurrection disabled. Scheduled return to hunting location has been cancelled.";
				player.sendMessage(message);
				player.sendPacket(new ExShowScreenMessage(message, 4000));
			}
			else
			{
				String message = "Auto-resurrection disabled.";
				player.sendMessage(message);
				player.sendPacket(new ExShowScreenMessage(message, 3000));
			}
		}
	}
	
	public void trySendAutoPlayRange(PlayerInstance player, boolean lazy)
	{
		if (!player.getAutoPlaySettings().isShowRange() || !isPlayerAutoHunting(player))
		{
			return;
		}
		if ((player._lastAutofarmRangeDisplayTs + (lazy ? 3_000 : 500)) < System.currentTimeMillis())
		{
			final ILocational lastReceivedLoc = player.getAutoPlaySettings().getLastReceivedRangeLoc();
			if (lazy && (lastReceivedLoc != null) && (player.calculateDistance2D(lastReceivedLoc) < 300))
			{
				return;
			}
			player.getAutoPlaySettings().setLastReceivedRangeLoc(player.getLocation());
			player._lastAutofarmRangeDisplayTs = System.currentTimeMillis();
			final int x, y, z;
			if (player.getAutoPlaySettings().isFixedCombatZone())
			{
				ILocational loc = player.getAutoPlaySettings().getFixedCombatZoneLoc();
				if (loc == null)
				{
					loc = player.getLocation();
					player.getAutoPlaySettings().setFixedCombatZoneLoc(loc);
				}
				x = loc.getX();
				y = loc.getY();
				z = (int) (loc.getZ() - player.getCollisionHeight()) + (player.isTransformed() ? 15 : 5);
			}
			else
			{
				x = player.getX();
				y = player.getY();
				z = (int) (player.getZ() - player.getCollisionHeight()) + (player.isTransformed() ? 15 : 5);
			}
			final boolean isLongRange = player.getAutoPlaySettings().isLongRange();
			final int radius = isLongRange ? player.getAutoPlaySettings().getRange2() : player.getAutoPlaySettings().getRange1();
			final ExServerPrimitive primitive = new ExServerPrimitive("autoFarmRange", x, y, z + 65535);
			int pointsBottom[][] = new int[POINTS_IN_CIRCLE][3];
			for (int i = 0; i < POINTS_IN_CIRCLE; ++i)
			{
				double angle1 = (i * 2 * Math.PI) / POINTS_IN_CIRCLE;
				double angle2 = ((i + 1) * 2 * Math.PI) / POINTS_IN_CIRCLE;
				int x1 = (int) (x + (radius * Math.cos(angle1)));
				int y1 = (int) (y + (radius * Math.sin(angle1)));
				int x2 = (int) (x + (radius * Math.cos(angle2)));
				int y2 = (int) (y + (radius * Math.sin(angle2)));
				primitive.addLine(player.getAutoPlaySettings().getNextTargetMode().getColor(), x1, y1, z, x2, y2, z);
				primitive.addLine(player.getAutoPlaySettings().getNextTargetMode().getColor(), x1, y1, z + (isLongRange ? 400 : 200), x2, y2, z + (isLongRange ? 400 : 200));
				pointsBottom[i][0] = x1;
				pointsBottom[i][1] = y1;
				pointsBottom[i][2] = z;
			}
			for (int i = 0; i < pointsBottom.length; i += 2)
			{
				// final int x1, y1, z1, x2, y2, z2;
				// if ((i % 2) == 0)
				// {
				final int x1 = pointsBottom[i][0];
				final int y1 = pointsBottom[i][1];
				final int z1 = pointsBottom[i][2];
				final int x2 = pointsBottom[(i + 1) < pointsBottom.length ? i + 1 : 0][0];
				final int y2 = pointsBottom[(i + 1) < pointsBottom.length ? i + 1 : 0][1];
				final int z2 = pointsBottom[(i + 1) < pointsBottom.length ? i + 1 : 0][2] + (isLongRange ? 400 : 200);
				primitive.addLine(player.getAutoPlaySettings().getNextTargetMode().getColor(), x1, y1, z1, x2, y2, z2);
				// }
				// else
				// {
				// x1 = pointsUpper[i][0];
				// y1 = pointsUpper[i][1];
				// z1 = pointsUpper[i][2];
				// x2 = pointsBottom[(i + 1) < pointsBottom.length ? i + 1 : 0][0];
				// y2 = pointsBottom[(i + 1) < pointsBottom.length ? i + 1 : 0][1];
				// z2 = pointsBottom[(i + 1) < pointsBottom.length ? i + 1 : 0][2];
				// }
				// primitive.addLine(player.getAutoPlaySettings().getNextTargetMode().getColor(), x1, y1, z1, x2, y2, z2);
				// primitive.addLine(player.getAutoPlaySettings().getNextTargetMode().getColor(), x1, y1, z2, x2, y2, z1);
			}
			player.sendPacket(primitive);
			player.getAutoPlaySettings().setReceivedRangePacket(true);
		}
	}
	
	public void removeAutoPlayRange(PlayerInstance player)
	{
		if (player.getAutoPlaySettings().hasReceivedRangePacket())
		{
			final ExServerPrimitive primitive = new ExServerPrimitive("autoFarmRange", player.getX(), player.getY(), player.getZ() + 65535);
			primitive.addPoint(0, 0, 0, 0);
			player.sendPacket(primitive);
			player.getAutoPlaySettings().setReceivedRangePacket(false);
		}
		removeAutoPlayFixedZoneLoc(player);
	}
	
	public void trySendAutoPlayFixedZoneLoc(PlayerInstance player)
	{
		if (!player.getAutoPlaySettings().isFixedCombatZone() || !player.getAutoPlaySettings().isShowRange() || !isPlayerAutoHunting(player))
		{
			return;
		}
		if ((player._lastAutofarmFixedZoneLocDisplayTs + 500) < System.currentTimeMillis())
		{
			player._lastAutofarmFixedZoneLocDisplayTs = System.currentTimeMillis();
			ILocational loc = player.getAutoPlaySettings().getFixedCombatZoneLoc();
			if (loc == null)
			{
				loc = player.getLocation();
				player.getAutoPlaySettings().setFixedCombatZoneLoc(loc);
			}
			final float collisionHeight = player.getCollisionHeight();
			final int x = loc.getX();
			final int y = loc.getY();
			final int z = (int) (loc.getZ() - collisionHeight) + 5;
			final ExServerPrimitive primitive = new ExServerPrimitive("autoFarmFixedZoneLoc", x, y, z + 65535);
			// Cuboid
			final int x1 = (int) (x + Math.sqrt((Math.pow(LINE_LENGTH, 2)) / 2));
			final int x2 = (int) (x - Math.sqrt((Math.pow(LINE_LENGTH, 2)) / 2));
			final int y1 = (int) (y + Math.sqrt((Math.pow(LINE_LENGTH, 2)) / 2));
			final int y2 = (int) (y - Math.sqrt((Math.pow(LINE_LENGTH, 2)) / 2));
			primitive.addLine(Color.GRAY, x1, y1, z, x2, y1, z); // Ground Square Line
			primitive.addLine(Color.GRAY, x2, y1, z, x2, y2, z); // Ground Square Line
			primitive.addLine(Color.GRAY, x2, y2, z, x1, y2, z); // Ground Square Line
			primitive.addLine(Color.GRAY, x1, y2, z, x1, y1, z); // Ground Square Line
			int upZ = z + 50;
			primitive.addLine(Color.GRAY, x1, y1, upZ, x2, y1, upZ); // Up Square Line
			primitive.addLine(Color.GRAY, x2, y1, upZ, x2, y2, upZ); // Up Square Line
			primitive.addLine(Color.GRAY, x2, y2, upZ, x1, y2, upZ); // Up Square Line
			primitive.addLine(Color.GRAY, x1, y2, upZ, x1, y1, upZ); // Up Square Line
			primitive.addLine(Color.GRAY, x1, y1, z, x1, y1, upZ); // Height Line
			primitive.addLine(Color.GRAY, x2, y1, z, x2, y1, upZ); // Height Line
			primitive.addLine(Color.GRAY, x2, y2, z, x2, y2, upZ); // Height Line
			primitive.addLine(Color.GRAY, x1, y2, z, x1, y2, upZ); // Height Line
			player.sendPacket(primitive);
			player.getAutoPlaySettings().setReceivedFixedZoneLocPacket(true);
		}
	}
	
	public void removeAutoPlayFixedZoneLoc(PlayerInstance player)
	{
		if (player.getAutoPlaySettings().hasReceivedFixedZoneLocPacket())
		{
			final ExServerPrimitive primitive = new ExServerPrimitive("autoFarmFixedZoneLoc", player.getX(), player.getY(), player.getZ() + 65535);
			primitive.addPoint(0, 0, 0, 0);
			player.sendPacket(primitive);
			player.getAutoPlaySettings().setReceivedFixedZoneLocPacket(false);
		}
	}
	
	public void printDebug()
	{
		System.out.println("--- " + getClass().getSimpleName() + " ---");
		for (int i = 0; i < THREAD_COUNT; i++)
		{
			System.out.println("List " + i + ": " + _players[i].players.size() + " players.");
		}
	}
	
	public void setDebug(boolean val)
	{
		_debug = val;
		for (int i = 0; i < THREAD_COUNT; i++)
		{
			_debugLongestTimePlayer[i] = null;
		}
	}
	
	public boolean isInDebug()
	{
		return _debug;
	}
	
	private static class SingletonHolder
	{
		protected static final AutoPlayTaskManager INSTANCE = new AutoPlayTaskManager();
	}
	
	private class AutoPlayTask implements Runnable
	{
		private final int _i;
		
		protected AutoPlayTask(int index)
		{
			_i = index;
		}
		
		@Override
		public void run()
		{
			if (Config.ONLY_CHARACTER_CREATE)
			{
				return;
			}
			String previousThreadName = null;
			if (_debug)
			{
				previousThreadName = Thread.currentThread().getName();
				Thread.currentThread().setName("L2AutoPlay-" + _i);
				_debugStartTime[_i] = System.currentTimeMillis();
				_debugLongestTimePlayer[_i] = null;
				_debugLongestTime[_i] = 0;
			}
			PLAY:
			for (PlayerInstance player : _players[_i].players)
			{
				if (_debug)
				{
					_timeElapsedForPlayer[_i] = System.currentTimeMillis();
				}
				// if ((!player.isFakePlayer() && ((!player.isOnline() || player.isInOfflineMode()))) || player.isDead())
				// {
				// stopAutoPlay(player);
				// continue PLAY;
				// }
				if (!player.isOnline() || (player.isInOfflineMode() && !player.isOfflinePlay()))
				{
					stopAutoPlay(player);
					continue PLAY;
				}
				// if (!player.isFakePlayer() && (player.getClient().getConnectionState() != ConnectionState.IN_GAME))
				// {
				// continue PLAY;
				// }
				if (player.isMounted() || player.isClassicMounted() || player.isTargetingDisabled() || player.isLockedTarget() || player.isTeleporting())
				{
					continue PLAY;
				}
				if (player.inObserverMode())
				{
					continue;
				}
				if (!player.getAutoUseSettings().isTownMode() && player.isInsideZone(ZoneId.PEACE))
				{
					continue;
				}
				if (player.isInOlympiadMode())
				{
					continue;
				}
				if (player.isFakePlayer() && player.isTeleporting())
				{
					continue;
				}
				// Skip thinking.
				final WorldObject target = player.getTarget();
				if ((target != null) && target.isCreature())
				{
					final Creature creature = (Creature) target;
					if (creature.isAlikeDead())
					{
						player.setTarget(null);
					}
				}
				// Pickup.
				if (player.getAutoPlaySettings().doPickup())
				{
					PICKUP:
					for (ItemInstance droppedItem : World.getInstance().getVisibleObjectsInRange(player, ItemInstance.class, 200))
					{
						// Check if item is reachable.
						if ((droppedItem == null) //
						|| (!droppedItem.isSpawned()) //
						|| (droppedItem.getOwnerId() != player.getObjectId()) //
						|| !GeoEngine.getInstance().canMoveToTarget(player.getX(), player.getY(), player.getZ(), droppedItem.getX(), droppedItem.getY(), droppedItem.getZ(), player.getInstanceWorld()))
						{
							continue PICKUP;
						}
						// Move to item.
						if (player.calculateDistance2D(droppedItem) > 70)
						{
							if (!player.isMoving())
							{
								player.doPickupItem(droppedItem);
							}
							continue PLAY;
						}
						// Try to pick it up.
						if (!droppedItem.isProtected() || (droppedItem.getOwnerId() == player.getObjectId()))
						{
							player.doPickupItem(droppedItem);
							continue PLAY; // Avoid pickup being skipped.
						}
					}
				}
				final int distance = player.isFakePlayer() && !player.isInsideZone(ZoneId.FAKE_PLAYER) ? 5000 : player.getAutoPlaySettings().isLongRange() ? player.getAutoPlaySettings().getRange2() : player.getAutoPlaySettings().getRange1();
				NextTargetMode nextTargetMode = player.getAutoPlaySettings().getNextTargetMode();
				int pvpPlayersNearby;
				switch (nextTargetMode)
				{
					case CHARACTERS:
					case ANY_TARGET:
					case PAYBACK_ATTACK:
					{
						pvpPlayersNearby = World.getInstance().getVisibleObjectsInRange(player, PlayerInstance.class, 1400).stream().filter(p -> p.isAutoAttackable(player)).collect(Collectors.toList()).size();
						break;
					}
					default:
					{
						pvpPlayersNearby = 0;
						break;
					}
				}
				// Find target.
				Creature creature = null;
				WorldObject existingTarget = player.getTarget();
				if (Config.ENABLE_CUSTOM_AUTO_PVP_BONUS)
				{
					if (Config.LIST_CHARNAME_BONUS_AUTO_PVP.contains(player.getName()))
					{
						MAX_PVP_ENEMIES = Config.MAX_PVP_ENEMIES_FOR_BONUS_CHAR;
					}
					else
					{
						MAX_PVP_ENEMIES = Config.MAX_PVP_ENEMIES_FOR_NORMAL_CHAR;
					}
				}
				else
				{
					MAX_PVP_ENEMIES = Config.MAX_PVP_ENEMIES_FOR_NORMAL_CHAR;
				}
				if ((existingTarget != null) && existingTarget.isPlayable() && (pvpPlayersNearby > MAX_PVP_ENEMIES))
				{
					if ((player._lastPvpAutofarmWarningTs + 3_000) < System.currentTimeMillis())
					{
						player._lastPvpAutofarmWarningTs = System.currentTimeMillis();
						player.sendPacket(new ExShowScreenMessage("PvP AutoPlay is not available against more than " + MAX_PVP_ENEMIES + " enemies.", ExShowScreenMessage.TOP_CENTER, 3000, 0, true, true));
						player.sendMessage("PvP AutoPlay is not available against more than " + MAX_PVP_ENEMIES + " enemies.");
					}
				}
				else if (verifyTarget(player, existingTarget, true))
				{
					creature = (Creature) existingTarget;
				}
				if (!respectfulHunting(player, creature))
				{
					creature = null;
				}
				if (creature == null)
				{
					double closestDistance = Double.MAX_VALUE;
					// TODO: uncomment when fake players in use
					// List<WorldObject> fakePlayerIgnore = new ArrayList<>();
					// if (player.isFakePlayer())
					// {
					// for (Playable playable : World.getInstance().getVisibleObjectsInRange(player, Playable.class, 1100))
					// {
					// if (playable.getActingPlayer().isFakePlayer() && (playable.getTarget() != null))
					// {
					// fakePlayerIgnore.add(playable.getTarget());
					// }
					// }
					// }
					final boolean isFixedCombatZone = player.getAutoPlaySettings().isFixedCombatZone();
					final int targetLookupDistance;
					if (isFixedCombatZone)
					{
						final int distToCenter = (int) player.calculateDistance2D(player.getAutoPlaySettings().getFixedCombatZoneLoc());
						targetLookupDistance = distToCenter >= distance ? (distance + distToCenter) : distance * 2;
					}
					else
					{
						targetLookupDistance = distance;
					}
					TARGET:
					for (Creature nearby : nextTargetMode == NextTargetMode.MONSTER ? World.getInstance().getVisibleObjectsInRange(player, MonsterInstance.class, targetLookupDistance) //
					: (nextTargetMode == NextTargetMode.CHARACTERS) ? World.getInstance().getVisibleObjectsInRange(player, PlayerInstance.class, targetLookupDistance) : (nextTargetMode == NextTargetMode.PAYBACK_ATTACK) ? World.getInstance().getVisibleObjectsInRange(player, Creature.class, targetLookupDistance) //
					: nextTargetMode == NextTargetMode.NPC ? World.getInstance().getVisibleObjectsInRange(player, Npc.class, targetLookupDistance) //
					: World.getInstance().getVisibleObjectsInRange(player, Creature.class, targetLookupDistance))
					{
						// Skip unavailable targets
						if ((nearby == null) || nearby.isAlikeDead())
						{
							continue TARGET;
						}
						// Check monster target.
						if (!respectfulHunting(player, nearby))
						{
							continue TARGET;
						}
						if (Config.ENABLE_CUSTOM_AUTO_PVP_BONUS)
						{
							if (Config.LIST_CHARNAME_BONUS_AUTO_PVP.contains(player.getName()))
							{
								MAX_PVP_ENEMIES = Config.MAX_PVP_ENEMIES_FOR_BONUS_CHAR;
							}
							else
							{
								MAX_PVP_ENEMIES = Config.MAX_PVP_ENEMIES_FOR_NORMAL_CHAR;
							}
						}
						else
						{
							MAX_PVP_ENEMIES = Config.MAX_PVP_ENEMIES_FOR_NORMAL_CHAR;
						}
						if (nearby.isPlayable() && (pvpPlayersNearby > MAX_PVP_ENEMIES))
						{
							if ((player._lastPvpAutofarmWarningTs + 3_000) < System.currentTimeMillis())
							{
								player._lastPvpAutofarmWarningTs = System.currentTimeMillis();
								player.sendPacket(new ExShowScreenMessage("PvP AutoPlay is not available against more than " + MAX_PVP_ENEMIES + " enemies.", ExShowScreenMessage.TOP_CENTER, 3000, 0, true, true));
								player.sendMessage("PvP AutoPlay is not available against more than " + MAX_PVP_ENEMIES + " enemies.");
							}
							continue TARGET;
						}
						// TODO: uncomment when fake players in use
						// FakePlayerZone fakePlayerMobZone = ZoneManager.getInstance().getZone(nearby.getLocation(), FakePlayerZone.class);
						// boolean fakePlayerZoneOk = !player.isFakePlayer() || ((fakePlayerMobZone != null) && (player.getFakePlayerInfo().TARGET_ZONE != null) && (fakePlayerMobZone.getId() == player.getFakePlayerInfo().TARGET_ZONE.getId()));
						// boolean fakePlayerStop = false;
						// Check if monster is reachable.
						if (verifyTarget(player, nearby, false) //
						/* && fakePlayerZoneOk */)
						{
							final double monsterDistance = player.calculateDistance2D(nearby);
							if (monsterDistance < closestDistance)
							{
								// TODO: uncomment when fake players in use
								// if (fakePlayerStop)
								// {
								// if ((nearby.getTarget() != null) && (nearby.getMostHated() == player))
								// {
								// monster = nearby;
								// closestDistance = monsterDistance;
								// }
								// }
								// else
								// {
								creature = nearby;
								closestDistance = monsterDistance;
								// }
							}
						}
					}
				}
				// New target was assigned.
				if (creature != null)
				{
					if (creature != player.getTarget())
					{
						player.setTarget(creature);
					}
					if (player.getAutoPlaySettings().getAutoActions().isEmpty() && player.getAutoPlaySettings().getAutoSkills().isEmpty())
					{
						creature.onAction(player);
					}
					// TODO: uncomment when fake players in use
					// FakePlayerZone fakePlayerZone = ZoneManager.getInstance().getZone(player.getLocation(), FakePlayerZone.class);
					// if (player.isFakePlayer() && ((fakePlayerZone == null) || (player.getFakePlayerInfo().TARGET_ZONE == null) || (fakePlayerZone.getId() != player.getFakePlayerInfo().TARGET_ZONE.getId())))
					// {
					// player.getAI().moveToPawn(monster, 50, true);
					// continue PLAY;
					// }
					boolean skillUsed = false;
					boolean nextAttack = false;
					if (!player.isCastingNow())
					{
						int autoSkillsSize = player.getAutoPlaySettings().getAutoSkills().size();
						int skillToUse = -1;
						if (!player.getAutoPlaySettings().getAutoSkills().isEmpty())
						{
							int toggledAttackCount = 0;
							for (Integer actionId : player.getAutoPlaySettings().getAutoActions())
							{
								if (actionId == 2)
								{
									toggledAttackCount++;
								}
							}
							if (player._autoFarmAutoAttacksDone >= toggledAttackCount)
							{
								if ((player._autoFarmSkillIndex + 1) > autoSkillsSize)
								{
									player._autoFarmSkillIndex = 0;
								}
								skillToUse = player.getAutoPlaySettings().getAutoSkills().get(player._autoFarmSkillIndex);
								player._autoFarmSkillIndex++;
							}
						}
						SKILLS:
						{
							if (player.getPet() != null)
							{
								PET_SKILLS:
								for (int skillId : player.getAutoPlaySettings().getPetAutoSkills())
								{
									final Skill skill = player.getPet().getKnownSkill(skillId);
									if (skill == null)
									{
										player.getAutoPlaySettings().getPetAutoSkills().remove((Object) skillId);
										continue PET_SKILLS;
									}
									if (skill.isBad())
									{
										if ((target == null) || (target == player) || (target == player.getPet()))
										{
											continue PET_SKILLS;
										}
										if (!target.isAutoAttackable(player.getPet()))
										{
											continue PET_SKILLS;
										}
									}
									if (player.getPet().isSkillDisabled(skill) || ((skill.getMpConsume() + skill.getMpInitialConsume()) > player.getPet().getCurrentMp()) || (skill.getHpConsume() > player.getPet().getCurrentHp()))
									{
										continue PET_SKILLS;
									}
									if (skill.checkCondition(player.getPet(), player.getTarget(), false))
									{
										if (player.getPet().useMagic(skill, null, false, false, false))
										{
											break PET_SKILLS;
										}
									}
								}
							}
							if (skillToUse == -1)
							{
								break SKILLS;
							}
							final Skill skill = player.getKnownSkill(skillToUse);
							if (skill == null)
							{
								player.getAutoPlaySettings().getAutoSkills().remove((Object) skillToUse);
								break SKILLS;
							}
							// Check bad skill target.
							if (skill.isBad())
							{
								if ((target == null) || (target == player))
								{
									break SKILLS;
								}
								if (!target.isAutoAttackable(player))
								{
									break SKILLS;
								}
								if (creature != null)
								{
									Integer debuffId = NO_SPAM_DEBUFFS.get(skill.getId());
									if ((debuffId != null) && creature.isAffectedBySkill(debuffId.intValue()))
									{
										break SKILLS;
									}
								}
							}
							switch (skill.getOperateType())
							{
								case DA1:
								case DA2:
								case DA3:
								case DA4:
								case DA5:
								{
									if (player.calculateDistance2D(creature) < 200)
									{
										break SKILLS;
									}
								}
							}
							switch (skill.getId())
							{
								case 45311: // Deadly Pull
								case 10015: // Chain Strike
								case 47277: // Chain Strike (Shock)
								case 53003: // Mass Chain Strike
								case 821: // Shadow Step
								case 47340: // Shadow Assault
								{
									if (player.calculateDistance2D(creature) < 200)
									{
										break SKILLS;
									}
								}
							}
							if ((skill.getId() == 1157 || skill.getId() == 417) && player.getCurrentMpPercent() > MP_RECHARGE_MAX) // Body to mind / Pain of Sagittarius
							{
								break SKILLS;
							}
							if (skill.getNextAction() == NextActionType.ATTACK)
							{
								nextAttack = true;
							}
							if (player.isSkillDisabled(skill) || ((skill.getMpConsume() + skill.getMpInitialConsume()) > player.getCurrentMp()) || (skill.getHpConsume() > player.getCurrentHp()) || (skill.getDpConsume() > player.getCurrentDp()) || (skill.getBpConsume() > player.getCurrentBp()))
							{
								break SKILLS;
							}
							if (skill.checkCondition(player, player.getTarget(), false))
							{
								skillUsed = player.useMagic(skill, null, false, false, false);
								if (skillUsed)
								{
									player._autoFarmSkippedSkills = 0;
									player._autoFarmAutoAttacksDone = 0;
								}
							}
						}
						if (!skillUsed && nextAttack && (creature != null))
						{
							player._autoFarmSkippedSkills++;
							if (player._autoFarmSkippedSkills > Math.max(30, autoSkillsSize / 3))
							{
								creature.onAction(player);
								player._autoFarmSkippedSkills = 0;
							}
						}
					}
					for (Integer actionId : player.getAutoPlaySettings().getAutoActions())
					{
						if (actionId == 1010) // 1010 - Blessing of Seraphim
						{
							if (player.isAffectedBySkill(4702))
							{
								continue;
							}
						}
						else if (actionId == 1011)
						{
							if (player.isAffectedBySkill(4703)) // 1011 - Gift of Seraphim
							{
								continue;
							}
						}
						else if (actionId == 1168)
						{
							if (player.isAffectedBySkill(47902)) // 1168 - Cat Emperor - Emperor's Blessing
							{
								continue;
							}
						}
						else if (actionId == 1170)
						{
							if (player.isAffectedBySkill(47905)) // 1170 - Unicorn Guard - Guard's Blessing
							{
								continue;
							}
						}
						else if (actionId == 1172)
						{
							if (player.isAffectedBySkill(47908)) // 1172 - Lord Raise - Lord's Blessing
							{
								continue;
							}
						}
						else if (actionId == 42)
						{
							final Summon summon = player.getAnyServitor();
							if (player.isAffectedBySkill(4378) || (summon != null && summon.isAffectedBySkill(4378))) // 42 - Personal Damage Shield
							{
								continue;
							}
						}
						else if (actionId == 2) // auto attack
						{
							if (player.isCastingNow() || skillUsed)
							{
								continue;
							}
						}
						if ((actionId == 45) && player.getCurrentMpPercent() > MP_RECHARGE_MAX) // Master Recharge
						{
							continue;
						}
						final ActionDataHolder actionHolder = ActionData.getInstance().getActionData(actionId);
						if (actionHolder != null)
						{
							final IPlayerActionHandler actionHandler = PlayerActionHandler.getInstance().getHandler(actionHolder.getHandler());
							if (actionHandler != null)
							{
								actionHandler.useAction(player, actionHolder, false, false);
							}
						}
						final long millis = System.currentTimeMillis();
						if ((player._autoFarmAttackStopFollowTs + 3000) < millis)
						{
							player.getAI().stopFollow();
							player._autoFarmAttackStopFollowTs = millis;
						}
					}
				}
				// player.sendPacket(ExAutoPlayDoMacro.STATIC_PACKET);
				if (_debug)
				{
					_timeElapsedForPlayer[_i] = System.currentTimeMillis() - _timeElapsedForPlayer[_i];
					if (_debugLongestTime[_i] < _timeElapsedForPlayer[_i])
					{
						_debugLongestTime[_i] = _timeElapsedForPlayer[_i];
						_debugLongestTimePlayer[_i] = player;
					}
				}
			}
			if (_debug)
			{
				LOGGER.info(getClass().getSimpleName() + " (" + _i + ") (" + _players[_i].players.size() + " players) Time Elapsed: " + (Math.max(0, System.currentTimeMillis() - _debugStartTime[_i])) + " ms. Longest task: " + _debugLongestTimePlayer[_i] + " " + _debugLongestTime[_i] + " ms.");
			}
			if (previousThreadName != null)
			{
				Thread.currentThread().setName(previousThreadName);
			}
		}
	}
}
