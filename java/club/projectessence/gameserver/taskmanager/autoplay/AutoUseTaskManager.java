/*
 * This file is part of the L2J Mobius project.
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.taskmanager.autoplay;

import java.util.List;
import java.util.concurrent.ScheduledFuture;
import java.util.logging.Logger;

import club.projectessence.Config;
import club.projectessence.commons.concurrent.ThreadPool;
import club.projectessence.gameserver.handler.IItemHandler;
import club.projectessence.gameserver.handler.ItemHandler;
import club.projectessence.gameserver.model.actor.Summon;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.model.effects.EffectFlag;
import club.projectessence.gameserver.model.holders.ItemSkillHolder;
import club.projectessence.gameserver.model.items.EtcItem;
import club.projectessence.gameserver.model.items.Item;
import club.projectessence.gameserver.model.items.instance.ItemInstance;
import club.projectessence.gameserver.model.skills.AbnormalType;
import club.projectessence.gameserver.model.skills.BuffInfo;
import club.projectessence.gameserver.model.skills.Skill;
import club.projectessence.gameserver.model.skills.targets.TargetType;
import club.projectessence.gameserver.model.stats.Stat;
import club.projectessence.gameserver.model.zone.ZoneId;
import gabriel.eventEngine.interf.GabrielEvents;

/**
 * <AUTHOR>
 */
public class AutoUseTaskManager
{
	public static final int				SHORTCUT_HP		= 277;
	public static final int				SHORTCUT_HP2	= 275;
	public static final int				SHORTCUT_MP		= 274;
	public static final int				SHORTCUT_CP		= 276;
	public static final int				SHORTCUT_PET_HP	= 278;
	private static final Logger			LOGGER			= Logger.getLogger(AutoUseTaskManager.class.getName());
	public static int					INTERVAL		= 100;
	private static ScheduledFuture<?>	_tasks[];
	private static AutoPlayPlayerList	_players[];
	private static boolean				_debug			= false;
	private static long					_debugStartTime[];
	private static PlayerInstance		_debugLongestTimePlayer[];
	private static long					_debugLongestTime[];
	private static long					_timeElapsedForPlayer[];
	private final int					THREAD_COUNT	= Config.AUTO_USE_THREAD_COUNT;
	
	private AutoUseTaskManager()
	{
		_players = new AutoPlayPlayerList[THREAD_COUNT];
		for (int i = 0; i < THREAD_COUNT; i++)
		{
			_players[i] = new AutoPlayPlayerList();
		}
		_debugStartTime = new long[THREAD_COUNT];
		_debugLongestTimePlayer = new PlayerInstance[THREAD_COUNT];
		_debugLongestTime = new long[THREAD_COUNT];
		_timeElapsedForPlayer = new long[THREAD_COUNT];
		startTasks();
	}
	
	public static AutoUseTaskManager getInstance()
	{
		return SingletonHolder.INSTANCE;
	}
	
	public void startTasks()
	{
		if ((_tasks == null) || (_tasks.length == 0))
		{
			_tasks = new ScheduledFuture[THREAD_COUNT];
		}
		for (int i = 0; i < THREAD_COUNT; i++)
		{
			if (_tasks[i] != null)
			{
				_tasks[i].cancel(true);
				_tasks[i] = null;
			}
		}
		for (int i = 0; i < THREAD_COUNT; i++)
		{
			_tasks[i] = ThreadPool.get().scheduleAtFixedRate(new AutoUseTask(i), INTERVAL, INTERVAL);
		}
	}
	
	public void startAutoUseTask(PlayerInstance player)
	{
		if (isPlayerAutoUsing(player))
		{
			return;
		}
		int lowest = -1;
		int lowestCount = Integer.MAX_VALUE;
		for (int i = 0; i < THREAD_COUNT; i++)
		{
			final int size = _players[i].players.size();
			if (size < lowestCount)
			{
				lowestCount = size;
				lowest = i;
			}
		}
		_players[lowest].players.add(player);
	}
	
	public void stopAutoUseTask(PlayerInstance player)
	{
		for (int i = 0; i < THREAD_COUNT; i++)
		{
			_players[i].players.remove(player);
		}
	}
	
	public void stopAutoUseTask(PlayerInstance player, boolean onlyIfNeeded)
	{
		if (!onlyIfNeeded)
		{
			stopAutoUseTask(player);
		}
		else
		{
			if (player.getAutoUseSettings().getAutoSupplyItems().isEmpty() && player.getAutoUseSettings().getAutoPotionItems().isEmpty() && player.getAutoUseSettings().getAutoSkills().isEmpty())
			{
				stopAutoUseTask(player);
			}
		}
	}
	
	public boolean isPlayerAutoUsing(PlayerInstance player)
	{
		for (int i = 0; i < THREAD_COUNT; i++)
		{
			if (_players[i].players.contains(player))
			{
				return true;
			}
		}
		return false;
	}
	
	public void addAutoSupplyItem(PlayerInstance player, int itemId)
	{
		player.getAutoUseSettings().getAutoSupplyItems().add(itemId);
		startAutoUseTask(player);
	}
	
	public void removeAutoSupplyItem(PlayerInstance player, int itemId)
	{
		player.getAutoUseSettings().getAutoSupplyItems().remove(itemId);
		stopAutoUseTask(player, true);
	}
	
	public void addAutoLootBox(PlayerInstance player, int itemId)
	{
		player.getAutoUseSettings().getAutoLootBoxes().add(itemId);
		startAutoUseTask(player);
	}
	
	public void removeAutoLootBox(PlayerInstance player, int itemId)
	{
		player.getAutoUseSettings().getAutoLootBoxes().remove(itemId);
		stopAutoUseTask(player, true);
	}
	
	public AutoUsePotionType getAutoUsePotionTypeByShortcutId(int shortcutId)
	{
		switch (shortcutId)
		{
			case SHORTCUT_HP:
				return AutoUsePotionType.HP;
			case SHORTCUT_HP2:
				return AutoUsePotionType.HP2;
			case SHORTCUT_MP:
				return AutoUsePotionType.MP;
			case SHORTCUT_CP:
				return AutoUsePotionType.CP;
		}
		return null;
	}
	
	public void addAutoPotionItem(PlayerInstance player, int shortcutId, int itemId)
	{
		addAutoPotionItem(player, getAutoUsePotionTypeByShortcutId(shortcutId), itemId);
	}
	
	public void addAutoPotionItem(PlayerInstance player, AutoUsePotionType autoUsePotionType, int itemId)
	{
		player.getAutoUseSettings().getAutoPotionItems().put(autoUsePotionType, itemId);
		startAutoUseTask(player);
	}
	
	public void removeAutoPotionItem(PlayerInstance player, int shortcutId)
	{
		AutoUsePotionType potionType = getAutoUsePotionTypeByShortcutId(shortcutId);
		if (potionType != null)
		{
			player.getAutoUseSettings().getAutoPotionItems().remove(potionType);
		}
		stopAutoUseTask(player, true);
	}
	
	public void addPetAutoPotionItem(PlayerInstance player, int itemId)
	{
		player.getAutoUseSettings().getPetAutoPotionItems().add(itemId);
		startAutoUseTask(player);
	}
	
	public void removePetAutoPotionItem(PlayerInstance player, int itemId)
	{
		player.getAutoUseSettings().getPetAutoPotionItems().remove(itemId);
		stopAutoUseTask(player, true);
	}
	
	public void addAutoSkill(PlayerInstance player, int skillId)
	{
		player.getAutoUseSettings().getAutoSkills().add(skillId);
		startAutoUseTask(player);
	}
	
	public void removeAutoSkill(PlayerInstance player, int skillId)
	{
		player.getAutoUseSettings().getAutoSkills().remove(skillId);
		stopAutoUseTask(player, true);
	}
	
	public void addPetAutoSkill(PlayerInstance player, int skillId)
	{
		player.getAutoUseSettings().getPetAutoSkills().add(skillId);
		startAutoUseTask(player);
	}
	
	public void removePetAutoSkill(PlayerInstance player, int skillId)
	{
		player.getAutoUseSettings().getPetAutoSkills().remove(skillId);
		stopAutoUseTask(player, true);
	}
	
	public void printDebug()
	{
		System.out.println("--- " + getClass().getSimpleName() + " ---");
		for (int i = 0; i < THREAD_COUNT; i++)
		{
			System.out.println("List " + i + ": " + _players[i].players.size() + " players.");
		}
	}
	
	public void setDebug(boolean val)
	{
		_debug = val;
		for (int i = 0; i < THREAD_COUNT; i++)
		{
			_debugLongestTimePlayer[i] = null;
		}
	}
	
	public boolean isInDebug()
	{
		return _debug;
	}
	
	public static enum AutoUsePotionType
	{
		HP2,
		HP,
		MP,
		CP
	}
	
	private static class SingletonHolder
	{
		protected static final AutoUseTaskManager INSTANCE = new AutoUseTaskManager();
	}
	
	private class AutoUseTask implements Runnable
	{
		private final int _i;
		
		protected AutoUseTask(int index)
		{
			_i = index;
		}
		
		@Override
		public void run()
		{
			if (Config.ONLY_CHARACTER_CREATE)
			{
				return;
			}
			String previousThreadName = null;
			if (_debug)
			{
				previousThreadName = Thread.currentThread().getName();
				Thread.currentThread().setName("L2AutoUse-" + _i);
				_debugStartTime[_i] = System.currentTimeMillis();
				_debugLongestTimePlayer[_i] = null;
				_debugLongestTime[_i] = 0;
			}
			for (PlayerInstance player : _players[_i].players)
			{
				if (_debug)
				{
					_timeElapsedForPlayer[_i] = System.currentTimeMillis();
				}
				// if (!player.isFakePlayer() && ((!player.isOnline() || player.isInOfflineMode())))
				// {
				// stopAutoUseTask(player);
				// continue;
				// }
				if (!player.isOnline() || (player.isInOfflineMode() && !player.isOfflinePlay()))
				{
					stopAutoUseTask(player);
					continue;
				}
				// if (!player.isFakePlayer() && ((!player.isOnline() || (player.isInOfflineMode() && !player.isOfflinePlay()))))
				// {
				// stopAutoUseTask(player);
				// continue;
				// }
				if (player.isDead())
				{
					continue;
				}
				if (player.isTeleporting() || player.hasBlockActions() || player.isControlBlocked() || player.isAlikeDead())
				{
					continue;
				}
				// if (!player.isFakePlayer() && (player.getClient().getConnectionState() != ConnectionState.IN_GAME))
				// {
				// continue;
				// }
				if (player.inObserverMode())
				{
					continue;
				}
				LOOT_BOXES:
				for (Integer itemId : player.getAutoUseSettings().getAutoLootBoxes())
				{
					final ItemInstance item = player.getInventory().getItemByItemId(itemId.intValue());
					if (item == null)
					{
						player.getAutoUseSettings().getAutoLootBoxes().remove(itemId);
						continue LOOT_BOXES; // TODO: break?
					}
					if (player.isInOlympiadMode() && item.isOlyRestrictedItem())
					{
						continue LOOT_BOXES;
					}
					if (item.isLocked())
					{
						continue;
					}
					if (!item.getItem().checkCondition(player, player, false))
					{
						continue LOOT_BOXES;
					}
					final int reuseDelay = item.getReuseDelay();
					if ((reuseDelay <= 0) || (player.getItemRemainingReuseTime(item.getObjectId()) <= 0))
					{
						final EtcItem etcItem = item.getEtcItem();
						final IItemHandler handler = ItemHandler.getInstance().getHandler(etcItem);
						if ((handler != null) && handler.useItem(player, item, false))
						{
							if (reuseDelay > 0)
							{
								player.addTimeStampItem(item, reuseDelay);
							}
							if (item.getCount() < 1)
							{
								player.sendToggledShortcuts();
							}
						}
					}
				}
				if (player.getAutoUseSettings().isTownMode() || !player.isInsideZone(ZoneId.PEACE))
				{
					ITEMS:
					for (Integer itemId : player.getAutoUseSettings().getAutoSupplyItems())
					{
						final ItemInstance item = player.getInventory().getItemByItemId(itemId.intValue());
						if (item == null)
						{
							player.getAutoUseSettings().getAutoSupplyItems().remove(itemId);
							continue ITEMS; // TODO: break?
						}
						if (player.isInOlympiadMode() && item.isOlyRestrictedItem())
						{
							continue ITEMS;
						}
						if (item.isLocked())
						{
							continue;
						}
						final Item it = item.getItem();
						if (it != null)
						{
							final List<ItemSkillHolder> skills = it.getAllSkills();
							if (skills != null)
							{
								for (ItemSkillHolder itemSkillHolder : skills)
								{
									final Skill skill = itemSkillHolder.getSkill();
									if ((player.isInOlympiadMode() && skill.isBlockedInOlympiad()) || player.hasSkillReuse(skill.getReuseHashCode()) || !skill.checkCondition(player, player, false))
									{
										continue ITEMS;
									}
									BuffInfo info = null;
									final AbnormalType type = skill.getAbnormalType();
									if (type != AbnormalType.NONE)
									{
										if (player.hasAbnormalType(type))
										{
											for (BuffInfo t : player.getEffectList().getEffects())
											{
												if (t.isAbnormalType(type))
												{
													if ((t.getTime() > 3))
													{
														if (skill.getAbnormalLevel() <= t.getSkill().getAbnormalLevel())
														{
															continue ITEMS;
														}
													}
												}
											}
										}
									}
									else
									{
										info = player.getEffectList().getBuffInfoBySkillId(skill.getId());
										if ((info != null) && (info.getTime() > 3))
										{
											if (skill.getAbnormalLevel() <= info.getSkill().getAbnormalLevel())
											{
												continue ITEMS;
											}
										}
									}
									// if ((skill.getAbnormalType() != AbnormalType.NONE) && player.hasAbnormalType(skill.getAbnormalType()))
									// {
									// continue ITEMS;
									// }
								}
							}
						}
						if (!item.getItem().checkCondition(player, player, false))
						{
							continue ITEMS;
						}
						final int reuseDelay = item.getReuseDelay();
						if ((reuseDelay <= 0) || (player.getItemRemainingReuseTime(item.getObjectId()) <= 0))
						{
							final EtcItem etcItem = item.getEtcItem();
							final IItemHandler handler = ItemHandler.getInstance().getHandler(etcItem);
							if ((handler != null) && handler.useItem(player, item, false))
							{
								if (reuseDelay > 0)
								{
									player.addTimeStampItem(item, reuseDelay);
								}
								if (item.getCount() < 1)
								{
									player.sendToggledShortcuts();
								}
							}
						}
					}
					POTIONS:
					for (AutoUsePotionType type : AutoUsePotionType.values())
					{
						Integer itemId = player.getAutoUseSettings().getAutoPotionItems().get(type);
						if ((type == null) || (itemId == null) || (itemId <= 0))
						{
							continue POTIONS;
						}
						int currentPercent = type == AutoUsePotionType.MP ? player.getCurrentMpPercent() : type == AutoUsePotionType.CP ? player.getCurrentCpPercent() : player.getCurrentHpPercent();
						if ((currentPercent < (type == AutoUsePotionType.MP ? player.getAutoPlaySettings().getAutoMpPotionPercent() : type == AutoUsePotionType.CP ? player.getAutoPlaySettings().getAutoCpPotionPercent() : type == AutoUsePotionType.HP2 ? player.getAutoPlaySettings().getAutoHp2PotionPercent() : player.getAutoPlaySettings().getAutoHpPotionPercent())))
						{
							final ItemInstance item = player.getInventory().getItemByItemId(itemId.intValue());
							if (item == null)
							{
								player.getAutoUseSettings().getAutoPotionItems().remove(type);
								continue POTIONS; // TODO: break?
							}
							if (player.isInOlympiadMode() && item.isOlyRestrictedItem())
							{
								continue POTIONS;
							}
							if (item.isLocked())
							{
								continue;
							}
							if (GabrielEvents.isInEvent(player))
							{
								continue;
							}
							final int reuseDelay = item.getReuseDelay();
							if ((reuseDelay <= 0) || (player.getItemRemainingReuseTime(item.getObjectId()) <= 0))
							{
								final EtcItem etcItem = item.getEtcItem();
								final IItemHandler handler = ItemHandler.getInstance().getHandler(etcItem);
								if ((handler != null) && handler.useItem(player, item, false))
								{
									if (reuseDelay > 0)
									{
										player.addTimeStampItem(item, reuseDelay);
									}
									if (item.getCount() < 1)
									{
										player.sendToggledShortcuts();
									}
								}
							}
						}
					}
					if (player.getPet() != null)
					{
						PET_POTIONS:
						for (Integer itemId : player.getAutoUseSettings().getPetAutoPotionItems())
						{
							int currentPercent = player.getPet().getCurrentHpPercent();
							if ((currentPercent < player.getAutoPlaySettings().getPetAutoPotionPercent()))
							{
								final ItemInstance item = player.getInventory().getItemByItemId(itemId.intValue());
								if (item == null)
								{
									player.getAutoUseSettings().getPetAutoPotionItems().remove(itemId);
									continue PET_POTIONS; // TODO: break?
								}
								if (player.isInOlympiadMode() && item.isOlyRestrictedItem())
								{
									continue PET_POTIONS;
								}
								if (item.isLocked())
								{
									continue;
								}
								if (GabrielEvents.isInEvent(player))
								{
									continue;
								}
								final int reuseDelay = item.getReuseDelay();
								if ((reuseDelay <= 0) || (player.getItemRemainingReuseTime(item.getObjectId()) <= 0))
								{
									final EtcItem etcItem = item.getEtcItem();
									final IItemHandler handler = ItemHandler.getInstance().getHandler(etcItem);
									if ((handler != null) && handler.useItem(player, item, false))
									{
										if (reuseDelay > 0)
										{
											player.addTimeStampItem(item, reuseDelay);
										}
										if (item.getCount() < 1)
										{
											player.sendToggledShortcuts();
										}
									}
								}
							}
						}
					}
					if (player.getPet() != null)
					{
						PET_BUFFS:
						for (Integer skillId : player.getAutoUseSettings().getPetAutoSkills())
						{
							final Skill skill = player.getPet().getKnownSkill(skillId);
							if (skill == null)
							{
								player.getAutoUseSettings().getPetAutoSkills().remove(skillId);
								continue PET_BUFFS; // TODO: break?
							}
							if (player.getPet().getEffectList().getBlockedAbnormalTypes().contains(skill.getAbnormalType()))
							{
								continue;
							}
							if ((player.isInOlympiadMode() && skill.isBlockedInOlympiad()))
							{
								continue PET_BUFFS;
							}
							if (!player.getPet().hasSkillReuse(skill.getReuseHashCode()))
							{
								if (!skill.checkCondition(player.getPet(), player.getPet(), false))
								{
									continue PET_BUFFS;
								}
							}
							BuffInfo info = player.getPet().getEffectList().getFirstBuffInfoByAbnormalType(skill.getAbnormalType());
							if (skill.getAbnormalType() != AbnormalType.NONE)
							{
								if ((info != null) && (info.getTime() > 3))
								{
									if (skill.getAbnormalLevel() <= info.getSkill().getAbnormalLevel())
									{
										continue PET_BUFFS;
									}
								}
							}
							else
							{
								info = player.getPet().getEffectList().getBuffInfoBySkillId(skill.getId());
								if ((info != null) && (info.getTime() > 3))
								{
									if (skill.getAbnormalLevel() <= info.getSkill().getAbnormalLevel())
									{
										continue PET_BUFFS;
									}
								}
							}
							if (player.getPet().isSkillDisabled(skill) || ((skill.getMpConsume() + skill.getMpInitialConsume()) > player.getCurrentMp()) || (skill.getHpConsume() > player.getCurrentHp()))
							{
								continue PET_BUFFS;
							}
							if (skill.checkCondition(player.getPet(), player.getPet(), false))
							{
								player.getPet().useMagic(skill, null, false, false, false);
							}
						}
					}
					if (!player.isMounted() && !player.isClassicMounted())
					{
						BUFFS:
						for (Integer skillId : player.getAutoUseSettings().getAutoSkills())
						{
							final Skill skill = player.getKnownSkill(skillId);
							if (skill == null)
							{
								player.getAutoUseSettings().getAutoSkills().remove(skillId);
								continue BUFFS; // TODO: break?
							}
							if (player.getEffectList().getBlockedAbnormalTypes().contains(skill.getAbnormalType()))
							{
								continue;
							}
							if ((player.getEffectList().isAffected(EffectFlag.MUTED) && skill.isMagic()) //
							|| (player.getEffectList().isAffected(EffectFlag.PSYCHICAL_MUTED) && skill.isPhysical()))
							{
								continue;
							}
							if ((skill.getId() == 50) || (skill.getId() == 8) || (skill.getId() == 918) || (skill.getId() == 919))
							{
								if (player.getCharges() >= player.getStat().getValue(Stat.MAX_MOMENTUM))
								{
									continue BUFFS;
								}
							}
							if ((player.isInOlympiadMode() && skill.isBlockedInOlympiad()))
							{
								continue BUFFS;
							}
							if (!player.hasSkillReuse(skill.getReuseHashCode()))
							{
								Summon summon = null;
								// Summon check.
								if (skill.getTargetType() == TargetType.SUMMON)
								{
									if (!player.hasServitors()) // Is this check truly needed?
									{
										continue BUFFS;
									}
									int occurrences = 0;
									for (Summon servitor : player.getServitors().values())
									{
										summon = servitor;
										if (servitor.isDead() || servitor.isAffectedBySkill(skillId.intValue()))
										{
											occurrences++;
										}
										if (!skill.checkCondition(player, servitor, false))
										{
											continue BUFFS;
										}
									}
									if (occurrences == player.getServitors().size())
									{
										continue BUFFS;
									}
								}
								else if (!skill.checkCondition(player, player, false))
								{
									continue BUFFS;
								}
								if (skill.getAutouseCubicId() > 0)
								{
									if (player.getCubicById(skill.getAutouseCubicId()) != null)
									{
										continue BUFFS;
									}
								}
								else if (skill.isAutouseSummon())
								{
									if (summon == null) // can be not searched before
									{
										summon = player.getAnyServitor();
									}
									if ((summon != null) && (summon.getCurrentHpPercent() > 5) && (summon.getCurrentMpPercent() > 5))
									{
										continue BUFFS;
									}
								}
								else
								{
									BuffInfo info = player.getEffectList().getFirstBuffInfoByAbnormalType(skill.getAbnormalType());
									if (skill.getAbnormalType() != AbnormalType.NONE)
									{
										if ((info != null) && (info.getTime() > 3))
										{
											if (skill.getAbnormalLevel() <= info.getSkill().getAbnormalLevel())
											{
												continue BUFFS;
											}
										}
									}
									else
									{
										info = player.getEffectList().getBuffInfoBySkillId(skill.getId());
										if ((info != null) && (info.getTime() > 3))
										{
											if (skill.getAbnormalLevel() <= info.getSkill().getAbnormalLevel())
											{
												continue BUFFS;
											}
										}
									}
								}
								if (skill.getItemConsumeId() != 0)
								{
									ItemInstance consumeItem = player.getInventory().getItemByItemId(skill.getItemConsumeId());
									if ((consumeItem == null) || (consumeItem.getCount() < skill.getItemConsumeCount()))
									{
										continue BUFFS;
									}
								}
								if (player.isSkillDisabled(skill) || ((skill.getMpConsume() + skill.getMpInitialConsume()) > player.getCurrentMp()) || (skill.getHpConsume() > player.getCurrentHp()) || (skill.getDpConsume() > player.getCurrentDp()) || (skill.getBpConsume() > player.getCurrentBp()))
								{
									continue BUFFS;
								}
								if (skill.checkCondition(player, summon != null ? summon : player, false))
								{
									// fix casting same skill and transforms overlapping
									if (!player.isCastingNow(s -> ((s.getSkill().getId() == skill.getId()) || (s.getSkill().getId() == 1800)) || (s.getSkill().getId() == 1801) || (s.getSkill().getId() == 1802) || (s.getSkill().getId() == 54102)))
									{
										player.useMagic(skill, null, false, false, false);
									}
								}
							}
						}
					}
				}
				if (_debug)
				{
					_timeElapsedForPlayer[_i] = System.currentTimeMillis() - _timeElapsedForPlayer[_i];
					if (_debugLongestTime[_i] < _timeElapsedForPlayer[_i])
					{
						_debugLongestTime[_i] = _timeElapsedForPlayer[_i];
						_debugLongestTimePlayer[_i] = player;
					}
				}
			}
			if (_debug)
			{
				LOGGER.info(getClass().getSimpleName() + " (" + _i + ") (" + _players[_i].players.size() + " players) Time Elapsed: " + (Math.max(0, System.currentTimeMillis() - _debugStartTime[_i])) + " ms. Longest task: " + _debugLongestTimePlayer[_i] + " " + _debugLongestTime[_i] + " ms.");
			}
			if (previousThreadName != null)
			{
				Thread.currentThread().setName(previousThreadName);
			}
		}
	}
}
