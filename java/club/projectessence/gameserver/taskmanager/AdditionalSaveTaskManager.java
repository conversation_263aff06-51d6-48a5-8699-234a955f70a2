/*
 * This file is part of the L2J Mobius project.
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.taskmanager;

import java.util.concurrent.ScheduledFuture;
import java.util.logging.Logger;

import club.projectessence.Config;
import club.projectessence.commons.concurrent.ThreadPool;
import club.projectessence.gameserver.Shutdown;
import club.projectessence.gameserver.model.World;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.instancemanager.UniqueOnlineManager;
import custom.gve.service.FactionBalanceService;

/**
 * <AUTHOR>
 */
public class AdditionalSaveTaskManager
{
	private static final Logger			LOGGER		= Logger.getLogger(AdditionalSaveTaskManager.class.getName());
	public static long					INTERVAL	= 5 * 60_000;
	public static boolean				ENABLED		= true;
	private static ScheduledFuture<?>	_task		= null;
	private boolean						_working	= false;
	
	public AdditionalSaveTaskManager()
	{
		startTask();
	}
	
	public static AdditionalSaveTaskManager getInstance()
	{
		return SingletonHolder.INSTANCE;
	}
	
	public void startTask()
	{
		LOGGER.info(getClass().getSimpleName() + " Stopping task...");
		stopTask();
		LOGGER.info(getClass().getSimpleName() + " Starting task...");
		_working = false;
		_task = ThreadPool.get().scheduleAtFixedRate(() ->
		{
			if (!_working && ENABLED && !Shutdown.IS_SHUTDOWNING)
			{
				_working = true;
				int count = 0;
				long start = System.currentTimeMillis();
				if (Config.ENABLE_DATABASE_LOGGER)
				{
					LOGGER.info("#################################################################################################################");
				}
				if (Config.ENABLE_DATABASE_LOGGER)
				{
					LOGGER.info(Thread.currentThread() + "-" + getClass().getSimpleName() + ": Start Saving characters");
				}
				for (PlayerInstance player : World.getInstance().getPlayers())
				{
					if (!ENABLED || Shutdown.IS_SHUTDOWNING)
					{
						_working = false;
						break;
					}
					player.autoSave();
					count++;
				}
				FactionBalanceService.getInstance().updateAndSaveFactionCounts();
				// Save HWID data to database
				UniqueOnlineManager.getInstance().storeTemp();
				LOGGER.info(Thread.currentThread() + "-" + getClass().getSimpleName() + ": Saved characters: " + count + " in " + (System.currentTimeMillis() - start) + " ms.");
				if (Config.ENABLE_DATABASE_LOGGER)
				{
					LOGGER.info("#################################################################################################################");
				}
				_working = false;
			}
		}, 5000, INTERVAL);
	}
	
	public void stopTask()
	{
		_working = false;
		if (_task != null)
		{
			_task.cancel(true);
			_task = null;
		}
	}
	
	public boolean isRunning()
	{
		return _task != null;
	}
	
	private static class SingletonHolder
	{
		protected static final AdditionalSaveTaskManager INSTANCE = new AdditionalSaveTaskManager();
	}
}