/*
 * This file is part of the L2J Mobius project.
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.taskmanager;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.concurrent.ScheduledFuture;
import java.util.logging.Logger;

import club.projectessence.commons.concurrent.ThreadPool;
import club.projectessence.commons.database.DatabaseFactory;
import club.projectessence.gameserver.model.World;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;

/**
 * <AUTHOR>
 */
public class DonateManager
{
	private static final Logger			LOGGER		= Logger.getLogger(DonateManager.class.getName());
	public static long					INTERVAL	= 10_000;
	private static ScheduledFuture<?>	_task		= null;
	
	public DonateManager()
	{
		startTask();
	}
	
	public static DonateManager getInstance()
	{
		return SingletonHolder.INSTANCE;
	}
	
	public void startTask()
	{
		stopTask();
		_task = ThreadPool.get().scheduleAtFixedRate(() ->
		{
			try (Connection conn = DatabaseFactory.getConnection())
			{
				try (PreparedStatement ps = conn.prepareStatement("SELECT * FROM donations WHERE status='PAID'"))// AND NOT platform='Paygol'"))
				{
					try (ResultSet rs = ps.executeQuery())
					{
						while (rs.next())
						{
							try
							{
								int id = rs.getInt("id");
								String date = rs.getString("date");
								String account = rs.getString("account").toLowerCase();
								float amount = rs.getFloat("amount");
								String platform = rs.getString("platform");
								for (PlayerInstance player : World.getInstance().getPlayers())
								{
									if (player.isInOfflineMode() || !player.isOnline())
									{
										continue;
									}
									if (player.getAccountName().toLowerCase().equals(account))
									{
										player.setPrimePoints((int) (player.getPrimePoints() + (amount * 100/* * (platform.equals("Paygol") ? 1 : 1.2) */))); // Donation Event
										try (PreparedStatement stmt = conn.prepareStatement("UPDATE donations SET status='DELIVERED' WHERE id=?"))
										{
											stmt.setInt(1, id);
											stmt.execute();
										}
										catch (Exception e)
										{
											LOGGER.warning(getClass().getSimpleName() + ": failed to update donation [ID: " + id + "] status for player: " + player);
										}
										finally
										{
											LOGGER.warning(getClass().getSimpleName() + ": Delivered donation: [ID: " + id + "] [Date: " + date + "] [Account: " + account + "] [Amount: " + amount + "] [Platform: " + platform + "]");
										}
										break;
									}
								}
							}
							catch (Exception e)
							{
								LOGGER.warning(getClass().getSimpleName() + ": Could not deliver donation. " + e.getMessage());
							}
						}
					}
				}
				// try (PreparedStatement ps = conn.prepareStatement("SELECT * FROM donate_premium WHERE claimed='PAID'"))
				// {
				// try (ResultSet rs = ps.executeQuery())
				// {
				// while (rs.next())
				// {
				// int id = rs.getInt("id");
				// String date = rs.getString("date");
				// String account = rs.getString("email").toLowerCase();
				// String platform = rs.getString("platform");
				// PremiumManager.getInstance().addPremiumTime(account, 30, TimeUnit.DAYS);
				// try (PreparedStatement stmt = conn.prepareStatement("UPDATE donate_premium SET claimed='DELIVERED' WHERE id=?"))
				// {
				// stmt.setInt(1, id);
				// stmt.execute();
				// }
				// catch (Exception e)
				// {
				// LOGGER.warning(getClass().getSimpleName() + ": failed to update donation_premium [ID: " + id + "] status for account: " + account);
				// }
				// finally
				// {
				// LOGGER.warning(getClass().getSimpleName() + ": Delivered donation_premium: [ID: " + id + "] [Date: " + date + "] [Account: " + account + "] [Platform: " + platform + "]");
				// }
				// }
				// }
				// }
			}
			catch (Exception e)
			{
				LOGGER.warning("Error in DonateManager:" + e);
				e.printStackTrace();
			}
		}, INTERVAL, INTERVAL);
	}
	
	public void stopTask()
	{
		if (_task != null)
		{
			_task.cancel(true);
			_task = null;
		}
	}
	
	public boolean isRunning()
	{
		return _task != null;
	}
	
	private static class SingletonHolder
	{
		protected static final DonateManager INSTANCE = new DonateManager();
	}
}
