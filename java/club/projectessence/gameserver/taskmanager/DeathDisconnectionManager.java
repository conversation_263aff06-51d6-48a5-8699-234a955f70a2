/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.taskmanager;

import club.projectessence.commons.concurrent.ThreadPool;
import club.projectessence.gameserver.GameServer;
import club.projectessence.gameserver.model.World;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.network.Disconnection;

import java.util.concurrent.ScheduledFuture;
import java.util.logging.Logger;

/**
 * <AUTHOR>
 */
public class DeathDisconnectionManager {
	protected static final Logger LOGGER_ACCOUNTING = Logger.getLogger("accounting");
	private static final Logger LOGGER = Logger.getLogger(DeathDisconnectionManager.class.getName());
	public static long ALLOWED_DEATH_TIME = 900_000;
	private static ScheduledFuture<?> _task = null;
	// public static long ALLOWED_DEATH_TIME = 180_000;

	public DeathDisconnectionManager() {
	}

	public static DeathDisconnectionManager getInstance() {
		return SingletonHolder.INSTANCE;
	}

	public void startTask() {
		stopTask();
		_task = ThreadPool.get().scheduleAtFixedRate(() ->
		{
			long time = System.currentTimeMillis();
			for (PlayerInstance player : World.getInstance().getPlayers()) {
				if (player.isDead() && !player.isInOlympiadMode() && ((player._deathTime + ALLOWED_DEATH_TIME) < time)) {
					LOGGER.info(getClass().getSimpleName() + ": Disconnected: " + player);
					String clientInfos = player.getClient().toString();
					LOGGER_ACCOUNTING.info("Disconnecting;" + clientInfos + ";DeathDisconnectionManager.class;" + GameServer.getInstance().getUsedMemoryMB() + ";calling Disconnection.of(player).defaultSequence(true)");
					Disconnection.of(player).logout(true, false);
					LOGGER_ACCOUNTING.info("Disconnected;" + clientInfos + ";DeathDisconnectionManager.class;" + GameServer.getInstance().getUsedMemoryMB() + ";");
					// TODO: logger here, but in disconnection there should be another logger already
				}
			}
		}, 3000, ALLOWED_DEATH_TIME);
	}

	public void stopTask() {
		if (_task != null) {
			_task.cancel(true);
			_task = null;
		}
	}

	public boolean isRunning() {
		return _task != null;
	}

	private static class SingletonHolder {
		protected static final DeathDisconnectionManager INSTANCE = new DeathDisconnectionManager();
	}
}
