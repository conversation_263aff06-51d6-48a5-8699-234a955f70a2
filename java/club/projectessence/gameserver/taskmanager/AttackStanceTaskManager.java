/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.taskmanager;

import club.projectessence.commons.concurrent.ThreadPool;
import club.projectessence.gameserver.model.actor.Creature;
import club.projectessence.gameserver.model.actor.Summon;
import club.projectessence.gameserver.network.serverpackets.AutoAttackStop;

import java.util.Iterator;
import java.util.Map;
import java.util.Map.Entry;
import java.util.concurrent.ConcurrentHashMap;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Attack stance task manager.
 *
 * <AUTHOR>
 */
public class AttackStanceTaskManager {
	public static final long COMBAT_TIME = 10000;
	private static final Logger LOGGER = Logger.getLogger(AttackStanceTaskManager.class.getName());
	private static final Map<Creature, Long> _attackStanceTasks = new ConcurrentHashMap<>();
	private static boolean _working = false;

	/**
	 * Instantiates a new attack stance task manager.
	 */
	protected AttackStanceTaskManager() {
		ThreadPool.get().scheduleAtFixedRate(() ->
		{
			if (_working) {
				return;
			}
			_working = true;

			final long current = System.currentTimeMillis();
			try {
				final Iterator<Entry<Creature, Long>> iterator = _attackStanceTasks.entrySet().iterator();
				Entry<Creature, Long> entry;
				Creature creature;
				while (iterator.hasNext()) {
					entry = iterator.next();
					if ((current - entry.getValue().longValue()) > COMBAT_TIME) {
						creature = entry.getKey();
						if (creature != null) {
							if (!creature.isDead()) {
								creature.broadcastPacket(new AutoAttackStop(creature.getObjectId()));
							}
							creature.getAI().setAutoAttacking(false);
							if (creature.isPlayer() && creature.hasSummon()) {
								creature.getActingPlayer().clearDamageTaken();
								final Summon pet = creature.getPet();
								if (pet != null) {
									pet.broadcastPacket(new AutoAttackStop(pet.getObjectId()));
								}
								creature.getServitors().values().forEach(s ->
								{
									if (!s.isDead()) {
										s.broadcastPacket(new AutoAttackStop(s.getObjectId()));
									}
								});
							}
						}
						iterator.remove();
					}
				}
			} catch (Exception e) {
				// Unless caught here, players remain in attack positions.
				LOGGER.log(Level.WARNING, "Error in AttackStanceTaskManager: " + e.getMessage(), e);
			}

			_working = false;
		}, 0, 1000);
	}

	/**
	 * Gets the single instance of AttackStanceTaskManager.
	 *
	 * @return single instance of AttackStanceTaskManager
	 */
	public static AttackStanceTaskManager getInstance() {
		return SingletonHolder.INSTANCE;
	}

	/**
	 * Adds the attack stance task.
	 *
	 * @param creature the actor
	 */
	public void addAttackStanceTask(Creature creature) {
		if (creature == null) {
			return;
		}

		_attackStanceTasks.put(creature, System.currentTimeMillis());
	}

	/**
	 * Removes the attack stance task.
	 *
	 * @param creature the actor
	 */
	public void removeAttackStanceTask(Creature creature) {
		Creature actor = creature;
		if (actor != null) {
			if (actor.isSummon()) {
				actor = actor.getActingPlayer();
			}
			_attackStanceTasks.remove(actor);
		}
	}

	/**
	 * Checks for attack stance task.
	 *
	 * @param creature the actor
	 * @return {@code true} if the character has an attack stance task, {@code false} otherwise
	 */
	public boolean hasAttackStanceTask(Creature creature) {
		Creature actor = creature;
		if (actor != null) {
			if (actor.isSummon()) {
				actor = actor.getActingPlayer();
			}
			return _attackStanceTasks.containsKey(actor);
		}
		return false;
	}

	private static class SingletonHolder {
		protected static final AttackStanceTaskManager INSTANCE = new AttackStanceTaskManager();
	}
}
