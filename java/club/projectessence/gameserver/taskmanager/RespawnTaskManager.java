/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.taskmanager;

import club.projectessence.Config;
import club.projectessence.commons.concurrent.ThreadPool;
import club.projectessence.gameserver.model.Spawn;
import club.projectessence.gameserver.model.actor.Npc;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 */
public class RespawnTaskManager {
	private final List<Map<Npc, Long>> PENDING_RESPAWNS;
	private int nextList = 0;

	public RespawnTaskManager() {
		PENDING_RESPAWNS = new ArrayList<>(Config.RESPAWN_MANAGER_THREAD_COUNT);
		for (int i = 0; i < Config.RESPAWN_MANAGER_THREAD_COUNT; i++) {
			PENDING_RESPAWNS.add(new ConcurrentHashMap<>());
		}
		for (int i = 0; i < Config.RESPAWN_MANAGER_THREAD_COUNT; i++) {
			ThreadPool.get().scheduleAtFixedRate(new RespawnTask(i), 0, 1000);
		}
	}

	public void add(Npc npc, long time) {
		int next = Math.abs(nextList++ % PENDING_RESPAWNS.size());
		PENDING_RESPAWNS.get(next).put(npc, time);
	}

	private class RespawnTask implements Runnable {
		private final int _i;

		protected RespawnTask(int index) { _i = index; }

		@Override
		public void run() {
			final long time = System.currentTimeMillis();
			for (Entry<Npc, Long> entry : PENDING_RESPAWNS.get(_i).entrySet()) {
				if (time > entry.getValue().longValue()) {
					final Npc npc = entry.getKey();
					PENDING_RESPAWNS.get(_i).remove(npc);
					final Spawn spawn = npc.getSpawn();
					if (spawn != null) {
						spawn.respawnNpc(npc);
						spawn._scheduledCount--;
					}
				}
			}
		}
	}

	public static RespawnTaskManager getInstance() {
		return SingletonHolder.INSTANCE;
	}

	private static class SingletonHolder {
		protected static final RespawnTaskManager INSTANCE = new RespawnTaskManager();
	}
}
