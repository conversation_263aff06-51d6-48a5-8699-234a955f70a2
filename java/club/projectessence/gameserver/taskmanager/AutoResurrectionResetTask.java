/*
 * This file is part of the L2J Mobius project.
 * 
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 * 
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 * 
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.taskmanager;

import java.util.Calendar;
import java.util.concurrent.ScheduledFuture;
import java.util.logging.Logger;

import club.projectessence.commons.concurrent.ThreadPool;
import club.projectessence.gameserver.model.World;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;

/**
 * Task manager for resetting auto-resurrection daily usage.
 * Resets all players' daily auto-resurrection usage at 6:30 AM daily.
 * 
 * <AUTHOR>
 */
public class AutoResurrectionResetTask
{
	private static final Logger LOGGER = Logger.getLogger(AutoResurrectionResetTask.class.getName());
	
	private static final int RESET_HOUR = 6;
	private static final int RESET_MINUTE = 30;
	
	private ScheduledFuture<?> _resetTask;
	
	protected AutoResurrectionResetTask()
	{
		scheduleNextReset();
		LOGGER.info("AutoResurrectionResetTask: Initialized. Next reset scheduled for " + RESET_HOUR + ":" + String.format("%02d", RESET_MINUTE));
	}
	
	/**
	 * Schedules the next daily reset.
	 */
	private void scheduleNextReset()
	{
		final Calendar calendar = Calendar.getInstance();
		calendar.set(Calendar.HOUR_OF_DAY, RESET_HOUR);
		calendar.set(Calendar.MINUTE, RESET_MINUTE);
		calendar.set(Calendar.SECOND, 0);
		calendar.set(Calendar.MILLISECOND, 0);
		
		// If the time has already passed today, schedule for tomorrow
		if (calendar.getTimeInMillis() <= System.currentTimeMillis())
		{
			calendar.add(Calendar.DAY_OF_MONTH, 1);
		}
		
		final long delay = calendar.getTimeInMillis() - System.currentTimeMillis();
		
		_resetTask = ThreadPool.schedule(this::performReset, delay);
		
		LOGGER.info("AutoResurrectionResetTask: Next reset scheduled in " + (delay / 1000 / 60) + " minutes");
	}
	
	/**
	 * Performs the daily reset of auto-resurrection usage.
	 */
	private void performReset()
	{
		LOGGER.info("AutoResurrectionResetTask: Starting daily auto-resurrection usage reset...");
		
		int resetCount = 0;
		
		// Reset for all online players
		for (PlayerInstance player : World.getInstance().getPlayers())
		{
			if (player != null && player.isOnline())
			{
				resetPlayerUsage(player);
				resetCount++;
			}
		}
		
		LOGGER.info("AutoResurrectionResetTask: Daily reset completed. Reset " + resetCount + " online players.");
		
		// Schedule the next reset
		scheduleNextReset();
	}
	
	/**
	 * Resets auto-resurrection usage for a specific player.
	 * 
	 * @param player the player to reset
	 */
	private void resetPlayerUsage(PlayerInstance player)
	{
		player.getAutoPlaySettings().setAutoResurrectionUsesToday(0);
		
		// Also reset consecutive deaths if enough time has passed
		long lastDeathTime = player.getAutoPlaySettings().getLastDeathTime();
		long currentTime = System.currentTimeMillis();
		
		// Reset consecutive deaths if last death was more than 1 hour ago
		if ((currentTime - lastDeathTime) > (60 * 60 * 1000))
		{
			player.getAutoPlaySettings().resetConsecutiveDeaths(player);
		}
	}
	
	/**
	 * Manually triggers a reset (for admin use).
	 */
	public void manualReset()
	{
		LOGGER.info("AutoResurrectionResetTask: Manual reset triggered by admin");
		performReset();
	}
	
	/**
	 * Cancels the scheduled reset task.
	 */
	public void shutdown()
	{
		if (_resetTask != null)
		{
			_resetTask.cancel(false);
			_resetTask = null;
		}
		LOGGER.info("AutoResurrectionResetTask: Shutdown completed");
	}
	
	/**
	 * Gets the singleton instance.
	 * 
	 * @return the singleton instance
	 */
	public static AutoResurrectionResetTask getInstance()
	{
		return SingletonHolder.INSTANCE;
	}
	
	private static class SingletonHolder
	{
		protected static final AutoResurrectionResetTask INSTANCE = new AutoResurrectionResetTask();
	}
}
