/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.taskmanager;

import club.projectessence.commons.concurrent.ThreadPool;
import club.projectessence.commons.database.DatabaseFactory;
import club.projectessence.commons.util.Rnd;
import club.projectessence.gameserver.cache.HtmCache;
import club.projectessence.gameserver.enums.ChatType;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.model.holders.ItemHolder;
import club.projectessence.gameserver.model.holders.PromoCodeHolder;
import club.projectessence.gameserver.network.serverpackets.CreatureSay;
import club.projectessence.gameserver.util.Util;

import javax.mail.*;
import javax.mail.internet.InternetAddress;
import javax.mail.internet.MimeMessage;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.*;
import java.util.logging.Logger;

/**
 * <AUTHOR>
 */
public class PromoCodeManager {
	private static final Logger LOGGER = Logger.getLogger(PromoCodeManager.class.getName());
	private static final HashMap<String, PromoCodeHolder> _codes = new HashMap<>();
	private static final String SELECT_CODES = "SELECT * FROM promo_codes";
	private static final String INSERT_CODE = "INSERT INTO promo_codes (code, type, params, used) VALUES (?, ?, ?, ?)";
	private static final String DELETE_CODE = "DELETE FROM promo_codes WHERE code=?";
	private static final String UPDATE_CODE_USER = "UPDATE promo_codes SET used=? WHERE code=?";
	private static final String SELECT_CODES_LAUNCH_PACK = "SELECT * FROM donate_packs";
	private static final String INSERT_CODE_LAUNCH_PACK = "INSERT INTO donate_packs (date, email, amount, platform, claimed, claimeddate, transactionid, code) VALUES (?, ?, ?, ?, ?, ?, ?, ?)";
	private static final String DELETE_CODE_LAUNCH_PACK = "DELETE FROM donate_packs WHERE code=?";
	private static final String UPDATE_CODE_USER_LAUNCH_PACK = "UPDATE donate_packs SET claimed=?, claimeddate=? WHERE code=?";
	private static final List<ItemHolder> LIGHT_PACK = new ArrayList<>();
	private static final List<ItemHolder> STANDARD_PACK = new ArrayList<>();
	private static final List<ItemHolder> DELUXE_PACK = new ArrayList<>();
	public static long INTERVAL = 60_000;

	static {
		List<ItemHolder> starterPack = new ArrayList<>();
		starterPack.add(new ItemHolder(91767, 2));
		starterPack.add(new ItemHolder(91864, 2));
		starterPack.add(new ItemHolder(94733, 1));
		starterPack.add(new ItemHolder(93500, 5));
		starterPack.add(new ItemHolder(92387, 1));
		starterPack.add(new ItemHolder(3031, 2000));
		starterPack.add(new ItemHolder(90907, 500));
		starterPack.add(new ItemHolder(91641, 100));
		starterPack.add(new ItemHolder(91912, 5000));
		starterPack.add(new ItemHolder(94269, 50));
		starterPack.add(new ItemHolder(94271, 50));

		LIGHT_PACK.add(new ItemHolder(91757, 50)); // Magic Lamp Charging Potion
		LIGHT_PACK.add(new ItemHolder(92991, 10)); // Einhasad's Blessing Scroll
		LIGHT_PACK.addAll(starterPack);

		STANDARD_PACK.add(new ItemHolder(91757, 100)); // Magic Lamp Charging Potion
		STANDARD_PACK.add(new ItemHolder(92991, 20)); // Einhasad's Blessing Scroll
		STANDARD_PACK.add(new ItemHolder(100000, 1)); // Premium Account
		STANDARD_PACK.add(new ItemHolder(100014, 1)); // Cloaks' Appearance Chest
		STANDARD_PACK.addAll(starterPack);
		STANDARD_PACK.add(new ItemHolder(95681, 1)); // Premium Growth Kit

		DELUXE_PACK.add(new ItemHolder(91757, 250)); // Magic Lamp Charging Potion
		DELUXE_PACK.add(new ItemHolder(92991, 50)); // Einhasad's Blessing Scroll
		DELUXE_PACK.add(new ItemHolder(91767, 10)); // Enchant Kit: Talisman of Aden
		DELUXE_PACK.add(new ItemHolder(100000, 1)); // Premium Account
		DELUXE_PACK.add(new ItemHolder(100005, 1)); // Color Nickname
		DELUXE_PACK.add(new ItemHolder(100014, 1)); // Cloaks' Appearance Chest
		DELUXE_PACK.addAll(starterPack);
		DELUXE_PACK.add(new ItemHolder(95681, 1)); // Premium Growth Kit
	}

	public PromoCodeManager() {
		try (Connection conn = DatabaseFactory.getConnection()) {
			try (PreparedStatement ps = conn.prepareStatement(SELECT_CODES)) {
				try (ResultSet rs = ps.executeQuery()) {
					while (rs.next()) {
						String code = rs.getString("code");
						PromoCodeType type = PromoCodeType.valueOf(rs.getString("type"));
						String params = rs.getString("params");
						String used = rs.getString("used");
						_codes.put(code, new PromoCodeHolder(code, type, params, used));
					}
				}
			}
			try (PreparedStatement ps = conn.prepareStatement(SELECT_CODES_LAUNCH_PACK)) {
				try (ResultSet rs = ps.executeQuery()) {
					while (rs.next()) {
						String code = rs.getString("code");
						PromoCodeType type = PromoCodeType.LAUNCH_PACK;
						String params = rs.getString("amount");
						String used = rs.getString("claimed");
						_codes.put(code, new PromoCodeHolder(code, type, params, used));
					}
				}
			}
		} catch (Exception e) {
			LOGGER.warning("Error in " + getClass().getSimpleName() + ":" + e);
			e.printStackTrace();
		}

		ThreadPool.get().scheduleAtFixedRate(this::sendCodeEmails, INTERVAL, INTERVAL);
	}

	public static PromoCodeManager getInstance() {
		return SingletonHolder.INSTANCE;
	}

	private int getLaunchPackIdByPrice(float price) {
		if ((price > 4.8) && (price < 12.5)) {
			return 1;
		} else if ((price > 12.3) && (price < 20)) {
			return 2;
		} else if (price > 19.8) {
			return 3;
		}
		return 0;
	}

	private void sendCodeEmails() {
		try (Connection con = DatabaseFactory.getConnection()) {
			List<String> sentEmails = new ArrayList<>();
			try (PreparedStatement ps = con.prepareStatement("SELECT * FROM donate_packs WHERE claimed = 'PAID'")) {
				ResultSet rs = ps.executeQuery();
				while (rs.next()) {
					String email = rs.getString("email");
					if ((email != null) && !email.isBlank() && !email.equals("GENERATED")) {
						int id = rs.getInt("id");
						String date = rs.getString("date");
						float amount = rs.getFloat("amount");
						String platform = rs.getString("platform");
						String transactionId = rs.getString("transactionid");
						String code = rs.getString("code");

						Properties properties = System.getProperties();
						properties.setProperty("mail.smtp.host", "mail.privateemail.com");
						properties.setProperty("mail.smtp.port", "587");
						properties.setProperty("mail.smtp.auth", "true");
						properties.put("mail.smtp.starttls.enable", "true");
						Session session = Session.getDefaultInstance(properties, new Authenticator() {
							@Override
							protected PasswordAuthentication getPasswordAuthentication() {
								return new PasswordAuthentication("<EMAIL>", "projectessenceclub2021");
							}
						});

						try {
							MimeMessage message = new MimeMessage(session);
							message.setFrom(new InternetAddress("<EMAIL>"));
							message.addRecipient(Message.RecipientType.TO, new InternetAddress(email));
							message.setSubject("Your purchase on projectessence");
							int packId = getLaunchPackIdByPrice(amount);
							String packName = packId == 1 ? "Light" : packId == 2 ? "Standard" : packId == 3 ? "Deluxe" : "";
							message.setContent(HtmCache.getInstance().getHtm(null, "data/html/mods/prelaunch-pack-mail-template.html").replace("{{code}}", code).replace("{{PACK_NAME}}", packName), "text/html");
							Transport.send(message);
							sentEmails.add(email);
							LOGGER.info(getClass().getSimpleName() + ": Sent email to " + email + " [ID: " + id + "] [Purchase date: " + date + "] [Amount: " + amount + "] [Platform: " + platform + "] [Transaction ID: " + transactionId + "] [Code: " + code + "]");
						} catch (MessagingException mex) {
							System.out.println(mex.getMessage());
							mex.printStackTrace();
						}
					}
				}
			}
			if (!sentEmails.isEmpty()) {
				try (PreparedStatement ps = con.prepareStatement("UPDATE donate_packs SET claimed='SENT' WHERE `email`=?")) {
					for (String email : sentEmails) {
						ps.setString(1, email);
						ps.addBatch();
					}
					ps.executeBatch();
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	public String generateCode(PromoCodeType type, String params) {
		switch (type) {
			case LAUNCH_PACK: {
				String code = Rnd.get(10000, 99999) + "-" //
						+ Rnd.get(10000, 99999) + "-" //
						+ Rnd.get(10000, 99999) + "-" //
						+ Rnd.get(10000, 99999) + "-" //
						+ Rnd.get(10000, 99999);
				_codes.put(code, new PromoCodeHolder(code, type, params, "SENT"));
				try (Connection conn = DatabaseFactory.getConnection()) {
					try (PreparedStatement ps = conn.prepareStatement(INSERT_CODE_LAUNCH_PACK)) {
						ps.setString(1, "");
						ps.setString(2, "GENERATED");
						ps.setString(3, params);
						ps.setString(4, "");
						ps.setString(5, "SENT");
						ps.setString(6, "NULL");
						ps.setInt(7, 0);
						ps.setString(8, code);
						ps.execute();
						LOGGER.info(getClass().getSimpleName() + ": generated new code: [" + code + "] [" + type.toString() + "] [" + params + "]");
						return code;
					}
				} catch (Exception e) {
					LOGGER.warning(getClass().getSimpleName() + ": failed inserting new code. " + e);
					e.printStackTrace();
				}
				break;
			}
			default: {
				String code = Util.generateRandomString(5).toUpperCase() + "-" + Util.generateRandomString(5).toUpperCase() + "-" + Util.generateRandomString(5).toUpperCase() + "-" + Util.generateRandomString(5).toUpperCase();
				_codes.put(code, new PromoCodeHolder(code, type, params, null));
				try (Connection conn = DatabaseFactory.getConnection()) {
					try (PreparedStatement ps = conn.prepareStatement(INSERT_CODE)) {
						ps.setString(1, code);
						ps.setString(2, type.toString());
						ps.setString(3, params);
						ps.setString(4, null);
						ps.execute();
						LOGGER.info(getClass().getSimpleName() + ": generated new code: [" + code + "] [" + type.toString() + "] [" + params + "]");
						return code;
					}
				} catch (Exception e) {
					LOGGER.warning(getClass().getSimpleName() + ": failed inserting new code. " + e);
					e.printStackTrace();
				}
				break;
			}
		}
		return null;
	}

	public void deleteCode(String code) {
		_codes.remove(code);
		try (Connection conn = DatabaseFactory.getConnection()) {
			try (PreparedStatement ps = conn.prepareStatement(DELETE_CODE)) {
				ps.setString(1, code);
				ps.execute();
			}
			try (PreparedStatement ps = conn.prepareStatement(DELETE_CODE_LAUNCH_PACK)) {
				ps.setString(1, code);
				ps.execute();
			}
		} catch (Exception e) {
			LOGGER.warning(getClass().getSimpleName() + ": failed deleting code. " + e);
			e.printStackTrace();
		}
	}

	public PromoCodeHolder getCode(String code) {
		return _codes.get(code);
	}

	public Map<String, PromoCodeHolder> getCodes() {
		return _codes;
	}

	public boolean useCode(PlayerInstance player, PromoCodeHolder codeHolder) {
		try {
			if (!codeHolder.isUsed()) {
				if (codeHolder.getType() == PromoCodeType.LAUNCH_PACK) {
					if (player.getAccountVariables().getString("LAUNCH_PACK_CLAIMED_2", "N").equals("Y")) {
						player.sendPacket(new CreatureSay(null, ChatType.HERO_VOICE, ">> Launch Pack", "Can be only claimed once per account!", 0));
						return false;
					}
					player.getAccountVariables().set("LAUNCH_PACK_CLAIMED_2", "Y");
				}
				codeHolder.setUser(player.getName());
				try (Connection conn = DatabaseFactory.getConnection()) {
					if (codeHolder.getType() == PromoCodeType.LAUNCH_PACK) {
						try (PreparedStatement ps = conn.prepareStatement(UPDATE_CODE_USER_LAUNCH_PACK)) {
							ps.setString(1, player.getName());
							ps.setLong(2, System.currentTimeMillis());
							ps.setString(3, codeHolder.getCode());
							ps.execute();
						}
					} else {
						try (PreparedStatement ps = conn.prepareStatement(UPDATE_CODE_USER)) {
							ps.setString(1, player.getName());
							ps.setString(2, codeHolder.getCode());
							ps.execute();
						}
					}
				} catch (Exception e) {
					LOGGER.warning(getClass().getSimpleName() + ": failed updating code. " + codeHolder.getCode() + " " + e);
					e.printStackTrace();
				}
				switch (codeHolder.getType()) {
					case BALANCE: {
						int amount = (int) (Double.parseDouble(codeHolder.getParams()) * 100);
						player.setPrimePoints(player.getPrimePoints() + amount);
						return true;
					}
					case ITEMS: {
						String items[] = codeHolder.getParams().split(";");
						for (String itemStr : items) {
							String split[] = itemStr.split(",");
							int itemId = Integer.parseInt(split[0]);
							long count = Long.parseLong(split[1]);
							player.addItem("Promo Code: " + codeHolder.getCode(), itemId, count, player, true);
						}
						return true;
					}
					case LAUNCH_PACK: {
						float amount = Float.parseFloat(codeHolder.getParams());
						List<ItemHolder> items = null;
						int packId = getLaunchPackIdByPrice(amount);
						if (packId == 1) {
							items = LIGHT_PACK;
							player.sendPacket(new CreatureSay(null, ChatType.HERO_VOICE, ">> Launch Pack", "Light pack has been claimed!", 0));
						} else if (packId == 2) {
							items = STANDARD_PACK;
							player.sendPacket(new CreatureSay(null, ChatType.HERO_VOICE, ">> Launch Pack", "Standard pack has been claimed!", 0));
						} else if (packId == 3) {
							items = DELUXE_PACK;
							player.sendPacket(new CreatureSay(null, ChatType.HERO_VOICE, ">> Launch Pack", "Deluxe pack has been claimed!", 0));
						}
						if (items != null) {
							for (ItemHolder item : items) {
								player.addItem("Launch Pack: " + codeHolder.getCode(), item, player, true);
							}
						}
					}
				}
			}
			return false;
		} catch (Exception e) {
			LOGGER.info(getClass().getSimpleName() + " " + player + " error redeeming code: [" + codeHolder.getCode() + "] [" + codeHolder.getType() + "] [" + codeHolder.getParams() + "]");
			e.printStackTrace();
			return false;
		} finally {
			LOGGER.info(getClass().getSimpleName() + " " + player + " redeemed code: [" + codeHolder.getCode() + "] [" + codeHolder.getType() + "] [" + codeHolder.getParams() + "]");
		}
	}

	public enum PromoCodeType {
		BALANCE,
		ITEMS,
		LAUNCH_PACK;
	}

	private static class SingletonHolder {
		protected static final PromoCodeManager INSTANCE = new PromoCodeManager();
	}
}
