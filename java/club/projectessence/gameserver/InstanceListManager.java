/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver;

/**
 * Interface for managers of list of instances.
 *
 * <AUTHOR>
 */
public interface InstanceListManager {
	/**
	 * Loads instances with their data from persistent format.<br>
	 * This method has no side effect as calling methods of another instance manager.
	 */
	void loadInstances();

	/**
	 * For each loaded instance, updates references to related instances.
	 */
	void updateReferences();

	/**
	 * Activates instances so their setup is performed.
	 */
	void activateInstances();
}
