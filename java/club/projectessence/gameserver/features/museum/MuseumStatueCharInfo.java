package club.projectessence.gameserver.features.museum;

import club.projectessence.commons.network.ServerPacketId;
import club.projectessence.gameserver.model.CharSelectInfoPackage;
import club.projectessence.gameserver.model.itemcontainer.Inventory;
import club.projectessence.gameserver.model.skills.AbnormalVisualEffect;
import club.projectessence.gameserver.network.GameClient;
import club.projectessence.gameserver.network.serverpackets.ServerPacket;
import io.github.joealisson.mmocore.WritableBuffer;

/**
 * MuseumStatueCharInfo - Send CharInfo packet for museum statues to make them look like players
 * Based on L2J Sunrise core.diff implementation
 */
public class MuseumStatueCharInfo extends ServerPacket
{
    private final MuseumStatueInstance _statue;
    private final CharSelectInfoPackage _charLooks;
    
    // Paperdoll order for equipment display - MUST match CharInfo exactly
    private static final int[] PAPERDOLL_ORDER = new int[]
    {
        Inventory.PAPERDOLL_UNDER,
        Inventory.PAPERDOLL_HEAD,
        Inventory.PAPERDOLL_RHAND,
        Inventory.PAPERDOLL_LHAND,
        Inventory.PAPERDOLL_GLOVES,
        Inventory.PAPERDOLL_CHEST,
        Inventory.PAPERDOLL_LEGS,
        Inventory.PAPERDOLL_FEET,
        Inventory.PAPERDOLL_CLOAK,
        Inventory.PAPERDOLL_RHAND,
        Inventory.PAPERDOLL_HAIR,
        Inventory.PAPERDOLL_HAIR2
    };
    
    public MuseumStatueCharInfo(MuseumStatueInstance statue)
    {
        _statue = statue;
        _charLooks = statue.getCharLooks();
    }
    
    @Override
    public void writeImpl(GameClient client, WritableBuffer buffer)
    {
        if (_charLooks == null)
        {
            return;
        }

        // Validate critical data to prevent client crash
        if (_charLooks.getName() == null || _charLooks.getName().isEmpty())
        {
            return;
        }

        // Additional validation
        if (_charLooks.getRace() < 0 || _charLooks.getRace() > 6)
        {
            return;
        }

        if (_charLooks.getSex() < 0 || _charLooks.getSex() > 1)
        {
            return;
        }

        if (_charLooks.getBaseClassId() < 0)
        {
            return;
        }

        // Use CharInfo packet ID to display as player
        writeId(ServerPacketId.CHAR_INFO, buffer);

        // Match CharInfo.java structure exactly
        buffer.writeByte(0x00); // Grand Crusade
        buffer.writeInt(_statue.getX()); // Confirmed
        buffer.writeInt(_statue.getY()); // Confirmed
        buffer.writeInt(_statue.getZ()); // Confirmed
        buffer.writeInt(0); // Vehicle ID - Confirmed
        buffer.writeInt(_statue.getObjectId()); // Confirmed
        buffer.writeString(_charLooks.getName()); // Confirmed
        buffer.writeShort(_charLooks.getRace()); // Confirmed - SHORT not INT
        buffer.writeByte(_charLooks.getSex() == 1 ? 0x01 : 0x00); // Confirmed - BYTE not INT
        buffer.writeInt(_charLooks.getBaseClassId()); // Root class ID

        // Equipment (paperdoll) - match CharInfo exactly
        for (int slot : PAPERDOLL_ORDER)
        {
            buffer.writeInt(_charLooks.getPaperdollItemId(slot));
        }

        // Augmentations - match CharInfo exactly
        int[] augmentSlots = {
            Inventory.PAPERDOLL_RHAND,
            Inventory.PAPERDOLL_LHAND,
            Inventory.PAPERDOLL_GLOVES,
            Inventory.PAPERDOLL_CHEST,
            Inventory.PAPERDOLL_LEGS,
            Inventory.PAPERDOLL_FEET
        };
        
        for (int slot : augmentSlots)
        {
            buffer.writeInt(0); // option1Id
            buffer.writeInt(0); // option2Id
        }

        // Armor enchant - match CharInfo exactly
        buffer.writeByte(0);

        // Visual IDs - match CharInfo exactly
        for (int slot : PAPERDOLL_ORDER)
        {
            buffer.writeInt(_charLooks.getPaperdollItemId(slot));
        }

        // PvP/Karma - match CharInfo exactly
        buffer.writeByte(0); // PvP flag
        buffer.writeInt(0); // Reputation

        // Speeds - match CharInfo exactly
        buffer.writeInt(0); // mAtkSpd
        buffer.writeInt(0); // pAtkSpd
        buffer.writeShort(0); // runSpd
        buffer.writeShort(0); // walkSpd
        buffer.writeShort(0); // swimRunSpd
        buffer.writeShort(0); // swimWalkSpd
        buffer.writeShort(0); // flyRunSpd
        buffer.writeShort(0); // flyWalkSpd
        buffer.writeShort(0); // flyRunSpd (duplicate)
        buffer.writeShort(0); // flyWalkSpd (duplicate)

        // Multipliers - match CharInfo exactly (DOUBLE not FLOAT)
        buffer.writeDouble(1.0); // Move multiplier
        buffer.writeDouble(1.0); // Attack speed multiplier

        // Collision - match CharInfo exactly (DOUBLE not FLOAT)
        buffer.writeDouble(8.0); // Collision radius
        buffer.writeDouble(23.0); // Collision height
        
        // Appearance - match CharInfo exactly
        buffer.writeInt(_charLooks.getHairStyle());
        buffer.writeInt(_charLooks.getHairColor());
        buffer.writeInt(_charLooks.getFace());

        // Title - match CharInfo exactly
        buffer.writeString(_statue.getTitle());

        // Clan info - match CharInfo exactly
        buffer.writeInt(0); // Clan ID
        buffer.writeInt(0); // Clan crest ID
        buffer.writeInt(0); // Ally ID
        buffer.writeInt(0); // Ally crest ID

        // Status flags - match CharInfo exactly
        buffer.writeByte(0x01); // Sitting (standing)
        buffer.writeByte(0x00); // Running
        buffer.writeByte(0x00); // In combat
        buffer.writeByte(0x00); // Dead
        buffer.writeByte(0x00); // Invisible
        buffer.writeByte(0x00); // Mount type
        buffer.writeByte(0x00); // Private store

        // Cubics - match CharInfo exactly
        buffer.writeShort(0);

        // More status - match CharInfo exactly
        buffer.writeByte(0x00); // Matching room
        buffer.writeByte(0x00); // Water/flying
        buffer.writeShort(0); // Recommendations
        buffer.writeInt(0); // Mount NPC ID
        buffer.writeInt(_charLooks.getClassId());
        buffer.writeInt(0x00); // Unknown
        buffer.writeByte(0); // Enchant level
        buffer.writeByte(0x00); // Team
        buffer.writeInt(0); // Clan crest large
        buffer.writeByte(_charLooks.isNoble() ? 1 : 0);
        buffer.writeByte(0); // Hero
        buffer.writeByte(0); // Fishing

        // Fishing location - match CharInfo exactly
        buffer.writeInt(0);
        buffer.writeInt(0);
        buffer.writeInt(0);

        // Name color - match CharInfo exactly
        buffer.writeInt(0xFFFFFF); // White name color
        buffer.writeInt(_statue.getHeading());
        buffer.writeByte(0); // Pledge class
        buffer.writeShort(0); // Pledge type
        buffer.writeInt(0xFFFF77); // Title color
        buffer.writeByte(0); // Cursed weapon
        buffer.writeInt(0); // Clan reputation
        buffer.writeInt(0); // Transformation
        buffer.writeInt(0); // Agathion
        buffer.writeByte(0x00); // Unknown

        // HP/MP/CP - match CharInfo exactly
        buffer.writeInt(1000); // Current CP
        buffer.writeInt(1000); // Max HP
        buffer.writeInt(1000); // Current HP
        buffer.writeInt(1000); // Max MP
        buffer.writeInt(1000); // Current MP

        // Unknown - match CharInfo exactly
        buffer.writeByte(0x00);

        // Abnormal effects - add both stone and hero weapon effects for beautiful statue appearance
        buffer.writeInt(2); // Two effects: stone + hero weapon
        buffer.writeShort(AbnormalVisualEffect.FLESH_STONE.getClientId());
        buffer.writeShort(AbnormalVisualEffect.V_WORLDCASTLEWAR_HERO_WEAPON_AVE.getClientId());

        // Position/Hero - match CharInfo exactly
        buffer.writeByte(0); // Position
        buffer.writeByte(0x00); // Hair accessory
        buffer.writeByte(0); // Ability points used
        buffer.writeInt(0x00); // Unknown

        // Castle/Rank - match CharInfo exactly
        buffer.writeInt(0x00); // Castle owner
        buffer.writeInt(0x00); // Rank

        // Final fields - match CharInfo exactly
        buffer.writeShort(0x00);
        buffer.writeByte(0x00);
        buffer.writeInt(0x00);
        buffer.writeByte(0x00);
        buffer.writeInt(0x00);
        buffer.writeInt(0x00);
        buffer.writeByte(0); // Vanguard beast ID


    }
}
