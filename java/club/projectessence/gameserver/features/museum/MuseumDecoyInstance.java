package club.projectessence.gameserver.features.museum;

import club.projectessence.gameserver.model.CharSelectInfoPackage;
import club.projectessence.gameserver.model.actor.Creature;
import club.projectessence.gameserver.model.actor.instance.DecoyInstance;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.model.actor.templates.NpcTemplate;
import club.projectessence.gameserver.network.serverpackets.CharInfo;

/**
 * MuseumDecoyInstance - Uses DecoyInstance pattern to display museum statues as players
 * This leverages existing CharInfo support for DecoyInstance
 */
public class MuseumDecoyInstance extends DecoyInstance
{
    private final MuseumStatueInstance _originalStatue;
    private final CharSelectInfoPackage _charLooks;
    
    public MuseumDecoyInstance(NpcTemplate template, MuseumStatueInstance originalStatue, PlayerInstance fakeOwner)
    {
        super(template, fakeOwner, Integer.MAX_VALUE); // Never expire
        _originalStatue = originalStatue;
        _charLooks = originalStatue.getCharLooks();
        
        // Set position to match original statue
        setXYZ(originalStatue.getX(), originalStatue.getY(), originalStatue.getZ());
        setHeading(originalStatue.getHeading());
        
        // Set title
        setTitle(originalStatue.getTitle());
        
        // Make immobile
        setInvul(true);
        setImmobilized(true);
    }
    
    @Override
    public void sendInfo(PlayerInstance player)
    {
        if (isVisibleFor(player))
        {
            // Use existing CharInfo constructor for DecoyInstance
            // This will display the statue as a player using the owner's appearance
            player.sendPacket(new CharInfo(this, false));
        }
    }
    
    @Override
    public PlayerInstance getActingPlayer()
    {
        // Return the fake owner (which should have the appearance we want)
        return super.getActingPlayer();
    }
    
    public MuseumStatueInstance getOriginalStatue()
    {
        return _originalStatue;
    }
    
    public CharSelectInfoPackage getCharLooks()
    {
        return _charLooks;
    }
    
    public void showChatWindow(PlayerInstance player)
    {
        // Delegate to original statue
        if (_originalStatue != null)
        {
            _originalStatue.showChatWindow(player);
        }
    }

    public void onBypassFeedback(PlayerInstance player, String command)
    {
        // Delegate to original statue
        if (_originalStatue != null)
        {
            _originalStatue.onBypassFeedback(player, command);
        }
    }

    @Override
    public boolean isAutoAttackable(Creature attacker)
    {
        // Museum statues cannot be attacked
        return false;
    }
}
