package club.projectessence.gameserver.features.museum;

import club.projectessence.gameserver.model.Location;
import java.util.ArrayList;

/**
 * Museum Pillar - Represents a combined achievement pillar
 * Uses normalize-based scoring from multiple achievement types
 * 
 * <AUTHOR>
 */
public class MuseumPillar 
{
    private String _name;
    private Location _spawnLocation;
    private ArrayList<String> _includedTypes;
    private TopPlayer _currentLeader;
    private double _currentLeaderScore;
    private MuseumStatueInstance _spawnedStatue;
    
    public MuseumPillar(String name, Location spawnLocation, ArrayList<String> includedTypes)
    {
        _name = name;
        _spawnLocation = spawnLocation;
        _includedTypes = includedTypes;
        _currentLeader = null;
        _currentLeaderScore = 0.0;
        _spawnedStatue = null;
    }
    
    public String getName()
    {
        return _name;
    }
    
    public Location getSpawnLocation()
    {
        return _spawnLocation;
    }
    
    public ArrayList<String> getIncludedTypes()
    {
        return _includedTypes;
    }
    
    public TopPlayer getCurrentLeader()
    {
        return _currentLeader;
    }
    
    public void setCurrentLeader(TopPlayer leader)
    {
        _currentLeader = leader;
    }
    
    public double getCurrentLeaderScore()
    {
        return _currentLeaderScore;
    }
    
    public void setCurrentLeaderScore(double score)
    {
        _currentLeaderScore = score;
    }
    
    public MuseumStatueInstance getSpawnedStatue()
    {
        return _spawnedStatue;
    }
    
    public void setSpawnedStatue(MuseumStatueInstance statue)
    {
        _spawnedStatue = statue;
    }
    
    /**
     * Check if this pillar includes a specific achievement type
     */
    public boolean includesType(String type)
    {
        return _includedTypes.contains(type);
    }
}
