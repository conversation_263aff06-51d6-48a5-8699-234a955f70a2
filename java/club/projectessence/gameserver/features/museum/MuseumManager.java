package club.projectessence.gameserver.features.museum;

import java.io.File;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.HashMap;
import java.util.Map;
import java.util.logging.Logger;

import javax.xml.parsers.DocumentBuilderFactory;

import club.projectessence.commons.concurrent.ThreadPool;
import club.projectessence.commons.database.DatabaseFactory;
import club.projectessence.gameserver.data.xml.NpcData;
import club.projectessence.gameserver.instancemanager.GlobalVariables;
import club.projectessence.gameserver.model.World;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import org.w3c.dom.Document;
import org.w3c.dom.Node;

import club.projectessence.Config;
import club.projectessence.gameserver.model.Location;

public class MuseumManager
{
	private static final Logger _log = Logger.getLogger(MuseumManager.class.getName());
	private final HashMap<Integer, String> _categoryNames;
	private final HashMap<Integer, MuseumCategory> _categories;
	private final HashMap<Integer, ArrayList<MuseumCategory>> _categoriesByCategoryId;
	private final HashMap<Integer, ArrayList<Integer>> _playersWithReward;

	// Pillar system additions
	private final HashMap<String, MuseumPillar> _pillars;
	private final HashMap<String, Integer> _serverMaxValues;

	private int refreshTotal;

	public MuseumManager()
	{
		refreshTotal = 3600;
		_categoryNames = new HashMap<>();
		_categories = new HashMap<>();
		_categoriesByCategoryId = new HashMap<>();
		_playersWithReward = new HashMap<>();

		// Initialize pillar system
		_pillars = new HashMap<>();
		_serverMaxValues = new HashMap<>();

		loadCategories();
		final long monthlyUpdate = Math.max(100L, (GlobalVariables.getInstance().hasVariable("museum_monthly") ? GlobalVariables.getInstance().getLong("museum_monthly") : 0L) - System.currentTimeMillis());
		final long weeklyUpdate = Math.max(100L, (GlobalVariables.getInstance().hasVariable("museum_weekly") ? GlobalVariables.getInstance().getLong("museum_weekly") : 0L) - System.currentTimeMillis());
		final long dailyUpdate = Math.max(100L, (GlobalVariables.getInstance().hasVariable("museum_daily") ? GlobalVariables.getInstance().getLong("museum_daily") : 0L) - System.currentTimeMillis());
		ThreadPool.scheduleAtFixedRate(new UpdateStats(RefreshTime.Total), 100L, refreshTotal * 1000L);
		ThreadPool.schedule(new UpdateStats(RefreshTime.Monthly), monthlyUpdate);
		ThreadPool.schedule(new UpdateStats(RefreshTime.Weekly), weeklyUpdate);
		ThreadPool.schedule(new UpdateStats(RefreshTime.Daily), dailyUpdate);
	}
	public void giveRewards()
	{
		final ArrayList<Integer> withReward = new ArrayList<>();
		for (final Map.Entry<Integer, ArrayList<Integer>> entry : _playersWithReward.entrySet())
		{
			final PlayerInstance player = World.getInstance().getPlayer(entry.getKey());
			if (player != null)
			{
				if (!player.isOnline())
				{
					continue;
				}
				final ArrayList<Integer> cats = entry.getValue();
				for (final int catId : cats)
				{
					if (!_categories.containsKey(catId))
					{
						withReward.add(entry.getKey());
					}
					else
					{
						final MuseumCategory cat = _categories.get(catId);
						if (cat == null)
						{
							withReward.add(entry.getKey());
						}
						else
						{
							for (final MuseumReward reward : cat.getRewards())
							{
								reward.giveReward(player);
								withReward.add(entry.getKey());
							}
						}
					}
				}
			}
		}
		for (final int i : withReward)
		{
			_playersWithReward.remove(i);
		}
		if (_playersWithReward.size() == 0)
		{
			return;
		}
	}

	public void giveReward(final PlayerInstance player)
	{
		if (!_playersWithReward.containsKey(player.getObjectId()))
		{
			return;
		}
		final ArrayList<Integer> cats = _playersWithReward.get(player.getObjectId());
		if (cats.size() < 1)
		{
			_playersWithReward.remove(player.getObjectId());
			return;
		}
		for (final int catId : cats)
		{
			if (!_categories.containsKey(catId))
			{
				continue;
			}
			final MuseumCategory cat = _categories.get(catId);
			for (final MuseumReward reward : cat.getRewards())
			{
				reward.giveReward(player);
			}
		}
		_playersWithReward.remove(player.getObjectId());
	}

	public void restoreLastTops(final RefreshTime time)
	{
		for (final MuseumCategory cat : getAllCategories().values())
		{
			int i = 1;
			if (!cat.getRefreshTime().equals(time) && !time.equals(RefreshTime.Total))
			{
				continue;
			}
			cat.getAllStatuePlayers().clear();
			try (final Connection con = DatabaseFactory.getConnection();
				final PreparedStatement statement = con.prepareStatement("SELECT * FROM museum_last_statistics as mls INNER JOIN museum_statistics as ms ON ms.objectId=mls.objectId WHERE mls.category = ? AND mls.category = ms.category AND mls.count > 0 AND mls.timer = '" + cat.getRefreshTime().name().toLowerCase() + "' ORDER BY mls.count DESC LIMIT 5"))
			{
				statement.setString(1, cat.getType());
				try (final ResultSet rset = statement.executeQuery())
				{
					while (rset.next())
					{
						final int objectId = rset.getInt("objectId");
						final String name = rset.getString("name");
						final long count = rset.getLong("count");
						cat.getAllStatuePlayers().put(i, new TopPlayer(objectId, name, count));
						++i;
					}
				}
			}
			catch (Exception e)
			{
				_log.severe("Failed loading character museum data: " + e.getMessage());
			}
		}
	}


	public void cleanLastTops(final RefreshTime time)
	{
		try (final Connection con = DatabaseFactory.getConnection();
			 final PreparedStatement statement = con.prepareStatement("DELETE FROM museum_last_statistics WHERE timer='" + time.name().toLowerCase() + "'"))
		{
			statement.execute();
		}
		catch (Exception e)
		{
			_log.warning("Could not store char museum data: " + e.getMessage());
		}
	}

	public void refreshTopsFromDatabase(final RefreshTime time)
	{
		_playersWithReward.clear();
		try (final Connection con = DatabaseFactory.getConnection())
		{
			for (final MuseumCategory cat : _categories.values())
			{
				if (!cat.getRefreshTime().equals(time) && !time.equals(RefreshTime.Total))
				{
					continue;
				}
				cat.getAllTops().clear();
				cat.getAllTotalTops().clear();
				cat.getAllStatuePlayers().clear();
				int i = 1;
				int h = 1;
				try (
					final PreparedStatement statement = con.prepareStatement("SELECT * FROM museum_statistics WHERE category = ? AND " + cat.getRefreshTime().name().toLowerCase() + "_count > 0 ORDER BY " + cat.getRefreshTime().name().toLowerCase() + "_count DESC LIMIT " + (cat.getRefreshTime().equals(RefreshTime.Total) ? 20 : 10)))
				{
					statement.setString(1, cat.getType());
					try (final ResultSet rset = statement.executeQuery())
					{
						while (rset.next())
						{
							final int objectId = rset.getInt("objectId");
							final String name = rset.getString("name");
							final long count = rset.getLong(cat.getRefreshTime().name().toLowerCase() + "_count");
							final boolean hasReward = rset.getBoolean("hasReward");
							if (hasReward)
							{
								if (!_playersWithReward.containsKey(objectId))
								{
									_playersWithReward.put(objectId, new ArrayList<Integer>());
								}
								_playersWithReward.get(objectId).add((cat.getCategoryId() * 256) + cat.getTypeId());
							}
							if (cat.getRefreshTime().equals(RefreshTime.Total))
							{
								cat.getAllTotalTops().put(i, new TopPlayer(objectId, name, count));
							}
							else
							{
								cat.getAllTops().put(i, new TopPlayer(objectId, name, count));
							}
							if ((i < 6) && time.equals(cat.getRefreshTime()))
							{
								try (final PreparedStatement stat = con.prepareStatement("REPLACE museum_last_statistics SET objectId=" + objectId + ", name='" + name + "', category='" + cat.getType() + "', count=" + count + ", timer='" + time.name().toLowerCase() + "';"))
								{
									stat.execute();
								}
								if (i == 1)
								{
									try (final PreparedStatement stat = con.prepareStatement("UPDATE museum_statistics SET hasReward = 1 WHERE objectId = " + objectId + " AND category = '" + cat.getType() + "'"))
									{
										stat.execute();
									}
									if (!_playersWithReward.containsKey(objectId))
									{
										_playersWithReward.put(objectId, new ArrayList<Integer>());
									}
									_playersWithReward.get(objectId).add((cat.getCategoryId() * 256) + cat.getTypeId());
								}
							}
							++i;
						}
					}
				}
				if (cat.getRefreshTime().equals(RefreshTime.Total))
				{
					continue;
				}
				try (final PreparedStatement statement = con.prepareStatement("SELECT * FROM museum_statistics WHERE category = ? AND total_count > 0 ORDER BY total_count DESC LIMIT 10"))
				{
					statement.setString(1, cat.getType());
					try (final ResultSet rset = statement.executeQuery())
					{
						while (rset.next())
						{
							final int objectId = rset.getInt("objectId");
							final String name = rset.getString("name");
							final long count = rset.getLong("total_count");
							cat.getAllTotalTops().put(h, new TopPlayer(objectId, name, count));
							++h;
						}
					}
				}
			}
			if (!time.equals(RefreshTime.Total))
			{
				try (final PreparedStatement statement2 = con.prepareStatement("UPDATE museum_statistics SET " + time.name().toLowerCase() + "_count = 0"))
				{
					statement2.execute();
				}
			}
		}
		catch (Exception e)
		{
			_log.warning("Could not store char museum data: " + e.getMessage());
		}
		restoreLastTops(time);
		giveRewards();
	}
	public void loadCategories()
	{
		_log.info(this.getClass().getSimpleName() + ": Initializing");
		_categoryNames.clear();
		_categories.clear();
		_categoriesByCategoryId.clear();
		final DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
		factory.setValidating(false);
		factory.setIgnoringComments(true);
		final File file = new File(Config.DATAPACK_ROOT, "data/xml/sunrise/MuseumCategories.xml");
		Document doc = null;
		if (file.exists())
		{
			try
			{
				doc = factory.newDocumentBuilder().parse(file);
			}
			catch (Exception e)
			{
				_log.warning("Could not parse MuseumCategories.xml file: " + e.getMessage());
				return;
			}
			int categoryId = 0;
			final Node n = doc.getFirstChild();
			for (Node d = n.getFirstChild(); d != null; d = d.getNextSibling())
			{
				if (d.getNodeName().equalsIgnoreCase("set"))
				{
					final String name = d.getAttributes().getNamedItem("name").getNodeValue();
					final String val = d.getAttributes().getNamedItem("val").getNodeValue();
					if (name.equalsIgnoreCase("refreshAllStatisticsIn"))
					{
						refreshTotal = Integer.parseInt(val);
					}
				}
				// Parse pillars section
				if (d.getNodeName().equalsIgnoreCase("pillars"))
				{
					for (Node pillarNode = d.getFirstChild(); pillarNode != null; pillarNode = pillarNode.getNextSibling())
					{
						if (pillarNode.getNodeName().equalsIgnoreCase("pillar"))
						{
							final String pillarName = pillarNode.getAttributes().getNamedItem("name").getNodeValue();
							final int spawnX = Integer.parseInt(pillarNode.getAttributes().getNamedItem("spawn_x").getNodeValue());
							final int spawnY = Integer.parseInt(pillarNode.getAttributes().getNamedItem("spawn_y").getNodeValue());
							final int spawnZ = Integer.parseInt(pillarNode.getAttributes().getNamedItem("spawn_z").getNodeValue());
							final int spawnHeading = Integer.parseInt(pillarNode.getAttributes().getNamedItem("spawn_heading").getNodeValue());

							final Location spawnLocation = new Location(spawnX, spawnY, spawnZ, spawnHeading);
							final ArrayList<String> includedTypes = new ArrayList<>();

							for (Node includeNode = pillarNode.getFirstChild(); includeNode != null; includeNode = includeNode.getNextSibling())
							{
								if (includeNode.getNodeName().equalsIgnoreCase("include"))
								{
									final String includeType = includeNode.getAttributes().getNamedItem("type").getNodeValue();
									includedTypes.add(includeType);
								}
							}

							_pillars.put(pillarName, new MuseumPillar(pillarName, spawnLocation, includedTypes));
						}
					}
				}
				if (d.getNodeName().equalsIgnoreCase("category"))
				{
					final ArrayList<MuseumCategory> list = new ArrayList<>();
					final String categoryName = d.getAttributes().getNamedItem("name").getNodeValue();
					int typeId = 0;
					for (Node h = d.getFirstChild(); h != null; h = h.getNextSibling())
					{
						if (h.getNodeName().equalsIgnoreCase("type"))
						{
							final String typeName = h.getAttributes().getNamedItem("name").getNodeValue();
							final String type = h.getAttributes().getNamedItem("type").getNodeValue();
							final String refreshTime = h.getAttributes().getNamedItem("refreshTime").getNodeValue();
							boolean timer = false;
							if (h.getAttributes().getNamedItem("timer") != null)
							{
								timer = Boolean.parseBoolean(h.getAttributes().getNamedItem("timer").getNodeValue());
							}
							String additionalText = "";
							if (h.getAttributes().getNamedItem("additionalText") != null)
							{
								additionalText = h.getAttributes().getNamedItem("additionalText").getNodeValue();
							}
							final ArrayList<Location> statueSpawns = new ArrayList<>();
							final ArrayList<MuseumReward> rewards = new ArrayList<>();
							int rewardId = 0;
							for (Node a = h.getFirstChild(); a != null; a = a.getNextSibling())
							{
								if (a.getNodeName().equalsIgnoreCase("spawn"))
								{
									final int x = Integer.parseInt(a.getAttributes().getNamedItem("x").getNodeValue());
									final int y = Integer.parseInt(a.getAttributes().getNamedItem("y").getNodeValue());
									final int z = Integer.parseInt(a.getAttributes().getNamedItem("z").getNodeValue());
									final int heading = (a.getAttributes().getNamedItem("heading") != null) ? Integer.parseInt(a.getAttributes().getNamedItem("heading").getNodeValue()) : 0;
									statueSpawns.add(new Location(x, y, z, heading));
								}
								if (a.getNodeName().equalsIgnoreCase("reward"))
								{
									String rewardType = "";
									int itemId = 0;
									int minCount = 0;
									int maxCount = 0;
									double chance = 0.0;
									if (a.getAttributes().getNamedItem("type") != null)
									{
										rewardType = a.getAttributes().getNamedItem("type").getNodeValue();
									}
									if (a.getAttributes().getNamedItem("id") != null)
									{
										itemId = Integer.parseInt(a.getAttributes().getNamedItem("id").getNodeValue());
									}
									if (a.getAttributes().getNamedItem("min") != null)
									{
										minCount = Integer.parseInt(a.getAttributes().getNamedItem("min").getNodeValue());
									}
									if (a.getAttributes().getNamedItem("max") != null)
									{
										maxCount = Integer.parseInt(a.getAttributes().getNamedItem("max").getNodeValue());
									}
									if (a.getAttributes().getNamedItem("chance") != null)
									{
										chance = Double.parseDouble(a.getAttributes().getNamedItem("chance").getNodeValue());
									}
									rewards.add(new MuseumReward(rewardId, rewardType, itemId, minCount, maxCount, chance));
									++rewardId;
								}
							}
							final int key = (categoryId * 256) + typeId;
							final MuseumCategory category = new MuseumCategory(categoryId, typeId, categoryName, typeName, type, refreshTime, timer, additionalText, statueSpawns, rewards);
							list.add(category);
							_categories.put(key, category);
							++typeId;
						}
					}
					_categoriesByCategoryId.put(categoryId, list);
					_categoryNames.put(categoryId, categoryName);
					++categoryId;
				}
			}
		}
		_log.info(this.getClass().getSimpleName() + ": Successfully loaded " + _categoryNames.size() + " categories, " + _categories.size() + " post categories, and " + _pillars.size() + " museum pillars.");
	}
	public HashMap<Integer, String> getAllCategoryNames()
	{
		return _categoryNames;
	}

	public HashMap<Integer, MuseumCategory> getAllCategories()
	{
		return _categories;
	}

	public ArrayList<MuseumCategory> getAllCategoriesByCategoryId(final int id)
	{
		if (_categoriesByCategoryId.containsKey(id))
		{
			return _categoriesByCategoryId.get(id);
		}
		return null;
	}

	public void restoreDataForChar(final PlayerInstance player)
	{
		final HashMap<String, long[]> data = new HashMap<>();
		try (final Connection con = DatabaseFactory.getConnection();
			final PreparedStatement statement = con.prepareStatement("SELECT * FROM museum_statistics WHERE objectId = ?"))
		{
			statement.setInt(1, player.getObjectId());
			try (final ResultSet rset = statement.executeQuery())
			{
				while (rset.next())
				{
					final long[] d =
					{
						rset.getLong("total_count"),
						rset.getLong("monthly_count"),
						rset.getLong("weekly_count"),
						rset.getLong("daily_count")
					};
					final String category = rset.getString("category");
					data.put(category, d);
				}
			}
		}
		catch (Exception e)
		{
			_log.severe("Failed loading character museum data: " + e.getMessage());
		}
		player.setMuseumPlayer(new MuseumPlayer(player.getObjectId(), player.getName(), data));
	}

	public void updateDataForChar(final PlayerInstance player)
	{
		if (player.getMuseumPlayer() == null)
		{
			return;
		}
		String update = "";
		try (final Connection con = DatabaseFactory.getConnection())
		{
			for (final Map.Entry<String, long[]> entry : player.getMuseumPlayer().getData().entrySet())
			{
				update = "REPLACE museum_statistics SET objectId=" + player.getObjectId() + ", name='" + player.getName() + "', category='" + entry.getKey() + "', total_count=" + entry.getValue()[0] + ", monthly_count=" + entry.getValue()[1] + ", weekly_count=" + entry.getValue()[2] + ", daily_count=" + entry.getValue()[3] + ", hasReward=0;";
				try (final PreparedStatement statement = con.prepareStatement(update))
				{
					statement.execute();
				}
			}
		}
		catch (Exception e)
		{
			_log.warning("Could not store char museum data: " + e.getMessage());
		}
	}

	public void reloadConfigs()
	{
		loadCategories();
		restoreLastTops(RefreshTime.Total);
	}

	/**
	 * Calculate normalized score for a player's achievement type
	 * Formula: (playerValue / maxValueOnServer) * 100
	 */
	public double calculateNormalizedScore(PlayerInstance player, String achievementType)
	{
		if (player.getMuseumPlayer() == null)
		{
			// Try to restore museum data for player
			restoreDataForChar(player);
			if (player.getMuseumPlayer() == null)
			{
				return 0.0;
			}
		}

		long playerValue = player.getMuseumPlayer().getData(RefreshTime.Total, achievementType);
		int maxValue = _serverMaxValues.getOrDefault(achievementType, 1); // Avoid division by zero

		if (Config.DEBUG)
		{
			_log.info("Normalized score for " + player.getName() + " - " + achievementType + ": " + playerValue + "/" + maxValue + " = " + ((double) playerValue / maxValue) * 100.0);
		}

		return ((double) playerValue / maxValue) * 100.0;
	}

	/**
	 * Calculate combined pillar score for a player
	 */
	public double calculatePillarScore(PlayerInstance player, String pillarName)
	{
		MuseumPillar pillar = _pillars.get(pillarName);
		if (pillar == null || player.getMuseumPlayer() == null)
		{
			return 0.0;
		}

		double totalScore = 0.0;

		for (String achievementType : pillar.getIncludedTypes())
		{
			if (achievementType.equals("all_combined"))
			{
				// Server Legend - sum of all other pillars
				for (String otherPillarName : _pillars.keySet())
				{
					if (!otherPillarName.equals(pillarName))
					{
						totalScore += calculatePillarScore(player, otherPillarName);
					}
				}
			}
			else
			{
				double normalizedScore = calculateNormalizedScore(player, achievementType);

				// Special handling for PvP defeats (reduce score)
				if (achievementType.equals("pvp_defeats"))
				{
					totalScore -= normalizedScore * 0.5;
				}
				else
				{
					totalScore += normalizedScore;
				}
			}
		}

		return Math.max(0, totalScore); // Ensure non-negative
	}

	/**
	 * Update pillar leaders after statistics refresh
	 */
	public void updatePillarLeaders()
	{
		updateServerMaxValues();

		for (MuseumPillar pillar : _pillars.values())
		{
			TopPlayer bestPlayer = null;
			double bestScore = 0.0;



			// Check all players with museum data (from database)
			try (Connection con = DatabaseFactory.getConnection();
				 PreparedStatement statement = con.prepareStatement("SELECT DISTINCT objectId, name FROM museum_statistics"))
			{
				try (ResultSet rset = statement.executeQuery())
				{
					while (rset.next())
					{
						int objectId = rset.getInt("objectId");
						String playerName = rset.getString("name");

						// Calculate pillar score directly from database
						double playerScore = calculatePillarScoreFromDatabase(objectId, playerName, pillar.getName());

						if (playerScore > bestScore)
						{
							bestScore = playerScore;
							bestPlayer = new TopPlayer(objectId, playerName, (long) playerScore);
						}
					}
				}
			}
			catch (Exception e)
			{
				_log.warning("Error checking pillar leaders: " + e.getMessage());
				e.printStackTrace();
			}

			if (bestPlayer != null)
			{
				pillar.setCurrentLeader(bestPlayer);
				pillar.setCurrentLeaderScore(bestScore);
				spawnPillarStatue(pillar);
			}
		}
	}

	/**
	 * Spawn statue for pillar leader
	 * Uses same logic as successful spawnTestStatue()
	 */
	public void spawnPillarStatue(MuseumPillar pillar)
	{
		// Remove existing statue
		if (pillar.getSpawnedStatue() != null)
		{
			pillar.getSpawnedStatue().deleteMe();
		}

		// Spawn new statue for current leader
		if (pillar.getCurrentLeader() != null)
		{
			// Check if NPC template exists (same as AdminMuseumTest)
			var template = NpcData.getInstance().getTemplate(30001); // Lector
			if (template == null)
			{
				template = NpcData.getInstance().getTemplate(31); // Fallback to Guard
				if (template == null)
				{
					_log.warning("Error: No valid NPC template found for pillar statue!");
					return;
				}
			}

			final MuseumStatueInstance statue = new MuseumStatueInstance(
				template,
				pillar.getCurrentLeader().getObjectId(),
				pillar.getName() // Use pillar name as title
			);

			Location loc = pillar.getSpawnLocation();
			statue.setXYZ(loc);
			statue.setHeading(loc.getHeading());

			// Make sure statue is visible and targetable (same as test)
			statue.setTargetable(true);

			statue.spawnMe();

			// Force broadcast to all nearby players (KEY FIX!)
			statue.broadcastInfo();

			pillar.setSpawnedStatue(statue);

			_log.info("Pillar statue spawned for " + pillar.getName() + " representing player: " + pillar.getCurrentLeader().getName());
		}
	}

	/**
	 * Update server max values for normalization
	 */
	public void updateServerMaxValues()
	{
		try (final Connection con = DatabaseFactory.getConnection())
		{
			// First, get max values for all categories in database (for pillar system)
			try (final PreparedStatement statement = con.prepareStatement(
				"SELECT category, MAX(total_count) as max_value FROM museum_statistics GROUP BY category"))
			{
				_serverMaxValues.clear();
				try (final ResultSet rset = statement.executeQuery())
				{
					while (rset.next())
					{
						String category = rset.getString("category");
						int maxValue = rset.getInt("max_value");
						_serverMaxValues.put(category, Math.max(1, maxValue));

						if (Config.DEBUG)
						{
							_log.info("Server max value for " + category + ": " + maxValue);
						}
					}
				}
			}

			// Then, ensure XML categories are also included
			for (MuseumCategory category : _categories.values())
			{
				if (!_serverMaxValues.containsKey(category.getType()))
				{
					try (final PreparedStatement statement = con.prepareStatement(
						"SELECT MAX(total_count) as max_value FROM museum_statistics WHERE category = ?"))
					{
						statement.setString(1, category.getType());
						try (final ResultSet rset = statement.executeQuery())
						{
							if (rset.next())
							{
								int maxValue = rset.getInt("max_value");
								_serverMaxValues.put(category.getType(), Math.max(1, maxValue));
							}
						}
					}
				}
			}

			_log.info("Updated server max values: " + _serverMaxValues.size() + " categories");
		}
		catch (Exception e)
		{
			_log.warning("Could not update server max values: " + e.getMessage());
			e.printStackTrace();
		}
	}

	/**
	 * Add museum data for a player - main integration method
	 * Call this from gameplay events to collect statistics
	 */
	public void addMuseumData(PlayerInstance player, String category, long value)
	{
		if (player == null || category == null || value <= 0)
		{
			return;
		}

		// Initialize museum player if not exists
		if (player.getMuseumPlayer() == null)
		{
			restoreDataForChar(player);
		}

		// Add data to player's museum statistics
		if (player.getMuseumPlayer() != null)
		{
			player.getMuseumPlayer().addData(category, value);

			// Update database immediately for important events
			updateDataForChar(player);


		}
	}

	/**
	 * Convenience methods for common museum data types
	 */
	public void addFactionPvpKill(PlayerInstance player)
	{
		addMuseumData(player, "faction_pvp_kills", 1);
	}

	public void addFactionWarPoints(PlayerInstance player, int points)
	{
		addMuseumData(player, "faction_war_points", points);
	}

	public void addPvpVictory(PlayerInstance player)
	{
		addMuseumData(player, "pvp_victories", 1);
	}

	public void addPvpDefeat(PlayerInstance player)
	{
		addMuseumData(player, "pvp_defeats", 1);
	}

	public void addGveSkillsLearned(PlayerInstance player)
	{
		addMuseumData(player, "gve_skills_learned", 1);
	}

	public void addDailyGvePoints(PlayerInstance player, int points)
	{
		addMuseumData(player, "daily_gve_points", points);
	}

	public void addComboKillsGve(PlayerInstance player, int combo)
	{
		addMuseumData(player, "combo_kills_gve", combo);
	}

	public void updateLongestKillStreak(PlayerInstance player, int streak)
	{
		// Only update if this streak is longer than current record
		if (player.getMuseumPlayer() != null)
		{
			long currentBest = player.getMuseumPlayer().getData(RefreshTime.Total, "longest_kill_streak");
			if (streak > currentBest)
			{
				// Set the new record (not add)
				player.getMuseumPlayer().getData().put("longest_kill_streak", new long[]{streak, streak, streak, streak});
				updateDataForChar(player);
			}
		}
	}

	public void addChaoticRaidsTotal(PlayerInstance player)
	{
		addMuseumData(player, "chaotic_raids_total", 1);
	}

	public void addEpicRaidsTotal(PlayerInstance player)
	{
		addMuseumData(player, "epic_raids_total", 1);
	}

	public void addLeadershipActivity(PlayerInstance player)
	{
		addMuseumData(player, "leadership_activities", 1);
	}

	/**
	 * Calculate pillar score directly from database
	 */
	public double calculatePillarScoreFromDatabase(int objectId, String playerName, String pillarName)
	{
		MuseumPillar pillar = _pillars.get(pillarName);
		if (pillar == null)
		{
			return 0.0;
		}

		double totalScore = 0.0;

		try (Connection con = DatabaseFactory.getConnection())
		{
			for (String achievementType : pillar.getIncludedTypes())
			{
				if (achievementType.equals("all_combined"))
				{
					// Server Legend - sum of all other pillars
					for (String otherPillarName : _pillars.keySet())
					{
						if (!otherPillarName.equals(pillarName))
						{
							totalScore += calculatePillarScoreFromDatabase(objectId, playerName, otherPillarName);
						}
					}
				}
				else
				{
					// Get player value from database
					long playerValue = 0;
					try (PreparedStatement statement = con.prepareStatement(
						"SELECT total_count FROM museum_statistics WHERE objectId = ? AND category = ?"))
					{
						statement.setInt(1, objectId);
						statement.setString(2, achievementType);
						try (ResultSet rset = statement.executeQuery())
						{
							if (rset.next())
							{
								playerValue = rset.getLong("total_count");
							}
						}
					}

					// Calculate normalized score
					int maxValue = _serverMaxValues.getOrDefault(achievementType, 1);
					double normalizedScore = ((double) playerValue / maxValue) * 100.0;

					// Special handling for PvP defeats (reduce score)
					if (achievementType.equals("pvp_defeats"))
					{
						totalScore -= normalizedScore * 0.5;
					}
					else
					{
						totalScore += normalizedScore;
					}
				}
			}
		}
		catch (Exception e)
		{
			_log.warning("Error calculating pillar score for " + playerName + ": " + e.getMessage());
		}

		return Math.max(0.0, totalScore);
	}

	/**
	 * Get all pillars
	 */
	public HashMap<String, MuseumPillar> getAllPillars()
	{
		return _pillars;
	}

	public static MuseumManager getInstance()
	{
		return SingletonHolder._instance;
	}
	public class UpdateStats implements Runnable
	{
		RefreshTime _time;

		public UpdateStats(final RefreshTime time)
		{
			_time = time;
		}

		@Override
		public void run()
		{
			long time = 0L;
			switch (_time)
			{
				case Monthly:
				{
					final Calendar c = Calendar.getInstance();
					c.set(2, c.get(2) + 1);
					c.set(5, 1);
					c.set(11, 0);
					c.set(12, 0);
					c.set(13, 0);
					time = Math.max(100L, c.getTimeInMillis() - System.currentTimeMillis());
					GlobalVariables.getInstance().set("museum_monthly", c.getTimeInMillis());
					ThreadPool.schedule(new UpdateStats(RefreshTime.Monthly), time);
					cleanLastTops(_time);
					break;
				}
				case Weekly:
				{
					final Calendar c = Calendar.getInstance();
					c.set(7, 2);
					c.set(11, 0);
					c.set(12, 0);
					c.set(13, 0);
					if (c.getTimeInMillis() < System.currentTimeMillis())
					{
						c.setTimeInMillis(c.getTimeInMillis() + 604800000L);
					}
					time = Math.max(100L, c.getTimeInMillis() - System.currentTimeMillis());
					GlobalVariables.getInstance().set("museum_weekly", c.getTimeInMillis());
					ThreadPool.schedule(new UpdateStats(_time), time);
					cleanLastTops(_time);
					break;
				}
				case Daily:
				{
					final Calendar c = Calendar.getInstance();
					c.set(6, c.get(6) + 1);
					c.set(11, 0);
					c.set(12, 0);
					c.set(13, 0);
					time = Math.max(100L, c.getTimeInMillis() - System.currentTimeMillis());
					GlobalVariables.getInstance().set("museum_daily", c.getTimeInMillis());
					ThreadPool.schedule(new UpdateStats(_time), time);
					cleanLastTops(_time);
					break;
				}
			}
			refreshTops();
			restoreLastTops(RefreshTime.Total);
			updatePillarLeaders(); // Update pillar system after refresh
		}

		public void refreshTops()
		{
			for (final PlayerInstance player : World.getInstance().getPlayers())
			{
				if (player.getMuseumPlayer() != null)
				{
					player.getMuseumPlayer().resetData(_time);
				}
			}
			refreshTopsFromDatabase(_time);
		}
	}

	private static class SingletonHolder
	{
		protected static final MuseumManager _instance;

		static
		{
			_instance = new MuseumManager();
		}
	}
}
