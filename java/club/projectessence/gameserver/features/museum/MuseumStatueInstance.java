package club.projectessence.gameserver.features.museum;

import club.projectessence.commons.database.DatabaseFactory;
import club.projectessence.gameserver.communitybbs.Manager.MuseumBBSManager;
import club.projectessence.gameserver.enums.InstanceType;
import club.projectessence.gameserver.model.CharSelectInfoPackage;
import club.projectessence.gameserver.model.actor.Npc;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.model.actor.templates.NpcTemplate;
import club.projectessence.gameserver.network.serverpackets.*;
import club.projectessence.gameserver.features.museum.MuseumStatueCharInfo;
import club.projectessence.gameserver.features.museum.MuseumDecoyInstance;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;


public class MuseumStatueInstance extends Npc {
    public MuseumStatueInstance(NpcTemplate template) {
        super(template);
    }


    int _type;
    int _playerObjectId;
    CharSelectInfoPackage _charLooks;
    MuseumCategory _category;

    public MuseumStatueInstance(final NpcTemplate template, final int playerObjectId, final int type) {
        super(template);
        setInstanceType(InstanceType.MuseumStatueInstance);
        _playerObjectId = playerObjectId;
        _type = type;
        restoreCharLooks();
        _category = MuseumManager.getInstance().getAllCategories().get(type);

        // Set title based on category or default for pillars
        if (_category != null) {
            setTitle(_category.getTypeName());
        } else {
            setTitle("Museum Champion"); // Default title for pillar statues
        }
    }

    /**
     * Constructor for pillar statues with custom title
     */
    public MuseumStatueInstance(final NpcTemplate template, final int playerObjectId, final String pillarTitle) {
        super(template);
        setInstanceType(InstanceType.MuseumStatueInstance);
        _playerObjectId = playerObjectId;
        _type = -1; // Special type for pillars
        restoreCharLooks();
        _category = null; // No category for pillars
        setTitle(pillarTitle);
    }

    public void restoreCharLooks() {
        try (final Connection con = DatabaseFactory.getConnection();
             final PreparedStatement statement = con.prepareStatement("SELECT * FROM characters WHERE charId=?")) {
            statement.setInt(1, _playerObjectId);
            try (final ResultSet rset = statement.executeQuery()) {
                if (rset.next()) {
                    _charLooks = CharSelectionInfo.restoreChar(rset, 0);
                    if (_charLooks == null) {
    
                    }
                }
            }
        } catch (Exception e) {
            // Silently handle char info restoration errors
        }
    }

    public CharSelectInfoPackage getCharLooks() {
        return _charLooks;
    }

    @Override
    public void sendInfo(PlayerInstance player)
    {
        if (isVisibleFor(player))
        {
            try {
                // Try simple CharInfo approach
                if (_charLooks != null) {
                    // Use custom CharInfo constructor for museum statues
                    player.sendPacket(new CharInfo(this, false));


                    return;
                }
            } catch (Exception e) {
                // Silently handle CharInfo errors and fallback to NPC info
            }

            // Fallback to normal NPC info
            super.sendInfo(player);

        }
    }

    @Override
    public void onBypassFeedback(final PlayerInstance player, final String command) {
        player.sendPacket(ActionFailed.STATIC_PACKET);
        super.onBypassFeedback(player, command);
    }

    @Override
    public void showChatWindow(final PlayerInstance player) {
        String html = "<html><body scroll=no>";

        // Check if this is a pillar statue (no category)
        if (_category == null) {
            html += showPillarStatue();
        } else {
            html += showStatue();
        }

        html += "</body></html>";
        separateAndSend(html, player);
    }

    public String showStatue() {
        String html = "";
        html += "<br><br><br><center><table><tr><td width=25></td><td><table border=1 bgcolor=3b3c34><tr><td>";
        html = html + "<br><center><font name=\"ScreenMessageLarge\" color=b7b8b2>" + _category.getTypeName() + "</font></center>";
        html += "<table><tr>";
        if (!_category.getRefreshTime().equals(RefreshTime.Total)) {
            html += "<td align=center width=260>";
            html = html + "<button value=\"" + _category.getRefreshTime().name() + " Rankings\" action=\"\" fore=\"L2UI_CT1.Button_DF_Calculator\" back=\"L2UI_CT1.Button_DF_Calculator_Down\" width=\"257\" height=\"24\"/>";
            html += "</td>";
        }
        html = html + "<td align=center width=" + (_category.getRefreshTime().equals(RefreshTime.Total) ? 520 : 260) + ">";
        html += "<button value=\"Total Rankings\" action=\"\" fore=\"L2UI_CT1.Button_DF_Calculator\" back=\"L2UI_CT1.Button_DF_Calculator_Down\" width=\"257\" height=\"24\"/>";
        html += "</td>";
        html += "</tr><tr>";
        if (!_category.getRefreshTime().equals(RefreshTime.Total)) {
            html += "<td align=center width=260>";
            for (int i = 0; i < 5; ++i) {
                String name = "No information.";
                String value = "No information.";
                int cellSpacing = -1;
                if (_category.getAllStatuePlayers().size() > i) {
                    final TopPlayer player = _category.getAllStatuePlayers().get(i + 1);
                    if (player != null) {
                        name = player.getName();
                        final long count = player.getCount();
                        value = MuseumBBSManager.getInstance().convertToValue(count, _category.isTimer(), _category.getAdditionalText());
                        cellSpacing = ((count > 999L) ? -3 : -2);
                    }
                }
                final String bgColor = (i == 0) ? "746833" : (((i % 2) == 1) ? "171612" : "23221e");
                final String numberColor = (i == 0) ? "ffca37" : "dededf";
                final String nameColor = (i == 0) ? "eac842" : "e2e2e0";
                final String valueColor = (i == 0) ? "eee79f" : "a78d6c";
                html = html + "<table width=250 bgcolor=" + bgColor + " height=42><tr>";
                html = html + "<td width=50 align=center><font color=" + numberColor + " name=ScreenMessageLarge />" + ((i < 1) ? ("{" + (i + 1) + "}") : (i + 1)) + "</font></td>";
                html += "<td width=200 align=left>";
                html = html + "<table cellspacing=" + cellSpacing + "><tr><td width=200><font color=" + nameColor + " name=ScreenMessageSmall>" + name + "</font></td></tr><tr><td width=200><font color=" + valueColor + " name=ScreenMessageSmall>" + value + "</font></td></tr></table>";
                html += "<img src=\"L2UI.SquareBlank\" width=1 height=5/></td>";
                html += "";
                html += "</tr></table><img src=\"L2UI.SquareGray\" width=250 height=1/>";
            }
            html += "</td>";
        }
        html = html + "<td align=center width=" + (_category.getRefreshTime().equals(RefreshTime.Total) ? 520 : 260) + ">";
        for (int i = 0; i < 5; ++i) {
            String name = "No information.";
            String value = "No information.";
            int cellSpacing = -1;
            if (_category.getAllTotalTops().size() > i) {
                final TopPlayer player = _category.getAllTotalTops().get(i + 1);
                if (player != null) {
                    name = player.getName();
                    final long count = player.getCount();
                    value = MuseumBBSManager.getInstance().convertToValue(count, _category.isTimer(), _category.getAdditionalText());
                    cellSpacing = ((count > 999L) ? -3 : -2);
                }
            }
            final String bgColor = (i == 0) ? "746833" : (((i % 2) == 1) ? "171612" : "23221e");
            final String numberColor = (i == 0) ? "ffca37" : "dededf";
            final String nameColor = (i == 0) ? "eac842" : "e2e2e0";
            final String valueColor = (i == 0) ? "eee79f" : "a78d6c";
            html = html + "<table width=250 bgcolor=" + bgColor + " height=42><tr>";
            html = html + "<td width=50 align=center><font color=" + numberColor + " name=ScreenMessageLarge />" + ((i < 1) ? ("{" + (i + 1) + "}") : (i + 1)) + "</font></td>";
            html += "<td width=200 align=left>";
            html = html + "<table cellspacing=" + cellSpacing + "><tr><td width=200><font color=" + nameColor + " name=ScreenMessageSmall>" + name + "</font></td></tr><tr><td width=200><font color=" + valueColor + " name=ScreenMessageSmall>" + value + "</font></td></tr></table>";
            html += "<img src=\"L2UI.SquareBlank\" width=1 height=5/></td>";
            html += "";
            html += "</tr></table><img src=\"L2UI.SquareGray\" width=250 height=1/>";
        }
        html += "</td>";
        html += "</tr></table><br><br></td></tr></table></td></tr></table>";
        html += "</center>";
        return html;
    }

    protected void separateAndSend(final String html, final PlayerInstance acha) {
        if (html == null) {
            return;
        }
        if (html.length() < 4096) {
            acha.sendPacket(new ShowBoard(html, "101"));
            acha.sendPacket(new ShowBoard((String) null, "102"));
            acha.sendPacket(new ShowBoard((String) null, "103"));
        } else if (html.length() < 8192) {
            acha.sendPacket(new ShowBoard(html.substring(0, 4096), "101"));
            acha.sendPacket(new ShowBoard(html.substring(4096), "102"));
            acha.sendPacket(new ShowBoard((String) null, "103"));
        } else if (html.length() < 16384) {
            acha.sendPacket(new ShowBoard(html.substring(0, 4096), "101"));
            acha.sendPacket(new ShowBoard(html.substring(4096, 8192), "102"));
            acha.sendPacket(new ShowBoard(html.substring(8192), "103"));
        }
    }

    /**
     * Show pillar statue interface (for statues without category)
     */
    public String showPillarStatue() {
        String html = "";
        html += "<br><br><br><center><table><tr><td width=25></td><td><table border=1 bgcolor=3b3c34><tr><td>";
        html += "<br><center><font name=\"ScreenMessageLarge\" color=FFCC00>" + getTitle() + "</font></center>";
        html += "<br><center><font color=LEVEL>Museum Champion</font></center>";

        // Get player name and detailed statistics from database
        String playerName = "Unknown";
        StringBuilder statsHtml = new StringBuilder();

        try (final Connection con = DatabaseFactory.getConnection()) {
            // Get player name
            try (final PreparedStatement statement = con.prepareStatement("SELECT char_name FROM characters WHERE charId=?")) {
                statement.setInt(1, _playerObjectId);
                try (final ResultSet rset = statement.executeQuery()) {
                    if (rset.next()) {
                        playerName = rset.getString("char_name");
                    }
                }
            }

            // Get pillar-specific statistics
            MuseumPillar pillar = MuseumManager.getInstance().getAllPillars().get(getTitle());
            if (pillar != null) {
                statsHtml.append("<br><center><font color=LEVEL>--- Achievement Statistics ---</font></center>");

                for (String achievementType : pillar.getIncludedTypes()) {
                    if (!achievementType.equals("all_combined")) {
                        try (final PreparedStatement statStatement = con.prepareStatement(
                            "SELECT total_count, weekly_count, daily_count FROM museum_statistics WHERE objectId=? AND category=?")) {
                            statStatement.setInt(1, _playerObjectId);
                            statStatement.setString(2, achievementType);
                            try (final ResultSet statRset = statStatement.executeQuery()) {
                                if (statRset.next()) {
                                    long totalCount = statRset.getLong("total_count");
                                    long weeklyCount = statRset.getLong("weekly_count");
                                    long dailyCount = statRset.getLong("daily_count");

                                    String displayName = formatAchievementName(achievementType);
                                    statsHtml.append("<br><font color=FFFF00>").append(displayName).append(":</font>");
                                    statsHtml.append("<br><font color=00FF00>Total: ").append(totalCount).append("</font>");
                                    if (weeklyCount > 0) {
                                        statsHtml.append(" <font color=FFCC00>Weekly: ").append(weeklyCount).append("</font>");
                                    }
                                    if (dailyCount > 0) {
                                        statsHtml.append(" <font color=FF6600>Daily: ").append(dailyCount).append("</font>");
                                    }
                                }
                            }
                        }
                    }
                }

                // Show pillar score
                double pillarScore = MuseumManager.getInstance().calculatePillarScoreFromDatabase(_playerObjectId, playerName, getTitle());
                statsHtml.append("<br><br><center><font color=FFCC00>Pillar Score: ").append(String.format("%.2f", pillarScore)).append("</font></center>");
            }

        } catch (Exception e) {
            statsHtml.append("<br><center><font color=FF0000>Error loading statistics</font></center>");
        }

        html += "<br><center><font color=00FF00>Champion: " + playerName + "</font></center>";
        html += statsHtml.toString();
        html += "<br><center><font color=FFFF00>Congratulations on achieving excellence!</font></center>";

        html += "</td></tr></table></td><td width=25></td></tr></table></center>";
        return html;
    }

    /**
     * Format achievement type name for display
     */
    private String formatAchievementName(String achievementType) {
        switch (achievementType) {
            case "faction_pvp_kills": return "Faction PvP Kills";
            case "faction_war_points": return "Faction War Points";
            case "leadership_activities": return "Leadership Activities";
            case "gve_skills_learned": return "GVE Skills Learned";
            case "daily_gve_points": return "Daily GVE Points";
            case "combo_kills_gve": return "Combo Kills";
            case "longest_kill_streak": return "Longest Kill Streak";
            case "pvp_victories": return "PvP Victories";
            case "pvp_defeats": return "PvP Defeats";
            case "chaotic_raids_total": return "Chaotic Raids";
            case "epic_raids_total": return "Epic Raids";
            default: return achievementType.replace("_", " ");
        }
    }
}
