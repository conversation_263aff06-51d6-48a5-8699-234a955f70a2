/*
 * This file is part of the L2J Mobius project.
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver;

import java.awt.GraphicsEnvironment;
import java.awt.Toolkit;
import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.lang.management.ManagementFactory;
import java.net.InetSocketAddress;
import java.time.Duration;
import java.util.Calendar;
import java.util.logging.Level;
import java.util.logging.LogManager;
import java.util.logging.Logger;

import club.projectessence.Config;
import club.projectessence.commons.concurrent.ThreadPool;
import club.projectessence.commons.database.DatabaseFactory;
import club.projectessence.commons.enums.ServerMode;
import club.projectessence.commons.util.DeadLockDetector;
import club.projectessence.gameserver.cache.HtmCache;
import club.projectessence.gameserver.communitybbs.SunriseBoards.dropCalc.DropInfoHandler;
import club.projectessence.gameserver.communitybbs.SunriseBoards.dropCalc.ImagesCache;
import club.projectessence.gameserver.data.EventDroplist;
import club.projectessence.gameserver.data.ItemTable;
import club.projectessence.gameserver.data.SchemeBufferTable;
import club.projectessence.gameserver.data.sql.AnnouncementsTable;
import club.projectessence.gameserver.data.sql.CharNameTable;
import club.projectessence.gameserver.data.sql.CharSummonTable;
import club.projectessence.gameserver.data.sql.ClanTable;
import club.projectessence.gameserver.data.sql.CrestTable;
import club.projectessence.gameserver.data.sql.OfflineTraderTable;
import club.projectessence.gameserver.data.xml.ActionData;
import club.projectessence.gameserver.data.xml.AdminData;
import club.projectessence.gameserver.data.xml.AppearanceItemData;
import club.projectessence.gameserver.data.xml.ArmorSetData;
import club.projectessence.gameserver.data.xml.ArtifactData;
import club.projectessence.gameserver.data.xml.AttendanceRewardData;
import club.projectessence.gameserver.data.xml.BalanceItemData;
import club.projectessence.gameserver.data.xml.BalthusEventData;
import club.projectessence.gameserver.data.xml.BeautyShopData;
import club.projectessence.gameserver.data.xml.BlessItemData;
import club.projectessence.gameserver.data.xml.BuyListData;
import club.projectessence.gameserver.data.xml.CategoryData;
import club.projectessence.gameserver.data.xml.ClanExperienceData;
import club.projectessence.gameserver.data.xml.ClanHallData;
import club.projectessence.gameserver.data.xml.ClassListData;
import club.projectessence.gameserver.data.xml.ClientWeaponTypesData;
import club.projectessence.gameserver.data.xml.CollectionData;
import club.projectessence.gameserver.data.xml.CombinationItemsData;
import club.projectessence.gameserver.data.xml.CubicData;
import club.projectessence.gameserver.data.xml.DailyMissionData;
import club.projectessence.gameserver.data.xml.DoorData;
import club.projectessence.gameserver.data.xml.DyePotentialData;
import club.projectessence.gameserver.data.xml.ElementalSpiritData;
import club.projectessence.gameserver.data.xml.EnchantChallengePointsData;
import club.projectessence.gameserver.data.xml.EnchantItemData;
import club.projectessence.gameserver.data.xml.EnchantItemGroupsData;
import club.projectessence.gameserver.data.xml.EnchantItemHPBonusData;
import club.projectessence.gameserver.data.xml.EnchantItemOptionsData;
import club.projectessence.gameserver.data.xml.EnchantSkillGroupsData;
import club.projectessence.gameserver.data.xml.EnsoulData;
import club.projectessence.gameserver.data.xml.EquipmentUpgradeNormalData;
import club.projectessence.gameserver.data.xml.EventEngineData;
import club.projectessence.gameserver.data.xml.ExperienceData;
import club.projectessence.gameserver.data.xml.FaceHairItemData;
import club.projectessence.gameserver.data.xml.FactionLeaderCommandData;
import club.projectessence.gameserver.data.xml.FactionLeaderSkillData;
import club.projectessence.gameserver.data.xml.FactionWarSkillData;
import club.projectessence.gameserver.data.xml.FenceData;
import club.projectessence.gameserver.data.xml.FishingData;
import club.projectessence.gameserver.data.xml.GveRewardData;
import club.projectessence.gameserver.data.xml.HennaData;
import club.projectessence.gameserver.data.xml.HitConditionBonusData;
import club.projectessence.gameserver.data.xml.InitialEquipmentData;
import club.projectessence.gameserver.data.xml.InitialShortcutData;
import club.projectessence.gameserver.data.xml.ItemCrystallizationData;
import club.projectessence.gameserver.data.xml.ItemDeletionData;
import club.projectessence.gameserver.data.xml.KarmaData;
import club.projectessence.gameserver.data.xml.L2PassData;
import club.projectessence.gameserver.data.xml.LCoinShopData;
import club.projectessence.gameserver.data.xml.LetterCollectorData;
import club.projectessence.gameserver.data.xml.LuckyGameData;
import club.projectessence.gameserver.data.xml.MableGameEventData;
import club.projectessence.gameserver.data.xml.MissionLevelData;
import club.projectessence.gameserver.data.xml.MultisellData;
import club.projectessence.gameserver.data.xml.NicknameItemData;
import club.projectessence.gameserver.data.xml.NpcData;
import club.projectessence.gameserver.data.xml.OptionData;
import club.projectessence.gameserver.data.xml.PetDataTable;
import club.projectessence.gameserver.data.xml.PetExtractData;
import club.projectessence.gameserver.data.xml.PetSkillData;
import club.projectessence.gameserver.data.xml.PlayerTemplateData;
import club.projectessence.gameserver.data.xml.PlayerXpPercentLostData;
import club.projectessence.gameserver.data.xml.PremiumData;
import club.projectessence.gameserver.data.xml.PrimeShopData;
import club.projectessence.gameserver.data.xml.PurgeData;
import club.projectessence.gameserver.data.xml.RaidDropAnnounceData;
import club.projectessence.gameserver.data.xml.RaidSpawnData;
import club.projectessence.gameserver.data.xml.RaidTeleportData;
import club.projectessence.gameserver.data.xml.RandomCraftData;
import club.projectessence.gameserver.data.xml.RandomCraftExtractionData;
import club.projectessence.gameserver.data.xml.RecipeData;
import club.projectessence.gameserver.data.xml.ResidenceFunctionsData;
import club.projectessence.gameserver.data.xml.ResurrectionFeesData;
import club.projectessence.gameserver.data.xml.SayuneData;
import club.projectessence.gameserver.data.xml.SecondaryAuthData;
import club.projectessence.gameserver.data.xml.ShuttleData;
import club.projectessence.gameserver.data.xml.SiegeScheduleData;
import club.projectessence.gameserver.data.xml.SkillData;
import club.projectessence.gameserver.data.xml.SkillTreeData;
import club.projectessence.gameserver.data.xml.SpawnData;
import club.projectessence.gameserver.data.xml.SpecialHuntingZoneData;
import club.projectessence.gameserver.data.xml.StaticObjectData;
import club.projectessence.gameserver.data.xml.TeleportListData;
import club.projectessence.gameserver.data.xml.TeleporterData;
import club.projectessence.gameserver.data.xml.TranscendentInstanceData;
import club.projectessence.gameserver.data.xml.TransformData;
import club.projectessence.gameserver.data.xml.VariationData;
import club.projectessence.gameserver.data.xml.VipSystemData;
import club.projectessence.gameserver.discord.DiscordBotManager;
import club.projectessence.gameserver.discord.webhook.DiscordWebhookManager;
import club.projectessence.gameserver.fakeplayers.FakePlayerManager;
import club.projectessence.gameserver.fakeplayers.data.FakePlayerHennaData;
import club.projectessence.gameserver.fakeplayers.data.FakePlayerItemData;
import club.projectessence.gameserver.fakeplayers.data.FakePlayerNameData;
import club.projectessence.gameserver.fakeplayers.data.FakePlayerShopNameData;
import club.projectessence.gameserver.fakeplayers.data.FakePlayerSkillData;
import club.projectessence.gameserver.fakeplayers.data.FakePlayerTownData;
import club.projectessence.gameserver.fakeplayers.data.FakePlayerZoneData;
import club.projectessence.gameserver.features.museum.MuseumManager;
import club.projectessence.gameserver.geoengine.GeoEngine;
import club.projectessence.gameserver.handler.ConditionHandler;
import club.projectessence.gameserver.handler.DailyMissionHandler;
import club.projectessence.gameserver.handler.EffectHandler;
import club.projectessence.gameserver.handler.SkillConditionHandler;
import club.projectessence.gameserver.instancemanager.AirShipManager;
import club.projectessence.gameserver.instancemanager.AntiFeedManager;
import club.projectessence.gameserver.instancemanager.ArtifactManager;
import club.projectessence.gameserver.instancemanager.BoatManager;
import club.projectessence.gameserver.instancemanager.CastleManager;
import club.projectessence.gameserver.instancemanager.CastleManorManager;
import club.projectessence.gameserver.instancemanager.ClanEntryManager;
import club.projectessence.gameserver.instancemanager.ClanHallAuctionManager;
import club.projectessence.gameserver.instancemanager.CommissionManager;
import club.projectessence.gameserver.instancemanager.CursedWeaponsManager;
import club.projectessence.gameserver.instancemanager.CustomMailManager;
import club.projectessence.gameserver.instancemanager.DBSpawnManager;
import club.projectessence.gameserver.instancemanager.FactionLeaderManager;
import club.projectessence.gameserver.instancemanager.FactionWarManager;
import club.projectessence.gameserver.instancemanager.FactionZoneManager;
import club.projectessence.gameserver.instancemanager.FortManager;
import club.projectessence.gameserver.instancemanager.FortSiegeManager;
import club.projectessence.gameserver.instancemanager.GlobalVariables;
import club.projectessence.gameserver.instancemanager.GrandBossManager;
import club.projectessence.gameserver.instancemanager.IdManager;
import club.projectessence.gameserver.instancemanager.InstanceManager;
import club.projectessence.gameserver.instancemanager.ItemAuctionManager;
import club.projectessence.gameserver.instancemanager.ItemsOnGroundManager;
import club.projectessence.gameserver.instancemanager.MailManager;
import club.projectessence.gameserver.instancemanager.MapRegionManager;
import club.projectessence.gameserver.instancemanager.MatchingRoomManager;
import club.projectessence.gameserver.instancemanager.MentorManager;
import club.projectessence.gameserver.instancemanager.OutpostPortalManager;
import club.projectessence.gameserver.instancemanager.PcCafePointsManager;
import club.projectessence.gameserver.instancemanager.PetitionManager;
import club.projectessence.gameserver.instancemanager.PrecautionaryRestartManager;
import club.projectessence.gameserver.instancemanager.PremiumManager;
import club.projectessence.gameserver.instancemanager.PrivateStoreHistoryManager;
import club.projectessence.gameserver.instancemanager.PunishmentManager;
import club.projectessence.gameserver.instancemanager.QuestManager;
import club.projectessence.gameserver.instancemanager.QueueManager;
import club.projectessence.gameserver.instancemanager.RaidSpawnManager;
import club.projectessence.gameserver.instancemanager.RandomCraftPremiumManager;
import club.projectessence.gameserver.instancemanager.RankManager;
import club.projectessence.gameserver.instancemanager.SellBuffsManager;
import club.projectessence.gameserver.instancemanager.ServerRestartManager;
import club.projectessence.gameserver.instancemanager.SiegeGuardManager;
import club.projectessence.gameserver.instancemanager.SiegeManager;
import club.projectessence.gameserver.instancemanager.UniqueOnlineManager;
import club.projectessence.gameserver.instancemanager.VengeanceManager;
import club.projectessence.gameserver.instancemanager.VipSystemManager;
import club.projectessence.gameserver.instancemanager.WalkingManager;
import club.projectessence.gameserver.instancemanager.WorldExchangeManager;
import club.projectessence.gameserver.instancemanager.ZoneManager;
import club.projectessence.gameserver.instancemanager.events.EventFixedDropManager;
import club.projectessence.gameserver.instancemanager.events.GoldFestival.GoldFestivalEvent;
import club.projectessence.gameserver.instancemanager.games.MonsterRace;
import club.projectessence.gameserver.model.World;
import club.projectessence.gameserver.model.events.EventDispatcher;
import club.projectessence.gameserver.model.olympiad.Hero;
import club.projectessence.gameserver.model.olympiad.Olympiad;
import club.projectessence.gameserver.model.votereward.VoteSystem;
import club.projectessence.gameserver.network.ClientPacketHandler;
import club.projectessence.gameserver.network.GameClient;
import club.projectessence.gameserver.network.loginserver.LoginServerNetworkManager;
import club.projectessence.gameserver.scripting.ScriptEngineManager;
import club.projectessence.gameserver.taskmanager.AdditionalSaveTaskManager;
import club.projectessence.gameserver.taskmanager.DeathDisconnectionManager;
import club.projectessence.gameserver.taskmanager.DonateManager;
import club.projectessence.gameserver.taskmanager.PromoCodeManager;
import club.projectessence.gameserver.taskmanager.SteadyBoxTaskManager;
import club.projectessence.gameserver.taskmanager.TaskManager;
import club.projectessence.gameserver.telegram.TelegramBotApi;
import club.projectessence.gameserver.util.Broadcast;
import gabriel.eventEngine.interf.GabrielEvents;
import io.github.joealisson.mmocore.ConnectionBuilder;
import io.github.joealisson.mmocore.ConnectionHandler;

public class GameServer
{
	public static final Calendar				dateTimeServerStarted	= Calendar.getInstance();
	private static final Logger					LOGGER					= Logger.getLogger(GameServer.class.getName());
	private static GameServer					INSTANCE;
	private DeadLockDetector					_deadDetectThread;
	private ConnectionHandler<GameClient>		connectionHandler;
	
	public GameServer() throws Exception
	{
		final long serverLoadStart = System.currentTimeMillis();
		// GUI
		if (!GraphicsEnvironment.isHeadless())
		{
			// System.out.println("GameServer: Running in GUI mode.");
			// new Gui();
		}
		// Create log folder
		final File logFolder = new File(".", "log");
		logFolder.mkdir();
		// Create input stream for log file -- or store file data into memory
		try (InputStream is = new FileInputStream(new File("./log.cfg")))
		{
			LogManager.getLogManager().readConfiguration(is);
		}
		// Initialize config
		Config.load(ServerMode.GAME);
		printSection("Database");
		DatabaseFactory.init();
		printSection("ThreadPool");
		ThreadPool.get();
		printSection("IdManager");
		IdManager.getInstance();
		if (!IdManager.hasInitialized())
		{
			LOGGER.severe(getClass().getSimpleName() + ": Could not read object IDs from database. Please check your configuration.");
			throw new Exception("Could not initialize the ID factory!");
		}
		// load script engines
		printSection("Scripting Engine");
		EventDispatcher.getInstance();
		ScriptEngineManager.getInstance();
		printSection("World");
		// start game time control early
		GameTimeController.init();
		World.getInstance();
		MapRegionManager.getInstance();
		ZoneManager.getInstance();
		DoorData.getInstance();
		FenceData.getInstance();
		AnnouncementsTable.getInstance();
		GlobalVariables.getInstance();
		World.LOOT_BOXES_OPENED = GlobalVariables.getInstance().getInt("LOOT_BOXES_OPEN", 0);
		World.L_COINS_FARMED = GlobalVariables.getInstance().getLong("L_COINS_FARMED", 0);
		// Event Sibi Stats
		World.BALTHUS_KNIGHT_MARK_FARMED = GlobalVariables.getInstance().getLong("BALTHUS_KNIGHT_MARK_FARMED", 0);
		World.LIFE_CONTROL_TOWER_FARMED = GlobalVariables.getInstance().getLong("LIFE_CONTROL_TOWER_FARMED", 0);
		World.MID_GRADE_HP_POTION_FARMED = GlobalVariables.getInstance().getLong("MID_GRADE_HP_POTION_FARMED", 0);
		World.SCROLL_BOOST_ATTACK_FARMED = GlobalVariables.getInstance().getLong("SCROLL_BOOST_ATTACK_FARMED", 0);
		World.SCROLL_BOOST_DEFENSE_FARMED = GlobalVariables.getInstance().getLong("SCROLL_BOOST_DEFENSE_FARMED", 0);
		World.SAYHA_COOKIE_FARMED = GlobalVariables.getInstance().getLong("SAYHA_COOKIE_FARMED", 0);
		World.SAYHA_BLESSING_FARMED = GlobalVariables.getInstance().getLong("SAYHA_BLESSING_FARMED", 0);
		World.ADENA_TAKE = GlobalVariables.getInstance().getLong("ADENA_TAKE", 0);
		World.L_COINS_TAKE = GlobalVariables.getInstance().getLong("L_COINS_TAKE", 0);
		World.SIBI_COIN_TAKE = GlobalVariables.getInstance().getLong("SIBI_COIN_TAKE", 0);
		// Event Watermemon Stats
		// World.WATERMELON_EVENT_ADENA_TAKE = GlobalVariables.getInstance().getLong("WATERMELON_EVENT_ADENA_TAKE", 0);
		// World.WATERMELON_EVENT_L_COINS_TAKE = GlobalVariables.getInstance().getLong("WATERMELON_EVENT_L_COINS_TAKE", 0);
		printSection("Data");
		ActionData.getInstance();
		CategoryData.getInstance();
		SecondaryAuthData.getInstance();
		CombinationItemsData.getInstance();
		SayuneData.getInstance();
		DailyMissionHandler.getInstance().executeScript();
		DailyMissionData.getInstance();
		ElementalSpiritData.getInstance();
		TranscendentInstanceData.getInstance();
		FactionLeaderCommandData.getInstance();
		printSection("Skills");
		SkillConditionHandler.getInstance().executeScript();
		EffectHandler.getInstance().executeScript();
		EnchantSkillGroupsData.getInstance();
		SkillTreeData.getInstance();
		SkillData.getInstance();
		PetSkillData.getInstance();
		printSection("Items");
		ConditionHandler.getInstance().executeScript();
		ItemTable.getInstance();
		FaceHairItemData.getInstance();
		EnchantItemGroupsData.getInstance();
		EnchantItemData.getInstance();
		EnchantItemOptionsData.getInstance();
		EnchantChallengePointsData.getInstance();
		ItemCrystallizationData.getInstance();
		OptionData.getInstance();
		VariationData.getInstance();
		EnsoulData.getInstance();
		EnchantItemHPBonusData.getInstance();
		BuyListData.getInstance();
		MultisellData.getInstance();
		EquipmentUpgradeNormalData.getInstance();
		RecipeData.getInstance();
		ArmorSetData.getInstance();
		BalanceItemData.getInstance();
		GveRewardData.getInstance();
		FishingData.getInstance();
		HennaData.getInstance();
		DyePotentialData.getInstance();
		PrimeShopData.getInstance();
		LCoinShopData.getInstance();
		PcCafePointsManager.getInstance();
		AppearanceItemData.getInstance();
		CommissionManager.getInstance();
		if (Config.ENABLE_WORLD_EXCHANGE)
		{
			WorldExchangeManager.getInstance();
		}
		LuckyGameData.getInstance();
		AttendanceRewardData.getInstance();
		ClientWeaponTypesData.getInstance();
		BlessItemData.getInstance();
		CollectionData.getInstance();
		ItemDeletionData.getInstance();
		RaidDropAnnounceData.getInstance();
		NicknameItemData.getInstance();
		printSection("Characters");
		ClassListData.getInstance();
		InitialEquipmentData.getInstance();
		InitialShortcutData.getInstance();
		ExperienceData.getInstance();
		PlayerXpPercentLostData.getInstance();
		KarmaData.getInstance();
		HitConditionBonusData.getInstance();
		PlayerTemplateData.getInstance();
		CharNameTable.getInstance();
		AdminData.getInstance();
		PetDataTable.getInstance();
		PetExtractData.getInstance();
		CubicData.getInstance();
		CharSummonTable.getInstance().init();
		BeautyShopData.getInstance();
		MentorManager.getInstance();
		ResurrectionFeesData.getInstance();
		VipSystemManager.getInstance();
		if (Config.PREMIUM_SYSTEM_ENABLED)
		{
			LOGGER.info("PremiumManager: Premium system is enabled.");
			PremiumManager.getInstance();
			PremiumData.getInstance();
		}
		RandomCraftPremiumManager.getInstance();
		printSection("Clans");
		ClanTable.getInstance();
		ResidenceFunctionsData.getInstance();
		ClanHallData.getInstance();
		ClanHallAuctionManager.getInstance();
		ClanEntryManager.getInstance();
		ClanExperienceData.getInstance();
		printSection("Geodata");
		GeoEngine.getInstance();
		printSection("NPCs");
		NpcData.getInstance();
		SpecialHuntingZoneData.getInstance();
		SpawnData.getInstance();
		WalkingManager.getInstance();
		StaticObjectData.getInstance();
		ItemAuctionManager.getInstance();
		CastleManager.getInstance().loadInstances();
		SchemeBufferTable.getInstance();
		GrandBossManager.getInstance();
		RaidTeleportData.getInstance();
		RaidSpawnData.getInstance();
		RaidSpawnManager.getInstance();
		EventDroplist.getInstance();
		EventFixedDropManager.getInstance();
		printSection("Instance");
		InstanceManager.getInstance();
		printSection("Olympiad");
		Olympiad.getInstance();
		Hero.getInstance();
		// Call to load caches
		printSection("Cache");
		HtmCache.getInstance();
		CrestTable.getInstance();
		TeleportListData.getInstance();
		TeleporterData.getInstance();
		MatchingRoomManager.getInstance();
		PetitionManager.getInstance();
		CursedWeaponsManager.getInstance();
		TransformData.getInstance();
		RankManager.getInstance();
		PurgeData.getInstance();
		if (Config.SELLBUFF_ENABLED)
		{
			SellBuffsManager.getInstance();
		}
		printSection("Scripts");
		QuestManager.getInstance();
		BoatManager.getInstance();
		AirShipManager.getInstance();
		ShuttleData.getInstance();
		try
		{
			LOGGER.info(getClass().getSimpleName() + ": Loading server scripts:");

			// Check if JDK is available before attempting script compilation
			if (javax.tools.ToolProvider.getSystemJavaCompiler() == null) {
				LOGGER.severe("Java compiler not available! Make sure you are running with JDK, not JRE.");
				LOGGER.severe("Current Java version: " + System.getProperty("java.version"));
				LOGGER.severe("Java home: " + System.getProperty("java.home"));
				LOGGER.severe("Script compilation will be skipped!");
				return;
			}

			ScriptEngineManager.getInstance().executeScript(ScriptEngineManager.MASTER_HANDLER_FILE);
			ScriptEngineManager.getInstance().executeScriptList();
		}
		catch (Exception e)
		{
			LOGGER.log(Level.SEVERE, getClass().getSimpleName() + ": Failed to execute script list!", e);
			LOGGER.severe("This is usually caused by:");
			LOGGER.severe("1. Missing JDK (only JRE installed)");
			LOGGER.severe("2. Missing script files");
			LOGGER.severe("3. Incorrect file permissions");
			LOGGER.severe("4. Corrupted build");
			LOGGER.severe("Please run check_jdk_windows.bat to diagnose the issue.");
		}
		SpawnData.getInstance().init();
		DBSpawnManager.getInstance();
		printSection("Event Engine");
		EventEngineData.getInstance();
		LetterCollectorData.getInstance();
		GoldFestivalEvent.getInstance();
		MableGameEventData.getInstance();
		BalthusEventData.getInstance();
		VoteSystem.initialize();
		printSection("Siege");
		SiegeManager.getInstance().getSieges();
		CastleManager.getInstance().activateInstances();
		FortManager.getInstance().loadInstances();
		FortManager.getInstance().activateInstances();
		FortSiegeManager.getInstance();
		SiegeScheduleData.getInstance();
		CastleManorManager.getInstance();
		SiegeGuardManager.getInstance();
		QuestManager.getInstance().report();
		if (Config.ENABLE_DROP_CALCULATOR)
		{
			ImagesCache.getInstance();
			// ItemIconData.getInstance();
			DropInfoHandler.getInstance().load();
		}
		if (Config.SAVE_DROPPED_ITEM)
		{
			ItemsOnGroundManager.getInstance();
		}
		if ((Config.AUTODESTROY_ITEM_AFTER > 0) || (Config.HERB_AUTO_DESTROY_TIME > 0))
		{
			ItemsAutoDestroy.getInstance();
		}
		MonsterRace.getInstance();
		TaskManager.getInstance();
		AntiFeedManager.getInstance().registerEvent(AntiFeedManager.GAME_ID);
		if (Config.ALLOW_MAIL)
		{
			MailManager.getInstance();
		}
		if (Config.CUSTOM_MAIL_MANAGER_ENABLED)
		{
			CustomMailManager.getInstance();
		}
		printSection("Faction and Outpost Managers");
		FactionZoneManager.getInstance();
		FactionWarManager.getInstance();
		OutpostPortalManager.getInstance();
		FactionWarSkillData.getInstance();
		if (Config.FACTION_LEADER_ENABLED)
		{
			FactionLeaderSkillData.getInstance();
			FactionLeaderManager.getInstance();
		}
		printSection("Artifacts");
		ArtifactData.getInstance();
		ArtifactManager.getInstance();
		printSection("Museum");
		MuseumManager.getInstance();
		PunishmentManager.getInstance();
		Runtime.getRuntime().addShutdownHook(Shutdown.getInstance());
		// Event Engine systems section
		printSection("Event Engine");
		GabrielEvents.start();
		LOGGER.info("IdManager: Free ObjectID's remaining: " + IdManager.size());
		if ((Config.OFFLINE_TRADE_ENABLE || Config.OFFLINE_CRAFT_ENABLE) && Config.RESTORE_OFFLINERS)
		{
			OfflineTraderTable.getInstance().restoreOfflineTraders();
		}
		if (Config.SERVER_RESTART_SCHEDULE_ENABLED)
		{
			ServerRestartManager.getInstance();
		}
		if (Config.PRECAUTIONARY_RESTART_ENABLED)
		{
			PrecautionaryRestartManager.getInstance();
		}
		if (Config.DEADLOCK_DETECTOR)
		{
			_deadDetectThread = new DeadLockDetector(Duration.ofSeconds(Config.DEADLOCK_CHECK_INTERVAL), () ->
			{
				if (Config.RESTART_ON_DEADLOCK)
				{
					Broadcast.toAllOnlinePlayers("Server has stability issues - restarting now.");
					Shutdown.getInstance().startShutdown(null, 60, true);
				}
			});
			_deadDetectThread.setDaemon(true);
			_deadDetectThread.start();
		}
		else
		{
			_deadDetectThread = null;
		}
		RandomCraftData.getInstance();
		RandomCraftExtractionData.getInstance();
		VengeanceManager.getInstance();
		L2PassData.getInstance();
		MissionLevelData.getInstance();
		PrivateStoreHistoryManager.getInstance().restore();
		SteadyBoxTaskManager.getInstance();
		DonateManager.getInstance();
		DeathDisconnectionManager.getInstance();
		PromoCodeManager.getInstance();
		VipSystemData.getInstance();
		AdditionalSaveTaskManager.getInstance();
		printSection("Fake Players");
		FakePlayerHennaData.getInstance();
		FakePlayerItemData.getInstance();
		FakePlayerSkillData.getInstance();
		FakePlayerNameData.getInstance();
		FakePlayerShopNameData.getInstance();
		FakePlayerTownData.getInstance();
		FakePlayerZoneData.getInstance();
		FakePlayerManager.getInstance();
		DiscordBotManager.getInstance();
		DiscordWebhookManager.getInstance();
		TelegramBotApi.getInstance();
		// TODO[K] - Strix section start
		// StrixPlatform.getInstance();
		// TODO[K] - Strix section end
		UniqueOnlineManager.getInstance();
		System.gc();
		final long totalMem = Runtime.getRuntime().maxMemory() / 1048576;
		LOGGER.info(getClass().getSimpleName() + ": Started, using " + getUsedMemoryMB() + " of " + totalMem + " MB total memory.");
		LOGGER.info(getClass().getSimpleName() + ": Maximum number of connected players is " + Config.MAXIMUM_ONLINE_USERS + ".");
		LOGGER.info(getClass().getSimpleName() + ": Server loaded in " + ((System.currentTimeMillis() - serverLoadStart) / 1000) + " seconds.");
		System.setProperty("async-mmocore.configurationFile", "config/async-mmocore.ini");
		connectionHandler = ConnectionBuilder.create(new InetSocketAddress(Config.PORT_GAME), GameClient::new, new ClientPacketHandler(), ThreadPool::executeMmoCore, ThreadPool.get()).disableAutoReading(!Config.ASYNC_MMOCORE_AUTO_READ).fairnessBuckets(Config.ASYNC_MMOCORE_FAIRNESS_BUCKETS).build();
		connectionHandler.start();
		if (Boolean.getBoolean("newLoginServer"))
		{
			LoginServerNetworkManager.getInstance().connect();
		}
		else
		{
			LoginServerThread.getInstance().start();
		}
		QueueManager.getInstance();
		Toolkit.getDefaultToolkit().beep();
	}
	
	public static void main(String[] args) throws Exception
	{
		INSTANCE = new GameServer();
	}
	
	public static GameServer getInstance()
	{
		return INSTANCE;
	}
	
	public long getUsedMemoryMB()
	{
		return (Runtime.getRuntime().totalMemory() - Runtime.getRuntime().freeMemory()) / 1048576;
	}
	
	public DeadLockDetector getDeadLockDetectorThread()
	{
		return _deadDetectThread;
	}
	
	public ConnectionHandler<GameClient> getConnectionHandler()
	{
		return connectionHandler;
	}
	
	public long getStartedTime()
	{
		return ManagementFactory.getRuntimeMXBean().getStartTime();
	}
	
	public String getUptime()
	{
		final long uptime = ManagementFactory.getRuntimeMXBean().getUptime() / 1000;
		final long hours = uptime / 3600;
		final long mins = (uptime - (hours * 3600)) / 60;
		final long secs = ((uptime - (hours * 3600)) - (mins * 60));
		if (hours > 0)
		{
			return hours + "hrs " + mins + "mins " + secs + "secs";
		}
		return mins + "mins " + secs + "secs";
	}
	
	private void printSection(String section)
	{
		String s = "=[ " + section + " ]";
		while (s.length() < 61)
		{
			s = "-" + s;
		}
		LOGGER.info(s);
	}
}
