/*
 * This file is part of the L2J Mobius project.
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ScheduledFuture;
import java.util.logging.Level;
import java.util.logging.Logger;

import club.projectessence.Config;
import club.projectessence.commons.concurrent.ThreadPool;
import club.projectessence.commons.database.DatabaseBackup;
import club.projectessence.commons.database.DatabaseFactory;
import club.projectessence.gameserver.data.SchemeBufferTable;
import club.projectessence.gameserver.data.SpawnTable;
import club.projectessence.gameserver.data.sql.ClanTable;
import club.projectessence.gameserver.data.sql.OfflineTraderTable;
import club.projectessence.gameserver.data.xml.LCoinShopData;
import club.projectessence.gameserver.data.xml.MableGameEventData;
import club.projectessence.gameserver.data.xml.PrimeShopData;
import club.projectessence.gameserver.fakeplayers.FakePlayerManager;
import club.projectessence.gameserver.instancemanager.CastleManorManager;
import club.projectessence.gameserver.instancemanager.CeremonyOfChaosManager;
import club.projectessence.gameserver.instancemanager.CursedWeaponsManager;
import club.projectessence.gameserver.instancemanager.DBSpawnManager;
import club.projectessence.gameserver.instancemanager.FactionWarManager;
import club.projectessence.gameserver.instancemanager.GlobalVariables;
import club.projectessence.gameserver.instancemanager.GrandBossManager;
import club.projectessence.gameserver.instancemanager.ItemAuctionManager;
import club.projectessence.gameserver.instancemanager.ItemsOnGroundManager;
import club.projectessence.gameserver.instancemanager.PrecautionaryRestartManager;
import club.projectessence.gameserver.instancemanager.QuestManager;
import club.projectessence.gameserver.instancemanager.RaidSpawnManager;
import club.projectessence.gameserver.instancemanager.UniqueOnlineManager;
import club.projectessence.gameserver.instancemanager.VengeanceManager;
import club.projectessence.gameserver.instancemanager.WorldExchangeManager;
import club.projectessence.gameserver.instancemanager.ZoneManager;
import club.projectessence.gameserver.model.Spawn;
import club.projectessence.gameserver.model.World;
import club.projectessence.gameserver.model.WorldObject;
import club.projectessence.gameserver.model.actor.Npc;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.model.olympiad.Hero;
import club.projectessence.gameserver.model.olympiad.Olympiad;
import club.projectessence.gameserver.model.zone.type.FactionZone;
import club.projectessence.gameserver.network.Disconnection;
import club.projectessence.gameserver.network.SystemMessageId;
import club.projectessence.gameserver.network.loginserverpackets.game.ServerStatus;
import club.projectessence.gameserver.network.serverpackets.SystemMessage;
import club.projectessence.gameserver.util.Broadcast;
import custom.gve.service.FactionBalanceService;
import gabriel.eventEngine.interf.GabrielEvents;

/**
 * This class provides the functions for shutting down and restarting the server.<br>
 * It closes all open client connections and saves all data.
 *
 * @version $Revision: 1.2.4.5 $ $Date: 2005/03/27 15:29:09 $
 */
public class Shutdown extends Thread
{
	private static final Logger		LOGGER				= Logger.getLogger(Shutdown.class.getName());
	private static final int		SIGTERM				= 0;
	private static final int		GM_SHUTDOWN			= 1;
	private static final int		GM_RESTART			= 2;
	private static final int		ABORT				= 3;
	private static final String[]	MODE_TEXT			=
	{
		"SIGTERM",
		"shutting down",
		"restarting",
		"aborting"
	};
	public static boolean			IS_SHUTDOWNING		= false;
	private static Shutdown			_counterInstance	= null;
	private int						_secondsShut;
	private int						_shutdownMode;
	
	/**
	 * Default constructor is only used internal to create the shutdown-hook instance
	 */
	protected Shutdown()
	{
		_secondsShut = -1;
		_shutdownMode = SIGTERM;
	}
	
	/**
	 * This creates a countdown instance of Shutdown.
	 *
	 * @param seconds
	 *            how many seconds until shutdown
	 * @param restart
	 *            true if the server will restart after shutdown
	 */
	public Shutdown(int seconds, boolean restart)
	{
		_secondsShut = Math.max(0, seconds);
		_shutdownMode = restart ? GM_RESTART : GM_SHUTDOWN;
	}
	
	/**
	 * Get the shutdown-hook instance the shutdown-hook instance is created by the first call of this function, but it has to be registered externally.
	 *
	 * @return instance of Shutdown, to be used as shutdown hook
	 */
	public static Shutdown getInstance()
	{
		return SingletonHolder.INSTANCE;
	}
	
	private void SendServerQuit(int seconds)
	{
		final SystemMessage sysm = new SystemMessage(SystemMessageId.THE_SERVER_WILL_BE_COMING_DOWN_IN_S1_SECOND_S_PLEASE_FIND_A_SAFE_PLACE_TO_LOG_OUT);
		sysm.addInt(seconds);
		Broadcast.toAllOnlinePlayers(sysm);
	}
	
	/**
	 * This function is called when a new thread starts. If this thread is the thread of getInstance, then this is the shutdown hook and we save all data and disconnect all clients.<br>
	 * After this thread ends, the server will completely exit. If this is not the thread of getInstance, then this is a countdown thread.<br>
	 * We start the countdown, and when we finished it, and it was not aborted, we tell the shutdown-hook why we call exit, and then call exit. When the exit status of the server is 1, startServer.sh / startServer.bat will restart the server.
	 */
	@Override
	public void run()
	{
		if (this == getInstance())
		{
			IS_SHUTDOWNING = true;
			final TimeCounter tc = new TimeCounter();
			final TimeCounter tc1 = new TimeCounter();
			// Stop fake player tasks
			try
			{
				for (PlayerInstance fp : World.getInstance().getFakePlayers())
				{
					fp.stopFakePlayerTask();
				}
				LOGGER.info("FakePlayerManager: All fake player tasks stopped(" + tc.getEstimatedTimeAndRestartCounter() + "ms).");
			}
			catch (Throwable t)
			{
				LOGGER.log(Level.WARNING, "Error stopping fake player tasks.", t);
			}
			// Save offline traders before disconnecting players
			try
			{
				if ((Config.OFFLINE_TRADE_ENABLE || Config.OFFLINE_CRAFT_ENABLE) && Config.RESTORE_OFFLINERS && !Config.STORE_OFFLINE_TRADE_IN_REALTIME)
				{
					OfflineTraderTable.getInstance().storeOffliners();
					LOGGER.info("Offline Traders Table: Offline shops stored(" + tc.getEstimatedTimeAndRestartCounter() + "ms).");
				}
			}
			catch (Throwable t)
			{
				LOGGER.log(Level.WARNING, "Error saving offline shops.", t);
			}
			// Disconnect all players
			try
			{
				disconnectAllCharacters();
				LOGGER.info("All players disconnected and saved(" + tc.getEstimatedTimeAndRestartCounter() + "ms).");
			}
			catch (Throwable t)
			{
				LOGGER.log(Level.WARNING, "Error disconnecting players.", t);
			}
			// Save temporary data for UniqueOnlineManager
			try
			{
				UniqueOnlineManager.getInstance().storeTemp();
				LOGGER.info("UniqueOnlineManager: Temporary data stored(" + tc.getEstimatedTimeAndRestartCounter() + "ms).");
			}
			catch (Throwable t)
			{
				LOGGER.log(Level.WARNING, "Error saving UniqueOnlineManager data.", t);
			}
			// Save VengeanceManager data
			try
			{
				VengeanceManager.getInstance().save();
				LOGGER.info("VengeanceManager: Data saved(" + tc.getEstimatedTimeAndRestartCounter() + "ms).");
			}
			catch (Throwable t)
			{
				LOGGER.log(Level.WARNING, "Error saving VengeanceManager data.", t);
			}
			// Save LCoinShop data
			try
			{
				LCoinShopData.getInstance().save();
				LOGGER.info("LCoinShopData: Data saved(" + tc.getEstimatedTimeAndRestartCounter() + "ms).");
			}
			catch (Throwable t)
			{
				LOGGER.log(Level.WARNING, "Error saving LCoinShop data.", t);
			}
			// Save MableGameEvent data
			try
			{
				MableGameEventData.getInstance().save();
				LOGGER.info("MableGameEventData: Data saved(" + tc.getEstimatedTimeAndRestartCounter() + "ms).");
			}
			catch (Throwable t)
			{
				LOGGER.log(Level.WARNING, "Error saving MableGameEvent data.", t);
			}
			// Save PrimeShop data
			try
			{
				PrimeShopData.getInstance().save();
				LOGGER.info("PrimeShopData: Data saved(" + tc.getEstimatedTimeAndRestartCounter() + "ms).");
			}
			catch (Throwable t)
			{
				LOGGER.log(Level.WARNING, "Error saving PrimeShop data.", t);
			}
			// Notify GabrielEvents of server shutdown
			GabrielEvents.serverShutDown();
			// Save remaining data
			saveData();
			tc.restartCounter();
			// Shutdown network connections
			try
			{
				GameServer.getInstance().getConnectionHandler().shutdown();
				LOGGER.info("Game Server: Networking has been shut down(" + tc.getEstimatedTimeAndRestartCounter() + "ms).");
			}
			catch (Throwable t)
			{
				LOGGER.log(Level.WARNING, "Error shutting down network connections.", t);
			}
			// Close database connection
			try
			{
				DatabaseFactory.close();
				LOGGER.info("Database Factory: Database connection has been shut down(" + tc.getEstimatedTimeAndRestartCounter() + "ms).");
			}
			catch (Throwable t)
			{
				LOGGER.log(Level.WARNING, "Error closing database connection.", t);
			}
			// Backup database if enabled
			if (Config.BACKUP_DATABASE)
			{
				DatabaseBackup.performBackup();
			}
			// Halt the JVM
			if (getInstance()._shutdownMode == GM_RESTART)
			{
				Runtime.getRuntime().halt(2);
			}
			else
			{
				Runtime.getRuntime().halt(0);
			}
			LOGGER.info("The server has been successfully shut down in " + (tc1.getEstimatedTime() / 1000) + "seconds.");
		}
		else
		{
			// GM shutdown: send warnings and then call exit to start shutdown sequence
			countdown();
			// Last point where logging is operational
			LOGGER.warning("GM shutdown countdown is over. " + MODE_TEXT[_shutdownMode] + " NOW!");
			switch (_shutdownMode)
			{
				case GM_SHUTDOWN:
				{
					getInstance().setMode(GM_SHUTDOWN);
					System.exit(0);
					break;
				}
				case GM_RESTART:
				{
					getInstance().setMode(GM_RESTART);
					System.exit(2);
					break;
				}
				case ABORT:
				{
					LoginServerThread.getInstance().setServerStatus(ServerStatus.STATUS_AUTO);
					break;
				}
			}
		}
	}
	
	/**
	 * This functions starts a shutdown countdown.
	 *
	 * @param player
	 *            GM who issued the shutdown command
	 * @param seconds
	 *            seconds until shutdown
	 * @param restart
	 *            true if the server will restart after shutdown
	 */
	public void startShutdown(PlayerInstance player, int seconds, boolean restart)
	{
		_shutdownMode = restart ? GM_RESTART : GM_SHUTDOWN;
		if (player != null)
		{
			LOGGER.warning("GM: " + player.getName() + "(" + player.getObjectId() + ") issued shutdown command. " + MODE_TEXT[_shutdownMode] + " in " + seconds + " seconds!");
		}
		else
		{
			LOGGER.warning("Server scheduled restart issued shutdown command. " + (restart ? "Restart" : "Shutdown") + " in " + seconds + " seconds!");
		}
		if (_shutdownMode > 0)
		{
			switch (seconds)
			{
				case 540:
				case 480:
				case 420:
				case 360:
				case 300:
				case 240:
				case 180:
				case 120:
				case 60:
				case 30:
				case 10:
				case 5:
				case 4:
				case 3:
				case 2:
				case 1:
					break;
				default:
					SendServerQuit(seconds);
			}
		}
		if (_counterInstance != null)
		{
			_counterInstance._abort();
		}
		if (Config.PRECAUTIONARY_RESTART_ENABLED)
		{
			PrecautionaryRestartManager.getInstance().restartEnabled();
		}
		// The main instance should only run for shutdown hook, so we start a new instance
		_counterInstance = new Shutdown(seconds, restart);
		_counterInstance.start();
	}
	
	/**
	 * This function aborts a running countdown.
	 *
	 * @param player
	 *            GM who issued the abort command
	 */
	public void abort(PlayerInstance player)
	{
		IS_SHUTDOWNING = false;
		LOGGER.warning("GM: " + (player != null ? player.getName() + "(" + player.getObjectId() + ") " : "") + "issued shutdown ABORT. " + MODE_TEXT[_shutdownMode] + " has been stopped!");
		if (_counterInstance != null)
		{
			_counterInstance._abort();
			if (Config.PRECAUTIONARY_RESTART_ENABLED)
			{
				PrecautionaryRestartManager.getInstance().restartAborted();
			}
			Broadcast.toAllOnlinePlayers("Server " + MODE_TEXT[_shutdownMode] + " aborted.", false);
		}
	}
	
	/**
	 * Set the shutdown mode.
	 *
	 * @param mode
	 *            what mode shall be set
	 */
	private void setMode(int mode)
	{
		_shutdownMode = mode;
	}
	
	/**
	 * Set shutdown mode to ABORT.
	 */
	private void _abort()
	{
		_shutdownMode = ABORT;
	}
	
	/**
	 * This counts the countdown and reports it to all players. Countdown is aborted if mode changes to ABORT.
	 */
	private void countdown()
	{
		try
		{
			while (_secondsShut > 0)
			{
				// Rehabilitate previous server status if shutdown is aborted.
				if (_shutdownMode == ABORT)
				{
					if (LoginServerThread.getInstance().getServerStatus() == ServerStatus.STATUS_GM_ONLY)
					{
						LoginServerThread.getInstance().setServerStatus((Config.SERVER_GMONLY) ? ServerStatus.STATUS_GM_ONLY : ServerStatus.STATUS_AUTO);
					}
					break;
				}
				switch (_secondsShut)
				{
					case 540:
					case 480:
					case 420:
					case 360:
					case 300:
					case 240:
					case 180:
					case 120:
					case 60:
					case 30:
					case 10:
					case 5:
					case 4:
					case 3:
					case 2:
					case 1:
						SendServerQuit(_secondsShut);
				}
				if (_secondsShut == 120)
				{
					for (PlayerInstance player : World.getInstance().getPlayers())
					{
						if (player.isInOfflineMode())
						{
							Disconnection.of(player).logout(true, true);
						}
					}
				}
				if ((_secondsShut <= 60) && ((_secondsShut % 5) == 0))
				{
					for (WorldObject obj : World.getInstance().getVisibleObjects())
					{
						if ((obj != null) && obj.isNpc())
						{
							final Npc target = (Npc) obj;
							target.deleteMe();
							final Spawn spawn = target.getSpawn();
							if (spawn != null)
							{
								spawn.stopRespawn();
								SpawnTable.getInstance().deleteSpawn(spawn, false);
							}
						}
					}
				}
				// Prevent players from logging in.
				if ((_secondsShut <= 60) && (LoginServerThread.getInstance().getServerStatus() != ServerStatus.STATUS_GM_ONLY))
				{
					LoginServerThread.getInstance().setServerStatus(ServerStatus.STATUS_GM_ONLY);
				}
				_secondsShut--;
				final int delay = 1000; // milliseconds
				Thread.sleep(delay);
			}
		}
		catch (Exception e)
		{
			// This will never happen
		}
	}
	
	/**
	 * This sends a last byebye and saves remaining data.
	 */
	private void saveData()
	{
		switch (_shutdownMode)
		{
			case SIGTERM:
			{
				LOGGER.info("SIGTERM received. Shutting down NOW!");
				break;
			}
			case GM_SHUTDOWN:
			{
				LOGGER.info("GM shutdown received. Shutting down NOW!");
				break;
			}
			case GM_RESTART:
			{
				LOGGER.info("GM restart received. Restarting NOW!");
				break;
			}
		}
		final TimeCounter tc = new TimeCounter();
		// Stop FactionWarManager tasks and save faction points
		try
		{
			FactionWarManager.getInstance().shutdown();
			LOGGER.info("FactionWarManager: Faction points saved and tasks stopped(" + tc.getEstimatedTimeAndRestartCounter() + "ms).");
		}
		catch (Throwable t)
		{
			LOGGER.log(Level.WARNING, "Error saving faction points or stopping FactionWarManager tasks.", t);
		}
		// Save all raidboss and GrandBoss status
		DBSpawnManager.getInstance().cleanUp();
		RaidSpawnManager.getInstance().cleanUp();
		LOGGER.info("RaidBossSpawnManager: All raidboss info saved(" + tc.getEstimatedTimeAndRestartCounter() + "ms).");
		GrandBossManager.getInstance().cleanUp();
		LOGGER.info("GrandBossManager: All Grand Boss info saved(" + tc.getEstimatedTimeAndRestartCounter() + "ms).");
		// Stop ItemAuctionManager tasks
		ItemAuctionManager.getInstance().shutdown();
		LOGGER.info("Item Auction Manager: All tasks stopped(" + tc.getEstimatedTimeAndRestartCounter() + "ms).");
		// Save Olympiad data
		Olympiad.getInstance().saveOlympiadStatus();
		LOGGER.info("Olympiad System: Data saved(" + tc.getEstimatedTimeAndRestartCounter() + "ms).");
		// Stop CeremonyOfChaosManager scheduler
		CeremonyOfChaosManager.getInstance().stopScheduler();
		LOGGER.info("CeremonyOfChaosManager: Scheduler stopped(" + tc.getEstimatedTimeAndRestartCounter() + "ms).");
		// Save Hero data
		Hero.getInstance().shutdown();
		LOGGER.info("Hero System: Data saved(" + tc.getEstimatedTimeAndRestartCounter() + "ms).");
		// Save Clan data
		ClanTable.getInstance().shutdown();
		LOGGER.info("Clan System: Data saved(" + tc.getEstimatedTimeAndRestartCounter() + "ms).");
		// Save Cursed Weapons data
		CursedWeaponsManager.getInstance().saveData();
		LOGGER.info("Cursed Weapons Manager: Data saved(" + tc.getEstimatedTimeAndRestartCounter() + "ms).");
		// Save CastleManorManager data
		if (!Config.ALT_MANOR_SAVE_ALL_ACTIONS)
		{
			CastleManorManager.getInstance().storeMe();
			LOGGER.info("Castle Manor Manager: Data saved(" + tc.getEstimatedTimeAndRestartCounter() + "ms).");
		}
		// Save Quest data
		QuestManager.getInstance().save();
		LOGGER.info("Quest Manager: Data saved(" + tc.getEstimatedTimeAndRestartCounter() + "ms).");
		// Save Global Variables
		GlobalVariables.getInstance().set("LOOT_BOXES_OPEN", World.LOOT_BOXES_OPENED);
		GlobalVariables.getInstance().set("L_COINS_FARMED", World.L_COINS_FARMED);
		// Event Sibi Stats
		GlobalVariables.getInstance().set("BALTHUS_KNIGHT_MARK_FARMED", World.BALTHUS_KNIGHT_MARK_FARMED);
		GlobalVariables.getInstance().set("LIFE_CONTROL_TOWER_FARMED", World.LIFE_CONTROL_TOWER_FARMED);
		GlobalVariables.getInstance().set("MID_GRADE_HP_POTION_FARMED", World.MID_GRADE_HP_POTION_FARMED);
		GlobalVariables.getInstance().set("SCROLL_BOOST_ATTACK_FARMED", World.SCROLL_BOOST_ATTACK_FARMED);
		GlobalVariables.getInstance().set("SCROLL_BOOST_DEFENSE_FARMED", World.SCROLL_BOOST_DEFENSE_FARMED);
		GlobalVariables.getInstance().set("SAYHA_COOKIE_FARMED", World.SAYHA_COOKIE_FARMED);
		GlobalVariables.getInstance().set("SAYHA_BLESSING_FARMED", World.SAYHA_BLESSING_FARMED);
		GlobalVariables.getInstance().set("ADENA_TAKE", World.ADENA_TAKE);
		GlobalVariables.getInstance().set("L_COINS_TAKE", World.L_COINS_TAKE);
		GlobalVariables.getInstance().set("SIBI_COIN_TAKE", World.SIBI_COIN_TAKE);
		GlobalVariables.getInstance().storeMe();
		LOGGER.info("Global Variables Manager: Variables saved(" + tc.getEstimatedTimeAndRestartCounter() + "ms).");
		// Save SchemeBufferTable data
		SchemeBufferTable.getInstance().saveSchemes();
		LOGGER.info("SchemeBufferTable data has been saved.");
		// Save World Exchange data
		if (Config.ENABLE_WORLD_EXCHANGE)
		{
			WorldExchangeManager.getInstance().storeMe();
			LOGGER.info("World Exchange Manager: Data saved(" + tc.getEstimatedTimeAndRestartCounter() + "ms).");
		}
		// Save FakePlayerManager data
		FakePlayerManager.getInstance().storeFakePlayers();
		// Save items on ground
		if (Config.SAVE_DROPPED_ITEM)
		{
			ItemsOnGroundManager.getInstance().saveInDb();
			LOGGER.info("Items On Ground Manager: Data saved(" + tc.getEstimatedTimeAndRestartCounter() + "ms).");
			ItemsOnGroundManager.getInstance().cleanUp();
			LOGGER.info("Items On Ground Manager: Cleaned up(" + tc.getEstimatedTimeAndRestartCounter() + "ms).");
		}
		// Save FactionBalanceService data
		try
		{
			FactionBalanceService.getInstance().saveToDatabase();
			LOGGER.info("FactionBalanceService: Data saved(" + tc.getEstimatedTimeAndRestartCounter() + "ms).");
		}
		catch (Throwable t)
		{
			LOGGER.log(Level.WARNING, "Error saving FactionBalanceService data.", t);
		}
		// Save all faction zones
		try
		{
			for (FactionZone zone : ZoneManager.getInstance().getAllZones(FactionZone.class))
			{
				zone.saveToDatabase();
			}
			LOGGER.info("FactionZoneManager: All faction zones saved(" + tc.getEstimatedTimeAndRestartCounter() + "ms).");
		}
		catch (Throwable t)
		{
			LOGGER.log(Level.WARNING, "Error saving faction zones.", t);
		}
		// Stop GameTimeController
		try
		{
			GameTimeController.getInstance().stopTimer();
			LOGGER.info("Game Time Controller: Timer stopped(" + tc.getEstimatedTimeAndRestartCounter() + "ms).");
		}
		catch (Throwable t)
		{
			LOGGER.log(Level.WARNING, "Error stopping GameTimeController.", t);
		}
		// Stop all thread pools
		try
		{
			ThreadPool.get().shutdown();
			LOGGER.info("Thread Pool Manager: Manager has been shut down(" + tc.getEstimatedTimeAndRestartCounter() + "ms).");
		}
		catch (Throwable t)
		{
			LOGGER.log(Level.WARNING, "Error shutting down ThreadPool.", t);
		}
		// Interrupt LoginServerThread
		try
		{
			LoginServerThread.getInstance().interrupt();
			LOGGER.info("Login Server Thread: Thread interrupted(" + tc.getEstimatedTimeAndRestartCounter() + "ms).");
		}
		catch (Throwable t)
		{
			LOGGER.log(Level.WARNING, "Error interrupting LoginServerThread.", t);
		}
		// Sleep for a short period to ensure all tasks are completed
		try
		{
			Thread.sleep(5000);
		}
		catch (Exception e)
		{
			// This will never happen
		}
	}
	
	/**
	 * This disconnects all clients from the server.
	 */
	private void disconnectAllCharacters()
	{
		long start = System.currentTimeMillis();
		List<PlayerInstance> players_1 = new ArrayList<>();
		List<PlayerInstance> players_2 = new ArrayList<>();
		int i = 0;
		for (PlayerInstance player : World.getInstance().getPlayers())
		{
			i++;
			if ((i % 2) == 0)
			{
				players_1.add(player);
			}
			else
			{
				players_2.add(player);
			}
		}
		ScheduledFuture<?> thread_1 = ThreadPool.schedule(() ->
		{
			for (PlayerInstance player : players_1)
			{
				long charStart = System.currentTimeMillis();
				Disconnection.of(player).logout(true, true);
				long time = System.currentTimeMillis() - charStart;
				LOGGER.info("Disconnection of " + player + " took " + time + " ms.");
			}
		}, 100);
		ScheduledFuture<?> thread_2 = ThreadPool.schedule(() ->
		{
			for (PlayerInstance player : players_2)
			{
				long charStart = System.currentTimeMillis();
				Disconnection.of(player).logout(true, true);
				long time = System.currentTimeMillis() - charStart;
				LOGGER.info("Disconnection of " + player + " took " + time + " ms.");
			}
		}, 100);
		while (!thread_1.isDone() || !thread_2.isDone())
		{
			try
			{
				Thread.sleep(1000);
			}
			catch (InterruptedException e)
			{
				e.printStackTrace();
			}
		}
		for (PlayerInstance player : World.getInstance().getPlayers())
		{
			long charStart = System.currentTimeMillis();
			Disconnection.of(player).logout(true, true);
			long time = System.currentTimeMillis() - charStart;
			LOGGER.info("Disconnection of " + player + " took " + time + " ms.");
		}
		LOGGER.info("Disconnection of characters took " + (System.currentTimeMillis() - start) + " ms.");
	}
	
	/**
	 * A simple class used to track down the estimated time of method executions.<br>
	 * Once this class is created, it saves the start time, and when you want to get the estimated time, use the getEstimatedTime() method.
	 */
	private static final class TimeCounter
	{
		private long _startTime;
		
		protected TimeCounter()
		{
			restartCounter();
		}
		
		protected void restartCounter()
		{
			_startTime = System.currentTimeMillis();
		}
		
		protected long getEstimatedTimeAndRestartCounter()
		{
			final long toReturn = System.currentTimeMillis() - _startTime;
			restartCounter();
			return toReturn;
		}
		
		protected long getEstimatedTime()
		{
			return System.currentTimeMillis() - _startTime;
		}
	}
	
	private static class SingletonHolder
	{
		protected static final Shutdown INSTANCE = new Shutdown();
	}
}