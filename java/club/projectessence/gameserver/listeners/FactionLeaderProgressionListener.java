package club.projectessence.gameserver.listeners;

import club.projectessence.gameserver.instancemanager.FactionLeaderManager;
import club.projectessence.gameserver.instancemanager.FactionLeaderProgressionManager;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.model.events.EventType;
import club.projectessence.gameserver.model.events.ListenerRegisterType;
import club.projectessence.gameserver.model.events.ListenersContainer;
import club.projectessence.gameserver.model.events.annotations.RegisterEvent;
import club.projectessence.gameserver.model.events.annotations.RegisterType;
import club.projectessence.gameserver.model.events.impl.creature.player.OnPlayerLogin;
import club.projectessence.gameserver.model.events.impl.creature.player.OnPlayerLogout;
import club.projectessence.gameserver.model.events.impl.creature.player.OnPlayerPvPKill;
import club.projectessence.gameserver.model.events.impl.creature.player.OnPlayerLevelChanged;
import club.projectessence.gameserver.model.events.listeners.ConsumerEventListener;
import club.projectessence.gameserver.enums.LeadershipAchievement;
import club.projectessence.gameserver.enums.LeadershipTask;
import club.projectessence.gameserver.features.museum.MuseumManager;

/**
 * Event listener for faction leader progression tracking.
 */
public class FactionLeaderProgressionListener extends ListenersContainer {
	@RegisterEvent(EventType.ON_PLAYER_LOGIN)
	@RegisterType(ListenerRegisterType.GLOBAL_PLAYERS)
	public void onPlayerLogin(OnPlayerLogin event)
	{
		PlayerInstance player = event.getPlayer();
		if (player == null) return;
		
		// Check if player is a faction leader
		if (FactionLeaderManager.getInstance().isLeader(player))
		{
			// Initialize progression data if needed
			FactionLeaderProgressionManager.getInstance().getOrCreateProgression(player);
			
			// Update online time task
			FactionLeaderProgressionManager.getInstance().updateTaskProgress(player, LeadershipTask.ONLINE_TIME, 0);
		}
	}
	
	@RegisterEvent(EventType.ON_PLAYER_LOGOUT)
	@RegisterType(ListenerRegisterType.GLOBAL_PLAYERS)
	public void onPlayerLogout(OnPlayerLogout event)
	{
		PlayerInstance player = event.getPlayer();
		if (player == null) return;
		
		// Save progression data on logout
		if (FactionLeaderManager.getInstance().isLeader(player))
		{
			// Calculate online time for the session
			// This would need to be tracked separately
		}
	}
	
	@RegisterEvent(EventType.ON_PLAYER_PVP_KILL)
	@RegisterType(ListenerRegisterType.GLOBAL_PLAYERS)
	public void onPlayerPvPKill(OnPlayerPvPKill event)
	{
		PlayerInstance killer = event.getPlayer();
		PlayerInstance victim = event.getTarget();
		
		if (killer == null || victim == null) return;
		
		// Check if killer is faction leader
		if (FactionLeaderManager.getInstance().isLeader(killer))
		{
			// Award combat LP
			FactionLeaderProgressionManager.getInstance().awardLeadershipPoints(
				killer, 10, LeadershipAchievement.AchievementCategory.COMBAT, "PvP Kill"
			);

			// Update PvP tasks
			FactionLeaderProgressionManager.getInstance().updateTaskProgress(killer, LeadershipTask.PVP_LEADERSHIP, 1);

			// Add museum data for leadership activity
			MuseumManager.getInstance().addLeadershipActivity(killer);

			// Check for achievements
			// This would need to track total kills
			checkCombatAchievements(killer);
		}
	}
	
	@RegisterEvent(EventType.ON_PLAYER_LEVEL_CHANGED)
	@RegisterType(ListenerRegisterType.GLOBAL_PLAYERS)
	public void onPlayerLevelChanged(OnPlayerLevelChanged event)
	{
		PlayerInstance player = event.getPlayer();
		if (player == null) return;
		
		// Check if a faction member leveled up under leader's guidance
		PlayerInstance leader = getFactionLeader(player);
		if (leader != null && leader != player)
		{
			// Award leadership LP to the leader
			FactionLeaderProgressionManager.getInstance().awardLeadershipPoints(
				leader, 5, LeadershipAchievement.AchievementCategory.LEADERSHIP, "Member Level Up: " + player.getName()
			);
			
			// Update mentor task
			if (player.getLevel() >= 85) // Max level or high level
			{
				FactionLeaderProgressionManager.getInstance().updateTaskProgress(leader, LeadershipTask.HELP_MEMBERS, 1);
			}
		}
	}
	
	/**
	 * Check and award combat-related achievements
	 */
	private void checkCombatAchievements(PlayerInstance player)
	{
		FactionLeaderProgressionManager progressionManager = FactionLeaderProgressionManager.getInstance();
		
		// Get player's combat statistics (would need to be tracked)
		int totalKills = getTotalPvPKills(player);
		int totalWars = getTotalWarsWon(player);
		int totalDefenses = getTotalZoneDefenses(player);
		
		// Check achievements
		progressionManager.checkAndAwardAchievement(player, LeadershipAchievement.FIRST_BLOOD, totalKills);
		progressionManager.checkAndAwardAchievement(player, LeadershipAchievement.SLAYER, totalKills);
		progressionManager.checkAndAwardAchievement(player, LeadershipAchievement.WARMONGER, totalWars);
		progressionManager.checkAndAwardAchievement(player, LeadershipAchievement.DEFENDER, totalDefenses);
	}
	
	/**
	 * Check and award leadership-related achievements
	 */
	private void checkLeadershipAchievements(PlayerInstance player)
	{
		FactionLeaderProgressionManager progressionManager = FactionLeaderProgressionManager.getInstance();
		
		// Get player's leadership statistics
		int membersRecruited = getMembersRecruited(player);
		int outpostsCaptured = getOutpostsCaptured(player);
		int alliancesMade = getAlliancesMade(player);
		long treasuryBalance = getFactionTreasuryBalance(player);
		
		// Check achievements
		progressionManager.checkAndAwardAchievement(player, LeadershipAchievement.RECRUITER, membersRecruited);
		progressionManager.checkAndAwardAchievement(player, LeadershipAchievement.STRATEGIST, outpostsCaptured);
		progressionManager.checkAndAwardAchievement(player, LeadershipAchievement.DIPLOMAT, alliancesMade);
		progressionManager.checkAndAwardAchievement(player, LeadershipAchievement.ECONOMIST, (int) (treasuryBalance / 1000000));
	}
	
	/**
	 * Get the faction leader for a player
	 */
	private PlayerInstance getFactionLeader(PlayerInstance player)
	{
		// This would need to be implemented to find the current faction leader
		return FactionLeaderManager.getInstance().getLeader(player.getFaction());
	}
	
	// Placeholder methods for statistics tracking
	// These would need to be implemented with proper data tracking
	
	private int getTotalPvPKills(PlayerInstance player)
	{
		// Get from progression statistics or database
		return 0;
	}
	
	private int getTotalWarsWon(PlayerInstance player)
	{
		// Get from progression statistics or database
		return 0;
	}
	
	private int getTotalZoneDefenses(PlayerInstance player)
	{
		// Get from progression statistics or database
		return 0;
	}
	
	private int getMembersRecruited(PlayerInstance player)
	{
		// Get from progression statistics or database
		return 0;
	}
	
	private int getOutpostsCaptured(PlayerInstance player)
	{
		// Get from progression statistics or database
		return 0;
	}
	
	private int getAlliancesMade(PlayerInstance player)
	{
		// Get from progression statistics or database
		return 0;
	}
	
	private long getFactionTreasuryBalance(PlayerInstance player)
	{
		// Get from faction treasury system
		return 0;
	}
	
	/**
	 * Award LP for various faction leader activities
	 */
	public static void awardActivityLP(PlayerInstance player, String activity, int baseLP)
	{
		if (!FactionLeaderManager.getInstance().isLeader(player)) return;
		
		LeadershipAchievement.AchievementCategory category = LeadershipAchievement.AchievementCategory.LEADERSHIP;
		
		// Determine category based on activity
		if (activity.contains("combat") || activity.contains("pvp") || activity.contains("war"))
		{
			category = LeadershipAchievement.AchievementCategory.COMBAT;
		}
		else if (activity.contains("social") || activity.contains("recruit") || activity.contains("alliance"))
		{
			category = LeadershipAchievement.AchievementCategory.SOCIAL;
		}
		else if (activity.contains("economic") || activity.contains("treasury") || activity.contains("trade"))
		{
			category = LeadershipAchievement.AchievementCategory.ECONOMIC;
		}
		
		FactionLeaderProgressionManager.getInstance().awardLeadershipPoints(player, baseLP, category, activity);
	}
	
	/**
	 * Update task progress for various activities
	 */
	public static void updateTaskProgress(PlayerInstance player, String taskType, int progress)
	{
		if (!FactionLeaderManager.getInstance().isLeader(player)) return;
		
		try
		{
			LeadershipTask task = LeadershipTask.valueOf(taskType);
			FactionLeaderProgressionManager.getInstance().updateTaskProgress(player, task, progress);
		}
		catch (IllegalArgumentException e)
		{
			// Invalid task type, ignore
		}
	}
}
