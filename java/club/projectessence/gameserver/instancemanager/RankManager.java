/*
 * This file is part of the L2J Mobius project.
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.instancemanager;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.concurrent.ConcurrentHashMap;
import java.util.logging.Level;
import java.util.logging.Logger;

import club.projectessence.commons.concurrent.ThreadPool;
import club.projectessence.commons.database.DatabaseFactory;
import club.projectessence.gameserver.data.sql.CharNameTable;
import club.projectessence.gameserver.data.sql.ClanTable;
import club.projectessence.gameserver.data.xml.PetDataTable;
import club.projectessence.gameserver.data.xml.SkillData;
import club.projectessence.gameserver.model.Location;
import club.projectessence.gameserver.model.PetData;
import club.projectessence.gameserver.model.StatSet;
import club.projectessence.gameserver.model.World;
import club.projectessence.gameserver.model.actor.instance.BuffzoneNpcInstance;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.model.clan.Clan;
import club.projectessence.gameserver.model.olympiad.Hero;
import club.projectessence.gameserver.model.skills.Skill;
import club.projectessence.gameserver.model.zone.ZoneId;
import club.projectessence.gameserver.network.SystemMessageId;
import club.projectessence.gameserver.network.serverpackets.ExRotation;
import club.projectessence.gameserver.network.serverpackets.SystemMessage;
import club.projectessence.gameserver.network.serverpackets.ranking.ExBowActionTo;
import club.projectessence.gameserver.network.serverpackets.ranking.ExRankingCharBuffzoneNpcInfo;
import club.projectessence.gameserver.network.serverpackets.ranking.ExRankingCharBuffzoneNpcPosition;
import club.projectessence.gameserver.util.Broadcast;
import club.projectessence.gameserver.util.Util;

/**
 * <AUTHOR> Benetis
 * @rework Thoss
 */
public class RankManager
{
	public static final long							BUFFZONE_DURATION				= 12 * 60 * 60 * 1000;
	public static final long							BUFFZONE_COOLDOWN				= 24 * 60 * 60 * 1000;
	private static final Logger							LOGGER							= Logger.getLogger(RankManager.class.getName());
	private static final String							SELECT_CHARACTERS				= "SELECT charId,char_name,level,race,base_class, clanid FROM characters WHERE accesslevel = 0 AND level > 39 AND UNIX_TIMESTAMP() - characters.lastAccess / 1000 < 604800 ORDER BY exp DESC";																																																																																																// LIMIT
																																																																																																																																																																					// "
																																																																																																																																																																					// +
																																																																																																																																																																					// PLAYER_LIMIT;
	private static final String							SELECT_RANKING_STATISTICS		= "SELECT charId,`time`,server_rank,race_rank,exp_earned FROM ranking_statistics";
	private static final String							GET_NEW_RANKING_STATISTICS		= "SELECT charId,exp FROM characters WHERE UNIX_TIMESTAMP() - characters.lastAccess / 1000 < 604800";
	private static final String							INSERT_NEW_RANKING_STATISTICS	= "INSERT INTO ranking_statistics (charId, `time`, server_rank, race_rank, exp_earned) VALUES(?, ?, ?, ?, ?)";
	private static final String							SELECT_REAL_RANKINGS			= "SELECT charId,server_rank,race_rank FROM rankings_level ORDER BY server_rank ASC";
	private static final String							DELETE_REAL_RANKINGS			= "TRUNCATE TABLE rankings_level";
	private static final String							INSERT_REAL_RANKINGS			= "INSERT INTO rankings_level (charId, server_rank, race_rank) VALUES (?, ?, ?)";
	private static final String							GET_CURRENT_OLY_CYCLE_DATA		= "SELECT characters.char_name, characters.level, characters.base_class, characters.clanid, olympiad_nobles.charId, olympiad_nobles.olympiad_points, olympiad_nobles.competitions_done, olympiad_nobles.competitions_won, olympiad_nobles.competitions_lost FROM characters, olympiad_nobles WHERE UNIX_TIMESTAMP() - characters.lastAccess / 1000 < 604800 AND characters.accesslevel=0 AND characters.charId = olympiad_nobles.charId ORDER BY olympiad_nobles.olympiad_points DESC, olympiad_nobles.competitions_done DESC, olympiad_nobles.competitions_won DESC, characters.exp DESC";
	private static final String							DELETE_PREVIOUS_OLY_CYCLE		= "TRUNCATE TABLE rankings_oly_previous";
	private static final String							SELECT_PREVIOUS_OLY_CYCLE		= "SELECT rankings_oly_previous.charId, rankings_oly_previous.server_rank, rankings_oly_previous.class_id, rankings_oly_previous.class_rank, rankings_oly_previous.competitions_won, rankings_oly_previous.competitions_lost, rankings_oly_previous.olympiad_points, characters.level, characters.clanid FROM rankings_oly_previous INNER JOIN characters ON rankings_oly_previous.charId = characters.charId";
	private static final String							INSERT_PREVIOUS_OLY_CYCLE		= "INSERT INTO rankings_oly_previous VALUES (?, ?, ?, ?, ?, ?, ?)";
	private static final String							SELECT_REAL_PVP_RANKINGS		= "SELECT charId,server_rank,race_rank FROM rankings_pvp ORDER BY server_rank ASC";
	private static final String							DELETE_REAL_PVP_RANKINGS		= "TRUNCATE TABLE rankings_pvp";
	private static final String							INSERT_REAL_PVP_RANKINGS		= "INSERT INTO rankings_pvp (charId, server_rank, race_rank) VALUES (?, ?, ?)";
	private static final String							SELECT_PVP_CHARACTERS			= "SELECT charId,char_name,level,pvpkills,pvpdeaths,race,base_class, clanid FROM characters WHERE accesslevel = 0 AND level > 39 AND UNIX_TIMESTAMP() - characters.lastAccess / 1000 < 604800 ORDER BY pvpkills DESC";																																																																																										// LIMIT
																																																																																																																																																																					// "
																																																																																																																																																																					// +
																																																																																																																																																																					// PLAYER_LIMIT;
	private static final String							SELECT_REAL_CLAN_RANKINGS		= "SELECT `clanId`, `rank` FROM rankings_clan ORDER BY `rank` ASC";
	private static final String							DELETE_REAL_CLAN_RANKINGS		= "TRUNCATE TABLE rankings_clan";
	private static final String							INSERT_REAL_CLAN_RANKINGS		= "INSERT INTO rankings_clan (`clanId`, `rank`) VALUES (?, ?)";
	private static final String							SELECT_CLANS					= "SELECT clan_id,clan_name,clan_level,clan_exp,leader_id,level,(SELECT COUNT(charId) FROM characters WHERE clanId = clan_data.clan_id) AS member_count FROM clan_data, characters WHERE clan_data.leader_id = characters.charId ORDER BY clan_exp DESC";
	private static final String							SELECT_REAL_PET_RANKINGS		= "SELECT pet_control_object_id,server_rank,race_rank,ownerId,item_id,name,pets.level as level,evolveLevel,clanid,pets.exp as exp, characters.char_name as char_name, characters.level as char_level, characters.race as char_race FROM rankings_pet, pets, items, characters WHERE rankings_pet.pet_control_object_id = pets.item_obj_id AND pets.ownerId = characters.charId AND pets.item_obj_id = items.object_id ORDER BY server_rank ASC";
	private static final String							DELETE_REAL_PET_RANKINGS		= "TRUNCATE TABLE rankings_pet";
	private static final String							INSERT_REAL_PET_RANKINGS		= "INSERT INTO rankings_pet (pet_control_object_id, server_rank, race_rank) VALUES (?, ?, ?)";
	private static final String							SELECT_PETS						= "SELECT item_obj_id,name,pets.level as level,evolveLevel,clanid,item_id,ownerId,pets.exp as exp, characters.char_name as char_name, characters.level as char_level, characters.race as char_race FROM pets,characters,items WHERE pets.ownerId = characters.charId AND pets.item_obj_id = items.object_id AND accesslevel = 0 AND pets.level > 39 ORDER BY pets.exp DESC";
	// Skills
	private static final Skill							PVP_RANK_1_3					= SkillData.getInstance().getSkill(52019, 1);
	private static final Skill							SERVER_RANK_1					= SkillData.getInstance().getSkill(60003, 1);
	private static final Skill							SERVER_RANK_2_30				= SkillData.getInstance().getSkill(60004, 1);
	private static final Skill							SERVER_RANK_31_100				= SkillData.getInstance().getSkill(60005, 1);
	private static final Skill							HUMAN_RACE_RANK_1				= SkillData.getInstance().getSkill(60006, 1);
	private static final Skill							ELF_RACE_RANK_1					= SkillData.getInstance().getSkill(60007, 1);
	private static final Skill							DARK_ELF_RACE_RANK_1			= SkillData.getInstance().getSkill(60008, 1);
	private static final Skill							ORC_RACE_RANK_1					= SkillData.getInstance().getSkill(60009, 1);
	private static final Skill							DWARF_RACE_RANK_1				= SkillData.getInstance().getSkill(60010, 1);
	private static final Skill							KAMAEL_RACE_RANK_1				= SkillData.getInstance().getSkill(60011, 1);
	private static final Skill							SYLPH_RACE_RANK_1				= SkillData.getInstance().getSkill(46033, 1);
	private static final Skill							SERVER_RANK_1_BONUS				= SkillData.getInstance().getSkill(60014, 1);
	private static final Skill							SERVER_RANK_2_BONUS				= SkillData.getInstance().getSkill(60013, 1);
	private static final Skill							SERVER_RANK_3_BONUS				= SkillData.getInstance().getSkill(60012, 1);
	// Used only for visualization on rank manager?
	// private static final Skill RACE_RANKING_BENEFIT = SkillData.getInstance().getSkill(60015, 1);
	private static final Skill							HUMAN_LEVEL_TRANSFORM_CLASS		= SkillData.getInstance().getSkill(54204, 1);
	private static final Skill							ELF_LEVEL_TRANSFORM_CLASS		= SkillData.getInstance().getSkill(54210, 1);
	private static final Skill							DARK_ELF_LEVEL_TRANSFORM_CLASS	= SkillData.getInstance().getSkill(54211, 1);
	private static final Skill							ORC_LEVEL_TRANSFORM_CLASS		= SkillData.getInstance().getSkill(54209, 1);
	private static final Skill							DWARF_LEVEL_TRANSFORM_CLASS		= SkillData.getInstance().getSkill(54212, 1);
	private static final Skill							KAMAEL_LEVEL_TRANSFORM_CLASS	= SkillData.getInstance().getSkill(54205, 1);
	private static final Skill							SYLPH_LEVEL_TRANSFORM_CLASS		= SkillData.getInstance().getSkill(54226, 1);
	private final Map<Integer, StatSet>					_mainList						= new ConcurrentHashMap<>();
	private final Map<Integer, StatSet>					_currentVisualList				= new ConcurrentHashMap<>();
	private final Map<Integer, Integer>					_levelRealRanks					= new ConcurrentHashMap<>();
	private final Map<Integer, Integer>					_levelRealRaceRanks				= new ConcurrentHashMap<>();
	private final Map<Integer, Integer>					_levelVisualRanks				= new ConcurrentHashMap<>();
	private final Map<Integer, Integer>					_levelVisualRaceRanks			= new ConcurrentHashMap<>();
	private final Map<Integer, Map<Integer, StatSet>>	_rankingStatistics				= new ConcurrentHashMap<>();
	private final Map<Integer, StatSet>					_olyList						= new ConcurrentHashMap<>();
	private final Map<Integer, StatSet>					_olyPreviousList				= new ConcurrentHashMap<>();
	private final Map<Integer, StatSet>					_pvpList						= new ConcurrentHashMap<>();
	private final Map<Integer, StatSet>					_pvpVisualList					= new ConcurrentHashMap<>();
	private final Map<Integer, Integer>					_pvpRealRanks					= new ConcurrentHashMap<>();
	private final Map<Integer, Integer>					_pvpRealRaceRanks				= new ConcurrentHashMap<>();
	private final Map<Integer, Integer>					_pvpVisualRanks					= new ConcurrentHashMap<>();
	private final Map<Integer, StatSet>					_clanList						= new ConcurrentHashMap<>();
	private final Map<Integer, StatSet>					_clanVisualList					= new ConcurrentHashMap<>();
	private final Map<Integer, Integer>					_clanRealRanks					= new ConcurrentHashMap<>();
	private final Map<Integer, Integer>					_clanVisualRanks				= new ConcurrentHashMap<>();
	private final Map<Integer, StatSet>					_petList						= new ConcurrentHashMap<>();
	private final Map<Integer, StatSet>					_petVisualList					= new ConcurrentHashMap<>();
	private final Map<Integer, Integer>					_petRealRanks					= new ConcurrentHashMap<>();
	private final Map<Integer, Integer>					_petRealRaceRanks				= new ConcurrentHashMap<>();
	private final Map<Integer, Integer>					_petVisualRanks					= new ConcurrentHashMap<>();
	private Location									_buffzoneNpcLocation			= null;
	// private static final Skill DEATH_KNIGHT_LEVEL_TRANSFORM_CLASS = SkillData.getInstance().getSkill(54208, 1);
	public static final int								PLAYER_LIMIT					= 500;
	
	// Throttling maps to prevent packet spam
	private final Map<Integer, Long> _lastBowActionTime = new ConcurrentHashMap<>();
	private final Map<Integer, Long> _lastRotationTime = new ConcurrentHashMap<>();
	private static final long BOW_ACTION_THROTTLE = 30000; // 30 seconds
	private static final long ROTATION_THROTTLE = 5000; // 5 seconds
	private static final int RANK_EFFECT_RANGE = 800; // Reduced from 1400 to 800

	protected RankManager()
	{
		ThreadPool.scheduleAtFixedRate(() ->
		{
			if (!_mainList.isEmpty())
			{
				PlayerInstance rank1 = World.getInstance().getPlayer(_mainList.get(1).getInt("charId"));
				if (rank1 != null)
				{
					if (rank1.isInsideZone(ZoneId.PEACE))
					{
						long currentTime = System.currentTimeMillis();

						// Throttled rotation updates
						final int[] rotationCount = {0}; // Use array to allow modification in lambda
						World.getInstance().forEachVisibleObjectInRange(rank1, PlayerInstance.class, RANK_EFFECT_RANGE, player ->
						{
							if (!player.isSitting() && !player.hasBlockActions() && !player.isGM() && !player.isInCombat())
							{
								Long lastRotation = _lastRotationTime.get(player.getObjectId());
								if (lastRotation == null || (currentTime - lastRotation) >= ROTATION_THROTTLE)
								{
									int newHeading = Util.calculateHeadingFrom(player, rank1);
									player.setHeading(newHeading);
									Broadcast.toSelfAndKnownPlayersInRadius(rank1, new ExRotation(player.getObjectId(), newHeading), RANK_EFFECT_RANGE);
									_lastRotationTime.put(player.getObjectId(), currentTime);
									rotationCount[0]++;
								}
							}
						});
						if (rotationCount[0] > 0)
						{
							LOGGER.info("RankManager: Applied rotation to " + rotationCount[0] + " players around rank 1 player " + rank1.getName());
						}

						// Throttled bow action - much less frequent
						Long lastBowAction = _lastBowActionTime.get(rank1.getObjectId());
						if (lastBowAction == null || (currentTime - lastBowAction) >= BOW_ACTION_THROTTLE)
						{
							int playersInRange = World.getInstance().getVisibleObjectsInRange(rank1, PlayerInstance.class, RANK_EFFECT_RANGE).size();
							if (playersInRange > 0)
							{
								LOGGER.info("RankManager: Sending bow action to " + playersInRange + " players around rank 1 player " + rank1.getName());
								Broadcast.toSelfAndKnownPlayersInRadius(rank1, new ExBowActionTo(rank1), RANK_EFFECT_RANGE);
								_lastBowActionTime.put(rank1.getObjectId(), currentTime);
							}
						}
					}
				}
			}
		}, 30000, 30000); // Increased from 15000 to 30000 (30 seconds)

		// Cleanup task for throttling maps to prevent memory leaks
		ThreadPool.scheduleAtFixedRate(() ->
		{
			long currentTime = System.currentTimeMillis();
			int bowActionSizeBefore = _lastBowActionTime.size();
			int rotationSizeBefore = _lastRotationTime.size();

			_lastBowActionTime.entrySet().removeIf(entry -> (currentTime - entry.getValue()) > (BOW_ACTION_THROTTLE * 2));
			_lastRotationTime.entrySet().removeIf(entry -> (currentTime - entry.getValue()) > (ROTATION_THROTTLE * 10));

			int bowActionCleaned = bowActionSizeBefore - _lastBowActionTime.size();
			int rotationCleaned = rotationSizeBefore - _lastRotationTime.size();

			if (bowActionCleaned > 0 || rotationCleaned > 0)
			{
				LOGGER.info("RankManager: Cleaned up " + bowActionCleaned + " bow action entries and " + rotationCleaned + " rotation entries");
			}
		}, 300000, 300000); // Cleanup every 5 minutes

		load();
	}
	
	public static RankManager getInstance()
	{
		return SingletonHolder.INSTANCE;
	}
	
	private void load()
	{
		long startTime = System.currentTimeMillis();
		try (Connection con = DatabaseFactory.getConnection())
		{
			try (PreparedStatement ps = con.prepareStatement(SELECT_RANKING_STATISTICS))
			{
				ResultSet rs = ps.executeQuery();
				final List<Integer> existed = new ArrayList<>();
				while (rs.next())
				{
					int charId = rs.getInt("charId");
					int time = rs.getInt("time");
					StatSet info = new StatSet();
					info.set("charId", charId);
					info.set("time", time);
					info.set("server_rank", rs.getInt("server_rank"));
					info.set("race_rank", rs.getInt("race_rank"));
					info.set("exp_earned", rs.getLong("exp_earned"));
					if (existed.contains(charId))
					{
						_rankingStatistics.get(charId).put(time, info);
					}
					else
					{
						Map<Integer, StatSet> map = new ConcurrentHashMap<>();
						map.put(time, info);
						_rankingStatistics.put(charId, map);
						existed.add(charId);
					}
				}
			}
			LOGGER.info("Ranking system loaded statictics at " + (System.currentTimeMillis() - startTime) + " ms.");
			try (PreparedStatement ps = con.prepareStatement(SELECT_REAL_RANKINGS))
			{
				ResultSet rs = ps.executeQuery();
				int i = 0;
				while (rs.next())
				{
					i++;
					StatSet player = new StatSet();
					int charId = rs.getInt("charId");
					int serverRank = rs.getInt("server_rank");
					int raceRank = rs.getInt("race_rank");
					player.set("charId", charId);
					player.set("server_rank", serverRank);
					player.set("race_rank", raceRank);
					_mainList.put(i, player);
					_levelRealRanks.put(charId, serverRank);
					_levelRealRaceRanks.put(charId, raceRank);
				}
			}
			LOGGER.info("Ranking system loaded real ranking at " + (System.currentTimeMillis() - startTime) + " ms.");
			try (PreparedStatement ps = con.prepareStatement(SELECT_REAL_PVP_RANKINGS))
			{
				ResultSet rs = ps.executeQuery();
				int i = 0;
				while (rs.next())
				{
					i++;
					StatSet player = new StatSet();
					int charId = rs.getInt("charId");
					int serverRank = rs.getInt("server_rank");
					int raceRank = rs.getInt("race_rank");
					player.set("charId", charId);
					player.set("server_rank", serverRank);
					player.set("race_rank", raceRank);
					_pvpList.put(i, player);
					_pvpRealRanks.put(charId, serverRank);
					_pvpRealRaceRanks.put(charId, raceRank);
				}
			}
			LOGGER.info("Ranking system loaded real PVP at " + (System.currentTimeMillis() - startTime) + " ms.");
			try (PreparedStatement ps = con.prepareStatement(SELECT_REAL_CLAN_RANKINGS))
			{
				ResultSet rs = ps.executeQuery();
				int i = 0;
				while (rs.next())
				{
					i++;
					StatSet clan = new StatSet();
					int clanId = rs.getInt("clanId");
					int rank = rs.getInt("rank");
					clan.set("clanId", clanId);
					clan.set("rank", rank);
					_clanList.put(i, clan);
					_clanRealRanks.put(clanId, rank);
				}
			}
			LOGGER.info("Ranking system loaded real CLAN at " + (System.currentTimeMillis() - startTime) + " ms.");
			try (PreparedStatement ps = con.prepareStatement(SELECT_REAL_PET_RANKINGS))
			{
				ResultSet rs = ps.executeQuery();
				int i = 0;
				while (rs.next())
				{
					i++;
					StatSet pet = new StatSet();
					int petControlObjectId = rs.getInt("pet_control_object_id");
					int serverRank = rs.getInt("server_rank");
					int raceRank = rs.getInt("race_rank");
					pet.set("pet_control_object_id", petControlObjectId);
					pet.set("server_rank", serverRank);
					pet.set("race_rank", raceRank);
					pet.set("ownerId", rs.getInt("ownerId"));
					pet.set("owner_name", rs.getString("char_name"));
					pet.set("owner_race", rs.getString("char_race"));
					pet.set("owner_level", rs.getInt("char_level"));
					pet.set("name", PetDataTable.getInstance().getRankingEncodedPetName(petControlObjectId));
					pet.set("level", rs.getInt("level"));
					pet.set("exp", rs.getLong("exp"));
					pet.set("evolve_level", rs.getInt("evolveLevel"));
					final int clanId = rs.getInt("clanid");
					if (clanId > 0)
					{
						try
						{
							pet.set("clanName", ClanTable.getInstance().getClan(clanId).getName());
						}
						catch (Exception e)
						{
							pet.set("clanName", "");
							LOGGER.warning("Ranking load: Invalid clan for: " + pet + " [" + petControlObjectId + "]");
						}
					}
					else
					{
						pet.set("clanName", "");
					}
					PetData petData = PetDataTable.getInstance().getPetDataByItemId(rs.getInt("item_id"));
					final int race = petData.getType();
					pet.set("race", race);
					pet.set("npcId", petData.getNpcId());
					_petList.put(i, pet);
					_petRealRanks.put(petControlObjectId, serverRank);
					_petRealRaceRanks.put(petControlObjectId, raceRank);
				}
			}
			LOGGER.info("Ranking system loaded real PET at " + (System.currentTimeMillis() - startTime) + " ms.");
			try (PreparedStatement ps = con.prepareStatement(SELECT_PREVIOUS_OLY_CYCLE))
			{
				try (ResultSet rs = ps.executeQuery())
				{
					int i = 0;
					while (rs.next())
					{
						i++;
						final StatSet player = new StatSet();
						final int charId = rs.getInt("rankings_oly_previous.charId");
						final int clanId = rs.getInt("characters.clanid");
						final Clan clan = ClanTable.getInstance().getClan(clanId);
						player.set("charId", charId);
						player.set("name", CharNameTable.getInstance().getNameById(charId));
						player.set("clanName", clan == null ? "" : clan.getName());
						player.set("level", rs.getInt("characters.level"));
						player.set("classId", rs.getInt("rankings_oly_previous.class_id"));
						player.set("clanLevel", clan == null ? 0 : clan.getLevel());
						player.set("competitions_won", rs.getInt("rankings_oly_previous.competitions_won"));
						player.set("competitions_lost", rs.getInt("rankings_oly_previous.competitions_lost"));
						player.set("olympiad_points", rs.getInt("rankings_oly_previous.olympiad_points"));
						if (Hero.getInstance().getCompleteHeroes().containsKey(charId))
						{
							final StatSet hero = Hero.getInstance().getCompleteHeroes().get(charId);
							player.set("count", hero.getInt("count", 0));
							player.set("legend_count", hero.getInt("legend_count", 0));
						}
						else
						{
							player.set("count", 0);
							player.set("legend_count", 0);
						}
						player.set("classRank", rs.getInt("rankings_oly_previous.class_rank"));
						_olyPreviousList.put(i, player);
					}
				}
			}
			LOGGER.info("Ranking system loaded previous OLYMPIAD CYCLE at " + (System.currentTimeMillis() - startTime) + " ms.");
		}
		catch (Exception e)
		{
			LOGGER.warning("Could not load ranking statistics");
			e.printStackTrace();
		}
		LOGGER.info("Ranking system loaded in " + (System.currentTimeMillis() - startTime) + " ms.");
		updateVisualList();
	}
	
	public void dailyUpdate()
	{
		updateVisualList();
		long overAllstartTime = System.currentTimeMillis();
		long startTime = System.currentTimeMillis();
		Calendar calendar = Calendar.getInstance();
		calendar.setTimeInMillis(System.currentTimeMillis());
		calendar.set(Calendar.MILLISECOND, 0);
		calendar.set(Calendar.SECOND, 0);
		calendar.set(Calendar.MINUTE, 0);
		calendar.set(Calendar.HOUR_OF_DAY, 0);
		int time = (int) (calendar.getTimeInMillis() / 1000);
		try (Connection con = DatabaseFactory.getConnection())
		{
			// Personal statistics
			final List<StatSet> updatedInfo = new ArrayList<>();
			try (PreparedStatement ps = con.prepareStatement(GET_NEW_RANKING_STATISTICS))
			{
				ResultSet rs = ps.executeQuery();
				while (rs.next())
				{
					int charId = rs.getInt("charId");
					long exp = rs.getLong("exp");
					Map<Integer, StatSet> playerData = _rankingStatistics.get(charId);
					if (playerData == null)
					{
						playerData = new ConcurrentHashMap<>();
					}
					StatSet lastInfo = null;
					StatSet newInfo = new StatSet();
					newInfo.set("charId", charId);
					newInfo.set("time", time);
					newInfo.set("server_rank", getPlayerVisualRank(charId));
					newInfo.set("race_rank", getPlayerVisualRaceRank(charId));
					if (playerData.size() > 0)
					{
						for (StatSet info : playerData.values())
						{
							exp -= info.getLong("exp_earned", 0);
							lastInfo = info;
						}
					}
					if ((lastInfo == null) || (lastInfo.getInt("time", 0) != time))
					{
						newInfo.set("exp_earned", exp);
						playerData.put(time, newInfo);
						_rankingStatistics.put(charId, playerData);
						updatedInfo.add(newInfo);
					}
				}
			}
			LOGGER.info("Ranking system dailyUpdate: updated ranking statistics in " + (System.currentTimeMillis() - startTime) + " ms.");
			startTime = System.currentTimeMillis();
			try (PreparedStatement ps = con.prepareStatement(INSERT_NEW_RANKING_STATISTICS))
			{
				for (StatSet info : updatedInfo)
				{
					ps.setInt(1, info.getInt("charId"));
					ps.setInt(2, info.getInt("time"));
					ps.setInt(3, info.getInt("server_rank"));
					ps.setInt(4, info.getInt("race_rank"));
					ps.setLong(5, info.getLong("exp_earned"));
					ps.addBatch();
				}
				ps.executeBatch();
			}
			LOGGER.info("Ranking system dailyUpdate: inserted ranking statistics in " + (System.currentTimeMillis() - startTime) + " ms.");
			startTime = System.currentTimeMillis();
			// Real ranks
			_mainList.clear();
			for (Entry<Integer, StatSet> entry : _currentVisualList.entrySet())
			{
				StatSet player = entry.getValue();
				StatSet newInfo = new StatSet();
				int charId = player.getInt("charId");
				int serverRank = entry.getKey();
				int raceRank = player.getInt("raceRank");
				newInfo.set("charId", charId);
				newInfo.set("server_rank", serverRank);
				newInfo.set("race_rank", raceRank);
				_mainList.put(entry.getKey(), newInfo);
				_levelRealRanks.put(charId, serverRank);
				_levelRealRaceRanks.put(charId, raceRank);
			}
			LOGGER.info("Ranking system dailyUpdate: updated _mainList in " + (System.currentTimeMillis() - startTime) + " ms.");
			startTime = System.currentTimeMillis();
			try (PreparedStatement ps = con.prepareStatement(DELETE_REAL_RANKINGS))
			{
				ps.execute();
			}
			LOGGER.info("Ranking system dailyUpdate: deleted real ranking in " + (System.currentTimeMillis() - startTime) + " ms.");
			startTime = System.currentTimeMillis();
			try (PreparedStatement ps = con.prepareStatement(INSERT_REAL_RANKINGS))
			{
				for (StatSet player : _mainList.values())
				{
					ps.setInt(1, player.getInt("charId"));
					ps.setInt(2, player.getInt("server_rank"));
					ps.setInt(3, player.getInt("race_rank"));
					ps.addBatch();
				}
				ps.executeBatch();
			}
			LOGGER.info("Ranking system dailyUpdate: inserted real ranking in " + (System.currentTimeMillis() - startTime) + " ms.");
			startTime = System.currentTimeMillis();
			// Pvp ranks
			_pvpList.clear();
			for (Entry<Integer, StatSet> entry : _pvpVisualList.entrySet())
			{
				StatSet player = entry.getValue();
				StatSet newInfo = new StatSet();
				int charId = player.getInt("charId");
				int serverRank = entry.getKey();
				int raceRank = player.getInt("raceRank");
				newInfo.set("charId", charId);
				newInfo.set("server_rank", serverRank);
				newInfo.set("race_rank", raceRank);
				_pvpList.put(entry.getKey(), newInfo);
				_pvpRealRanks.put(charId, serverRank);
				_pvpRealRaceRanks.put(charId, raceRank);
			}
			LOGGER.info("Ranking system dailyUpdate: updated _pvpList in " + (System.currentTimeMillis() - startTime) + " ms.");
			startTime = System.currentTimeMillis();
			try (PreparedStatement ps = con.prepareStatement(DELETE_REAL_PVP_RANKINGS))
			{
				ps.execute();
			}
			LOGGER.info("Ranking system dailyUpdate: deleted real PVP in " + (System.currentTimeMillis() - startTime) + " ms.");
			startTime = System.currentTimeMillis();
			try (PreparedStatement ps = con.prepareStatement(INSERT_REAL_PVP_RANKINGS))
			{
				for (StatSet player : _pvpList.values())
				{
					ps.setInt(1, player.getInt("charId"));
					ps.setInt(2, player.getInt("server_rank"));
					ps.setInt(3, player.getInt("race_rank"));
					ps.addBatch();
				}
				ps.executeBatch();
			}
			LOGGER.info("Ranking system dailyUpdate: inserted real PVP in " + (System.currentTimeMillis() - startTime) + " ms.");
			startTime = System.currentTimeMillis();
			// Clan ranks
			_clanList.clear();
			for (Entry<Integer, StatSet> entry : _clanVisualList.entrySet())
			{
				StatSet clan = entry.getValue();
				StatSet newInfo = new StatSet();
				int clanId = clan.getInt("clanId");
				int serverRank = entry.getKey();
				newInfo.set("clanId", clanId);
				newInfo.set("rank", serverRank);
				_clanList.put(entry.getKey(), newInfo);
				_clanRealRanks.put(clanId, serverRank);
			}
			LOGGER.info("Ranking system dailyUpdate: updated _clanList in " + (System.currentTimeMillis() - startTime) + " ms.");
			startTime = System.currentTimeMillis();
			try (PreparedStatement ps = con.prepareStatement(DELETE_REAL_CLAN_RANKINGS))
			{
				ps.execute();
			}
			LOGGER.info("Ranking system dailyUpdate: deleted real CLAN in " + (System.currentTimeMillis() - startTime) + " ms.");
			startTime = System.currentTimeMillis();
			try (PreparedStatement ps = con.prepareStatement(INSERT_REAL_CLAN_RANKINGS))
			{
				for (StatSet clan : _clanList.values())
				{
					ps.setInt(1, clan.getInt("clanId"));
					ps.setInt(2, clan.getInt("rank"));
					ps.addBatch();
				}
				ps.executeBatch();
			}
			LOGGER.info("Ranking system dailyUpdate: inserted real CLAN in " + (System.currentTimeMillis() - startTime) + " ms.");
			startTime = System.currentTimeMillis();
			// Pets ranks
			_petList.clear();
			for (Entry<Integer, StatSet> entry : _petVisualList.entrySet())
			{
				StatSet pet = entry.getValue();
				StatSet newInfo = new StatSet();
				int petControlObjectId = pet.getInt("pet_control_object_id");
				int serverRank = entry.getKey();
				int raceRank = pet.getInt("raceRank");
				newInfo.set("pet_control_object_id", petControlObjectId);
				newInfo.set("ownerId", pet.getInt("ownerId"));
				newInfo.set("owner_name", pet.getString("owner_name"));
				newInfo.set("owner_race", pet.getString("owner_race"));
				newInfo.set("owner_level", pet.getInt("owner_level"));
				newInfo.set("server_rank", serverRank);
				newInfo.set("race_rank", raceRank);
				newInfo.set("race", pet.getInt("race"));
				newInfo.set("name", pet.getString("name"));
				newInfo.set("level", pet.getInt("level"));
				newInfo.set("exp", pet.getLong("exp"));
				newInfo.set("evolve_level", pet.getInt("evolve_level"));
				newInfo.set("clanName", pet.getString("clanName"));
				_petList.put(entry.getKey(), newInfo);
				_petRealRanks.put(petControlObjectId, serverRank);
				_petRealRaceRanks.put(petControlObjectId, raceRank);
			}
			LOGGER.info("Ranking system dailyUpdate: updated _petList in " + (System.currentTimeMillis() - startTime) + " ms.");
			startTime = System.currentTimeMillis();
			try (PreparedStatement ps = con.prepareStatement(DELETE_REAL_PET_RANKINGS))
			{
				ps.execute();
			}
			LOGGER.info("Ranking system dailyUpdate: deleted real PET in " + (System.currentTimeMillis() - startTime) + " ms.");
			startTime = System.currentTimeMillis();
			try (PreparedStatement ps = con.prepareStatement(INSERT_REAL_PET_RANKINGS))
			{
				for (StatSet pet : _petList.values())
				{
					ps.setInt(1, pet.getInt("pet_control_object_id"));
					ps.setInt(2, pet.getInt("server_rank"));
					ps.setInt(3, pet.getInt("race_rank"));
					ps.addBatch();
				}
				ps.executeBatch();
			}
			LOGGER.info("Ranking system dailyUpdate: inserted real PET in " + (System.currentTimeMillis() - startTime) + " ms.");
			startTime = System.currentTimeMillis();
		}
		catch (Exception e)
		{
			LOGGER.warning("Failed to update ranking statistics.");
			e.printStackTrace();
		}
		for (PlayerInstance player : World.getInstance().getPlayers())
		{
			applyEffects(player);
		}
		LOGGER.info("Ranking system updated in " + (System.currentTimeMillis() - overAllstartTime) + " ms.");
	}
	
	public synchronized void updateVisualList()
	{
		long startTime = System.currentTimeMillis();
		try (Connection con = DatabaseFactory.getConnection())
		{
			// Load charIds All
			_currentVisualList.clear();
			try (PreparedStatement statement = con.prepareStatement(SELECT_CHARACTERS))
			{
				final Map<Integer, List<Integer>> raceRanks = new HashMap<>();
				final ResultSet rset = statement.executeQuery();
				int i = 1;
				while (rset.next())
				{
					final StatSet player = new StatSet();
					final int charId = rset.getInt("charId");
					final String charName = rset.getString("char_name");
					player.set("charId", charId);
					player.set("name", charName);
					player.set("level", rset.getInt("level"));
					player.set("classId", rset.getInt("base_class"));
					final int race = rset.getInt("race");
					player.set("race", race);
					if (raceRanks.get(race) == null)
					{
						raceRanks.put(race, new ArrayList<Integer>());
					}
					raceRanks.get(race).add(charId);
					final int clanId = rset.getInt("clanid");
					if (clanId > 0)
					{
						try
						{
							player.set("clanName", ClanTable.getInstance().getClan(clanId).getName());
						}
						catch (Exception e)
						{
							player.set("clanName", "");
							LOGGER.warning("Ranking update: Invalid clan for: " + charName + " [" + charId + "]");
						}
					}
					else
					{
						player.set("clanName", "");
					}
					_currentVisualList.put(i, player);
					_levelVisualRanks.put(charId, i);
					i++;
				}
				for (Entry<Integer, StatSet> entry : _currentVisualList.entrySet())
				{
					StatSet player = entry.getValue();
					int charId = player.getInt("charId");
					int race = player.getInt("race");
					int raceRank = raceRanks.get(race).indexOf(charId) + 1;
					_currentVisualList.get(entry.getKey()).set("raceRank", raceRank);
					_levelVisualRaceRanks.put(charId, raceRank);
				}
			}
			catch (Exception e)
			{
				LOGGER.log(Level.WARNING, "Could not load chars total rank data: " + this + " - " + e.getMessage(), e);
			}
			finally
			{
				LOGGER.info(getClass().getSimpleName() + ": Updated visual ranking list.");
			}
			// load olympiad data.
			_olyList.clear();
			try (PreparedStatement statement = con.prepareStatement(GET_CURRENT_OLY_CYCLE_DATA))
			{
				final Map<Integer, List<Integer>> classRanks = new HashMap<>();
				final ResultSet rset = statement.executeQuery();
				int i = 1;
				while (rset.next())
				{
					final StatSet player = new StatSet();
					final int charId = rset.getInt("charId");
					final String charName = rset.getString("char_name");
					player.set("charId", charId);
					player.set("name", charName);
					final int clanId = rset.getInt("clanid");
					if (clanId > 0)
					{
						player.set("clanName", ClanTable.getInstance().getClan(clanId).getName());
					}
					else
					{
						player.set("clanName", "");
					}
					player.set("level", rset.getInt("level"));
					int classId = rset.getInt("base_class");
					switch (classId)
					{
						case 199:
						case 203:
						case 207:
						{
							classId = 199;
						}
					}
					player.set("classId", classId);
					if (clanId > 0)
					{
						try
						{
							player.set("clanLevel", ClanTable.getInstance().getClan(clanId).getLevel());
						}
						catch (Exception e)
						{
							player.set("clanLevel", 0);
							LOGGER.warning("Ranking update: Invalid clan for: " + charName + " [" + charId + "]");
						}
					}
					else
					{
						player.set("clanLevel", 0);
					}
					player.set("competitions_won", rset.getInt("competitions_won"));
					player.set("competitions_lost", rset.getInt("competitions_lost"));
					player.set("olympiad_points", rset.getInt("olympiad_points"));
					if (Hero.getInstance().getCompleteHeroes().containsKey(charId))
					{
						final StatSet hero = Hero.getInstance().getCompleteHeroes().get(charId);
						player.set("count", hero.getInt("count", 0));
						player.set("legend_count", hero.getInt("legend_count", 0));
					}
					else
					{
						player.set("count", 0);
						player.set("legend_count", 0);
					}
					if (classRanks.get(classId) == null)
					{
						classRanks.put(classId, new ArrayList<Integer>());
					}
					classRanks.get(classId).add(charId);
					_olyList.put(i, player);
					i++;
				}
				for (Entry<Integer, StatSet> entry : _olyList.entrySet())
				{
					StatSet player = entry.getValue();
					int charId = player.getInt("charId");
					int classId = player.getInt("classId");
					int classRank = classRanks.get(classId).indexOf(charId) + 1;
					_olyList.get(entry.getKey()).set("classRank", classRank);
				}
			}
			catch (Exception e)
			{
				LOGGER.log(Level.WARNING, "Could not load olympiad total rank data: " + this + " - " + e.getMessage(), e);
			}
			finally
			{
				LOGGER.info(getClass().getSimpleName() + ": Updated visual olympiad ranking list.");
			}
			// Load pvp
			_pvpVisualList.clear();
			try (PreparedStatement statement = con.prepareStatement(SELECT_PVP_CHARACTERS))
			{
				final Map<Integer, List<Integer>> raceRanks = new HashMap<>();
				final ResultSet rset = statement.executeQuery();
				int i = 1;
				while (rset.next())
				{
					final StatSet player = new StatSet();
					final int charId = rset.getInt("charId");
					final String charName = rset.getString("char_name");
					player.set("charId", charId);
					player.set("name", charName);
					player.set("level", rset.getInt("level"));
					player.set("pvpkills", rset.getInt("pvpkills"));
					player.set("pvpdeaths", rset.getInt("pvpdeaths"));
					player.set("classId", rset.getInt("base_class"));
					final int race = rset.getInt("race");
					player.set("race", race);
					if (raceRanks.get(race) == null)
					{
						raceRanks.put(race, new ArrayList<Integer>());
					}
					raceRanks.get(race).add(charId);
					final int clanId = rset.getInt("clanid");
					if (clanId > 0)
					{
						try
						{
							player.set("clanName", ClanTable.getInstance().getClan(clanId).getName());
						}
						catch (Exception e)
						{
							player.set("clanName", "");
							LOGGER.warning("Ranking update: Invalid clan for: " + charName + " [" + charId + "]");
						}
					}
					else
					{
						player.set("clanName", "");
					}
					_pvpVisualList.put(i, player);
					_pvpVisualRanks.put(charId, i);
					i++;
				}
				for (Entry<Integer, StatSet> entry : _pvpVisualList.entrySet())
				{
					StatSet player = entry.getValue();
					int charId = player.getInt("charId");
					int race = player.getInt("race");
					int raceRank = raceRanks.get(race).indexOf(charId) + 1;
					_pvpVisualList.get(entry.getKey()).set("raceRank", raceRank);
				}
			}
			catch (Exception e)
			{
				LOGGER.log(Level.WARNING, "Could not load chars pvp ranking: " + this + " - " + e.getMessage(), e);
			}
			finally
			{
				LOGGER.info(getClass().getSimpleName() + ": Updated visual Pvp ranking list.");
			}
			// Load clan
			_clanVisualList.clear();
			try (PreparedStatement statement = con.prepareStatement(SELECT_CLANS))
			{
				final ResultSet rset = statement.executeQuery();
				int i = 1;
				while (rset.next())
				{
					final StatSet clan = new StatSet();
					final int clanId = rset.getInt("clan_id");
					final String clanName = rset.getString("clan_name");
					clan.set("clanId", clanId);
					clan.set("name", clanName);
					clan.set("level", rset.getInt("clan_level"));
					clan.set("exp", rset.getInt("clan_exp"));
					int leaderId = rset.getInt("leader_id");
					clan.set("leaderName", CharNameTable.getInstance().getNameById(leaderId));
					clan.set("leaderLevel", rset.getInt("level"));
					clan.set("memberCount", rset.getInt("member_count"));
					_clanVisualList.put(i, clan);
					_clanVisualRanks.put(clanId, i);
					i++;
				}
			}
			catch (Exception e)
			{
				LOGGER.log(Level.WARNING, "Could not load clan ranking: " + this + " - " + e.getMessage(), e);
			}
			finally
			{
				LOGGER.info(getClass().getSimpleName() + ": Updated visual Clan ranking list.");
			}
			// Load pets
			_petVisualList.clear();
			try (PreparedStatement statement = con.prepareStatement(SELECT_PETS))
			{
				final Map<Integer, List<Integer>> raceRanks = new HashMap<>();
				final ResultSet rset = statement.executeQuery();
				int i = 1;
				while (rset.next())
				{
					final StatSet pet = new StatSet();
					final int petControlObjectId = rset.getInt("item_obj_id");
					pet.set("pet_control_object_id", petControlObjectId);
					pet.set("name", PetDataTable.getInstance().getRankingEncodedPetName(petControlObjectId));
					pet.set("ownerId", rset.getInt("ownerId"));
					pet.set("owner_name", rset.getString("char_name"));
					pet.set("owner_race", rset.getString("char_race"));
					pet.set("owner_level", rset.getInt("char_level"));
					pet.set("level", rset.getInt("level"));
					pet.set("evolve_level", rset.getInt("evolveLevel"));
					pet.set("exp", rset.getLong("exp"));
					PetData petData = PetDataTable.getInstance().getPetDataByItemId(rset.getInt("item_id"));
					final int race = petData.getType();
					pet.set("race", race);
					pet.set("npcId", petData.getNpcId());
					if (raceRanks.get(race) == null)
					{
						raceRanks.put(race, new ArrayList<Integer>());
					}
					raceRanks.get(race).add(petControlObjectId);
					final int clanId = rset.getInt("clanid");
					if (clanId > 0)
					{
						try
						{
							pet.set("clanName", ClanTable.getInstance().getClan(clanId).getName());
						}
						catch (Exception e)
						{
							pet.set("clanName", "");
							LOGGER.warning("Ranking update: Invalid clan for: " + pet + " [" + petControlObjectId + "]");
						}
					}
					else
					{
						pet.set("clanName", "");
					}
					_petVisualList.put(i, pet);
					_petVisualRanks.put(petControlObjectId, i);
					i++;
				}
				for (Entry<Integer, StatSet> entry : _petVisualList.entrySet())
				{
					StatSet pet = entry.getValue();
					int petControlObjectId = pet.getInt("pet_control_object_id");
					int race = pet.getInt("race");
					int raceRank = raceRanks.get(race).indexOf(petControlObjectId) + 1;
					_petVisualList.get(entry.getKey()).set("raceRank", raceRank);
				}
			}
			catch (Exception e)
			{
				LOGGER.log(Level.WARNING, "Could not load pet ranking: " + this + " - " + e.getMessage(), e);
			}
			finally
			{
				LOGGER.info(getClass().getSimpleName() + ": Updated visual Pet ranking list.");
			}
		}
		catch (Exception e)
		{
			LOGGER.log(Level.WARNING, getClass().getSimpleName() + ": Could not update visual list." + " - " + e.getMessage(), e);
		}
		finally
		{
			LOGGER.info(getClass().getSimpleName() + ": Updated visual list in " + (System.currentTimeMillis() - startTime) + " ms.");
		}
	}
	
	public void onOlympiadEnd()
	{
		_olyPreviousList.clear();
		try (Connection con = DatabaseFactory.getConnection())
		{
			try (PreparedStatement statement = con.prepareStatement(GET_CURRENT_OLY_CYCLE_DATA))
			{
				final Map<Integer, List<Integer>> classRanks = new HashMap<>();
				final ResultSet rset = statement.executeQuery();
				int i = 1;
				while (rset.next())
				{
					final StatSet player = new StatSet();
					final int charId = rset.getInt("charId");
					final String charName = rset.getString("char_name");
					player.set("charId", charId);
					player.set("name", charName);
					final int clanId = rset.getInt("clanid");
					if (clanId > 0)
					{
						player.set("clanName", ClanTable.getInstance().getClan(clanId).getName());
					}
					else
					{
						player.set("clanName", "");
					}
					player.set("level", rset.getInt("level"));
					int classId = rset.getInt("base_class");
					switch (classId)
					{
						case 199:
						case 203:
						case 207:
						{
							classId = 199;
						}
					}
					player.set("classId", classId);
					if (clanId > 0)
					{
						try
						{
							player.set("clanLevel", ClanTable.getInstance().getClan(clanId).getLevel());
						}
						catch (Exception e)
						{
							player.set("clanLevel", 0);
							LOGGER.warning("Ranking update: Invalid clan for: " + charName + " [" + charId + "]");
						}
					}
					else
					{
						player.set("clanLevel", 0);
					}
					player.set("competitions_won", rset.getInt("competitions_won"));
					player.set("competitions_lost", rset.getInt("competitions_lost"));
					player.set("olympiad_points", rset.getInt("olympiad_points"));
					if (Hero.getInstance().getCompleteHeroes().containsKey(charId))
					{
						final StatSet hero = Hero.getInstance().getCompleteHeroes().get(charId);
						player.set("count", hero.getInt("count", 0));
						player.set("legend_count", hero.getInt("legend_count", 0));
					}
					else
					{
						player.set("count", 0);
						player.set("legend_count", 0);
					}
					if (classRanks.get(classId) == null)
					{
						classRanks.put(classId, new ArrayList<Integer>());
					}
					classRanks.get(classId).add(charId);
					_olyPreviousList.put(i, player);
					i++;
				}
				for (Entry<Integer, StatSet> entry : _olyPreviousList.entrySet())
				{
					StatSet player = entry.getValue();
					int charId = player.getInt("charId");
					int classId = player.getInt("classId");
					int classRank = classRanks.get(classId).indexOf(charId) + 1;
					_olyPreviousList.get(entry.getKey()).set("classRank", classRank);
				}
			}
			try (PreparedStatement ps = con.prepareStatement(DELETE_PREVIOUS_OLY_CYCLE))
			{
				ps.execute();
			}
			try (PreparedStatement ps = con.prepareStatement(INSERT_PREVIOUS_OLY_CYCLE))
			{
				for (Entry<Integer, StatSet> entry : _olyPreviousList.entrySet())
				{
					final StatSet player = entry.getValue();
					ps.setInt(1, player.getInt("charId"));
					ps.setInt(2, entry.getKey());
					ps.setInt(3, player.getInt("classId"));
					ps.setInt(4, player.getInt("classRank"));
					ps.setInt(5, player.getInt("competitions_won"));
					ps.setInt(6, player.getInt("competitions_lost"));
					ps.setInt(7, player.getInt("olympiad_points"));
					ps.addBatch();
				}
				ps.executeBatch();
			}
		}
		catch (Exception e)
		{
			LOGGER.log(Level.WARNING, "Could not load olympiad total rank data: " + this + " - " + e.getMessage(), e);
		}
		finally
		{
			LOGGER.info(getClass().getSimpleName() + ": Updated visual olympiad ranking list.");
		}
	}
	
	public List<PlayerInstance> getTop50OnlinePlayers()
	{
		List<PlayerInstance> top50 = new ArrayList<>();
		for (int i = 1; i <= 50; i++)
		{
			if (_mainList.size() < i)
			{
				break;
			}
			int charId = _mainList.get(i).getInt("charId");
			PlayerInstance player = World.getInstance().getPlayer(charId);
			if ((player != null) && player.isOnline())
			{
				top50.add(player);
			}
		}
		return top50;
	}
	
	public Map<Integer, StatSet> getMainList()
	{
		return _mainList;
	}
	
	public Map<Integer, StatSet> getCurrentVisualList()
	{
		return _currentVisualList;
	}
	
	public Map<Integer, StatSet> getOlyRankList()
	{
		return _olyList;
	}
	
	public Map<Integer, StatSet> getOlyPreviousRankList()
	{
		return _olyPreviousList;
	}
	
	public int getPlayerRealRank(int objectId)
	{
		return _levelRealRanks.getOrDefault(objectId, 0);
	}

	/**
	 * Clear throttling data for a player when they logout
	 * @param objectId the player's object ID
	 */
	public void clearPlayerThrottling(int objectId)
	{
		boolean hadBowAction = _lastBowActionTime.remove(objectId) != null;
		boolean hadRotation = _lastRotationTime.remove(objectId) != null;
		if (hadBowAction || hadRotation)
		{
			LOGGER.info("RankManager: Cleared throttling data for player " + objectId + " (bow: " + hadBowAction + ", rotation: " + hadRotation + ")");
		}
	}
	
	public int getPlayerRealRaceRank(int objectId)
	{
		return _levelRealRaceRanks.getOrDefault(objectId, 0);
	}
	
	public int getPlayerVisualRank(int objectId)
	{
		return _levelVisualRanks.getOrDefault(objectId, 0);
	}
	
	public int getPlayerVisualRaceRank(int objectId)
	{
		return _levelVisualRaceRanks.getOrDefault(objectId, 0);
	}
	
	public Map<Integer, StatSet> getRankingStatistics(int objectId)
	{
		return _rankingStatistics.get(objectId);
	}
	
	public Map<Integer, StatSet> getPvpVisualList()
	{
		return _pvpVisualList;
	}
	
	public int getPlayerRealPvpRank(int objectId)
	{
		return _pvpRealRanks.getOrDefault(objectId, 0);
	}
	
	public int getPlayerRealRacePvpRank(int objectId)
	{
		return _pvpRealRaceRanks.getOrDefault(objectId, 0);
	}
	
	public int getPlayerVisualPvpRank(int objectId)
	{
		return _pvpVisualRanks.getOrDefault(objectId, 0);
	}
	
	public Map<Integer, StatSet> getClanVisualList()
	{
		return _clanVisualList;
	}
	
	public int getClanRealRank(int clanId)
	{
		return _clanRealRanks.getOrDefault(clanId, 0);
	}
	
	public int getClanVisualRank(int clanId)
	{
		return _clanVisualRanks.getOrDefault(clanId, 0);
	}
	
	public Map<Integer, StatSet> getPetRankList()
	{
		return _petList;
	}
	
	public Map<Integer, StatSet> getPetVisualList()
	{
		return _petVisualList;
	}
	
	public int getPetRealRank(int objectId)
	{
		return _petRealRanks.getOrDefault(objectId, 0);
	}
	
	public int getPetRealRaceRank(int objectId)
	{
		return _petRealRaceRanks.getOrDefault(objectId, 0);
	}
	
	public int getPetVisualRank(int objectId)
	{
		return _petVisualRanks.getOrDefault(objectId, 0);
	}
	
	public void applyEffects(PlayerInstance player)
	{
		// Remove existing effects and skills.
		player.getEffectList().stopSkillEffects(true, PVP_RANK_1_3);
		player.getEffectList().stopSkillEffects(true, SERVER_RANK_1);
		player.getEffectList().stopSkillEffects(true, SERVER_RANK_2_30);
		player.getEffectList().stopSkillEffects(true, SERVER_RANK_31_100);
		player.getEffectList().stopSkillEffects(true, HUMAN_RACE_RANK_1);
		player.getEffectList().stopSkillEffects(true, ELF_RACE_RANK_1);
		player.getEffectList().stopSkillEffects(true, DARK_ELF_RACE_RANK_1);
		player.getEffectList().stopSkillEffects(true, ORC_RACE_RANK_1);
		player.getEffectList().stopSkillEffects(true, DWARF_RACE_RANK_1);
		player.getEffectList().stopSkillEffects(true, KAMAEL_RACE_RANK_1);
		player.getEffectList().stopSkillEffects(true, SYLPH_RACE_RANK_1);
		player.getEffectList().stopSkillEffects(true, HUMAN_LEVEL_TRANSFORM_CLASS);
		player.getEffectList().stopSkillEffects(true, ELF_LEVEL_TRANSFORM_CLASS);
		player.getEffectList().stopSkillEffects(true, DARK_ELF_LEVEL_TRANSFORM_CLASS);
		player.getEffectList().stopSkillEffects(true, ORC_LEVEL_TRANSFORM_CLASS);
		player.getEffectList().stopSkillEffects(true, DWARF_LEVEL_TRANSFORM_CLASS);
		player.getEffectList().stopSkillEffects(true, KAMAEL_LEVEL_TRANSFORM_CLASS);
		player.getEffectList().stopSkillEffects(true, SYLPH_LEVEL_TRANSFORM_CLASS);
		player.removeSkill(SERVER_RANK_1_BONUS);
		player.removeSkill(SERVER_RANK_2_BONUS);
		player.removeSkill(SERVER_RANK_3_BONUS);
		player.removeSkill(HUMAN_LEVEL_TRANSFORM_CLASS);
		player.removeSkill(ELF_LEVEL_TRANSFORM_CLASS);
		player.removeSkill(DARK_ELF_LEVEL_TRANSFORM_CLASS);
		player.removeSkill(ORC_LEVEL_TRANSFORM_CLASS);
		player.removeSkill(DWARF_LEVEL_TRANSFORM_CLASS);
		player.removeSkill(KAMAEL_LEVEL_TRANSFORM_CLASS);
		player.removeSkill(SYLPH_LEVEL_TRANSFORM_CLASS);
		// player.removeSkill(DEATH_KNIGHT_LEVEL_TRANSFORM_CLASS);
		// Add pvp rank skills.
		int pvpRank = getPlayerRealPvpRank(player.getObjectId());
		if ((pvpRank >= 1) && (pvpRank <= 3))
		{
			PVP_RANK_1_3.applyEffects(player, player);
		}
		// Add global rank skills.
		int rank = getPlayerRealRank(player.getObjectId());
		if (rank > 0)
		{
			if (rank <= 1)
			{
				player.addSkill(SERVER_RANK_1_BONUS, false);
				player.addSkill(SERVER_RANK_2_BONUS, false);
				player.addSkill(SERVER_RANK_3_BONUS, false);
			}
			else if (rank <= 2)
			{
				player.addSkill(SERVER_RANK_2_BONUS, false);
				player.addSkill(SERVER_RANK_3_BONUS, false);
			}
			else if (rank <= 3)
			{
				player.addSkill(SERVER_RANK_3_BONUS, false);
			}
			if (rank <= 1)
			{
				SERVER_RANK_1.applyEffects(player, player);
			}
			else if (rank <= 30)
			{
				SERVER_RANK_2_30.applyEffects(player, player);
			}
			else if (rank <= 100)
			{
				SERVER_RANK_31_100.applyEffects(player, player);
			}
		}
		// Apply race rank effects.
		final int raceRank = getPlayerRealRaceRank(player.getObjectId());
		if (raceRank == 1)
		{
			switch (player.getRace())
			{
				case HUMAN -> HUMAN_RACE_RANK_1.applyEffects(player, player);
				case ELF -> ELF_RACE_RANK_1.applyEffects(player, player);
				case DARK_ELF -> DARK_ELF_RACE_RANK_1.applyEffects(player, player);
				case ORC -> ORC_RACE_RANK_1.applyEffects(player, player);
				case DWARF -> DWARF_RACE_RANK_1.applyEffects(player, player);
				case KAMAEL -> KAMAEL_RACE_RANK_1.applyEffects(player, player);
				case SYLPH -> SYLPH_RACE_RANK_1.applyEffects(player, player);
			}
		}
		// Add race rank transform skills.
		final int raceTransform = getPlayerRealRaceRank(player.getObjectId());
		if ((raceTransform > 0) && (raceTransform <= 3))
		{
			switch (player.getRace())
			{
				case HUMAN -> player.addSkill(HUMAN_LEVEL_TRANSFORM_CLASS, false);
				case ELF -> player.addSkill(ELF_LEVEL_TRANSFORM_CLASS, false);
				case DARK_ELF -> player.addSkill(DARK_ELF_LEVEL_TRANSFORM_CLASS, false);
				case ORC -> player.addSkill(ORC_LEVEL_TRANSFORM_CLASS, false);
				case DWARF -> player.addSkill(DWARF_LEVEL_TRANSFORM_CLASS, false);
				case KAMAEL -> player.addSkill(KAMAEL_LEVEL_TRANSFORM_CLASS, false);
				case SYLPH -> player.addSkill(SYLPH_LEVEL_TRANSFORM_CLASS, false);
			}
		}
		if ((player._loginTime + 5000) < System.currentTimeMillis())
		{
			player.broadcastUserInfo();
		}
	}
	
	public void spawnBuffzoneNpc(PlayerInstance player)
	{
		if (getPlayerRealRank(player.getObjectId()) != 1)
		{
			return;
		}
		if (!player.isInsideZone(ZoneId.PEACE))
		{
			player.sendPacket(SystemMessageId.YOU_CANNOT_USE_LEADER_POWER_HERE);
			return;
		}
		if ((GlobalVariables.getInstance().getLong(GlobalVariables.LEADER_POWER_SPAWN_TIMESTAMP, 0L) + BUFFZONE_COOLDOWN) > System.currentTimeMillis())
		{
			player.sendPacket(SystemMessageId.LEADER_POWER_COOLDOWN);
			return;
		}
		if (!player.reduceAdena("SpawnRank1BuffzoneNpc", 20_000_000, player, true))
		{
			return;
		}
		GlobalVariables.getInstance().set(GlobalVariables.LEADER_POWER_SPAWN_TIMESTAMP, System.currentTimeMillis());
		final BuffzoneNpcInstance buffzone = new BuffzoneNpcInstance(player);
		buffzone.setCurrentHp(buffzone.getMaxHp());
		buffzone.setCurrentMp(buffzone.getMaxMp());
		buffzone.setHeading(player.getHeading());
		buffzone.setInstance(player.getInstanceWorld());
		buffzone.setSummoner(player);
		buffzone.setHeading(player.getHeading());
		buffzone.spawnMe(player.getX(), player.getY(), player.getZ());
		player.sendPacket(new ExRankingCharBuffzoneNpcInfo());
		player.sendPacket(new ExRankingCharBuffzoneNpcPosition());
		// TODO: 2nd string is location
		Broadcast.toAllOnlinePlayers(new SystemMessage(SystemMessageId.A_RANKING_LEADER_C1_USED_LEADER_POWER_IN_S2).addString(player.getName()).addString("the world"));
	}
	
	public Location getBuffzoneNpcLocation()
	{
		return _buffzoneNpcLocation;
	}
	
	public void setBuffzoneNpcLocation(Location loc)
	{
		_buffzoneNpcLocation = loc;
	}
	
	private static class SingletonHolder
	{
		protected static final RankManager INSTANCE = new RankManager();
	}
}