package club.projectessence.gameserver.instancemanager;

import java.time.Instant;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.logging.Logger;
import java.util.stream.Collectors;

import club.projectessence.Config;
import club.projectessence.gameserver.instancemanager.AntiFeedManager;
import club.projectessence.gameserver.instancemanager.ZoneDominanceManager;
import club.projectessence.gameserver.enums.Faction;
import club.projectessence.gameserver.model.Party;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.model.clan.Clan;
import club.projectessence.gameserver.model.itemcontainer.Inventory;
import club.projectessence.gameserver.model.variables.PlayerVariables;
import club.projectessence.gameserver.model.zone.ZoneId;
import club.projectessence.gameserver.util.Util;
import club.projectessence.gameserver.features.museum.MuseumManager;

public class GveRewardManager
{
	private static final Logger													LOGGER				= Logger.getLogger(GveRewardManager.class.getName());
	private static final GveRewardManager										INSTANCE			= new GveRewardManager();
	private final Map<Integer, Map<Integer, Map.Entry<AtomicInteger, Instant>>>	killPenalties		= new ConcurrentHashMap<>();
	private final Map<Integer, Map<Integer, Instant>>							killPenaltiesTime	= new ConcurrentHashMap<>();
	private final Map<Integer, Map.Entry<AtomicInteger, Instant>>				deathPenalties		= new ConcurrentHashMap<>();
	private final Map<Integer, Instant>											deathPenaltiesTime	= new ConcurrentHashMap<>();
	private final Map<Integer, Map<Integer, Map.Entry<AtomicInteger, Instant>>>	seriesPenalties		= new ConcurrentHashMap<>();
	private final Map<Integer, Map<Integer, Instant>>							seriesPenaltiesTime	= new ConcurrentHashMap<>();
	private final CopyOnWriteArrayList<Integer>									revivePenalties		= new CopyOnWriteArrayList<>();
	
	public static GveRewardManager getInstance()
	{
		return INSTANCE;
	}
	
	public void tryGiveReward(PlayerInstance killer, PlayerInstance assistant, List<PlayerInstance> debuffers, PlayerInstance victim)
	{
		tryGiveReward(killer, assistant, debuffers, victim, 1.0);
	}
	
	public void tryGiveReward(PlayerInstance killer, PlayerInstance assistant, List<PlayerInstance> debuffers, PlayerInstance victim, double rewardMod)
	{
		if (Config.LOG_FACTION_DETAILS)
		{
			LOGGER.fine("tryGiveReward: Killer=" + killer.getName() + ", Assistant=" + (assistant != null ? assistant.getName() : "null") + ", Debuffers=" + debuffers + ", Victim=" + (victim != null ? victim.getName() : "null"));
		}
		if (victim == null)
		{
			LOGGER.warning("Victim is null, skipping reward (should only be called for PvP).");
			return;
		}
		Party party = killer.getParty();
		if (party != null && party == victim.getParty())
		{
			return; // Do not reward if killer and victim are in the same party
		}
		// Kiểm tra faction để đảm bảo không thưởng khi giết đồng minh
		if (killer.getFaction() == victim.getFaction())
		{
			killer.sendMessage("You cannot gain GvE Skill Points for killing an ally from the same faction!");
			return;
		}

		// Kiểm tra AntiFeed để ngăn chặn farming điểm bằng alt characters
		if (!AntiFeedManager.getInstance().check(killer, victim))
		{
			killer.sendMessage("Anti-feed protection: No GvE Skill Points awarded for this kill!");
			return;
		}

		// Tính toán reward một lần để tối ưu performance
		double expReward = killer.getExpReward();
		double spReward = killer.getSpReward();
		double lcoinReward = killer.getLCoinReward();

		// Kiểm tra điều kiện cho các vai trò
		boolean validKiller = checkCondition(killer, victim, true);
		boolean validAssistant = assistant != null && !killer.equals(assistant) &&
			(killer.getParty() != assistant.getParty() || killer.getParty() == null) &&
			checkCondition(assistant, victim, false);

		if (Config.LOG_FACTION_DETAILS)
		{
			LOGGER.fine("ValidKiller=" + validKiller + ", ValidAssistant=" + validAssistant);
		}

		// Loại bỏ killer và assistant khỏi danh sách debuffers để tránh thưởng trùng lặp
		List<PlayerInstance> filteredDebuffers = debuffers.stream()
			.filter(player -> player != null && !player.equals(killer) && !player.equals(assistant))
			.filter(player -> checkCondition(player, victim, false))
			.collect(Collectors.toList());

		final int MAX_DEBUFFERS = 5;
		int numDebuffers = filteredDebuffers.size();
		if (numDebuffers > MAX_DEBUFFERS)
		{
			Collections.shuffle(filteredDebuffers);
			filteredDebuffers = filteredDebuffers.subList(0, MAX_DEBUFFERS);
			numDebuffers = MAX_DEBUFFERS;
			if (Config.LOG_FACTION_DETAILS)
			{
				LOGGER.info("Limited debuffers to " + MAX_DEBUFFERS + ": " + filteredDebuffers);
			}
		}
		boolean validDebuffers = !filteredDebuffers.isEmpty();

		if (!validKiller && !validAssistant && !validDebuffers)
		{
			return;
		}
		// Tính toán phần chia reward
		double killerShare;
		double assistantShare;
		double debufferShare;

		if (validKiller && validAssistant && validDebuffers)
		{
			killerShare = 0.5D;
			assistantShare = 0.3D;
			debufferShare = 0.2D / numDebuffers;
		}
		else if (validKiller && !validAssistant && validDebuffers)
		{
			killerShare = 0.8D;
			assistantShare = 0.0D;
			debufferShare = 0.2D / numDebuffers;
		}
		else if (!validKiller && validAssistant && validDebuffers)
		{
			killerShare = 0.0D;
			assistantShare = 0.8D;
			debufferShare = 0.2D / numDebuffers;
		}
		else if (!validKiller && !validAssistant && validDebuffers)
		{
			killerShare = 0.0D;
			assistantShare = 0.0D;
			debufferShare = 1.0D / numDebuffers;
		}
		else if (validKiller && validAssistant)
		{
			killerShare = 0.6D;
			assistantShare = 0.4D;
			debufferShare = 0.0D;
		}
		else if (validKiller && !validAssistant)
		{
			killerShare = 1.0D;
			assistantShare = 0.0D;
			debufferShare = 0.0D;
		}
		else if (!validKiller && validAssistant)
		{
			killerShare = 0.0D;
			assistantShare = 1.0D;
			debufferShare = 0.0D;
		}
		else
		{
			return;
		}
		// Tạo victim name một lần để tránh gọi getName() nhiều lần
		String victimName = victim.getName();

		// Thưởng cho killer (bao gồm GvE Skill Points)
		if (killerShare > 0.0D && validKiller)
		{
			int gveSkillPoints = calculateGvESkillPoints(killer, victim);
			giveReward(killer, victim, expReward * killerShare * rewardMod, spReward * killerShare * rewardMod, lcoinReward * killerShare * rewardMod, true, true);
			killer.addGveSkillPoints(gveSkillPoints, false); // Sử dụng silent version
			killer.sendMessage("You have earned " + gveSkillPoints + " GvE Skill Points for killing " + victimName + "!");
			updatePenalties(killer, victim);
			killer.manageComboKill();
			storeRewardVar(killer, 1);

			// Add museum data for GVE activities
			MuseumManager.getInstance().addDailyGvePoints(killer, gveSkillPoints);

			// Check for combo kills and update museum
			int comboCount = killer.getComboKills();
			if (comboCount > 1)
			{
				MuseumManager.getInstance().addComboKillsGve(killer, 1);
			}

			// Update longest kill streak if this is a new record
			MuseumManager.getInstance().updateLongestKillStreak(killer, comboCount);
		}

		// Thưởng cho assistant (chỉ EXP/SP/L-Coin, không có GvE Skill Points)
		if (assistantShare > 0.0D && validAssistant)
		{
			giveReward(assistant, victim, expReward * assistantShare * rewardMod, spReward * assistantShare * rewardMod, lcoinReward * assistantShare * rewardMod, false, false);
			assistant.sendMessage("You assisted in killing " + victimName + "!");
			updatePenalties(assistant, victim);
			storeRewardVar(assistant, 1);
		}

		// Thưởng cho debuffers (chỉ EXP/SP/L-Coin, không có GvE Skill Points)
		if (debufferShare > 0.0D && validDebuffers)
		{
			// Tính toán reward cho debuffers một lần
			long totalDebufferExp = (long) (expReward * debufferShare * rewardMod * numDebuffers);
			long totalDebufferSp = (long) (spReward * debufferShare * rewardMod * numDebuffers);
			long totalDebufferLCoin = (long) (lcoinReward * debufferShare * rewardMod * numDebuffers);

			long expPerDebuffer = totalDebufferExp / numDebuffers;
			long spPerDebuffer = totalDebufferSp / numDebuffers;
			long lCoinPerDebuffer = totalDebufferLCoin >= numDebuffers ? totalDebufferLCoin / numDebuffers : 0;

			long remainingExp = totalDebufferExp % numDebuffers;
			long remainingSp = totalDebufferSp % numDebuffers;
			long remainingLCoin = totalDebufferLCoin >= numDebuffers ? totalDebufferLCoin % numDebuffers : totalDebufferLCoin;

			// Tạo message một lần để tránh tạo string nhiều lần
			String contributionMessage = "You contributed to killing " + victimName + "!";

			for (int i = 0; i < numDebuffers; i++)
			{
				PlayerInstance debuffer = filteredDebuffers.get(i);
				long adjustedExp = expPerDebuffer + (i < remainingExp ? 1 : 0);
				long adjustedSp = spPerDebuffer + (i < remainingSp ? 1 : 0);
				long adjustedLCoin = lCoinPerDebuffer + (i < remainingLCoin ? 1 : 0);

				giveReward(debuffer, victim, adjustedExp, adjustedSp, adjustedLCoin, false, false);
				debuffer.sendMessage(contributionMessage);
				updatePenalties(debuffer, victim);
				storeRewardVar(debuffer, 1);
			}
		}
		victim.broadcastUserInfo();
	}
	
	// Phương thức tính GvE Skill Points - chỉ dành cho killer
	private int calculateGvESkillPoints(PlayerInstance killer, PlayerInstance victim)
	{
		int totalPoints = 1; // Điểm cơ bản là 1 điểm

		// Nếu người chơi có premium, cộng thêm 1 điểm
		if (killer.hasPremiumStatus())
		{
			totalPoints += 1;
		}

		// Thêm bonus 1 điểm nếu killer level thấp hơn victim 5 cấp trở lên
		int levelDifference = victim.getLevel() - killer.getLevel();
		if (levelDifference >= 5)
		{
			totalPoints += 1;
		}

		// Giới hạn tối thiểu và tối đa (1-3 điểm)
		int basePoints = Math.max(1, Math.min(3, totalPoints));

		// Apply Zone Dominance GvE Points bonus
		int finalPoints = basePoints;
		if (Config.ENABLE_ZONE_DOMINANCE_SYSTEM && killer.getFaction() != Faction.NONE)
		{
			int gveBonus = ZoneDominanceManager.getInstance().getGvePointsBonus(killer.getFaction());
			if (gveBonus > 0)
			{
				finalPoints = basePoints + (basePoints * gveBonus / 100);
				if (Config.LOG_FACTION_DETAILS)
				{
					LOGGER.fine("Zone Dominance GvE Skill Points bonus applied: " + basePoints + " -> " + finalPoints + " (+" + gveBonus + "%)");
				}
				// Send debug message to GM players
				ZoneDominanceManager.getInstance().sendBonusDebugMessage(killer, "GvE Skill Points", basePoints, finalPoints, gveBonus);
			}
		}

		return finalPoints;
	}
	
	private void updatePenalties(PlayerInstance player, PlayerInstance victim)
	{
		incrementKillerPenalty(player, victim);
		incrementDeathPenalty(victim);
		incrementSeriesPenalty(player, victim);
	}
	
	private void incrementKillerPenalty(PlayerInstance actor, PlayerInstance victim)
	{
		int actorObjectId = actor.getObjectId();
		int victimObjectId = victim != null ? victim.getObjectId() : 0;
		Map<Integer, Instant> actorKillPenaltiesTime = killPenaltiesTime.computeIfAbsent(actorObjectId, k -> new ConcurrentHashMap<>());
		if (actorKillPenaltiesTime.containsKey(victimObjectId))
		{
			return;
		}
		Map<Integer, Map.Entry<AtomicInteger, Instant>> actorKillPenalties = killPenalties.computeIfAbsent(actorObjectId, k -> new ConcurrentHashMap<>());
		Map.Entry<AtomicInteger, Instant> pair = actorKillPenalties.get(victimObjectId);
		if (pair == null)
		{
			Instant time = Instant.now().plusSeconds(Config.GVE_KILL_PENALTY_REMOVE_TIME);
			pair = Map.entry(new AtomicInteger(0), time);
			actorKillPenalties.put(victimObjectId, pair);
		}
		else
		{
			if (Instant.now().isAfter(pair.getValue()))
			{
				actorKillPenalties.remove(victimObjectId);
				incrementKillerPenalty(actor, victim);
			}
		}
		int count = pair.getKey().incrementAndGet();
		if (count > Config.GVE_KILL_PENALTY_COUNT)
		{
			Instant time = Instant.now().plusSeconds(Config.GVE_KILL_PENALTY_TIME);
			actorKillPenaltiesTime.put(victimObjectId, time);
			LOGGER.info("Applied kill penalty for " + actor.getName() + " on " + (victim != null ? victim.getName() : "null") + ", count=" + count + ", until " + time);
		}
	}
	
	private void incrementDeathPenalty(PlayerInstance victim)
	{
		if (victim == null)
		{
			return;
		}
		int victimObjectId = victim.getObjectId();
		if (deathPenaltiesTime.containsKey(victimObjectId))
		{
			return;
		}
		Map.Entry<AtomicInteger, Instant> pair = deathPenalties.get(victimObjectId);
		if (pair == null)
		{
			Instant time = Instant.now().plusSeconds(Config.GVE_DEATH_PENALTY_REMOVE_TIME);
			pair = Map.entry(new AtomicInteger(0), time);
			deathPenalties.put(victimObjectId, pair);
		}
		else
		{
			if (Instant.now().isAfter(pair.getValue()))
			{
				deathPenalties.remove(victimObjectId);
				incrementDeathPenalty(victim);
			}
		}
		int count = pair.getKey().incrementAndGet();
		if (count > Config.GVE_DEATH_PENALTY_COUNT)
		{
			Instant time = Instant.now().plusSeconds(Config.GVE_DEATH_PENALTY_TIME);
			deathPenaltiesTime.put(victimObjectId, time);
			LOGGER.info("Applied death penalty for " + victim.getName() + ", count=" + count + ", until " + time);
		}
	}
	
	private void incrementSeriesPenalty(PlayerInstance actor, PlayerInstance victim)
	{
		int actorObjectId = actor.getObjectId();
		int victimObjectId = victim != null ? victim.getObjectId() : 0;
		Map<Integer, Instant> actorSeriesPenaltiesTime = seriesPenaltiesTime.computeIfAbsent(actorObjectId, k -> new ConcurrentHashMap<>());
		if (actorSeriesPenaltiesTime.containsKey(victimObjectId))
		{
			return;
		}
		Map<Integer, Map.Entry<AtomicInteger, Instant>> actorSeriesPenalties = seriesPenalties.computeIfAbsent(actorObjectId, k -> new ConcurrentHashMap<>());
		Map.Entry<AtomicInteger, Instant> pair = actorSeriesPenalties.get(victimObjectId);
		if (pair == null)
		{
			actorSeriesPenalties.clear();
			Instant time = Instant.now().plusSeconds(Config.GVE_SERIES_KILL_PENALTY_REMOVE_TIME);
			pair = Map.entry(new AtomicInteger(0), time);
			actorSeriesPenalties.put(victimObjectId, pair);
		}
		else
		{
			if (Instant.now().isAfter(pair.getValue()))
			{
				actorSeriesPenalties.remove(victimObjectId);
				incrementSeriesPenalty(actor, victim);
			}
		}
		int count = pair.getKey().incrementAndGet();
		if (count > Config.GVE_SERIES_KILL_COUNT)
		{
			Instant time = Instant.now().plusSeconds(Config.GVE_SERIES_KILL_PENALTY_TIME);
			actorSeriesPenaltiesTime.put(victimObjectId, time);
			LOGGER.info("Applied series penalty for " + actor.getName() + " on " + (victim != null ? victim.getName() : "null") + ", count=" + count + ", until " + time);
		}
	}
	
	private void giveReward(PlayerInstance activeChar, PlayerInstance victim, double exp, double sp, double lcoin, boolean killer, boolean addClanExp)
	{
		Party party = activeChar.getParty();
		if (party != null)
		{
			final List<PlayerInstance> toReward = new LinkedList<>();
			for (PlayerInstance member : party.getMembers())
			{
				if (Util.checkIfInRange(Config.ALT_PARTY_RANGE, activeChar, member, true))
				{
					toReward.add(member);
				}
			}
			if (!toReward.isEmpty())
			{
				final long expPerMember = (long) exp / toReward.size();
				final long spPerMember = (long) sp / toReward.size();
				final long lcoinPerMember = (long) lcoin / toReward.size();
				LOGGER.info("Party reward distribution for " + activeChar.getName() + ": Total EXP=" + (long) exp + ", SP=" + (long) sp + ", L-Coin=" + (long) lcoin + ", Members=" + toReward.size());
				for (PlayerInstance member : toReward)
				{
					member.addExpAndSp(expPerMember, spPerMember);
					member.addItem("Party", Inventory.LCOIN_ID, lcoinPerMember, activeChar, true);
					if (Config.LOG_FACTION_DETAILS)
					{
						LOGGER.fine("Reward for party member (" + member.getName() + "): " + expPerMember + " EXP, " + spPerMember + " SP, " + lcoinPerMember + " L-Coin");
					}
					if (member.isInsideZone(ZoneId.DEFEND))
					{
						int prev = member.getVariables().getInt("defend_value", 0);
						member.getVariables().set("defend_value", prev + 1);
					}
					if (member.isInsideZone(ZoneId.DEFEND_ARTIFACT))
					{
						int prev = member.getVariables().getInt("artifact_defend_value", 0);
						member.getVariables().set("artifact_defend_value", prev + 1);
					}
				}
			}
		}
		else
		{
			activeChar.addExpAndSp((long) exp, (long) sp);
			activeChar.addItem("GveReward", Inventory.LCOIN_ID, (long) lcoin, activeChar, true);
			if (activeChar.isInsideZone(ZoneId.DEFEND))
			{
				int prev = activeChar.getVariables().getInt("defend_value", 0);
				activeChar.getVariables().set("defend_value", prev + 1);
			}
			if (activeChar.isInsideZone(ZoneId.DEFEND_ARTIFACT))
			{
				int prev = activeChar.getVariables().getInt("artifact_defend_value", 0);
				activeChar.getVariables().set("artifact_defend_value", prev + 1);
			}
		}
		if (addClanExp && victim != null)
		{
			Clan killerClan = activeChar.getClan();
			Clan victimClan = victim.getClan();
			if (killerClan != null && victimClan != null && victimClan.getExp() > 0)
			{
				killerClan.addExp(activeChar, 50);
				if (victim.getPledgeType() != Clan.SUBUNIT_ACADEMY)
				{
					victimClan.addExp(null, -50);
				}
			}
		}
	}
	
	public boolean checkCondition(PlayerInstance activeChar, PlayerInstance victim, boolean isKiller)
	{
		if (activeChar == null)
		{
			LOGGER.warning("checkCondition: activeChar is null, returning true");
			return true;
		}
		if (victim != null && revivePenalties.contains(victim.getObjectId()))
		{
			if (FactionWarManager.getInstance().isWarActive())
			{
				if (Config.LOG_FACTION_DETAILS)
				{
					LOGGER.info("checkCondition: Victim " + victim.getName() + " is in revivePenalties, but Faction War is active, ignoring penalty.");
				}
			}
			else
			{
				activeChar.sendMessage("No rewards granted: The victim is in a revive penalty state.");
				return false;
			}
		}
		if (activeChar.equals(victim))
		{
			if (Config.LOG_FACTION_DETAILS)
			{
				LOGGER.fine("checkCondition: activeChar equals victim, returning true");
			}
			return true;
		}
		if (activeChar.isInOfflineMode() || (victim != null && victim.isInOfflineMode()))
		{
			LOGGER.info("checkCondition: activeChar or victim is offline, returning false");
			return false;
		}
		long resetTime = activeChar.getVariables().getLong(PlayerVariables.GVE_KILL_REWARD_DAILY_COUNT_RESET_TIME, 0L);
		if (System.currentTimeMillis() > resetTime)
		{
			activeChar.getVariables().set(PlayerVariables.GVE_KILL_REWARD_DAILY_COUNT, 0L);
			activeChar.getVariables().set(PlayerVariables.GVE_KILL_REWARD_DAILY_COUNT_RESET_TIME, getRewardLimitResetTime());
			if (Config.LOG_FACTION_DETAILS)
			{
				LOGGER.info("checkCondition: Reset daily count for " + activeChar.getName());
			}
		}
		int victimObjectId = victim != null ? victim.getObjectId() : 0;
		if (isKiller)
		{
			Map<Integer, Instant> actorKillPenaltiesTime = killPenaltiesTime.get(activeChar.getObjectId());
			if (actorKillPenaltiesTime != null)
			{
				Instant killPenaltyTime = actorKillPenaltiesTime.get(victimObjectId);
				if (killPenaltyTime != null)
				{
					if (Instant.now().isBefore(killPenaltyTime))
					{
						LOGGER.info("checkCondition: " + activeChar.getName() + " has kill penalty for " + victim.getName() + ", penalty ends at " + killPenaltyTime + ", returning false");
						return false;
					}
					else
					{
						actorKillPenaltiesTime.remove(victimObjectId);
						activeChar.broadcastUserInfo();
						if (Config.LOG_FACTION_DETAILS)
						{
							LOGGER.info("checkCondition: Kill penalty for " + activeChar.getName() + " on " + victim.getName() + " has expired, removed");
						}
					}
				}
			}
		}
		if (victim != null)
		{
			Instant deathPenaltyTime = deathPenaltiesTime.get(victim.getObjectId());
			if (deathPenaltyTime != null)
			{
				if (Instant.now().isBefore(deathPenaltyTime))
				{
					LOGGER.info("checkCondition: Victim " + victim.getName() + " has death penalty, penalty ends at " + deathPenaltyTime + ", returning false");
					return false;
				}
				else
				{
					deathPenaltiesTime.remove(victim.getObjectId());
					activeChar.broadcastUserInfo();
					if (Config.LOG_FACTION_DETAILS)
					{
						LOGGER.info("checkCondition: Death penalty for " + victim.getName() + " has expired, removed");
					}
				}
			}
		}
		Map<Integer, Instant> actorSeriesPenaltiesTime = seriesPenaltiesTime.get(activeChar.getObjectId());
		if (actorSeriesPenaltiesTime != null)
		{
			Instant seriesPenaltyTime = actorSeriesPenaltiesTime.get(victimObjectId);
			if (seriesPenaltyTime != null)
			{
				if (Instant.now().isBefore(seriesPenaltyTime))
				{
					LOGGER.info("checkCondition: " + activeChar.getName() + " has series penalty for " + victim.getName() + ", penalty ends at " + seriesPenaltyTime + ", returning false");
					return false;
				}
				else
				{
					actorSeriesPenaltiesTime.remove(victimObjectId);
					activeChar.broadcastUserInfo();
					if (Config.LOG_FACTION_DETAILS)
					{
						LOGGER.info("checkCondition: Series penalty for " + activeChar.getName() + " on " + victim.getName() + " has expired, removed");
					}
				}
			}
		}
		if (Config.LOG_FACTION_DETAILS)
		{
			LOGGER.fine("checkCondition: All checks passed for " + activeChar.getName() + ", returning true");
		}
		return true;
	}
	
	public void manageRevivePenalty(PlayerInstance player, boolean add)
	{
		if (add)
		{
			revivePenalties.addIfAbsent(player.getObjectId());
		}
		else
		{
			revivePenalties.remove((Integer) player.getObjectId());
		}
	}
	
	private void storeRewardVar(PlayerInstance player, long count)
	{
		long total = player.getVariables().getLong(PlayerVariables.GVE_KILL_REWARD_DAILY_COUNT, 0L) + count;
		long resetTime = getRewardLimitResetTime();
		player.getVariables().set(PlayerVariables.GVE_KILL_REWARD_DAILY_COUNT, total);
		player.getVariables().set(PlayerVariables.GVE_KILL_REWARD_DAILY_COUNT_RESET_TIME, resetTime);
	}
	
	private long getRewardLimitResetTime()
	{
		Calendar calendar = Calendar.getInstance();
		calendar.set(Calendar.MINUTE, 0);
		calendar.set(Calendar.SECOND, 0);
		calendar.set(Calendar.HOUR_OF_DAY, 6);
		if (calendar.getTime().after(new Date()))
		{
			return calendar.getTimeInMillis();
		}
		calendar.add(Calendar.DAY_OF_MONTH, 1);
		return calendar.getTimeInMillis();
	}
}