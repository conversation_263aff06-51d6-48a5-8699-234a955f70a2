/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.instancemanager;

import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.model.zone.ZoneType;

/**
 * <AUTHOR>
 */
public class HeavenlyRiftManager {
	private static final ZoneType ZONE_1 = ZoneManager.getInstance().getZoneByName("heavenly_rift_1");
	private static final ZoneType ZONE_2 = ZoneManager.getInstance().getZoneByName("heavenly_rift_2");
	private static final ZoneType ZONE_3 = ZoneManager.getInstance().getZoneByName("heavenly_rift_3");

	public static ZoneType getZone1() {
		return ZONE_1;
	}

	public static ZoneType getZone2() {
		return ZONE_2;
	}

	public static ZoneType getZone3() {
		return ZONE_3;
	}

	public static boolean isInside(PlayerInstance player) {
		return ZONE_1.isCharacterInZone(player) || ZONE_2.isCharacterInZone(player) || ZONE_3.isCharacterInZone(player);
	}
}
