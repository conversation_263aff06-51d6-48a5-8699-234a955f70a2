package club.projectessence.gameserver.instancemanager;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.List;
import java.util.logging.Logger;

import club.projectessence.Config;
import club.projectessence.commons.database.DatabaseFactory;
import club.projectessence.gameserver.data.xml.ArtifactData;
import club.projectessence.gameserver.enums.Faction;
import club.projectessence.gameserver.model.ArtifactEntity;
import club.projectessence.gameserver.model.zone.ZoneType;
import club.projectessence.gameserver.model.zone.type.TaxZone;

public class ArtifactManager
{
	private static final Logger			LOGGER	= Logger.getLogger(ArtifactManager.class.getName());
	private final List<ArtifactEntity>	artifacts;
	
	protected ArtifactManager()
	{
		artifacts = ArtifactData.getInstance().getArtifacts();
		loadFromDatabase();
		initializeArtifacts();
	}
	
	public static ArtifactManager getInstance()
	{
		return SingletonHolder.INSTANCE;
	}
	
	private void loadFromDatabase()
	{
		// First, insert missing artifact states
		try (Connection con = DatabaseFactory.getConnection(); PreparedStatement ps = con.prepareStatement("INSERT INTO artifact_state (artifact_id, faction, region) " + "SELECT ?, ?, ? FROM DUAL WHERE NOT EXISTS (SELECT 1 FROM artifact_state WHERE artifact_id = ?)"))
		{
			for (ArtifactEntity artifact : artifacts)
			{
				int artifactId = artifact.getTemplate().getId();
				ps.setInt(1, artifactId);
				ps.setString(2, Faction.NONE.toString());
				ps.setString(3, artifact.getTemplate().getRegion());
				ps.setInt(4, artifactId);
				ps.addBatch();
			}
			ps.executeBatch();
		}
		catch (Exception e)
		{
			LOGGER.warning("ArtifactManager: Failed to initialize artifact states: " + e.getMessage());
		}
		// Then, load artifact states
		try (Connection con = DatabaseFactory.getConnection(); PreparedStatement ps = con.prepareStatement("SELECT artifact_id, faction FROM artifact_state"); ResultSet rs = ps.executeQuery())
		{
			while (rs.next())
			{
				int artifactId = rs.getInt("artifact_id");
				Faction faction = Faction.valueOf(rs.getString("faction"));
				ArtifactEntity artifact = artifacts.stream().filter(a -> a.getTemplate().getId() == artifactId).findFirst().orElse(null);
				if (artifact != null)
				{
					artifact.setFraction(faction);
					// Log only if LOG_FACTION_DETAILS is enabled
					if (Config.LOG_FACTION_DETAILS)
					{
						LOGGER.info("ArtifactManager: Loaded state for Artifact ID " + artifactId + " with faction " + faction);
					}
				}
				else
				{
					LOGGER.warning("ArtifactManager: Artifact ID " + artifactId + " not found in loaded data.");
				}
			}
		}
		catch (Exception e)
		{
			LOGGER.warning("ArtifactManager: Failed to load artifact states: " + e.getMessage());
		}
	}
	
	private void initializeArtifacts()
	{
		for (ArtifactEntity artifact : artifacts)
		{
			artifact.spawn();
		}
		// Log only if LOG_FACTION_DETAILS is enabled
		if (Config.LOG_FACTION_DETAILS)
		{
			LOGGER.info("ArtifactManager: Initialized " + artifacts.size() + " artifacts.");
		}
	}
	
	public void saveToDatabase(ArtifactEntity artifact)
	{
		try (Connection con = DatabaseFactory.getConnection(); PreparedStatement ps = con.prepareStatement("REPLACE INTO artifact_state (artifact_id, faction, region) VALUES (?, ?, ?)"))
		{
			ps.setInt(1, artifact.getTemplate().getId());
			ps.setString(2, artifact.getFraction().toString());
			ps.setString(3, artifact.getTemplate().getRegion());
			int rowsAffected = ps.executeUpdate();
			if (rowsAffected == 0)
			{
				LOGGER.warning("ArtifactManager: No rows updated for Artifact ID " + artifact.getTemplate().getId() + ". Ensure the artifact exists in artifact_state.");
			}
			else
			{
				// Log only if LOG_FACTION_DETAILS is enabled
				if (Config.LOG_FACTION_DETAILS)
				{
					LOGGER.info("ArtifactManager: Saved state for Artifact ID " + artifact.getTemplate().getId() + " with faction " + artifact.getFraction());
				}
			}
		}
		catch (Exception e)
		{
			LOGGER.warning("ArtifactManager: Failed to save artifact state to database: " + e.getMessage());
		}
	}
	
	public void saveAllToDatabase()
	{
		for (ArtifactEntity artifact : artifacts)
		{
			try (Connection con = DatabaseFactory.getConnection(); PreparedStatement ps = con.prepareStatement("REPLACE INTO artifact_state (artifact_id, faction, region) VALUES (?, ?, ?)"))
			{
				ps.setInt(1, artifact.getTemplate().getId());
				ps.setString(2, artifact.getFraction().toString());
				ps.setString(3, artifact.getTemplate().getRegion());
				int rowsAffected = ps.executeUpdate();
				if (rowsAffected == 0)
				{
					LOGGER.warning("ArtifactManager: No rows updated for Artifact ID " + artifact.getTemplate().getId() + " during shutdown.");
				}
				else
				{
					// Log only if LOG_FACTION_DETAILS is enabled
					if (Config.LOG_FACTION_DETAILS)
					{
						LOGGER.info("ArtifactManager: Saved state for Artifact ID " + artifact.getTemplate().getId() + " with faction " + artifact.getFraction() + " during shutdown.");
					}
				}
			}
			catch (Exception e)
			{
				LOGGER.warning("ArtifactManager: Failed to save artifact state during shutdown for Artifact ID " + artifact.getTemplate().getId() + ": " + e.getMessage());
			}
		}
	}
	
	public List<ArtifactEntity> getArtifacts()
	{
		return artifacts;
	}
	
	/**
	 * Count the number of artifacts controlled by a faction.
	 *
	 * @param faction
	 *            The faction to check
	 * @return The number of artifacts controlled by the faction
	 */
	public long getArtifactCountForFaction(Faction faction)
	{
		return artifacts.stream().filter(artifact -> artifact.getFraction() == faction).count();
	}
	
	/**
	 * Find the artifact controlling the area at coordinates (x, y, z).
	 *
	 * @param x
	 *            X coordinate
	 * @param y
	 *            Y coordinate
	 * @param z
	 *            Z coordinate
	 * @return The ArtifactEntity controlling the area, or null if not found
	 */
	public ArtifactEntity getArtifactForTerritory(int x, int y, int z)
	{
		// Check if the coordinates (x, y, z) are within any TaxZone
		List<ZoneType> zones = ZoneManager.getInstance().getZones(x, y, z);
		for (ZoneType zone : zones)
		{
			if (zone instanceof TaxZone)
			{
				TaxZone taxZone = (TaxZone) zone;
				int taxZoneDomainId = taxZone.getCastle() != null ? taxZone.getCastle().getResidenceId() : -1;
				if (taxZoneDomainId == -1)
				{
					LOGGER.warning("ArtifactManager: TaxZone at (" + x + "," + y + "," + z + ") has no valid domainId.");
					continue;
				}
				for (ArtifactEntity artifact : artifacts)
				{
					if (artifact.getTemplate().getDomainId() == taxZoneDomainId)
					{
						return artifact;
					}
				}
			}
		}
		return null; // Return null if no matching artifact is found
	}
	
	private static class SingletonHolder
	{
		protected static final ArtifactManager INSTANCE = new ArtifactManager();
	}
}