/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.instancemanager;

import club.projectessence.Config;
import club.projectessence.commons.database.DatabaseFactory;
import club.projectessence.gameserver.data.xml.SpawnData;
import club.projectessence.gameserver.model.Spawn;
import club.projectessence.gameserver.model.WorldObject;
import club.projectessence.gameserver.model.actor.Npc;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.model.clan.Clan;
import club.projectessence.gameserver.model.items.instance.ItemInstance;
import club.projectessence.gameserver.model.siege.Fort;
import club.projectessence.gameserver.model.siege.FortSiege;
import club.projectessence.gameserver.model.skills.CommonSkill;
import club.projectessence.gameserver.model.spawns.NpcSpawnTemplate;
import club.projectessence.gameserver.network.SystemMessageId;
import club.projectessence.gameserver.network.serverpackets.InventoryUpdate;
import club.projectessence.gameserver.network.serverpackets.SystemMessage;

import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.List;
import java.util.Properties;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.logging.Level;
import java.util.logging.Logger;

public class FortSiegeManager {
	private static final Logger LOGGER = Logger.getLogger(FortSiegeManager.class.getName());
	private final int _siegeClanMinLevel = 4; // Changeable in fortsiege.properties
	private int _attackerMaxClans = 500; // Max number of clans
	// Fort Siege settings
	// private Map<Integer, List<FortSiegeSpawn>> _commanderSpawnList;
	// private Map<Integer, List<CombatFlag>> _flagList;
	// private boolean _justToTerritory = true; // Changeable in fortsiege.properties
	private int _flagMaxCount = 1; // Changeable in fortsiege.properties
	private int _siegeLength = 60; // Time in minute. Changeable in fortsiege.properties
	private int _countDownLength = 10; // Time in minute. Changeable in fortsiege.properties
	private int _suspiciousMerchantRespawnDelay = 180; // Time in minute. Changeable in fortsiege.properties
	private List<FortSiege> _sieges;

	protected FortSiegeManager() {
		load();
	}

	public static FortSiegeManager getInstance() {
		return SingletonHolder.INSTANCE;
	}

	public void addCombatFlaglagSkills(PlayerInstance character) {
		Clan clan = character.getClan();
		if ((clan != null)) {
			if ((clan.getLevel() >= getSiegeClanMinLevel()) && FortManager.getInstance().getFortById(FortManager.ORC_FORTRESS_ID).getSiege().isInProgress()) {
				character.addSkill(CommonSkill.FLAG_DISPLAY.getSkill(), false);
				character.addSkill(CommonSkill.REMOTE_FLAG_DISPLAY.getSkill(), false);
				character.addSkill(CommonSkill.FLAG_POWER_FAST_RUN.getSkill(), false);
				character.addSkill(CommonSkill.FLAG_EQUIP.getSkill(), false);
				// TODO: CommonSkill.GET_COMBAT_FLAG & CommonSkill.DROP_COMBAT_FLAG are probably used for animations ?
				switch (character.getClassId()) {
					// Warrior
					case DUELIST:
					case DREADNOUGHT:
					case TITAN:
					case GRAND_KHAVATARI:
					case FORTUNE_SEEKER:
					case MAESTRO:
					case DOOMBRINGER:
					case SOUL_HOUND:
					case DEATH_KIGHT_HUMAN:
					case DEATH_KIGHT_ELF:
					case DEATH_KIGHT_DARK_ELF: {
						character.addSkill(CommonSkill.FLAG_POWER_WARRIOR.getSkill(), false);
						break;
					}

					// Knight
					case PHOENIX_KNIGHT:
					case HELL_KNIGHT:
					case EVA_TEMPLAR:
					case SHILLIEN_TEMPLAR: {
						character.addSkill(CommonSkill.FLAG_POWER_KNIGHT.getSkill(), false);
						break;
					}

					// Rogue
					case ADVENTURER:
					case WIND_RIDER:
					case GHOST_HUNTER: {
						character.addSkill(CommonSkill.FLAG_POWER_ROGUE.getSkill(), false);
						break;
					}
					// Archer
					case SAGITTARIUS:
					case MOONLIGHT_SENTINEL:
					case GHOST_SENTINEL:
					case TRICKSTER: {
						character.addSkill(CommonSkill.FLAG_POWER_ARCHER.getSkill(), false);
						break;
					}
					// Mage
					case ARCHMAGE:
					case SOULTAKER:
					case MYSTIC_MUSE:
					case STORM_SCREAMER: {
						character.addSkill(CommonSkill.FLAG_POWER_MAGE.getSkill(), false);
						break;
					}
					// Summoner
					case ARCANA_LORD:
					case ELEMENTAL_MASTER:
					case SPECTRAL_MASTER: {
						character.addSkill(CommonSkill.FLAG_POWER_SUMMONER.getSkill(), false);
						break;
					}
					// Healer
					case CARDINAL:
					case EVA_SAINT:
					case SHILLIEN_SAINT: {
						character.addSkill(CommonSkill.FLAG_POWER_HEALER.getSkill(), false);
						break;
					}
					// Enchanter
					case HIEROPHANT: {
						character.addSkill(CommonSkill.FLAG_POWER_ENCHANTER.getSkill(), false);
						break;
					}
					// Bard
					case SWORD_MUSE:
					case SPECTRAL_DANCER: {
						character.addSkill(CommonSkill.FLAG_POWER_BARD.getSkill(), false);
						break;
					}
					// Shaman
					case DOMINATOR:
					case DOOMCRYER: {
						character.addSkill(CommonSkill.FLAG_POWER_SHAMAN.getSkill(), false);
						break;
					}
				}
			}
		}
	}

	public void removeCombatFlagSkills(PlayerInstance character) {
		character.removeSkill(CommonSkill.FLAG_DISPLAY.getSkill());
		character.removeSkill(CommonSkill.REMOTE_FLAG_DISPLAY.getSkill());
		character.removeSkill(CommonSkill.FLAG_POWER_FAST_RUN.getSkill());
		character.removeSkill(CommonSkill.FLAG_EQUIP.getSkill());
		character.removeSkill(CommonSkill.FLAG_POWER_WARRIOR.getSkill());
		character.removeSkill(CommonSkill.FLAG_POWER_KNIGHT.getSkill());
		character.removeSkill(CommonSkill.FLAG_POWER_ROGUE.getSkill());
		character.removeSkill(CommonSkill.FLAG_POWER_ARCHER.getSkill());
		character.removeSkill(CommonSkill.FLAG_POWER_MAGE.getSkill());
		character.removeSkill(CommonSkill.FLAG_POWER_SUMMONER.getSkill());
		character.removeSkill(CommonSkill.FLAG_POWER_HEALER.getSkill());
		character.removeSkill(CommonSkill.FLAG_POWER_ENCHANTER.getSkill());
		character.removeSkill(CommonSkill.FLAG_POWER_BARD.getSkill());
		character.removeSkill(CommonSkill.FLAG_POWER_SHAMAN.getSkill());
		character.removeSkill(CommonSkill.FLAG_POWER_ENCHANTER.getSkill());
		character.removeSkill(CommonSkill.FLAG_EQUIP.getSkill());
	}

	/**
	 * @param clan   The Clan of the player
	 * @param fortid
	 * @return true if the clan is registered or owner of a fort
	 */
	public boolean checkIsRegistered(Clan clan, int fortid) {
		if (clan == null) {
			return false;
		}

		boolean register = false;
		try (Connection con = DatabaseFactory.getConnection();
		     PreparedStatement ps = con.prepareStatement("SELECT clan_id FROM fortsiege_clans where clan_id=? and fort_id=?")) {
			ps.setInt(1, clan.getId());
			ps.setInt(2, fortid);
			try (ResultSet rs = ps.executeQuery()) {
				if (rs.next()) {
					register = true;
				}
			}
		} catch (Exception e) {
			LOGGER.log(Level.WARNING, "Exception: checkIsRegistered(): " + e.getMessage(), e);
		}
		return register;
	}

	private void load() {
		final Properties siegeSettings = new Properties();
		final File file = new File(Config.FORTSIEGE_CONFIG_FILE);
		try (InputStream is = new FileInputStream(file)) {
			siegeSettings.load(is);
		} catch (Exception e) {
			LOGGER.log(Level.WARNING, "Error while loading Fort Siege Manager settings!", e);
		}

		// Siege setting
		// _justToTerritory = Boolean.parseBoolean(siegeSettings.getProperty("JustToTerritory", "true"));
		_attackerMaxClans = Integer.decode(siegeSettings.getProperty("AttackerMaxClans", "500"));
		_flagMaxCount = Integer.decode(siegeSettings.getProperty("MaxFlags", "1"));
		// _siegeClanMinLevel = Integer.decode(siegeSettings.getProperty("SiegeClanMinLevel", "4"));
		_siegeLength = Integer.decode(siegeSettings.getProperty("SiegeLength", "60"));
		_countDownLength = Integer.decode(siegeSettings.getProperty("CountDownLength", "10"));
		_suspiciousMerchantRespawnDelay = Integer.decode(siegeSettings.getProperty("SuspiciousMerchantRespawnDelay", "180"));
	}

	public int getAttackerMaxClans() {
		return _attackerMaxClans;
	}

	public int getFlagMaxCount() {
		return _flagMaxCount;
	}

	public int getSuspiciousMerchantRespawnDelay() {
		return _suspiciousMerchantRespawnDelay;
	}

	public FortSiege getSiege(WorldObject activeObject) {
		return getSiege(activeObject.getX(), activeObject.getY(), activeObject.getZ());
	}

	public FortSiege getSiege(int x, int y, int z) {
		for (Fort fort : FortManager.getInstance().getForts()) {
			if (fort.getSiege().checkIfInZone(x, y, z)) {
				return fort.getSiege();
			}
		}
		return null;
	}

	public int getSiegeClanMinLevel() {
		return _siegeClanMinLevel;
	}

	public int getSiegeLength() {
		return _siegeLength;
	}

	public int getCountDownLength() {
		return _countDownLength;
	}

	public List<FortSiege> getSieges() {
		if (_sieges == null) {
			_sieges = new CopyOnWriteArrayList<>();
		}
		return _sieges;
	}

	public void addSiege(FortSiege fortSiege) {
		getSieges().add(fortSiege);
	}

	public boolean isCombatFlag(int itemId) {
		return (itemId == FortManager.COMBAT_FLAG_ID);
	}

	public boolean activateCombatFlag(PlayerInstance player, ItemInstance item) {
		if (!checkIfCanPickup(player)) {
			return false;
		}

		if (player.isMounted()) {
			player.sendPacket(SystemMessageId.YOU_DO_NOT_MEET_THE_REQUIRED_CONDITION_TO_EQUIP_THAT_ITEM);
			return false;
		}

		player.getInventory().equipItem(item);
		// Refresh inventory
		if (!Config.FORCE_INVENTORY_UPDATE) {
			final InventoryUpdate iu = new InventoryUpdate();
			iu.addItem(item);
			player.sendInventoryUpdate(iu);
		} else {
			player.sendItemList();
		}

		player.broadcastUserInfo();
		player.setCombatFlagEquipped(true);
		addCombatFlaglagSkills(player);

		final SystemMessage sm = new SystemMessage(SystemMessageId.YOU_HAVE_EQUIPPED_YOUR_S1);
		sm.addItemName(item);
		player.sendPacket(sm);

		return true;
	}

	public boolean checkIfCanPickup(PlayerInstance player) {
		if (player.isCombatFlagEquipped()) {
			return false;
		}

		final Fort fort = FortManager.getInstance().getFort(player);
		if ((fort == null) || (fort.getResidenceId() <= 0)) {
			return false;
		}
		if (!fort.getSiege().isInProgress()) {
			player.sendPacket(new SystemMessage(SystemMessageId.THE_FORTRESS_BATTLE_OF_S1_HAS_FINISHED).addItemName(FortManager.COMBAT_FLAG_ID));
			return false;
		}

		return true;
	}

	public void dropCombatFlag(PlayerInstance player, int fortId) {
		final Fort fort = FortManager.getInstance().getFortById(fortId);
		if (player != null) {
			removeCombatFlagSkills(player);
			final long slot = player.getInventory().getSlotFromItem(player.getInventory().getItemByItemId(FortManager.COMBAT_FLAG_ID));
			player.getInventory().unEquipItemInBodySlot(slot);
			ItemInstance flag = player.getInventory().getItemByItemId(FortManager.COMBAT_FLAG_ID);

			SpawnData.getInstance().getSpawns().forEach(spawnTemplate -> spawnTemplate.getGroupsByName(flag.getVariables().getString(FortSiege.GREG_SPAWN_VAR, FortSiege.ORC_FORTRESS_GREG_BOTTOM_RIGHT_SPAWN)).forEach(holder ->
			{
				holder.spawnAll();
				for (NpcSpawnTemplate nst : holder.getSpawns()) {
					for (Npc npc : nst.getSpawnedNpcs()) {
						Spawn spawn = npc.getSpawn();
						if (spawn != null) {
							spawn.stopRespawn();
						}
					}
				}
			}));
			player.destroyItem("CombatFlag", flag, null, true);
			player.setCombatFlagEquipped(false);
			player.broadcastUserInfo();
		}
		fort.getSiege().addFlagCount(-1);
	}

	private static class SingletonHolder {
		protected static final FortSiegeManager INSTANCE = new FortSiegeManager();
	}
}
