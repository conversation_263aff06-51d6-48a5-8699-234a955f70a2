/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.instancemanager;

import club.projectessence.Config;
import club.projectessence.commons.concurrent.ThreadPool;
import club.projectessence.commons.database.DatabaseFactory;
import club.projectessence.commons.util.Rnd;
import club.projectessence.gameserver.data.SpawnTable;
import club.projectessence.gameserver.data.xml.NpcData;
import club.projectessence.gameserver.data.xml.RaidSpawnData;
import club.projectessence.gameserver.data.xml.SpecialHuntingZoneData;
import club.projectessence.gameserver.enums.RaidBossStatus;
import club.projectessence.gameserver.model.Location;
import club.projectessence.gameserver.model.Spawn;
import club.projectessence.gameserver.model.StatSet;
import club.projectessence.gameserver.model.actor.Npc;
import club.projectessence.gameserver.model.actor.templates.NpcTemplate;
import club.projectessence.gameserver.model.events.EventDispatcher;
import club.projectessence.gameserver.model.events.impl.creature.npc.OnNpcSpawn;
import club.projectessence.gameserver.util.Util;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Calendar;
import java.util.Date;
import java.util.Map;
import java.util.Map.Entry;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ScheduledFuture;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * <AUTHOR>
 */
public class RaidSpawnManager {
	public static final Location[] ULTIMATE_BEHEMOTH_LOC =
			{
					// TODO: 388 Benetis (Find proper locations)
					new Location(143405, 108834, -3949),
					new Location(144005, 110583, -3949),
					new Location(144399, 112727, -3949),
					new Location(149470, 119719, -4867),
			};
	private static final Logger LOGGER = Logger.getLogger(RaidSpawnManager.class.getName());
	private static final Location[] BEHEMOTH_LOC =
			{
					new Location(148309, 117955, -3715),
					new Location(153959, 118909, -3809),
					new Location(154363, 121253, -3809),
			};
	private static Npc _toiRaid = null;
	private static Npc _chaoticRaid = null;
	protected final Map<Integer, Npc> _npcs = new ConcurrentHashMap<>();
	protected final Map<Integer, Spawn> _spawns = new ConcurrentHashMap<>();
	protected final Map<Integer, StatSet> _storedInfo = new ConcurrentHashMap<>();
	protected final Map<Integer, ScheduledFuture<?>> _schedules = new ConcurrentHashMap<>();

	/**
	 * Instantiates a new raid npc spawn manager.
	 */
	protected RaidSpawnManager() {
		load();
	}

	public static RaidSpawnManager getInstance() {
		return SingletonHolder.INSTANCE;
	}

	/**
	 * Load.
	 */
	public void load() {
		if (Config.ALT_DEV_NO_SPAWNS) {
			return;
		}

		_npcs.clear();
		_spawns.clear();
		_storedInfo.clear();
		_schedules.clear();

		try (Connection con = DatabaseFactory.getConnection();
		     PreparedStatement statement = con.prepareStatement("SELECT * FROM raid_respawns");
		     ResultSet rset = statement.executeQuery()) {
			while (rset.next()) {
				final NpcTemplate template = getValidTemplate(rset.getInt("id"));
				if (template != null) {
					final Spawn spawn = new Spawn(template);
					spawn.setXYZ(rset.getInt("x"), rset.getInt("y"), rset.getInt("z"));
					spawn.setAmount(1);
					spawn.setHeading(rset.getInt("heading"));

					addNewSpawn(spawn, rset.getLong("deathTime"), rset.getLong("respawnTime"), rset.getDouble("currentHp"), rset.getDouble("currentMp"), false);
				} else {
					LOGGER.warning(getClass().getSimpleName() + ": Could not load npc #" + rset.getInt("id") + " from DB");
				}
			}

			LOGGER.info(getClass().getSimpleName() + ": Loaded " + _npcs.size() + " instances.");
			LOGGER.info(getClass().getSimpleName() + ": Scheduled " + _schedules.size() + " instances.");
		} catch (SQLException e) {
			LOGGER.log(Level.WARNING, getClass().getSimpleName() + ": Couldnt load raid_respawns table", e);
		} catch (Exception e) {
			LOGGER.log(Level.WARNING, getClass().getSimpleName() + ": Error while initializing RaidSpawnManager: ", e);
		}

		for (Entry<Integer, StatSet> entry : RaidSpawnData.getInstance().getAllRaidsSpawnInfo().entrySet()) {
			try {
				int raidId = entry.getKey();
				final StatSet set = entry.getValue();
				if (!_npcs.containsKey(raidId)) {
					final NpcTemplate template = getValidTemplate(raidId);
					if (template != null) {
						final Spawn spawn = new Spawn(template);
						spawn.setXYZ(set.getInt("x"), set.getInt("y"), set.getInt("z"));
						spawn.setAmount(1);
						spawn.setHeading(set.getInt("heading"));

						addNewSpawn(spawn, true);
					} else {
						LOGGER.warning(getClass().getSimpleName() + ": Could not load npc #" + set.getInt("id") + " from DB");
					}
				}
			} catch (Exception e) {
				LOGGER.log(Level.WARNING, getClass().getSimpleName() + ": Error while initializing RaidSpawnManager: ", e);
			}
		}
	}

	private void scheduleSpawn(int npcId) {
		if (npcId == 25912) // Behemoth
		{
			_spawns.get(npcId).setXYZ(BEHEMOTH_LOC[Rnd.get(BEHEMOTH_LOC.length)]);
		}

		switch (npcId) {
			case 29170: {
				try {
					final int newNpcId = Rnd.get(29170, 29173);
					final Spawn spawn = new Spawn(newNpcId);
					final Spawn oldSpawn = _spawns.get(npcId);
					switch (newNpcId) {
						case 29170 -> spawn.setXYZ(190414, 18382, -3720);
						case 29171 -> spawn.setXYZ(190014, 22378, -3720);
						case 29172 -> spawn.setXYZ(192414, 18382, -3720);
						case 29173 -> spawn.setXYZ(192414, 22378, -3480);
						default -> spawn.setXYZ(oldSpawn.getLocation());
					}
					spawn.setHeading(oldSpawn.getHeading());
					spawn.stopRespawn();
					final int specialHuntingZoneId = RaidSpawnData.getInstance().getGlobalSpecialHuntingZoneId(spawn.getId());
					if (specialHuntingZoneId >= 0) {
						spawn.setInstanceId(SpecialHuntingZoneData.getInstance().getGlobalInstance(specialHuntingZoneId).getId());
					}
					final Npc npc = spawn.doSpawn(false);
					npc.setDBStatus(RaidBossStatus.ALIVE);
					_chaoticRaid = npc;
					final StatSet info = new StatSet();
					info.set("currentHP", npc.getCurrentHp());
					info.set("currentMP", npc.getCurrentMp());
					info.set("respawnTime", 0);
					_storedInfo.put(npcId, info);
					_npcs.put(npcId, npc);
					_schedules.remove(npcId);
				} catch (Exception e) {
					e.printStackTrace();
				}
				return;
			}
			case 29171:
			case 29172:
			case 29173: {
				return;
			}
		}

		final int specialHuntingZoneId = RaidSpawnData.getInstance().getGlobalSpecialHuntingZoneId(_spawns.get(npcId).getId());
		if (specialHuntingZoneId >= 0) {
			_spawns.get(npcId).setInstanceId(SpecialHuntingZoneData.getInstance().getGlobalInstance(specialHuntingZoneId).getId());
		}
		final Npc npc = _spawns.get(npcId).doSpawn();
		if (npc != null) {
			npc.setDBStatus(RaidBossStatus.ALIVE);

			final StatSet info = new StatSet();
			info.set("currentHP", npc.getCurrentHp());
			info.set("currentMP", npc.getCurrentMp());
			info.set("respawnTime", 0);
			_storedInfo.put(npcId, info);
			_npcs.put(npcId, npc);
			LOGGER.info(getClass().getSimpleName() + ": Spawning NPC " + npc.getName());
		}

		_schedules.remove(npcId);
	}

	/**
	 * Update status.
	 *
	 * @param npc       the npc
	 * @param isNpcDead the is npc dead
	 */
	public void updateStatus(Npc npc, boolean isNpcDead) {
		Npc boss = npc;
		if (boss.getId() == 25949) // Hermit Beoro
		{
			boss = _npcs.get(25948); // Beoro
		}

		switch (boss.getId()) {
			case 29170:
			case 29171:
			case 29172:
			case 29173: {
				final StatSet info = new StatSet();
				info.set("currentHP", 0);
				info.set("currentMP", 0);
				info.set("deathTime", System.currentTimeMillis());
				long respawnTime = calculateNextRespawn(29170);
				final long respawnDelay = respawnTime - System.currentTimeMillis();
				info.set("respawnTime", respawnTime);
				_storedInfo.put(29170, info);
				_spawns.get(29170).setRespawnDelay((int) (respawnDelay / 1000));
				if (!_schedules.containsKey(29170) && (respawnDelay > 0)) {
					LOGGER.info(getClass().getSimpleName() + ": Updated " + boss.getName() + " respawn time to " + Util.formatDate(new Date(respawnTime), "dd.MM.yyyy HH:mm"));
					final int bossId = 29170; // Benetis: STUPID JAVA
					_schedules.put(bossId, ThreadPool.get().schedule(() -> scheduleSpawn(bossId), respawnDelay));
					updateDb();
				} else {
					LOGGER.warning(getClass().getSimpleName() + ": Failed to update " + boss.getName() + " respawn time.");
				}
				return;
			}
		}

		final StatSet info = _storedInfo.get(boss.getId());
		if (info == null) {
			return;
		}

		if (isNpcDead) {
			boss.setDBStatus(RaidBossStatus.DEAD);
			final long respawnTime = calculateNextRespawn(boss.getId());
			final long respawnDelay = respawnTime - System.currentTimeMillis();
			boss.getSpawn().setRespawnDelay((int) (respawnDelay / 1000));
			info.set("currentHP", boss.getMaxHp());
			info.set("currentMP", boss.getMaxMp());
			info.set("deathTime", System.currentTimeMillis());
			info.set("respawnTime", respawnTime);
			if (!_schedules.containsKey(boss.getId()) && (respawnDelay > 0)) {
				LOGGER.info(getClass().getSimpleName() + ": Updated " + boss.getName() + " respawn time to " + Util.formatDate(new Date(respawnTime), "dd.MM.yyyy HH:mm"));
				final int bossId = boss.getId(); // Benetis: STUPID JAVA
				_schedules.put(bossId, ThreadPool.get().schedule(() -> scheduleSpawn(bossId), respawnDelay));
				updateDb();
			} else {
				LOGGER.warning(getClass().getSimpleName() + ": Failed to update " + boss.getName() + " respawn time.");
			}
		} else {
			boss.setDBStatus(RaidBossStatus.ALIVE);

			info.set("currentHP", boss.getCurrentHp());
			info.set("currentMP", boss.getCurrentMp());
			info.set("deathTime", 0);
			info.set("respawnTime", 0);
		}
		_storedInfo.put(boss.getId(), info);
	}

	private long calculateNextRespawn(int raidId) {
		switch (RaidSpawnData.getInstance().getRespawnType(raidId)) {
			case 1: // handled by scripts
			{
				if (raidId == 25927) // Kadin
				{
					Calendar c = Calendar.getInstance();
					c.setTimeInMillis(System.currentTimeMillis());
					c.set(Calendar.HOUR_OF_DAY, 22);
					c.set(Calendar.MINUTE, Rnd.get(0, 30));
					c.set(Calendar.SECOND, 0);
					c.set(Calendar.MILLISECOND, 0);
					int minutesLeft = (int) ((c.getTimeInMillis() - System.currentTimeMillis()) / 1000 / 60);
					if ((c.getTimeInMillis() <= System.currentTimeMillis()) || (minutesLeft < 31)) {
						c.add(Calendar.HOUR, 24);
					}
					return c.getTimeInMillis();
				} else if (raidId == 25912) // Monster
				{
					Calendar c = Calendar.getInstance();
					c.setTimeInMillis(System.currentTimeMillis());
					c.set(Calendar.HOUR_OF_DAY, 22);
					c.set(Calendar.MINUTE, Rnd.get(0, 30));
					c.set(Calendar.SECOND, 0);
					c.set(Calendar.MILLISECOND, 0);
					int minutesLeft = (int) ((c.getTimeInMillis() - System.currentTimeMillis()) / 1000 / 60);
					if ((c.getTimeInMillis() <= System.currentTimeMillis()) || (minutesLeft < 31)) {
						c.add(Calendar.HOUR, 24);
					}
					// Custom
					while ((c.get(Calendar.DAY_OF_WEEK) != Calendar.MONDAY) //
							&& (c.get(Calendar.DAY_OF_WEEK) != Calendar.WEDNESDAY) //
							&& (c.get(Calendar.DAY_OF_WEEK) != Calendar.FRIDAY)) {
						c.add(Calendar.HOUR, 24);
					}
					return c.getTimeInMillis();
				}
				return -1;
			}
			case 2: // x hours
			{
				String respawnData = RaidSpawnData.getInstance().getRespawnData(raidId);
				if (respawnData == null) {
					return -1;
				}
				String minutesStr = respawnData.substring(Math.max(respawnData.length() - 2, 0));
				respawnData = respawnData.replace(minutesStr, "");
				String hoursStr = respawnData;
				return System.currentTimeMillis() + (Integer.parseInt(hoursStr) * 3_600_000) + (Integer.parseInt(minutesStr) * 60_000);
			}
			case 3: // static respawn time. hourMinute
			{
				String respawnData = RaidSpawnData.getInstance().getRespawnData(raidId);
				if (respawnData == null) {
					return -1;
				}
				long earliestTime = -1;
				for (String respData : respawnData.split(";")) {
					String minutesStr = respData.substring(Math.max(respData.length() - 2, 0));
					respData = respData.replace(minutesStr, "");
					String hoursStr = respData;
					Calendar c = Calendar.getInstance();
					int hour = Integer.parseInt(hoursStr);
					c.setTimeInMillis(System.currentTimeMillis());
					c.set(Calendar.HOUR_OF_DAY, hour);
					c.set(Calendar.MINUTE, Integer.parseInt(minutesStr));
					c.set(Calendar.SECOND, 0);
					c.set(Calendar.MILLISECOND, 0);
					if (c.getTimeInMillis() <= System.currentTimeMillis()) {
						c.add(Calendar.HOUR, 24);
					}
					long respTime = c.getTimeInMillis();
					if ((earliestTime == -1) || (respTime < earliestTime)) {
						earliestTime = respTime;
					}
				}
				return earliestTime;
			}
			case 4: // static respawn time. dayHourMinute
			{
				// Should be handled by bosses scripts
				String respawnData = RaidSpawnData.getInstance().getRespawnData(raidId);
				if (respawnData == null) {
					return -1;
				}
				long earliestTime = -1;
				for (String respData : respawnData.split(";")) {
					String minutesStr = respData.substring(Math.max(respData.length() - 2, 0));
					respData = respData.replace(minutesStr, "");
					String hoursStr = respData.substring(Math.max(respData.length() - 2, 0));
					respData = respData.replace(hoursStr, "");
					String dayStr = respData;
					Calendar c = Calendar.getInstance();
					c.set(Calendar.HOUR_OF_DAY, Integer.parseInt(hoursStr));
					c.set(Calendar.MINUTE, Integer.parseInt(minutesStr));
					c.set(Calendar.SECOND, 0);
					c.set(Calendar.MILLISECOND, 0);
					int dayOfWeek = Integer.parseInt(dayStr);
					for (int i = 0; i < 7; i++) {
						if ((c.get(Calendar.DAY_OF_WEEK) == dayOfWeek) && (c.getTimeInMillis() > System.currentTimeMillis())) {
							break;
						}
						c.add(Calendar.DAY_OF_WEEK, 1);
					}
					long respTime = c.getTimeInMillis();
					if ((earliestTime == -1) || (respTime < earliestTime)) {
						earliestTime = respTime;
					}
				}
				return earliestTime;
			}
			default: {
				return -1;
			}
		}
	}

	/**
	 * Adds the new spawn.
	 *
	 * @param spawn       the spawn dat
	 * @param deathTime   the death time
	 * @param respawnTime the respawn time
	 * @param currentHP   the current hp
	 * @param currentMP   the current mp
	 * @param storeInDb   the store in db
	 */
	public void addNewSpawn(Spawn spawn, long deathTime, long respawnTime, double currentHP, double currentMP, boolean storeInDb) {
		if (spawn == null) {
			return;
		}
		if (_spawns.containsKey(spawn.getId())) {
			return;
		}

		final int npcId = spawn.getId();
		final long time = System.currentTimeMillis();
		SpawnTable.getInstance().addNewSpawn(spawn, false);
		if ((respawnTime == 0) || (time > respawnTime)) {
			switch (npcId) {
				case 29170: {
					try {
						final int newNpcId = Rnd.get(29170, 29173);
						final Spawn newSpawn = new Spawn(newNpcId);
						switch (newNpcId) {
							case 29170 -> newSpawn.setXYZ(190414, 18382, -3720);
							case 29171 -> newSpawn.setXYZ(190014, 22378, -3720);
							case 29172 -> newSpawn.setXYZ(192414, 18382, -3720);
							case 29173 -> newSpawn.setXYZ(192414, 22378, -3480);
							default -> newSpawn.setXYZ(spawn.getLocation());
						}
						newSpawn.setHeading(spawn.getHeading());
						newSpawn.stopRespawn();
						final int specialHuntingZoneId = RaidSpawnData.getInstance().getGlobalSpecialHuntingZoneId(newSpawn.getId());
						if (specialHuntingZoneId >= 0) {
							newSpawn.setInstanceId(SpecialHuntingZoneData.getInstance().getGlobalInstance(specialHuntingZoneId).getId());
						}
						final Npc npc = newSpawn.doSpawn(false);
						_chaoticRaid = npc;
						_schedules.remove(npcId);
						_npcs.put(29170, npc);
						final StatSet info = new StatSet();
						info.set("currentHP", currentHP);
						info.set("currentMP", currentMP);
						info.set("deathTime", 0);
						info.set("respawnTime", 0);
						_storedInfo.put(29170, info);

						_spawns.put(29170, spawn);

						if (storeInDb) {
							try (Connection con = DatabaseFactory.getConnection();
							     PreparedStatement statement = con.prepareStatement("INSERT INTO raid_respawns (id, x, y, z, heading, deathTime, respawnTime, currentHp, currentMp) VALUES(?, ?, ?, ?, ?, ?, ?, ?, ?)")) {
								statement.setInt(1, 29170);
								statement.setInt(2, spawn.getX());
								statement.setInt(3, spawn.getY());
								statement.setInt(4, spawn.getZ());
								statement.setInt(5, spawn.getHeading());
								statement.setLong(6, deathTime);
								statement.setLong(7, respawnTime);
								statement.setDouble(8, currentHP);
								statement.setDouble(9, currentMP);
								statement.execute();
							} catch (Exception e) {
								// problem with storing spawn
								LOGGER.log(Level.WARNING, getClass().getSimpleName() + ": Could not store npc #" + npcId + " in the DB: ", e);
							}
						}
					} catch (Exception e) {
						e.printStackTrace();
					}
					return;
				}
				case 29171:
				case 29172:
				case 29173: {
					break;
				}
				default: {
					final int specialHuntingZoneId = RaidSpawnData.getInstance().getGlobalSpecialHuntingZoneId(spawn.getId());
					if (specialHuntingZoneId >= 0) {
						spawn.setInstanceId(SpecialHuntingZoneData.getInstance().getGlobalInstance(specialHuntingZoneId).getId());
					}
					final Npc npc = spawn.doSpawn();
					if (npc.getId() == 29146) { // Darion
						ThreadPool.get().schedule(() -> EventDispatcher.getInstance().notifyEventAsync(new OnNpcSpawn(npc), npc), 30_000);
					}
					if (npc != null) {
						npc.setCurrentHp(currentHP);
						npc.setCurrentMp(currentMP);
						npc.setDBStatus(RaidBossStatus.ALIVE);

						_npcs.put(npcId, npc);

						final StatSet info = new StatSet();
						info.set("currentHP", currentHP);
						info.set("currentMP", currentMP);
						info.set("deathTime", 0);
						info.set("respawnTime", 0);
						_storedInfo.put(npcId, info);
					}
					break;
				}
			}
		} else {
			final StatSet info = new StatSet();
			info.set("currentHP", currentHP);
			info.set("currentMP", currentMP);
			info.set("deathTime", deathTime);
			info.set("respawnTime", respawnTime);
			_storedInfo.put(npcId, info);
			final long spawnTime = respawnTime - System.currentTimeMillis();
			_schedules.put(npcId, ThreadPool.get().schedule(() -> scheduleSpawn(npcId), spawnTime));
		}

		_spawns.put(npcId, spawn);

		if (storeInDb) {
			try (Connection con = DatabaseFactory.getConnection();
			     PreparedStatement statement = con.prepareStatement("INSERT INTO raid_respawns (id, x, y, z, heading, deathTime, respawnTime, currentHp, currentMp) VALUES(?, ?, ?, ?, ?, ?, ?, ?, ?)")) {
				statement.setInt(1, spawn.getId());
				statement.setInt(2, spawn.getX());
				statement.setInt(3, spawn.getY());
				statement.setInt(4, spawn.getZ());
				statement.setInt(5, spawn.getHeading());
				statement.setLong(6, deathTime);
				statement.setLong(7, respawnTime);
				statement.setDouble(8, currentHP);
				statement.setDouble(9, currentMP);
				statement.execute();
			} catch (Exception e) {
				// problem with storing spawn
				LOGGER.log(Level.WARNING, getClass().getSimpleName() + ": Could not store npc #" + npcId + " in the DB: ", e);
			}
		}
	}

	public Npc addNewSpawn(Spawn spawn, boolean storeInDb) {
		if (spawn == null) {
			return null;
		}

		final int npcId = spawn.getId();
		final Spawn existingSpawn = _spawns.get(npcId);
		if (existingSpawn != null) {
			return existingSpawn.getLastSpawn();
		}

		SpawnTable.getInstance().addNewSpawn(spawn, false);

		final int specialHuntingZoneId = RaidSpawnData.getInstance().getGlobalSpecialHuntingZoneId(spawn.getId());
		if (specialHuntingZoneId >= 0) {
			spawn.setInstanceId(SpecialHuntingZoneData.getInstance().getGlobalInstance(specialHuntingZoneId).getId());
		}
		final Npc npc = spawn.doSpawn();
		if (npc == null) {
			throw new NullPointerException();
		}
		npc.setDBStatus(RaidBossStatus.ALIVE);

		final StatSet info = new StatSet();
		info.set("currentHP", npc.getMaxHp());
		info.set("currentMP", npc.getMaxMp());
		info.set("respawnTime", 0);
		_npcs.put(npcId, npc);
		_storedInfo.put(npcId, info);

		_spawns.put(npcId, spawn);

		if (storeInDb) {
			try (Connection con = DatabaseFactory.getConnection();
			     PreparedStatement statement = con.prepareStatement("INSERT INTO raid_respawns (id, x, y, z, heading, respawnTime, currentHp, currentMp) VALUES(?, ?, ?, ?, ?, ?, ?, ?)")) {
				statement.setInt(1, spawn.getId());
				statement.setInt(2, spawn.getX());
				statement.setInt(3, spawn.getY());
				statement.setInt(4, spawn.getZ());
				statement.setInt(5, spawn.getHeading());
				statement.setLong(6, 0);
				statement.setDouble(7, npc.getMaxHp());
				statement.setDouble(8, npc.getMaxMp());
				statement.execute();
			} catch (Exception e) {
				// problem with storing spawn
				LOGGER.log(Level.WARNING, getClass().getSimpleName() + ": Could not store npc #" + npcId + " in the DB: ", e);
			}
		}

		return npc;
	}

	/**
	 * Delete spawn.
	 *
	 * @param spawn    the spawn dat
	 * @param updateDb the update db
	 */
	public void deleteSpawn(Spawn spawn, boolean updateDb) {
		if (spawn == null) {
			return;
		}

		final int npcId = spawn.getId();
		_spawns.remove(npcId);
		_npcs.remove(npcId);
		_storedInfo.remove(npcId);

		final ScheduledFuture<?> task = _schedules.remove(npcId);
		if (task != null) {
			task.cancel(true);
		}

		if (updateDb) {
			try (Connection con = DatabaseFactory.getConnection();
			     PreparedStatement ps = con.prepareStatement("DELETE FROM raid_respawns WHERE id = ?")) {
				ps.setInt(1, npcId);
				ps.execute();
			} catch (Exception e) {
				// problem with deleting spawn
				LOGGER.log(Level.WARNING, getClass().getSimpleName() + ": Could not remove npc #" + npcId + " from DB: ", e);
			}
		}

		SpawnTable.getInstance().deleteSpawn(spawn, false);
	}

	/**
	 * Update database.
	 */
	private void updateDb() {
		try (Connection con = DatabaseFactory.getConnection();
		     PreparedStatement statement = con.prepareStatement("UPDATE raid_respawns SET deathTime = ?, respawnTime = ?, currentHP = ?, currentMP = ? WHERE id = ?")) {
			for (Entry<Integer, StatSet> entry : _storedInfo.entrySet()) {
				final Integer npcId = entry.getKey();
				if (npcId == null) {
					continue;
				}

				final Npc npc = _npcs.get(npcId);
				if (npc == null) {
					continue;
				}

				if (npc.getDBStatus() == RaidBossStatus.ALIVE) {
					updateStatus(npc, false);
				}

				final StatSet info = entry.getValue();
				if (info == null) {
					continue;
				}

				try {
					// try
					// {
					// LOGGER.info(getClass().getSimpleName() + ": Updating DB: [npcId: " + npcId + "] [deathTime: " + info.getLong("deathTime") + "] [respawnTime: " + info.getLong("respawnTime") + "] [currentHP: " + (npc.isDead() ? npc.getMaxHp() : info.getDouble("currentHP")) + "] [currentMP: " +
					// (npc.isDead() ? npc.getMaxMp() : info.getDouble("currentMP")) + "]");
					// }
					// catch (Exception e)
					// {
					// e.printStackTrace();
					// }
					statement.setLong(1, info.getLong("deathTime"));
					statement.setLong(2, info.getLong("respawnTime"));
					statement.setDouble(3, npc.isDead() ? npc.getMaxHp() : Math.max(0, info.getDouble("currentHP")));
					statement.setDouble(4, npc.isDead() ? npc.getMaxMp() : Math.max(0, info.getDouble("currentMP")));
					statement.setInt(5, npcId);
					statement.executeUpdate();
					statement.clearParameters();
				} catch (SQLException e) {
					LOGGER.log(Level.WARNING, getClass().getSimpleName() + ": Couldnt update raid_respawns table ", e);
				}
			}
		} catch (SQLException e) {
			LOGGER.log(Level.WARNING, getClass().getSimpleName() + ": SQL error while updating database spawn to database: ", e);
		}
	}

	/**
	 * Gets the all npc status.
	 *
	 * @return the all npc status
	 */
	public String[] getAllNpcsStatus() {
		final String[] msg = new String[(_npcs == null) ? 0 : _npcs.size()];
		if (_npcs == null) {
			msg[0] = "None";
			return msg;
		}

		int index = 0;
		for (Npc npc : _npcs.values()) {
			msg[index++] = npc.getName() + ": " + npc.getDBStatus().name();
		}

		return msg;
	}

	/**
	 * Gets the npc status.
	 *
	 * @param npcId the npc id
	 * @return the raid npc status
	 */
	public String getNpcsStatus(int npcId) {
		String msg = "NPC Status..." + Config.EOL;
		if (_npcs == null) {
			msg += "None";
			return msg;
		}

		if (_npcs.containsKey(npcId)) {
			final Npc npc = _npcs.get(npcId);
			msg += npc.getName() + ": " + npc.getDBStatus().name();
		}

		return msg;
	}

	/**
	 * Gets the raid npc status id.
	 *
	 * @param npcId the npc id
	 * @return the raid npc status id
	 */
	public RaidBossStatus getNpcStatusId(int npcId) {
		if ((_chaoticRaid != null) && (npcId == _chaoticRaid.getId())) {
			return _chaoticRaid.isDead() ? RaidBossStatus.DEAD : RaidBossStatus.ALIVE;
		}
		if (_npcs.containsKey(npcId)) {
			return _npcs.get(npcId).getDBStatus();
		} else if (_schedules.containsKey(npcId)) {
			return RaidBossStatus.DEAD;
		} else {
			return RaidBossStatus.UNDEFINED;
		}
	}

	/**
	 * @param npcId
	 * @return 0 if dead, 1 if alive, 2 if in combat
	 */
	public int getNpcVisualStatus(int npcId) {
		if ((_chaoticRaid != null) && (_chaoticRaid.getId() == npcId) && !_chaoticRaid.isDead()) {
			return 1 + (_chaoticRaid.isInCombat() ? 1 : 0);
		} else if (_npcs.containsKey(npcId)) {
			if (npcId == 29170) // Chaotic Core (should be handled by chaoticRaid, if alive)
			{
				return 0;
			}
			RaidBossStatus status = getNpcStatusId(npcId);
			if (status == RaidBossStatus.ALIVE) {
				return 1 + (_npcs.get(npcId).isInCombat() ? 1 : 0); // 1 - Alive, 2 - In combat
			}
		} else if ((_toiRaid != null) && (_toiRaid.getId() == npcId) && !_toiRaid.isDead()) {
			return 1 + (_toiRaid.isInCombat() ? 1 : 0);
		}
		return 0; // Dead
	}

	public long getDeathTime(int npcId) {
		StatSet info = _storedInfo.get(npcId);
		if (info != null) {
			return info.getLong("deathTime", 0);
		}
		return 0;
	}

	/**
	 * Gets the valid template.
	 *
	 * @param npcId the npc id
	 * @return the valid template
	 */
	public NpcTemplate getValidTemplate(int npcId) {
		return NpcData.getInstance().getTemplate(npcId);
	}

	/**
	 * Notify spawn night npc.
	 *
	 * @param npc the npc
	 */
	public void notifySpawnNightNpc(Npc npc) {
		final StatSet info = new StatSet();
		info.set("currentHP", npc.getCurrentHp());
		info.set("currentMP", npc.getCurrentMp());
		info.set("respawnTime", 0);
		npc.setDBStatus(RaidBossStatus.ALIVE);

		_storedInfo.put(npc.getId(), info);
		_npcs.put(npc.getId(), npc);
	}

	/**
	 * Checks if the npc is defined.
	 *
	 * @param npcId the npc id
	 * @return {@code true} if is defined
	 */
	public boolean isDefined(int npcId) {
		return _spawns.containsKey(npcId);
	}

	/**
	 * Gets the npcs.
	 *
	 * @return the npcs
	 */
	public Map<Integer, Npc> getNpcs() {
		return _npcs;
	}

	/**
	 * Gets the spawns.
	 *
	 * @return the spawns
	 */
	public Map<Integer, Spawn> getSpawns() {
		return _spawns;
	}

	/**
	 * Gets the stored info.
	 *
	 * @return the stored info
	 */
	public Map<Integer, StatSet> getStoredInfo() {
		return _storedInfo;
	}

	/**
	 * Saves and clears the raid npces status, including all schedules.
	 */
	public void cleanUp() {
		updateDb();

		_npcs.clear();

		for (ScheduledFuture<?> shedule : _schedules.values()) {
			shedule.cancel(true);
		}
		_schedules.clear();

		_storedInfo.clear();
		_spawns.clear();
	}

	public Npc getToiRaid() {
		return _toiRaid;
	}

	public void setToiRaid(Npc npc) {
		_toiRaid = npc;
	}

	public Npc getChaoticRaid() {
		return _chaoticRaid;
	}

	public void setChaoticRaid(Npc npc) {
		_chaoticRaid = npc;
	}

	private static class SingletonHolder {
		protected static final RaidSpawnManager INSTANCE = new RaidSpawnManager();
	}
}
