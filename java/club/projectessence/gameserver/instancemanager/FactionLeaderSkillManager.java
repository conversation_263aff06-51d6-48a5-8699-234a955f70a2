package club.projectessence.gameserver.instancemanager;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.logging.Logger;

import club.projectessence.gameserver.data.xml.FactionLeaderSkillData;
import club.projectessence.gameserver.data.xml.SkillData;
import club.projectessence.gameserver.enums.Faction;
import club.projectessence.gameserver.instancemanager.FactionLeaderManager;
import club.projectessence.gameserver.instancemanager.FactionLeaderProgressionManager;
import club.projectessence.gameserver.instancemanager.FactionWarManager;
import club.projectessence.gameserver.enums.LeadershipSkill;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.model.faction.FactionLeaderProgression;
import club.projectessence.gameserver.model.faction.FactionLeaderSkillTemplate;
import club.projectessence.gameserver.model.skills.Skill;

/**
 * Manager for faction leader skill stat bonuses.
 * Handles applying and removing stat bonuses based on learned skills.
 */
public class FactionLeaderSkillManager
{
	private static final Logger LOGGER = Logger.getLogger(FactionLeaderSkillManager.class.getName());
	
	// Cache for active leadership skills per player
	private final Map<Integer, List<Integer>> _activeLeadershipSkills = new ConcurrentHashMap<>();
	
	protected FactionLeaderSkillManager()
	{
		LOGGER.info("FactionLeaderSkillManager: Initialized.");
	}
	
	public static FactionLeaderSkillManager getInstance()
	{
		return SingletonHolder.INSTANCE;
	}
	
	/**
	 * Apply all skill bonuses for a faction leader
	 */
	public void applySkillBonuses(PlayerInstance player)
	{
		if (player == null) return;
		
		// Clear existing bonuses first
		removeSkillBonuses(player);
		
		// Get player's progression
		FactionLeaderProgression progression = FactionLeaderProgressionManager.getInstance().getOrCreateProgression(player);
		if (progression == null) return;
		
		// Check if player is faction leader
		boolean isFactionLeader = FactionLeaderManager.getInstance().isLeader(player);
		boolean isInFactionWar = FactionWarManager.getInstance().isWarActive();

		// Apply skills for each learned skill
		for (LeadershipSkill skill : progression.getLearnedSkills())
		{
			FactionLeaderSkillTemplate template = FactionLeaderSkillData.getInstance().getSkill(skill.name());
			if (template != null && template.getSkillId() > 0)
			{
				applyLeadershipSkill(player, template, isFactionLeader, isInFactionWar);
			}
		}
		
		// Recalculate player stats
		player.getStat().recalculateStats(true);
	}
	
	/**
	 * Remove all skill bonuses for a player
	 */
	public void removeSkillBonuses(PlayerInstance player)
	{
		if (player == null) return;

		List<Integer> activeSkills = _activeLeadershipSkills.remove(player.getObjectId());
		if (activeSkills != null && !activeSkills.isEmpty())
		{
			// Remove each skill
			for (Integer skillId : activeSkills)
			{
				player.removeSkill(skillId, false);
			}

			// Recalculate stats to apply changes
			player.getStat().recalculateStats(true);
		}
	}
	
	/**
	 * Apply leadership skill to player
	 */
	private void applyLeadershipSkill(PlayerInstance player, FactionLeaderSkillTemplate template,
		boolean isFactionLeader, boolean isInFactionWar)
	{
		// Check if conditions are met for this skill
		if (!shouldApplySkill(template, isFactionLeader, isInFactionWar))
		{
			return;
		}

		// Get the actual skill from SkillData
		Skill skill = SkillData.getInstance().getSkill(template.getSkillId(), template.getSkillLevel());
		if (skill == null)
		{
			LOGGER.warning("Leadership skill not found: ID=" + template.getSkillId() + " Level=" + template.getSkillLevel());
			return;
		}

		// Add skill to player
		player.addSkill(skill, false);

		// Store for removal later
		List<Integer> activeSkills = _activeLeadershipSkills.computeIfAbsent(player.getObjectId(), k -> new ArrayList<>());
		if (!activeSkills.contains(template.getSkillId()))
		{
			activeSkills.add(template.getSkillId());
		}
	}

	/**
	 * Check if skill should be applied based on conditions
	 */
	private boolean shouldApplySkill(FactionLeaderSkillTemplate template, boolean isFactionLeader, boolean isInFactionWar)
	{
		// Auto-learn skills are always applied if player is faction leader
		if (template.isAutoLearn())
		{
			return isFactionLeader;
		}

		// Prestige skills require faction leader status
		if (template.isPrestigeRequired())
		{
			return isFactionLeader;
		}

		// Regular skills require faction leader status
		return isFactionLeader;
	}

	/**
	 * Check if a condition is met
	 */
	private boolean isConditionMet(String condition, boolean isFactionLeader, boolean isInFactionWar)
	{
		switch (condition.toUpperCase())
		{
			case "FACTION_LEADER":
				return isFactionLeader;
			case "FACTION_WAR":
				return isInFactionWar && isFactionLeader;
			case "FACTION_ACTIVITY":
				return isFactionLeader; // Simplified - could be more complex
			case "ALWAYS":
				return true;
			default:
				return false;
		}
	}
	


	/**
	 * Get active leadership skills for a player
	 */
	public List<Integer> getActiveLeadershipSkills(int objectId)
	{
		return _activeLeadershipSkills.getOrDefault(objectId, new ArrayList<>());
	}
	
	/**
	 * Update skill bonuses when faction leader status changes
	 */
	public void onFactionLeaderStatusChanged(PlayerInstance player)
	{
		applySkillBonuses(player);
	}
	
	/**
	 * Update skill bonuses when faction war status changes
	 */
	public void onFactionWarStatusChanged()
	{
		// Update all faction leaders
		for (Faction faction : Faction.values())
		{
			if (faction == Faction.NONE) continue;
			
			PlayerInstance leader = FactionLeaderManager.getInstance().getLeader(faction);
			if (leader != null)
			{
				applySkillBonuses(leader);
			}
		}
	}
	
	/**
	 * Update skill bonuses when a skill is learned
	 */
	public void onSkillLearned(PlayerInstance player, LeadershipSkill skill)
	{
		applySkillBonuses(player);
		player.sendMessage("✨ Skill bonuses applied: " + skill.getName());
	}
	
	private static class SingletonHolder
	{
		protected static final FactionLeaderSkillManager INSTANCE = new FactionLeaderSkillManager();
	}
}
