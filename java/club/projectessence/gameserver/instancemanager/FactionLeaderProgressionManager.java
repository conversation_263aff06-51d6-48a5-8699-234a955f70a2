package club.projectessence.gameserver.instancemanager;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ScheduledFuture;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.List;
import java.util.ArrayList;
import java.util.Set;
import java.util.HashSet;

import club.projectessence.commons.concurrent.ThreadPool;
import club.projectessence.commons.database.DatabaseFactory;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.model.faction.FactionLeaderProgression;
import club.projectessence.gameserver.model.faction.FactionLeaderTaskProgress;
import club.projectessence.gameserver.enums.Faction;
import club.projectessence.gameserver.enums.LeadershipLevel;
import club.projectessence.gameserver.enums.LeadershipAchievement;
import club.projectessence.gameserver.enums.LeadershipSkill;
import club.projectessence.gameserver.enums.LeadershipTask;
import club.projectessence.gameserver.network.serverpackets.NpcHtmlMessage;

/**
 * Manager for faction leader progression system.
 */
public class FactionLeaderProgressionManager
{
	private static final Logger LOGGER = Logger.getLogger(FactionLeaderProgressionManager.class.getName());
	
	// Cache for progression data
	private final Map<Integer, FactionLeaderProgression> _progressionCache = new ConcurrentHashMap<>();
	private final Map<Integer, Map<LeadershipTask, FactionLeaderTaskProgress>> _taskCache = new ConcurrentHashMap<>();
	
	// Scheduled tasks
	private ScheduledFuture<?> _saveTask;
	private ScheduledFuture<?> _resetTask;
	
	protected FactionLeaderProgressionManager()
	{
		load();
		startScheduledTasks();
		LOGGER.info("FactionLeaderProgressionManager: Initialized.");
	}
	
	public static FactionLeaderProgressionManager getInstance()
	{
		return SingletonHolder.INSTANCE;
	}
	
	private static class SingletonHolder
	{
		protected static final FactionLeaderProgressionManager INSTANCE = new FactionLeaderProgressionManager();
	}
	
	// Loading and Saving
	private void load()
	{
		loadProgressionData();
		loadTaskData();
	}
	
	private void loadProgressionData()
	{
		try (Connection con = DatabaseFactory.getConnection();
			 PreparedStatement ps = con.prepareStatement("SELECT * FROM faction_leader_progression"))
		{
			try (ResultSet rs = ps.executeQuery())
			{
				while (rs.next())
				{
					int objId = rs.getInt("obj_id");
					int faction = rs.getInt("faction");
					
					FactionLeaderProgression progression = new FactionLeaderProgression(objId, faction);
					progression.setLeadershipPoints(rs.getInt("leadership_points"));
					progression.setLeadershipLevel(rs.getInt("leadership_level"));
					progression.setPrivilegeLevel(rs.getInt("privilege_level"));
					progression.setPrestigeRank(rs.getInt("prestige_rank"));
					progression.setTotalTerms(rs.getInt("total_terms"));
					
					// Load achievements
					String achievementsStr = rs.getString("achievements");
					if (achievementsStr != null && !achievementsStr.isEmpty())
					{
						String[] achievementNames = achievementsStr.split(",");
						for (String name : achievementNames)
						{
							try
							{
								LeadershipAchievement achievement = LeadershipAchievement.valueOf(name.trim());
								progression.getAchievements().add(achievement);
							}
							catch (IllegalArgumentException e)
							{
								LOGGER.warning("Invalid achievement name: " + name);
							}
						}
					}
					
					// Load learned skills
					String skillsStr = rs.getString("learned_skills");
					if (skillsStr != null && !skillsStr.isEmpty())
					{
						String[] skillNames = skillsStr.split(",");
						for (String name : skillNames)
						{
							try
							{
								LeadershipSkill skill = LeadershipSkill.valueOf(name.trim());
								progression.getLearnedSkills().add(skill);
							}
							catch (IllegalArgumentException e)
							{
								LOGGER.warning("Invalid skill name: " + name);
							}
						}
					}
					
					// Load skill points
					progression.addSkillPoints(LeadershipSkill.SkillTree.COMBAT, rs.getInt("skill_points_combat"));
					progression.addSkillPoints(LeadershipSkill.SkillTree.STRATEGIC, rs.getInt("skill_points_strategy"));
					progression.addSkillPoints(LeadershipSkill.SkillTree.SOCIAL, rs.getInt("skill_points_social"));
					
					_progressionCache.put(objId, progression);
				}
			}
		}
		catch (SQLException e)
		{
			LOGGER.log(Level.SEVERE, "Error loading faction leader progression data", e);
		}
		
		LOGGER.info("FactionLeaderProgressionManager: Loaded " + _progressionCache.size() + " progression records.");
	}
	
	private void loadTaskData()
	{
		try (Connection con = DatabaseFactory.getConnection();
			PreparedStatement ps = con.prepareStatement("SELECT * FROM faction_leader_tasks"))
		{
			try (ResultSet rs = ps.executeQuery())
			{
				while (rs.next())
				{
					int objId = rs.getInt("obj_id");
					String taskName = rs.getString("task_type");
					
					try
					{
						LeadershipTask task = LeadershipTask.valueOf(taskName);
						FactionLeaderTaskProgress progress = new FactionLeaderTaskProgress(
							objId, task, rs.getInt("progress"), rs.getBoolean("completed"),
							rs.getLong("reset_time"), rs.getBoolean("reward_claimed")
						);
						
						_taskCache.computeIfAbsent(objId, k -> new ConcurrentHashMap<>()).put(task, progress);
					}
					catch (IllegalArgumentException e)
					{
						LOGGER.warning("Invalid task name: " + taskName);
					}
				}
			}
		}
		catch (SQLException e)
		{
			LOGGER.log(Level.SEVERE, "Error loading faction leader task data", e);
		}
		
		LOGGER.info("FactionLeaderProgressionManager: Loaded task data for " + _taskCache.size() + " players.");
	}
	
	public void save()
	{
		saveProgressionData();
		saveTaskData();
	}
	
	private void saveProgressionData()
	{
		try (Connection con = DatabaseFactory.getConnection())
		{
			// Clear existing data
			try (PreparedStatement ps = con.prepareStatement("DELETE FROM faction_leader_progression"))
			{
				ps.executeUpdate();
			}
			
			// Insert current data
			try (PreparedStatement ps = con.prepareStatement(
				"INSERT INTO faction_leader_progression (obj_id, faction, leadership_points, leadership_level, " +
				"privilege_level, prestige_rank, total_terms, total_combat_lp, total_strategy_lp, total_social_lp, " +
				"total_economic_lp, achievements, skill_points_combat, skill_points_strategy, skill_points_social, " +
				"learned_skills, last_updated) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)"))
			{
				for (FactionLeaderProgression progression : _progressionCache.values())
				{
					ps.setInt(1, progression.getObjId());
					ps.setInt(2, progression.getFaction());
					ps.setInt(3, progression.getLeadershipPoints());
					ps.setInt(4, progression.getLeadershipLevel());
					ps.setInt(5, progression.getPrivilegeLevel());
					ps.setInt(6, progression.getPrestigeRank());
					ps.setInt(7, progression.getTotalTerms());
					ps.setInt(8, progression.getTotalCombatLP());
					ps.setInt(9, progression.getTotalStrategyLP());
					ps.setInt(10, progression.getTotalSocialLP());
					ps.setInt(11, progression.getTotalEconomicLP());
					
					// Achievements
					StringBuilder achievements = new StringBuilder();
					for (LeadershipAchievement achievement : progression.getAchievements())
					{
						if (achievements.length() > 0) achievements.append(",");
						achievements.append(achievement.name());
					}
					ps.setString(12, achievements.toString());
					
					ps.setInt(13, progression.getSkillPoints(LeadershipSkill.SkillTree.COMBAT));
					ps.setInt(14, progression.getSkillPoints(LeadershipSkill.SkillTree.STRATEGIC));
					ps.setInt(15, progression.getSkillPoints(LeadershipSkill.SkillTree.SOCIAL));
					
					// Learned skills
					StringBuilder skills = new StringBuilder();
					for (LeadershipSkill skill : progression.getLearnedSkills())
					{
						if (skills.length() > 0) skills.append(",");
						skills.append(skill.name());
					}
					ps.setString(16, skills.toString());
					
					ps.setLong(17, progression.getLastUpdated());
					
					ps.addBatch();
				}
				ps.executeBatch();
			}
		}
		catch (SQLException e)
		{
			LOGGER.log(Level.SEVERE, "Error saving faction leader progression data", e);
		}
	}
	
	private void saveTaskData()
	{
		try (Connection con = DatabaseFactory.getConnection())
		{
			// Clear existing data
			try (PreparedStatement ps = con.prepareStatement("DELETE FROM faction_leader_tasks"))
			{
				ps.executeUpdate();
			}
			
			// Insert current data
			try (PreparedStatement ps = con.prepareStatement(
				"INSERT INTO faction_leader_tasks (obj_id, task_type, task_category, progress, target, " +
				"completed, reset_time, reward_claimed) VALUES (?, ?, ?, ?, ?, ?, ?, ?)"))
			{
				for (Map.Entry<Integer, Map<LeadershipTask, FactionLeaderTaskProgress>> entry : _taskCache.entrySet())
				{
					for (FactionLeaderTaskProgress taskProgress : entry.getValue().values())
					{
						ps.setInt(1, taskProgress.getObjId());
						ps.setString(2, taskProgress.getTask().name());
						ps.setString(3, taskProgress.getTask().getCategory().name());
						ps.setInt(4, taskProgress.getProgress());
						ps.setInt(5, taskProgress.getTarget());
						ps.setBoolean(6, taskProgress.isCompleted());
						ps.setLong(7, taskProgress.getResetTime());
						ps.setBoolean(8, taskProgress.isRewardClaimed());
						
						ps.addBatch();
					}
				}
				ps.executeBatch();
			}
		}
		catch (SQLException e)
		{
			LOGGER.log(Level.SEVERE, "Error saving faction leader task data", e);
		}
	}
	
	// Progression Management
	public FactionLeaderProgression getProgression(int objId)
	{
		return _progressionCache.get(objId);
	}
	
	public FactionLeaderProgression getOrCreateProgression(PlayerInstance player)
	{
		FactionLeaderProgression progression = _progressionCache.get(player.getObjectId());
		if (progression == null)
		{
			progression = new FactionLeaderProgression(player.getObjectId(), player.getFaction().ordinal());
			_progressionCache.put(player.getObjectId(), progression);
		}
		return progression;
	}
	
	// LP Management
	public void awardLeadershipPoints(PlayerInstance player, int points, LeadershipAchievement.AchievementCategory category, String reason)
	{
		if (player == null || points <= 0) return;
		
		FactionLeaderProgression progression = getOrCreateProgression(player);
		int oldLevel = progression.getLeadershipLevel();
		
		progression.addLeadershipPoints(points, category);
		
		// Check for level up
		int newLevel = progression.getLeadershipLevel();
		if (newLevel > oldLevel)
		{
			onLevelUp(player, oldLevel, newLevel);
		}
		
		// Send notification
		player.sendMessage("Leadership Points +" + points + " (" + category.getName() + "): " + reason);
		player.sendMessage("Total LP: " + progression.getLeadershipPoints() + " | Level: " + newLevel);
	}
	
	private void onLevelUp(PlayerInstance player, int oldLevel, int newLevel)
	{
		LeadershipLevel level = LeadershipLevel.values()[newLevel - 1];
		
		player.sendMessage("🎉 Leadership Level Up! You are now " + level.getTitle() + "!");
		player.sendMessage("🔓 New privileges unlocked: " + level.getPrivilegeType().getName());
		
		// Award skill points
		FactionLeaderProgression progression = getProgression(player.getObjectId());
		if (progression != null)
		{
			int skillPoints = level.getSkillPointsAwarded();
			progression.addSkillPoints(LeadershipSkill.SkillTree.COMBAT, skillPoints / 3);
			progression.addSkillPoints(LeadershipSkill.SkillTree.STRATEGIC, skillPoints / 3);
			progression.addSkillPoints(LeadershipSkill.SkillTree.SOCIAL, skillPoints / 3);
			
			player.sendMessage("💎 Skill Points awarded: " + skillPoints + " (distributed across skill trees)");
		}
		
		// Broadcast to faction
		// TODO: Implement faction broadcast
	}
	
	// Achievement Management
	public boolean checkAndAwardAchievement(PlayerInstance player, LeadershipAchievement achievement, int currentValue)
	{
		FactionLeaderProgression progression = getProgression(player.getObjectId());
		if (progression == null || progression.hasAchievement(achievement)) return false;
		
		if (currentValue >= achievement.getRequirement())
		{
			if (progression.addAchievement(achievement))
			{
				player.sendMessage("🏆 Achievement Unlocked: " + achievement.getName() + "!");
				player.sendMessage("📜 " + achievement.getDescription());
				player.sendMessage("💎 Leadership Points +" + achievement.getLpReward());

				// Update skill bonuses if player is faction leader
				if (FactionLeaderManager.getInstance().isLeader(player))
				{
					FactionLeaderSkillManager.getInstance().applySkillBonuses(player);
				}
				return true;
			}
		}
		return false;
	}
	
	// Task Management
	public Map<LeadershipTask, FactionLeaderTaskProgress> getPlayerTasks(int objId)
	{
		return _taskCache.getOrDefault(objId, new ConcurrentHashMap<>());
	}
	
	public void updateTaskProgress(PlayerInstance player, LeadershipTask task, int progress)
	{
		Map<LeadershipTask, FactionLeaderTaskProgress> playerTasks = _taskCache.computeIfAbsent(player.getObjectId(), k -> new ConcurrentHashMap<>());
		
		FactionLeaderTaskProgress taskProgress = playerTasks.get(task);
		if (taskProgress == null)
		{
			taskProgress = new FactionLeaderTaskProgress(player.getObjectId(), task);
			playerTasks.put(task, taskProgress);
		}
		
		if (taskProgress.needsReset())
		{
			taskProgress.reset();
		}
		
		int oldProgress = taskProgress.getProgress();
		taskProgress.addProgress(progress);
		
		if (taskProgress.isCompleted() && oldProgress < taskProgress.getTarget())
		{
			player.sendMessage("✅ Task Completed: " + task.getName() + "!");
			player.sendMessage("🎁 Reward: " + task.getLpReward() + " Leadership Points (claim in task menu)");
		}
	}
	
	public boolean claimTaskReward(PlayerInstance player, LeadershipTask task)
	{
		Map<LeadershipTask, FactionLeaderTaskProgress> playerTasks = getPlayerTasks(player.getObjectId());
		FactionLeaderTaskProgress taskProgress = playerTasks.get(task);
		
		if (taskProgress != null && taskProgress.canClaimReward())
		{
			taskProgress.claimReward();
			awardLeadershipPoints(player, task.getLpReward(), LeadershipAchievement.AchievementCategory.LEADERSHIP, "Task: " + task.getName());
			return true;
		}
		return false;
	}
	
	// Scheduled Tasks
	private void startScheduledTasks()
	{
		// Save every 5 minutes
		_saveTask = ThreadPool.scheduleAtFixedRate(this::save, 300000, 300000);
		
		// Reset tasks every hour
		_resetTask = ThreadPool.scheduleAtFixedRate(this::resetExpiredTasks, 3600000, 3600000);
	}
	
	private void resetExpiredTasks()
	{
		for (Map<LeadershipTask, FactionLeaderTaskProgress> playerTasks : _taskCache.values())
		{
			for (FactionLeaderTaskProgress taskProgress : playerTasks.values())
			{
				if (taskProgress.needsReset())
				{
					taskProgress.reset();
				}
			}
		}
	}
	
	public void shutdown()
	{
		if (_saveTask != null)
		{
			_saveTask.cancel(false);
		}
		if (_resetTask != null)
		{
			_resetTask.cancel(false);
		}
		save();
		LOGGER.info("FactionLeaderProgressionManager: Shutdown completed.");
	}
}
