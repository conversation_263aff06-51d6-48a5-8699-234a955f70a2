/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.instancemanager;

import club.projectessence.commons.database.DatabaseFactory;
import club.projectessence.gameserver.model.StatSet;
import club.projectessence.gameserver.model.World;
import club.projectessence.gameserver.model.actor.Summon;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.model.effects.EffectFlag;
import club.projectessence.gameserver.model.holders.SkillHolder;
import club.projectessence.gameserver.model.itemcontainer.Inventory;
import club.projectessence.gameserver.model.zone.ZoneId;
import club.projectessence.gameserver.network.SystemMessageId;
import club.projectessence.gameserver.network.serverpackets.vengeance.ExPvpBookShareRevengeKillerLocation;
import club.projectessence.gameserver.network.serverpackets.vengeance.ExPvpBookShareRevengeList;
import club.projectessence.gameserver.network.serverpackets.vengeance.ExPvpBookShareRevengeNewRevengeInfo;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.logging.Logger;

/**
 * <AUTHOR>
 */
public class VengeanceManager {
	private static final String DELETE_VENGEANCES = "TRUNCATE TABLE player_vengeances";
	private static final String INSERT_VENGEANCES = "INSERT INTO player_vengeances (charId, type, killer_name, killer_clan, killer_level, killer_race, killer_class, victim_name, victim_clan, victim_level, victim_race, victim_class, was_shared, show_location_remaining, teleport_remaining, shared_teleport_remaining, kill_time, share_time) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
	private static final SkillHolder HIDE_SKILL = new SkillHolder(922, 1);
	private static final long VENGEANCE_DURATION = 6 * 60 * 60 * 1000;
	private static final int[] LOCATION_PRICE =
			{
					0,
					50000,
					100000,
					100000,
					200000
			};
	private static final int[] TELEPORT_PRICE =
			{
					10,
					50,
					100,
					100,
					200
			};
	private static final Map<Integer, List<VengeanceHolder>> _vengeances = new ConcurrentHashMap<>();
	private static final Logger LOGGER = Logger.getLogger(VengeanceManager.class.getName());

	protected VengeanceManager() {
		try (Connection con = DatabaseFactory.getConnection();
		     PreparedStatement ps = con.prepareStatement("SELECT * FROM player_vengeances")) {
			ResultSet rs = ps.executeQuery();
			while (rs.next()) {
				int charId = rs.getInt("charId");
				List<VengeanceHolder> vengeances = _vengeances.get(charId);
				if (vengeances == null) {
					vengeances = new CopyOnWriteArrayList<>();
				}

				StatSet killer = new StatSet();
				killer.set("name", rs.getString("killer_name"));
				killer.set("clan", rs.getString("killer_clan"));
				killer.set("level", rs.getInt("killer_level"));
				killer.set("race", rs.getInt("killer_race"));
				killer.set("class", rs.getInt("killer_class"));
				StatSet victim = new StatSet();
				victim.set("name", rs.getString("victim_name"));
				victim.set("clan", rs.getString("victim_clan"));
				victim.set("level", rs.getInt("victim_level"));
				victim.set("race", rs.getInt("victim_race"));
				victim.set("class", rs.getInt("victim_class"));
				vengeances.add(new VengeanceHolder(killer, victim, VengeanceType.values()[rs.getInt("type")], rs.getBoolean("was_shared"), rs.getInt("show_location_remaining"), rs.getInt("teleport_remaining"), rs.getInt("shared_teleport_remaining"), rs.getLong("kill_time"), rs.getLong("share_time")));
				_vengeances.put(charId, vengeances);
			}
		} catch (Exception e) {
			LOGGER.warning("Failed loading vengeances.");
			e.printStackTrace();
		}

	}

	public static VengeanceManager getInstance() {
		return SingletonHolder.INSTANCE;
	}

	public void save() {
		for (Entry<Integer, List<VengeanceHolder>> entry : _vengeances.entrySet()) {
			List<VengeanceHolder> vengeances = entry.getValue();
			if (vengeances != null) {
				List<VengeanceHolder> toRemove = new ArrayList<>();
				for (VengeanceHolder holder : vengeances) {
					if ((holder.getKillTime() != 0) && ((holder.getKillTime() + VENGEANCE_DURATION) < System.currentTimeMillis())) {
						toRemove.add(holder);
					} else if ((holder.getShareTime() != 0) && ((holder.getShareTime() + VENGEANCE_DURATION) < System.currentTimeMillis())) {
						toRemove.add(holder);
					}
				}
				for (VengeanceHolder holder : toRemove) {
					vengeances.remove(holder);
				}
			}
		}

		try (Connection con = DatabaseFactory.getConnection()) {
			try (PreparedStatement ps = con.prepareStatement(DELETE_VENGEANCES)) {
				ps.execute();
			}
			try (PreparedStatement ps = con.prepareStatement(INSERT_VENGEANCES)) {
				for (Entry<Integer, List<VengeanceHolder>> e : _vengeances.entrySet()) {
					List<VengeanceHolder> vengeances = e.getValue();
					if ((vengeances == null) || vengeances.isEmpty()) {
						continue;
					}
					for (VengeanceHolder holder : vengeances) {
						ps.clearParameters();
						ps.setInt(1, e.getKey());
						ps.setInt(2, holder.getType().ordinal());
						ps.setString(3, holder.getKillerName());
						ps.setString(4, holder.getKillerClanName());
						ps.setInt(5, holder.getKillerLevel());
						ps.setInt(6, holder.getKillerRaceId());
						ps.setInt(7, holder.getKillerClassId());
						ps.setString(8, holder.getVictimName());
						ps.setString(9, holder.getVictimClanName());
						ps.setInt(10, holder.getVictimLevel());
						ps.setInt(11, holder.getVictimRaceId());
						ps.setInt(12, holder.getVictimClassId());
						ps.setBoolean(13, holder.wasShared());
						ps.setInt(14, holder.getShowLocationRemaining());
						ps.setInt(15, holder.getTeleportRemaining());
						ps.setInt(16, holder.getSharedTeleportRemaining());
						ps.setLong(17, holder.getKillTime());
						ps.setLong(18, holder.getShareTime());
						ps.addBatch();
					}
				}
				ps.executeBatch();
			}
		} catch (Exception e) {
			LOGGER.warning(getClass().getSimpleName() + " Error while saving vengeances.");
			e.printStackTrace();
		}

	}

	public void addNewKill(PlayerInstance victim, PlayerInstance killer) {
		try {
			final int victimObjId = victim.getObjectId();
			List<VengeanceHolder> vengeances = _vengeances.get(victimObjId);
			if (vengeances == null) {
				vengeances = new CopyOnWriteArrayList<>();
			}
			boolean exists = false;
			final List<VengeanceHolder> toRemove = new ArrayList<>();
			for (VengeanceHolder holder : vengeances) {
				if ((holder.getKillTime() != 0) && ((holder.getKillTime() + VENGEANCE_DURATION) < System.currentTimeMillis())) {
					toRemove.add(holder);
				} else if ((holder.getShareTime() != 0) && ((holder.getShareTime() + VENGEANCE_DURATION) < System.currentTimeMillis())) {
					toRemove.add(holder);
				} else if (holder.getKillerName().equals(killer.getName())) {
					exists = true;
				}
			}
			vengeances.removeAll(toRemove);
			if (exists) {
				return;
			}
			vengeances.add(new VengeanceHolder(killer, victim, VengeanceType.VENGEANCE));
			_vengeances.put(victimObjId, vengeances);
			victim.sendPacket(new ExPvpBookShareRevengeNewRevengeInfo(victim.getName(), killer.getName(), VengeanceType.VENGEANCE));
			victim.sendPacket(new ExPvpBookShareRevengeList(victim));
		} catch (Exception e) {
			LOGGER.warning(getClass().getSimpleName() + ": Failed adding vengeance kill [Victim: " + victim.getName() + "] [Killer: " + killer.getName() + "]");
			e.printStackTrace();
		}
	}

	public void locateKiller(PlayerInstance player, String killerName) {
		final List<VengeanceHolder> vengeances = _vengeances.get(player.getObjectId());
		if (vengeances == null) {
			return;
		}
		VengeanceHolder holder = null;
		for (VengeanceHolder vh : vengeances) {
			if (vh.getKillerName().equals(killerName)) {
				holder = vh;
				break;
			}
		}
		if (holder == null) {
			return;
		}
		PlayerInstance killer = World.getInstance().getPlayer(killerName);
		if ((killer == null) || (killer.isOnline() == false)) {
			player.sendPacket(SystemMessageId.THE_ENEMY_IS_OFFLINE_AND_CANNOT_BE_FOUND_RIGHT_NOW);
			return;
		}
		if (killer.isInsideZone(ZoneId.PEACE) || killer.isInInstance() || killer.isInSpecialHuntingZone() || killer.isInsideZone(ZoneId.SIEGE)) {
			player.sendPacket(SystemMessageId.THE_CHARACTER_IS_IN_A_LOCATION_WHERE_IT_IS_IMPOSSIBLE_TO_USE_THIS_FUNCTION);
			return;
		}
		if (player.isDead() || player.isInInstance() || player.isInSpecialHuntingZone() || player.isInsideZone(ZoneId.SIEGE)) {
			player.sendPacket(SystemMessageId.THE_CHARACTER_IS_IN_A_LOCATION_WHERE_IT_IS_IMPOSSIBLE_TO_USE_THIS_FUNCTION);
			return;
		}
		if (holder.getShowLocationRemaining() > 0) {
			int price = LOCATION_PRICE[Math.min(5 - holder.getShowLocationRemaining(), LOCATION_PRICE.length - 1)];
			if (killer.getEinhasadOverseeingLevel() > 0) {
				price = 0;
			}
			if (price == 0 || player.reduceAdena("Vengeance find location", price, player, true)) {
				holder.setShowLocationRemaining(holder.getShowLocationRemaining() - 1);
				player.sendPacket(new ExPvpBookShareRevengeKillerLocation(killer));
				player.sendPacket(new ExPvpBookShareRevengeList(player));
			}
		}
	}

	private boolean checkTeleportConditions(PlayerInstance player, PlayerInstance killer) {
		if ((killer == null) || (killer.isOnline() == false)) {
			player.sendPacket(SystemMessageId.THE_ENEMY_IS_OFFLINE_AND_CANNOT_BE_FOUND_RIGHT_NOW);
			return false;
		}
		if (killer.isTeleporting() || killer.isInsideZone(ZoneId.PEACE) || killer.isInInstance() || killer.isInSpecialHuntingZone() || killer.isInsideZone(ZoneId.SIEGE) || killer.isInsideZone(ZoneId.NO_BOOKMARK)) {
			player.sendPacket(SystemMessageId.THE_CHARACTER_IS_IN_A_LOCATION_WHERE_IT_IS_IMPOSSIBLE_TO_USE_THIS_FUNCTION_2);
			return false;
		}
		if (killer.isDead() || killer.isInInstance() || killer.isInSpecialHuntingZone() || killer.isInsideZone(ZoneId.SIEGE)) {
			player.sendPacket(SystemMessageId.THE_CHARACTER_IS_IN_A_LOCATION_WHERE_IT_IS_IMPOSSIBLE_TO_USE_THIS_FUNCTION);
			return false;
		}

		// TOI (Check is needed as no_bookmark zone is removed)
		if (ZoneManager.getInstance().getZoneById(70055).isCharacterInZone(killer)) {
			player.sendPacket(SystemMessageId.THE_CHARACTER_IS_IN_A_LOCATION_WHERE_IT_IS_IMPOSSIBLE_TO_USE_THIS_FUNCTION);
			return false;
		}

		if (player.isInInstance() || player.isInSpecialHuntingZone() || player.isInsideZone(ZoneId.SIEGE)) {
			player.sendPacket(SystemMessageId.THE_CHARACTER_IS_IN_A_LOCATION_WHERE_IT_IS_IMPOSSIBLE_TO_USE_THIS_FUNCTION);
			return false;
		}
		if (player.isDead()) {
			player.sendPacket(SystemMessageId.YOU_CANNOT_USE_TELEPORT_WHILE_YOU_ARE_DEAD);
			return false;
		}
		if (player.isInCombat()) {
			player.sendPacket(SystemMessageId.YOU_CANNOT_TELEPORT_IN_COMBAT);
			return false;
		}
		if (player.hasBlockActions() || player.isAffected(EffectFlag.FEAR)) {
			return false;
		}

		if (player.isInsideZone(ZoneId.UNDERGROUND_LABYRINTH)) {
			player.sendPacket(SystemMessageId.THHE_FUNCTION_IS_NOT_AVAILABLE_IN_THE_UNDERGROUND_LABYRINTH);
			return false;
		}
		return true;
	}

	public void teleportToKiller(PlayerInstance player, String killerName) {
		List<VengeanceHolder> vengeances = _vengeances.get(player.getObjectId());
		if (vengeances == null) {
			return;
		}
		VengeanceHolder holder = null;
		for (VengeanceHolder vh : vengeances) {
			if (vh.getKillerName().equals(killerName)) {
				holder = vh;
				break;
			}
		}
		if (holder == null) {
			return;
		}
		if (holder.wasShared()) {
			return;
		}
		PlayerInstance killer = World.getInstance().getPlayer(killerName);
		if (!checkTeleportConditions(player, killer)) {
			return;
		}

		if (holder.getTeleportRemaining() > 0) {
			int price = TELEPORT_PRICE[Math.min(5 - holder.getTeleportRemaining(), TELEPORT_PRICE.length - 1)];
			if (killer.getEinhasadOverseeingLevel() > 0) {
				price = 0;
			}
			if (player.destroyItemByItemId("Vengeance Teleport", Inventory.LCOIN_ID, price, player, true)) {
				holder.setTeleportRemaining(holder.getTeleportRemaining() - 1);
				HIDE_SKILL.getSkill().applyEffects(player, player);
				for (Summon s : player.getServitorsAndPets()) {
					HIDE_SKILL.getSkill().applyEffects(s, s);
				}
				player.teleToLocation(killer.getLocation());
			}
		}
	}

	public void teleportToSharedKiller(PlayerInstance player, String victimName, String killerName) {
		if (player.getName().equals(killerName)) {
			return;
		}
		List<VengeanceHolder> vengeances = _vengeances.get(player.getObjectId());
		if (vengeances == null) {
			return;
		}
		VengeanceHolder holder = null;
		for (VengeanceHolder vh : vengeances) {
			if (vh.getVictimName().equals(victimName) && vh.getKillerName().equals(killerName)) {
				holder = vh;
				break;
			}
		}
		if (holder == null) {
			return;
		}
		if (!holder.wasShared()) {
			return;
		}
		PlayerInstance killer = World.getInstance().getPlayer(killerName);
		if (!checkTeleportConditions(player, killer)) {
			return;
		}

		if (holder.getSharedTeleportRemaining() > 0) {
			int price = 100;
			if (player.destroyItemByItemId("Vengeance Teleport", Inventory.LCOIN_ID, price, player, true)) {
				holder.setSharedTeleportRemaining(holder.getSharedTeleportRemaining() - 1);
				HIDE_SKILL.getSkill().applyEffects(player, player);
				for (Summon s : player.getServitorsAndPets()) {
					HIDE_SKILL.getSkill().applyEffects(s, s);
				}
				player.teleToLocation(killer.getLocation());
			}
		}
	}

	public void requestHelp(PlayerInstance player, PlayerInstance killer, int type) {
		List<VengeanceHolder> vengeances = _vengeances.get(player.getObjectId());
		if (vengeances == null) {
			return;
		}
		VengeanceHolder holder = null;
		for (VengeanceHolder vh : vengeances) {
			if (vh.getKillerName().equals(killer.getName())) {
				holder = vh;
				break;
			}
		}
		if (holder == null) {
			return;
		}
		if (!holder.wasShared()) {
			int price = 100_000;
			if (player.reduceAdena("Vengeance request help", price, player, true)) {
				holder.setShared(true);
				holder.setType(VengeanceType.OWN_HELP_REQUEST);
				holder.setShareTime(System.currentTimeMillis());

				List<PlayerInstance> targets = type == 1 ? (player.getClan() == null ? new ArrayList<>() : player.getClan().getOnlineMembers(player.getObjectId())) : type == 2 ? RankManager.getInstance().getTop50OnlinePlayers() : new ArrayList<>();
				for (PlayerInstance target : targets) {
					if (target == killer) {
						continue;
					}
					List<VengeanceHolder> target_vengeances = _vengeances.get(target.getObjectId());
					if (target_vengeances == null) {
						target_vengeances = new ArrayList<>();
					}
					for (VengeanceHolder temp : target_vengeances) {
						if (temp.getVictimName().equals(player.getName()) && temp.getKillerName().equals(killer.getName())) {
							if (temp != holder) {
								target_vengeances.remove(temp);
								break;
							}
						}
					}
					target_vengeances.add(new VengeanceHolder(killer, player, VengeanceType.HELP_REQUEST, 1, holder.getKillTime(), System.currentTimeMillis()));
					_vengeances.put(target.getObjectId(), target_vengeances);

					target.sendPacket(new ExPvpBookShareRevengeNewRevengeInfo(player.getName(), killer.getName(), VengeanceType.HELP_REQUEST));
					target.sendPacket(new ExPvpBookShareRevengeList(target));
				}
			}
			player.sendPacket(new ExPvpBookShareRevengeList(player));
		}
	}

	public List<VengeanceHolder> getVengeances(PlayerInstance player) {
		return _vengeances.get(player.getObjectId());
	}

	public enum VengeanceType {
		OWN_HELP_REQUEST,
		VENGEANCE,
		HELP_REQUEST;
	}

	private static class SingletonHolder {
		protected static final VengeanceManager INSTANCE = new VengeanceManager();
	}

	public class VengeanceHolder {
		private final String _killerName;
		private final String _killerClanName;
		private final int _killerLevel;
		private final int _killerRaceId;
		private final int _killerClassId;
		private final long _killTime;
		private final String _victimName;
		private final String _victimClanName;
		private final int _victimLevel;
		private final int _victimRaceId;
		private final int _victimClassId;
		private VengeanceType _type;
		private boolean _wasShared;
		private long _shareTime;
		private int _showLocationRemaining;
		private int _teleportRemaining;
		private int _sharedTeleportRemaining;

		protected VengeanceHolder(PlayerInstance killer, PlayerInstance victim, VengeanceType type) {
			_type = type;
			_wasShared = false;
			_killerName = killer.getName();
			_killerClanName = killer.getClan() == null ? "" : killer.getClan().getName();
			_killerLevel = killer.getLevel();
			_killerRaceId = killer.getRace().ordinal();
			_killerClassId = killer.getClassId().getId();
			_killTime = System.currentTimeMillis();
			_shareTime = 0;
			_showLocationRemaining = 5;
			_teleportRemaining = 5;
			_sharedTeleportRemaining = 1;
			_victimName = victim.getName();
			_victimClanName = victim.getClan() == null ? "" : victim.getClan().getName();
			_victimLevel = victim.getLevel();
			_victimRaceId = victim.getRace().ordinal();
			_victimClassId = victim.getClassId().getId();
		}

		/**
		 * Used for help request
		 *
		 * @param killer
		 * @param victim
		 * @param type
		 * @param sharedTeleportRemaining
		 * @param killTime
		 * @param shareTime
		 */
		protected VengeanceHolder(PlayerInstance killer, PlayerInstance victim, VengeanceType type, int sharedTeleportRemaining, long killTime, long shareTime) {
			_type = type;
			_wasShared = true;
			_killerName = killer.getName();
			_killerClanName = killer.getClan() == null ? "" : killer.getClan().getName();
			_killerLevel = killer.getLevel();
			_killerRaceId = killer.getRace().ordinal();
			_killerClassId = killer.getClassId().getId();
			_killTime = killTime;
			_shareTime = shareTime;
			_showLocationRemaining = 0;
			_teleportRemaining = 0;
			_sharedTeleportRemaining = sharedTeleportRemaining;
			_victimName = victim.getName();
			_victimClanName = victim.getClan() == null ? "" : victim.getClan().getName();
			_victimLevel = victim.getLevel();
			_victimRaceId = victim.getRace().ordinal();
			_victimClassId = victim.getClassId().getId();
		}

		protected VengeanceHolder(StatSet killer, StatSet victim, VengeanceType type, boolean wasShared, int showLocationRemaining, int teleportRemaining, int sharedTeleportRemaining, long killTime, long shareTime) {
			_type = type;
			_wasShared = wasShared;
			_killerName = killer.getString("name");
			_killerClanName = killer.getString("clan");
			_killerLevel = killer.getInt("level");
			_killerRaceId = killer.getInt("race");
			_killerClassId = killer.getInt("class");
			_killTime = killTime;
			_shareTime = shareTime;
			_showLocationRemaining = showLocationRemaining;
			_teleportRemaining = teleportRemaining;
			_sharedTeleportRemaining = sharedTeleportRemaining;
			_victimName = victim.getString("name");
			_victimClanName = victim.getString("clan");
			_victimLevel = victim.getInt("level");
			_victimRaceId = victim.getInt("race");
			_victimClassId = victim.getInt("class");
		}

		public VengeanceType getType() {
			return _type;
		}

		public void setType(VengeanceType type) {
			_type = type;
		}

		public boolean wasShared() {
			return _wasShared;
		}

		public void setShared(boolean wasShared) {
			_wasShared = wasShared;
		}

		public String getKillerName() {
			return _killerName;
		}

		public String getKillerClanName() {
			return _killerClanName;
		}

		public int getKillerLevel() {
			return _killerLevel;
		}

		public int getKillerRaceId() {
			return _killerRaceId;
		}

		public int getKillerClassId() {
			return _killerClassId;
		}

		public long getKillTime() {
			return _killTime;
		}

		public long getShareTime() {
			return _shareTime;
		}

		public void setShareTime(long shareTime) {
			_shareTime = shareTime;
		}

		public int getShowLocationRemaining() {
			return _showLocationRemaining;
		}

		public void setShowLocationRemaining(int count) {
			_showLocationRemaining = count;
		}

		public int getTeleportRemaining() {
			return _teleportRemaining;
		}

		public void setTeleportRemaining(int count) {
			_teleportRemaining = count;
		}

		public int getSharedTeleportRemaining() {
			return _sharedTeleportRemaining;
		}

		public void setSharedTeleportRemaining(int count) {
			_sharedTeleportRemaining = count;
		}

		public String getVictimName() {
			return _victimName;
		}

		public String getVictimClanName() {
			return _victimClanName;
		}

		public int getVictimLevel() {
			return _victimLevel;
		}

		public int getVictimRaceId() {
			return _victimRaceId;
		}

		public int getVictimClassId() {
			return _victimClassId;
		}
	}
}
