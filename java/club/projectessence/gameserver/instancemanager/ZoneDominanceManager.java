package club.projectessence.gameserver.instancemanager;

import club.projectessence.Config;
import club.projectessence.gameserver.enums.Faction;
import club.projectessence.gameserver.model.World;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.model.zone.type.FactionZone;
import club.projectessence.gameserver.util.Broadcast;


import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.logging.Logger;

/**
 * Zone Dominance Manager
 * Handles zone control bonuses and rewards for factions
 * 
 * <AUTHOR>
 */
public class ZoneDominanceManager
{
    private static final Logger LOGGER = Logger.getLogger(ZoneDominanceManager.class.getName());
    private static final ZoneDominanceManager INSTANCE = new ZoneDominanceManager();
    
    // Dominance tiers
    public enum DominanceTier
    {
        NONE(0, 19, "No Control", "808080"),
        MINOR(20, 49, "Minor Control", "CD853F"),
        MAJOR(50, 69, "Major Control", "C0C0C0"),
        TOTAL(70, 100, "Total Dominance", "FFD700");
        
        private final int minPercent;
        private final int maxPercent;
        private final String name;
        private final String color;
        
        DominanceTier(int minPercent, int maxPercent, String name, String color)
        {
            this.minPercent = minPercent;
            this.maxPercent = maxPercent;
            this.name = name;
            this.color = color;
        }
        
        public int getMinPercent() { return minPercent; }
        public int getMaxPercent() { return maxPercent; }
        public String getName() { return name; }
        public String getColor() { return color; }
        
        public static DominanceTier getTierByPercent(double percent)
        {
            for (DominanceTier tier : values())
            {
                if (percent >= tier.minPercent && percent <= tier.maxPercent)
                {
                    return tier;
                }
            }
            return NONE;
        }
    }
    
    // Current dominance status
    private final ConcurrentHashMap<Faction, DominanceTier> currentDominance = new ConcurrentHashMap<>();
    private final ConcurrentHashMap<Faction, Double> zoneControlPercentage = new ConcurrentHashMap<>();
    
    // Players with active bonuses (kept for compatibility)
    private final CopyOnWriteArrayList<PlayerInstance> playersWithBonuses = new CopyOnWriteArrayList<>();

    private ZoneDominanceManager()
    {
        // Initialize dominance status
        currentDominance.put(Faction.FIRE, DominanceTier.NONE);
        currentDominance.put(Faction.WATER, DominanceTier.NONE);
        zoneControlPercentage.put(Faction.FIRE, 0.0);
        zoneControlPercentage.put(Faction.WATER, 0.0);
    }
    
    public static ZoneDominanceManager getInstance()
    {
        return INSTANCE;
    }
    
    /**
     * Calculate zone control percentage for a faction
     */
    public double getFactionZoneControlPercentage(Faction faction)
    {
        if (faction == null || faction == Faction.NONE)
        {
            return 0.0;
        }
        
        List<FactionZone> allZones = new ArrayList<>(ZoneManager.getInstance().getAllZones(FactionZone.class));
        if (allZones.isEmpty())
        {
            return 0.0;
        }
        
        long controlledZones = allZones.stream()
            .filter(zone -> zone.getControllingFaction() == faction)
            .count();

        double percentage = (controlledZones * 100.0) / allZones.size();

        zoneControlPercentage.put(faction, percentage);

        return percentage;
    }
    
    /**
     * Get current dominance tier for a faction
     */
    public DominanceTier getFactionDominanceTier(Faction faction)
    {
        double percentage = getFactionZoneControlPercentage(faction);
        DominanceTier tier = DominanceTier.getTierByPercent(percentage);
        currentDominance.put(faction, tier);
        return tier;
    }
    
    /**
     * Update dominance status for all factions
     */
    public void updateDominanceStatus()
    {
        DominanceTier oldFireTier = currentDominance.get(Faction.FIRE);
        DominanceTier oldWaterTier = currentDominance.get(Faction.WATER);
        
        DominanceTier newFireTier = getFactionDominanceTier(Faction.FIRE);
        DominanceTier newWaterTier = getFactionDominanceTier(Faction.WATER);
        
        // Check for tier changes
        if (oldFireTier != newFireTier)
        {
            onDominanceTierChange(Faction.FIRE, oldFireTier, newFireTier);
        }
        
        if (oldWaterTier != newWaterTier)
        {
            onDominanceTierChange(Faction.WATER, oldWaterTier, newWaterTier);
        }
        
        // Update bonuses for all players
        updateAllPlayerBonuses();
    }
    
    /**
     * Handle dominance tier change
     */
    private void onDominanceTierChange(Faction faction, DominanceTier oldTier, DominanceTier newTier)
    {
        String factionName = faction == Faction.FIRE ? Config.FACTION_FIRE_TEAM_NAME : Config.FACTION_WATER_TEAM_NAME;
        String message = String.format("%s faction has achieved %s! (%.1f%% zone control)", 
            factionName, newTier.getName(), zoneControlPercentage.get(faction));
            
        Broadcast.toAllOnlinePlayers(message, true);
        
        if (Config.LOG_FACTION_DETAILS)
        {
            LOGGER.info("ZoneDominance: " + faction + " tier changed from " + oldTier + " to " + newTier);
        }
    }
    
    /**
     * Update bonuses for all players
     * Note: Bonuses are automatically applied when rewards are calculated
     * This method just logs the status update
     */
    private void updateAllPlayerBonuses()
    {
        if (Config.LOG_FACTION_DETAILS)
        {
            int firePlayerCount = 0;
            int waterPlayerCount = 0;

            for (PlayerInstance player : World.getInstance().getPlayers())
            {
                if (player != null && player.isOnline() && player.getFaction() != Faction.NONE)
                {
                    if (player.getFaction() == Faction.FIRE)
                    {
                        firePlayerCount++;
                    }
                    else if (player.getFaction() == Faction.WATER)
                    {
                        waterPlayerCount++;
                    }
                }
            }

            DominanceTier fireTier = getFactionDominanceTier(Faction.FIRE);
            DominanceTier waterTier = getFactionDominanceTier(Faction.WATER);

            LOGGER.info("Zone Dominance updated - Fire: " + fireTier.getName() + " (" + firePlayerCount + " players), " +
                "Water: " + waterTier.getName() + " (" + waterPlayerCount + " players)");
        }
    }
    
    /**
     * Apply dominance bonuses to a player
     * Note: Bonuses are automatically applied when rewards are calculated
     * This method is kept for compatibility but doesn't need to do anything
     */
    public void applyDominanceBonuses(PlayerInstance player)
    {
        if (player == null || player.getFaction() == Faction.NONE)
        {
            return;
        }

        // Bonuses are automatically applied in:
        // - GvE Points: FactionWarManager.addPersonalPoints() and GveRewardManager.calculateGvESkillPoints()
        // - Adena Drop Rate: NpcTemplate.calculateDrop()
        // - L-Coin Drop Rate: Attackable.java and DropManager
        // - EXP/SP: PlayerStat.addExpAndSp()

        if (!playersWithBonuses.contains(player))
        {
            playersWithBonuses.add(player);
        }

        if (Config.LOG_FACTION_DETAILS)
        {
            DominanceTier tier = getFactionDominanceTier(player.getFaction());
            LOGGER.info("Zone Dominance status for " + player.getName() + ": " + tier.getName() +
                " (Bonuses applied automatically when earning rewards)");
        }
    }

    /**
     * Remove dominance bonuses from a player
     * Note: Bonuses are automatically calculated based on current faction status
     * This method is kept for compatibility but doesn't need to do anything
     */
    public void removeDominanceBonuses(PlayerInstance player)
    {
        if (player == null)
        {
            return;
        }

        // Bonuses are automatically calculated based on current faction status
        // No need to manually remove anything

        playersWithBonuses.remove(player);
    }
    
    /**
     * Get dominance bonuses HTML for Community Board
     */
    public String getDominanceBonusesHtml(Faction faction)
    {
        DominanceTier tier = getFactionDominanceTier(faction);
        StringBuilder html = new StringBuilder();
        
        html.append("<font color=\"").append(tier.getColor()).append("\">").append(tier.getName()).append("</font><br>");
        
        switch (tier)
        {
            case MINOR:
                html.append("<font color=\"00FF00\">• +").append(Config.ZONE_DOMINANCE_MINOR_GVE_BONUS).append("% GvE Points</font><br>");
                html.append("<font color=\"00FF00\">• +").append(Config.ZONE_DOMINANCE_MINOR_ADENA_BONUS).append("% Adena Drop Rate</font><br>");
                html.append("<font color=\"00FF00\">• +").append(Config.ZONE_DOMINANCE_MINOR_LCOIN_BONUS).append("% L-Coin Drop Rate</font><br>");
                html.append("<font color=\"00FF00\">• +").append(Config.ZONE_DOMINANCE_MINOR_EXP_SP_BONUS).append("% EXP/SP</font>");
                break;
            case MAJOR:
                html.append("<font color=\"00FF00\">• +").append(Config.ZONE_DOMINANCE_MAJOR_GVE_BONUS).append("% GvE Points</font><br>");
                html.append("<font color=\"00FF00\">• +").append(Config.ZONE_DOMINANCE_MAJOR_ADENA_BONUS).append("% Adena Drop Rate</font><br>");
                html.append("<font color=\"00FF00\">• +").append(Config.ZONE_DOMINANCE_MAJOR_LCOIN_BONUS).append("% L-Coin Drop Rate</font><br>");
                html.append("<font color=\"00FF00\">• +").append(Config.ZONE_DOMINANCE_MAJOR_EXP_SP_BONUS).append("% EXP/SP</font>");
                break;
            case TOTAL:
                html.append("<font color=\"00FF00\">• +").append(Config.ZONE_DOMINANCE_TOTAL_GVE_BONUS).append("% GvE Points</font><br>");
                html.append("<font color=\"00FF00\">• +").append(Config.ZONE_DOMINANCE_TOTAL_ADENA_BONUS).append("% Adena Drop Rate</font><br>");
                html.append("<font color=\"00FF00\">• +").append(Config.ZONE_DOMINANCE_TOTAL_LCOIN_BONUS).append("% L-Coin Drop Rate</font><br>");
                html.append("<font color=\"00FF00\">• +").append(Config.ZONE_DOMINANCE_TOTAL_EXP_SP_BONUS).append("% EXP/SP</font>");
                break;
            default:
                html.append("<font color=\"808080\">No Bonuses</font>");
                break;
        }
        
        return html.toString();
    }
    
    /**
     * Get zone control percentage (cached)
     */
    public double getCachedZoneControlPercentage(Faction faction)
    {
        return zoneControlPercentage.getOrDefault(faction, 0.0);
    }
    
    /**
     * Get current dominance tier (cached)
     */
    public DominanceTier getCachedDominanceTier(Faction faction)
    {
        return currentDominance.getOrDefault(faction, DominanceTier.NONE);
    }

    /**
     * Get GvE Points bonus percentage for a faction
     */
    public int getGvePointsBonus(Faction faction)
    {
        DominanceTier tier = getFactionDominanceTier(faction);
        switch (tier)
        {
            case MINOR:
                return Config.ZONE_DOMINANCE_MINOR_GVE_BONUS;
            case MAJOR:
                return Config.ZONE_DOMINANCE_MAJOR_GVE_BONUS;
            case TOTAL:
                return Config.ZONE_DOMINANCE_TOTAL_GVE_BONUS;
            default:
                return 0;
        }
    }

    /**
     * Get Adena drop bonus percentage for a faction
     */
    public int getAdenaDropBonus(Faction faction)
    {
        DominanceTier tier = getFactionDominanceTier(faction);
        switch (tier)
        {
            case MINOR:
                return Config.ZONE_DOMINANCE_MINOR_ADENA_BONUS;
            case MAJOR:
                return Config.ZONE_DOMINANCE_MAJOR_ADENA_BONUS;
            case TOTAL:
                return Config.ZONE_DOMINANCE_TOTAL_ADENA_BONUS;
            default:
                return 0;
        }
    }

    /**
     * Get L-Coin drop bonus percentage for a faction
     */
    public int getLCoinDropBonus(Faction faction)
    {
        DominanceTier tier = getFactionDominanceTier(faction);
        switch (tier)
        {
            case MINOR:
                return Config.ZONE_DOMINANCE_MINOR_LCOIN_BONUS;
            case MAJOR:
                return Config.ZONE_DOMINANCE_MAJOR_LCOIN_BONUS;
            case TOTAL:
                return Config.ZONE_DOMINANCE_TOTAL_LCOIN_BONUS;
            default:
                return 0;
        }
    }

    /**
     * Get EXP/SP bonus percentage for a faction
     */
    public int getExpSpBonus(Faction faction)
    {
        DominanceTier tier = getFactionDominanceTier(faction);
        switch (tier)
        {
            case MINOR:
                return Config.ZONE_DOMINANCE_MINOR_EXP_SP_BONUS;
            case MAJOR:
                return Config.ZONE_DOMINANCE_MAJOR_EXP_SP_BONUS;
            case TOTAL:
                return Config.ZONE_DOMINANCE_TOTAL_EXP_SP_BONUS;
            default:
                return 0;
        }
    }

    /**
     * Check if zone dominance system is enabled
     */
    public boolean isEnabled()
    {
        return Config.ENABLE_ZONE_DOMINANCE_SYSTEM;
    }

    /**
     * Send debug message to player about bonus application
     */
    public void sendBonusDebugMessage(PlayerInstance player, String bonusType, int originalValue, int finalValue, int bonusPercent)
    {
        if (player == null || !player.isGM())
        {
            return;
        }

        DominanceTier tier = getFactionDominanceTier(player.getFaction());
        double controlPercent = getFactionZoneControlPercentage(player.getFaction());

        String message = String.format("[Zone Dominance] %s: %d -> %d (+%d%%) | %s (%.1f%% zones)",
            bonusType, originalValue, finalValue, bonusPercent, tier.getName(), controlPercent);

        player.sendMessage(message);
    }

    /**
     * Test method to show all current bonuses for a player
     */
    public void showCurrentBonuses(PlayerInstance player)
    {
        if (player == null || player.getFaction() == Faction.NONE)
        {
            return;
        }

        DominanceTier tier = getFactionDominanceTier(player.getFaction());
        double controlPercent = getFactionZoneControlPercentage(player.getFaction());

        player.sendMessage("=== Zone Dominance Status ===");
        player.sendMessage("Faction: " + player.getFaction());
        player.sendMessage("Zone Control: " + String.format("%.1f", controlPercent) + "%");
        player.sendMessage("Tier: " + tier.getName());
        player.sendMessage("Bonuses: GvE +" + getGvePointsBonus(player.getFaction()) + "%, " +
            "Adena +" + getAdenaDropBonus(player.getFaction()) + "%, " +
            "L-Coin +" + getLCoinDropBonus(player.getFaction()) + "%, " +
            "EXP/SP +" + getExpSpBonus(player.getFaction()) + "%");
    }
}
