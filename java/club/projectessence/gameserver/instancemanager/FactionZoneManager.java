package club.projectessence.gameserver.instancemanager;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

import club.projectessence.Config;
import club.projectessence.commons.concurrent.ThreadPool;
import club.projectessence.commons.database.DatabaseFactory;
import club.projectessence.commons.util.Rnd;
import club.projectessence.gameserver.data.xml.OutpostData;
import club.projectessence.gameserver.enums.Faction;
import club.projectessence.gameserver.enums.TeleportWhereType;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.model.zone.GveZoneType;
import club.projectessence.gameserver.model.zone.type.FactionZone;
import club.projectessence.gameserver.network.serverpackets.ExShowScreenMessage;
import club.projectessence.gameserver.util.Broadcast;

public class FactionZoneManager
{
	private static final Logger							LOGGER					= Logger.getLogger(FactionZoneManager.class.getName());
	private final Map<String, ZoneStatus>				zoneStatuses			= new ConcurrentHashMap<>();
	private volatile List<String>						nextActiveOutposts		= Collections.synchronizedList(new ArrayList<>());
	private static final long							ZONE_CHANGE_DELAY		= Config.ZONE_CHANGE_INTERVAL * 60 * 1000L;
	private static final long							PROTECTION_DURATION		= Config.PROTECTION_DURATION * 60 * 1000L;
	// Calculate correct delays for announcements
	// For 5-minute announcement: delay = 0 (announce immediately)
	// For 1-minute announcement: delay = (5-1) = 4 minutes
	// For activation: delay = 5 minutes
	private static final long							ANNOUNCE_5_MINUTES_DELAY	= 0; // Announce immediately
	private static final long							ANNOUNCE_1_MINUTE_DELAY		= (Config.ZONE_CHANGE_ANNOUNCE_TIMES[0] - Config.ZONE_CHANGE_ANNOUNCE_TIMES[1]) * 60 * 1000L; // 4 minutes later
	private static final long							ACTIVATION_DELAY			= Config.ZONE_CHANGE_ANNOUNCE_TIMES[0] * 60 * 1000L; // 5 minutes later
	private static final long							CHANGE_ZONE_INTERVAL	= Config.ZONE_CHANGE_INTERVAL * 60 * 1000L;
	private final Map<Faction, List<PlayerInstance>>	factionPlayers			= new ConcurrentHashMap<>();

	// Circuit Breaker for error handling
	private volatile int								consecutiveFailures		= 0;
	private static final int							MAX_CONSECUTIVE_FAILURES = Config.CIRCUIT_BREAKER_MAX_FAILURES;
	private volatile long								lastFailureTime			= 0;
	private static final long							CIRCUIT_BREAKER_TIMEOUT	= Config.CIRCUIT_BREAKER_TIMEOUT_MINUTES * 60 * 1000L;
	
	public enum ZoneStatus
	{
		DISABLED,
		ENABLED,
		ACTIVATED
	}
	
	private FactionZoneManager()
	{
		factionPlayers.put(Faction.FIRE, Collections.synchronizedList(new ArrayList<>()));
		factionPlayers.put(Faction.WATER, Collections.synchronizedList(new ArrayList<>()));
		factionPlayers.put(Faction.NONE, Collections.synchronizedList(new ArrayList<>()));
		loadZones();
		initializeZones();
		schedulePeriodicTasks();
	}

	/**
	 * Check if circuit breaker is open (too many failures)
	 */
	private boolean isCircuitBreakerOpen()
	{
		if (consecutiveFailures >= MAX_CONSECUTIVE_FAILURES)
		{
			long timeSinceLastFailure = System.currentTimeMillis() - lastFailureTime;
			if (timeSinceLastFailure < CIRCUIT_BREAKER_TIMEOUT)
			{
				return true;
			}
			else
			{
				// Reset circuit breaker after timeout
				consecutiveFailures = 0;
				LOGGER.info("FactionZone: Circuit breaker reset after timeout");
			}
		}
		return false;
	}

	/**
	 * Record a failure for circuit breaker
	 */
	private void recordFailure()
	{
		consecutiveFailures++;
		lastFailureTime = System.currentTimeMillis();
		LOGGER.warning("FactionZone: Recorded failure #" + consecutiveFailures);
	}

	/**
	 * Record a success for circuit breaker
	 */
	private void recordSuccess()
	{
		if (consecutiveFailures > 0)
		{
			LOGGER.info("FactionZone: Operation successful, resetting failure count");
			consecutiveFailures = 0;
		}
	}
	
	private void loadZones()
	{
		try (Connection con = DatabaseFactory.getConnection())
		{
			// Tải các zone từ database và đặt tất cả về ENABLED
			try (PreparedStatement ps = con.prepareStatement("SELECT * FROM faction_zones"); ResultSet rs = ps.executeQuery())
			{
				while (rs.next())
				{
					String zoneName = rs.getString("zone_name");
					Faction controllingFaction = Faction.valueOf(rs.getString("controlling_faction"));
					ZoneStatus status = ZoneStatus.ENABLED; // Luôn đặt về ENABLED khi khởi động
					boolean isUnderAttack = rs.getBoolean("is_under_attack");
					int outpostStatus = rs.getInt("outpost_status");
					long protectionEndTime = 0; // Reset thời gian bảo vệ
					String zoneType = rs.getString("zone_type");
					FactionZone zone = ZoneManager.getInstance().getZoneByName(zoneName, FactionZone.class);
					if (zone != null)
					{
						zone.setControllingFaction(controllingFaction);
						zone.setUnderAttack(isUnderAttack);
						zone.setOutpostStatus(outpostStatus);
						zone.setStatus(status);
						zone.setProtectionEndTime(protectionEndTime);
						zone.setZoneType(GveZoneType.fromString(zoneType));
						zoneStatuses.put(zoneName, status);
						saveFactionToDatabase(zoneName, controllingFaction, status, isUnderAttack, outpostStatus); // Cập nhật database
						if (Config.LOG_FACTION_DETAILS)
						{
							LOGGER.info("Loaded and reset zone " + zoneName + " to ENABLED with controllingFaction " + controllingFaction + ", zoneType: " + zoneType);
						}
					}
				}
			}
			// Kiểm tra và thêm bản ghi cho các zone mới từ ZoneManager
			Collection<FactionZone> allZones = ZoneManager.getInstance().getAllZones(FactionZone.class);
			for (FactionZone zone : allZones)
			{
				String zoneName = zone.getName();
				try (PreparedStatement ps = con.prepareStatement("SELECT COUNT(*) FROM faction_zones WHERE zone_name = ?"))
				{
					ps.setString(1, zoneName);
					try (ResultSet rs = ps.executeQuery())
					{
						rs.next();
						if (rs.getInt(1) == 0)
						{
							try (PreparedStatement insertPs = con.prepareStatement("INSERT INTO faction_zones (zone_name, controlling_faction, status, is_under_attack, outpost_status, protection_end_time, zone_type) VALUES (?, ?, ?, ?, ?, ?, ?)"))
							{
								insertPs.setString(1, zoneName);
								insertPs.setString(2, Faction.NONE.name());
								insertPs.setString(3, ZoneStatus.ENABLED.name());
								insertPs.setBoolean(4, false);
								insertPs.setInt(5, 0);
								insertPs.setLong(6, 0);
								String zoneType = zone.getZoneType() != null ? zone.getZoneType().name() : GveZoneType.GVE_LOW.name();
								insertPs.setString(7, zoneType);
								insertPs.executeUpdate();
								if (Config.LOG_FACTION_DETAILS)
								{
									LOGGER.info("FactionZoneManager: Added new zone record for " + zoneName + " with status ENABLED and zone_type " + zoneType);
								}
								zone.setControllingFaction(Faction.NONE);
								zone.setUnderAttack(false);
								zone.setOutpostStatus(0);
								zone.setStatus(ZoneStatus.ENABLED);
								zone.setProtectionEndTime(0);
								zone.setZoneType(GveZoneType.fromString(zoneType));
								zoneStatuses.put(zoneName, ZoneStatus.ENABLED);
							}
						}
					}
				}
			}
		}
		catch (SQLException e)
		{
			LOGGER.log(Level.SEVERE, "Failed to load or initialize zones from database", e);
		}
		if (zoneStatuses.isEmpty())
		{
			LOGGER.warning("faction_zones table is empty. Initializing with default factions...");
			for (FactionZone zone : ZoneManager.getInstance().getAllZones(FactionZone.class))
			{
				String zoneName = zone.getName();
				zone.setControllingFaction(Faction.NONE);
				zone.setUnderAttack(false);
				zone.setOutpostStatus(0);
				ZoneStatus status = ZoneStatus.ENABLED;
				zone.setStatus(status);
				zoneStatuses.put(zoneName, status);
				saveFactionToDatabase(zoneName, Faction.NONE, status, false, 0);
			}
		}
	}
	
	private void initializeZones()
	{
		Collection<FactionZone> allZonesCollection = ZoneManager.getInstance().getAllZones(FactionZone.class);
		List<FactionZone> allZones = new ArrayList<>(allZonesCollection);
		if (allZones.isEmpty())
		{
			LOGGER.severe("FactionZone: No zones loaded from ZoneManager. Cannot initialize zones.");
			return;
		}
		StringBuilder initMessage = new StringBuilder();
		initMessage.append("FactionZoneManager: Initializing GvE zones...\n");
		for (FactionZone zone : allZones)
		{
			if (zone.getStatus() != ZoneStatus.DISABLED)
			{
				zone.setStatus(ZoneStatus.ENABLED);
				zoneStatuses.put(zone.getName(), ZoneStatus.ENABLED);
				zone.onChangeStatus();
				initMessage.append("Enabled zone: ").append(OutpostData.getInstance().getOutpostName(zone.getName())).append(" controlled by ").append(zone.getControllingFaction()).append(" [Type: ").append(zone.getZoneType().getValue()).append("]\n");
			}
		}
		int activeZoneCount = Config.ACTIVE_ZONE_COUNT;
		LOGGER.info("FactionZoneManager: Config.ACTIVE_ZONE_COUNT = " + activeZoneCount);
		Map<GveZoneType, List<FactionZone>> zonesByType = new ConcurrentHashMap<>();
		for (GveZoneType zoneType : GveZoneType.values())
		{
			zonesByType.put(zoneType, allZones.stream().filter(zone -> zone.getStatus() == ZoneStatus.ENABLED && zone.getZoneType() == zoneType).collect(Collectors.toList()));
			LOGGER.info("FactionZoneManager: Zones of type " + zoneType + ": " + zonesByType.get(zoneType).size());
		}
		List<FactionZone> selectedZones = new ArrayList<>();
		// Ưu tiên chọn 1 zone từ GVE_LOW
		List<FactionZone> lowZones = zonesByType.get(GveZoneType.GVE_LOW);
		if (!lowZones.isEmpty())
		{
			FactionZone selected = Rnd.get(lowZones);
			selectedZones.add(selected);
			zonesByType.get(GveZoneType.GVE_LOW).remove(selected);
			LOGGER.info("FactionZoneManager: Selected GVE_LOW zone: " + selected.getName());
		}
		// Ưu tiên chọn 1 zone từ GVE_MID
		List<FactionZone> midZones = zonesByType.get(GveZoneType.GVE_MID);
		if (!midZones.isEmpty() && selectedZones.size() < activeZoneCount)
		{
			FactionZone selected = Rnd.get(midZones);
			selectedZones.add(selected);
			zonesByType.get(GveZoneType.GVE_MID).remove(selected);
			LOGGER.info("FactionZoneManager: Selected GVE_MID zone: " + selected.getName());
		}
		// Ưu tiên chọn 1 zone từ GVE_HIGH
		List<FactionZone> highZones = zonesByType.get(GveZoneType.GVE_HIGH);
		if (!highZones.isEmpty() && selectedZones.size() < activeZoneCount)
		{
			FactionZone selected = Rnd.get(highZones);
			selectedZones.add(selected);
			zonesByType.get(GveZoneType.GVE_HIGH).remove(selected);
			LOGGER.info("FactionZoneManager: Selected GVE_HIGH zone: " + selected.getName());
		}
		// Nếu vẫn chưa đủ 3 zone, bổ sung từ các zone còn lại
		if (selectedZones.size() < activeZoneCount && !allZones.isEmpty())
		{
			List<FactionZone> remainingZones = new ArrayList<>();
			for (List<FactionZone> zoneList : zonesByType.values())
			{
				remainingZones.addAll(zoneList);
			}
			remainingZones.removeAll(selectedZones);
			LOGGER.info("FactionZoneManager: Remaining enabled zones: " + remainingZones.size());
			while (selectedZones.size() < activeZoneCount && !remainingZones.isEmpty())
			{
				FactionZone selected = Rnd.get(remainingZones);
				selectedZones.add(selected);
				remainingZones.remove(selected);
				LOGGER.info("FactionZoneManager: Added additional zone to reach ActiveZoneCount: " + selected.getName());
			}
		}
		for (FactionZone zone : selectedZones)
		{
			initMessage.append("Scheduled activation zone: ").append(OutpostData.getInstance().getOutpostName(zone.getName())).append(" [Type: ").append(zone.getZoneType().getValue()).append("]\n");
			LOGGER.info("FactionZoneManager: Scheduled activation zone: " + zone.getName());
		}
		initMessage.append("FactionZone initialization completed. Total scheduled zones: " + selectedZones.size() + ". Activation will start after delay.");
		LOGGER.info(initMessage.toString());
		// Lên lịch kích hoạt sau 5 phút và 1 phút thông báo
		scheduleInitialActivation(selectedZones.stream().map(FactionZone::getName).collect(Collectors.toList()));
	}
	
	private void scheduleInitialActivation(List<String> initialOutposts)
	{
		if (initialOutposts.isEmpty())
		{
			LOGGER.warning("FactionZone: No initial outposts to schedule activation.");
			return;
		}

		long initialDelay = Config.INITIAL_ACTIVATION_DELAY_MINUTES * 60 * 1000L;

		// Schedule announcements using same config as regular zone changes
		for (int announceTime : Config.ZONE_CHANGE_ANNOUNCE_TIMES)
		{
			if (announceTime < Config.INITIAL_ACTIVATION_DELAY_MINUTES)
			{
				long announceDelay = (Config.INITIAL_ACTIVATION_DELAY_MINUTES - announceTime) * 60 * 1000L;
				final int finalAnnounceTime = announceTime;
				ThreadPool.schedule(() ->
				{
					announceZones(finalAnnounceTime, initialOutposts);
				}, announceDelay);
			}
		}

		// Schedule actual activation
		ThreadPool.schedule(() ->
		{
			if (Config.LOG_FACTION_DETAILS)
			{
				LOGGER.info("FactionZone: Executing initial activation at " + new Date());
			}

			// Send activation message
			String activationMessage = "FACTION ZONES ACTIVATED!";
			Broadcast.toAllOnlinePlayers(activationMessage, true);

			nextActiveOutposts = new ArrayList<>(initialOutposts);
			changeZones();

			if (Config.LOG_FACTION_DETAILS)
			{
				LOGGER.info("FactionZone: Initial activation completed for zones: " + nextActiveOutposts);
			}

			// Start regular zone change cycle
			scheduleZoneChangeCycle();
		}, initialDelay);
	}
	
	private void scheduleZoneChangeCycle()
	{
		LOGGER.info("FactionZone: Initializing zone change cycle with ZONE_CHANGE_DELAY=" + ZONE_CHANGE_DELAY + "ms, ANNOUNCE_5_MINUTES_DELAY=" + ANNOUNCE_5_MINUTES_DELAY + "ms, ANNOUNCE_1_MINUTE_DELAY=" + ANNOUNCE_1_MINUTE_DELAY + "ms, ACTIVATION_DELAY=" + ACTIVATION_DELAY + "ms");

		// TODO: Current logic has timing issues - announcements and zone changes happen in wrong order
		// Should be: Announce -> Wait -> Announce -> Wait -> Change Zones
		// Currently: Change Zones -> Announce (for next cycle) - this is confusing for players
		ThreadPool.scheduleAtFixedRate(() ->
		{
			try
			{
				long cycleStart = System.currentTimeMillis();
				if (Config.LOG_FACTION_DETAILS)
				{
					LOGGER.info("FactionZone: Starting zone change cycle at " + new Date(cycleStart));
				}
				if (nextActiveOutposts.isEmpty())
				{
					LOGGER.warning("FactionZone: nextActiveOutposts is empty at start of cycle");
					setNextOutposts();
				}
				List<String> currentOutposts = new ArrayList<>(nextActiveOutposts);
				if (Config.LOG_FACTION_DETAILS)
				{
					LOGGER.info("FactionZone: nextActiveOutposts for this cycle: " + currentOutposts);
				}
				// Schedule 5-minute announcement (immediately)
				ThreadPool.schedule(() ->
				{
					if (Config.LOG_FACTION_DETAILS)
					{
						LOGGER.info("FactionZone: Executing 5-minute announcement at " + new Date());
					}
					announceZones(5, currentOutposts);
					if (Config.LOG_FACTION_DETAILS)
					{
						LOGGER.info("FactionZone: Announced zone change in 5 minutes for zones: " + currentOutposts);
					}
				}, ANNOUNCE_5_MINUTES_DELAY);

				// Schedule 1-minute announcement (4 minutes later)
				ThreadPool.schedule(() ->
				{
					if (Config.LOG_FACTION_DETAILS)
					{
						LOGGER.info("FactionZone: Executing 1-minute announcement at " + new Date());
					}
					announceZones(1, currentOutposts);
					if (Config.LOG_FACTION_DETAILS)
					{
						LOGGER.info("FactionZone: Announced zone change in 1 minute for zones: " + currentOutposts);
					}
				}, ANNOUNCE_1_MINUTE_DELAY);

				// Schedule zone activation (5 minutes later)
				ThreadPool.schedule(() ->
				{
					if (Config.LOG_FACTION_DETAILS)
					{
						LOGGER.info("FactionZone: Executing changeZones at " + new Date());
					}
					nextActiveOutposts = new ArrayList<>(currentOutposts);
					changeZones();
					if (Config.LOG_FACTION_DETAILS)
					{
						LOGGER.info("FactionZone: Preparing next cycle by calling setNextOutposts");
					}
					setNextOutposts();
					if (Config.LOG_FACTION_DETAILS)
					{
						LOGGER.info("FactionZone: Completed zone change cycle at " + new Date());
					}
				}, ACTIVATION_DELAY);
			}
			catch (Exception e)
			{
				LOGGER.log(Level.SEVERE, "FactionZone: Error in zone change cycle: " + e.getMessage(), e);
				recordFailure();
			}
		}, ZONE_CHANGE_DELAY, ZONE_CHANGE_DELAY); // Lặp lại mỗi 30 phút
	}
	
	private void announceZones(int minutes, List<String> outposts)
	{
		if (outposts.isEmpty())
		{
			LOGGER.warning("FactionZone: Cannot announce zone change for " + minutes + " minute(s) because outposts list is empty");
			return;
		}

		// DEBUG: Always log announcement calls to track timing issues
		LOGGER.info("FactionZone: [DEBUG] announceZones called for " + minutes + " minute(s) at " + new Date() + " with outposts: " + outposts);

		StringBuilder message = new StringBuilder();
		message.append("Zones will be activated in ").append(minutes).append(" minute(s):\n");
		for (String zoneName : outposts)
		{
			String outpostName = OutpostData.getInstance().getOutpostName(zoneName);
			FactionZone zone = ZoneManager.getInstance().getZoneByName(zoneName, FactionZone.class);
			String zoneType = zone != null ? zone.getZoneType().getValue() : "Unknown";
			message.append("- ").append(outpostName).append(" [").append(zoneType).append("]\n");
		}
		String[] lines = message.toString().trim().split("\n");
		for (String line : lines)
		{
			Broadcast.toAllOnlinePlayers(line, true);
		}
		if (Config.LOG_FACTION_DETAILS)
		{
			LOGGER.info("FactionZone: Announced zone change for " + minutes + " minute(s): " + message.toString().replace("\n", "; "));
		}
	}
	
	private void changeZones()
	{
		LOGGER.info("FactionZone: Starting changeZones cycle at " + new Date());

		// Check circuit breaker
		if (isCircuitBreakerOpen())
		{
			LOGGER.warning("FactionZone: Circuit breaker is open, skipping zone change cycle");
			return;
		}

		try
		{
			Collection<FactionZone> allZones = ZoneManager.getInstance().getAllZones(FactionZone.class);
			if (allZones.isEmpty())
			{
				LOGGER.severe("FactionZone: No zones available for changeZones cycle.");
				setNextOutposts();
				return;
			}
			List<String> activatedOutposts = getActiveOutposts();
			if (Config.LOG_FACTION_DETAILS)
			{
				LOGGER.info("FactionZone: Deactivating zones: " + activatedOutposts);
			}
			for (String zoneName : activatedOutposts)
			{
				FactionZone zone = ZoneManager.getInstance().getZoneByName(zoneName, FactionZone.class);
				if (zone != null && zone.getStatus() == ZoneStatus.ACTIVATED)
				{
					zone.setStatus(ZoneStatus.ENABLED);
					zoneStatuses.put(zoneName, ZoneStatus.ENABLED);
					zone.setUnderAttack(false);
					saveFactionToDatabase(zoneName, zone.getControllingFaction(), ZoneStatus.ENABLED, false, 0);
					if (Config.LOG_FACTION_DETAILS)
					{
						LOGGER.info("FactionZone: Changing status of zone " + zoneName + " from ACTIVATED to ENABLED");
					}
					zone.onChangeStatus();
					List<PlayerInstance> playersInZone = getPlayersInZone(zoneName);
					for (PlayerInstance player : playersInZone)
					{
						if (player.getFaction() != zone.getControllingFaction() && player.getFaction() != Faction.NONE)
						{
							teleportToBase(player);
							if (Config.LOG_FACTION_DETAILS)
							{
								LOGGER.fine("FactionZone: Player " + player.getName() + " teleported back to base from zone " + zoneName + " due to zone deactivation.");
							}
						}
					}
				}
			}
			if (Config.LOG_FACTION_DETAILS)
			{
				LOGGER.info("FactionZone: Activating zones: " + nextActiveOutposts);
			}
			for (String zoneName : nextActiveOutposts)
			{
				if (zoneName == null)
				{
					LOGGER.severe("FactionZone: Found null zoneName in nextActiveOutposts. Skipping...");
					continue;
				}
				FactionZone zone = ZoneManager.getInstance().getZoneByName(zoneName, FactionZone.class);
				if (zone == null)
				{
					LOGGER.severe("FactionZone: No FactionZone for outpost zone " + zoneName);
					continue;
				}
				if (zone.getStatus() == ZoneStatus.ACTIVATED)
				{
					if (Config.LOG_FACTION_DETAILS)
					{
						LOGGER.info("FactionZone: Zone " + zoneName + " is already ACTIVATED. Skipping...");
					}
					continue;
				}
				zone.setStatus(ZoneStatus.ACTIVATED);
				zoneStatuses.put(zoneName, ZoneStatus.ACTIVATED);
				saveFactionToDatabase(zoneName, zone.getControllingFaction(), ZoneStatus.ACTIVATED, zone.isUnderAttack(), 0);
				if (Config.LOG_FACTION_DETAILS)
				{
					LOGGER.info("FactionZone: Changing status of zone " + zoneName + " from ENABLED to ACTIVATED");
				}
				zone.onChangeStatus();
				Faction controllingFaction = zone.getControllingFaction();
				List<PlayerInstance> playersInZone = getPlayersInZone(zoneName);
				String outpostName = OutpostData.getInstance().getOutpostName(zoneName);
				if (controllingFaction == Faction.NONE)
				{
					for (PlayerInstance player : playersInZone)
					{
						teleportToBase(player);
						if (Config.LOG_FACTION_DETAILS)
						{
							LOGGER.fine("FactionZone: Player " + player.getName() + " teleported back to base from neutral zone " + zoneName + " on activation.");
						}
					}
					List<PlayerInstance> allPlayers = new ArrayList<>();
					allPlayers.addAll(getFactionPlayers(Faction.FIRE));
					allPlayers.addAll(getFactionPlayers(Faction.WATER));
					if (!allPlayers.isEmpty())
					{
						notifyPlayers(zoneName, allPlayers);
					}
				}
			}
			// Gửi thông báo duy nhất với xuống dòng rõ ràng
			List<FactionZone> activatedZones = allZones.stream().filter(zone -> zone.getStatus() == ZoneStatus.ACTIVATED).collect(Collectors.toList());
			if (!activatedZones.isEmpty())
			{
				StringBuilder message = new StringBuilder("Zones activated:\n");
				for (FactionZone zone : activatedZones)
				{
					message.append("- ").append(OutpostData.getInstance().getOutpostName(zone.getName())).append(" [").append(zone.getZoneType().getValue()).append("]\n");
				}
				// Loại bỏ ký tự xuống dòng thừa ở cuối
				String finalMessage = message.toString().trim();
				Broadcast.toAllOnlinePlayers(finalMessage, true);
				if (Config.LOG_FACTION_DETAILS)
				{
					LOGGER.info("FactionZone: Broadcasted activated zones: " + finalMessage);
				}
			}
			setNextOutposts();

			// Update zone dominance status after zone changes
			ZoneDominanceManager.getInstance().updateDominanceStatus();

			LOGGER.info("FactionZone: Completed changeZones cycle at " + new Date());
			recordSuccess(); // Record successful operation
		}
		catch (Exception e)
		{
			LOGGER.log(Level.SEVERE, "FactionZone: Error in changeZones: " + e.getMessage(), e);
			recordFailure(); // Record failure for circuit breaker

			// Try to recover by ensuring next outposts are set
			try
			{
				setNextOutposts();
				LOGGER.info("FactionZone: Recovery attempt - setNextOutposts completed");
			}
			catch (Exception recoveryException)
			{
				LOGGER.log(Level.SEVERE, "FactionZone: Recovery failed: " + recoveryException.getMessage(), recoveryException);
			}
		}
	}
	
	private void setNextOutposts()
	{
		if (Config.LOG_FACTION_DETAILS)
		{
			LOGGER.fine("FactionZone: Current zoneStatuses: " + zoneStatuses);
		}
		nextActiveOutposts.clear();
		List<FactionZone> allEnabledZones = ZoneManager.getInstance().getAllZones(FactionZone.class).stream().filter(zone -> zoneStatuses.getOrDefault(zone.getName(), ZoneStatus.DISABLED) == ZoneStatus.ENABLED).collect(Collectors.toList());
		Map<GveZoneType, List<FactionZone>> zonesByType = new ConcurrentHashMap<>();
		for (GveZoneType zoneType : GveZoneType.values())
		{
			zonesByType.put(zoneType, allEnabledZones.stream().filter(zone -> zone.getZoneType() == zoneType).collect(Collectors.toList()));
			LOGGER.info("FactionZoneManager: Zones of type " + zoneType + " available for change: " + zonesByType.get(zoneType).size());
		}
		int activeZoneCount = Config.ACTIVE_ZONE_COUNT;
		List<FactionZone> selectedZones = new ArrayList<>();
		// Ưu tiên chọn 1 zone từ GVE_LOW
		List<FactionZone> lowZones = zonesByType.get(GveZoneType.GVE_LOW);
		if (!lowZones.isEmpty())
		{
			FactionZone selected = Rnd.get(lowZones);
			selectedZones.add(selected);
			zonesByType.get(GveZoneType.GVE_LOW).remove(selected);
			LOGGER.info("FactionZoneManager: Selected GVE_LOW zone for change: " + selected.getName());
		}
		// Ưu tiên chọn 1 zone từ GVE_MID
		List<FactionZone> midZones = zonesByType.get(GveZoneType.GVE_MID);
		if (!midZones.isEmpty() && selectedZones.size() < activeZoneCount)
		{
			FactionZone selected = Rnd.get(midZones);
			selectedZones.add(selected);
			zonesByType.get(GveZoneType.GVE_MID).remove(selected);
			LOGGER.info("FactionZoneManager: Selected GVE_MID zone for change: " + selected.getName());
		}
		// Ưu tiên chọn 1 zone từ GVE_HIGH
		List<FactionZone> highZones = zonesByType.get(GveZoneType.GVE_HIGH);
		if (!highZones.isEmpty() && selectedZones.size() < activeZoneCount)
		{
			FactionZone selected = Rnd.get(highZones);
			selectedZones.add(selected);
			zonesByType.get(GveZoneType.GVE_HIGH).remove(selected);
			LOGGER.info("FactionZoneManager: Selected GVE_HIGH zone for change: " + selected.getName());
		}
		// Nếu vẫn chưa đủ 3 zone, bổ sung từ các zone còn lại
		if (selectedZones.size() < activeZoneCount && !allEnabledZones.isEmpty())
		{
			List<FactionZone> remainingZones = new ArrayList<>();
			for (List<FactionZone> zoneList : zonesByType.values())
			{
				remainingZones.addAll(zoneList);
			}
			remainingZones.removeAll(selectedZones);
			LOGGER.info("FactionZoneManager: Remaining enabled zones: " + remainingZones.size());
			while (selectedZones.size() < activeZoneCount && !remainingZones.isEmpty())
			{
				FactionZone selected = Rnd.get(remainingZones);
				selectedZones.add(selected);
				remainingZones.remove(selected);
				LOGGER.info("FactionZoneManager: Added additional zone to reach ActiveZoneCount: " + selected.getName());
			}
		}
		nextActiveOutposts.addAll(selectedZones.stream().map(FactionZone::getName).collect(Collectors.toList()));
		if (Config.LOG_FACTION_DETAILS)
		{
			LOGGER.fine("Next active outposts set to: " + nextActiveOutposts);
		}
	}
	
	private void saveFactionToDatabase(String zoneName, Faction controllingFaction, ZoneStatus status, boolean isUnderAttack, int outpostStatus)
	{
		saveFactionToDatabaseWithRetry(zoneName, controllingFaction, status, isUnderAttack, outpostStatus, Config.DATABASE_RETRY_ATTEMPTS);
	}

	/**
	 * Save faction data to database with retry mechanism and better error handling
	 */
	private void saveFactionToDatabaseWithRetry(String zoneName, Faction controllingFaction, ZoneStatus status, boolean isUnderAttack, int outpostStatus, int retries)
	{
		while (retries > 0)
		{
			try (Connection con = DatabaseFactory.getConnection(); PreparedStatement ps = con.prepareStatement("INSERT INTO faction_zones (zone_name, controlling_faction, status, is_under_attack, outpost_status, protection_end_time, zone_type) " + "VALUES (?, ?, ?, ?, ?, ?, ?) " + "ON DUPLICATE KEY UPDATE controlling_faction = ?, status = ?, is_under_attack = ?, outpost_status = ?, protection_end_time = ?, zone_type = ?"))
			{
				FactionZone zone = ZoneManager.getInstance().getZoneByName(zoneName, FactionZone.class);
				long protectionEndTime = (zone != null) ? zone.getProtectionEndTime() : 0;
				String zoneType = (zone != null) ? zone.getZoneType().name() : GveZoneType.GVE_LOW.name();
				ps.setString(1, zoneName);
				ps.setString(2, controllingFaction.name());
				ps.setString(3, status.name());
				ps.setBoolean(4, isUnderAttack);
				ps.setInt(5, outpostStatus);
				ps.setLong(6, protectionEndTime);
				ps.setString(7, zoneType);
				ps.setString(8, controllingFaction.name());
				ps.setString(9, status.name());
				ps.setBoolean(10, isUnderAttack);
				ps.setInt(11, outpostStatus);
				ps.setLong(12, protectionEndTime);
				ps.setString(13, zoneType);
				ps.executeUpdate();
				if (Config.LOG_FACTION_DETAILS)
				{
					LOGGER.fine("Saved zone " + zoneName + " with status: " + status + ", protectionEndTime: " + protectionEndTime + ", zoneType: " + zoneType);
				}
				return;
			}
			catch (SQLException e)
			{
				retries--;
				if (retries == 0)
				{
					LOGGER.log(Level.SEVERE, "Failed to save zone " + zoneName + " after 3 retries", e);
				}
				else
				{
					try
					{
						Thread.sleep(1000);
					}
					catch (InterruptedException ie)
					{
						LOGGER.warning("Retry interrupted for zone " + zoneName);
					}
				}
			}
		}
	}
	
	private void notifyPlayers(String zoneName, List<PlayerInstance> players)
	{
		FactionZone targetZone = ZoneManager.getInstance().getZoneByName(zoneName, FactionZone.class);
		if (targetZone == null)
		{
			LOGGER.warning("FactionZone: No target zone found for zoneName " + zoneName + " in notifyPlayers");
			for (PlayerInstance player : players)
			{
				if (player != null && player.isOnline())
				{
					teleportToBase(player);
					LOGGER.warning("FactionZone: No target zone set for player " + player.getName() + " in notifyPlayers");
				}
			}
			return;
		}
		String outpostName = OutpostData.getInstance().getOutpostName(zoneName);
		String message = "Zone " + outpostName + " is active! Open the Community Board to teleport!";
		for (PlayerInstance player : players)
		{
			if (player == null || !player.isOnline())
				continue;
			if (targetZone.isUnderProtection())
			{
				if (Config.LOG_FACTION_DETAILS)
				{
					LOGGER.fine("FactionZone: Skipped notification for player " + player.getName() + " to " + zoneName + " due to zone protection");
				}
				continue;
			}
			player.sendPacket(new ExShowScreenMessage(message, ExShowScreenMessage.TOP_CENTER, 10000, 0, true, true));
			if (Config.LOG_FACTION_DETAILS)
			{
				LOGGER.fine("FactionZone: Sent teleport notification to player " + player.getName() + " for zone " + zoneName);
			}
		}
	}
	
	private void teleportToBase(PlayerInstance player)
	{
		// if (player.getFaction() == Faction.FIRE)
		// {
		// player.teleToLocation(Config.FACTION_FIRE_BASE_LOCATION);
		// }
		// else if (player.getFaction() == Faction.WATER)
		// {
		// player.teleToLocation(Config.FACTION_WATER_BASE_LOCATION);
		// }
		// else
		// {
		// player.teleToLocation(83000, 148000, -3400);
		// }
		if (Config.LOG_FACTION_DETAILS)
		{
			LOGGER.fine("Teleporting player " + player.getName() + " to nearest town for faction " + player.getFaction());
		}
		MapRegionManager.getInstance().getTeleToLocation(player, TeleportWhereType.TOWN);
	}
	
	public void setZoneFaction(String zoneName, Faction faction)
	{
		FactionZone zone = ZoneManager.getInstance().getZoneByName(zoneName, FactionZone.class);
		if (zone != null && faction != zone.getControllingFaction())
		{
			zone.setControllingFaction(faction);
			zone.setProtectionEndTime(System.currentTimeMillis() + PROTECTION_DURATION);
			if (Config.LOG_FACTION_DETAILS)
			{
				LOGGER.info("FactionZone: Protection activated for zone " + zoneName + " until " + new Date(zone.getProtectionEndTime()));
			}
			saveFactionToDatabase(zoneName, faction, zone.getStatus(), zone.isUnderAttack(), zone.getOutpostStatus());
			List<PlayerInstance> playersInZone = getPlayersInZone(zoneName);
			for (PlayerInstance player : playersInZone)
			{
				if (player.getFaction() == faction)
				{
					FactionWarManager.getInstance().addPersonalPoints(player, 20, "capturing zone " + zoneName);
				}
				if (player.getFaction() != faction && player.getFaction() != Faction.NONE)
				{
					teleportToBase(player);
					if (Config.LOG_FACTION_DETAILS)
					{
						LOGGER.fine("FactionZone: Player " + player.getName() + " teleported back to base from zone " + zoneName + " due to faction change.");
					}
				}
			}

			// Update zone dominance status
			ZoneDominanceManager.getInstance().updateDominanceStatus();
		}
	}
	
	public List<String> getActiveOutposts()
	{
		return OutpostData.getInstance().getAllOutposts().stream().map(statSet -> statSet.getString("zoneName")).filter(zoneName -> zoneStatuses.getOrDefault(zoneName, ZoneStatus.DISABLED) == ZoneStatus.ACTIVATED).collect(Collectors.toList());
	}
	
	public List<String> getNextActiveOutposts()
	{
		return nextActiveOutposts;
	}
	
	public List<PlayerInstance> getPlayersInZone(String zoneName)
	{
		FactionZone zone = ZoneManager.getInstance().getZoneByName(zoneName, FactionZone.class);
		if (zone == null)
		{
			return new ArrayList<>();
		}
		return zone.getPlayersInZone();
	}
	
	public Faction getZoneFaction(String zoneName)
	{
		FactionZone zone = ZoneManager.getInstance().getZoneByName(zoneName, FactionZone.class);
		return zone != null ? zone.getControllingFaction() : null;
	}
	
	public ZoneStatus getZoneStatus(String zoneName)
	{
		return zoneStatuses.getOrDefault(zoneName, ZoneStatus.DISABLED);
	}
	
	public static long getChangeZoneInterval()
	{
		return CHANGE_ZONE_INTERVAL;
	}
	
	private void scheduleAnnounceChangeZone(int minutes)
	{
		if (nextActiveOutposts.isEmpty())
		{
			LOGGER.warning("FactionZone: Cannot announce zone change for " + minutes + " minute(s) because nextActiveOutposts is empty");
			return;
		}
		StringBuilder message = new StringBuilder();
		message.append("Zones will be activated in ").append(minutes).append(" minute(s):\n");
		for (String zoneName : nextActiveOutposts)
		{
			String outpostName = OutpostData.getInstance().getOutpostName(zoneName);
			FactionZone zone = ZoneManager.getInstance().getZoneByName(zoneName, FactionZone.class);
			String zoneType = zone != null ? zone.getZoneType().getValue() : "Unknown";
			message.append("- ").append(outpostName).append(" [").append(zoneType).append("]\n");
		}
		String[] lines = message.toString().trim().split("\n");
		for (String line : lines)
		{
			Broadcast.toAllOnlinePlayers(line, true);
		}
		if (Config.LOG_FACTION_DETAILS)
		{
			LOGGER.info("FactionZone: Announced zone change for " + minutes + " minute(s): " + message.toString().replace("\n", "; "));
		}
	}
	
	public Map<String, ZoneStatus> getZoneStatuses()
	{
		return zoneStatuses;
	}
	
	public void addPlayerToFaction(PlayerInstance player, Faction faction)
	{
		synchronized (factionPlayers)
		{
			for (List<PlayerInstance> players : factionPlayers.values())
			{
				players.remove(player);
			}
			factionPlayers.get(faction).add(player);
			if (Config.LOG_FACTION_DETAILS)
			{
				LOGGER.fine("Added player " + player.getName() + " to faction " + faction);
			}
		}
	}
	
	public void removePlayerFromFaction(PlayerInstance player, Faction faction)
	{
		synchronized (factionPlayers)
		{
			factionPlayers.get(faction).remove(player);
			if (Config.LOG_FACTION_DETAILS)
			{
				LOGGER.fine("Removed player " + player.getName() + " from faction " + faction);
			}
		}
	}
	
	public List<PlayerInstance> getFactionPlayers(Faction faction)
	{
		return new ArrayList<>(factionPlayers.get(faction));
	}

	/**
	 * Clean up player from zone tracking (called when player exits zone)
	 */
	public void cleanupPlayerFromZone(PlayerInstance player, String zoneName)
	{
		if (player == null || zoneName == null)
		{
			return;
		}

		if (Config.LOG_FACTION_DETAILS)
		{
			LOGGER.fine("Cleaning up player " + player.getName() + " from zone " + zoneName);
		}

		// Additional cleanup logic can be added here if needed
		// For example, removing from any zone-specific tracking
	}

	/**
	 * Periodic cleanup of disconnected players
	 */
	public void performPeriodicCleanup()
	{
		for (Map.Entry<Faction, List<PlayerInstance>> entry : factionPlayers.entrySet())
		{
			List<PlayerInstance> players = entry.getValue();
			synchronized (players)
			{
				players.removeIf(player -> player == null || !player.isOnline());
			}
		}

		if (Config.LOG_FACTION_DETAILS)
		{
			LOGGER.fine("Performed periodic cleanup of faction players");
		}
	}

	/**
	 * Health check for faction zone system
	 */
	public boolean performHealthCheck()
	{
		try
		{
			// Check if zones are loaded
			Collection<FactionZone> allZones = ZoneManager.getInstance().getAllZones(FactionZone.class);
			if (allZones.isEmpty())
			{
				LOGGER.warning("Health Check FAILED: No faction zones found");
				return false;
			}

			// Check if next outposts are set
			if (nextActiveOutposts.isEmpty())
			{
				LOGGER.warning("Health Check WARNING: No next active outposts set");
			}

			// Check circuit breaker status
			if (isCircuitBreakerOpen())
			{
				LOGGER.warning("Health Check WARNING: Circuit breaker is open");
			}

			// Check database connectivity
			try (Connection con = DatabaseFactory.getConnection())
			{
				if (con == null || con.isClosed())
				{
					LOGGER.warning("Health Check FAILED: Database connection issue");
					return false;
				}
			}
			catch (SQLException e)
			{
				LOGGER.warning("Health Check FAILED: Database error - " + e.getMessage());
				return false;
			}

			LOGGER.info("Health Check PASSED: Faction zone system is healthy");
			return true;
		}
		catch (Exception e)
		{
			LOGGER.log(Level.SEVERE, "Health Check FAILED: Unexpected error - " + e.getMessage(), e);
			return false;
		}
	}

	/**
	 * Get system status for monitoring
	 */
	public String getSystemStatus()
	{
		StringBuilder status = new StringBuilder();
		status.append("=== Faction Zone System Status ===\n");
		status.append("Active Zones: ").append(nextActiveOutposts.size()).append("\n");
		status.append("Circuit Breaker Failures: ").append(consecutiveFailures).append("/").append(MAX_CONSECUTIVE_FAILURES).append("\n");
		status.append("Circuit Breaker Open: ").append(isCircuitBreakerOpen()).append("\n");

		for (Map.Entry<Faction, List<PlayerInstance>> entry : factionPlayers.entrySet())
		{
			status.append("Faction ").append(entry.getKey()).append(" Players: ").append(entry.getValue().size()).append("\n");
		}

		return status.toString();
	}

	/**
	 * Schedule periodic maintenance tasks
	 */
	private void schedulePeriodicTasks()
	{
		// Schedule periodic cleanup
		long cleanupInterval = Config.PERIODIC_CLEANUP_INTERVAL_MINUTES * 60 * 1000L;
		ThreadPool.scheduleAtFixedRate(() ->
		{
			try
			{
				performPeriodicCleanup();
			}
			catch (Exception e)
			{
				LOGGER.log(Level.WARNING, "Error in periodic cleanup: " + e.getMessage(), e);
			}
		}, cleanupInterval, cleanupInterval);

		// Schedule health check
		long healthCheckInterval = Config.HEALTH_CHECK_INTERVAL_MINUTES * 60 * 1000L;
		ThreadPool.scheduleAtFixedRate(() ->
		{
			try
			{
				boolean healthy = performHealthCheck();
				if (!healthy)
				{
					LOGGER.warning("Faction Zone System health check failed!");
				}
			}
			catch (Exception e)
			{
				LOGGER.log(Level.WARNING, "Error in health check: " + e.getMessage(), e);
			}
		}, healthCheckInterval, healthCheckInterval);

		LOGGER.info("FactionZone: Scheduled periodic maintenance tasks");
	}

	/**
	 * Improved zone change cycle with proper timing
	 * TODO: Replace scheduleZoneChangeCycle() with this method for better player experience
	 */
	private void scheduleImprovedZoneChangeCycle()
	{
		LOGGER.info("FactionZone: Initializing IMPROVED zone change cycle");

		// Schedule the main cycle
		ThreadPool.scheduleAtFixedRate(() ->
		{
			try
			{
				if (isCircuitBreakerOpen())
				{
					LOGGER.warning("FactionZone: Circuit breaker is open, skipping cycle");
					return;
				}

				// Step 1: Prepare next zones
				setNextOutposts();
				if (nextActiveOutposts.isEmpty())
				{
					LOGGER.warning("FactionZone: No zones to activate, skipping cycle");
					return;
				}

				List<String> upcomingZones = new ArrayList<>(nextActiveOutposts);
				LOGGER.info("FactionZone: Starting new cycle - upcoming zones: " + upcomingZones);

				// Step 2: Announce 5 minutes before
				announceZones(5, upcomingZones);

				// Step 3: Schedule 1-minute announcement
				ThreadPool.schedule(() ->
				{
					announceZones(1, upcomingZones);
				}, (5 - 1) * 60 * 1000L); // 4 minutes later

				// Step 4: Schedule actual zone change
				ThreadPool.schedule(() ->
				{
					changeZones();
					recordSuccess();
				}, 5 * 60 * 1000L); // 5 minutes later

			}
			catch (Exception e)
			{
				LOGGER.log(Level.SEVERE, "FactionZone: Error in improved zone change cycle: " + e.getMessage(), e);
				recordFailure();
			}
		}, ZONE_CHANGE_DELAY, ZONE_CHANGE_DELAY);
	}

	// Removed weather and synergy features as requested
	
	private static class SingletonHolder
	{
		protected static final FactionZoneManager INSTANCE = new FactionZoneManager();
	}
	
	public static FactionZoneManager getInstance()
	{
		return SingletonHolder.INSTANCE;
	}
}