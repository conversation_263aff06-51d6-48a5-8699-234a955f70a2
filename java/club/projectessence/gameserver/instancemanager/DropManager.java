/*
 * This file is part of the L2J Mobius project.
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.instancemanager;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.text.SimpleDateFormat;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.concurrent.ScheduledFuture;
import java.util.logging.Logger;

import club.projectessence.Config;
import club.projectessence.commons.concurrent.ThreadPool;
import club.projectessence.commons.database.DatabaseFactory;
import club.projectessence.commons.util.Rnd;
import club.projectessence.gameserver.model.actor.instance.MonsterInstance;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.enums.Faction;

public class DropManager
{
	private static final Logger								LOGGER					= Logger.getLogger(DropManager.class.getName());
	private List<Schedule>									HOT_TIME_SCHEDULE		= new ArrayList<>();
	private List<Drop>										DROP_LIST				= new ArrayList<>();
	private int												MAX_LEVEL_DIFFERENCE	= 9;
	private double											DROP_BOOST				= 1.0;
	private boolean											BUFF_ACTIVE				= false;
	private Schedule										BUFF_SCHEDULE;
	//
	private SimpleDateFormat								DATE_FORMATTER			= new SimpleDateFormat("yyyy-MM-dd");
	private Map<String, Map<String, Map<Integer, Long>>>	DATA_ACTIVE				= new HashMap<>();
	private Map<String, Map<String, Map<Integer, Long>>>	DATA_PASSIVE			= new HashMap<>();
	//
	private final static int								LCOIN					= 91663;
	
	protected DropManager()
	{
		loadActiveSql();
		loadPassiveSql();
		HOT_TIME_SCHEDULE.add(new Schedule("16:05:00", "17:00:00", 2.0));
		HOT_TIME_SCHEDULE.add(new Schedule("19:42:00", "20:00:00", 2.0));
		// DROP_LIST.add(new Drop(57, 100_000, 200_000, 50, 1_000_000));
		DROP_LIST.add(new Drop(LCOIN, 1, 2, 12, 10000, false)); // Normal Char
		DROP_LIST.add(new Drop(LCOIN, 4, 6, 21, 60000, true)); // premium Char
		// DROP_LIST.add(new Drop(WATERMELON_SEED_TIME_LIMITED, 1, 1, 50, 300, false)); // premium Char
		// DROP_LIST.add(new Drop(WATERMELON_SEED_TIME_LIMITED, 1, 1, 50, 300, true)); // premium Char
		BUFF_SCHEDULE = new Schedule("00:00:00", "23:59:00", 0);
		final Calendar c = Calendar.getInstance();
		c.set(Calendar.MINUTE, 0);
		c.set(Calendar.SECOND, 0);
		c.set(Calendar.MILLISECOND, 0);
		while (c.getTimeInMillis() < System.currentTimeMillis())
		{
			c.add(Calendar.MINUTE, 1);
		}
		long init = c.getTimeInMillis() - System.currentTimeMillis();
		ThreadPool.scheduleAtFixedRate(() ->
		{
			checkSchedule();
		}, init, 1 * 60 * 1000);
		ThreadPool.scheduleAtFixedRate(() ->
		{
			saveActiveSql();
		}, 1 * 60 * 1000, 1 * 60 * 1000);
		ThreadPool.scheduleAtFixedRate(() ->
		{
			savePassiveSql();
		}, 1 * 60 * 1000, 1 * 60 * 1000);
		checkSchedule();
	}
	
	public long getMaxDrop(PlayerInstance player, int itemId)
	{
		boolean p = PremiumManager.getInstance().getPremiumExpiration(player.getObjectId()) > 0;
		long max = p ? Config.LCOIN_DROP_DAILY_MAX_PREMIUM_CHAR : Config.LCOIN_DROP_DAILY_NORMAL_CHAR;
		return max;
	}
	
	private void loadActiveSql()
	{
		String account_name;
		int item_id;
		long item_count;
		try (Connection con = DatabaseFactory.getConnection(); PreparedStatement ps = con.prepareStatement("SELECT * FROM character_drop_active WHERE date=?"))
		{
			String date = getDate();
			ps.setString(1, date);
			DATA_ACTIVE.put(date, new HashMap<>());
			Map<String, Map<Integer, Long>> data = DATA_ACTIVE.get(date);
			try (ResultSet rs = ps.executeQuery())
			{
				while (rs.next())
				{
					account_name = rs.getString("account_name");
					item_id = rs.getInt("item_id");
					item_count = rs.getLong("item_count");
					if (!data.containsKey(account_name))
					{
						data.put(account_name, new HashMap<>());
					}
					data.get(account_name).put(item_id, item_count);
				}
			}
		}
		catch (Exception e)
		{
			e.printStackTrace();
		}
	}
	
	private void loadPassiveSql()
	{
		String account_name;
		int item_id;
		long item_count;
		try (Connection con = DatabaseFactory.getConnection(); PreparedStatement ps = con.prepareStatement("SELECT * FROM character_drop_passive WHERE date=?"))
		{
			String date = getDate();
			ps.setString(1, date);
			DATA_PASSIVE.put(date, new HashMap<>());
			Map<String, Map<Integer, Long>> data = DATA_PASSIVE.get(date);
			try (ResultSet rs = ps.executeQuery())
			{
				while (rs.next())
				{
					account_name = rs.getString("account_name");
					item_id = rs.getInt("item_id");
					item_count = rs.getLong("item_count");
					if (!data.containsKey(account_name))
					{
						data.put(account_name, new HashMap<>());
					}
					data.get(account_name).put(item_id, item_count);
				}
			}
		}
		catch (Exception e)
		{
			e.printStackTrace();
		}
	}
	
	private void saveActiveSql()
	{
		String date = getDate();
		if (!DATA_ACTIVE.containsKey(date))
		{
			return;
		}
		if (DATA_ACTIVE.get(date).isEmpty())
		{
			return;
		}
		try (Connection con = DatabaseFactory.getConnection(); PreparedStatement statement = con.prepareStatement("INSERT INTO `character_drop_active` (`date`, `account_name`, `item_id`, `item_count`) VALUES(?, ?, ?, ?) ON DUPLICATE KEY UPDATE `item_count` = ?"))
		{
			for (Entry<String, Map<Integer, Long>> data : DATA_ACTIVE.get(date).entrySet())
			{
				for (Entry<Integer, Long> data2 : data.getValue().entrySet())
				{
					statement.setString(1, date);
					statement.setString(2, data.getKey());
					statement.setInt(3, data2.getKey());
					statement.setLong(4, data2.getValue());
					statement.setLong(5, data2.getValue());
					statement.addBatch();
				}
			}
			statement.executeBatch();
		}
		catch (Exception e)
		{
			e.printStackTrace();
		}
	}
	
	private void savePassiveSql()
	{
		String date = getDate();
		if (!DATA_PASSIVE.containsKey(date))
		{
			return;
		}
		if (DATA_PASSIVE.get(date).isEmpty())
		{
			return;
		}
		try (Connection con = DatabaseFactory.getConnection(); PreparedStatement statement = con.prepareStatement("INSERT INTO `character_drop_passive` (`date`, `account_name`, `item_id`, `item_count`) VALUES(?, ?, ?, ?) ON DUPLICATE KEY UPDATE `item_count` = ?"))
		{
			for (Entry<String, Map<Integer, Long>> data : DATA_PASSIVE.get(date).entrySet())
			{
				for (Entry<Integer, Long> data2 : data.getValue().entrySet())
				{
					statement.setString(1, date);
					statement.setString(2, data.getKey());
					statement.setInt(3, data2.getKey());
					statement.setLong(4, data2.getValue());
					statement.setLong(5, data2.getValue());
					statement.addBatch();
				}
			}
			statement.executeBatch();
		}
		catch (Exception e)
		{
			e.printStackTrace();
		}
	}
	
	private void checkSchedule()
	{
		final LocalTime now = LocalTime.now().plusSeconds(1);
		if (now.isAfter(LocalTime.parse(BUFF_SCHEDULE.time_from)) && now.isBefore(LocalTime.parse(BUFF_SCHEDULE.time_to)))
		{
			BUFF_ACTIVE = false; // default true
		}
		else
		{
			BUFF_ACTIVE = false;
		}
		for (Schedule s : HOT_TIME_SCHEDULE)
		{
			if (now.isAfter(LocalTime.parse(s.time_from)) && now.isBefore(LocalTime.parse(s.time_to)))
			{
				DROP_BOOST = s.drop_boost;
				return;
			}
		}
		DROP_BOOST = 1.0;
	}
	
	public boolean isBuffActive()
	{
		return BUFF_ACTIVE;
	}
	
	public void dropItem(PlayerInstance player, MonsterInstance monster)
	{
		if (monster.getLevel() + MAX_LEVEL_DIFFERENCE < player.getLevel())
		{
			player.sendMessage("Max level difference: " + MAX_LEVEL_DIFFERENCE);
			return;
		}
		boolean p = PremiumManager.getInstance().getPremiumExpiration(player.getObjectId()) > 0;
		for (Drop drop : DROP_LIST)
		{
			// Calculate final drop chance with bonuses
			double finalChance = drop.item_chance;

			// Apply Zone Dominance L-Coin drop rate bonus
			if (drop.item_id == LCOIN && Config.ENABLE_ZONE_DOMINANCE_SYSTEM && player.getFaction() != null && player.getFaction() != Faction.NONE)
			{
				int lcoinBonus = club.projectessence.gameserver.instancemanager.ZoneDominanceManager.getInstance().getLCoinDropBonus(player.getFaction());
				if (lcoinBonus > 0)
				{
					double bonusMultiplier = 1.0 + (lcoinBonus / 100.0);
					finalChance *= bonusMultiplier;
					if (Config.LOG_FACTION_DETAILS)
					{
						LOGGER.fine("Zone Dominance L-Coin drop rate bonus applied in passive drop: +" + lcoinBonus + "% (x" + bonusMultiplier + ")");
					}
				}
			}

			if (finalChance > Rnd.get(100))
			{
				if (drop.premium && !p)
				{
					continue;
				}
				if (!drop.premium && p)
				{
					continue;
				}
				Map<Integer, Long> data = getActiveItemData(player.getAccountName(), drop.item_id);
				if (drop.daily_max <= data.get(drop.item_id))
				{
					continue;
				}
				long amount = Rnd.get(drop.item_min, drop.item_max);
				if (DROP_BOOST > 1.0)
				{
					amount *= DROP_BOOST;
				}


				if (amount + data.get(drop.item_id) > drop.daily_max)
				{
					amount = drop.daily_max - data.get(drop.item_id);
				}
				data.put(drop.item_id, data.get(drop.item_id) + amount);
				player.addItem("drop_manager", drop.item_id, amount, player, true);
			}
		}
	}
	
	public Map<Integer, Long> getActiveItemData(String accountName, int itemId)
	{
		String date = getDate();
		if (!DATA_ACTIVE.containsKey(date))
		{
			DATA_ACTIVE.put(date, new HashMap<>());
		}
		Map<String, Map<Integer, Long>> DATA2 = DATA_ACTIVE.get(date);
		if (!DATA2.containsKey(accountName))
		{
			DATA2.put(accountName, new HashMap<>());
		}
		if (!DATA2.get(accountName).containsKey(itemId))
		{
			DATA2.get(accountName).put(itemId, 0L);
		}
		return DATA2.get(accountName);
	}
	
	public Map<Integer, Long> getPassiveItemData(String accountName, int itemId)
	{
		String date = getDate();
		if (!DATA_PASSIVE.containsKey(date))
		{
			DATA_PASSIVE.put(date, new HashMap<>());
		}
		Map<String, Map<Integer, Long>> DATA2 = DATA_PASSIVE.get(date);
		if (!DATA2.containsKey(accountName))
		{
			DATA2.put(accountName, new HashMap<>());
		}
		if (!DATA2.get(accountName).containsKey(itemId))
		{
			DATA2.get(accountName).put(itemId, 0L);
		}
		return DATA2.get(accountName);
	}
	
	private String getDate()
	{
		return DATE_FORMATTER.format(new Date());
	}
	
	private class Schedule
	{
		public String	time_from;
		public String	time_to;
		public double	drop_boost;
		
		private Schedule(String time_from, String time_to, double drop_boost)
		{
			this.time_from = time_from;
			this.time_to = time_to;
			this.drop_boost = drop_boost;
		}
	}
	
	private class Drop
	{
		int		item_id;
		long	item_min;
		long	item_max;
		double	item_chance;
		int		daily_max;
		boolean	premium;
		
		Drop(int item_id, long item_min, long item_max, double item_chance, int daily_max, boolean premium)
		{
			this.item_id = item_id;
			this.item_min = item_min;
			this.item_max = item_max;
			this.item_chance = item_chance;
			this.daily_max = daily_max;
			this.premium = premium;
		}
	}
	
	private Map<Integer, ScheduledFuture<?>> _passive_income_drop = new HashMap<>();
	
	public void startPassiveDrop(PlayerInstance player)
	{
		if (!_passive_income_drop.containsKey(player.getObjectId()))
		{
			_passive_income_drop.put(player.getObjectId(), null);
		}
		if (_passive_income_drop.get(player.getObjectId()) == null)
		{
			@SuppressWarnings("static-access")
			ScheduledFuture<?> thread = ThreadPool.get().scheduleAtFixedRate(new PassiveDrop(player), 35000, 35000);
			_passive_income_drop.put(player.getObjectId(), thread);
		}
	}
	
	public void stopPassiveDrop(PlayerInstance player)
	{
		if (_passive_income_drop.get(player.getObjectId()) != null)
		{
			_passive_income_drop.get(player.getObjectId()).cancel(true);
			_passive_income_drop.put(player.getObjectId(), null);
		}
	}
	
	public class PassiveDrop implements Runnable
	{
		private final PlayerInstance _player;
		
		public PassiveDrop(PlayerInstance player)
		{
			_player = player;
		}
		
		@Override
		public void run()
		{
			// System.out.println(Rnd.get(100));
			if (_player == null || !_player.isOnline())
			{
				return;
			}
			if (!_player.isInCombat())
			{
				return;
			}
			boolean p = PremiumManager.getInstance().getPremiumExpiration(_player.getObjectId()) > 0;
			Map<Integer, Long> data = DropManager.getInstance().getPassiveItemData(_player.getAccountName(), LCOIN);
			long max = lcoinDropMaxDaily(_player);
			if (max <= data.get(LCOIN))
			{
				return;
			}
			long amount;
			switch (_player.getLevel())
			{
				default:
				case 40:
				case 41:
				case 42:
				case 43:
				case 44:
				case 45:
				case 46:
				case 47:
				case 48:
				case 49:
				case 50:
				case 51:
				case 52:
				case 53:
				case 54:
				case 55:
				case 56:
				case 57:
				case 58:
				case 59:
				case 60:
				case 61:
				case 62:
				case 63:
				case 64:
				case 65:
				case 66:
				case 67:
				case 68:
				case 69:
				case 70:
				case 71:
				case 72:
				case 73:
				case 74:
				{
					amount = p ? 5 : 3;
					break;
				}
				case 75:
				{
					amount = p ? Config.LCOIN_DROP_AMOUNT_PREMIUM_CHAR_LEVEL_75 : Config.LCOIN_DROP_AMOUNT_NORMAL_CHAR_LEVEL_75;
					break;
				}
				case 76:
				{
					amount = p ? Config.LCOIN_DROP_AMOUNT_PREMIUM_CHAR_LEVEL_76 : Config.LCOIN_DROP_AMOUNT_NORMAL_CHAR_LEVEL_76;
					break;
				}
				case 77:
				{
					amount = p ? Config.LCOIN_DROP_AMOUNT_PREMIUM_CHAR_LEVEL_77 : Config.LCOIN_DROP_AMOUNT_NORMAL_CHAR_LEVEL_77;
					break;
				}
				case 78:
				{
					amount = p ? Config.LCOIN_DROP_AMOUNT_PREMIUM_CHAR_LEVEL_78 : Config.LCOIN_DROP_AMOUNT_NORMAL_CHAR_LEVEL_78;
					break;
				}
				case 79:
				{
					amount = p ? Config.LCOIN_DROP_AMOUNT_PREMIUM_CHAR_LEVEL_79 : Config.LCOIN_DROP_AMOUNT_NORMAL_CHAR_LEVEL_79;
					break;
				}
				case 80:
				{
					amount = p ? Config.LCOIN_DROP_AMOUNT_PREMIUM_CHAR_LEVEL_80 : Config.LCOIN_DROP_AMOUNT_NORMAL_CHAR_LEVEL_80;
					break;
				}
				case 81:
				{
					amount = p ? Config.LCOIN_DROP_AMOUNT_PREMIUM_CHAR_LEVEL_81 : Config.LCOIN_DROP_AMOUNT_NORMAL_CHAR_LEVEL_81;
					break;
				}
				case 82:
				{
					amount = p ? Config.LCOIN_DROP_AMOUNT_PREMIUM_CHAR_LEVEL_82 : Config.LCOIN_DROP_AMOUNT_NORMAL_CHAR_LEVEL_82;
					break;
				}
				case 83:
				{
					amount = p ? Config.LCOIN_DROP_AMOUNT_PREMIUM_CHAR_LEVEL_83 : Config.LCOIN_DROP_AMOUNT_NORMAL_CHAR_LEVEL_83;
					break;
				}
				case 84:
				{
					amount = p ? Config.LCOIN_DROP_AMOUNT_PREMIUM_CHAR_LEVEL_84 : Config.LCOIN_DROP_AMOUNT_NORMAL_CHAR_LEVEL_84;
					break;
				}
				case 85:
				{
					amount = p ? Config.LCOIN_DROP_AMOUNT_PREMIUM_CHAR_LEVEL_85 : Config.LCOIN_DROP_AMOUNT_NORMAL_CHAR_LEVEL_85;
					break;
				}
				case 86:
				{
					amount = p ? Config.LCOIN_DROP_AMOUNT_PREMIUM_CHAR_LEVEL_86 : Config.LCOIN_DROP_AMOUNT_NORMAL_CHAR_LEVEL_86;
					break;
				}
				case 87:
				{
					amount = p ? Config.LCOIN_DROP_AMOUNT_PREMIUM_CHAR_LEVEL_87 : Config.LCOIN_DROP_AMOUNT_NORMAL_CHAR_LEVEL_87;
					break;
				}
				case 88:
				{
					amount = p ? Config.LCOIN_DROP_AMOUNT_PREMIUM_CHAR_LEVEL_88 : Config.LCOIN_DROP_AMOUNT_NORMAL_CHAR_LEVEL_88;
					break;
				}
				case 89:
				{
					amount = p ? Config.LCOIN_DROP_AMOUNT_PREMIUM_CHAR_LEVEL_89 : Config.LCOIN_DROP_AMOUNT_NORMAL_CHAR_LEVEL_89;
					break;
				}
				case 90:
				case 91:
				case 92:
				case 93:
				case 94:
				case 95:
				case 96:
				case 97:
				case 98:
				case 99:
				{
					amount = p ? Config.LCOIN_DROP_AMOUNT_NORMAL_CHAR_LEVEL_90_99 : Config.LCOIN_DROP_AMOUNT_PREMIUM_CHAR_LEVEL_90_99;
					break;
				}
			}
			if (amount + data.get(LCOIN) > max)
			{
				amount = max - data.get(LCOIN);
			}
			data.put(LCOIN, data.get(LCOIN) + amount);
			_player.addItem("drop_manager", LCOIN, amount, _player, true);
		}
	}
	
	public long lcoinDropMaxDaily(PlayerInstance player)
	{
		boolean p = PremiumManager.getInstance().getPremiumExpiration(player.getObjectId()) > 0;
		long max = p ? Config.LCOIN_DROP_DAILY_MAX_PREMIUM_CHAR : Config.LCOIN_DROP_DAILY_NORMAL_CHAR;
		return max;
	}
	
	public static DropManager getInstance()
	{
		return SingletonHolder.INSTANCE;
	}
	
	private static class SingletonHolder
	{
		protected static final DropManager INSTANCE = new DropManager();
	}
}