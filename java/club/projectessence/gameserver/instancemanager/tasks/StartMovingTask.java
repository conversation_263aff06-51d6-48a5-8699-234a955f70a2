/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.instancemanager.tasks;

import club.projectessence.gameserver.instancemanager.WalkingManager;
import club.projectessence.gameserver.model.actor.Npc;

/**
 * Task which starts npc movement.
 *
 * <AUTHOR>
 */
public class StartMovingTask implements Runnable {
	final Npc _npc;
	final String _routeName;

	public StartMovingTask(Npc npc, String routeName) {
		_npc = npc;
		_routeName = routeName;
	}

	@Override
	public void run() {
		if (_npc != null) {
			WalkingManager.getInstance().startMoving(_npc, _routeName);
		}
	}
}
