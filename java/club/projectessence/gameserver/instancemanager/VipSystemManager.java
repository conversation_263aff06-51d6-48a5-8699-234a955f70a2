/*
 * This file is part of the L2J Mobius project.
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.instancemanager;

import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.function.Consumer;

import club.projectessence.gameserver.data.xml.SkillData;
import club.projectessence.gameserver.data.xml.VipSystemData;
import club.projectessence.gameserver.model.VipInfo;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.model.events.Containers;
import club.projectessence.gameserver.model.events.EventType;
import club.projectessence.gameserver.model.events.impl.creature.player.OnPlayerLoad;
import club.projectessence.gameserver.model.events.impl.creature.player.OnPlayerLogin;
import club.projectessence.gameserver.model.events.listeners.ConsumerEventListener;
import club.projectessence.gameserver.model.skills.Skill;
import club.projectessence.gameserver.model.variables.AccountVariables;
import club.projectessence.gameserver.network.serverpackets.vipsystem.ExBRNewIconCashBtnWnd;
import club.projectessence.gameserver.network.serverpackets.vipsystem.ReceiveVipInfo;

/**
 * <AUTHOR>
 */
public final class VipSystemManager
{
	private final ConsumerEventListener _vipLoginListener = new ConsumerEventListener(null, EventType.ON_PLAYER_LOGIN, (Consumer<OnPlayerLogin>) this::onVipLogin, this);
	
	protected VipSystemManager()
	{
		Containers.Global().addListener(new ConsumerEventListener(Containers.Global(), EventType.ON_PLAYER_LOAD, (Consumer<OnPlayerLoad>) this::onPlayerLoaded, this));
	}
	
	private void onPlayerLoaded(OnPlayerLoad event)
	{
		if (!VipSystemData.getInstance().isEnabled())
		{
			return;
		}
		final PlayerInstance player = event.getPlayer();
		player.setVipTier(getVipTier(player));
		if (player.getVipTier() > 0)
		{
			manageTier(player);
			player.addListener(_vipLoginListener);
		}
		else
		{
			player.sendPacket(new ReceiveVipInfo(player));
			player.sendPacket(new ExBRNewIconCashBtnWnd(0));
		}
	}
	
	private boolean canReceiveGift(PlayerInstance player)
	{
		if (!VipSystemData.getInstance().isEnabled())
		{
			return false;
		}
		if (player.getVipTier() <= 0)
		{
			return false;
		}
		return player.getAccountVariables().getLong(AccountVariables.VIP_ITEM_BOUGHT, 0) <= 0;
	}
	
	private void onVipLogin(OnPlayerLogin event)
	{
		if (!VipSystemData.getInstance().isEnabled())
		{
			return;
		}
		final PlayerInstance player = event.getPlayer();
		if (canReceiveGift(player))
		{
			player.sendPacket(new ExBRNewIconCashBtnWnd(1));
		}
		else
		{
			player.sendPacket(new ExBRNewIconCashBtnWnd(0));
		}
		player.removeListener(_vipLoginListener);
		player.sendPacket(new ReceiveVipInfo(player));
	}
	
	public void manageTier(PlayerInstance player)
	{
		if (!VipSystemData.getInstance().isEnabled())
		{
			return;
		}
		if (!checkVipTierExpiration(player))
		{
			player.sendPacket(new ReceiveVipInfo(player));
		}
		if (player.getVipTier() > 1)
		{
			for (int i = player.getVipTier() - 1; i >= 0; i--)
			{
				final int oldSkillId = VipSystemData.getInstance().getSkillId((byte) (i));
				if (oldSkillId > 0)
				{
					player.removeSkill(oldSkillId);
				}
			}
		}
		final int skillId = VipSystemData.getInstance().getSkillId(player.getVipTier());
		if (skillId > 0)
		{
			final Skill skill = SkillData.getInstance().getSkill(skillId, 1);
			if (skill != null)
			{
				player.addSkill(skill);
			}
		}
	}
	
	public int getVipTier(PlayerInstance player)
	{
		return getVipInfo(player).tier();
	}
	
	public int getVipTier(long points)
	{
		int temp = getVipInfo(points).tier();
		final int maxTier = VipSystemData.getInstance().getMaxTier();
		if (temp > maxTier)
		{
			temp = maxTier;
		}
		return temp;
	}
	
	private VipInfo getVipInfo(PlayerInstance player)
	{
		return getVipInfo(player.getVipPoints());
	}
	
	private VipInfo getVipInfo(long points)
	{
		for (int i = 0; i < VipSystemData.getInstance().getVipTiers().size(); i++)
		{
			if (points < VipSystemData.getInstance().getVipTiers().get(i).pointsRequired())
			{
				int temp = (byte) (i - 1);
				final int maxTier = VipSystemData.getInstance().getMaxTier();
				if (temp > maxTier)
				{
					temp = maxTier;
				}
				return VipSystemData.getInstance().getVipTiers().get(temp);
			}
		}
		return VipSystemData.getInstance().getVipTiers().get(VipSystemData.getInstance().getMaxTier());
	}
	
	public long getPointsDepreciatedOnLevel(int vipTier)
	{
		final VipInfo tier = VipSystemData.getInstance().getVipTiers().get(vipTier);
		if (tier == null)
		{
			return 0;
		}
		return tier.pointsDepreciated();
	}
	
	public long getPointsToLevel(int vipTier)
	{
		final VipInfo tier = VipSystemData.getInstance().getVipTiers().get(vipTier);
		if (tier == null)
		{
			return 0;
		}
		return tier.pointsRequired();
	}
	
	public boolean checkVipTierExpiration(PlayerInstance player)
	{
		final Instant now = Instant.now();
		if (now.isAfter(Instant.ofEpochMilli(player.getVipTierExpiration())))
		{
			player.updateVipPoints(-getPointsDepreciatedOnLevel(player.getVipTier()));
			player.setVipTierExpiration(Instant.now().plus(30, ChronoUnit.DAYS).toEpochMilli());
			return true;
		}
		return false;
	}
	
	public static VipSystemManager getInstance()
	{
		return Singleton.INSTANCE;
	}
	
	private static class Singleton
	{
		protected static final VipSystemManager INSTANCE = new VipSystemManager();
	}
}