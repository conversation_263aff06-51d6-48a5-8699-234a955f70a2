package club.projectessence.gameserver.instancemanager.events.GoldFestival;

/**
 * <AUTHOR>
 */
public class GoldFestivalEventReward {
	private static final int TOP_GRADE = 1;
	private static final int MIDDLE_GRADE = 2;
	private static final int LOW_GRADE = 3;

	public int _id;
	public long _count;
	public int _grade;
	public long _totalAmount;
	public long _currentAmount;
	public double _chance;

	public GoldFestivalEventReward(int id, long count, int grade, long totalAmount, long currentAmount, double chance) {
		_id = id;
		_count = count;
		_grade = grade;
		_totalAmount = totalAmount;
		_currentAmount = currentAmount;
		_chance = chance;
	}

	public GoldFestivalEventReward(GoldFestivalEventReward reward) {
		_id = reward._id;
		_count = reward._count;
		_grade = reward._grade;
		_totalAmount = reward._totalAmount;
		_currentAmount = reward._currentAmount;
		_chance = reward._chance;
	}

	public boolean hasAvailableReward() {
		return _totalAmount > 0;
	}

	public boolean isTopGrade() {
		return _grade == TOP_GRADE;
	}

	public boolean isMiddleGrade() {
		return _grade == MIDDLE_GRADE;
	}

	public boolean isLowGrade() {
		return _grade == LOW_GRADE;
	}

	public int getId() {
		return _id;
	}
}