package club.projectessence.gameserver.instancemanager.events.GoldFestival;

import java.io.File;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ScheduledFuture;
import java.util.logging.Logger;
import java.util.stream.Collectors;

import org.w3c.dom.Document;
import org.w3c.dom.NamedNodeMap;
import org.w3c.dom.Node;

import club.projectessence.commons.concurrent.ThreadPool;
import club.projectessence.commons.util.IXmlReader;
import club.projectessence.commons.util.Rnd;
import club.projectessence.gameserver.enums.ChatType;
import club.projectessence.gameserver.enums.MailType;
import club.projectessence.gameserver.instancemanager.MailManager;
import club.projectessence.gameserver.model.Message;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.model.clan.Clan;
import club.projectessence.gameserver.model.itemcontainer.Mail;
import club.projectessence.gameserver.network.GameClient;
import club.projectessence.gameserver.network.serverpackets.CreatureSay;
import club.projectessence.gameserver.network.serverpackets.ExItemAnnounce;
import club.projectessence.gameserver.network.serverpackets.ExItemAnnounce.ItemAnnounceType;
import club.projectessence.gameserver.network.serverpackets.festivalbm.ExFestivalBmAllItemInfo;
import club.projectessence.gameserver.network.serverpackets.festivalbm.ExFestivalBmGame;
import club.projectessence.gameserver.network.serverpackets.festivalbm.ExFestivalBmInfo;
import club.projectessence.gameserver.network.serverpackets.festivalbm.ExFestivalBmTopItemInfo;
import club.projectessence.gameserver.util.Broadcast;

/**
 * <AUTHOR>
 */
public class GoldFestivalEvent implements IXmlReader
{
	private static final Logger									LOGGER					= Logger.getLogger(GoldFestivalEvent.class.getName());
	private static final int									UPDATE_INTERVAL			= 5_000;
	private static final int									CLAN_REWARD_ITEM_ID		= 94834;
	private static final int									CLAN_REWARD_ITEM_COUNT	= 1000;
	private static final int									TICKET					= 94871;
	private static final int									TICKET_AMOUNT_PER_GAME	= 1;
	// private static final int FIRST_START = 11;
	// private static final int FIRST_END = 13;
	// private static final int SECOND_START = 20;
	// private static final int SECOND_END = 22;
	private static final int									FIRST_START				= 21;
	private static final int									FIRST_END				= 23;
	// Mail
	public static String										MAIL_TITLE				= "Gold Festival Clan Reward";
	public static String										MAIL_BODY				= "Your clan member won one of the main prize of the festival. All (online) clan members receive an additional reward.";
	private final Map<Integer, List<GoldFestivalEventReward>>	_rewardData				= new HashMap<>();
	private final Set<PlayerInstance>							_listToUpdate			= ConcurrentHashMap.newKeySet();
	private boolean												_isEventEnabled;
	private List<GoldFestivalEventReward>						_activeRewards;
	private boolean												_exchangeEnabled;
	private long												_startTime;
	private long												_endTime;
	private ScheduledFuture<?>									_updateTask				= null;
	private ScheduledFuture<?>									_startTask				= null;
	private ScheduledFuture<?>									_endTask				= null;
	
	private GoldFestivalEvent()
	{
		load();
		if (_isEventEnabled)
		{
			startEvent();
		}
	}
	
	public static GoldFestivalEvent getInstance()
	{
		return SingletonHolder.INSTANCE;
	}
	
	@Override
	public void load()
	{
		_rewardData.clear();
		parseDatapackFile("data/GoldFestival.xml");
		LOGGER.warning(getClass().getSimpleName() + ": Loaded " + _rewardData.size() + " games.");
		for (Entry<Integer, List<GoldFestivalEventReward>> entry : _rewardData.entrySet())
		{
			int items = 0;
			double chance = 0;
			for (GoldFestivalEventReward reward : entry.getValue())
			{
				items += reward._totalAmount;
				chance += reward._chance;
			}
			LOGGER.warning(getClass().getSimpleName() + ": Game " + entry.getKey() + " Total amount: " + items + " Total chance: " + chance + "%");
		}
	}
	
	@Override
	public void parseDocument(Document doc, File f)
	{
		for (Node n = doc.getFirstChild(); n != null; n = n.getNextSibling())
		{
			if ("list".equals(n.getNodeName()))
			{
				_isEventEnabled = parseBoolean(n.getAttributes(), "isEnabled");
				for (Node rewards = n.getFirstChild(); rewards != null; rewards = rewards.getNextSibling())
				{
					if ("rewards".equals(rewards.getNodeName()))
					{
						final int game = Integer.parseInt(rewards.getAttributes().getNamedItem("game").getNodeValue());
						for (Node item = rewards.getFirstChild(); item != null; item = item.getNextSibling())
						{
							if ("item".equals(item.getNodeName()))
							{
								final NamedNodeMap attrs = item.getAttributes();
								final int itemId = Integer.parseInt(attrs.getNamedItem("id").getNodeValue());
								final int grade = Integer.parseInt(attrs.getNamedItem("grade").getNodeValue());
								final long itemCount = Long.parseLong(attrs.getNamedItem("count").getNodeValue());
								final long totalAmount = Long.parseLong(attrs.getNamedItem("totalAmount").getNodeValue());
								final double chance = Double.parseDouble(attrs.getNamedItem("chance").getNodeValue());
								List<GoldFestivalEventReward> rewardList = _rewardData.get(game);
								if (rewardList == null)
								{
									rewardList = new ArrayList<>();
									_rewardData.put(game, rewardList);
								}
								rewardList.add(new GoldFestivalEventReward(itemId, itemCount, grade, totalAmount, totalAmount, chance));
							}
						}
					}
				}
			}
		}
	}
	
	private void start()
	{
		if (!_isEventEnabled)
		{
			return;
		}
		LOGGER.warning(getClass().getSimpleName() + ": Started!");
		_exchangeEnabled = true;
		_endTime = getNextEndTime();
		_startTime = 0;
		final int currentGame;
		final Calendar calendar = Calendar.getInstance();
		int h = calendar.get(Calendar.HOUR_OF_DAY);
		if ((h >= FIRST_START) && (h < FIRST_END))
		{
			LOGGER.info(getClass().getSimpleName() + ": Chosen game 1");
			currentGame = 1;
		}
		// else if ((h >= SECOND_START) && (h < SECOND_END))
		// {
		// LOGGER.info(getClass().getSimpleName() + ": Chosen game 2");
		// currentGame = 2;
		// }
		else
		{
			LOGGER.info(getClass().getSimpleName() + ": No game found, adding 30 minutes");
			calendar.add(Calendar.MINUTE, 30);
			h = calendar.get(Calendar.HOUR_OF_DAY);
			if ((h >= FIRST_START) && (h < FIRST_END))
			{
				LOGGER.info(getClass().getSimpleName() + ": Chosen game 1");
				currentGame = 1;
			}
			// else if ((h >= SECOND_START) && (h < SECOND_END))
			// {
			// LOGGER.info(getClass().getSimpleName() + ": Chosen game 2");
			// currentGame = 2;
			// }
			else
			{
				currentGame = 1;
				LOGGER.info(getClass().getSimpleName() + ": No game found, using default game 1");
			}
		}
		initRewards(currentGame);
		sendEventInfoToAll();
		Broadcast.toAllOnlinePlayersOnScreen("Golden Festival has started!");
		if (_updateTask != null)
		{
			_updateTask.cancel(true);
			_updateTask = null;
		}
		_updateTask = ThreadPool.get().scheduleAtFixedRate(this::updateInfo, 500, UPDATE_INTERVAL);
		if (_endTask != null)
		{
			_endTask.cancel(false);
			_endTask = null;
		}
		LOGGER.warning(getClass().getSimpleName() + ": Scheduled end in " + _endTime + " ms.");
		ThreadPool.get().schedule(this::end, _endTime);
	}
	
	private void end()
	{
		_exchangeEnabled = false;
		_startTime = getNextStartTime();
		_endTime = 0;
		for (GoldFestivalEventReward reward : _activeRewards)
		{
			reward._currentAmount = 0;
		}
		if (_updateTask != null)
		{
			_updateTask.cancel(true);
			_updateTask = null;
		}
		if (_endTask != null)
		{
			_endTask.cancel(false);
			_endTask = null;
		}
		updateInfo();
		_listToUpdate.clear();
		sendEventInfoToAll();
		Broadcast.toAllOnlinePlayersOnScreen("Golden Festival has ended!");
		LOGGER.warning(getClass().getSimpleName() + ": Scheduled start in " + _startTime + " ms.");
		ThreadPool.get().schedule(this::start, _startTime);
	}
	
	public synchronized void exchange(PlayerInstance player)
	{
		if (!_isEventEnabled || !hasAvailableRewards())
		{
			return;
		}
		if (player.getLevel() < 85)
		{
			player.sendPacket(new CreatureSay(player, ChatType.SCREEN_ANNOUNCE, "", "Only characters of Lv. 85+ can participate in the event.", 0));
			return;
		}
		int ticketAmount = (int) player.getInventory().getInventoryItemCount(TICKET, -1);
		if (ticketAmount < TICKET_AMOUNT_PER_GAME)
		{
			return;
		}
		double totalChance = 0;
		double totalPossibleChance = 0;
		for (GoldFestivalEventReward reward : _activeRewards)
		{
			if (reward._currentAmount <= 0)
			{
				continue;
			}
			totalPossibleChance += reward._chance;
		}
		final double chance = Rnd.get(0, totalPossibleChance);
		for (GoldFestivalEventReward reward : _activeRewards)
		{
			if (reward._currentAmount <= 0)
			{
				continue;
			}
			totalChance += reward._chance;
			if (totalChance >= chance)
			{
				reward._currentAmount--;
				player.destroyItemByItemId(getClass().getSimpleName(), TICKET, TICKET_AMOUNT_PER_GAME, player, true);
				ticketAmount -= TICKET_AMOUNT_PER_GAME;
				player.addItem(getClass().getSimpleName(), reward._id, reward._count, player, true);
				player.sendPacket(new ExFestivalBmGame(TICKET, ticketAmount, TICKET_AMOUNT_PER_GAME, reward._grade, reward._id, (int) reward._count));
				if (reward.isTopGrade())
				{
					Broadcast.toAllOnlinePlayers(new ExItemAnnounce(player, reward._id, 0, ItemAnnounceType.EVENT_PARTICIPATE));
					// sendClanReward(player);
					if (noTopGradeRewardsRemaining())
					{
						show(player, true);
						updateInfo();
						end();
						return;
					}
					sendEventInfoToAll();
				}
				break;
			}
		}
		show(player, true);
	}
	
	public void show(PlayerInstance player, boolean show)
	{
		if (!_isEventEnabled)
		{
			return;
		}
		if (show)
		{
			if (!_listToUpdate.contains(player))
			{
				_listToUpdate.add(player);
			}
			final int ticketAmount = (int) player.getInventory().getInventoryItemCount(TICKET, -1);
			player.sendPacket(new ExFestivalBmInfo(TICKET, ticketAmount, TICKET_AMOUNT_PER_GAME));
			player.sendPacket(new ExFestivalBmAllItemInfo(getSortedRewards()));
		}
		else
		{
			_listToUpdate.remove(player);
		}
	}
	
	private void sendClanReward(PlayerInstance player)
	{
		if (player != null)
		{
			Clan clan = player.getClan();
			if (clan == null)
			{
				return;
			}
			LOGGER.warning(getClass().getSimpleName() + ": Give Clan Reward->: clan=" + clan + " player=" + player);
			clan.getOnlineMembers(player.getObjectId()).forEach(this::sendMail);
		}
		else
		{
			LOGGER.warning(getClass().getSimpleName() + ": Player is Null!");
		}
	}
	
	private void sendMail(PlayerInstance player)
	{
		final Message msg = new Message(player.getObjectId(), MAIL_TITLE, MAIL_BODY, MailType.SYSTEM);
		final Mail attachment = msg.createAttachments();
		attachment.addItem(getClass().getSimpleName(), CLAN_REWARD_ITEM_ID, CLAN_REWARD_ITEM_COUNT, null, null);
		MailManager.getInstance().sendMessage(msg);
	}
	
	protected void startEvent()
	{
		if (_rewardData.size() == 0)
		{
			LOGGER.warning(getClass().getSimpleName() + ": Empty rewards list.");
			return;
		}
		LOGGER.warning(getClass().getSimpleName() + ": Activated");
		if (_startTask != null)
		{
			_startTask.cancel(false);
			_startTask = null;
		}
		if (_endTask != null)
		{
			_endTask.cancel(false);
			_endTask = null;
		}
		if (_updateTask != null)
		{
			_updateTask.cancel(true);
			_updateTask = null;
		}
		_listToUpdate.clear();
		final Calendar calendar = Calendar.getInstance();
		int h = calendar.get(Calendar.HOUR_OF_DAY);
		if (isExchangePeriod(h))
		{
			start();
		}
		else
		{
			_startTime = getNextStartTime();
			LOGGER.warning(getClass().getSimpleName() + ": Scheduled start in " + _startTime + " ms.");
			_startTask = ThreadPool.get().schedule(this::start, _startTime);
		}
	}
	
	public void onPlayerLogin(PlayerInstance player)
	{
		if (_isEventEnabled && (player != null))
		{
			sendEventInfo(player);
		}
	}
	
	private void initRewards(int currentGame)
	{
		_activeRewards = new ArrayList<>();
		final List<GoldFestivalEventReward> topRewards = new ArrayList<>();
		final List<GoldFestivalEventReward> rewards = new ArrayList<>();
		for (GoldFestivalEventReward eventReward : _rewardData.get(currentGame))
		{
			final GoldFestivalEventReward reward = new GoldFestivalEventReward(eventReward);
			switch (reward._grade)
			{
				case 3: // *
				case 2: // **
				{
					rewards.add(reward);
					break;
				}
				case 1: // ***
				{
					topRewards.add(reward);
					break;
				}
				default:
				{
					LOGGER.warning(getClass().getSimpleName() + ": Incorrect reward grade " + reward._grade);
				}
			}
		}
		if (_activeRewards != null)
		{
			_activeRewards.clear();
		}
		Collections.shuffle(topRewards);
		if (_activeRewards != null)
		{
			_activeRewards.addAll(topRewards.stream().limit(3).collect(Collectors.toList()));
			_activeRewards.addAll(rewards);
		}
	}
	
	private void updateInfo()
	{
		Iterator<PlayerInstance> itr = _listToUpdate.iterator();
		while (itr.hasNext())
		{
			final PlayerInstance player = itr.next();
			final GameClient client = player.getClient();
			if (!player.isOnline() || (client == null) || client.isDetached())
			{
				_listToUpdate.remove(player);
			}
			else
			{
				sendEventInfo(player);
				player.sendPacket(new ExFestivalBmAllItemInfo(getSortedRewards()));
			}
		}
	}
	
	private void sendEventInfo(PlayerInstance player)
	{
		if (!_isEventEnabled || (player == null))
		{
			return;
		}
		player.sendPacket(new ExFestivalBmTopItemInfo(_exchangeEnabled ? getNextEndTime() : getNextStartTime(), _exchangeEnabled, _activeRewards));
	}
	
	private void sendEventInfoToAll()
	{
		if (!_isEventEnabled)
		{
			return;
		}
		Broadcast.toAllOnlinePlayers(new ExFestivalBmTopItemInfo(_exchangeEnabled ? getNextEndTime() : getNextStartTime(), _exchangeEnabled, _activeRewards));
	}
	
	private List<GoldFestivalEventReward> getSortedRewards()
	{
		List<GoldFestivalEventReward> showRewards = new ArrayList<>(_activeRewards);
		showRewards.sort(Comparator.comparing(GoldFestivalEventReward::getId));
		return showRewards;
	}
	
	private int getNextEndTime()
	{
		int time = 0;
		if (_exchangeEnabled)
		{
			final Calendar calendar = Calendar.getInstance();
			int h = calendar.get(Calendar.HOUR_OF_DAY);
			int m = calendar.get(Calendar.MINUTE);
			int s = calendar.get(Calendar.SECOND);
			int ms = calendar.get(Calendar.MILLISECOND);
			time += 3600000 - ((m * 60000) + (s * 1000) + ms);
			if ((h >= FIRST_START) && (h < FIRST_END))
			{
				time += 3600000 * ((FIRST_END - h) - 1);
			}
			// if ((h >= SECOND_START) && (h < SECOND_END))
			// {
			// time += 3600000 * ((SECOND_END - h) - 1);
			// }
		}
		return time;
	}
	
	private int getNextStartTime()
	{
		int time = 0;
		if (!_exchangeEnabled)
		{
			final Calendar calendar = Calendar.getInstance();
			int h = calendar.get(Calendar.HOUR_OF_DAY);
			int m = calendar.get(Calendar.MINUTE);
			int s = calendar.get(Calendar.SECOND);
			int ms = calendar.get(Calendar.MILLISECOND);
			time += 3600000 - ((m * 60000) + (s * 1000) + ms); // minutes
			// if ((h >= FIRST_START) && (h < SECOND_START)) // time between 1 and 2 games (day)
			// {
			// time += 3600000 * ((SECOND_START - h) - 1);
			// }
			// else if ((h >= SECOND_START) || (h < FIRST_START)) // time between 2 and 1 games (night)
			{
				if (h >= FIRST_START)
				{
					time += 3600000 * ((FIRST_START + 24) - h - 1);
				}
				else
				{
					time += 3600000 * (FIRST_START - h - 1);
				}
			}
		}
		return time;
	}
	
	private boolean isExchangePeriod(int h)
	{
		if (!_isEventEnabled)
		{
			return false;
		}
		return ((h >= FIRST_START) && (h < FIRST_END));// || ((h >= SECOND_START) && (h < SECOND_END));
	}
	
	private boolean hasAvailableRewards()
	{
		return _activeRewards.stream().anyMatch(GoldFestivalEventReward::hasAvailableReward);
	}
	
	private boolean noTopGradeRewardsRemaining()
	{
		for (GoldFestivalEventReward reward : _activeRewards)
		{
			if (reward.isTopGrade() && (reward._currentAmount > 0))
			{
				return false;
			}
		}
		return true;
	}
	
	public void adminInitRewards(int game)
	{
		LOGGER.info(getClass().getSimpleName() + ": initRewards for game " + game + " called by admin.");
		initRewards(game);
		sendEventInfoToAll();
		Broadcast.toAllOnlinePlayers(new ExFestivalBmAllItemInfo(getSortedRewards()));
	}
	
	public void adminStartEvent()
	{
		LOGGER.info(getClass().getSimpleName() + ": start called by admin.");
		if (_updateTask != null)
		{
			_updateTask.cancel(true);
			_updateTask = null;
		}
		if (_startTask != null)
		{
			_startTask.cancel(true);
			_startTask = null;
		}
		if (_endTask != null)
		{
			_endTask.cancel(true);
			_endTask = null;
		}
		start();
		Broadcast.toAllOnlinePlayers(new ExFestivalBmAllItemInfo(getSortedRewards()));
	}
	
	public void adminEndEvent()
	{
		LOGGER.info(getClass().getSimpleName() + ": end called by admin.");
		if (_updateTask != null)
		{
			_updateTask.cancel(true);
			_updateTask = null;
		}
		if (_startTask != null)
		{
			_startTask.cancel(true);
			_startTask = null;
		}
		if (_endTask != null)
		{
			_endTask.cancel(true);
			_endTask = null;
		}
		end();
		Broadcast.toAllOnlinePlayers(new ExFestivalBmAllItemInfo(getSortedRewards()));
	}
	
	private static class SingletonHolder
	{
		protected static final GoldFestivalEvent INSTANCE = new GoldFestivalEvent();
	}
}