/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.instancemanager.events;

import club.projectessence.Config;
import club.projectessence.gameserver.model.Party;
import club.projectessence.gameserver.model.actor.Attackable;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.network.SystemMessageId;
import club.projectessence.gameserver.network.serverpackets.SystemMessage;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.logging.Logger;

/**
 * <AUTHOR>
 */
public class EventFixedDropManager {
	protected static final Logger LOGGER = Logger.getLogger(EventFixedDropManager.class.getName());
	private static final Map<String, Long> _timestamps = new ConcurrentHashMap<>();
	private static final Map<String, Long> _timestamps2 = new ConcurrentHashMap<>();

	private EventFixedDropManager() {

	}

	public static EventFixedDropManager getInstance() {
		return SingletonHolder.INSTANCE;
	}

	public void tryItemDrop(Party party, PlayerInstance player, Attackable attackable) {
		if (!Config.EVENT_FIXED_DROP_ENABLED) {
			return;
		}
		if (((player.getLevel() < 40) || (attackable.getLevel() < 40)) || (Math.abs(player.getLevel() - attackable.getLevel()) >= 15) || player.isDead()) {
			return;
		}
		final long currentMillis = System.currentTimeMillis();
		final long delay;
		switch (attackable.getId()) {
			case 22271:
			case 22272: {
				delay = 110 * 1000;
				break;
			}
			case 29371:
			case 29372:
			case 29373:  {
				delay = 70 * 1000;
				break;
			}
			default: {
				delay = -1;
			}

		}
		if (delay >= 0 && currentMillis > (_timestamps.getOrDefault(player.getAccountName(), 0L) + delay/*(Config.EVENT_FIXED_DROP_DELAY)*/)) {
			_timestamps.put(player.getAccountName(), currentMillis);
			final int itemId = 97145;
			final int dropCount = 1;
			player.addItem("Fixed Event Drop", itemId, dropCount, null, true);

			// Send messages to other party members about reward
			if (party != null) {
				// if (dropCount > 1)
				// {
				// final SystemMessage msg = new SystemMessage(SystemMessageId.C1_HAS_OBTAINED_S3_S2);
				// msg.addString(player.getName());
				// msg.addItemName(Inventory.LCOIN_ID);
				// msg.addLong(dropCount);
				// party.broadcastToPartyMembers(player, msg);
				// }
				// else
				// {
				final SystemMessage msg = new SystemMessage(SystemMessageId.C1_HAS_OBTAINED_S2);
				msg.addString(player.getName());
				msg.addItemName(itemId);
				party.broadcastToPartyMembers(player, msg);
				// }
			}
		}

		if (currentMillis > (_timestamps2.getOrDefault(player.getAccountName(), 0L) + 25_000/*(Config.EVENT_FIXED_DROP_DELAY)*/)) {
			_timestamps2.put(player.getAccountName(), currentMillis);
			final int itemId = 71035;
			final int dropCount = 1;
			player.addItem("Fixed Event Drop", itemId, dropCount, null, true);

			// Send messages to other party members about reward
			if (party != null) {
				final SystemMessage msg = new SystemMessage(SystemMessageId.C1_HAS_OBTAINED_S2);
				msg.addString(player.getName());
				msg.addItemName(itemId);
				party.broadcastToPartyMembers(player, msg);
			}
		}
	}

	private static class SingletonHolder {
		protected static final EventFixedDropManager INSTANCE = new EventFixedDropManager();
	}
}
