package club.projectessence.gameserver.instancemanager;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.text.SimpleDateFormat;
import java.time.Duration;
import java.time.Instant;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Future;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.locks.ReentrantReadWriteLock;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.logging.Logger;
import java.util.stream.Collectors;

import club.projectessence.Config;
import club.projectessence.commons.concurrent.ThreadPool;
import club.projectessence.commons.database.DatabaseFactory;
import club.projectessence.gameserver.data.sql.CharNameTable;
import club.projectessence.gameserver.data.xml.SkillData;
import club.projectessence.gameserver.discord.DiscordBotManager;
import club.projectessence.gameserver.enums.Faction;
import club.projectessence.gameserver.enums.FactionLeaderPrivilegesType;
import club.projectessence.gameserver.enums.FactionLeaderStateType;
import club.projectessence.gameserver.enums.LeadershipLevel;
import club.projectessence.gameserver.enums.LeadershipAchievement;
import club.projectessence.gameserver.enums.LeadershipSkill;
import club.projectessence.gameserver.enums.LeadershipTask;
import club.projectessence.gameserver.model.faction.FactionLeaderProgression;
import club.projectessence.gameserver.model.faction.FactionLeaderTaskProgress;
import club.projectessence.gameserver.model.World;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.model.events.Containers;
import club.projectessence.gameserver.model.events.EventType;
import club.projectessence.gameserver.model.events.ListenersContainer;
import club.projectessence.gameserver.model.events.impl.creature.player.OnPlayerLogin;
import club.projectessence.gameserver.model.events.impl.creature.player.OnPlayerProfessionChange;
import club.projectessence.gameserver.model.events.listeners.ConsumerEventListener;
import club.projectessence.gameserver.model.gve.FactionLeader;
import club.projectessence.gameserver.model.gve.FactionLeaderPrivileges;
import club.projectessence.gameserver.model.gve.FactionLeaderRequest;
import club.projectessence.gameserver.model.gve.FactionLeaderVote;
import club.projectessence.gameserver.model.holders.ItemHolder;
import club.projectessence.gameserver.model.skills.Skill;
// import club.projectessence.gameserver.network.serverpackets.NpcHtmlMessage; // No longer needed - using Community Board
import club.projectessence.gameserver.util.Broadcast;

/**
 * Faction Leader Manager.
 * Manages the election process for faction leaders, including registration, voting, and leader privileges.
 */
public class FactionLeaderManager
{
	private static final Logger							LOGGER				= Logger.getLogger(FactionLeaderManager.class.getName());
	private static final FactionLeaderManager			INSTANCE			= new FactionLeaderManager();
	public final Map<Faction, FactionLeader>			factionMap;
	private final AtomicInteger							cycle				= new AtomicInteger(0);
	private volatile FactionLeaderStateType				state				= FactionLeaderStateType.NONE;
	private long										startCycle;
	private long										endCycle;
	private final ReentrantReadWriteLock				lock				= new ReentrantReadWriteLock();
	private Future<?>									endCycleTask;
	private Future<?>									stateTask;
	private final Map<Faction, List<LeaderCandidate>>	candidateCache		= new ConcurrentHashMap<>();
	// Listeners container for global player events
	private final ListenersContainer					listenerContainer	= Containers.Players();
	
	/**
	 * A class representing a vote result entry.
	 */
	public static class VoteResult
	{
		private final String	charName;
		private final long		votes;
		
		public VoteResult(String charName, long votes)
		{
			this.charName = charName;
			this.votes = votes;
		}
		
		public String getCharName()
		{
			return charName;
		}
		
		public long getVotes()
		{
			return votes;
		}
	}
	
	/**
	 * A class representing a candidate for faction leader.
	 */
	public static class LeaderCandidate
	{
		private final int		objId;
		private final String	charName;
		
		public LeaderCandidate(int objId, String charName)
		{
			this.objId = objId;
			this.charName = charName;
		}
		
		public int getObjId()
		{
			return objId;
		}
		
		public String getCharName()
		{
			return charName;
		}
	}
	
	/**
	 * A task to change the election state.
	 */
	private static class StateTask implements Runnable
	{
		private final FactionLeaderManager		manager;
		private final FactionLeaderStateType	nextState;
		
		public StateTask(FactionLeaderManager manager, FactionLeaderStateType nextState)
		{
			this.manager = manager;
			this.nextState = nextState;
		}
		
		@Override
		public void run()
		{
			manager.changeState(nextState);
		}
	}
	
	/**
	 * A task to end the election cycle.
	 */
	private static class EndCycleTask implements Runnable
	{
		private final FactionLeaderManager manager;
		
		public EndCycleTask(FactionLeaderManager manager)
		{
			this.manager = manager;
		}
		
		@Override
		public void run()
		{
			manager.endCycle();
		}
	}
	
	// Event handlers
	private final Consumer<OnPlayerLogin>				playerLoginEvent			= event ->
																					{
																						PlayerInstance player = event.getPlayer();
																						if (player.getFaction() == Faction.NONE)
																						{
																							return;
																						}
																						// Announce if the player is a faction leader
																						if (isFactionLeader(player))
																						{
																							List<PlayerInstance> factionPlayers = World.getInstance().getPlayers().stream().filter(p -> p.getFaction() == player.getFaction() && p.isOnline()).collect(Collectors.toList());
																							Broadcast.toPlayers(factionPlayers, "Faction Leader " + player.getName() + " has logged in!", false);
																						}
																						FactionLeader factionLeader = getFactionLeader(player.getFaction());
																						if (factionLeader == null)
																						{
																							return;
																						}
																						// Process privileges (e.g., apply faction leader skills)
																						privilegeProcessing(player, factionLeader);
																						// Schedule sending candidate list or main interface based on the election state
																						if (getState() == FactionLeaderStateType.VOTE)
																						{
																							ThreadPool.schedule(() ->
																																											{
																																												if (isVote(player))
																																												{
																																													return;
																																												}
																																												// Kiểm tra danh sách ứng cử viên trước khi gửi giao diện
																																												List<LeaderCandidate> candidates = getCandidates(player.getFaction());
																																												if (!candidates.isEmpty())
																																												{
																																													sendCandidates(player, 0);
																																												}
																																												// Nếu không có ứng cử viên, không gửi gì
																																											}, 60_000);
																						}
																						else if (getState() == FactionLeaderStateType.INNINGS_REQUEST)
																						{
																							ThreadPool.schedule(() ->
																																											{
																																												if (isRequest(player))
																																												{
																																													return;
																																												}
																																												sendMain(player);
																																											}, 60_000);
																						}
																					};
	private final Consumer<OnPlayerProfessionChange>	playerProfessionChangeEvent	= event ->
																					{
																						PlayerInstance player = event.getPlayer();
																						// Process privileges (e.g., apply faction leader skills)
																						privilegeProcessing(player);
																					};
	
	private FactionLeaderManager()
	{
		factionMap = Arrays.stream(Faction.values()).filter(faction -> faction != Faction.NONE).collect(Collectors.toMap(Function.identity(), FactionLeader::new));
		// Register event listeners
		listenerContainer.addListener(new ConsumerEventListener(listenerContainer, EventType.ON_PLAYER_LOGIN, playerLoginEvent, this));
		listenerContainer.addListener(new ConsumerEventListener(listenerContainer, EventType.ON_PLAYER_PROFESSION_CHANGE, playerProfessionChangeEvent, this));
		// Restore the faction leader state when the manager is initialized
		restore();

		// Initialize progression manager
		FactionLeaderProgressionManager.getInstance();
	}
	
	/**
	 * Process privileges for a player, applying faction leader skills if applicable.
	 *
	 * @param player
	 *            The player to process privileges for.
	 */
	public void privilegeProcessing(PlayerInstance player)
	{
		FactionLeader factionLeader = getFactionLeader(player.getFaction());
		if (factionLeader == null)
		{
			return;
		}
		privilegeProcessing(player, factionLeader);
	}
	
	/**
	 * Apply faction leader privileges to a player, granting skills if the player has full privileges.
	 *
	 * @param player
	 *            The player to apply privileges to.
	 * @param factionLeader
	 *            The faction leader data for the player's faction.
	 */
	public void privilegeProcessing(PlayerInstance player, FactionLeader factionLeader)
	{
		FactionLeaderPrivileges privileges = factionLeader.getLeaderPrivileges(player.getObjectId());
		if (privileges != null && (privileges.getPrivileges() & FactionLeaderPrivilegesType.FULL.getMask()) == FactionLeaderPrivilegesType.FULL.getMask())
		{
			giveSkill(player);
			// Apply faction leader skill bonuses
			FactionLeaderSkillManager.getInstance().applySkillBonuses(player);
		}
		else
		{
			// Remove faction leader skill bonuses if not leader
			FactionLeaderSkillManager.getInstance().removeSkillBonuses(player);
		}
	}
	
	/**
	 * Get the singleton instance of FactionLeaderManager.
	 *
	 * @return The FactionLeaderManager instance.
	 */
	public static FactionLeaderManager getInstance()
	{
		return INSTANCE;
	}
	
	/**
	 * Send the main faction leader election interface to the player.
	 * DEPRECATED: Use Community Board instead
	 *
	 * @param player
	 *            The player to send the interface to.
	 */
	@Deprecated
	public void sendMain(PlayerInstance player)
	{
		if (player == null)
		{
			return;
		}
		// Redirect to Community Board
		player.sendMessage("Please use Community Board (Alt+B) to access Faction Leader system.");
		return;
		/*
		// OLD NPC HTML CODE - DISABLED
		NpcHtmlMessage html = new NpcHtmlMessage(0);
		html.setFile(player, "data/html/CommunityBoard/KhoaCustom/gve/leader/main.htm");
		// ... rest of the old code ...
		player.sendPacket(html);
		*/
	}

	/**
	 * Get countdown information for the current phase.
	 *
	 * @param state The current election state
	 * @return Formatted countdown string
	 */
	private String getCountdownInfo(FactionLeaderStateType state)
	{
		try
		{
			ZonedDateTime now = ZonedDateTime.now(ZoneId.systemDefault());
			ZonedDateTime targetTime = null;
			String phaseText = "";

			switch (state)
			{
				case INNINGS_REQUEST:
					// Calculate end of registration phase
					ZonedDateTime startCycleTime = ZonedDateTime.ofInstant(Instant.ofEpochMilli(getStartCycle()), ZoneId.systemDefault());
					targetTime = startCycleTime.plusDays(1).withHour(Config.FACTION_LEADER_START_VOTE_HOUR).withMinute(Config.FACTION_LEADER_START_VOTE_MINUTE).withSecond(0).withNano(0);
					phaseText = "⏰ Registration ends in: ";
					break;
				case VOTE:
					// Calculate end of voting phase
					targetTime = ZonedDateTime.ofInstant(Instant.ofEpochMilli(endCycle), ZoneId.systemDefault());
					phaseText = "⏰ Voting ends in: ";
					break;
				default:
					return "<font color=\"FFFFFF\">⏳ Please wait for the next election cycle</font>";
			}

			if (targetTime != null && targetTime.isAfter(now))
			{
				Duration duration = Duration.between(now, targetTime);
				long hours = duration.toHours();
				long minutes = duration.toMinutes() % 60;

				if (hours > 0)
				{
					return "<font color=\"FFFF00\">" + phaseText + hours + "h " + minutes + "m</font>";
				}
				else
				{
					return "<font color=\"FF5000\">" + phaseText + minutes + "m</font>";
				}
			}
			else
			{
				return "<font color=\"FF5000\">⚠️ Phase ending soon!</font>";
			}
		}
		catch (Exception e)
		{
			return "<font color=\"FFFFFF\">⏳ Time calculation unavailable</font>";
		}
	}

	/**
	 * Send the registration success interface to the player.
	 *
	 * @param player
	 *            The player to send the interface to.
	 */
	@Deprecated
	public void sendRegisteredSuccess(PlayerInstance player)
	{
		if (player == null)
		{
			return;
		}
		// Redirect to Community Board
		player.sendMessage("Registration successful! Please use Community Board (Alt+B) to view status.");
	}
	
	/**
	 * Send the list of candidates for faction leader election to the player.
	 * DEPRECATED: Use Community Board instead
	 *
	 * @param player
	 *            The player to send the candidate list to.
	 * @param page
	 *            The page number of the candidate list to display.
	 */
	@Deprecated
	public void sendCandidates(PlayerInstance player, int page)
	{
		if (player == null)
		{
			return;
		}
		// Redirect to Community Board
		player.sendMessage("Please use Community Board (Alt+B) to access Faction Leader system.");
		return;
		/*
		// OLD NPC HTML CODE - DISABLED
		// ... rest of the old sendCandidates code ...
		*/
	}
	
	/**
	 * Send the voting success interface to the player.
	 * DEPRECATED: Use Community Board instead
	 *
	 * @param player
	 *            The player to send the interface to.
	 */
	@Deprecated
	public void sendVotedSuccess(PlayerInstance player)
	{
		if (player == null)
		{
			return;
		}
		// Redirect to Community Board
		player.sendMessage("Vote successful! Please use Community Board (Alt+B) to view results.");
	}
	
	/**
	 * Send the vote cancellation success interface to the player.
	 * DEPRECATED: Use Community Board instead
	 *
	 * @param player
	 *            The player to send the interface to.
	 */
	@Deprecated
	public void sendVoteCancelledSuccess(PlayerInstance player)
	{
		if (player == null)
		{
			return;
		}
		// Redirect to Community Board
		player.sendMessage("Vote cancelled! Please use Community Board (Alt+B) to view candidates.");
	}
	
	public List<LeaderCandidate> getCandidates(Faction faction)
	{
		return candidateCache.computeIfAbsent(faction, f ->
		{
			List<LeaderCandidate> list = new ArrayList<>();
			try (Connection con = DatabaseFactory.getConnection(); PreparedStatement ps = con.prepareStatement("SELECT f.charId, f.char_name " + "FROM faction_leader_request fl " + "LEFT JOIN (SELECT charId, char_name FROM characters WHERE faction = ?) AS f ON fl.obj_id = f.charId " + "WHERE f.charId IS NOT NULL " + "ORDER BY f.char_name"))
			{
				ps.setInt(1, faction.getId());
				try (ResultSet rs = ps.executeQuery())
				{
					while (rs.next())
					{
						int charId = rs.getInt("charId");
						String charName = rs.getString("char_name");
						list.add(new LeaderCandidate(charId, charName));
					}
				}
			}
			catch (Exception e)
			{
				LOGGER.warning("Failed to load faction leader candidates: " + e.getMessage());
			}
			return list;
		});
	}
	
	/**
	 * Grant faction leader skills to a player.
	 *
	 * @param player
	 *            The player to grant skills to.
	 */
	private void giveSkill(PlayerInstance player)
	{
		Config.FACTION_LEADER_SKILL_SET.forEach(skillHolder ->
		{
			Skill skill = SkillData.getInstance().getSkill(skillHolder.getSkillId(), skillHolder.getSkillLevel());
			if (skill != null)
			{
				player.addSkill(skill);
			}
		});
	}
	
	/**
	 * Restore the faction leader state from the database and schedule election cycles.
	 */
	public void restore()
	{
		LOGGER.info("FactionLeaderManager: Starting restore process.");
		if (!Config.FACTION_LEADER_ENABLED)
		{
			LOGGER.info("FactionLeaderManager: System disabled in configuration.");
			return;
		}
		factionMap.values().forEach(FactionLeader::restore);
		try (Connection con = DatabaseFactory.getConnection(); PreparedStatement ps = con.prepareStatement("SELECT * FROM faction_leader_state WHERE type=1 LIMIT 1"); ResultSet rs = ps.executeQuery())
		{
			if (rs.next())
			{
				cycle.set(rs.getInt("cycle"));
				state = FactionLeaderStateType.getValueFromOrdinal(rs.getInt("state"));
				endCycle = rs.getLong("end_cycle");
				startCycle = rs.getLong("start_cycle");
				if (Config.LOG_FACTION_DETAILS)
				{
					LOGGER.info("FactionLeaderManager: Restored state - Cycle: " + cycle.get() + ", State: " + state + ", End Cycle: " + new java.util.Date(endCycle));
				}
			}
			else
			{
				LOGGER.info("FactionLeaderManager: No previous state found, starting new cycle.");
			}
		}
		catch (Exception e)
		{
			LOGGER.warning("Failed to load faction leader state: " + e.getMessage());
		}
		ZonedDateTime current = ZonedDateTime.now(ZoneId.systemDefault());
		if (endCycle == 0 || cycle.get() == 0)
		{
			LOGGER.info("FactionLeaderManager: No valid cycle found, initializing new cycle.");
			newCycle();
		}
		else if (endCycle <= current.toInstant().toEpochMilli())
		{
			LOGGER.info("FactionLeaderManager: Current cycle has ended, finalizing and starting new cycle.");
			endCycle();
		}
		else
		{
			ZonedDateTime startCycleTime = ZonedDateTime.ofInstant(Instant.ofEpochMilli(startCycle), ZoneId.systemDefault());
			ZonedDateTime todayStartRequest = startCycleTime.withHour(Config.FACTION_LEADER_START_REQUEST_HOUR).withMinute(Config.FACTION_LEADER_START_REQUEST_MINUTE).withSecond(0).withNano(0);
			ZonedDateTime todayStartVote = startCycleTime.plusDays(1).withHour(Config.FACTION_LEADER_START_VOTE_HOUR).withMinute(Config.FACTION_LEADER_START_VOTE_MINUTE).withSecond(0).withNano(0);
			ZonedDateTime todayEndCycle = startCycleTime.plusDays(1).withHour(Config.FACTION_LEADER_END_CYCLE_HOUR).withMinute(Config.FACTION_LEADER_END_CYCLE_MINUTE).withSecond(0).withNano(0);
			if (current.isAfter(todayStartRequest) && current.isBefore(todayStartVote))
			{
				state = FactionLeaderStateType.INNINGS_REQUEST;
				if (Config.LOG_FACTION_DETAILS)
				{
					LOGGER.info("FactionLeaderManager: Current time is in INNINGS_REQUEST phase.");
				}
				ZonedDateTime startVoteTimeAdjusted = startCycleTime.plusDays(1).withHour(Config.FACTION_LEADER_START_VOTE_HOUR).withMinute(Config.FACTION_LEADER_START_VOTE_MINUTE).withSecond(0).withNano(0);
				if (Config.LOG_FACTION_DETAILS)
				{
					LOGGER.info("FactionLeaderManager: Scheduling VOTE phase at " + startVoteTimeAdjusted);
				}
				stateTask = ThreadPool.schedule(new StateTask(this, FactionLeaderStateType.VOTE), Duration.between(current, startVoteTimeAdjusted).toMillis());
			}
			else if (current.isAfter(todayStartVote) && current.isBefore(todayEndCycle))
			{
				state = FactionLeaderStateType.VOTE;
				if (Config.LOG_FACTION_DETAILS)
				{
					LOGGER.info("FactionLeaderManager: Current time is in VOTE phase.");
				}
			}
			else if (state == FactionLeaderStateType.NONE)
			{
				ZonedDateTime requestTime = startCycleTime.withHour(Config.FACTION_LEADER_START_REQUEST_HOUR).withMinute(Config.FACTION_LEADER_START_REQUEST_MINUTE).withSecond(0).withNano(0);
				if (Config.LOG_FACTION_DETAILS)
				{
					LOGGER.info("FactionLeaderManager: Scheduling INNINGS_REQUEST phase at " + requestTime);
				}
				stateTask = ThreadPool.schedule(new StateTask(this, FactionLeaderStateType.INNINGS_REQUEST), Duration.between(current, requestTime).toMillis());
			}
			else if (state == FactionLeaderStateType.INNINGS_REQUEST)
			{
				ZonedDateTime startVoteTimeAdjusted = startCycleTime.plusDays(1).withHour(Config.FACTION_LEADER_START_VOTE_HOUR).withMinute(Config.FACTION_LEADER_START_VOTE_MINUTE).withSecond(0).withNano(0);
				if (Config.LOG_FACTION_DETAILS)
				{
					LOGGER.info("FactionLeaderManager: Scheduling VOTE phase at " + startVoteTimeAdjusted);
				}
				stateTask = ThreadPool.schedule(new StateTask(this, FactionLeaderStateType.VOTE), Duration.between(ZonedDateTime.now(ZoneId.systemDefault()), startVoteTimeAdjusted).toMillis());
			}
			if (Config.LOG_FACTION_DETAILS)
			{
				LOGGER.info("FactionLeaderManager: Scheduling cycle end at " + new java.util.Date(endCycle));
			}
			endCycleTask = ThreadPool.schedule(new EndCycleTask(this), Duration.between(current, ZonedDateTime.ofInstant(Instant.ofEpochMilli(endCycle), ZoneId.systemDefault())).toMillis());
		}
		// Log current Faction Leaders
		for (Faction faction : factionMap.keySet())
		{
			FactionLeader factionLeader = factionMap.get(faction);
			if (factionLeader.getPrivilegesMap().isEmpty())
			{
				if (Config.LOG_FACTION_DETAILS)
				{
					LOGGER.info("FactionLeaderManager: No Faction Leader for " + faction + ".");
				}
			}
			else
			{
				factionLeader.getPrivilegesMap().forEach((objId, privileges) ->
				{
					String playerName = CharNameTable.getInstance().getNameById(objId);
					LOGGER.info("FactionLeaderManager: Current Faction Leader for " + faction + ": " + playerName + " (ID: " + objId + ")");
				});
			}
		}
	}
	
	/**
	 * Stop the scheduled task for ending the election cycle.
	 */
	private void stopEndCycleTask()
	{
		if (endCycleTask != null)
		{
			endCycleTask.cancel(false);
		}
		endCycleTask = null;
	}
	
	/**
	 * Stop the scheduled task for changing the election state.
	 */
	private void stopStateTask()
	{
		if (stateTask != null)
		{
			stateTask.cancel(false);
		}
		stateTask = null;
	}
	
	/**
	 * Change the election state and schedule the next state transition.
	 *
	 * @param nextState
	 *            The next state of the election cycle.
	 */
	public void changeState(FactionLeaderStateType nextState)
	{
		lock.writeLock().lock();
		try
		{
			state = nextState;
			if (nextState == FactionLeaderStateType.INNINGS_REQUEST)
			{
				Broadcast.toAllOnlinePlayers("The period of registration for Faction Leader elections has started!");
				World.getInstance().getPlayers().forEach(p ->
				{
					if (p.getFaction() != Faction.NONE)
					{
						sendMain(p);
					}
				});
				ZonedDateTime startCycleTime = ZonedDateTime.ofInstant(Instant.ofEpochMilli(startCycle), ZoneId.systemDefault());
				ZonedDateTime startVoteTime = startCycleTime.plusDays(cycle.get() == 1 ? 1 : 2).withHour(Config.FACTION_LEADER_START_VOTE_HOUR).withMinute(Config.FACTION_LEADER_START_VOTE_MINUTE).withSecond(0).withNano(0);
				stateTask = ThreadPool.schedule(new StateTask(this, FactionLeaderStateType.VOTE), Duration.between(ZonedDateTime.now(ZoneId.systemDefault()), startVoteTime).toMillis());
			}
			else if (nextState == FactionLeaderStateType.VOTE)
			{
				Broadcast.toAllOnlinePlayers("The voting period for Faction Leader elections has started!");
				candidateCache.clear();
			}
			saveFactionState();
		}
		finally
		{
			lock.writeLock().unlock();
		}
	}
	
	/**
	 * Start a new election cycle, resetting all data and scheduling the next cycle.
	 */
	private void newCycle()
	{
		factionMap.values().forEach(p ->
		{
			p.getLeaderVotes().clear();
			p.getRequestMap().clear();
			p.getPrivilegesMap().clear();
		});
		ZonedDateTime current = ZonedDateTime.now(ZoneId.systemDefault());
		ZonedDateTime startCycleTime = current.with(LocalTime.of(0, 0, 0, 0));
		ZonedDateTime requestTime = startCycleTime.withHour(Config.FACTION_LEADER_START_REQUEST_HOUR).withMinute(Config.FACTION_LEADER_START_REQUEST_MINUTE).withSecond(0).withNano(0);
		ZonedDateTime voteTime = startCycleTime.plusDays(1).withHour(Config.FACTION_LEADER_START_VOTE_HOUR).withMinute(Config.FACTION_LEADER_START_VOTE_MINUTE).withSecond(0).withNano(0);
		ZonedDateTime endCycleTime = startCycleTime.plusDays(1).withHour(Config.FACTION_LEADER_END_CYCLE_HOUR).withMinute(Config.FACTION_LEADER_END_CYCLE_MINUTE).withSecond(0).withNano(0);
		cycle.incrementAndGet();
		setStartCycle(startCycleTime.toInstant().toEpochMilli());
		setEndCycle(endCycleTime.toInstant().toEpochMilli());
		stopEndCycleTask();
		stopStateTask();
		// Check current time to set the appropriate state
		if (current.isBefore(requestTime))
		{
			// Before INNINGS_REQUEST, schedule it
			state = FactionLeaderStateType.NONE;
			if (Config.LOG_FACTION_DETAILS)
			{
				LOGGER.info("FactionLeaderManager: Scheduling INNINGS_REQUEST phase at " + requestTime);
			}
			stateTask = ThreadPool.schedule(new StateTask(this, FactionLeaderStateType.INNINGS_REQUEST), Duration.between(current, requestTime).toMillis());
		}
		else if (current.isAfter(requestTime) && current.isBefore(voteTime))
		{
			// In INNINGS_REQUEST phase, set state immediately
			state = FactionLeaderStateType.INNINGS_REQUEST;
			if (Config.LOG_FACTION_DETAILS)
			{
				LOGGER.info("FactionLeaderManager: Current time is in INNINGS_REQUEST phase.");
				LOGGER.info("FactionLeaderManager: Scheduling VOTE phase at " + voteTime);
			}
			stateTask = ThreadPool.schedule(new StateTask(this, FactionLeaderStateType.VOTE), Duration.between(current, voteTime).toMillis());
		}
		else if (current.isAfter(voteTime) && current.isBefore(endCycleTime))
		{
			// In VOTE phase, set state immediately
			state = FactionLeaderStateType.VOTE;
			if (Config.LOG_FACTION_DETAILS)
			{
				LOGGER.info("FactionLeaderManager: Current time is in VOTE phase.");
			}
		}
		else
		{
			// After the cycle ends, start a new cycle
			LOGGER.info("FactionLeaderManager: Current time is after cycle end, starting new cycle.");
			newCycle();
			return;
		}
		endCycleTask = ThreadPool.schedule(new EndCycleTask(this), Duration.between(current, endCycleTime).toMillis());
		// Clear faction leader data from database
		try (Connection con = DatabaseFactory.getConnection())
		{
			try (PreparedStatement ps = con.prepareStatement("DELETE FROM faction_leader_request"))
			{
				ps.execute();
			}
			try (PreparedStatement ps = con.prepareStatement("DELETE FROM faction_leader_privileges"))
			{
				ps.execute();
			}
			try (PreparedStatement ps = con.prepareStatement("DELETE FROM faction_leader_vote"))
			{
				ps.execute();
			}
		}
		catch (Exception e)
		{
			LOGGER.warning("Failed to clear faction leader data: " + e.getMessage());
		}
		// Clear candidate cache when starting a new cycle
		candidateCache.clear();
		saveFactionState();
	}
	
	/**
	 * Transition to the next election cycle, scheduling the next state transition.
	 */
	private void nextCycle()
	{
		cycle.incrementAndGet();
		ZonedDateTime current = ZonedDateTime.now(ZoneId.systemDefault());
		ZonedDateTime start = current.withHour(Config.FACTION_LEADER_NEXT_CYCLE_HOUR).withMinute(Config.FACTION_LEADER_NEXT_CYCLE_MINUTE).withSecond(0).withNano(0);
		ZonedDateTime requestTime = current.plusDays(1).withHour(Config.FACTION_LEADER_START_REQUEST_HOUR).withMinute(Config.FACTION_LEADER_START_REQUEST_MINUTE).withSecond(0).withNano(0);
		if (start.getDayOfYear() != current.getDayOfYear() || start.getYear() != current.getYear())
		{
			start = start.plusDays(1);
			requestTime = requestTime.plusDays(1);
		}
		ZonedDateTime endCycleTime = start.plusDays(2).withHour(Config.FACTION_LEADER_END_CYCLE_HOUR).withMinute(Config.FACTION_LEADER_END_CYCLE_MINUTE).withSecond(0).withNano(0);
		stopEndCycleTask();
		stopStateTask();
		setEndCycle(endCycleTime.toInstant().toEpochMilli());
		setStartCycle(start.toInstant().toEpochMilli());
		endCycleTask = ThreadPool.schedule(new EndCycleTask(this), Duration.between(current, endCycleTime).toMillis());
		stateTask = ThreadPool.schedule(new StateTask(this, FactionLeaderStateType.INNINGS_REQUEST), Duration.between(current, requestTime).toMillis());
	}
	
	/**
	 * End the current election cycle, announce the new faction leader, and start the next cycle.
	 *
	 * @return A list of pairs containing the faction and the name of the newly elected Faction Leader (or null if no leader was elected).
	 */
	public List<Map.Entry<Faction, String>> endCycle()
	{
		List<Map.Entry<Faction, String>> results = new ArrayList<>();
		lock.writeLock().lock();
		try
		{
			state = FactionLeaderStateType.NONE;
			stopStateTask();
			stopEndCycleTask();
			for (Faction faction : factionMap.keySet())
			{
				FactionLeader p = factionMap.get(faction);
				if (p == null || p.getFaction() == null)
				{
					LOGGER.warning("FactionLeader for faction " + faction + " is null or has null faction. Skipping.");
					results.add(Map.entry(faction, "No Leader Elected"));
					continue;
				}
				p.getPrivilegesMap().values().stream().map(pl -> World.getInstance().getPlayer(pl.getObjId())).filter(Objects::nonNull).forEach(this::removePlayerSkills);
				int leaderObjId = getLeaderFromVote(p);
				p.getPrivilegesMap().clear();
				p.getRequestMap().clear();
				p.getLeaderVotes().clear();
				String leaderName = null;
				if (leaderObjId != 0)
				{
					leaderName = CharNameTable.getInstance().getNameById(leaderObjId);
					if (leaderName == null)
					{
						LOGGER.warning("Faction Leader ID " + leaderObjId + " not found in characters table for faction " + p.getFaction() + ". Skipping leader assignment.");
					}
					else
					{
						p.getPrivilegesMap().put(leaderObjId, new FactionLeaderPrivileges(leaderObjId, FactionLeaderPrivilegesType.FULL.getMask()));
						List<PlayerInstance> factionPlayers = World.getInstance().getPlayers().stream().filter(player -> player.getFaction() == p.getFaction() && player.isOnline()).collect(Collectors.toList());
						Broadcast.toPlayers(factionPlayers, "New Faction Leader selected: " + leaderName, false);
						PlayerInstance leaderPlayer = World.getInstance().getPlayer(leaderObjId);
						for (ItemHolder itemHolder : Config.FACTION_LEADER_ITEM_SET)
						{
							if (itemHolder == null || itemHolder.getId() <= 0)
							{
								continue; // Skip invalid items silently
							}
							if (leaderPlayer != null)
							{
								try
								{
									leaderPlayer.addItem("Faction Leader Reward", itemHolder.getId(), itemHolder.getCount(), null, true);
								}
								catch (Exception e)
								{
									// Skip logging since item errors are not critical
								}
							}
							else
							{
								if (Config.LOG_FACTION_DETAILS)
								{
									LOGGER.info("Faction Leader " + leaderName + " (ID: " + leaderObjId + ") is offline. Reward pending: " + itemHolder.getId() + " x" + itemHolder.getCount());
								}
							}
						}
					}
				}
				results.add(Map.entry(faction, leaderName != null ? leaderName : "No Leader Elected"));
			}
			// Cập nhật tin nhắn Discord khi lãnh đạo thay đổi
			if (Config.DISCORD_ENABLED && Config.DISCORD_FACTION_NOTIFICATIONS_ENABLED && !Config.DISCORD_FACTION_WAR_WEBHOOK.isEmpty())
			{
				DiscordBotManager.getInstance().sendFactionWarInfo();
			}
			nextCycle();
			saveFactionState();
			// Clear faction leader data from database
			try (Connection con = DatabaseFactory.getConnection())
			{
				try (PreparedStatement ps = con.prepareStatement("DELETE FROM faction_leader_request"))
				{
					ps.execute();
				}
				try (PreparedStatement ps = con.prepareStatement("DELETE FROM faction_leader_privileges"))
				{
					ps.execute();
				}
				try (PreparedStatement ps = con.prepareStatement("DELETE FROM faction_leader_vote"))
				{
					ps.execute();
				}
			}
			catch (Exception e)
			{
				throw new RuntimeException("Failed to clear faction leader data: " + e.getMessage(), e);
			}
			factionMap.values().forEach(p -> batchLeaderPrivileges(p.getFaction(), new ArrayList<>(p.getPrivilegesMap().values())));
			return results;
		}
		catch (Exception e)
		{
			throw new RuntimeException("Error while ending faction leader election cycle: " + e.getMessage(), e);
		}
		finally
		{
			lock.writeLock().unlock();
		}
	}
	
	/**
	 * Determine the new faction leader based on votes.
	 *
	 * @param p
	 *            The faction leader data.
	 * @return The object ID of the new leader, or 0 if no leader is elected.
	 */
	private int getLeaderFromVote(FactionLeader p)
	{
		lock.readLock().lock();
		try
		{
			return p.getLeaderVotes().stream().collect(Collectors.groupingBy(FactionLeaderVote::getVotedForObjId, Collectors.counting())).entrySet().stream().max(Comparator.comparingLong(Map.Entry::getValue)).map(Map.Entry::getKey).orElse(0);
		}
		finally
		{
			lock.readLock().unlock();
		}
	}
	
	/**
	 * Remove faction leader skills from a player.
	 *
	 * @param player
	 *            The player to remove skills from.
	 */
	public void removePlayerSkills(PlayerInstance player)
	{
		Config.FACTION_LEADER_SKILL_SET.forEach(skillHolder -> player.removeSkill(skillHolder.getSkillId()));
	}
	
	/**
	 * Process a vote for a faction leader candidate.
	 *
	 * @param player
	 *            The player who is voting.
	 * @param votedForObjId
	 *            The object ID of the candidate being voted for.
	 * @return True if the vote is successful, false otherwise.
	 */
	public boolean vote(PlayerInstance player, int votedForObjId)
	{
		if (player == null)
		{
			return false;
		}
		FactionLeader factionLeader = getFactionLeader(player.getFaction());
		if (factionLeader == null)
		{
			return false;
		}
		if (!factionLeader.isRequest(votedForObjId))
		{
			return false;
		}
		lock.writeLock().lock();
		try
		{
			if (state != FactionLeaderStateType.VOTE)
			{
				player.sendMessage("The voting period is not active.");
				return false;
			}
			if (player.getLevel() < Config.FACTION_LEADER_MIN_LEVEL_FOR_VOTING)
			{
				player.sendMessage("You need to be at least level " + Config.FACTION_LEADER_MIN_LEVEL_FOR_VOTING + " to vote.");
				return false;
			}
			boolean voteFromHwid = factionLeader.getLeaderVotes().stream().anyMatch(v -> v.getHwid().equals(player.getHWID()));
			if (voteFromHwid && Config.FACTION_LEADER_CHECK_HWID)
			{
				player.sendMessage("You have already voted from this device.");
				return false;
			}
			boolean voteFromObjId = factionLeader.getLeaderVotes().stream().anyMatch(v -> v.getVotedObjId() == player.getObjectId());
			if (voteFromObjId)
			{
				player.sendMessage("You have already voted.");
				return false;
			}
			if (player.getObjectId() == votedForObjId && !Config.FACTION_LEADER_SELF_VOTE_AVAILABLE)
			{
				player.sendMessage("Self-voting is not allowed.");
				return false;
			}
			FactionLeaderVote vote = new FactionLeaderVote(player.getObjectId(), votedForObjId, player.getHWID());
			factionLeader.getLeaderVotes().add(vote);
			insertVote(player.getFaction(), vote);
			sendVotedSuccess(player);
			return true;
		}
		finally
		{
			lock.writeLock().unlock();
		}
	}
	
	/**
	 * Cancel a player's vote for a specific candidate.
	 *
	 * @param player
	 *            The player who wants to cancel their vote.
	 * @param votedForObjId
	 *            The object ID of the candidate the player voted for.
	 * @param page
	 *            The current page of the candidate list.
	 * @return True if the vote cancellation is successful, false otherwise.
	 */
	public boolean cancelVote(PlayerInstance player, int votedForObjId, int page)
	{
		if (player == null)
		{
			return false;
		}
		FactionLeader factionLeader = getFactionLeader(player.getFaction());
		if (factionLeader == null)
		{
			return false;
		}
		lock.writeLock().lock();
		try
		{
			if (state != FactionLeaderStateType.VOTE)
			{
				player.sendMessage("The voting period is not active.");
				return false;
			}
			Optional<FactionLeaderVote> playerVote = factionLeader.getLeaderVotes().stream().filter(v -> v.getVotedObjId() == player.getObjectId() && v.getVotedForObjId() == votedForObjId).findFirst();
			if (!playerVote.isPresent())
			{
				player.sendMessage("You have not voted for this candidate.");
				return false;
			}
			factionLeader.getLeaderVotes().removeIf(vote -> vote.getVotedObjId() == player.getObjectId() && vote.getVotedForObjId() == votedForObjId);
			try (Connection con = DatabaseFactory.getConnection(); PreparedStatement ps = con.prepareStatement("DELETE FROM faction_leader_vote WHERE faction = ? AND voted_obj_id = ? AND voted_for_obj_id = ?"))
			{
				ps.setInt(1, player.getFaction().getId());
				ps.setInt(2, player.getObjectId());
				ps.setInt(3, votedForObjId);
				ps.execute();
			}
			catch (Exception e)
			{
				LOGGER.warning("Failed to delete faction leader vote: " + e.getMessage());
			}
			sendVoteCancelledSuccess(player);
			return true;
		}
		finally
		{
			lock.writeLock().unlock();
		}
	}
	
	/**
	 * Register a player as a candidate for faction leader.
	 *
	 * @param player
	 *            The player registering as a candidate.
	 * @return True if registration is successful, false otherwise.
	 */
	public boolean request(PlayerInstance player)
	{
		if (player == null)
		{
			return false;
		}
		FactionLeader factionLeader = getFactionLeader(player.getFaction());
		if (factionLeader == null)
		{
			return false;
		}
		int personalEfficiency = FactionWarManager.getInstance().getPersonalEfficiency(player);
		if (personalEfficiency < Config.FACTION_LEADER_MIN_PERSONAL_EFFICIENCY)
		{
			player.sendMessage("Your efficiency (" + personalEfficiency + ") is too low. Minimum required: " + Config.FACTION_LEADER_MIN_PERSONAL_EFFICIENCY);
			return false;
		}
		if (state != FactionLeaderStateType.INNINGS_REQUEST)
		{
			player.sendMessage("The registration period is not active.");
			return false;
		}
		lock.writeLock().lock();
		try
		{
			if (state != FactionLeaderStateType.INNINGS_REQUEST)
			{
				player.sendMessage("The registration period is not active.");
				return false;
			}
			if (factionLeader.isRequest(player.getObjectId()))
			{
				player.sendMessage("You have already registered as a candidate.");
				return false;
			}
			FactionLeaderRequest request = new FactionLeaderRequest(player.getObjectId(), player.getHWID());
			factionLeader.getRequestMap().put(player.getObjectId(), request);
			insertRequest(player.getFaction(), request);
			// Update candidate cache
			List<LeaderCandidate> candidates = candidateCache.computeIfAbsent(player.getFaction(), f -> new ArrayList<>());
			candidates.add(new LeaderCandidate(player.getObjectId(), player.getName()));
			candidateCache.put(player.getFaction(), candidates);
			sendRegisteredSuccess(player);
			return true;
		}
		finally
		{
			lock.writeLock().unlock();
		}
	}
	
	/**
	 * Get the start time of the current election cycle.
	 *
	 * @return The start time in milliseconds.
	 */
	public long getStartCycle()
	{
		return startCycle;
	}
	
	/**
	 * Get the current election cycle number.
	 *
	 * @return The cycle number.
	 */
	public int getCycle()
	{
		return cycle.get();
	}
	
	/**
	 * Get the current state of the election cycle.
	 *
	 * @return The current state.
	 */
	public FactionLeaderStateType getState()
	{
		return state;
	}
	
	/**
	 * Get the end time of the current election cycle.
	 *
	 * @return The end time in milliseconds.
	 */
	public long getEndCycle()
	{
		return endCycle;
	}
	
	/**
	 * Set the start time of the election cycle.
	 *
	 * @param startCycle
	 *            The start time in milliseconds.
	 */
	public void setStartCycle(long startCycle)
	{
		this.startCycle = startCycle;
	}
	
	/**
	 * Set the current election cycle number.
	 *
	 * @param cycle
	 *            The cycle number.
	 */
	public void setCycle(int cycle)
	{
		this.cycle.set(cycle);
	}
	
	/**
	 * Set the current state of the election cycle.
	 *
	 * @param state
	 *            The state to set.
	 */
	public void setState(FactionLeaderStateType state)
	{
		this.state = state;
	}
	
	/**
	 * Set the end time of the election cycle.
	 *
	 * @param endCycle
	 *            The end time in milliseconds.
	 */
	public void setEndCycle(long endCycle)
	{
		this.endCycle = endCycle;
	}
	
	/**
	 * Get the faction leader data for a given faction.
	 *
	 * @param faction
	 *            The faction to get the leader data for.
	 * @return The faction leader data.
	 */
	public FactionLeader getFactionLeader(Faction faction)
	{
		return factionMap.get(faction);
	}
	
	/**
	 * Check if a player is a faction leader.
	 *
	 * @param player
	 *            The player to check.
	 * @return True if the player is a faction leader, false otherwise.
	 */
	public boolean isFactionLeader(PlayerInstance player)
	{
		if (player == null)
		{
			return false;
		}
		FactionLeader factionLeader = getFactionLeader(player.getFaction());
		return factionLeader != null && factionLeader.getPrivilegesMap().containsKey(player.getObjectId());
	}
	
	/**
	 * Check if a character with a given object ID is a faction leader.
	 *
	 * @param objId
	 *            The object ID of the character to check.
	 * @return True if the character is a faction leader, false otherwise.
	 */
	public boolean isFactionLeader(int objId)
	{
		for (Faction faction : Faction.values())
		{
			if (faction == Faction.NONE)
			{
				continue;
			}
			FactionLeader factionLeader = getFactionLeader(faction);
			if (factionLeader != null && factionLeader.getPrivilegesMap().containsKey(objId))
			{
				return true;
			}
		}
		return false;
	}
	
	/**
	 * Check if a player has requested to be a candidate.
	 *
	 * @param player
	 *            The player to check.
	 * @return True if the player has requested to be a candidate, false otherwise.
	 */
	public boolean isRequest(PlayerInstance player)
	{
		if (player == null)
		{
			return false;
		}
		FactionLeader factionLeader = getFactionLeader(player.getFaction());
		if (factionLeader == null)
		{
			return false;
		}
		return factionLeader.isRequest(player.getObjectId());
	}
	
	/**
	 * Check if a player has already voted in the current election cycle.
	 *
	 * @param player
	 *            The player to check.
	 * @return True if the player has voted, false otherwise.
	 */
	public boolean isVote(PlayerInstance player)
	{
		if (player == null)
		{
			return false;
		}
		FactionLeader factionLeader = getFactionLeader(player.getFaction());
		if (factionLeader == null)
		{
			return false;
		}
		lock.readLock().lock();
		try
		{
			return factionLeader.getLeaderVotes().stream().anyMatch(v -> v.getVotedObjId() == player.getObjectId());
		}
		finally
		{
			lock.readLock().unlock();
		}
	}
	
	/**
	 * Send the faction leader information interface to the player.
	 * DEPRECATED: Use Community Board instead
	 *
	 * @param player
	 *            The player to send the interface to.
	 */
	@Deprecated
	public void sendInfo(PlayerInstance player)
	{
		if (player == null)
		{
			return;
		}
		// Redirect to Community Board
		player.sendMessage("Please use Community Board (Alt+B) to access Faction Leader system.");
		return;
		/*
		// OLD NPC HTML CODE - DISABLED
		// ... rest of the old sendInfo code ...
		*/
	}

	/**
	 * Send the faction leader history interface to the player.
	 * DEPRECATED: Use Community Board instead
	 *
	 * @param player The player to send the interface to.
	 */
	@Deprecated
	public void sendHistory(PlayerInstance player)
	{
		if (player == null)
		{
			return;
		}
		// Redirect to Community Board
		player.sendMessage("Please use Community Board (Alt+B) to access Faction Leader system.");
	}

	/**
	 * Send the faction leader statistics interface to the player.
	 *
	 * @param player The player to send the interface to.
	 */
	@Deprecated
	public void sendStatistics(PlayerInstance player)
	{
		if (player == null)
		{
			return;
		}
		// Redirect to Community Board
		player.sendMessage("Please use Community Board (Alt+B) to access Faction Leader system.");
	}

	/**
	 * Send the faction leader progression interface to the player.
	 * DEPRECATED: Use Community Board instead
	 *
	 * @param player The player to send the interface to.
	 */
	@Deprecated
	public void sendProgression(PlayerInstance player)
	{
		if (player == null)
		{
			return;
		}
		// Redirect to Community Board
		player.sendMessage("Please use Community Board (Alt+B) to access Faction Leader system.");
		return;
		/*
		// OLD NPC HTML CODE - DISABLED
		*/
	}

	/**
	 * Send the faction leader achievements interface to the player.
	 * DEPRECATED: Use Community Board instead
	 *
	 * @param player The player to send the interface to.
	 */
	@Deprecated
	public void sendAchievements(PlayerInstance player)
	{
		if (player == null) return;
		player.sendMessage("Please use Community Board (Alt+B) to access Faction Leader system.");
		return;

		/*
		// OLD NPC HTML CODE - DISABLED
		*/
	}

	/**
	 * Send the faction leader skills interface to the player.
	 * DEPRECATED: Use Community Board instead
	 *
	 * @param player The player to send the interface to.
	 */
	@Deprecated
	public void sendSkills(PlayerInstance player)
	{
		if (player == null) return;
		player.sendMessage("Please use Community Board (Alt+B) to access Faction Leader system.");
	}

	/**
	 * Send the faction leader tasks interface to the player.
	 * DEPRECATED: Use Community Board instead
	 *
	 * @param player The player to send the interface to.
	 */
	@Deprecated
	public void sendTasks(PlayerInstance player)
	{
		if (player == null) return;
		player.sendMessage("Please use Community Board (Alt+B) to access Faction Leader system.");
		return;

		/*
		// OLD NPC HTML CODE - DISABLED
		*/
	}

	// Method addTaskRow removed - no longer needed since sendTasks is deprecated

	/**
	 * Learn a leadership skill
	 */
	public boolean learnSkill(PlayerInstance player, String skillName)
	{
		try
		{
			LeadershipSkill skill = LeadershipSkill.valueOf(skillName);
			FactionLeaderProgression progression = FactionLeaderProgressionManager.getInstance().getOrCreateProgression(player);

			if (progression.learnSkill(skill))
			{
				player.sendMessage("✨ Skill Learned: " + skill.getName() + "!");
				player.sendMessage("📜 " + skill.getDescription());
				player.sendMessage("💎 LP Cost: " + skill.getLpCost());
				return true;
			}
			else
			{
				player.sendMessage("❌ Cannot learn this skill. Check requirements.");
				return false;
			}
		}
		catch (IllegalArgumentException e)
		{
			player.sendMessage("❌ Invalid skill name.");
			return false;
		}
	}

	/**
	 * Claim task reward
	 */
	public boolean claimTaskReward(PlayerInstance player, String taskName)
	{
		try
		{
			LeadershipTask task = LeadershipTask.valueOf(taskName);
			return FactionLeaderProgressionManager.getInstance().claimTaskReward(player, task);
		}
		catch (IllegalArgumentException e)
		{
			player.sendMessage("❌ Invalid task name.");
			return false;
		}
	}

	/**
	 * Check if a player is currently a faction leader.
	 *
	 * @param player The player to check
	 * @return true if the player is a faction leader
	 */
	public boolean isLeader(PlayerInstance player)
	{
		if (player == null || player.getFaction() == Faction.NONE) return false;

		FactionLeader factionLeader = factionMap.get(player.getFaction());
		if (factionLeader == null) return false;

		return factionLeader.getPrivilegesMap().containsKey(player.getObjectId());
	}

	/**
	 * Get the current faction leader for a faction.
	 *
	 * @param faction The faction
	 * @return The faction leader player instance, or null if no leader or leader offline
	 */
	public PlayerInstance getLeader(Faction faction)
	{
		if (faction == Faction.NONE) return null;

		FactionLeader factionLeader = factionMap.get(faction);
		if (factionLeader == null) return null;

		for (Integer objId : factionLeader.getPrivilegesMap().keySet())
		{
			PlayerInstance player = World.getInstance().getPlayer(objId);
			if (player != null && player.isOnline())
			{
				return player;
			}
		}
		return null;
	}

	/**
	 * Check if all faction leaders are online.
	 *
	 * @return True if all faction leaders are online, false otherwise.
	 */
	public boolean isAllOnlineLeaders()
	{
		if (!Config.FACTION_LEADER_ENABLED)
		{
			return false;
		}
		return factionMap.values().stream().flatMap(e -> e.getPrivilegesMap().values().stream()).filter(e -> e.getPrivileges() == FactionLeaderPrivilegesType.FULL.getMask()).map(e -> World.getInstance().getPlayer(e.getObjId())).allMatch(player -> player != null && !player.isInOfflineMode());
	}
	
	/**
	 * Check if the current time is within the allowed interval for faction leader activities.
	 *
	 * @return True if the current time is within the allowed interval, false otherwise.
	 */
	public boolean isLeaderTime()
	{
		if (!Config.FACTION_LEADER_ENABLED)
		{
			return false;
		}
		if (!Config.FACTION_LEADER_AVAILABLE_INTERVALS_ENABLED)
		{
			return true;
		}
		LocalTime now = LocalTime.now();
		int currentHour = now.getHour();
		int currentMinute = now.getMinute();
		for (int[] interval : Config.FACTION_LEADER_AVAILABLE_INTERVALS)
		{
			int fromHour = interval[0];
			int fromMinute = interval[1];
			int toHour = interval[2];
			int toMinute = interval[3];
			// Chuyển thời gian thành phút để dễ so sánh
			int currentTotalMinutes = currentHour * 60 + currentMinute;
			int fromTotalMinutes = fromHour * 60 + fromMinute;
			int toTotalMinutes = toHour * 60 + toMinute;
			// Xử lý trường hợp khoảng thời gian kéo dài qua nửa đêm
			if (fromTotalMinutes <= toTotalMinutes)
			{
				// Trường hợp bình thường (from < to)
				if (currentTotalMinutes >= fromTotalMinutes && currentTotalMinutes <= toTotalMinutes)
				{
					return true;
				}
			}
			else
			{
				// Trường hợp qua nửa đêm (from > to)
				if (currentTotalMinutes >= fromTotalMinutes || currentTotalMinutes <= toTotalMinutes)
				{
					return true;
				}
			}
		}
		return false;
	}
	
	/**
	 * Save the current election state to the database.
	 */
	private void saveFactionState()
	{
		try (Connection con = DatabaseFactory.getConnection(); PreparedStatement ps = con.prepareStatement("REPLACE INTO faction_leader_state (type, cycle, state, end_cycle, start_cycle) VALUES (1,?,?,?,?)"))
		{
			ps.setInt(1, cycle.get());
			ps.setInt(2, state.ordinal());
			ps.setLong(3, endCycle);
			ps.setLong(4, startCycle);
			ps.executeUpdate();
		}
		catch (Exception e)
		{
			LOGGER.warning("Failed to save faction leader state: " + e.getMessage());
		}
	}
	
	/**
	 * Insert a vote into the database.
	 *
	 * @param faction
	 *            The faction the vote is for.
	 * @param vote
	 *            The vote data.
	 */
	private void insertVote(Faction faction, FactionLeaderVote vote)
	{
		try (Connection con = DatabaseFactory.getConnection(); PreparedStatement ps = con.prepareStatement("INSERT INTO faction_leader_vote (faction, voted_obj_id, voted_for_obj_id, hwid) VALUES (?,?,?,?)"))
		{
			ps.setInt(1, faction.getId());
			ps.setInt(2, vote.getVotedObjId());
			ps.setInt(3, vote.getVotedForObjId());
			ps.setString(4, vote.getHwid());
			ps.execute();
		}
		catch (Exception e)
		{
			LOGGER.warning("Failed to insert faction leader vote: " + e.getMessage());
		}
	}
	
	/**
	 * Insert a candidate request into the database.
	 *
	 * @param faction
	 *            The faction the request is for.
	 * @param request
	 *            The request data.
	 */
	private void insertRequest(Faction faction, FactionLeaderRequest request)
	{
		try (Connection con = DatabaseFactory.getConnection(); PreparedStatement ps = con.prepareStatement("INSERT INTO faction_leader_request (faction, obj_id, hwid) VALUES (?,?,?)"))
		{
			ps.setInt(1, faction.getId());
			ps.setInt(2, request.getObjId());
			ps.setString(3, request.getHwid());
			ps.execute();
		}
		catch (Exception e)
		{
			LOGGER.warning("Failed to insert faction leader request: " + e.getMessage());
		}
	}
	
	/**
	 * Batch insert faction leader privileges into the database.
	 *
	 * @param faction
	 *            The faction to insert privileges for.
	 * @param privileges
	 *            The list of privileges to insert.
	 */
	public void batchLeaderPrivileges(Faction faction, List<FactionLeaderPrivileges> privileges)
	{
		if (privileges.isEmpty())
		{
			return;
		}
		try (Connection con = DatabaseFactory.getConnection(); PreparedStatement ps = con.prepareStatement("INSERT INTO faction_leader_privileges (faction, obj_id, privileges) VALUES (?,?,?) ON DUPLICATE KEY UPDATE privileges = VALUES(privileges)"))
		{
			for (FactionLeaderPrivileges privilege : privileges)
			{
				ps.setInt(1, faction.getId());
				ps.setInt(2, privilege.getObjId());
				ps.setInt(3, privilege.getPrivileges());
				ps.addBatch();
			}
			ps.executeBatch();
		}
		catch (Exception e)
		{
			LOGGER.warning("Failed to batch insert faction leader privileges: " + e.getMessage());
		}
	}
	
	/**
	 * Get the current vote results for a given faction.
	 *
	 * @param faction
	 *            The faction to get vote results for.
	 * @return A list of candidates with their vote counts.
	 */
	public List<VoteResult> getVoteResults(Faction faction)
	{
		List<VoteResult> results = new ArrayList<>();
		FactionLeader factionLeader = getFactionLeader(faction);
		if (factionLeader == null)
		{
			return results;
		}
		// Get the list of candidates
		List<LeaderCandidate> candidates = getCandidates(faction);
		if (candidates.isEmpty())
		{
			return results;
		}
		// Count votes for each candidate
		Map<Integer, Long> voteCounts;
		lock.readLock().lock();
		try
		{
			voteCounts = factionLeader.getLeaderVotes().stream().collect(Collectors.groupingBy(FactionLeaderVote::getVotedForObjId, Collectors.counting()));
		}
		finally
		{
			lock.readLock().unlock();
		}
		// Create vote results
		for (LeaderCandidate candidate : candidates)
		{
			long votes = voteCounts.getOrDefault(candidate.getObjId(), 0L);
			results.add(new VoteResult(candidate.getCharName(), votes));
		}
		// Sort by number of votes (descending)
		results.sort((r1, r2) -> Long.compare(r2.getVotes(), r1.getVotes()));
		return results;
	}
}