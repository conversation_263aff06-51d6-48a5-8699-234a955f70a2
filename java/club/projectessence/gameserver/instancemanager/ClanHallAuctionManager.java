/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.instancemanager;

import club.projectessence.gameserver.data.xml.ClanHallData;
import club.projectessence.gameserver.model.clan.Clan;
import club.projectessence.gameserver.model.eventengine.AbstractEvent;
import club.projectessence.gameserver.model.eventengine.AbstractEventManager;
import club.projectessence.gameserver.model.eventengine.ScheduleTarget;
import club.projectessence.gameserver.model.residences.ClanHallAuction;

import java.util.HashMap;
import java.util.Map;
import java.util.Map.Entry;
import java.util.logging.Logger;

/**
 * <AUTHOR>
 */
public class ClanHallAuctionManager extends AbstractEventManager<AbstractEvent<?>> {
	private static final Logger LOGGER = Logger.getLogger(ClanHallAuctionManager.class.getName());

	private static final Map<Integer, ClanHallAuction> AUCTIONS = new HashMap<>();

	protected ClanHallAuctionManager() {
	}

	public static ClanHallAuctionManager getInstance() {
		return SingletonHolder.INSTANCE;
	}

	@ScheduleTarget
	private void onEventStart() {
		LOGGER.info(getClass().getSimpleName() + ": Clan Hall Auction has started!");
		AUCTIONS.clear();

		//@formatter:off
		ClanHallData.getInstance().getFreeAuctionableHall()
				.forEach(c -> AUCTIONS.put(c.getResidenceId(), new ClanHallAuction(c.getResidenceId())));
		//@formatter:on
	}

	@ScheduleTarget
	private void onEventEnd() {
		AUCTIONS.values().forEach(ClanHallAuction::finalizeAuctions);
		AUCTIONS.clear();
		LOGGER.info(getClass().getSimpleName() + ": Clan Hall Auction has ended!");
	}

	@Override
	public void onInitialized() {
	}

	public ClanHallAuction getClanHallAuctionById(int clanHallId) {
		return AUCTIONS.get(clanHallId);
	}

	public ClanHallAuction getClanHallAuctionByClan(Clan clan) {
		for (ClanHallAuction auction : AUCTIONS.values()) {
			if (auction.getBids().containsKey(clan.getId())) {
				return auction;
			}
		}
		return null;
	}

	public boolean checkForClanBid(int clanHallId, Clan clan) {
		for (Entry<Integer, ClanHallAuction> auction : AUCTIONS.entrySet()) {
			if ((auction.getKey() != clanHallId) && auction.getValue().getBids().containsKey(clan.getId())) {
				return true;
			}
		}
		return false;
	}

	private static class SingletonHolder {
		protected static final ClanHallAuctionManager INSTANCE = new ClanHallAuctionManager();
	}
}
