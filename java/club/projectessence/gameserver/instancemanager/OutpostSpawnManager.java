package club.projectessence.gameserver.instancemanager;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.logging.Level;
import java.util.logging.Logger;

import club.projectessence.Config;
import club.projectessence.gameserver.data.xml.NpcData;
import club.projectessence.gameserver.geoengine.GeoEngine;
import club.projectessence.gameserver.model.Location;
import club.projectessence.gameserver.model.Spawn;
import club.projectessence.gameserver.model.World;
import club.projectessence.gameserver.model.actor.Npc;
import club.projectessence.gameserver.model.actor.instance.OutpostInstance;
import club.projectessence.gameserver.model.actor.templates.NpcTemplate;
import club.projectessence.gameserver.model.zone.type.FactionZone;

public class OutpostSpawnManager
{
	protected static final Logger							LOGGER			= Logger.getLogger(OutpostSpawnManager.class.getName());
	private static final Map<String, List<OutpostInstance>>	zoneOutposts	= new ConcurrentHashMap<>();
	
	public static OutpostInstance spawnOutpost(int npcId, Location loc, String zoneName)
	{
		if (zoneName == null || zoneName.isEmpty())
		{
			LOGGER.severe("OutpostSpawnManager: Invalid zoneName provided for NPC ID: " + npcId);
			return null;
		}
		// Retrieve FactionZone to check controllingFaction
		FactionZone factionZone = ZoneManager.getInstance().getZoneByName(zoneName, FactionZone.class);
		if (factionZone == null)
		{
			LOGGER.severe("OutpostSpawnManager: No FactionZone found for zone " + zoneName + ". Cannot spawn Outpost.");
			return null;
		}
		// Check existing Outposts
		List<OutpostInstance> existingOutposts = zoneOutposts.getOrDefault(zoneName, new CopyOnWriteArrayList<>());
		if (!existingOutposts.isEmpty())
		{
			OutpostInstance existingOutpost = existingOutposts.get(0); // Only one Outpost per zone is supported
			if (existingOutpost.getControllingFaction() == factionZone.getControllingFaction() && !existingOutpost.isDead())
			{
				if (Config.LOG_FACTION_DETAILS)
				{
					LOGGER.info("OutpostSpawnManager: Zone " + zoneName + " already has a valid Outpost (ID " + existingOutpost.getObjectId() + "). Updating instead of respawning.");
				}
				existingOutpost.setControllingFaction(factionZone.getControllingFaction());
				existingOutpost.updateTitle();
				return existingOutpost;
			}
			else
			{
				if (Config.LOG_FACTION_DETAILS)
				{
					LOGGER.info("OutpostSpawnManager: Zone " + zoneName + " has an outdated Outpost (ID " + existingOutpost.getObjectId() + "). Removing it before spawning new Outpost.");
				}
				removeOutpost(zoneName);
			}
		}
		// Spawn new Outpost
		NpcTemplate template = NpcData.getInstance().getTemplate(npcId);
		if (template == null)
		{
			LOGGER.severe("OutpostSpawnManager: Template not found for NPC ID: " + npcId);
			return null;
		}
		try
		{
			if (Config.LOG_FACTION_DETAILS)
			{
				LOGGER.info("OutpostSpawnManager: Starting to spawn Outpost, NPC ID: " + npcId + ", Zone: " + zoneName);
			}
			final Spawn spawn = new Spawn(template);
			int adjustedZ = GeoEngine.getInstance().getHeight(loc.getX(), loc.getY(), loc.getZ());
			spawn.setXYZ(loc.getX(), loc.getY(), adjustedZ);
			spawn.setAmount(1);
			spawn.setHeading(0);
			if (Config.LOG_FACTION_DETAILS)
			{
				LOGGER.fine("OutpostSpawnManager: Calling doSpawn() at " + loc.getX() + "," + loc.getY() + "," + adjustedZ);
			}
			Npc npc = spawn.doSpawn();
			if (npc == null)
			{
				LOGGER.warning("OutpostSpawnManager: Spawn returned null for NPC ID: " + npcId);
				return null;
			}
			if (Config.LOG_FACTION_DETAILS)
			{
				LOGGER.fine("OutpostSpawnManager: Calling init()");
			}
			spawn.init();
			if (!(npc instanceof OutpostInstance))
			{
				LOGGER.warning("OutpostSpawnManager: Spawned NPC is not an OutpostInstance: " + npc.getClass().getSimpleName());
				npc.deleteMe();
				return null;
			}
			OutpostInstance outpost = (OutpostInstance) npc;
			outpost.setZoneName(zoneName); // Gán zoneName
			outpost.setInvul(false);
			outpost.setTargetable(true);
			// Ensure controllingFaction is set correctly
			outpost.setControllingFaction(factionZone.getControllingFaction());
			// Lưu vào database sau khi gán tất cả thuộc tính
			outpost.saveToDatabase(); // Đảm bảo gọi saveToDatabase()
			List<OutpostInstance> outposts = zoneOutposts.computeIfAbsent(zoneName, k -> new CopyOnWriteArrayList<>());
			outposts.add(outpost);
			OutpostPortalManager.getInstance().addOutpost(outpost);
			if (Config.LOG_FACTION_DETAILS)
			{
				LOGGER.info("OutpostSpawnManager: Spawned Outpost for zone: " + zoneName + " at " + loc.getX() + "," + loc.getY() + "," + adjustedZ + ", Max HP: " + outpost.getMaxHp() + ", isTargetable: " + outpost.isTargetable() + ", controllingFaction: " + outpost.getControllingFaction());
			}
			return outpost;
		}
		catch (Exception e)
		{
			LOGGER.log(Level.SEVERE, "OutpostSpawnManager: Error while spawning Outpost NPC ID " + npcId + ": " + e.getMessage(), e);
			return null;
		}
	}
	
	public static void removeOutpost(String zoneName)
	{
		List<OutpostInstance> outposts = zoneOutposts.getOrDefault(zoneName, new CopyOnWriteArrayList<>());
		if (!outposts.isEmpty())
		{
			for (OutpostInstance outpost : outposts)
			{
				if (outpost != null && !outpost.isDecayed())
				{
					World.getInstance().removeObject(outpost);
					outpost.deleteMe();
					OutpostPortalManager.getInstance().removeOutpost(outpost);
					if (Config.LOG_FACTION_DETAILS)
					{
						LOGGER.info("OutpostSpawnManager: Removed Outpost ID " + outpost.getObjectId() + " from zone: " + zoneName + " (Reason: Zone requires new Outpost or Outpost is outdated)");
					}
				}
			}
			outposts.clear();
		}
		else
		{
			if (Config.LOG_FACTION_DETAILS)
			{
				LOGGER.info("OutpostSpawnManager: No Outposts to remove in zone: " + zoneName);
			}
		}
		zoneOutposts.remove(zoneName);
	}
	
	public static OutpostInstance getOutpost(String zoneName)
	{
		List<OutpostInstance> outposts = zoneOutposts.get(zoneName);
		if (outposts != null && !outposts.isEmpty())
		{
			return outposts.get(0);
		}
		return null;
	}
	
	public static List<OutpostInstance> getZoneOutposts(String zoneName)
	{
		return zoneOutposts.computeIfAbsent(zoneName, k -> new CopyOnWriteArrayList<>());
	}
	
	public static void removeZone(String zoneName)
	{
		zoneOutposts.remove(zoneName);
	}
}