package club.projectessence.gameserver.instancemanager;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Future;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;
import java.util.logging.Logger;

import club.projectessence.Config;
import club.projectessence.commons.concurrent.ThreadPool;
import club.projectessence.commons.database.DatabaseFactory;
import club.projectessence.gameserver.cache.HtmCache;
import club.projectessence.gameserver.data.xml.PremiumData;
import club.projectessence.gameserver.data.xml.SkillData;
import club.projectessence.gameserver.model.Party;
import club.projectessence.gameserver.model.World;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.model.events.Containers;
import club.projectessence.gameserver.model.events.EventType;
import club.projectessence.gameserver.model.events.impl.creature.player.OnPlayerLogin;
import club.projectessence.gameserver.model.events.impl.creature.player.OnPlayerLogout;
import club.projectessence.gameserver.model.events.listeners.ConsumerEventListener;
import club.projectessence.gameserver.model.premium.PremiumAccount;
import club.projectessence.gameserver.model.premium.PremiumGift;
import club.projectessence.gameserver.model.events.EventType;
import club.projectessence.gameserver.model.events.ListenerRegisterType;
import club.projectessence.gameserver.model.events.ListenersContainer;
import club.projectessence.gameserver.model.events.annotations.RegisterEvent;
import club.projectessence.gameserver.model.events.annotations.RegisterType;
import club.projectessence.gameserver.model.events.impl.creature.player.OnPlayerLogout;
import club.projectessence.gameserver.model.skills.Skill;
import club.projectessence.gameserver.network.serverpackets.MagicSkillUse;

import club.projectessence.gameserver.model.variables.PlayerVariables;
import club.projectessence.gameserver.network.serverpackets.ExShowScreenMessage;
import club.projectessence.gameserver.network.serverpackets.MagicSkillUse;
import club.projectessence.gameserver.network.serverpackets.NpcHtmlMessage;

/**
 * Manages Premium accounts, storing data by charId with support for multiple Premium packages.
 */
public class PremiumManager extends ListenersContainer
{
	private static final Logger						LOGGER			= Logger.getLogger(PremiumManager.class.getName());
	private static final String						LOAD_SQL		= "SELECT char_id, premium_id, enddate, name, playername FROM character_premium WHERE char_id = ?";
	private static final String						UPDATE_SQL		= "REPLACE INTO character_premium (char_id, premium_id, enddate, name, playername) VALUES (?, ?, ?, ?, ?)";
	private static final String						DELETE_SQL		= "DELETE FROM character_premium WHERE char_id = ?";
	// Data Cache: charId -> {premiumId, enddate, name, playername}
	private final Map<Integer, PremiumDataEntry>	_premiumData	= new ConcurrentHashMap<>();
	// Expire Tasks
	private final Map<Integer, ScheduledFuture<?>>	_expireTasks	= new ConcurrentHashMap<>();
	// Skill Reapply Tasks for premium sharing
	private final Map<Integer, ScheduledFuture<?>>	_skillReapplyTasks	= new ConcurrentHashMap<>();
	// Party update debounce tasks
	private final Map<Integer, ScheduledFuture<?>>	_partyUpdateTasks	= new ConcurrentHashMap<>();
	
	// Internal class to store premium data
	private static class PremiumDataEntry
	{
		final int		premiumId;
		final long		enddate;
		final String	name;
		final String	playername;
		
		PremiumDataEntry(int premiumId, long enddate, String name, String playername)
		{
			this.premiumId = premiumId;
			this.enddate = enddate;
			this.name = name;
			this.playername = playername;
		}
		
		public String getName()
		{
			return name;
		}
		
		public String getPlayername()
		{
			return playername;
		}
	}
	
	protected PremiumManager()
	{
		// Register listeners
		Containers.Players().addListener(new ConsumerEventListener(Containers.Players(), EventType.ON_PLAYER_LOGOUT, event -> stopExpireTask(((OnPlayerLogout) event).getPlayer()), this));
	}
	
	public static PremiumManager getInstance()
	{
		return SingletonHolder.INSTANCE;
	}
	
	public void onPlayerLogin(PlayerInstance player)
	{
		final int charId = player.getObjectId();
		loadPremiumData(charId);
		final long now = System.currentTimeMillis();
		final PremiumDataEntry premiumEntry = _premiumData.get(charId);
		boolean hasPremium = premiumEntry != null && premiumEntry.enddate > now;
		player.setPremiumStatus(hasPremium);
		if (hasPremium)
		{
			startExpireTask(player, premiumEntry.enddate - now);
			notifyPremiumActivation(player);
			applyPremiumBonuses(player, premiumEntry.premiumId);
		}
		else if (premiumEntry != null && premiumEntry.enddate > 0)
		{
			removePremiumStatus(charId, false);
		}
	}
	
	private void startExpireTask(PlayerInstance player, long delay)
	{
		_expireTasks.computeIfAbsent(player.getObjectId(), k -> ThreadPool.get().schedule(new PremiumExpireTask(player), delay));
	}
	
	private void stopExpireTask(PlayerInstance player)
	{
		ScheduledFuture<?> task = _expireTasks.remove(player.getObjectId());
		if (task != null)
		{
			task.cancel(false);
		}
	}
	
	public void loadPremiumData(int charId)
	{
		try (Connection con = DatabaseFactory.getConnection(); PreparedStatement stmt = con.prepareStatement(LOAD_SQL))
		{
			stmt.setInt(1, charId);
			try (ResultSet rset = stmt.executeQuery())
			{
				if (rset.next())
				{
					int premiumId = rset.getInt("premium_id");
					long enddate = rset.getLong("enddate");
					String name = rset.getString("name");
					String playername = rset.getString("playername");
					_premiumData.put(charId, new PremiumDataEntry(premiumId, enddate, name, playername));
				}
			}
		}
		catch (SQLException e)
		{
			LOGGER.warning("Problem with PremiumManager: " + e.getMessage());
		}
	}
	
	public long getPremiumExpiration(int charId)
	{
		PremiumDataEntry premiumEntry = _premiumData.get(charId);
		return premiumEntry != null ? premiumEntry.enddate : 0L;
	}
	
	/**
	 * Lấy ID của gói Premium hiện tại của người chơi.
	 * 
	 * @param charId
	 *            ID của nhân vật
	 * @return ID của gói Premium hiện tại, hoặc 0 nếu không có gói Premium
	 */
	public int getPremiumId(int charId)
	{
		PremiumDataEntry premiumEntry = _premiumData.get(charId);
		return premiumEntry != null ? premiumEntry.premiumId : 0;
	}
	
	/**
	 * Lấy tên gói Premium hiện tại của người chơi.
	 *
	 * @param charId
	 *            ID của nhân vật
	 * @return Tên gói Premium, hoặc "None" nếu không có gói Premium
	 */
	public String getPremiumName(int charId)
	{
		PremiumDataEntry premiumEntry = _premiumData.get(charId);
		return premiumEntry != null ? premiumEntry.getName() : "None";
	}

	/**
	 * Check if player has premium status (for HWID system)
	 * @param player The player to check
	 * @return true if player has active premium
	 */
	public boolean hasPremiumStatus(PlayerInstance player)
	{
		if (player == null)
		{
			return false;
		}

		// First check player's internal premium status
		if (player.hasPremiumStatus())
		{
			return true;
		}

		// Double check with our cache
		final int charId = player.getObjectId();
		final long now = System.currentTimeMillis();
		final PremiumDataEntry premiumEntry = _premiumData.get(charId);
		return premiumEntry != null && premiumEntry.enddate > now;
	}

	/**
	 * Get maximum allowed players per HWID for a specific player
	 * @param player The player to check
	 * @return Maximum allowed connections for this player's HWID
	 */
	public int getMaxPlayersPerHwid(PlayerInstance player)
	{
		if (!Config.ENABLE_PREMIUM_HWID_SLOTS)
		{
			return Config.MAX_PLAYERS_PER_HWID;
		}

		// Check if player has premium status
		if (hasPremiumStatus(player))
		{
			return Config.PREMIUM_HWID_SLOTS;
		}

		// Default limit for free players
		return Config.MAX_PLAYERS_PER_HWID;
	}

	/**
	 * Apply shared premium benefits to all party members
	 * @param premiumPlayer The player with premium who is sharing
	 */
	public void applySharedPremiumToParty(PlayerInstance premiumPlayer)
	{
		if (premiumPlayer.getParty() == null || !premiumPlayer.hasPremiumStatus())
		{
			return;
		}

		// Get premium player's premium data
		int premiumId = getPremiumId(premiumPlayer.getObjectId());
		PremiumAccount premium = PremiumData.getInstance().getPremium(premiumId);
		if (premium == null)
		{
			return;
		}

		// Count non-premium party members
		int nonPremiumMembers = 0;
		for (PlayerInstance member : premiumPlayer.getParty().getMembers())
		{
			if (member != null && member != premiumPlayer && !member.hasPremiumStatus())
			{
				nonPremiumMembers++;
			}
		}

		// If no non-premium members, auto-disable sharing
		if (nonPremiumMembers == 0)
		{
			premiumPlayer.getVariables().set("SHARE_PREMIUM_TO_PARTY", false);
			premiumPlayer.getVariables().storeMe();
			premiumPlayer.sendMessage("Premium sharing automatically disabled - no party members need shared benefits.");
			return;
		}

		// Apply premium status skill effect to sharing player
		Skill premiumStatusSkill = SkillData.getInstance().getSkill(90043, 1);
		if (premiumStatusSkill != null)
		{
			premiumStatusSkill.applyEffects(premiumPlayer, premiumPlayer);

			if (premiumPlayer.getVariables().getBoolean("DEBUG", false))
			{
				System.out.println("DEBUG: Applied skill 90043 to " + premiumPlayer.getName());
			}
		}

		// Get the best (highest) share rate from all premium sharing players in party
		int bestShareRate = getBestShareRateInParty(premiumPlayer.getParty());

		// Apply shared benefits to party members with best share rate
		for (PlayerInstance member : premiumPlayer.getParty().getMembers())
		{
			if (member != null && member != premiumPlayer && !member.hasPremiumStatus())
			{
				applySharedPremiumBonuses(member, premium, bestShareRate);

				// Apply actual skill effect with icon
				Skill sharedPremiumSkill = SkillData.getInstance().getSkill(90044, 1);
				if (sharedPremiumSkill != null)
				{
					sharedPremiumSkill.applyEffects(member, member);
				}

				// Show who's providing the best rate
				String bestRateProvider = getBestShareRateProvider(premiumPlayer.getParty());
				member.sendMessage("You are now receiving " + bestShareRate + "% shared premium benefits (best rate from " + bestRateProvider + ")!");
			}
		}

		// Start premium consumption tracking for sharing
		startPremiumSharingTracking(premiumPlayer);

		// Schedule periodic skill reapply to ensure skill 90043 stays active
		schedulePremiumSkillReapply(premiumPlayer);
	}

	/**
	 * Remove shared premium benefits from all party members
	 * @param premiumPlayer The player who was sharing premium
	 */
	public void removeSharedPremiumFromParty(PlayerInstance premiumPlayer)
	{
		if (premiumPlayer.getVariables().getBoolean("DEBUG", false))
		{
			System.out.println("DEBUG removeSharedPremiumFromParty: called for " + premiumPlayer.getName() + ", party=" + (premiumPlayer.getParty() != null));
		}

		// Stop premium consumption tracking and consume time (even if party is null now)
		stopPremiumSharingTrackingAndConsume(premiumPlayer);

		// Only process party members if party still exists
		if (premiumPlayer.getParty() == null)
		{
			if (premiumPlayer.getVariables().getBoolean("DEBUG", false))
			{
				System.out.println("DEBUG: Party is null, skipping party member processing");
			}
			return;
		}

		// Remove premium status skill effect from sharing player
		if (premiumPlayer.getVariables().getBoolean("DEBUG", false))
		{
			System.out.println("DEBUG: Removing skills 90043 and 90044 from " + premiumPlayer.getName() + " (stop sharing)");
		}
		premiumPlayer.stopSkillEffects(true, 90043); // Premium Status
		premiumPlayer.stopSkillEffects(true, 90044); // Shared Premium Benefits (cleanup any ghost effects)

		// Cancel skill reapply task
		ScheduledFuture<?> skillTask = _skillReapplyTasks.remove(premiumPlayer.getObjectId());
		if (skillTask != null)
		{
			skillTask.cancel(false);
		}

		// Cancel party debounce task if this was the last sharing player
		if (premiumPlayer.getParty() != null)
		{
			int partyId = premiumPlayer.getParty().getLeader().getObjectId();
			boolean hasOtherSharingPlayers = false;
			for (PlayerInstance member : premiumPlayer.getParty().getMembers())
			{
				if (member != null && member != premiumPlayer && member.hasPremiumStatus() &&
					member.getVariables().getBoolean("SHARE_PREMIUM_TO_PARTY", false))
				{
					hasOtherSharingPlayers = true;
					break;
				}
			}

			if (!hasOtherSharingPlayers)
			{
				ScheduledFuture<?> partyTask = _partyUpdateTasks.remove(partyId);
				if (partyTask != null)
				{
					partyTask.cancel(false);
				}
			}
		}

		for (PlayerInstance member : premiumPlayer.getParty().getMembers())
		{
			if (member != null && member != premiumPlayer && !member.hasPremiumStatus())
			{
				removeSharedPremiumBonusesInternal(member);
				member.sendMessage("Shared premium benefits from " + premiumPlayer.getName() + " have been removed.");
			}
		}
	}

	/**
	 * Re-apply shared premium to party without consuming time (for party member changes)
	 * @param premiumPlayer The player sharing premium
	 */
	public void reapplySharedPremiumToParty(PlayerInstance premiumPlayer)
	{
		if (premiumPlayer.getParty() == null || !premiumPlayer.hasPremiumStatus() ||
			!premiumPlayer.getVariables().getBoolean("SHARE_PREMIUM_TO_PARTY", false))
		{
			return;
		}

		// Get premium player's premium data
		int premiumId = getPremiumId(premiumPlayer.getObjectId());
		PremiumAccount premium = PremiumData.getInstance().getPremium(premiumId);
		if (premium == null)
		{
			return;
		}

		// Remove shared benefits from all members first (without consuming time)
		for (PlayerInstance member : premiumPlayer.getParty().getMembers())
		{
			if (member != null && member != premiumPlayer && !member.hasPremiumStatus())
			{
				removeSharedPremiumBonusesInternal(member);
			}
		}

		// Count non-premium party members
		int nonPremiumMembers = 0;
		for (PlayerInstance member : premiumPlayer.getParty().getMembers())
		{
			if (member != null && member != premiumPlayer && !member.hasPremiumStatus())
			{
				nonPremiumMembers++;
			}
		}

		// If no non-premium members, auto-disable sharing
		if (nonPremiumMembers == 0)
		{
			premiumPlayer.getVariables().set("SHARE_PREMIUM_TO_PARTY", false);
			premiumPlayer.getVariables().storeMe();
			stopPremiumSharingTrackingAndConsume(premiumPlayer);
			premiumPlayer.sendMessage("Premium sharing automatically disabled - no party members need shared benefits.");
			return;
		}

		// Apply premium status skill effect to sharing player
		Skill premiumStatusSkill = SkillData.getInstance().getSkill(90043, 1);
		if (premiumStatusSkill != null)
		{
			premiumStatusSkill.applyEffects(premiumPlayer, premiumPlayer);

			if (premiumPlayer.getVariables().getBoolean("DEBUG", false))
			{
				System.out.println("DEBUG: Reapplied skill 90043 to " + premiumPlayer.getName());
			}
		}

		// Don't reset sharing start time when reapplying - preserve existing tracking
		// Only reset if no existing start time (first time sharing)
		long existingStartTime = premiumPlayer.getVariables().getLong("PREMIUM_SHARING_START_TIME", 0);
		if (existingStartTime == 0)
		{
			long currentTime = System.currentTimeMillis();
			premiumPlayer.getVariables().set("PREMIUM_SHARING_START_TIME", currentTime);
			premiumPlayer.getVariables().storeMe();

			if (premiumPlayer.getVariables().getBoolean("DEBUG", false))
			{
				System.out.println("DEBUG reapplySharedPremiumToParty: Set initial sharing start time to " + currentTime);
			}
		}
		else if (premiumPlayer.getVariables().getBoolean("DEBUG", false))
		{
			System.out.println("DEBUG reapplySharedPremiumToParty: Preserving existing start time " + existingStartTime);
		}

		// Get the best (highest) share rate from all premium sharing players in party
		int bestShareRate = getBestShareRateInParty(premiumPlayer.getParty());

		// Re-apply shared benefits to party members with best share rate
		for (PlayerInstance member : premiumPlayer.getParty().getMembers())
		{
			if (member != null && member != premiumPlayer && !member.hasPremiumStatus())
			{
				applySharedPremiumBonuses(member, premium, bestShareRate);

				// Apply actual skill effect with icon
				Skill sharedPremiumSkill = SkillData.getInstance().getSkill(90044, 1);
				if (sharedPremiumSkill != null)
				{
					sharedPremiumSkill.applyEffects(member, member);
				}

				// Show who's providing the best rate
				String bestRateProvider = getBestShareRateProvider(premiumPlayer.getParty());
				member.sendMessage("You are now receiving " + bestShareRate + "% shared premium benefits (best rate from " + bestRateProvider + ")!");
			}
		}

		// Note: Do NOT restart tracking here - keep existing tracking active
	}

	public void addPremiumTime(int charId, int timeValue, TimeUnit timeUnit, int premiumId)
	{
		final long addTime = timeUnit.toMillis(timeValue);
		final long now = System.currentTimeMillis();
		PremiumDataEntry currentPremium = _premiumData.get(charId);
		final long oldPremiumExpiration = currentPremium != null ? Math.max(now, currentPremium.enddate) : now;
		final long newPremiumExpiration = oldPremiumExpiration + addTime;
		// Lấy tên gói Premium từ PremiumData
		PremiumAccount premium = PremiumData.getInstance().getPremium(premiumId);
		String premiumName = premium != null ? premium.getName() : "Unknown";
		// Lấy tên người chơi
		String playername = "Unknown";
		for (PlayerInstance player : World.getInstance().getPlayers())
		{
			if (player.getObjectId() == charId)
			{
				playername = player.getName();
				break;
			}
		}
		// UPDATE DATABASE
		try (Connection con = DatabaseFactory.getConnection(); PreparedStatement stmt = con.prepareStatement(UPDATE_SQL))
		{
			stmt.setInt(1, charId);
			stmt.setInt(2, premiumId);
			stmt.setLong(3, newPremiumExpiration);
			stmt.setString(4, premiumName);
			stmt.setString(5, playername);
			stmt.execute();
		}
		catch (SQLException e)
		{
			LOGGER.warning("Problem with PremiumManager: " + e.getMessage());
		}
		// UPDATE CACHE
		_premiumData.put(charId, new PremiumDataEntry(premiumId, newPremiumExpiration, premiumName, playername));
		// UPDATE PLAYER PREMIUM STATUS
		for (PlayerInstance player : World.getInstance().getPlayers())
		{
			if (player.getObjectId() == charId)
			{
				stopExpireTask(player);
				startExpireTask(player, newPremiumExpiration - now);
				if (!player.hasPremiumStatus())
				{
					player.setPremiumStatus(true);
				}
				notifyPremiumActivation(player);
				applyPremiumBonuses(player, premiumId);
				break;
			}
		}
	}
	
	/**
	 * Cập nhật gói Premium mới cho người chơi mà không thay đổi thời gian hết hạn.
	 * 
	 * @param charId
	 *            ID của nhân vật
	 * @param newPremiumId
	 *            ID của gói Premium mới
	 */
	public void updatePremiumPackage(int charId, int newPremiumId)
	{
		PremiumDataEntry currentPremium = _premiumData.get(charId);
		if (currentPremium == null)
		{
			LOGGER.warning("Cannot update premium package for charId: " + charId + " - No existing premium data found.");
			return;
		}
		// Giữ nguyên thời gian hết hạn
		long existingEnddate = currentPremium.enddate;
		// Lấy tên gói Premium mới từ PremiumData
		PremiumAccount premium = PremiumData.getInstance().getPremium(newPremiumId);
		String premiumName = premium != null ? premium.getName() : "Unknown";
		// Lấy tên người chơi (nếu đã có trong cache thì sử dụng, nếu không thì lấy từ PlayerInstance)
		String playername = currentPremium.getPlayername();
		for (PlayerInstance player : World.getInstance().getPlayers())
		{
			if (player.getObjectId() == charId)
			{
				playername = player.getName();
				break;
			}
		}
		// UPDATE DATABASE
		try (Connection con = DatabaseFactory.getConnection(); PreparedStatement stmt = con.prepareStatement(UPDATE_SQL))
		{
			stmt.setInt(1, charId);
			stmt.setInt(2, newPremiumId);
			stmt.setLong(3, existingEnddate);
			stmt.setString(4, premiumName);
			stmt.setString(5, playername);
			stmt.execute();
		}
		catch (SQLException e)
		{
			LOGGER.warning("Problem with PremiumManager updating package: " + e.getMessage());
		}
		// UPDATE CACHE
		_premiumData.put(charId, new PremiumDataEntry(newPremiumId, existingEnddate, premiumName, playername));
		// Áp dụng lợi ích của gói Premium mới cho người chơi nếu đang online
		for (PlayerInstance player : World.getInstance().getPlayers())
		{
			if (player.getObjectId() == charId)
			{
				applyPremiumBonuses(player, newPremiumId);
				notifyPremiumActivation(player);
				break;
			}
		}
	}
	
	public void removePremiumStatus(int charId, boolean checkOnline)
	{
		if (checkOnline)
		{
			for (PlayerInstance player : World.getInstance().getPlayers())
			{
				if (player.getObjectId() == charId && player.hasPremiumStatus())
				{
					player.setPremiumStatus(false);
					stopExpireTask(player);
					player.sendExpBoostInfo(true);
					String html = HtmCache.getInstance().getHtm(player, "data/html/premium/done.htm");
					html = html.replace("%playername%", player.getName());
					player.sendPacket(new NpcHtmlMessage(html));
					// Xóa các lợi ích Premium
					removePremiumBonuses(player);
					// Xóa biến lưu ngày nhận quà
					player.getVariables().remove(PlayerVariables.LAST_PREMIUM_GIFT_DATE);
					player.getVariables().storeMe();
					break;
				}
			}
		}
		// UPDATE CACHE
		_premiumData.remove(charId);
		// UPDATE DATABASE
		try (Connection con = DatabaseFactory.getConnection(); PreparedStatement stmt = con.prepareStatement(DELETE_SQL))
		{
			stmt.setInt(1, charId);
			stmt.execute();
		}
		catch (SQLException e)
		{
			LOGGER.warning("Problem with PremiumManager: " + e.getMessage());
		}
	}
	
	private void notifyPremiumActivation(PlayerInstance player)
	{
		PremiumDataEntry premiumEntry = _premiumData.get(player.getObjectId());
		if (premiumEntry != null)
		{
			Calendar c = Calendar.getInstance();
			c.setTimeInMillis(premiumEntry.enddate);
			Date date = c.getTime();
			SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
			String txt = "Premium expiration date: " + sdf.format(date);
			player.sendPacket(new ExShowScreenMessage(txt, 2, 5000, 0, true, true));
			player.sendMessage(txt);
			player.sendExpBoostInfo(true);
		}
	}
	
	private void applyPremiumBonuses(PlayerInstance player, int premiumId)
	{
		PremiumAccount premium = PremiumData.getInstance().getPremium(premiumId);
		if (premium != null)
		{
			// Apply gifts (only if not received today)
			List<PremiumGift> gifts = premium.getGifts();
			if (gifts != null)
			{
				String lastGiftDateStr = player.getVariables().getString(PlayerVariables.LAST_PREMIUM_GIFT_DATE, "");
				SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
				String currentDateStr = sdf.format(new Date());
				boolean canReceiveGift = true;
				if (!lastGiftDateStr.isEmpty())
				{
					try
					{
						sdf.parse(lastGiftDateStr);
						canReceiveGift = !lastGiftDateStr.equals(currentDateStr);
					}
					catch (Exception e)
					{
						canReceiveGift = true;
					}
				}
				if (canReceiveGift)
				{
					for (PremiumGift gift : gifts)
					{
						if (gift.isRemovable())
						{
							player.getInventory().addItem("PremiumGift", gift.getId(), gift.getCount(), player, null);
						}
					}
					player.getVariables().set(PlayerVariables.LAST_PREMIUM_GIFT_DATE, currentDateStr);
					player.sendMessage("You have received your daily Premium gifts!");
				}
				else
				{
					player.sendMessage("You have already received your daily Premium gifts today.");
				}
			}
			// Apply rates (Chuyển đổi double thành float)
			player.getVariables().set(PlayerVariables.PREMIUM_EXP_RATE, (float) premium.getExp());
			player.getVariables().set(PlayerVariables.PREMIUM_SP_RATE, (float) premium.getSp());
			player.getVariables().set(PlayerVariables.PREMIUM_ADENA_RATE, (float) premium.getAdena()); // Legacy
			player.getVariables().set(PlayerVariables.PREMIUM_ADENA_CHANCE, (float) premium.getAdenaChance());
			player.getVariables().set(PlayerVariables.PREMIUM_ADENA_AMOUNT, (float) premium.getAdenaAmount());
			player.getVariables().set(PlayerVariables.PREMIUM_DROP_CHANCE, (float) premium.getDropChance());
			player.getVariables().set(PlayerVariables.PREMIUM_DROP_AMOUNT, (float) premium.getDropAmount());
			player.getVariables().set(PlayerVariables.PREMIUM_SPOIL_CHANCE, (float) premium.getSpoilChance());
			player.getVariables().set(PlayerVariables.PREMIUM_SPOIL_AMOUNT, (float) premium.getSpoilAmount());
			player.getVariables().set(PlayerVariables.PREMIUM_SAYHA_GRACE_XP, (float) premium.getSayhaGraceXp());
			player.getVariables().set(PlayerVariables.PREMIUM_LIMITED_SAYHA_GRACE_XP, (float) premium.getLimitedSayhaGraceXp());
			player.getVariables().set(PlayerVariables.PREMIUM_RESURRECTION_COST, (float) premium.getResurrectionCost());
			player.getVariables().set(PlayerVariables.PREMIUM_RANDOM_CRAFT_HERB, (float) premium.getRandomCraftHerb());
			player.getVariables().set(PlayerVariables.PREMIUM_LCOIN_DROP_AMOUNT_ADD, (float) premium.getLCoinDropAmountAdd());
			player.getVariables().set(PlayerVariables.PREMIUM_ONLY_FISHING, premium.getOnlyFishing());
			// Lưu DROP_CHANCE_BY_ID và DROP_AMOUNT_BY_ID
			for (Map.Entry<Integer, Float> entry : premium.getDropChanceByIdMap().entrySet())
			{
				player.getVariables().set("PREMIUM_DROP_CHANCE_BY_ID_" + entry.getKey(), entry.getValue());
			}
			for (Map.Entry<Integer, Float> entry : premium.getDropAmountByIdMap().entrySet())
			{
				player.getVariables().set("PREMIUM_DROP_AMOUNT_BY_ID_" + entry.getKey(), entry.getValue());
			}
			// Lưu vào cơ sở dữ liệu một lần duy nhất
			try
			{
				player.getVariables().storeMe();
			}
			catch (Exception e)
			{
				LOGGER.severe("Player " + player.getName() + " - Failed to store PlayerVariables: " + e.getMessage());
			}
		}
	}
	
	private void removePremiumBonuses(PlayerInstance player)
	{
		// Auto-stop premium sharing when premium expires
		if (player.getVariables().getBoolean("SHARE_PREMIUM_TO_PARTY", false))
		{
			if (player.getVariables().getBoolean("DEBUG", false))
			{
				System.out.println("DEBUG: Auto-stopping premium sharing for " + player.getName() + " (premium expired)");
			}

			// Stop sharing and consume time
			removeSharedPremiumFromParty(player);

			// Disable sharing setting
			player.getVariables().set("SHARE_PREMIUM_TO_PARTY", false);
			player.getVariables().storeMe();

			player.sendMessage("Premium sharing automatically stopped due to premium expiration.");

			// Reapply benefits for remaining premium players in party
			if (player.getParty() != null)
			{
				for (PlayerInstance member : player.getParty().getMembers())
				{
					if (member != null && member != player && member.hasPremiumStatus() &&
						member.getVariables().getBoolean("SHARE_PREMIUM_TO_PARTY", false))
					{
						reapplySharedPremiumToParty(member);
						break; // Only need to trigger once
					}
				}
			}
		}

		// Remove both premium skills when premium expires
		player.stopSkillEffects(true, 90043); // Premium Status
		player.stopSkillEffects(true, 90044); // Shared Premium Benefits (in case player was receiving)

		if (player.getVariables().getBoolean("DEBUG", false))
		{
			System.out.println("DEBUG: Removed skills 90043 and 90044 on premium expiration for " + player.getName());
		}

		// Đặt các biến Premium về giá trị mặc định
		player.getVariables().set(PlayerVariables.PREMIUM_EXP_RATE, 0.0f);
		player.getVariables().set(PlayerVariables.PREMIUM_SP_RATE, 0.0f);
		player.getVariables().set(PlayerVariables.PREMIUM_ADENA_RATE, 0.0f); // Legacy
		player.getVariables().set(PlayerVariables.PREMIUM_ADENA_CHANCE, 0.0f);
		player.getVariables().set(PlayerVariables.PREMIUM_ADENA_AMOUNT, 0.0f);
		player.getVariables().set(PlayerVariables.PREMIUM_DROP_CHANCE, 0.0f);
		player.getVariables().set(PlayerVariables.PREMIUM_DROP_AMOUNT, 0.0f);
		player.getVariables().set(PlayerVariables.PREMIUM_SPOIL_CHANCE, 0.0f);
		player.getVariables().set(PlayerVariables.PREMIUM_SPOIL_AMOUNT, 0.0f);
		player.getVariables().set(PlayerVariables.PREMIUM_SAYHA_GRACE_XP, 0.0f);
		player.getVariables().set(PlayerVariables.PREMIUM_LIMITED_SAYHA_GRACE_XP, 0.0f);
		player.getVariables().set(PlayerVariables.PREMIUM_RESURRECTION_COST, 0.0f);
		player.getVariables().set(PlayerVariables.PREMIUM_RANDOM_CRAFT_HERB, 0.0f);
		player.getVariables().set(PlayerVariables.PREMIUM_LCOIN_DROP_AMOUNT_ADD, 0.0f);
		player.getVariables().set(PlayerVariables.PREMIUM_ONLY_FISHING, false);
		// Xóa các biến DROP_CHANCE_BY_ID và DROP_AMOUNT_BY_ID
		for (int itemId : PremiumData.getInstance().getAllItemIdsInDropMaps())
		{
			player.getVariables().remove("PREMIUM_DROP_CHANCE_BY_ID_" + itemId);
			player.getVariables().remove("PREMIUM_DROP_AMOUNT_BY_ID_" + itemId);
		}
		// Lưu vào cơ sở dữ liệu
		player.getVariables().storeMe();
	}

	/**
	 * Apply shared premium bonuses with custom share rate
	 * @param player The player receiving shared benefits
	 * @param premium The premium account data to share
	 * @param shareRate The share rate percentage (10-90)
	 */
	private void applySharedPremiumBonuses(PlayerInstance player, PremiumAccount premium, int shareRate)
	{
		// Apply custom share rate of premium rates as shared benefits
		float shareMultiplier = shareRate / 100.0f;
		player.getVariables().set("SHARED_PREMIUM_EXP_RATE", (float) ((premium.getExp() - 1.0) * shareMultiplier + 1.0));
		player.getVariables().set("SHARED_PREMIUM_SP_RATE", (float) ((premium.getSp() - 1.0) * shareMultiplier + 1.0));
		player.getVariables().set("SHARED_PREMIUM_ADENA_CHANCE", (float) ((premium.getAdenaChance() - 1.0) * shareMultiplier + 1.0));
		player.getVariables().set("SHARED_PREMIUM_ADENA_AMOUNT", (float) ((premium.getAdenaAmount() - 1.0) * shareMultiplier + 1.0));
		player.getVariables().set("SHARED_PREMIUM_DROP_CHANCE", (float) ((premium.getDropChance() - 1.0) * shareMultiplier + 1.0));
		player.getVariables().set("SHARED_PREMIUM_DROP_AMOUNT", (float) ((premium.getDropAmount() - 1.0) * shareMultiplier + 1.0));
		player.getVariables().set("SHARED_PREMIUM_SPOIL_CHANCE", (float) ((premium.getSpoilChance() - 1.0) * shareMultiplier + 1.0));
		player.getVariables().set("SHARED_PREMIUM_SPOIL_AMOUNT", (float) ((premium.getSpoilAmount() - 1.0) * shareMultiplier + 1.0));
		player.getVariables().set("SHARED_PREMIUM_SAYHA_GRACE_XP", (float) ((premium.getSayhaGraceXp() - 1.0) * shareMultiplier + 1.0));
		player.getVariables().set("SHARED_PREMIUM_LIMITED_SAYHA_GRACE_XP", (float) ((premium.getLimitedSayhaGraceXp() - 1.0) * shareMultiplier + 1.0));
		player.getVariables().set("SHARED_PREMIUM_RESURRECTION_COST", (float) ((premium.getResurrectionCost() - 1.0) * shareMultiplier + 1.0));
		player.getVariables().set("SHARED_PREMIUM_RANDOM_CRAFT_HERB", (float) ((premium.getRandomCraftHerb() - 1.0) * shareMultiplier + 1.0));
		player.getVariables().set("SHARED_PREMIUM_LCOIN_DROP_AMOUNT_ADD", (float) (premium.getLCoinDropAmountAdd() * shareMultiplier));

		// Apply shared drop rates by item ID with custom share rate
		for (Map.Entry<Integer, Float> entry : premium.getDropChanceByIdMap().entrySet())
		{
			float sharedRate = (entry.getValue() - 1.0f) * shareMultiplier + 1.0f;
			player.getVariables().set("SHARED_PREMIUM_DROP_CHANCE_BY_ID_" + entry.getKey(), sharedRate);
		}
		for (Map.Entry<Integer, Float> entry : premium.getDropAmountByIdMap().entrySet())
		{
			float sharedRate = (entry.getValue() - 1.0f) * shareMultiplier + 1.0f;
			player.getVariables().set("SHARED_PREMIUM_DROP_AMOUNT_BY_ID_" + entry.getKey(), sharedRate);
		}

		player.getVariables().storeMe();
	}

	/**
	 * Apply shared premium bonuses with default 50% share rate (backward compatibility)
	 * @param player The player receiving shared benefits
	 * @param premium The premium account data to share
	 */
	private void applySharedPremiumBonuses(PlayerInstance player, PremiumAccount premium)
	{
		applySharedPremiumBonuses(player, premium, 50); // Default 50%
	}

	/**
	 * Remove shared premium bonuses (internal method)
	 * @param player The player to remove shared benefits from
	 */
	private void removeSharedPremiumBonusesInternal(PlayerInstance player)
	{
		// Remove shared premium skill effect
		player.stopSkillEffects(true, 90044);

		if (player.getVariables().getBoolean("DEBUG", false))
		{
			System.out.println("DEBUG: Removed skill 90044 from " + player.getName() + " (remove shared benefits)");
		}

		// Remove all shared premium variables
		player.getVariables().remove("SHARED_PREMIUM_EXP_RATE");
		player.getVariables().remove("SHARED_PREMIUM_SP_RATE");
		player.getVariables().remove("SHARED_PREMIUM_ADENA_CHANCE");
		player.getVariables().remove("SHARED_PREMIUM_ADENA_AMOUNT");
		player.getVariables().remove("SHARED_PREMIUM_DROP_CHANCE");
		player.getVariables().remove("SHARED_PREMIUM_DROP_AMOUNT");
		player.getVariables().remove("SHARED_PREMIUM_SPOIL_CHANCE");
		player.getVariables().remove("SHARED_PREMIUM_SPOIL_AMOUNT");
		player.getVariables().remove("SHARED_PREMIUM_SAYHA_GRACE_XP");
		player.getVariables().remove("SHARED_PREMIUM_LIMITED_SAYHA_GRACE_XP");
		player.getVariables().remove("SHARED_PREMIUM_RESURRECTION_COST");
		player.getVariables().remove("SHARED_PREMIUM_RANDOM_CRAFT_HERB");
		player.getVariables().remove("SHARED_PREMIUM_LCOIN_DROP_AMOUNT_ADD");

		// Remove shared drop rates by item ID
		for (int itemId : PremiumData.getInstance().getAllItemIdsInDropMaps())
		{
			player.getVariables().remove("SHARED_PREMIUM_DROP_CHANCE_BY_ID_" + itemId);
			player.getVariables().remove("SHARED_PREMIUM_DROP_AMOUNT_BY_ID_" + itemId);
		}

		player.getVariables().storeMe();
	}

	/**
	 * Remove shared premium bonuses from a specific player (public method)
	 * @param player The player to remove shared benefits from
	 */
	public void removeSharedPremiumBonuses(PlayerInstance player)
	{
		removeSharedPremiumBonusesInternal(player);
	}

	/**
	 * Get effective premium rate (individual premium or shared premium)
	 * @param player The player to check
	 * @param rateType The type of rate (e.g., "EXP_RATE", "DROP_CHANCE")
	 * @return The effective premium rate
	 */
	public float getEffectivePremiumRate(PlayerInstance player, String rateType)
	{
		// Check individual premium first
		if (player.hasPremiumStatus())
		{
			return player.getVariables().getFloat("PREMIUM_" + rateType, 1.0f);
		}

		// Check shared premium
		return player.getVariables().getFloat("SHARED_PREMIUM_" + rateType, 1.0f);
	}

	/**
	 * Check if player has any premium benefits (individual or shared)
	 * @param player The player to check
	 * @return true if player has premium benefits
	 */
	public boolean hasAnyPremiumBenefits(PlayerInstance player)
	{
		return player.hasPremiumStatus() || player.getVariables().getFloat("SHARED_PREMIUM_EXP_RATE", 0.0f) > 0.0f;
	}

	/**
	 * Check if player has individual premium (for gifts)
	 * @param player The player to check
	 * @return true if player has individual premium only
	 */
	public boolean hasIndividualPremiumOnly(PlayerInstance player)
	{
		return player.hasPremiumStatus();
	}

	/**
	 * Start premium sharing tracking - record start time
	 * @param premiumPlayer The player sharing premium
	 */
	private void startPremiumSharingTracking(PlayerInstance premiumPlayer)
	{
		// Always reset start time when starting new sharing session
		long startTime = System.currentTimeMillis();
		premiumPlayer.getVariables().set("PREMIUM_SHARING_START_TIME", startTime);
		premiumPlayer.getVariables().storeMe(); // Ensure immediate save

		if (premiumPlayer.getVariables().getBoolean("DEBUG", false))
		{
			System.out.println("DEBUG: Premium sharing tracking started at " + startTime);
		}
	}

	/**
	 * Schedule periodic reapply of premium status skill to ensure it stays active
	 * @param premiumPlayer The player sharing premium
	 */
	private void schedulePremiumSkillReapply(PlayerInstance premiumPlayer)
	{
		// Cancel existing task if any
		ScheduledFuture<?> existingTask = _skillReapplyTasks.remove(premiumPlayer.getObjectId());
		if (existingTask != null)
		{
			existingTask.cancel(false);
		}

		// Schedule new task to reapply skill every 30 seconds
		ScheduledFuture<?> task = ThreadPool.get().scheduleAtFixedRate(() -> {
			if (premiumPlayer.isOnline() &&
				premiumPlayer.hasPremiumStatus() &&
				premiumPlayer.getVariables().getBoolean("SHARE_PREMIUM_TO_PARTY", false) &&
				premiumPlayer.getParty() != null && premiumPlayer.getParty().getMemberCount() > 1)
			{
				// Reapply skill 90043
				Skill premiumStatusSkill = SkillData.getInstance().getSkill(90043, 1);
				if (premiumStatusSkill != null)
				{
					premiumStatusSkill.applyEffects(premiumPlayer, premiumPlayer);

					if (premiumPlayer.getVariables().getBoolean("DEBUG", false))
					{
						System.out.println("DEBUG: Periodic reapply skill 90043 to " + premiumPlayer.getName());
					}
				}
			}
			else
			{
				// Stop task if conditions no longer met
				ScheduledFuture<?> currentTask = _skillReapplyTasks.remove(premiumPlayer.getObjectId());
				if (currentTask != null)
				{
					currentTask.cancel(false);
				}
			}
		}, 60000, 60000); // 60 seconds interval (reduced frequency for large parties)

		_skillReapplyTasks.put(premiumPlayer.getObjectId(), task);
	}

	/**
	 * Debounced party update to handle frequent join/leave in large parties
	 * @param party The party that changed
	 */
	public void debouncedPartyUpdate(Party party)
	{
		if (party == null)
		{
			return;
		}

		int partyId = party.getLeader().getObjectId(); // Use leader ID as party identifier

		// Cancel existing debounce task
		ScheduledFuture<?> existingTask = _partyUpdateTasks.remove(partyId);
		if (existingTask != null)
		{
			existingTask.cancel(false);
		}

		// Schedule new debounced update (2 seconds delay)
		ScheduledFuture<?> task = ThreadPool.get().schedule(() -> {
			// Remove task from map
			_partyUpdateTasks.remove(partyId);

			// Apply premium sharing for all premium players in party
			for (PlayerInstance member : party.getMembers())
			{
				if (member != null && member.hasPremiumStatus() &&
					member.getVariables().getBoolean("SHARE_PREMIUM_TO_PARTY", false))
				{
					reapplySharedPremiumToParty(member);

					if (member.getVariables().getBoolean("DEBUG", false))
					{
						System.out.println("DEBUG: Debounced party update for " + member.getName());
					}
				}
			}
		}, 2000); // 2 second delay

		_partyUpdateTasks.put(partyId, task);
	}

	/**
	 * Stop premium sharing tracking and consume time based on duration (split among multiple premium players)
	 * @param premiumPlayer The player to stop sharing for
	 */
	private void stopPremiumSharingTrackingAndConsume(PlayerInstance premiumPlayer)
	{
		long startTime = premiumPlayer.getVariables().getLong("PREMIUM_SHARING_START_TIME", 0);
		if (premiumPlayer.getVariables().getBoolean("DEBUG", false))
		{
			System.out.println("DEBUG stopPremiumSharingTrackingAndConsume: startTime=" + startTime + " for " + premiumPlayer.getName());
		}

		if (startTime == 0)
		{
			if (premiumPlayer.getVariables().getBoolean("DEBUG", false))
			{
				System.out.println("DEBUG: No tracking was active for " + premiumPlayer.getName());
			}
			return; // No tracking was active
		}

		// Calculate duration in party
		long endTime = System.currentTimeMillis();
		long durationInParty = endTime - startTime; // milliseconds

		if (premiumPlayer.getVariables().getBoolean("DEBUG", false))
		{
			System.out.println("DEBUG: durationInParty=" + durationInParty + "ms (" + (durationInParty/1000) + "s)");
		}

		// Count how many premium players are sharing and non-premium members in the party
		int premiumSharingCount = 0;
		int nonPremiumCount = 0;
		if (premiumPlayer.getParty() != null)
		{
			for (PlayerInstance member : premiumPlayer.getParty().getMembers())
			{
				if (member != null)
				{
					if (member.hasPremiumStatus() && member.getVariables().getBoolean("SHARE_PREMIUM_TO_PARTY", false))
					{
						premiumSharingCount++;
					}
					else if (!member.hasPremiumStatus())
					{
						nonPremiumCount++;
					}
				}
			}
		}

		// If no non-premium members to benefit, don't consume time
		if (nonPremiumCount == 0)
		{
			if (premiumPlayer.getVariables().getBoolean("DEBUG", false))
			{
				System.out.println("DEBUG: No non-premium members in party, skipping time consumption");
			}

			// Clear tracking variables without consuming time
			premiumPlayer.getVariables().remove("PREMIUM_SHARING_START_TIME");
			premiumPlayer.getVariables().storeMe();
			return;
		}

		// If no other premium players sharing, this player consumes full time
		if (premiumSharingCount <= 1)
		{
			premiumSharingCount = 1;
		}

		// Split consumption among premium sharing players
		long consumedTime = durationInParty / premiumSharingCount;

		if (premiumPlayer.getVariables().getBoolean("DEBUG", false))
		{
			System.out.println("DEBUG: premiumSharingCount=" + premiumSharingCount + ", consumedTime=" + consumedTime + "ms");
		}

		// Consume premium time (split portion)
		long currentExpiration = getPremiumExpiration(premiumPlayer.getObjectId());
		long currentTime = System.currentTimeMillis();

		// Ensure we don't consume more time than available
		long availableTime = currentExpiration - currentTime;
		if (consumedTime > availableTime && availableTime > 0)
		{
			consumedTime = availableTime;
			if (premiumPlayer.getVariables().getBoolean("DEBUG", false))
			{
				System.out.println("DEBUG: Limited consumption to available time: " + consumedTime + "ms");
			}
		}

		long newExpiration = currentExpiration - consumedTime;

		if (premiumPlayer.getVariables().getBoolean("DEBUG", false))
		{
			System.out.println("DEBUG: currentExpiration=" + currentExpiration + ", newExpiration=" + newExpiration + ", consumedTime=" + consumedTime);
		}

		// Update premium expiration
		if (premiumPlayer.getVariables().getBoolean("DEBUG", false))
		{
			System.out.println("DEBUG: Calling updatePremiumExpiration for " + premiumPlayer.getName());
		}
		updatePremiumExpiration(premiumPlayer.getObjectId(), newExpiration);

		// Calculate consumed time for display
		long consumedMinutes = consumedTime / (60 * 1000);
		long consumedSeconds = (consumedTime % (60 * 1000)) / 1000;

		// Notify player about consumption
		if (consumedMinutes > 0)
		{
			if (premiumSharingCount > 1)
			{
				premiumPlayer.sendMessage("Premium time consumed: " + consumedMinutes + " minute(s) " + consumedSeconds + " second(s) (split among " + premiumSharingCount + " premium players)");
			}
			else
			{
				premiumPlayer.sendMessage("Premium time consumed: " + consumedMinutes + " minute(s) " + consumedSeconds + " second(s) for party sharing.");
			}
		}
		else if (consumedSeconds > 0)
		{
			if (premiumSharingCount > 1)
			{
				premiumPlayer.sendMessage("Premium time consumed: " + consumedSeconds + " second(s) (split among " + premiumSharingCount + " premium players)");
			}
			else
			{
				premiumPlayer.sendMessage("Premium time consumed: " + consumedSeconds + " second(s) for party sharing.");
			}
		}

		// Clear tracking
		premiumPlayer.getVariables().remove("PREMIUM_SHARING_START_TIME");
	}

	/**
	 * Update premium expiration time
	 * @param charId Character ID
	 * @param newExpiration New expiration time
	 */
	private void updatePremiumExpiration(int charId, long newExpiration)
	{
		// Debug
		PlayerInstance debugPlayer = World.getInstance().getPlayer(charId);
		if (debugPlayer != null && debugPlayer.getVariables().getBoolean("DEBUG", false))
		{
			System.out.println("DEBUG updatePremiumExpiration: charId=" + charId + ", newExpiration=" + newExpiration);
		}

		// Update database
		try (Connection con = DatabaseFactory.getConnection(); PreparedStatement stmt = con.prepareStatement("UPDATE character_premium SET enddate = ? WHERE char_id = ?"))
		{
			stmt.setLong(1, newExpiration);
			stmt.setInt(2, charId);
			stmt.execute();
		}
		catch (SQLException e)
		{
			LOGGER.warning("Problem updating premium expiration: " + e.getMessage());
		}

		// Update cache
		PremiumDataEntry entry = _premiumData.get(charId);
		if (entry != null)
		{
			_premiumData.put(charId, new PremiumDataEntry(entry.premiumId, newExpiration, entry.name, entry.playername));
		}

		// Update player's expire task if online
		for (PlayerInstance player : World.getInstance().getPlayers())
		{
			if (player.getObjectId() == charId)
			{
				stopExpireTask(player);
				long now = System.currentTimeMillis();
				if (newExpiration > now)
				{
					startExpireTask(player, newExpiration - now);

					if (player.getVariables().getBoolean("DEBUG", false))
					{
						System.out.println("DEBUG: Updated expire task for " + player.getName() + ", new expiration: " + newExpiration);
					}
				}
				else
				{
					// Premium expired, remove status
					player.setPremiumStatus(false);
					removePremiumBonuses(player);

					if (player.getVariables().getBoolean("DEBUG", false))
					{
						System.out.println("DEBUG: Premium expired for " + player.getName() + " after consumption");
					}
				}
				break;
			}
		}
	}

	/**
	 * Get real-time premium expiration considering current sharing consumption
	 * @param charId Character ID
	 * @return Real-time premium expiration timestamp
	 */
	public long getRealTimePremiumExpiration(int charId)
	{
		long baseExpiration = getPremiumExpiration(charId);
		if (baseExpiration == 0)
		{
			return 0; // No premium
		}

		// Check if player is currently sharing premium AND actively in party
		PlayerInstance player = World.getInstance().getPlayer(charId);
		if (player != null && player.getVariables().getBoolean("SHARE_PREMIUM_TO_PARTY", false) && player.getParty() != null)
		{
			long startTime = player.getVariables().getLong("PREMIUM_SHARING_START_TIME", 0);
			if (startTime > 0)
			{
				// Calculate time consumed so far
				long currentTime = System.currentTimeMillis();
				long timeConsumed = currentTime - startTime;

				// Validate time consumed (prevent negative or unrealistic values)
				if (timeConsumed > 0 && timeConsumed < (24 * 60 * 60 * 1000)) // Less than 24 hours
				{
					// Return remaining time (base expiration minus consumed time)
					long remainingTime = baseExpiration - timeConsumed;
					return Math.max(remainingTime, 0); // Ensure non-negative
				}
			}
		}

		return baseExpiration; // Not sharing or no tracking
	}

	/**
	 * Get current sharing duration for display purposes
	 * @param charId Character ID
	 * @return Sharing duration in milliseconds, or 0 if not sharing
	 */
	public long getCurrentSharingDuration(int charId)
	{
		PlayerInstance player = World.getInstance().getPlayer(charId);
		if (player != null)
		{
			boolean shareEnabled = player.getVariables().getBoolean("SHARE_PREMIUM_TO_PARTY", false);
			boolean inParty = player.getParty() != null;
			long startTime = player.getVariables().getLong("PREMIUM_SHARING_START_TIME", 0);

			// Conditional debug
			if (player.getVariables().getBoolean("DEBUG", false))
			{
				System.out.println("DEBUG getCurrentSharingDuration: shareEnabled=" + shareEnabled + ", inParty=" + inParty + ", startTime=" + startTime);
			}

			if (shareEnabled && inParty && startTime > 0)
			{
				long currentTime = System.currentTimeMillis();
				long duration = currentTime - startTime;

				if (player.getVariables().getBoolean("DEBUG", false))
				{
					System.out.println("DEBUG duration calculation: currentTime=" + currentTime + ", duration=" + duration);
				}

				// If duration is negative, reset startTime to current time
				if (duration < 0)
				{
					if (player.getVariables().getBoolean("DEBUG", false))
					{
						System.out.println("DEBUG: Negative duration detected, resetting startTime to current time");
					}
					player.getVariables().set("PREMIUM_SHARING_START_TIME", currentTime);
					player.getVariables().storeMe();
					return 0; // Return 0 for fresh start
				}

				// Return valid duration only
				if (duration >= 0 && duration < (24 * 60 * 60 * 1000))
				{
					return duration;
				}
				else
				{
					if (player.getVariables().getBoolean("DEBUG", false))
					{
						System.out.println("DEBUG: Invalid duration " + duration + ", resetting tracking");
					}
					// Reset invalid tracking
					player.getVariables().remove("PREMIUM_SHARING_START_TIME");
					player.getVariables().storeMe();
				}
			}
		}
		return 0;
	}

	/**
	 * Handle player logout - cleanup shared premium and consumption tasks
	 * @param event OnPlayerLogout event
	 */
	@RegisterEvent(EventType.ON_PLAYER_LOGOUT)
	@RegisterType(ListenerRegisterType.GLOBAL_PLAYERS)
	public void onPlayerLogout(OnPlayerLogout event)
	{
		PlayerInstance player = event.getPlayer();
		if (player == null)
		{
			return;
		}

		if (player.getVariables().getBoolean("DEBUG", false))
		{
			System.out.println("DEBUG: Player " + player.getName() + " logging out, checking premium sharing");
		}

		// Stop premium sharing if active and consume time
		if (player.getVariables().getBoolean("SHARE_PREMIUM_TO_PARTY", false))
		{
			if (player.getVariables().getBoolean("DEBUG", false))
			{
				System.out.println("DEBUG: Player " + player.getName() + " was sharing premium, stopping and consuming time");
			}

			removeSharedPremiumFromParty(player);
		}

		// Remove shared premium bonuses if player was receiving them
		if (!player.hasPremiumStatus())
		{
			removeSharedPremiumBonusesInternal(player);
		}

		// Force remove both premium skills on logout to prevent ghost effects
		player.stopSkillEffects(true, 90043); // Premium Status
		player.stopSkillEffects(true, 90044); // Shared Premium Benefits

		if (player.getVariables().getBoolean("DEBUG", false))
		{
			System.out.println("DEBUG: Removed skills 90043 and 90044 on logout for " + player.getName());
		}

		// Cleanup tasks
		ScheduledFuture<?> skillTask = _skillReapplyTasks.remove(player.getObjectId());
		if (skillTask != null)
		{
			skillTask.cancel(false);
		}
	}

	/**
	 * Handle player login - cleanup stale sharing tracking
	 * @param event OnPlayerLogin event
	 */
	@RegisterEvent(EventType.ON_PLAYER_LOGIN)
	@RegisterType(ListenerRegisterType.GLOBAL_PLAYERS)
	public void onPlayerLogin(OnPlayerLogin event)
	{
		PlayerInstance player = event.getPlayer();
		if (player == null)
		{
			return;
		}

		// Clear any stale sharing tracking from previous session
		// This prevents double consumption after relog
		if (player.getVariables().getLong("PREMIUM_SHARING_START_TIME", 0) > 0)
		{
			player.getVariables().remove("PREMIUM_SHARING_START_TIME");
			player.getVariables().storeMe();
		}

		// Clear ghost premium skills from previous session (crash/disconnect)
		player.stopSkillEffects(true, 90043); // Premium Status
		player.stopSkillEffects(true, 90044); // Shared Premium Benefits

		if (player.getVariables().getBoolean("DEBUG", false))
		{
			System.out.println("DEBUG: Cleared ghost premium skills on login for " + player.getName());
		}
	}



	/**
	 * Get the best (highest) share rate from all premium sharing players in party
	 * @param party The party to check
	 * @return The highest share rate found, or 50 if no sharing players
	 */
	private int getBestShareRateInParty(Party party)
	{
		if (party == null)
		{
			return 50; // Default rate
		}

		int bestRate = 0;
		for (PlayerInstance member : party.getMembers())
		{
			if (member != null && member.hasPremiumStatus() &&
				member.getVariables().getBoolean("SHARE_PREMIUM_TO_PARTY", false))
			{
				int memberRate = member.getVariables().getInt("PREMIUM_SHARE_RATE", 50);
				bestRate = Math.max(bestRate, memberRate);
			}
		}

		return bestRate > 0 ? bestRate : 50; // Return 50 if no sharing players found
	}

	/**
	 * Get the name of the player providing the best share rate
	 * @param party The party to check
	 * @return Name of the player with highest share rate
	 */
	private String getBestShareRateProvider(Party party)
	{
		if (party == null)
		{
			return "Unknown";
		}

		int bestRate = 0;
		String bestProvider = "Unknown";
		for (PlayerInstance member : party.getMembers())
		{
			if (member != null && member.hasPremiumStatus() &&
				member.getVariables().getBoolean("SHARE_PREMIUM_TO_PARTY", false))
			{
				int memberRate = member.getVariables().getInt("PREMIUM_SHARE_RATE", 50);
				if (memberRate > bestRate)
				{
					bestRate = memberRate;
					bestProvider = member.getName();
				}
			}
		}

		return bestProvider;
	}

	/**
	 * Format duration in milliseconds to HH:MM:SS or MM:SS format
	 * @param duration Duration in milliseconds
	 * @return Formatted time string
	 */
	public static String formatSharingDuration(long duration)
	{
		if (duration <= 0)
		{
			return "00:00";
		}

		long hours = duration / (60 * 60 * 1000);
		long minutes = (duration % (60 * 60 * 1000)) / (60 * 1000);
		long seconds = (duration % (60 * 1000)) / 1000;

		if (hours > 0)
		{
			return String.format("%02d:%02d:%02d", hours, minutes, seconds);
		}
		else
		{
			return String.format("%02d:%02d", minutes, seconds);
		}
	}

	private static class SingletonHolder
	{
		protected static final PremiumManager INSTANCE = new PremiumManager();
	}
	
	class PremiumExpireTask implements Runnable
	{
		final PlayerInstance _player;
		
		PremiumExpireTask(PlayerInstance player)
		{
			_player = player;
		}
		
		@Override
		public void run()
		{
			_player.setPremiumStatus(false);
			_player.sendExpBoostInfo(true);
			String html = HtmCache.getInstance().getHtm(_player, "data/html/premium/done.htm");
			html = html.replace("%playername%", _player.getName());
			_player.sendPacket(new NpcHtmlMessage(html));
			// Xóa các lợi ích Premium khi gói hết hạn
			removePremiumBonuses(_player);
		}
	}

	/**
	 * Cleanup all tasks when server shuts down
	 */
	public void shutdown()
	{
		// Cancel all skill reapply tasks
		for (ScheduledFuture<?> task : _skillReapplyTasks.values())
		{
			if (task != null && !task.isCancelled())
			{
				task.cancel(false);
			}
		}
		_skillReapplyTasks.clear();

		// Cancel all party update tasks
		for (ScheduledFuture<?> task : _partyUpdateTasks.values())
		{
			if (task != null && !task.isCancelled())
			{
				task.cancel(false);
			}
		}
		_partyUpdateTasks.clear();

		// Cancel all expire tasks
		for (ScheduledFuture<?> task : _expireTasks.values())
		{
			if (task != null && !task.isCancelled())
			{
				task.cancel(false);
			}
		}
		_expireTasks.clear();

		LOGGER.info("PremiumManager: All tasks cleaned up on shutdown");
	}
}