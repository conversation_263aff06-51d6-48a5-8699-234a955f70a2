package club.projectessence.gameserver.instancemanager;

import club.projectessence.Config;
import club.projectessence.commons.database.DatabaseFactory;
import club.projectessence.gameserver.enums.PrivateStoreType;
import club.projectessence.gameserver.model.items.instance.ItemInstance;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

public class PrivateStoreHistoryManager {
	private static final Logger LOGGER = Logger.getLogger(PrivateStoreHistoryManager.class.getName());

	private static final String SELECT = "SELECT * FROM items_transactions_history";
	private static final String INSERT = "INSERT INTO items_transactions_history (created_time,item_id,transaction_type,enchant_level,price,count) VALUES (?,?,?,?,?,?)";
	private static final String TRUNCATE = "TRUNCATE TABLE items_transactions_history";

	private static final ArrayList<ItemHistoryTransaction> _items = new ArrayList<>();

	public static PrivateStoreHistoryManager getInstance() {
		return PrivateStoreHistoryManager.SingletonHolder.INSTANCE;
	}

	public void registerTransaction(PrivateStoreType transactionType, ItemInstance item, long count, long price) {
		try {
			final ItemHistoryTransaction historyItem = new ItemHistoryTransaction(transactionType, count, price, item);
			_items.add(historyItem);
		} catch (Exception e) {
			LOGGER.log(Level.WARNING, "could not store history for item: " + item, e);
		}
	}

	public void restore() {
		_items.clear();
		try (Connection con = DatabaseFactory.getConnection();
		     PreparedStatement statement = con.prepareStatement(SELECT)) {
			try (ResultSet rs = statement.executeQuery()) {
				while (rs.next()) {
					final ItemHistoryTransaction item = new ItemHistoryTransaction(rs);
					_items.add(item);
				}
			}
		} catch (Exception e) {
			LOGGER.log(Level.WARNING, "could not restore history", e);
		}

		LOGGER.info(getClass().getSimpleName() + ": Loaded " + _items.size() + " items history.");
	}

	public void reset() {
		_items.clear();
		try (Connection con = DatabaseFactory.getConnection();
		     PreparedStatement statement = con.prepareStatement(TRUNCATE)) {
			statement.execute();
		} catch (Exception e) {
			LOGGER.log(Level.WARNING, "could not reset history", e);
		}

		LOGGER.info(getClass().getSimpleName() + ": weekly reset.");
	}

	public ArrayList<ItemHistoryTransaction> getHistory() {
		return getHistory(false);
	}

	public ArrayList<ItemHistoryTransaction> getHistory(boolean full) {
		if (!full) {
			ArrayList<ItemHistoryTransaction> list = new ArrayList<>(_items);
			Map<Integer, Integer> uniqueItemIds = list.stream().map(transaction -> transaction.getItemId()).collect(Collectors.toSet()).stream().collect(Collectors.toMap(Integer::intValue, e -> 0));
			list.sort(new SortByDate());
			ArrayList<ItemHistoryTransaction> finalList = new ArrayList<>();
			for (ItemHistoryTransaction transaction : list) {
				if (uniqueItemIds.get(transaction.getItemId()) < Config.STORE_HISTORY_LIMIT) {
					finalList.add(transaction);
					uniqueItemIds.put(transaction.getItemId(), uniqueItemIds.get(transaction.getItemId()) + 1);
				}
			}
			return finalList;
		}
		return _items;
	}

	public LinkedList<ItemHistoryTransaction> getTopHighestItem() {
		LinkedList<ItemHistoryTransaction> list = new LinkedList<>(_items);
		list.sort(new SortByPrice());
		return list;
	}

	public LinkedList<ItemHistoryTransaction> getTopMostItem() {
		final Map<Integer, ItemHistoryTransaction> map = new HashMap<>();
		for (ItemHistoryTransaction transaction : _items) {
			if (map.get(transaction.getItemId()) == null) {
				map.put(transaction.getItemId(), new ItemHistoryTransaction(transaction.getTransactionType(), transaction.getCount(), transaction.getPrice(), transaction.getItemId(), 0, false));
			} else {
				map.get(transaction.getItemId()).addCount(transaction.getCount());
			}
		}

		LinkedList<ItemHistoryTransaction> list = new LinkedList<>();
		map.forEach((itemID, transaction) -> list.add(transaction));
		list.sort(new SortByQuantity());

		return list;
	}

	private static class SortByPrice implements Comparator<ItemHistoryTransaction> {
		@Override
		public int compare(ItemHistoryTransaction a, ItemHistoryTransaction b) {
			return a.getPrice() > b.getPrice() ? -1 : a.getPrice() == b.getPrice() ? 0 : 1;
		}
	}

	private static class SortByQuantity implements Comparator<ItemHistoryTransaction> {
		@Override
		public int compare(ItemHistoryTransaction a, ItemHistoryTransaction b) {
			return a.getCount() > b.getCount() ? -1 : a.getCount() == b.getCount() ? 0 : 1;
		}
	}

	private static class SortByDate implements Comparator<ItemHistoryTransaction> {
		@Override
		public int compare(ItemHistoryTransaction a, ItemHistoryTransaction b) {
			return a.getTransactionDate() > b.getTransactionDate() ? -1 : a.getTransactionDate() == b.getTransactionDate() ? 0 : 1;
		}
	}

	private static class SingletonHolder {
		protected static final PrivateStoreHistoryManager INSTANCE = new PrivateStoreHistoryManager();
	}

	public static class ItemHistoryTransaction {
		private final long _transactionDate;
		private final int _itemId;
		private final PrivateStoreType _transactionType;
		private final int _enchantLevel;
		private final long _price;
		private long _count;

		public ItemHistoryTransaction(ResultSet rs) throws SQLException {
			_transactionDate = rs.getLong("created_time");
			_itemId = rs.getInt("item_id");
			_transactionType = rs.getInt("transaction_type") == 0x00 ? PrivateStoreType.SELL : PrivateStoreType.BUY;
			_enchantLevel = rs.getInt("enchant_level");
			_price = rs.getLong("price");
			_count = rs.getLong("count");
		}

		private ItemHistoryTransaction(PrivateStoreType transactionType, long count, long price, ItemInstance item) {
			this(transactionType, count, price, item.getId(), item.getEnchantLevel(), true);
		}

		public ItemHistoryTransaction(PrivateStoreType transactionType, long count, long price, int itemId, int enchantLevel, boolean saveToDB) {
			_transactionDate = System.currentTimeMillis();
			_itemId = itemId;
			_transactionType = transactionType;
			_enchantLevel = enchantLevel;
			_price = price;
			_count = count;

			if (saveToDB) {
				storeInDB();
			}
		}

		public long getTransactionDate() {
			return _transactionDate;
		}

		public PrivateStoreType getTransactionType() {
			return _transactionType;
		}

		public int getItemId() {
			return _itemId;
		}

		public int getEnchantLevel() {
			return _enchantLevel;
		}

		public long getPrice() {
			return _price;
		}

		public long getCount() {
			return _count;
		}

		public void addCount(long count) {
			_count += count;
		}

		private void storeInDB() {
			try (Connection con = DatabaseFactory.getConnection();
			     PreparedStatement ps = con.prepareStatement(INSERT)) {
				ps.setLong(1, _transactionDate);
				ps.setInt(2, _itemId);
				ps.setInt(3, _transactionType == PrivateStoreType.SELL ? 0x00 : 0x01);
				ps.setInt(4, _enchantLevel);
				ps.setLong(5, _price);
				ps.setLong(6, _count);

				ps.executeQuery();
			} catch (Exception e) {
				LOGGER.log(Level.SEVERE, "Could not insert history item " + this + " into DB: Reason: " + e.getMessage(), e);
			}
		}

		@Override
		public String toString() {
			return _transactionDate + "(" + _transactionType + ")" + "[" + _itemId + " +" + _enchantLevel + " c:" + _count + " p:" + _price + " ]";
		}
	}
}
