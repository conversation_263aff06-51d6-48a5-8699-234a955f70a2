package club.projectessence.gameserver.instancemanager;

import java.util.Comparator;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Map.Entry;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

import club.projectessence.Config;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;

public class BattleWithBalokManager
{
	private final Map<Integer, Integer>	_playerPoints	= new ConcurrentHashMap<>();
	private boolean						_inBattle		= false;
	private int							_reward			= 0;
	private int							_globalPoints	= 0;
	private int							_globalStage	= 0;
	private int							_globalStatus	= 0;
	
	public BattleWithBalokManager()
	{}
	
	public void addPointsForPlayer(PlayerInstance player, boolean isScorpion)
	{
		final int pointsToAdd = isScorpion ? Config.BALOK_POINTS_PER_MONSTER * 10 : Config.BALOK_POINTS_PER_MONSTER;
		final int currentPoints = _playerPoints.computeIfAbsent(player.getObjectId(), pts -> 0);
		int sum = pointsToAdd + currentPoints;
		_playerPoints.put(player.getObjectId(), sum);
	}
	
	public Map<Integer, Integer> getTopPlayers(int count)
	{
		return _playerPoints.entrySet().stream().sorted(Entry.comparingByValue(Comparator.reverseOrder())).limit(count).collect(Collectors.toMap(Entry::getKey, Entry::getValue, (e1, e2) -> e1, LinkedHashMap::new));
	}
	
	public int getPlayerRank(PlayerInstance player)
	{
		if (!_playerPoints.containsKey(player.getObjectId()))
		{
			return 0;
		}
		final Map<Integer, Integer> sorted = _playerPoints.entrySet().stream().sorted(Entry.comparingByValue(Comparator.reverseOrder())).collect(Collectors.toMap(Entry::getKey, Entry::getValue, (e1, e2) -> e1, LinkedHashMap::new));
		return sorted.keySet().stream().toList().indexOf(player.getObjectId()) + 1;
	}
	
	public int getMonsterPoints(PlayerInstance player)
	{
		return _playerPoints.computeIfAbsent(player.getObjectId(), pts -> 0);
	}
	
	public int getReward()
	{
		return _reward;
	}
	
	public void setReward(int value)
	{
		_reward = value;
	}
	
	public boolean getInBattle()
	{
		return _inBattle;
	}
	
	public void setInBattle(boolean value)
	{
		_inBattle = value;
	}
	
	public int getGlobalPoints()
	{
		return _globalPoints;
	}
	
	public void setGlobalPoints(int value)
	{
		_globalPoints = value;
	}
	
	public int getGlobalStage()
	{
		return _globalStage;
	}
	
	public void setGlobalStage(int value)
	{
		_globalStage = value;
	}
	
	public int getGlobalStatus()
	{
		return _globalStatus;
	}
	
	public void setGlobalStatus(int value)
	{
		_globalStatus = value;
	}
	
	public static BattleWithBalokManager getInstance()
	{
		return SingletonHolder.INSTANCE;
	}
	
	private static class SingletonHolder
	{
		protected static final BattleWithBalokManager INSTANCE = new BattleWithBalokManager();
	}
}
