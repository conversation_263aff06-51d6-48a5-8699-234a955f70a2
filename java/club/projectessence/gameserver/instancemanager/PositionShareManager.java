/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.instancemanager;

import club.projectessence.Config;
import club.projectessence.gameserver.enums.ChatType;
import club.projectessence.gameserver.model.Location;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.model.effects.EffectFlag;
import club.projectessence.gameserver.model.itemcontainer.Inventory;
import club.projectessence.gameserver.model.items.instance.ItemInstance;
import club.projectessence.gameserver.model.skills.CommonSkill;
import club.projectessence.gameserver.model.zone.ZoneId;
import club.projectessence.gameserver.model.zone.type.SiegeZone;
import club.projectessence.gameserver.network.SystemMessageId;
import club.projectessence.gameserver.network.serverpackets.ActionFailed;
import club.projectessence.gameserver.network.serverpackets.ExShowScreenMessage;
import club.projectessence.gameserver.network.serverpackets.SystemMessage;
import club.projectessence.gameserver.taskmanager.autoplay.AutoPlayTaskManager;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 */
public class PositionShareManager {

	private final Map<Integer, SharedPosition> _positions = new ConcurrentHashMap<>();

	protected PositionShareManager() {
	}

	public static PositionShareManager getInstance() {
		return SingletonHolder.INSTANCE;
	}

	public int getSharePositionPrice(PlayerInstance player) {
		int price = Config.SHARE_POSITION_CREATE_PRICE;
		int pvpRank = RankManager.getInstance().getPlayerRealPvpRank(player.getObjectId());
		if ((pvpRank >= 1) && (pvpRank <= 3)) {
			price = 0;
		}
		return price;
	}

	public int sharePosition(PlayerInstance player, ChatType chatType) {
		if (player.isDead() || player.isTeleporting()) {
			return 0;
		}
		int price = getSharePositionPrice(player);
		ItemInstance lcoin = player.getInventory().getItemByItemId(Inventory.LCOIN_ID);
		if ((price > 0) && ((lcoin == null) || (lcoin.getCount() < price))) {
			return 0;
		}
		if (player.isInsideZone(ZoneId.PEACE) || player.isInInstance() || player.isInSpecialHuntingZone() || player.isInsideZone(ZoneId.SIEGE)) {
			player.sendPacket(new SystemMessage(SystemMessageId.YOU_CAN_T_SHARE_YOUR_LOCATION_IN_THE_CURRENT_STATE));
			return 0;
		}
		if (((chatType == ChatType.PARTYROOM_COMMANDER) || (chatType == ChatType.PARTYMATCH_ROOM)) && //
				((player.getParty() != null) && (player.getParty().getLeader() == player))) {
			player.sendPacket(new SystemMessage(SystemMessageId.YOU_ARE_NOT_THE_LEADER_OF_A_COMMAND_CHANNEL_OR_PARTY_YOU_CAN_T_SHARE_YOUR_LOCATION_IN_THE_COMMAND_CHANNEL));
			return 0;
		}
		if (ZoneManager.getInstance().getZone(player, SiegeZone.class) != null) {
			player.sendPacket(SystemMessageId.YOU_CANNOT_TELEPORT_RIGHT_NOW);
			return 0;
		}
		// Monastery of Silence upstairs
		if (ZoneManager.getInstance().getZoneById(80058).getAllPlayersInside().contains(player)) {
			player.sendPacket(new SystemMessage(SystemMessageId.YOU_CAN_T_SHARE_YOUR_LOCATION_IN_THE_CURRENT_STATE));
			return 0;
		}
		// Fairy Settlement
//		if (ZoneManager.getInstance().getZoneById(80059).getAllPlayersInside().contains(player)) {
//			player.sendPacket(new SystemMessage(SystemMessageId.YOU_CAN_T_SHARE_YOUR_LOCATION_IN_THE_CURRENT_STATE));
//			return 0;
//		}
		// Morgos Military Base inside
		if (ZoneManager.getInstance().getZoneById(49999).getAllPlayersInside().contains(player)) {
			player.sendPacket(new SystemMessage(SystemMessageId.YOU_CAN_T_SHARE_YOUR_LOCATION_IN_THE_CURRENT_STATE));
			return 0;
		}
		// Orc barracks inside
		if (ZoneManager.getInstance().getZoneById(50000).getAllPlayersInside().contains(player)) {
			player.sendPacket(new SystemMessage(SystemMessageId.YOU_CAN_T_SHARE_YOUR_LOCATION_IN_THE_CURRENT_STATE));
			return 0;
		}
		// Orc Fortress
		if (ZoneManager.getInstance().getZoneById(50001).getAllPlayersInside().contains(player)) {
			player.sendPacket(new SystemMessage(SystemMessageId.YOU_CAN_T_SHARE_YOUR_LOCATION_IN_THE_CURRENT_STATE));
			return 0;
		}
		// Hellbound
		if (ZoneManager.getInstance().getZoneById(50002).getAllPlayersInside().contains(player)) {
			// Steel Citadel Floor 5 & Floor 10
			if (!ZoneManager.getInstance().getZoneById(10000).getAllPlayersInside().contains(player) && //
					!ZoneManager.getInstance().getZoneById(10001).getAllPlayersInside().contains(player)) {
				player.sendPacket(new SystemMessage(SystemMessageId.YOU_CAN_T_SHARE_YOUR_LOCATION_IN_THE_CURRENT_STATE));
				return 0;
			}
		}
		// Cruma Tower
		if (ZoneManager.getInstance().getZoneById(60000).getAllPlayersInside().contains(player)) {
			player.sendPacket(new SystemMessage(SystemMessageId.YOU_CAN_T_SHARE_YOUR_LOCATION_IN_THE_CURRENT_STATE));
			return 0;
		}
		// Jisra Kuka
		if (ZoneManager.getInstance().getZoneById(50003).isCharacterInZone(player)) {
			player.sendPacket(new SystemMessage(SystemMessageId.YOU_CAN_T_SHARE_YOUR_LOCATION_IN_THE_CURRENT_STATE));
			return 0;
		}
		// Antharas's Nest
		if (ZoneManager.getInstance().getZoneById(70050).getAllPlayersInside().contains(player)) {
			player.sendPacket(new SystemMessage(SystemMessageId.YOU_CAN_T_SHARE_YOUR_LOCATION_IN_THE_CURRENT_STATE));
			return 0;
		}

		if (ZoneManager.getInstance().getZoneByName("heavenly_rift_1").getAllPlayersInside().contains(player) //
				|| ZoneManager.getInstance().getZoneByName("heavenly_rift_2").getAllPlayersInside().contains(player) //
				|| ZoneManager.getInstance().getZoneByName("heavenly_rift_3").getAllPlayersInside().contains(player)) {
			player.sendPacket(new SystemMessage(SystemMessageId.YOU_CAN_T_SHARE_YOUR_LOCATION_IN_THE_CURRENT_STATE));
			return 0;
		}
		if (price > 0) {
			player.destroyItemByItemId("Share Position Create", lcoin.getId(), price, player, true);
		}
		int newId = _positions.size() + 1;
		_positions.put(newId, new SharedPosition(player, newId, chatType, player.getLocation()));
		return newId;
	}

	public void tryToTeleport(PlayerInstance player, int id) {
		SharedPosition sharedPosition = _positions.get(id);
		if (sharedPosition != null) {
			sharedPosition.teleport(player);
		}
	}

	public SharedPosition getSharedPosition(int id) {
		return _positions.get(id);
	}

	public void detachPlayer(int objId) {
		for (SharedPosition pos : _positions.values()) {
			if ((pos.getPlayer() != null) && (pos.getPlayer().getObjectId() == objId)) {
				pos.detachPlayer();
			}
		}
	}

	private static class SingletonHolder {
		protected static final PositionShareManager INSTANCE = new PositionShareManager();
	}

	public class SharedPosition {
		private final String _playerName;
		private final int _id;
		private final ChatType _chatType;
		private final Location _location;
		private PlayerInstance _player;
		private int _teleportsLeft;

		protected SharedPosition(PlayerInstance player, int id, ChatType chatType, Location location) {
			_player = player;
			_playerName = player.getName();
			_id = id;
			_chatType = chatType;
			_location = location;
			_teleportsLeft = 20;
		}

		public void teleport(PlayerInstance player) {
			if (canTeleport(player)) {
				_teleportsLeft--;

				//player.teleToLocation(_location);
				player.stopMove(null);
				AutoPlayTaskManager.getInstance().stopAutoPlay(player);
				player.setTeleportLoc(_location);
				if (player.isCastingNow()) {
					player.setQueuedSkill(CommonSkill.TELEPORT.getSkill(), null, false, false);
				} else {
					player.doCast(CommonSkill.TELEPORT.getSkill());
				}

				player.destroyItemByItemId("Share Position Teleport", Inventory.LCOIN_ID, Config.SHARE_POSITION_TELEPORT_PRICE, player, true);
			} else {
				SystemMessage sm = new SystemMessage(SystemMessageId.S1_CANNOT_BE_USED_DUE_TO_UNSUITABLE_TERMS);
				sm.addString("Teleportation");
				player.sendPacket(sm);
			}
		}

		public void detachPlayer() {
			_player = null;
		}

		public PlayerInstance getPlayer() {
			return _player;
		}

		public String getPlayerName() {
			return _playerName;
		}

		public int getId() {
			return _id;
		}

		public Location getLocation() {
			return _location;
		}

		public int getTeleportsLeft() {
			return _teleportsLeft;
		}

		public boolean canTeleport(PlayerInstance player) {
			if (_teleportsLeft <= 0) {
				return false;
			}

			if (Config.ONLY_CHARACTER_CREATE && !player.isGM()) {
				// player.sendMessage("Server has not started yet!");
				player.sendPacket(ActionFailed.STATIC_PACKET);
				player.sendPacket(new ExShowScreenMessage("Server has not started yet!", 2, 2000, 0, true, false));
				// Disconnection.of(client).defaultSequence(true);
				return false;
			}

			if (player.isDead()) {
				player.sendPacket(SystemMessageId.YOU_CANNOT_USE_TELEPORT_WHILE_YOU_ARE_DEAD);
				return false;
			}

			if (player.isInCombat()) // Custom
			{
				player.sendPacket(SystemMessageId.YOU_CANNOT_TELEPORT_RIGHT_NOW);
				return false;
			}

			if (ZoneManager.getInstance().getZone(_location, SiegeZone.class) != null) {
				player.sendPacket(SystemMessageId.YOU_CANNOT_TELEPORT_RIGHT_NOW);
				return false;
			}

			if (player.isInsideZone(ZoneId.UNDERGROUND_LABYRINTH)) {
				player.sendPacket(SystemMessageId.THHE_FUNCTION_IS_NOT_AVAILABLE_IN_THE_UNDERGROUND_LABYRINTH);
				return false;
			}

			if (player.isMovementDisabled() || player.cannotEscape() || player.hasBlockActions() || player.isAffected(EffectFlag.FEAR)) {
				return false;
			}

			if (player.isInOlympiadMode()) {
				player.sendPacket(SystemMessageId.YOU_CANNOT_USE_MY_TELEPORTS_WHILE_PARTICIPATING_IN_AN_OLYMPIAD_MATCH);
				return false;
			}

			if (player.isInInstance()) {
				player.sendPacket(SystemMessageId.YOU_CANNOT_USE_TELEPORT_IN_AN_INSTANCE_ZONE);
				return false;
			}

			ItemInstance lcoin = player.getInventory().getItemByItemId(Inventory.LCOIN_ID);
			if ((Config.SHARE_POSITION_TELEPORT_PRICE > 0) && ((lcoin == null) || (lcoin.getCount() < Config.SHARE_POSITION_TELEPORT_PRICE))) {
				return false;
			}

			if ((_player == null) || (_player == player)) {
				return true;
			}

			switch (_chatType) {
				case GENERAL:
				case SHOUT:
				case WORLD:
				case TRADE:
				case ANNOUNCEMENT:
				case CRITICAL_ANNOUNCE:
				case SCREEN_ANNOUNCE:
				case GM:
				case HERO_VOICE: {
					return true;
				}
				case PARTYMATCH_ROOM:
				case PARTYROOM_COMMANDER: {

					if (player.isInCommandChannel() && _player.isInCommandChannel() && (player.getCommandChannel() == _player.getCommandChannel())) {
						return true;
					}
					return false;
				}
				case PARTY: {
					if (player.isInParty() && _player.isInParty() && (player.getParty() == _player.getParty())) {
						return true;
					}
					return false;
				}
				case CLAN: {
					if ((player.getClan() != null) && (_player.getClan() != null) && (player.getClan() == _player.getClan())) {
						return true;
					}
					return false;
				}
				case ALLIANCE: {
					if ((player.getAllyId() != 0) && (_player.getAllyId() != 0) && (player.getAllyId() == _player.getAllyId())) {
						return true;
					}
					return false;
				}
				default: {
					return false;
				}
			}
		}
	}
}
