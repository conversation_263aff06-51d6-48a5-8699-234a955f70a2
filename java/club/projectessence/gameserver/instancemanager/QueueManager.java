/*
 * This file is part of the L2J Mobius project.
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.instancemanager;

import java.util.ArrayList;
import java.util.List;
import java.util.Queue;
import java.util.concurrent.ConcurrentLinkedQueue;

import club.projectessence.Config;
import club.projectessence.commons.concurrent.ThreadPool;
import club.projectessence.gameserver.Shutdown;
import club.projectessence.gameserver.cache.HtmCache;
import club.projectessence.gameserver.data.sql.CharNameTable;
import club.projectessence.gameserver.model.World;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.model.events.Containers;
import club.projectessence.gameserver.model.events.EventDispatcher;
import club.projectessence.gameserver.model.events.impl.creature.player.OnPlayerSelect;
import club.projectessence.gameserver.model.events.returns.TerminateReturn;
import club.projectessence.gameserver.network.ConnectionState;
import club.projectessence.gameserver.network.Disconnection;
import club.projectessence.gameserver.network.GameClient;
import club.projectessence.gameserver.network.serverpackets.CharSelected;
import club.projectessence.gameserver.network.serverpackets.NpcHtmlMessage;

/**
 * <AUTHOR>
 */
public class QueueManager
{
	private final Queue<GameClient>	_premiumQueue	= new ConcurrentLinkedQueue<>();
	private final Queue<GameClient>	_normalQueue	= new ConcurrentLinkedQueue<>();
	
	private QueueManager()
	{
		ThreadPool.get().scheduleAtFixedRate(this::queueTask, 15_000, 15_000);
	}
	
	public static QueueManager getInstance()
	{
		return SingletonHolder.INSTANCE;
	}
	
	private void queueTask()
	{
		if (Shutdown.IS_SHUTDOWNING)
		{
			return;
		}
		int playerCount = World.getInstance().getPlayers().size();
		if ((_premiumQueue.isEmpty() && _normalQueue.isEmpty()) || (playerCount >= Config.MAXIMUM_ONLINE_USERS_QUEUE))
		{
			return;
		}
		int newPlayers = 0;
		List<GameClient> joined = new ArrayList<>();
		for (GameClient client : _premiumQueue)
		{
			if ((playerCount + newPlayers) >= Config.MAXIMUM_ONLINE_USERS_QUEUE)
			{
				break;
			}
			joined.add(client);
			login(client);
			newPlayers++;
		}
		for (GameClient client : _normalQueue)
		{
			if ((playerCount + newPlayers) >= Config.MAXIMUM_ONLINE_USERS_QUEUE)
			{
				break;
			}
			joined.add(client);
			login(client);
			newPlayers++;
		}
		for (GameClient client : joined)
		{
			removeFromQueue(client);
		}
		sendQueueDialog();
	}
	
	private void sendQueueDialog()
	{
		final int premiumQueueSize = _premiumQueue.size();
		final int normalQueueSize = _normalQueue.size();
		int queuePlace = 0;
		for (GameClient client : _premiumQueue)
		{
			queuePlace++;
			String html = HtmCache.getInstance().getHtm(null, "data/html/queue.htm");
			if (html != null)
			{
				html = html.replace("%premiumQueue%", String.valueOf(premiumQueueSize));
				html = html.replace("%normalQueue%", String.valueOf(normalQueueSize));
				html = html.replace("%yourQueue%", "Premium");
				html = html.replace("%queuePlace%", String.valueOf(queuePlace));
				client.sendPacket(new NpcHtmlMessage(html));
			}
		}
		for (GameClient client : _normalQueue)
		{
			queuePlace++;
			String html = HtmCache.getInstance().getHtm(null, "data/html/queue.htm");
			if (html != null)
			{
				html = html.replace("%premiumQueue%", String.valueOf(premiumQueueSize));
				html = html.replace("%normalQueue%", String.valueOf(normalQueueSize));
				html = html.replace("%yourQueue%", "Normal");
				html = html.replace("%queuePlace%", String.valueOf(queuePlace));
				client.sendPacket(new NpcHtmlMessage(html));
			}
		}
	}
	
	public boolean isInQueue(GameClient client)
	{
		return _premiumQueue.contains(client) || _normalQueue.contains(client);
	}
	
	public void addToQueue(GameClient client)
	{
		if (isInQueue(client))
		{
			return;
		}
		if (client.canSkipQueue() || ((World.getInstance().getPlayers().size() < Config.MAXIMUM_ONLINE_USERS_QUEUE) && _premiumQueue.isEmpty() && _normalQueue.isEmpty()))
		{
			login(client);
		}
		else if (!_premiumQueue.contains(client) && !_normalQueue.contains(client))
		{
			PlayerInstance cha = client.load(client.getCharSlot());
			if (cha == null)
			{
				_normalQueue.add(client);
				sendQueueDialog();
				return;
			}
			// Lấy charId của nhân vật
			int charId = cha.getObjectId();
			// Đóng nhân vật tạm thời vì chúng ta chỉ cần charId
			cha = null;
			client.detachCharactersInfo();
			// Kiểm tra trạng thái Premium của nhân vật
			PremiumManager.getInstance().loadPremiumData(charId);
			if (PremiumManager.getInstance().getPremiumExpiration(charId) > System.currentTimeMillis())
			{
				_premiumQueue.add(client);
			}
			else
			{
				_normalQueue.add(client);
			}
			sendQueueDialog();
		}
	}
	
	public void removeFromQueue(GameClient client)
	{
		_premiumQueue.remove(client);
		_normalQueue.remove(client);
	}
	
	private void login(GameClient client)
	{
		// load up character from disk
		final PlayerInstance cha = client.load(client.getCharSlot());
		if (cha == null)
		{
			return; // handled in GameClient
		}
		CharNameTable.getInstance().addName(cha);
		cha.setClient(client);
		client.setPlayer(cha);
		cha.setOnlineStatus(true, true);
		final TerminateReturn terminate = EventDispatcher.getInstance().notifyEvent(new OnPlayerSelect(cha, cha.getObjectId(), cha.getName(), client), Containers.Players(), TerminateReturn.class);
		if ((terminate != null) && terminate.terminate())
		{
			Disconnection.of(cha).logout(false, false);
			return;
		}
		client.setCanSkipQueue(true);
		client.setConnectionState(ConnectionState.ENTERING);
		client.sendPacket(new CharSelected(cha, client.getSessionKey().playOkID1));
		client.detachCharactersInfo();
	}
	
	private static class SingletonHolder
	{
		protected static final QueueManager INSTANCE = new QueueManager();
	}
}
