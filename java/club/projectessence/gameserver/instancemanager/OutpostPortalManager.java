package club.projectessence.gameserver.instancemanager;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.concurrent.ConcurrentHashMap;
import java.util.logging.Logger;
import java.util.stream.Collectors;

import club.projectessence.Config;
import club.projectessence.gameserver.data.xml.OutpostData;
import club.projectessence.gameserver.enums.Faction;
import club.projectessence.gameserver.model.Location;
import club.projectessence.gameserver.model.actor.instance.OutpostInstance;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.model.zone.type.FactionZone;
import club.projectessence.gameserver.network.serverpackets.NpcHtmlMessage;

public class OutpostPortalManager
{
	private static final Logger					LOGGER					= Logger.getLogger(OutpostPortalManager.class.getName());
	private static final OutpostPortalManager	_instance				= new OutpostPortalManager();
	private final Map<Integer, OutpostInstance>	_outposts				= new ConcurrentHashMap<>();
	private static final Random					RANDOM					= new Random();
	private static final int					MAX_PLAYERS_PER_ZONE	= Config.MAX_PLAYERS_PER_ZONE;
	private static final int					RANDOM_RADIUS			= Config.TELEPORT_RANDOM_RADIUS;
	
	public static OutpostPortalManager getInstance()
	{
		return _instance;
	}
	
	public void addOutpost(OutpostInstance outpost)
	{
		_outposts.put(outpost.getObjectId(), outpost);
		if (Config.LOG_FACTION_DETAILS)
		{
			LOGGER.info("Added Outpost ID " + outpost.getObjectId() + " to OutpostPortalManager with controlling faction: " + outpost.getControllingFaction());
		}
	}
	
	public void removeOutpost(OutpostInstance outpost)
	{
		_outposts.remove(outpost.getObjectId());
		if (Config.LOG_FACTION_DETAILS)
		{
			LOGGER.info("Removed Outpost ID " + outpost.getObjectId() + " from OutpostPortalManager");
		}
	}
	
	public void showTeleportWindow(PlayerInstance player)
	{
		Faction playerFaction = player.getFaction();
		if (playerFaction == Faction.NONE)
		{
			player.sendMessage("You must join a faction to use this portal!");
			return;
		}
		StringBuilder sb = new StringBuilder();
		sb.append("<html><body>");
		sb.append("<center><font color=\"LEVEL\">Teleport to Controlled Zones</font></center><br>");
		// Lấy danh sách tất cả các zone do faction của người chơi kiểm soát (bất kể trạng thái)
		List<String> controlledZones = ZoneManager.getInstance().getAllZones(FactionZone.class).stream().filter(zone -> zone.getControllingFaction() == playerFaction).map(FactionZone::getName).collect(Collectors.toList());
		List<String> attackedZones = new ArrayList<>();
		for (String zoneName : controlledZones)
		{
			FactionZone zone = ZoneManager.getInstance().getZoneByName(zoneName, FactionZone.class);
			if (zone != null && zone.isUnderAttack())
			{
				attackedZones.add(zoneName);
			}
		}
		// Sắp xếp: Ưu tiên zone bị tấn công, sau đó theo GveZoneType
		controlledZones.sort(Comparator.comparingInt(zoneName ->
		{
			FactionZone zone = ZoneManager.getInstance().getZoneByName(zoneName, FactionZone.class);
			int attackedPriority = attackedZones.contains(zoneName) ? 0 : 1;
			if (zone == null)
			{
				return attackedPriority * 100 + 4;
			}
			int typePriority;
			switch (zone.getZoneType().getValue())
			{
				case "GVE_LOW":
					typePriority = 1;
					break;
				case "GVE_MID":
					typePriority = 2;
					break;
				case "GVE_HIGH":
					typePriority = 3;
					break;
				default:
					typePriority = 4;
					break;
			}
			return attackedPriority * 100 + typePriority;
		}));
		// Thêm thông báo nếu có zone bị tấn công
		if (!attackedZones.isEmpty())
		{
			String nearestAttackedZone = attackedZones.stream().min(Comparator.comparingDouble(zoneName ->
			{
				Location loc = OutpostData.getInstance().getMainLocation(zoneName);
				if (loc == null)
					return Double.MAX_VALUE;
				double dx = loc.getX() - player.getX();
				double dy = loc.getY() - player.getY();
				double dz = loc.getZ() - player.getZ();
				return Math.sqrt(dx * dx + dy * dy + dz * dz);
			})).orElse(attackedZones.get(0));
			sb.append("<center><font color=\"FF0000\">Zone under attack: " + OutpostData.getInstance().getOutpostName(nearestAttackedZone) + ". Open Community Board to teleport!</font></center><br>");
		}
		sb.append("Select a zone controlled by your faction to teleport to:<br>");
		if (controlledZones.isEmpty())
		{
			sb.append("Your faction does not control any zones at the moment.<br>");
		}
		else
		{
			for (String zoneName : controlledZones)
			{
				FactionZone zone = ZoneManager.getInstance().getZoneByName(zoneName, FactionZone.class);
				if (zone == null)
					continue;
				String displayName = OutpostData.getInstance().getOutpostName(zoneName) + " (" + playerFaction + ")";
				sb.append("<a action=\"bypass -h npc_").append(player.getTarget().getObjectId()).append("_teleport_to_zone ").append(zoneName).append("\">").append(displayName);
				if (zone.isUnderAttack())
				{
					sb.append(" (Under Attack)");
				}
				sb.append(" [").append(zone.getZoneType().getValue()).append("]");
				List<PlayerInstance> playersInZone = FactionZoneManager.getInstance().getPlayersInZone(zoneName);
				long fireCount = playersInZone.stream().filter(p -> p.getFaction() == Faction.FIRE).count();
				long waterCount = playersInZone.stream().filter(p -> p.getFaction() == Faction.WATER).count();
				sb.append(" - Players: ").append(playersInZone.size()).append(" (FIRE: ").append(fireCount).append(", WATER: ").append(waterCount).append(")");
				sb.append("</a><br>");
			}
		}
		sb.append("<br><button value=\"Back\" action=\"bypass -h npc_").append(player.getTarget().getObjectId()).append("_chat_0\" width=80 height=26 back=\"L2UI_CT1.Button_DF_Down\" fore=\"L2UI_CT1.Button_DF\">");
		sb.append("</center></body></html>");
		NpcHtmlMessage html = new NpcHtmlMessage(player.getTarget().getObjectId());
		html.setHtml(sb.toString());
		player.sendPacket(html);
		if (!attackedZones.isEmpty())
		{
			StringBuilder attackedMessage = new StringBuilder("The following zones controlled by your faction are under attack:\n");
			for (String zoneName : attackedZones)
			{
				attackedMessage.append("- ").append(OutpostData.getInstance().getOutpostName(zoneName)).append(" [").append(ZoneManager.getInstance().getZoneByName(zoneName, FactionZone.class).getZoneType().getValue()).append("]\n");
			}
			attackedMessage.append("Please teleport to defend them!");
			player.sendMessage(attackedMessage.toString());
		}
	}
	
	public void teleportToZone(PlayerInstance player, String zoneName)
	{
		FactionZone zone = ZoneManager.getInstance().getZoneByName(zoneName, FactionZone.class);
		if (zone == null)
		{
			player.sendMessage("Zone not found!");
			LOGGER.warning("Player " + player.getName() + " attempted to teleport to non-existent zone: " + zoneName);
			return;
		}
		Faction playerFaction = player.getFaction();
		Faction zoneFaction = FactionZoneManager.getInstance().getZoneFaction(zoneName);
		if (zoneFaction == Faction.NONE)
		{
			player.sendMessage("This zone is not controlled by any faction yet!");
			if (Config.LOG_FACTION_DETAILS)
			{
				LOGGER.info("Player " + player.getName() + " attempted to teleport to neutral zone " + zoneName);
			}
			return;
		}
		if (Config.LOG_FACTION_DETAILS)
		{
			LOGGER.info("Player " + player.getName() + " (Faction: " + playerFaction + ") attempting to teleport to zone " + zoneName + " (Faction: " + zoneFaction + ")");
		}
		if (playerFaction != zoneFaction)
		{
			player.sendMessage("You cannot teleport to " + zone.getInGameName() + " because it is currently controlled by the " + zoneFaction + " faction!");
			LOGGER.warning("Player " + player.getName() + " (Faction: " + playerFaction + ") attempted to teleport to enemy zone " + zoneName + " (Faction: " + zoneFaction + ")");
			return;
		}
		List<PlayerInstance> playersInZone = FactionZoneManager.getInstance().getPlayersInZone(zoneName);
		if (playersInZone.size() >= MAX_PLAYERS_PER_ZONE)
		{
			player.sendMessage("The zone " + zone.getInGameName() + " is full! Cannot teleport at this time.");
			if (Config.LOG_FACTION_DETAILS)
			{
				LOGGER.info("Player " + player.getName() + " failed to teleport to zone " + zoneName + " because it is full (current players: " + playersInZone.size() + ")");
			}
			return;
		}
		Location loc = OutpostData.getInstance().getMainLocation(zoneName);
		if (loc == null)
		{
			player.sendMessage("Teleport location not set for this zone!");
			return;
		}
		int baseX = loc.getX();
		int baseY = loc.getY();
		int baseZ = loc.getZ();
		int randomX = baseX + (RANDOM.nextInt(RANDOM_RADIUS * 2) - RANDOM_RADIUS);
		int randomY = baseY + (RANDOM.nextInt(RANDOM_RADIUS * 2) - RANDOM_RADIUS);
		player.teleToLocation(randomX, randomY, baseZ);
		if (Config.LOG_FACTION_DETAILS)
		{
			LOGGER.info("Player " + player.getName() + " teleported to " + zone.getInGameName() + " [Type: " + zone.getZoneType().getValue() + "] at " + randomX + "," + randomY + "," + baseZ);
		}
		long fireCount = playersInZone.stream().filter(p -> p.getFaction() == Faction.FIRE).count();
		long waterCount = playersInZone.stream().filter(p -> p.getFaction() == Faction.WATER).count();
		player.sendMessage("You have teleported to " + zone.getInGameName() + " [Type: " + zone.getZoneType().getValue() + "]. Current players: " + playersInZone.size() + " (FIRE: " + fireCount + ", WATER: " + waterCount + ")");
		if (zone.isUnderAttack())
		{
			player.sendMessage("The zone " + zone.getInGameName() + " is under attack! Prepare to defend!");
		}
	}
}