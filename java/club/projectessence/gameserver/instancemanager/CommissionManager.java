/*
 * This file is part of the L2J Mobius project.
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.instancemanager;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.sql.Timestamp;
import java.time.Duration;
import java.time.Instant;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ScheduledFuture;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

import club.projectessence.Config;
import club.projectessence.commons.concurrent.ThreadPool;
import club.projectessence.commons.database.DatabaseFactory;
import club.projectessence.gameserver.data.xml.CollectionData;
import club.projectessence.gameserver.data.xml.EnsoulData;
import club.projectessence.gameserver.data.xml.HennaData;
import club.projectessence.gameserver.data.xml.VariationData;
import club.projectessence.gameserver.enums.ItemGrade;
import club.projectessence.gameserver.enums.ItemLocation;
import club.projectessence.gameserver.enums.MailType;
import club.projectessence.gameserver.handler.IItemHandler;
import club.projectessence.gameserver.handler.ItemHandler;
import club.projectessence.gameserver.model.Message;
import club.projectessence.gameserver.model.World;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.model.commission.CommissionItem;
import club.projectessence.gameserver.model.holders.CollectionDataHolder;
import club.projectessence.gameserver.model.holders.ItemCollectionData;
import club.projectessence.gameserver.model.items.EtcItem;
import club.projectessence.gameserver.model.items.Item;
import club.projectessence.gameserver.model.items.instance.ItemInstance;
import club.projectessence.gameserver.network.SystemMessageId;
import club.projectessence.gameserver.network.serverpackets.ExSendUIEventCustom;
import club.projectessence.gameserver.network.serverpackets.commission.ExResponseCommissionBuyItem;
import club.projectessence.gameserver.network.serverpackets.commission.ExResponseCommissionDelete;
import club.projectessence.gameserver.network.serverpackets.commission.ExResponseCommissionInfo;
import club.projectessence.gameserver.network.serverpackets.commission.ExResponseCommissionList;
import club.projectessence.gameserver.network.serverpackets.commission.ExResponseCommissionList.CommissionListReplyType;
import club.projectessence.gameserver.network.serverpackets.commission.ExResponseCommissionRegister;

/**
 * <AUTHOR> Benetis
 */
public class CommissionManager
{
	private static final Logger				LOGGER							= Logger.getLogger(CommissionManager.class.getName());
	private static final int				ITEMS_LIMIT_PER_REQUEST			= 999;
	private static final int				MAX_ITEMS_REGISTRED_PER_PLAYER	= 10;
	private static final long				MIN_REGISTRATION_AND_SALE_FEE	= 1;
	private static final String				SELECT_ALL_ITEMS				= "SELECT * FROM `items` WHERE `loc` = ?";
	private static final String				SELECT_ALL_COMMISSION_ITEMS		= "SELECT * FROM `commission_items`";
	private static final String				INSERT_COMMISSION_ITEM			= "INSERT INTO `commission_items`(`item_object_id`, `price_per_unit`, `start_time`, `duration_in_days`) VALUES (?, ?, ?, ?)";
	private static final String				DELETE_COMMISSION_ITEM			= "DELETE FROM `commission_items` WHERE `commission_id` = ?";
	private final Map<Long, CommissionItem>	_commissionItems				= new ConcurrentHashMap<>();
	private static Logger					LOGGER_ITEMS					= Logger.getLogger("item");
	
	protected CommissionManager()
	{
		final Map<Integer, ItemInstance> itemInstances = new HashMap<>();
		try (Connection con = DatabaseFactory.getConnection())
		{
			try (PreparedStatement ps = con.prepareStatement(SELECT_ALL_ITEMS))
			{
				ps.setString(1, ItemLocation.COMMISSION.name());
				try (ResultSet rs = ps.executeQuery())
				{
					while (rs.next())
					{
						final ItemInstance itemInstance = new ItemInstance(rs);
						itemInstances.put(itemInstance.getObjectId(), itemInstance);
					}
				}
			}
			try (Statement st = con.createStatement(); ResultSet rs = st.executeQuery(SELECT_ALL_COMMISSION_ITEMS))
			{
				while (rs.next())
				{
					final long commissionId = rs.getLong("commission_id");
					final ItemInstance itemInstance = itemInstances.get(rs.getInt("item_object_id"));
					if (itemInstance == null)
					{
						LOGGER.warning(getClass().getSimpleName() + ": Failed loading commission item with commission id " + commissionId + " because item instance does not exist or failed to load.");
						continue;
					}
					final CommissionItem commissionItem = new CommissionItem(commissionId, itemInstance, rs.getInt("price_per_unit"), rs.getTimestamp("start_time").toInstant(), rs.getByte("duration_in_days"));
					_commissionItems.put(commissionItem.getCommissionId(), commissionItem);
					if (commissionItem.getEndTime().isBefore(Instant.now()))
					{
						expireSale(commissionItem);
					}
					else
					{
						commissionItem.setSaleEndTask(ThreadPool.get().schedule(() -> expireSale(commissionItem), Duration.between(Instant.now(), commissionItem.getEndTime()).toMillis()));
					}
				}
			}
		}
		catch (SQLException e)
		{
			LOGGER.log(Level.WARNING, getClass().getSimpleName() + ": Failed loading commission items.", e);
		}
		ThreadPool.get().scheduleAtFixedRate(this::sendPayments, 120_000, 120_000);
	}
	
	/**
	 * Checks if the player is allowed to interact with commission manager.
	 *
	 * @param player
	 *            the player
	 * @return {@code true} if the player is allowed to interact, {@code false} otherwise
	 */
	public static boolean isPlayerAllowedToInteract(PlayerInstance player)
	{
		// final Npc npc = player.getLastFolkNPC();
		// if ((npc != null) && (npc instanceof CommissionManagerInstance)) {
		// return npc.calculateDistance3D(player) <= INTERACTION_DISTANCE;
		// }
		// return false;
		return true;
	}
	
	/**
	 * Gets the single instance.
	 *
	 * @return the single instance
	 */
	public static CommissionManager getInstance()
	{
		return SingletonHolder.INSTANCE;
	}
	
	public void showAuctions(PlayerInstance player, int itemTypeInt, int gradeInt, String query)
	{
		// @formatter:off
		final List<CommissionItem> commissionItems = _commissionItems.values().stream().filter(c -> checkItemSearchRequirements(c, itemTypeInt, gradeInt, query)).limit(ITEMS_LIMIT_PER_REQUEST).collect(Collectors.toList());
		// @formatter:on
		if (commissionItems.isEmpty())
		{
			player.sendPacket(new ExResponseCommissionList(CommissionListReplyType.ITEM_DOES_NOT_EXIST));
			return;
		}
		int chunks = commissionItems.size() / ExResponseCommissionList.MAX_CHUNK_SIZE;
		if (commissionItems.size() > (chunks * ExResponseCommissionList.MAX_CHUNK_SIZE))
		{
			chunks++;
		}
		for (int i = chunks - 1; i >= 0; i--)
		{
			player.sendPacket(new ExResponseCommissionList(CommissionListReplyType.AUCTIONS, commissionItems, i, i * ExResponseCommissionList.MAX_CHUNK_SIZE));
		}
	}
	
	/**
	 * Shows the player his auctions.
	 *
	 * @param player
	 *            the player
	 */
	public void showPlayerAuctions(PlayerInstance player)
	{
		// @formatter:off
		final List<CommissionItem> commissionItems = _commissionItems.values().stream().filter(c -> c.getItemInstance().getOwnerId() == player.getObjectId()).limit(MAX_ITEMS_REGISTRED_PER_PLAYER).collect(Collectors.toList());
		// @formatter:on
		if (!commissionItems.isEmpty())
		{
			player.sendPacket(new ExResponseCommissionList(CommissionListReplyType.PLAYER_AUCTIONS, commissionItems));
		}
		else
		{
			player.sendPacket(new ExResponseCommissionList(CommissionListReplyType.PLAYER_AUCTIONS_EMPTY));
		}
	}
	
	/**
	 * Registers an item for the given player.
	 *
	 * @param player
	 *            the player
	 * @param itemObjectId
	 *            the item object id
	 * @param itemCount
	 *            the item count
	 * @param pricePerUnit
	 *            the price per unit
	 * @param durationInDays
	 *            the duration in days
	 */
	public void registerItem(PlayerInstance player, int itemObjectId, long itemCount, int pricePerUnit, byte durationInDays)
	{
		if (itemCount < 1)
		{
			player.sendPacket(SystemMessageId.THE_ITEM_HAS_FAILED_TO_BE_REGISTERED);
			player.sendPacket(ExResponseCommissionRegister.FAILED);
			return;
		}
		final long totalPrice = itemCount * pricePerUnit;
		if (totalPrice < MIN_REGISTRATION_AND_SALE_FEE)
		{
			player.sendPacket(SystemMessageId.THE_ITEM_CANNOT_BE_REGISTERED_BECAUSE_REQUIREMENTS_ARE_NOT_MET);
			player.sendPacket(ExResponseCommissionRegister.FAILED);
			return;
		}
		ItemInstance itemInstance = player.getInventory().getItemByObjectId(itemObjectId);
		if ((itemInstance == null) || !itemInstance.isAvailable(player, false, false) || (itemInstance.getCount() < itemCount))
		{
			player.sendPacket(SystemMessageId.THE_ITEM_HAS_FAILED_TO_BE_REGISTERED);
			player.sendPacket(ExResponseCommissionRegister.FAILED);
			return;
		}
		synchronized (this)
		{
			// @formatter:off
			final long playerRegisteredItems = _commissionItems.values().stream().filter(c -> c.getItemInstance().getOwnerId() == player.getObjectId()).count();
			// @formatter:on
			if (playerRegisteredItems >= MAX_ITEMS_REGISTRED_PER_PLAYER)
			{
				player.sendPacket(SystemMessageId.THE_ITEM_HAS_FAILED_TO_BE_REGISTERED);
				player.sendPacket(ExResponseCommissionRegister.FAILED);
				return;
			}
			itemInstance = player.getInventory().detachItem("Commission Registration", itemInstance, itemCount, ItemLocation.COMMISSION, player, null);
			if (itemInstance == null)
			{
				player.sendPacket(SystemMessageId.THE_ITEM_HAS_FAILED_TO_BE_REGISTERED);
				player.sendPacket(ExResponseCommissionRegister.FAILED);
				return;
			}
			try (Connection con = DatabaseFactory.getConnection(); PreparedStatement ps = con.prepareStatement(INSERT_COMMISSION_ITEM, Statement.RETURN_GENERATED_KEYS))
			{
				final Instant startTime = Instant.now();
				ps.setInt(1, itemInstance.getObjectId());
				ps.setLong(2, pricePerUnit);
				ps.setTimestamp(3, Timestamp.from(startTime));
				ps.setByte(4, durationInDays);
				ps.executeUpdate();
				try (ResultSet rs = ps.getGeneratedKeys())
				{
					if (rs.next())
					{
						final CommissionItem commissionItem = new CommissionItem(rs.getLong(1), itemInstance, pricePerUnit, startTime, durationInDays);
						final ScheduledFuture<?> saleEndTask = ThreadPool.get().schedule(() -> expireSale(commissionItem), Duration.between(Instant.now(), commissionItem.getEndTime()).toMillis());
						commissionItem.setSaleEndTask(saleEndTask);
						_commissionItems.put(commissionItem.getCommissionId(), commissionItem);
						player.getLastCommissionInfos().put(itemInstance.getId(), new ExResponseCommissionInfo(itemInstance.getId(), pricePerUnit, itemCount, (byte) ((durationInDays - 1) / 2)));
						player.sendPacket(SystemMessageId.THE_ITEM_HAS_BEEN_REGISTERED);
						player.sendPacket(ExResponseCommissionRegister.SUCCEED);
					}
				}
			}
			catch (SQLException e)
			{
				LOGGER.log(Level.WARNING, getClass().getSimpleName() + ": Failed inserting commission item. ItemInstance: " + itemInstance, e);
				player.sendPacket(SystemMessageId.THE_ITEM_HAS_FAILED_TO_BE_REGISTERED);
				player.sendPacket(ExResponseCommissionRegister.FAILED);
			}
		}
	}
	
	/**
	 * Deletes an item and returns it to the player.
	 *
	 * @param player
	 *            the player
	 * @param commissionId
	 *            the commission id
	 */
	public void deleteItem(PlayerInstance player, long commissionId)
	{
		final CommissionItem commissionItem = getCommissionItem(commissionId);
		if (commissionItem == null)
		{
			player.sendPacket(SystemMessageId.FAILED_TO_CANCEL_THE_SALE);
			player.sendPacket(ExResponseCommissionDelete.FAILED);
			return;
		}
		if (commissionItem.getItemInstance().getOwnerId() != player.getObjectId())
		{
			player.sendPacket(ExResponseCommissionDelete.FAILED);
			return;
		}
		if (!player.isInventoryUnder80(false) || (player.getWeightPenalty() >= 3))
		{
			player.sendPacket(SystemMessageId.IF_THE_WEIGHT_IS_80_OR_MORE_AND_THE_INVENTORY_NUMBER_IS_90_OR_MORE_PURCHASE_CANCELLATION_IS_NOT_POSSIBLE);
			player.sendPacket(SystemMessageId.FAILED_TO_CANCEL_THE_SALE);
			player.sendPacket(ExResponseCommissionDelete.FAILED);
			return;
		}
		if ((_commissionItems.remove(commissionId) == null) || !commissionItem.getSaleEndTask().cancel(false))
		{
			player.sendPacket(SystemMessageId.FAILED_TO_CANCEL_THE_SALE);
			player.sendPacket(ExResponseCommissionDelete.FAILED);
			return;
		}
		if (deleteItemFromDB(commissionId))
		{
			player.getInventory().addItem("Commission Cancellation", commissionItem.getItemInstance(), player, null);
			World.getInstance().addObject(commissionItem.getItemInstance());
			player.sendPacket(SystemMessageId.THE_SALE_IS_CANCELLED);
			player.sendPacket(ExResponseCommissionDelete.SUCCEED);
		}
		else
		{
			player.sendPacket(SystemMessageId.FAILED_TO_CANCEL_THE_SALE);
			player.sendPacket(ExResponseCommissionDelete.FAILED);
		}
	}
	
	/**
	 * Buys the item for the given player.
	 *
	 * @param player
	 *            the player
	 * @param commissionId
	 *            the commission id
	 */
	public void buyItem(PlayerInstance player, long commissionId)
	{
		final CommissionItem commissionItem = getCommissionItem(commissionId);
		if (commissionItem == null)
		{
			player.sendPacket(SystemMessageId.ITEM_PURCHASE_HAS_FAILED);
			player.sendPacket(ExResponseCommissionBuyItem.FAILED);
			return;
		}
		final ItemInstance itemInstance = commissionItem.getItemInstance();
		if (itemInstance.getOwnerId() == player.getObjectId())
		{
			player.sendPacket(SystemMessageId.ITEM_PURCHASE_HAS_FAILED);
			player.sendPacket(ExResponseCommissionBuyItem.FAILED);
			return;
		}
		if (!player.isInventoryUnder80(false) || (player.getWeightPenalty() >= 3))
		{
			player.sendPacket(SystemMessageId.IF_THE_WEIGHT_IS_80_OR_MORE_AND_THE_INVENTORY_NUMBER_IS_90_OR_MORE_PURCHASE_CANCELLATION_IS_NOT_POSSIBLE);
			player.sendPacket(ExResponseCommissionBuyItem.FAILED);
			return;
		}
		final int totalPrice = (int) (itemInstance.getCount() * commissionItem.getPricePerUnit());
		if ((player.getPrimePoints()) < totalPrice)
		{
			player.sendMessage("Not enough funds.");
			player.sendPacket(ExResponseCommissionBuyItem.FAILED);
			return;
		}
		player.setPrimePoints(player.getPrimePoints() - totalPrice);
		if ((_commissionItems.remove(commissionId) == null) || !commissionItem.getSaleEndTask().cancel(false))
		{
			player.setPrimePoints(player.getPrimePoints() + totalPrice);
			player.sendPacket(SystemMessageId.ITEM_PURCHASE_HAS_FAILED);
			player.sendPacket(ExResponseCommissionBuyItem.FAILED);
			return;
		}
		if (deleteItemFromDB(commissionId))
		{
			insertTransaction(itemInstance.getOwnerId(), player.getObjectId(), commissionItem.getItemInstance().getObjectId(), commissionItem.getItemInstance().getId(), commissionItem.getItemInstance().getCount(), commissionItem.getItemInstance().getEnchantLevel(), totalPrice);
			player.sendPacket(new ExResponseCommissionBuyItem(commissionItem));
			player.getInventory().addItem("Commission Buy Item", commissionItem.getItemInstance(), player, null);
			World.getInstance().addObject(commissionItem.getItemInstance());
			if (Config.LOG_ITEMS)
			{
				LOGGER_ITEMS.info("CUSTOM_LOG_BALANCE_SHOP:" + String.valueOf("CommissionShop") // in case of null
				+ ", item " + commissionItem.getItemInstance().getObjectId() //
				+ ":" + commissionItem.getItemInstance().getName() + (commissionItem.getItemInstance().getItem().getAdditionalName() == null ? "" : " " + commissionItem.getItemInstance().getItem().getAdditionalName()) //
				+ "[" + commissionItem.getItemInstance().getId() + "]" //
				+ "(" + commissionItem.getItemInstance().getCount() + "Price: " + totalPrice + "), PrevCount(" //
				+ String.valueOf(player.getInventory().getInventoryItemCount(commissionItem.getItemInstance().getId(), 0) - commissionItem.getItemInstance().getCount()) + "), " // in case of null
				+ "New Count(" + String.valueOf(player.getInventory().getInventoryItemCount(commissionItem.getItemInstance().getId(), 0)) + "), " + String.valueOf(player) + ", " // in case of null
				+ String.valueOf(player)); // in case of null
			}
		}
		else
		{
			player.setPrimePoints(player.getPrimePoints() + totalPrice);
			player.sendPacket(ExResponseCommissionBuyItem.FAILED);
		}
		player.sendPacket(new ExSendUIEventCustom(ExSendUIEventCustom.MONEY_STORE_BALANCE, (player.getPrimePoints())));
	}
	
	private void insertTransaction(int sellerId, int buyerId, int itemObjectID, int itemId, long itemCount, int enchantLevel, int totalPrice)
	{
		try (Connection conn = DatabaseFactory.getConnection(); PreparedStatement ps = conn.prepareStatement("INSERT INTO commission_transactions (sellerId, buyerId, itemObjectID, itemId, itemCount, enchantLevel, totalPrice, paymentStatus) VALUES (?, ?, ?, ?, ?, ?, ?, ?)"))
		{
			ps.setInt(1, sellerId);
			ps.setInt(2, buyerId);
			ps.setInt(3, itemObjectID);
			ps.setInt(4, itemId);
			ps.setLong(5, itemCount);
			ps.setInt(6, enchantLevel);
			ps.setInt(7, totalPrice);
			ps.setInt(8, 0);
			ps.execute();
		}
		catch (Exception e)
		{
			e.printStackTrace();
		}
	}
	
	private void sendPayments()
	{
		final List<Integer> ids = new ArrayList<>();
		final Map<Integer, Integer> toGive = new HashMap<>();
		try (Connection conn = DatabaseFactory.getConnection())
		{
			try (PreparedStatement ps = conn.prepareStatement("SELECT `id`, `sellerId`, `totalPrice` FROM commission_transactions WHERE `paymentStatus`=?"))
			{
				ps.setInt(1, 0);
				final ResultSet rs = ps.executeQuery();
				while (rs.next())
				{
					int id = rs.getInt(1);
					int sellerId = rs.getInt(2);
					final PlayerInstance player = World.getInstance().getPlayer(sellerId);
					if (player == null)
					{
						continue;
					}
					int totalPrice = rs.getInt(3);
					ids.add(id);
					toGive.compute(sellerId, (k, v) -> (v == null ? 0 : v) + totalPrice);
				}
			}
			catch (Exception e)
			{
				e.printStackTrace();
			}
			try (PreparedStatement ps = conn.prepareStatement("UPDATE commission_transactions SET `paymentStatus`=? WHERE `id`=?"))
			{
				for (Integer id : ids)
				{
					ps.setInt(1, 1);
					ps.setInt(2, id);
					ps.addBatch();
				}
				ps.executeBatch();
			}
			catch (Exception e)
			{
				e.printStackTrace();
			}
		}
		catch (Exception e)
		{
			e.printStackTrace();
		}
		for (Map.Entry<Integer, Integer> entry : toGive.entrySet())
		{
			final PlayerInstance player = World.getInstance().getPlayer(entry.getKey());
			player.setPrimePoints(player.getPrimePoints() + entry.getValue());
		}
	}
	
	/**
	 * Deletes a commission item from database.
	 *
	 * @param commissionId
	 *            the commission item
	 * @return {@code true} if the item was deleted successfully, {@code false} otherwise
	 */
	private boolean deleteItemFromDB(long commissionId)
	{
		try (Connection con = DatabaseFactory.getConnection(); PreparedStatement ps = con.prepareStatement(DELETE_COMMISSION_ITEM))
		{
			ps.setLong(1, commissionId);
			if (ps.executeUpdate() > 0)
			{
				return true;
			}
		}
		catch (SQLException e)
		{
			LOGGER.log(Level.WARNING, getClass().getSimpleName() + ": Failed deleting commission item. Commission ID: " + commissionId, e);
		}
		return false;
	}
	
	/**
	 * Expires the sale of a commission item and sends the item back to the player.
	 *
	 * @param commissionItem
	 *            the comission item
	 */
	private void expireSale(CommissionItem commissionItem)
	{
		if ((_commissionItems.remove(commissionItem.getCommissionId()) != null) && deleteItemFromDB(commissionItem.getCommissionId()))
		{
			final Message mail = new Message(commissionItem.getItemInstance().getOwnerId(), commissionItem.getItemInstance(), MailType.COMMISSION_ITEM_RETURNED);
			MailManager.getInstance().sendMessage(mail);
		}
	}
	
	/**
	 * Gets the commission item.
	 *
	 * @param commissionId
	 *            the commission id to get
	 * @return the commission item if it exists, {@code null} otherwise
	 */
	public CommissionItem getCommissionItem(long commissionId)
	{
		return _commissionItems.get(commissionId);
	}
	
	/**
	 * @param objectId
	 * @return {@code true} if player with the objectId has commission items, {@code false} otherwise
	 */
	public boolean hasCommissionItems(int objectId)
	{
		for (CommissionItem item : _commissionItems.values())
		{
			if (item.getItemInstance().getObjectId() == objectId)
			{
				return true;
			}
		}
		return false;
	}
	
	/**
	 * @param player
	 *            the player
	 * @param itemId
	 *            the item id
	 * @return {@code true} if the player has commissioned a specific item id, {@code false} otherwise.
	 */
	public boolean hasCommissionedItemId(PlayerInstance player, int itemId)
	{
		for (CommissionItem item : _commissionItems.values())
		{
			if ((item.getItemInstance().getOwnerId() == player.getObjectId()) && (item.getItemInstance().getItem().getId() == itemId))
			{
				return true;
			}
		}
		return false;
	}
	
	public boolean checkItemSearchRequirements(CommissionItem commissionItem, int itemTypeInt, int gradeInt, String query)
	{
		final MoneyStoreItemType itemType;
		final ItemGrade grade;
		try
		{
			itemType = itemTypeInt < 0 ? null : MoneyStoreItemType.values()[itemTypeInt];
			grade = gradeInt < 0 ? null : ItemGrade.values()[gradeInt];
		}
		catch (Exception e)
		{
			return false;
		}
		final Item item = commissionItem.getItemInfo().getItem();
		if (itemType != null && !checkItemType(item, itemType))
		{
			return false;
		}
		if (grade != null && grade != item.getItemGrade())
		{
			return false;
		}
		if (query.isBlank())
		{
			return true;
		}
		final String queryLower = query.toLowerCase();
		if (!item.getName().toLowerCase().contains(queryLower) && !item.getNameRu().toLowerCase().contains(queryLower))
		{
			return false;
		}
		return true;
	}
	
	private boolean checkItemType(Item item, MoneyStoreItemType itemType)
	{
		return switch (itemType)
		{
			case EQUIPMENT -> item.isEquipable();
			case ENHANCEMENT -> isEnhancementItem(item);
			case PRODUCT_LIST -> item.isPotion() || item.isScroll() || isTicket(item) || isPackOrCraft(item) || isGroceryMisc(item);
			case COLLECTIONS -> isCollection(item);
			default -> false;
		};
	}
	
	private boolean isEnchantScroll(Item item)
	{
		if (!(item instanceof EtcItem))
		{
			return false;
		}
		final IItemHandler ih = ItemHandler.getInstance().getHandler((EtcItem) item);
		return (ih != null) && ih.getClass().getSimpleName().equals("EnchantScrolls");
	}
	
	private boolean isCrystal(Item item)
	{
		return EnsoulData.getInstance().getStone(item.getId(), null) != null;
	}
	
	private boolean isLifeStone(Item item)
	{
		return VariationData.getInstance().getAnyVariation(item.getId()) != null;
	}
	
	private boolean isDye(Item item)
	{
		return HennaData.getInstance().getHennaByItemId(item.getId()) != null;
	}
	
	private boolean isSpellBook(Item item)
	{
		return item.getName().contains("Spellbook: ");
	}
	
	private boolean isEnhancementMisc(Item item)
	{
		return (item.getId() >= 91031) && (item.getId() <= 91038);
	}
	
	private boolean isEnhancementItem(Item item)
	{
		return isEnchantScroll(item) || isCrystal(item) || isLifeStone(item) || isDye(item) || isSpellBook(item) || isEnhancementMisc(item);
	}
	
	private boolean isTicket(Item item)
	{
		return (item.getId() == 90045) || (item.getId() == 91462) || (item.getId() == 91463) || (item.getId() == 91972) || (item.getId() == 93903);
	}
	
	private boolean isPackOrCraft(Item item)
	{
		if ((item.getId() == 92477) || (item.getId() == 91462) || (item.getId() == 92478) || (item.getId() == 92479) || (item.getId() == 92480) || (item.getId() == 92481) || (item.getId() == 49756) || (item.getId() == 93906) || (item.getId() == 93907) || (item.getId() == 93908) || (item.getId() == 93909) || (item.getId() == 93910) || (item.getId() == 91076))
		{
			return true;
		}
		if (!(item instanceof EtcItem))
		{
			return false;
		}
		final IItemHandler ih = ItemHandler.getInstance().getHandler((EtcItem) item);
		return (ih != null) && ih.getClass().getSimpleName().equals("ExtractableItems");
	}
	
	private boolean isGroceryMisc(Item item)
	{
		return !item.isEquipable() && !isEnhancementItem(item) && !isCollection(item) && !item.isPotion() && !item.isScroll() && !isTicket(item) && !isPackOrCraft(item);
	}
	
	private boolean isCollection(Item item)
	{
		for (CollectionDataHolder collectionHolder : CollectionData.getInstance().getCollections())
		{
			for (ItemCollectionData itemData : collectionHolder.items())
			{
				if (itemData.getItemIds().contains(item.getId()))
				{
					return true;
				}
			}
		}
		return false;
	}
	
	public enum MoneyStoreItemType
	{
		EQUIPMENT,
		ENHANCEMENT,
		PRODUCT_LIST,
		COLLECTIONS,
	}
	
	private static class SingletonHolder
	{
		protected static final CommissionManager INSTANCE = new CommissionManager();
	}
}
