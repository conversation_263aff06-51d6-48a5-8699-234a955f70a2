package club.projectessence.gameserver.instancemanager;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collection;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;
import java.util.logging.Logger;
import java.util.stream.Collectors;

import club.projectessence.Config;
import club.projectessence.commons.concurrent.ThreadPool;
import club.projectessence.commons.database.DatabaseFactory;
import club.projectessence.gameserver.discord.DiscordBotManager;
import club.projectessence.gameserver.enums.Faction;
import club.projectessence.gameserver.features.museum.MuseumManager;
import club.projectessence.gameserver.enums.MailType;
import club.projectessence.gameserver.model.Message;
import club.projectessence.gameserver.model.World;
import club.projectessence.gameserver.model.actor.Creature;
import club.projectessence.gameserver.model.actor.instance.OutpostInstance;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.model.holders.ItemHolder;
import club.projectessence.gameserver.model.itemcontainer.Mail;
import club.projectessence.gameserver.util.Broadcast;

public class FactionWarManager
{
	private static final Logger						LOGGER					= Logger.getLogger(FactionWarManager.class.getName());
	private final Map<Faction, List<PlayerRanking>>	playerRankings			= new ConcurrentHashMap<>();
	private final Map<Faction, Integer>				totalFactionPoints		= new ConcurrentHashMap<>();
	private List<PlayerRanking>						globalRankings			= new ArrayList<>();									// Lưu trữ bảng xếp hạng toàn cục
	private boolean									isWarActive				= false;
	private ScheduledFuture<?>						startTask;
	private ScheduledFuture<?>						endTask;
	private String									nearestFactionWarTime	= "Not Scheduled";
	
	public static class PlayerRanking
	{
		private final PlayerInstance	player;
		private final int				points;
		
		public PlayerRanking(PlayerInstance player, int points)
		{
			this.player = player;
			this.points = points;
		}
		
		public PlayerInstance getPlayer()
		{
			return player;
		}
		
		public int getPoints()
		{
			return points;
		}
	}
	
	private FactionWarManager()
	{
		playerRankings.put(Faction.FIRE, new ArrayList<>());
		playerRankings.put(Faction.WATER, new ArrayList<>());
		totalFactionPoints.put(Faction.FIRE, 0);
		totalFactionPoints.put(Faction.WATER, 0);
		if (Config.FACTION_WAR_ENABLED)
		{
			loadFactionPoints();
			loadFactionWarState();
			if (!isWarActive)
			{
				startWarSessionCycle();
			}
			LOGGER.info("FactionWarManager: Faction War system initialized.");
		}
		else
		{
			LOGGER.info("FactionWarManager: Faction War system disabled in configuration.");
		}
	}
	
	private void startWarSessionCycle()
	{
		long now = System.currentTimeMillis();
		List<TimeSlot> timeSlots = new ArrayList<>();
		for (int[] slot : Config.FACTION_WAR_TIME_SLOTS)
		{
			timeSlots.add(new TimeSlot(slot[0], slot[1], slot[2], slot[3]));
		}
		long nearestStartDelay = Long.MAX_VALUE;
		long nearestEndDelay = Long.MAX_VALUE;
		TimeSlot nearestStartSlot = null;
		TimeSlot nearestEndSlot = null;
		for (TimeSlot slot : timeSlots)
		{
			long startTimeToday = slot.getStartTimeInMillis(now);
			long endTimeToday = slot.getEndTimeInMillis(now);
			Calendar startCal = Calendar.getInstance();
			startCal.setTimeInMillis(startTimeToday);
			Calendar endCal = Calendar.getInstance();
			endCal.setTimeInMillis(endTimeToday);
			if (startCal.get(Calendar.DAY_OF_YEAR) > endCal.get(Calendar.DAY_OF_YEAR))
			{
				startCal.add(Calendar.DAY_OF_MONTH, -1);
				startTimeToday = startCal.getTimeInMillis();
			}
			if (now >= startTimeToday && now < endTimeToday)
			{
				isWarActive = true;
				nearestEndDelay = endTimeToday - now;
				nearestEndSlot = slot;
				SimpleDateFormat timeFormat = new SimpleDateFormat("HH:mm");
				nearestFactionWarTime = "From " + timeFormat.format(new Date(startTimeToday)) + " to " + timeFormat.format(new Date(endTimeToday));
				if (Config.LOG_FACTION_DETAILS)
				{
					LOGGER.info("FactionWarManager: Faction War session is ongoing (started at " + new java.util.Date(startTimeToday) + ", ending at " + new java.util.Date(endTimeToday) + ")");
					LOGGER.info("FactionWarManager: nearestFactionWarTime set to " + nearestFactionWarTime);
				}
				saveFactionWarState();
				break;
			}
			long startDelay = startTimeToday - now;
			if (startDelay < 0)
			{
				startCal.add(Calendar.DAY_OF_MONTH, 1);
				startTimeToday = startCal.getTimeInMillis();
				startDelay = startTimeToday - now;
			}
			if (startDelay < nearestStartDelay)
			{
				nearestStartDelay = startDelay;
				nearestStartSlot = slot;
			}
		}
		if (!isWarActive)
		{
			if (nearestStartSlot != null)
			{
				SimpleDateFormat timeFormat = new SimpleDateFormat("HH:mm");
				long startTime = now + nearestStartDelay;
				long endTime = nearestStartSlot.getEndTimeInMillis(startTime);
				nearestFactionWarTime = "From " + timeFormat.format(new Date(startTime)) + " to " + timeFormat.format(new Date(endTime));
				if (Config.LOG_FACTION_DETAILS)
				{
					LOGGER.info("FactionWarManager: Faction War session scheduled to start at " + new java.util.Date(startTime) + ". nearestFactionWarTime set to " + nearestFactionWarTime);
				}
			}
			else
			{
				nearestFactionWarTime = "Not Scheduled";
				LOGGER.warning("FactionWarManager: No valid time slots found. nearestFactionWarTime set to Not Scheduled.");
			}
			saveFactionWarState();
		}
		if (nearestStartSlot != null && !isWarActive)
		{
			startTask = ThreadPool.schedule(() ->
			{
				if (!Config.FACTION_WAR_ENABLED)
					return;
				startNewWarSession();
				LOGGER.info("FactionWarManager: Started Faction War session at " + new java.util.Date());
				saveFactionWarState();
			}, nearestStartDelay - 10 * 60 * 1000); // 10 phút trước khi bắt đầu
		}
		if (nearestEndSlot != null && isWarActive)
		{
			endTask = ThreadPool.schedule(() ->
			{
				if (!Config.FACTION_WAR_ENABLED)
					return;
				endWarSession();
				LOGGER.info("FactionWarManager: Ended Faction War session at " + new java.util.Date());
				startWarSessionCycle();
				saveFactionWarState();
			}, nearestEndDelay);
		}
		else if (nearestStartSlot != null && !isWarActive)
		{
			long endTime = nearestStartSlot.getEndTimeInMillis(now + nearestStartDelay);
			long endDelay = endTime - (now + nearestStartDelay);
			endTask = ThreadPool.schedule(() ->
			{
				if (!Config.FACTION_WAR_ENABLED)
					return;
				endWarSession();
				LOGGER.info("FactionWarManager: Ended Faction War session at " + new java.util.Date());
				startWarSessionCycle();
				saveFactionWarState();
			}, nearestStartDelay + endDelay);
		}
		if (Config.LOG_FACTION_DETAILS)
		{
			LOGGER.info("FactionWarManager: Scheduled Faction War session - Nearest start: " + new java.util.Date(now + nearestStartDelay));
		}
	}
	
	public void shutdown()
	{
		if (startTask != null)
		{
			startTask.cancel(false);
			startTask = null;
		}
		if (endTask != null)
		{
			endTask.cancel(false);
			endTask = null;
		}
		saveFactionPoints();
		if (Config.LOG_FACTION_DETAILS)
		{
			LOGGER.info("FactionWarManager: All scheduled tasks stopped and faction points saved.");
		}
	}
	
	public void startNewWarSession()
	{
		isWarActive = true;
		playerRankings.get(Faction.FIRE).clear();
		playerRankings.get(Faction.WATER).clear();
		globalRankings.clear(); // Xóa bảng xếp hạng toàn cục khi bắt đầu phiên mới
		resetFactionPoints();
		// Reset personal points for all players
		for (PlayerInstance player : World.getInstance().getPlayers())
		{
			player.setPersonalPoints(0);
		}
		Broadcast.toAllOnlinePlayers("Faction War: A new war session has started!", true);
		// Thông báo Discord
		if (Config.DISCORD_ENABLED && Config.DISCORD_FACTION_NOTIFICATIONS_ENABLED && !Config.DISCORD_FACTION_WAR_WEBHOOK.isEmpty())
		{
			DiscordBotManager.getInstance().sendFactionWarInfo();
		}
		LOGGER.info("FactionWarManager: Started a new Faction War session.");
		saveFactionWarState();
	}
	
	public void endWarSession()
	{
		isWarActive = false;
		// Step 1: Calculate rankings for each faction (for display purposes only)
		for (Faction faction : new Faction[]
		{
			Faction.FIRE, Faction.WATER
		})
		{
			List<PlayerRanking> rankings = new ArrayList<>();
			for (PlayerInstance player : World.getInstance().getPlayers())
			{
				if (player.getFaction() == faction)
				{
					int points = player.getPersonalPoints();
					rankings.add(new PlayerRanking(player, points));
				}
			}
			rankings.sort(Comparator.comparingInt(PlayerRanking::getPoints).reversed());
			playerRankings.put(faction, rankings);
		}
		// Step 2: Calculate global rankings across all factions
		globalRankings.clear(); // Đảm bảo làm sạch trước khi tính toán mới
		for (PlayerInstance player : World.getInstance().getPlayers())
		{
			if (player.getFaction() != Faction.NONE)
			{
				int points = player.getPersonalPoints();
				globalRankings.add(new PlayerRanking(player, points));
			}
		}
		globalRankings.sort(Comparator.comparingInt(PlayerRanking::getPoints).reversed());
		// Step 3: Determine the winning faction
		int firePoints = totalFactionPoints.getOrDefault(Faction.FIRE, 0);
		int waterPoints = totalFactionPoints.getOrDefault(Faction.WATER, 0);
		saveFactionPoints();
		Faction winningFaction = firePoints > waterPoints ? Faction.FIRE : Faction.WATER;
		Broadcast.toAllOnlinePlayers("Faction " + winningFaction + " has won this session with " + (winningFaction == Faction.FIRE ? firePoints : waterPoints) + " points!", true);
		// Thông báo Discord
		if (Config.DISCORD_ENABLED && Config.DISCORD_FACTION_NOTIFICATIONS_ENABLED && !Config.DISCORD_FACTION_WAR_WEBHOOK.isEmpty())
		{
			DiscordBotManager.getInstance().sendFactionWarInfo();
		}
		// Step 4: Group global rankings by account to prevent duplicate rewards
		Map<String, List<PlayerRanking>> accountRankings = new ConcurrentHashMap<>();
		for (int i = 0; i < globalRankings.size(); i++)
		{
			PlayerInstance player = globalRankings.get(i).getPlayer();
			if (player == null)
				continue;
			String accountName = player.getAccountName();
			accountRankings.computeIfAbsent(accountName, k -> new ArrayList<>()).add(new PlayerRanking(player, i + 1));
		}
		// Step 5: Distribute Faction War rewards (one per account, highest rank only)
		for (Map.Entry<String, List<PlayerRanking>> entry : accountRankings.entrySet())
		{
			String accountName = entry.getKey();
			List<PlayerRanking> accountChars = entry.getValue();
			// Sort by rank to get the highest rank for this account
			accountChars.sort(Comparator.comparingInt(r -> r.getPoints()));
			PlayerRanking bestRanking = accountChars.get(0); // Highest rank (lowest rank number)
			PlayerInstance player = bestRanking.getPlayer();
			int rank = bestRanking.getPoints(); // Points here represent the rank
			List<ItemHolder> rewards = getRewardForRank(rank);
			if (rewards == null || rewards.isEmpty())
				continue;
			String subject = "Faction War Reward";
			String content = "Congratulations! You have ranked #" + rank + " overall with " + player.getPersonalPoints() + " personal points in the " + player.getFaction() + " faction!";
			Message msg = new Message(player.getObjectId(), subject, content, MailType.SYSTEM);
			Mail attachments = msg.createAttachments();
			for (ItemHolder reward : rewards)
			{
				attachments.addItem("Faction War Reward", reward.getId(), reward.getCount(), null, null);
			}
			MailManager.getInstance().sendMessage(msg);
			player.sendMessage("Congratulations! You have ranked #" + rank + " overall with " + player.getPersonalPoints() + " personal points! Rewards have been sent via mail.");
			Broadcast.toAllOnlinePlayers("Faction War: " + player.getName() + " has achieved rank #" + rank + " overall in the " + player.getFaction() + " faction!", true);
		}
		// Reset personal points for all players after distributing rewards
		for (PlayerInstance player : World.getInstance().getPlayers())
		{
			player.setPersonalPoints(0);
		}
		resetFactionPoints();
		Broadcast.toAllOnlinePlayers("Faction War: The war session has ended! The next session will start soon.", true);
		LOGGER.info("FactionWarManager: Ended Faction War session and distributed rewards based on global rankings.");
		saveFactionWarState();
	}
	
	private List<ItemHolder> getRewardForRank(int rank)
	{
		if (rank == 1)
			return Config.FACTION_WAR_TOP_1_REWARD;
		else if (rank <= 10)
			return Config.FACTION_WAR_TOP_10_REWARD;
		else if (rank <= 50)
			return Config.FACTION_WAR_TOP_50_REWARD;
		else if (rank <= 100)
			return Config.FACTION_WAR_TOP_100_REWARD;
		else if (rank <= 300)
			return Config.FACTION_WAR_TOP_300_REWARD;
		else
			return Config.FACTION_WAR_NOT_REWARD;
	}
	
	public void addPersonalPoints(PlayerInstance player, int points, String reason)
	{
		if (!Config.FACTION_WAR_ENABLED || !isWarActive || player.getLevel() < Config.FACTION_WAR_MIN_LEVEL)
			return;

		// Apply Zone Dominance GvE Points bonus
		int finalPoints = points;
		if (Config.ENABLE_ZONE_DOMINANCE_SYSTEM && player.getFaction() != Faction.NONE)
		{
			int gveBonus = ZoneDominanceManager.getInstance().getGvePointsBonus(player.getFaction());
			if (gveBonus > 0)
			{
				finalPoints = points + (points * gveBonus / 100);
				if (Config.LOG_FACTION_DETAILS)
				{
					LOGGER.fine("Zone Dominance GvE bonus applied: " + points + " -> " + finalPoints + " (+" + gveBonus + "%)");
				}
				// Send debug message to GM players
				ZoneDominanceManager.getInstance().sendBonusDebugMessage(player, "Faction War Points", points, finalPoints, gveBonus);
			}
		}

		player.addPoints(finalPoints); // Sử dụng phương thức addPoints từ PlayerInstance

		if (Config.LOG_FACTION_DETAILS)
		{
			LOGGER.fine("FactionWarManager: Player " + player.getName() + " received " + finalPoints + " personal points for: " + reason + ", current personal points: " + player.getPersonalPoints());
		}

		// Add museum data for faction war points
		MuseumManager.getInstance().addFactionWarPoints(player, finalPoints);
	}
	
	public void addFactionPoints(Faction faction, int points)
	{
		if (faction == Faction.NONE)
			return;
		totalFactionPoints.compute(faction, (k, v) ->
		{
			int newPoints = (v == null ? 0 : v) + points;
			return Math.max(0, newPoints);
		});
		saveFactionPoints();
		if (Config.DISCORD_ENABLED && Config.DISCORD_FACTION_NOTIFICATIONS_ENABLED && !Config.DISCORD_FACTION_WAR_WEBHOOK.isEmpty())
		{
			DiscordBotManager.getInstance().sendFactionWarInfo(); // Cập nhật khi điểm số thay đổi
		}
		if (Config.LOG_FACTION_DETAILS)
		{
			LOGGER.fine("FactionWarManager: Added " + points + " points to faction " + faction + ", current total: " + totalFactionPoints.get(faction));
		}
	}
	
	public void setFactionPoints(Faction faction, int points)
	{
		if (faction == Faction.NONE)
			return;
		totalFactionPoints.put(faction, Math.max(0, points));
		saveFactionPoints();
		if (Config.DISCORD_ENABLED && Config.DISCORD_FACTION_NOTIFICATIONS_ENABLED && !Config.DISCORD_FACTION_WAR_WEBHOOK.isEmpty())
		{
			DiscordBotManager.getInstance().sendFactionWarInfo(); // Cập nhật khi điểm số thay đổi
		}
		if (Config.LOG_FACTION_DETAILS)
		{
			LOGGER.fine("FactionWarManager: Set faction points for " + faction + " to " + points);
		}
	}
	
	public void removeFactionPoints(Faction faction, int points)
	{
		if (faction == Faction.NONE)
			return;
		totalFactionPoints.compute(faction, (k, v) ->
		{
			int newPoints = (v == null ? 0 : v) - points;
			return Math.max(0, newPoints);
		});
		saveFactionPoints();
		if (Config.DISCORD_ENABLED && Config.DISCORD_FACTION_NOTIFICATIONS_ENABLED && !Config.DISCORD_FACTION_WAR_WEBHOOK.isEmpty())
		{
			DiscordBotManager.getInstance().sendFactionWarInfo(); // Cập nhật khi điểm số thay đổi
		}
		if (Config.LOG_FACTION_DETAILS)
		{
			LOGGER.fine("FactionWarManager: Removed " + points + " points from faction " + faction + ", current total: " + totalFactionPoints.get(faction));
		}
	}
	
	public void clearFactionPoints(Faction faction)
	{
		if (faction == Faction.NONE)
			return;
		totalFactionPoints.put(faction, 0);
		saveFactionPoints();
		if (Config.DISCORD_ENABLED && Config.DISCORD_FACTION_NOTIFICATIONS_ENABLED && !Config.DISCORD_FACTION_WAR_WEBHOOK.isEmpty())
		{
			DiscordBotManager.getInstance().sendFactionWarInfo(); // Cập nhật khi điểm số thay đổi
		}
		if (Config.LOG_FACTION_DETAILS)
		{
			LOGGER.fine("FactionWarManager: Cleared faction points for " + faction);
		}
	}
	
	private void loadFactionPoints()
	{
		try (Connection con = DatabaseFactory.getConnection(); PreparedStatement ps = con.prepareStatement("SELECT faction, points FROM faction_points"); ResultSet rs = ps.executeQuery())
		{
			while (rs.next())
			{
				Faction faction = Faction.valueOf(rs.getString("faction"));
				int points = rs.getInt("points");
				totalFactionPoints.put(faction, points);
				if (Config.LOG_FACTION_DETAILS)
				{
					LOGGER.info("Loaded faction points for " + faction + ": " + points);
				}
			}
		}
		catch (SQLException e)
		{
			LOGGER.severe("FactionWarManager: Failed to load faction points from faction_points table: " + e.getMessage());
		}
	}
	
	public void saveFactionPoints()
	{
		try (Connection con = DatabaseFactory.getConnection(); PreparedStatement ps = con.prepareStatement("INSERT INTO faction_points (faction, points) VALUES (?, ?) ON DUPLICATE KEY UPDATE points = ?"))
		{
			for (Map.Entry<Faction, Integer> entry : totalFactionPoints.entrySet())
			{
				ps.setString(1, entry.getKey().name());
				ps.setInt(2, entry.getValue());
				ps.setInt(3, entry.getValue());
				ps.addBatch();
			}
			ps.executeBatch();
			if (Config.LOG_FACTION_DETAILS)
			{
				LOGGER.fine("FactionWarManager: Saved faction points to faction_points table.");
			}
		}
		catch (SQLException e)
		{
			LOGGER.severe("FactionWarManager: Failed to save faction points to faction_points table: " + e.getMessage());
		}
	}
	
	public int getFactionPoints(Faction faction)
	{
		return totalFactionPoints.getOrDefault(faction, 0);
	}
	
	private void resetFactionPoints()
	{
		totalFactionPoints.put(Faction.FIRE, 0);
		totalFactionPoints.put(Faction.WATER, 0);
		saveFactionPoints();
	}
	
	public void onPvPKill(PlayerInstance killer, PlayerInstance target)
	{
		if (!Config.FACTION_WAR_ENABLED || !isWarActive)
			return;
		if (killer.getLevel() < Config.FACTION_WAR_MIN_LEVEL || target.getLevel() < Config.FACTION_WAR_MIN_LEVEL)
			return;
		if (killer.getFaction() == target.getFaction() || killer.getFaction() == Faction.NONE || target.getFaction() == Faction.NONE)
			return;
		addPersonalPoints(killer, Config.FACTION_WAR_PVP_KILL_POINTS, "killing " + target.getName() + " in PvP");
		totalFactionPoints.compute(killer.getFaction(), (k, v) ->
		{
			int newPoints = (v == null ? 0 : v) + Config.FACTION_WAR_PVP_KILL_POINTS;
			return Math.max(0, newPoints);
		});

		// Add museum data for faction PvP kill
		MuseumManager.getInstance().addFactionPvpKill(killer);
		totalFactionPoints.compute(target.getFaction(), (k, v) ->
		{
			int newPoints = (v == null ? 0 : v) - Config.FACTION_WAR_PVP_DEATH_POINTS;
			return Math.max(0, newPoints);
		});
		saveFactionPoints();
		if (Config.DISCORD_ENABLED && Config.DISCORD_FACTION_NOTIFICATIONS_ENABLED && !Config.DISCORD_FACTION_WAR_WEBHOOK.isEmpty())
		{
			DiscordBotManager.getInstance().sendFactionWarInfo(); // Cập nhật khi điểm số thay đổi
		}
	}
	
	public void onOutpostCapture(PlayerInstance player, OutpostInstance outpost)
	{
		if (!Config.FACTION_WAR_ENABLED || !isWarActive)
			return;
		if (player.getLevel() < Config.FACTION_WAR_MIN_LEVEL)
			return;
		Collection<PlayerInstance> players = getAroundPlayers(outpost, player.getFaction());
		// Loại bỏ chính player khỏi danh sách players để tránh trùng lặp
		players.remove(player);
		// Cộng điểm cho những người chơi xung quanh
		for (PlayerInstance p : players)
		{
			addPersonalPoints(p, Config.FACTION_WAR_OUTPOST_KILL_POINTS, "capturing Outpost " + outpost.getZoneName());
		}
		// Cộng điểm cho người tiêu diệt
		addPersonalPoints(player, Config.FACTION_WAR_OUTPOST_KILL_POINTS, "capturing Outpost " + outpost.getZoneName());
		// Cộng điểm cho phe
		int points = Config.FACTION_WAR_OUTPOST_KILL_POINTS;
		totalFactionPoints.compute(player.getFaction(), (k, v) ->
		{
			int newPoints = (v == null ? 0 : v) + points;
			return Math.max(0, newPoints);
		});
		Faction opposingFaction = (player.getFaction() == Faction.FIRE) ? Faction.WATER : Faction.FIRE;
		totalFactionPoints.compute(opposingFaction, (k, v) ->
		{
			int newPoints = (v == null ? 0 : v) - points;
			return Math.max(0, newPoints);
		});
		saveFactionPoints();
		if (Config.DISCORD_ENABLED && Config.DISCORD_FACTION_NOTIFICATIONS_ENABLED && !Config.DISCORD_FACTION_WAR_WEBHOOK.isEmpty())
		{
			DiscordBotManager.getInstance().sendFactionWarInfo(); // Cập nhật khi điểm số thay đổi
		}
	}
	
	public void onFortressSiege(PlayerInstance player)
	{
		if (!Config.FACTION_WAR_ENABLED || !isWarActive)
			return;
		if (player.getLevel() < Config.FACTION_WAR_MIN_LEVEL)
			return;
		Collection<PlayerInstance> players = getAroundPlayers(player, player.getFaction());
		// Loại bỏ chính player khỏi danh sách players để tránh trùng lặp
		players.remove(player);
		// Cộng điểm cho những người chơi xung quanh
		for (PlayerInstance p : players)
		{
			addPersonalPoints(p, Config.FACTION_WAR_FORTRESS_SIEGE_ZONE_POINTS, "participating in fortress siege");
		}
		// Cộng điểm cho người chơi chính
		addPersonalPoints(player, Config.FACTION_WAR_FORTRESS_SIEGE_ZONE_POINTS, "participating in fortress siege");
		// Cộng điểm cho phe
		int points = Config.FACTION_WAR_FORTRESS_SIEGE_ZONE_POINTS;
		totalFactionPoints.compute(player.getFaction(), (k, v) ->
		{
			int newPoints = (v == null ? 0 : v) + points;
			return Math.max(0, newPoints);
		});
		Faction opposingFaction = (player.getFaction() == Faction.FIRE) ? Faction.WATER : Faction.FIRE;
		totalFactionPoints.compute(opposingFaction, (k, v) ->
		{
			int newPoints = (v == null ? 0 : v) - points;
			return Math.max(0, newPoints);
		});
		saveFactionPoints();
		if (Config.DISCORD_ENABLED && Config.DISCORD_FACTION_NOTIFICATIONS_ENABLED && !Config.DISCORD_FACTION_WAR_WEBHOOK.isEmpty())
		{
			DiscordBotManager.getInstance().sendFactionWarInfo(); // Cập nhật khi điểm số thay đổi
		}
	}
	
	public void onCastleSiege(PlayerInstance player)
	{
		if (!Config.FACTION_WAR_ENABLED || !isWarActive)
			return;
		if (player.getLevel() < Config.FACTION_WAR_MIN_LEVEL)
			return;
		Collection<PlayerInstance> players = getAroundPlayers(player, player.getFaction());
		// Loại bỏ chính player khỏi danh sách players để tránh trùng lặp
		players.remove(player);
		// Cộng điểm cho những người chơi xung quanh
		for (PlayerInstance p : players)
		{
			addPersonalPoints(p, Config.FACTION_WAR_CASTLE_SIEGE_ZONE_POINTS, "participating in castle siege");
		}
		// Cộng điểm cho người chơi chính
		addPersonalPoints(player, Config.FACTION_WAR_CASTLE_SIEGE_ZONE_POINTS, "participating in castle siege");
		// Cộng điểm cho phe
		int points = Config.FACTION_WAR_CASTLE_SIEGE_ZONE_POINTS;
		totalFactionPoints.compute(player.getFaction(), (k, v) ->
		{
			int newPoints = (v == null ? 0 : v) + points;
			return Math.max(0, newPoints);
		});
		Faction opposingFaction = (player.getFaction() == Faction.FIRE) ? Faction.WATER : Faction.FIRE;
		totalFactionPoints.compute(opposingFaction, (k, v) ->
		{
			int newPoints = (v == null ? 0 : v) - points;
			return Math.max(0, newPoints);
		});
		saveFactionPoints();
		if (Config.DISCORD_ENABLED && Config.DISCORD_FACTION_NOTIFICATIONS_ENABLED && !Config.DISCORD_FACTION_WAR_WEBHOOK.isEmpty())
		{
			DiscordBotManager.getInstance().sendFactionWarInfo(); // Cập nhật khi điểm số thay đổi
		}
	}
	
	public void artifactCapture(PlayerInstance player)
	{
		if (!Config.FACTION_WAR_ENABLED || !isWarActive)
			return;
		if (player.getLevel() < Config.FACTION_WAR_MIN_LEVEL)
			return;
		Collection<PlayerInstance> players = getAroundPlayers(player, player.getFaction());
		// Loại bỏ chính player khỏi danh sách players để tránh trùng lặp
		players.remove(player);
		// Cộng điểm cho những người chơi xung quanh
		for (PlayerInstance p : players)
		{
			addPersonalPoints(p, Config.FACTION_WAR_ARTIFACT_CAPTURE_POINTS, "capture Artifact");
		}
		// Cộng điểm cho người tiêu diệt Elite Boss
		addPersonalPoints(player, Config.FACTION_WAR_ARTIFACT_CAPTURE_POINTS, "capture Artifact");
		// Cộng điểm cho phe
		int points = Config.FACTION_WAR_ARTIFACT_CAPTURE_POINTS;
		totalFactionPoints.compute(player.getFaction(), (k, v) ->
		{
			int newPoints = (v == null ? 0 : v) + points;
			return Math.max(0, newPoints);
		});
		Faction opposingFaction = (player.getFaction() == Faction.FIRE) ? Faction.WATER : Faction.FIRE;
		totalFactionPoints.compute(opposingFaction, (k, v) ->
		{
			int newPoints = (v == null ? 0 : v) - points;
			return Math.max(0, newPoints);
		});
		saveFactionPoints();
		if (Config.DISCORD_ENABLED && Config.DISCORD_FACTION_NOTIFICATIONS_ENABLED && !Config.DISCORD_FACTION_WAR_WEBHOOK.isEmpty())
		{
			DiscordBotManager.getInstance().sendFactionWarInfo(); // Cập nhật khi điểm số thay đổi
		}
	}
	
	public void onEliteBossKill(PlayerInstance player)
	{
		if (!Config.FACTION_WAR_ENABLED || !isWarActive)
			return;
		if (player.getLevel() < Config.FACTION_WAR_MIN_LEVEL)
			return;
		Collection<PlayerInstance> players = getAroundPlayers(player, player.getFaction());
		// Loại bỏ chính player khỏi danh sách players để tránh trùng lặp
		players.remove(player);
		// Cộng điểm cho những người chơi xung quanh
		for (PlayerInstance p : players)
		{
			addPersonalPoints(p, Config.FACTION_WAR_ELITE_BOSS_KILL_POINTS, "killing Elite Boss");
		}
		// Cộng điểm cho người tiêu diệt Elite Boss
		addPersonalPoints(player, Config.FACTION_WAR_ELITE_BOSS_KILL_POINTS, "killing Elite Boss");
		// Cộng điểm cho phe
		int points = Config.FACTION_WAR_ELITE_BOSS_KILL_POINTS;
		totalFactionPoints.compute(player.getFaction(), (k, v) ->
		{
			int newPoints = (v == null ? 0 : v) + points;
			return Math.max(0, newPoints);
		});
		Faction opposingFaction = (player.getFaction() == Faction.FIRE) ? Faction.WATER : Faction.FIRE;
		totalFactionPoints.compute(opposingFaction, (k, v) ->
		{
			int newPoints = (v == null ? 0 : v) - points;
			return Math.max(0, newPoints);
		});
		saveFactionPoints();
		if (Config.DISCORD_ENABLED && Config.DISCORD_FACTION_NOTIFICATIONS_ENABLED && !Config.DISCORD_FACTION_WAR_WEBHOOK.isEmpty())
		{
			DiscordBotManager.getInstance().sendFactionWarInfo(); // Cập nhật khi điểm số thay đổi
		}
	}
	
	public void onZoneParticipation(PlayerInstance player, String zoneName)
	{
		if (!Config.FACTION_WAR_ENABLED || !isWarActive)
			return;
		if (player.getLevel() < Config.FACTION_WAR_MIN_LEVEL)
			return;
		addPersonalPoints(player, 5, "participating in activated zone " + zoneName);
		int points = 5;
		totalFactionPoints.compute(player.getFaction(), (k, v) ->
		{
			int newPoints = (v == null ? 0 : v) + points;
			return Math.max(0, newPoints);
		});
		Faction opposingFaction = (player.getFaction() == Faction.FIRE) ? Faction.WATER : Faction.FIRE;
		totalFactionPoints.compute(opposingFaction, (k, v) ->
		{
			int newPoints = (v == null ? 0 : v) - points;
			return Math.max(0, newPoints);
		});
		saveFactionPoints();
		if (Config.DISCORD_ENABLED && Config.DISCORD_FACTION_NOTIFICATIONS_ENABLED && !Config.DISCORD_FACTION_WAR_WEBHOOK.isEmpty())
		{
			DiscordBotManager.getInstance().sendFactionWarInfo(); // Cập nhật khi điểm số thay đổi
		}
	}
	
	public void onDefendOutpost(PlayerInstance player, String zoneName)
	{
		if (!Config.FACTION_WAR_ENABLED || !isWarActive)
			return;
		if (player.getLevel() < Config.FACTION_WAR_MIN_LEVEL)
			return;
		addPersonalPoints(player, 10, "defending outpost in zone " + zoneName);
		int points = 10;
		totalFactionPoints.compute(player.getFaction(), (k, v) ->
		{
			int newPoints = (v == null ? 0 : v) + points;
			return Math.max(0, newPoints);
		});
		Faction opposingFaction = (player.getFaction() == Faction.FIRE) ? Faction.WATER : Faction.FIRE;
		totalFactionPoints.compute(opposingFaction, (k, v) ->
		{
			int newPoints = (v == null ? 0 : v) - points;
			return Math.max(0, newPoints);
		});
		saveFactionPoints();
		if (Config.DISCORD_ENABLED && Config.DISCORD_FACTION_NOTIFICATIONS_ENABLED && !Config.DISCORD_FACTION_WAR_WEBHOOK.isEmpty())
		{
			DiscordBotManager.getInstance().sendFactionWarInfo(); // Cập nhật khi điểm số thay đổi
		}
	}
	
	public boolean isWarActive()
	{
		return isWarActive;
	}
	
	private Collection<PlayerInstance> getAroundPlayers(Creature actor, Faction faction)
	{
		return World.getInstance().getVisibleObjectsInRange(actor, PlayerInstance.class, 2000).stream().filter(p -> p.getFaction() == faction).filter(p -> p.getLevel() >= Config.FACTION_WAR_MIN_LEVEL).collect(Collectors.toList());
	}
	
	public List<PlayerRanking> getPlayerRankings(Faction faction)
	{
		return playerRankings.getOrDefault(faction, new ArrayList<>());
	}
	
	public List<PlayerRanking> getGlobalRankings()
	{
		// If war is active, return real-time rankings
		if (isWarActive)
		{
			return getCurrentSessionRankings();
		}
		// If war is not active, return last session rankings
		return new ArrayList<>(globalRankings);
	}

	/**
	 * Get real-time rankings for current session
	 */
	private List<PlayerRanking> getCurrentSessionRankings()
	{
		List<PlayerRanking> currentRankings = new ArrayList<>();
		for (PlayerInstance player : World.getInstance().getPlayers())
		{
			if (player.getFaction() != Faction.NONE && player.getPersonalPoints() > 0)
			{
				currentRankings.add(new PlayerRanking(player, player.getPersonalPoints()));
			}
		}
		currentRankings.sort(Comparator.comparingInt(PlayerRanking::getPoints).reversed());
		return currentRankings;
	}
	
	public class TimeSlot
	{
		private final int	startHour;
		private final int	startMinute;
		private final int	endHour;
		private final int	endMinute;
		
		public TimeSlot(int startHour, int startMinute, int endHour, int endMinute)
		{
			this.startHour = startHour;
			this.startMinute = startMinute;
			this.endHour = endHour;
			this.endMinute = endMinute;
		}
		
		public int getStartHour()
		{
			return startHour;
		}
		
		public int getStartMinute()
		{
			return startMinute;
		}
		
		public int getEndHour()
		{
			return endHour;
		}
		
		public int getEndMinute()
		{
			return endMinute;
		}
		
		public long getStartTimeInMillis(long now)
		{
			Calendar calendar = Calendar.getInstance();
			calendar.setTimeInMillis(now);
			calendar.set(Calendar.HOUR_OF_DAY, startHour);
			calendar.set(Calendar.MINUTE, startMinute);
			calendar.set(Calendar.SECOND, 0);
			calendar.set(Calendar.MILLISECOND, 0);
			if (calendar.getTimeInMillis() <= now)
			{
				calendar.add(Calendar.DAY_OF_MONTH, 1);
			}
			return calendar.getTimeInMillis();
		}
		
		public long getEndTimeInMillis(long now)
		{
			Calendar calendar = Calendar.getInstance();
			calendar.setTimeInMillis(now);
			calendar.set(Calendar.HOUR_OF_DAY, endHour);
			calendar.set(Calendar.MINUTE, endMinute);
			calendar.set(Calendar.SECOND, 0);
			calendar.set(Calendar.MILLISECOND, 0);
			if (calendar.getTimeInMillis() <= now)
			{
				calendar.add(Calendar.DAY_OF_MONTH, 1);
			}
			return calendar.getTimeInMillis();
		}
	}
	
	private void saveFactionWarState()
	{
		try (Connection con = DatabaseFactory.getConnection(); PreparedStatement ps = con.prepareStatement("UPDATE faction_war_state SET is_war_active = ?, current_end_time = ?, last_updated = ? WHERE id = 1"))
		{
			ps.setBoolean(1, isWarActive);
			ps.setLong(2, isWarActive ? (System.currentTimeMillis() + (endTask != null ? endTask.getDelay(TimeUnit.MILLISECONDS) : 0)) : 0);
			ps.setLong(3, System.currentTimeMillis());
			ps.executeUpdate();
			if (Config.LOG_FACTION_DETAILS)
			{
				LOGGER.fine("FactionWarManager: Saved Faction War state: isWarActive=" + isWarActive);
			}
		}
		catch (SQLException e)
		{
			LOGGER.severe("FactionWarManager: Failed to save Faction War state: " + e.getMessage());
		}
	}
	
	private void loadFactionWarState()
	{
		try (Connection con = DatabaseFactory.getConnection(); PreparedStatement ps = con.prepareStatement("SELECT is_war_active, current_end_time, last_updated FROM faction_war_state WHERE id = 1"); ResultSet rs = ps.executeQuery())
		{
			if (rs.next())
			{
				isWarActive = rs.getBoolean("is_war_active");
				long currentEndTime = rs.getLong("current_end_time");
				long lastUpdated = rs.getLong("last_updated");
				long now = System.currentTimeMillis();
				if (isWarActive && currentEndTime > now)
				{
					if (Config.LOG_FACTION_DETAILS)
					{
						LOGGER.info("FactionWarManager: Restored Faction War state: isWarActive=" + isWarActive + ", ending at " + new java.util.Date(currentEndTime));
					}
					// Tìm TimeSlot phù hợp từ Config.FACTION_WAR_TIME_SLOTS
					SimpleDateFormat timeFormat = new SimpleDateFormat("HH:mm");
					boolean foundSlot = false;
					for (int[] slot : Config.FACTION_WAR_TIME_SLOTS)
					{
						Calendar nowCal = Calendar.getInstance();
						nowCal.setTimeInMillis(now);
						Calendar startCal = Calendar.getInstance();
						startCal.setTimeInMillis(now);
						startCal.set(Calendar.HOUR_OF_DAY, slot[0]);
						startCal.set(Calendar.MINUTE, slot[1]);
						startCal.set(Calendar.SECOND, 0);
						startCal.set(Calendar.MILLISECOND, 0);
						Calendar endCal = Calendar.getInstance();
						endCal.setTimeInMillis(now);
						endCal.set(Calendar.HOUR_OF_DAY, slot[2]);
						endCal.set(Calendar.MINUTE, slot[3]);
						endCal.set(Calendar.SECOND, 0);
						endCal.set(Calendar.MILLISECOND, 0);
						// Kiểm tra nếu currentEndTime nằm trong khoảng time slot
						long startTimeToday = startCal.getTimeInMillis();
						long endTimeToday = endCal.getTimeInMillis();
						if (startCal.get(Calendar.DAY_OF_YEAR) > endCal.get(Calendar.DAY_OF_YEAR))
						{
							startCal.add(Calendar.DAY_OF_MONTH, -1);
							startTimeToday = startCal.getTimeInMillis();
						}
						if (currentEndTime >= startTimeToday && currentEndTime <= endTimeToday)
						{
							nearestFactionWarTime = "From " + timeFormat.format(new Date(startTimeToday)) + " to " + timeFormat.format(new Date(endTimeToday));
							foundSlot = true;
							break;
						}
					}
					if (!foundSlot)
					{
						// Fallback: Nếu không tìm thấy slot, hiển thị thời gian kết thúc
						Calendar endCal = Calendar.getInstance();
						endCal.setTimeInMillis(currentEndTime);
						nearestFactionWarTime = "Ends at " + timeFormat.format(new Date(currentEndTime));
						LOGGER.warning("FactionWarManager: No matching time slot found for currentEndTime=" + new Date(currentEndTime) + ". Using fallback display.");
					}
					if (Config.LOG_FACTION_DETAILS)
					{
						LOGGER.info("FactionWarManager: nearestFactionWarTime set to " + nearestFactionWarTime);
					}
					endTask = ThreadPool.schedule(() ->
					{
						if (!Config.FACTION_WAR_ENABLED)
							return;
						endWarSession();
						LOGGER.info("FactionWarManager: Ended Faction War session at " + new java.util.Date());
						startWarSessionCycle();
						saveFactionWarState();
					}, currentEndTime - now);
				}
				else
				{
					isWarActive = false;
					nearestFactionWarTime = "Not Scheduled";
					saveFactionWarState();
					if (Config.LOG_FACTION_DETAILS)
					{
						LOGGER.info("FactionWarManager: Faction War is not active. Set nearestFactionWarTime to Not Scheduled.");
					}
				}
			}
		}
		catch (SQLException e)
		{
			LOGGER.severe("FactionWarManager: Failed to load Faction War state: " + e.getMessage());
		}
	}
	
	public String getFactionWarTime()
	{
		return nearestFactionWarTime;
	}
	
	/**
	 * Get the personal efficiency of a player, represented by their personal points.
	 *
	 * @param player
	 *            The player to get efficiency for.
	 * @return The player's personal points as their efficiency.
	 */
	public int getPersonalEfficiency(PlayerInstance player)
	{
		return player.getPersonalPoints();
	}
	
	private static class SingletonHolder
	{
		protected static final FactionWarManager INSTANCE = new FactionWarManager();
	}
	
	public static FactionWarManager getInstance()
	{
		return SingletonHolder.INSTANCE;
	}
}