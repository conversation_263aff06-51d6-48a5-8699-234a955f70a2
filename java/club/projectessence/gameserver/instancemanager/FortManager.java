/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.instancemanager;

import club.projectessence.commons.database.DatabaseFactory;
import club.projectessence.gameserver.InstanceListManager;
import club.projectessence.gameserver.model.WorldObject;
import club.projectessence.gameserver.model.clan.Clan;
import club.projectessence.gameserver.model.siege.Fort;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.Collection;
import java.util.Map;
import java.util.concurrent.ConcurrentSkipListMap;
import java.util.logging.Level;
import java.util.logging.Logger;

public class FortManager implements InstanceListManager {
	public static final int ORC_FORTRESS_ID = 117;
	public static final int COMBAT_FLAG_ID = 93331;
	public static final int FLAG_MAX_COUNT = 3;
	public static final int ORC_FORTRESS_FLAGPOLE_ID = 23170500;
	private static final Logger LOGGER = Logger.getLogger(FortManager.class.getName());
	private static final Map<Integer, Fort> _forts = new ConcurrentSkipListMap<>();

	public static FortManager getInstance() {
		return SingletonHolder.INSTANCE;
	}

	public Fort findNearestFort(WorldObject obj) {
		return findNearestFort(obj, Long.MAX_VALUE);
	}

	public Fort findNearestFort(WorldObject obj, long maxDistanceValue) {
		Fort nearestFort = getFort(obj);
		if (nearestFort == null) {
			long maxDistance = maxDistanceValue;
			for (Fort fort : _forts.values()) {
				final double distance = fort.getDistance(obj);
				if (maxDistance > distance) {
					maxDistance = (long) distance;
					nearestFort = fort;
				}
			}
		}
		return nearestFort;
	}

	public Fort getFortById(int fortId) {
		for (Fort f : _forts.values()) {
			if (f.getResidenceId() == fortId) {
				return f;
			}
		}
		return null;
	}

	public Fort getFortByOwner(Clan clan) {
		for (Fort f : _forts.values()) {
			if (f.getOwnerClan() == clan) {
				return f;
			}
		}
		return null;
	}

	public Fort getFort(String name) {
		for (Fort f : _forts.values()) {
			if (f.getName().equalsIgnoreCase(name.trim())) {
				return f;
			}
		}
		return null;
	}

	public Fort getFort(int x, int y, int z) {
		for (Fort f : _forts.values()) {
			if (f.checkIfInZone(x, y, z)) {
				return f;
			}
		}
		return null;
	}

	public Fort getFort(WorldObject activeObject) {
		return getFortById(FortManager.ORC_FORTRESS_ID);
		// return getFort(activeObject.getX(), activeObject.getY(), activeObject.getZ());
	}

	public Collection<Fort> getForts() {
		return _forts.values();
	}

	@Override
	public void loadInstances() {
		try (Connection con = DatabaseFactory.getConnection();
		     Statement s = con.createStatement();
		     ResultSet rs = s.executeQuery("SELECT id FROM fort ORDER BY id")) {
			while (rs.next()) {
				final int fortId = rs.getInt("id");
				_forts.put(fortId, new Fort(fortId));
			}

			LOGGER.info(getClass().getSimpleName() + ": Loaded " + _forts.values().size() + " fortress.");
		} catch (Exception e) {
			LOGGER.log(Level.WARNING, "Exception: loadFortData(): " + e.getMessage(), e);
		}
	}

	@Override
	public void updateReferences() {
	}

	@Override
	public void activateInstances() {
		for (Fort fort : _forts.values()) {
			fort.activateInstance();
		}
	}

	private static class SingletonHolder {
		protected static final FortManager INSTANCE = new FortManager();
	}
}
