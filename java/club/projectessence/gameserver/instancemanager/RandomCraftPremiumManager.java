/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.instancemanager;

import club.projectessence.commons.concurrent.ThreadPool;
import club.projectessence.commons.database.DatabaseFactory;
import club.projectessence.gameserver.model.World;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.model.events.Containers;
import club.projectessence.gameserver.model.events.EventType;
import club.projectessence.gameserver.model.events.ListenersContainer;
import club.projectessence.gameserver.model.events.impl.creature.player.OnPlayerLogin;
import club.projectessence.gameserver.model.events.impl.creature.player.OnPlayerLogout;
import club.projectessence.gameserver.model.events.listeners.ConsumerEventListener;
import club.projectessence.gameserver.network.serverpackets.ExShowScreenMessage;
import club.projectessence.gameserver.network.serverpackets.MagicSkillUse;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;
import java.util.logging.Logger;

/**
 * <AUTHOR>
 */
public class RandomCraftPremiumManager {
	private static final Logger LOGGER = Logger.getLogger(RandomCraftPremiumManager.class.getName());

	private static final String LOAD_SQL = "SELECT account_name,enddate FROM account_random_craft_premium WHERE account_name = ?";
	private static final String UPDATE_SQL = "REPLACE INTO account_random_craft_premium (account_name,enddate) VALUE (?,?)";
	private static final String DELETE_SQL = "DELETE FROM account_random_craft_premium WHERE account_name = ?";
	// Data Cache
	private final Map<String, Long> _premiumData = new ConcurrentHashMap<>();
	// expireTasks
	private final Map<String, ScheduledFuture<?>> _expiretasks = new ConcurrentHashMap<>();
	// Listeners
	private final ListenersContainer _listenerContainer = Containers.Players();
	// Moved to EnterWorld
	@SuppressWarnings("unused")
	private final Consumer<OnPlayerLogin> _playerLoginEvent = event ->
	{
		onPlayerLogin(event.getPlayer());
	};
	private final Consumer<OnPlayerLogout> _playerLogoutEvent = event ->
	{
		stopExpireTask(event.getPlayer());
	};

	protected RandomCraftPremiumManager() {
		// _listenerContainer.addListener(new ConsumerEventListener(_listenerContainer, EventType.ON_PLAYER_LOGIN, _playerLoginEvent, this));
		_listenerContainer.addListener(new ConsumerEventListener(_listenerContainer, EventType.ON_PLAYER_LOGOUT, _playerLogoutEvent, this));
	}

	public static RandomCraftPremiumManager getInstance() {
		return SingletonHolder.INSTANCE;
	}

	public void onPlayerLogin(PlayerInstance player) {
		final String accountName = player.getAccountName();
		loadPremiumData(accountName);
		final long now = System.currentTimeMillis();
		final long premiumExpiration = getPremiumExpiration(accountName);
		player.setRandomCraftPremiumStatus(premiumExpiration > now);
		if (player.hasRandomCraftPremiumStatus()) {
			startExpireTask(player, premiumExpiration - now);
		} else if (premiumExpiration > 0) {
			removePremiumStatus(accountName, false);
		}
	}

	/**
	 * @param player
	 * @param delay
	 */
	private void startExpireTask(PlayerInstance player, long delay) {
		_expiretasks.put(player.getAccountName(), ThreadPool.get().schedule(new PremiumExpireTask(player), delay));
	}

	/**
	 * @param player
	 */
	private void stopExpireTask(PlayerInstance player) {
		ScheduledFuture<?> task = _expiretasks.remove(player.getAccountName());
		if (task != null) {
			task.cancel(false);
			task = null;
		}
	}

	public void loadPremiumData(String accountName) {
		try (Connection con = DatabaseFactory.getConnection();
		     PreparedStatement stmt = con.prepareStatement(LOAD_SQL)) {
			stmt.setString(1, accountName);
			try (ResultSet rset = stmt.executeQuery()) {
				while (rset.next()) {
					_premiumData.put(rset.getString(1), rset.getLong(2));
				}
			}
		} catch (SQLException e) {
			LOGGER.warning("Problem with RandomCraftPremiumManager: " + e.getMessage());
		}
	}

	public long getPremiumExpiration(String accountName) {
		return _premiumData.getOrDefault(accountName, 0L);
	}

	public void addPremiumTime(String accountName, int timeValue, TimeUnit timeUnit) {
		final long addTime = timeUnit.toMillis(timeValue);
		final long now = System.currentTimeMillis();
		// new premium task at least from now
		final long oldPremiumExpiration = Math.max(now, getPremiumExpiration(accountName));
		final long newPremiumExpiration = oldPremiumExpiration + addTime;

		// UPDATE DATABASE
		try (Connection con = DatabaseFactory.getConnection();
		     PreparedStatement stmt = con.prepareStatement(UPDATE_SQL)) {
			stmt.setString(1, accountName);
			stmt.setLong(2, newPremiumExpiration);
			stmt.execute();
		} catch (SQLException e) {
			LOGGER.warning("Problem with RandomCraftPremiumManager: " + e.getMessage());
		}

		// UPDATE CACHE
		_premiumData.put(accountName, newPremiumExpiration);

		// UPDATE PlAYER PREMIUMSTATUS
		for (PlayerInstance player : World.getInstance().getPlayers()) {
			if (player.getAccountName().equals(accountName)) {
				stopExpireTask(player);
				startExpireTask(player, newPremiumExpiration - now);
				if (!player.hasRandomCraftPremiumStatus()) {
					player.setRandomCraftPremiumStatus(true);
				}
				player.broadcastPacket(new MagicSkillUse(player, 54205, 1, 0, 0));
				Calendar c = Calendar.getInstance();
				c.setTimeInMillis(getPremiumExpiration(player.getAccountName()));
				Date date = c.getTime();
				SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
				String txt = "Premium expiration date: " + sdf.format(date);
				player.sendPacket(new ExShowScreenMessage(txt, 2, 5000, 0, true, true));
				player.sendMessage(txt);
				break;
			}
		}
	}

	public void removePremiumStatus(String accountName, boolean checkOnline) {
		if (checkOnline) {
			for (PlayerInstance player : World.getInstance().getPlayers()) {
				if (accountName.equals(player.getAccountName()) && player.hasRandomCraftPremiumStatus()) {
					player.setRandomCraftPremiumStatus(false);
					stopExpireTask(player);
				}
				break;

			}
		}

		// UPDATE CACHE
		_premiumData.remove(accountName);

		// UPDATE DATABASE
		try (Connection con = DatabaseFactory.getConnection();
		     PreparedStatement stmt = con.prepareStatement(DELETE_SQL)) {
			stmt.setString(1, accountName);
			stmt.execute();
		} catch (SQLException e) {
			LOGGER.warning("Problem with RandomCraftPremiumManager: " + e.getMessage());
		}
	}

	private static class SingletonHolder {
		protected static final RandomCraftPremiumManager INSTANCE = new RandomCraftPremiumManager();
	}

	class PremiumExpireTask implements Runnable {
		final PlayerInstance _player;

		PremiumExpireTask(PlayerInstance player) {
			_player = player;
		}

		@Override
		public void run() {
			_player.setRandomCraftPremiumStatus(false);
		}
	}
}