/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.instancemanager;

import club.projectessence.gameserver.model.actor.Creature;
import club.projectessence.gameserver.model.holders.WarpedSpaceHolder;
import club.projectessence.gameserver.model.instancezone.Instance;
import club.projectessence.gameserver.util.Util;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 */
public class WarpedSpaceManager {
	private Map<Creature, WarpedSpaceHolder> _warpedSpace = null;

	public static WarpedSpaceManager getInstance() {
		return SingletonHolder.INSTANCE;
	}

	public void addWarpedSpace(Creature creature, int radius) {
		if (_warpedSpace == null) {
			synchronized (this) {
				if (_warpedSpace == null) {
					_warpedSpace = new ConcurrentHashMap<>();
				}
			}
		}
		_warpedSpace.put(creature, new WarpedSpaceHolder(creature, radius));
	}

	public void removeWarpedSpace(Creature creature) {
		_warpedSpace.remove(creature);
	}

	public boolean checkForWarpedSpace(int originX, int originY, int originZ, int destinationX, int destinationY, int destinationZ, Instance instance) {
		if (_warpedSpace != null) {
			for (WarpedSpaceHolder holder : _warpedSpace.values()) {
				final Creature creature = holder.getCreature();
				if (creature.getInstanceWorld() != instance) {
					continue;
				}
				final int radius = creature.getTemplate().getCollisionRadius();
				final boolean originInRange = Util.calculateDistance(creature.getX(), creature.getY(), creature.getZ(), originX, originY, originZ, false, false) <= (holder.getRange() + radius);
				final boolean destinationInRange = Util.calculateDistance(creature.getX(), creature.getY(), creature.getZ(), destinationX, destinationY, destinationZ, false, false) <= (holder.getRange() + radius);
				return destinationInRange ? !originInRange : originInRange;
			}
		}
		return false;
	}

	private static class SingletonHolder {
		protected static final WarpedSpaceManager INSTANCE = new WarpedSpaceManager();
	}
}
