package club.projectessence.gameserver.instancemanager;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.logging.Logger;
import java.util.List;
import java.util.ArrayList;

import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.model.faction.FactionLeaderProgression;
import club.projectessence.gameserver.enums.Faction;
import club.projectessence.gameserver.enums.FactionLeaderStateType;
import club.projectessence.gameserver.enums.LeadershipLevel;
import club.projectessence.gameserver.enums.LeadershipAchievement;
import club.projectessence.gameserver.enums.LeadershipSkill;
import club.projectessence.gameserver.enums.LeadershipTask;

/**
 * Manager for admin monitoring and control of faction leader system.
 */
public class FactionLeaderAdminManager
{
	private static final Logger LOGGER = Logger.getLogger(FactionLeaderAdminManager.class.getName());
	
	// Admin monitoring data
	private final Map<String, AdminAction> _recentActions = new ConcurrentHashMap<>();
	private final Map<Integer, Long> _lastProgressionChange = new ConcurrentHashMap<>();
	private boolean _debugMode = false;
	private long _systemStartTime = System.currentTimeMillis();
	
	protected FactionLeaderAdminManager()
	{
		LOGGER.info("FactionLeaderAdminManager: Initialized.");
	}
	
	public static FactionLeaderAdminManager getInstance()
	{
		return SingletonHolder.INSTANCE;
	}
	
	private static class SingletonHolder
	{
		protected static final FactionLeaderAdminManager INSTANCE = new FactionLeaderAdminManager();
	}
	
	/**
	 * Class to track admin actions
	 */
	public static class AdminAction
	{
		private final String adminName;
		private final String action;
		private final String target;
		private final long timestamp;
		private final String details;
		
		public AdminAction(String adminName, String action, String target, String details)
		{
			this.adminName = adminName;
			this.action = action;
			this.target = target;
			this.details = details;
			this.timestamp = System.currentTimeMillis();
		}
		
		public String getAdminName() { return adminName; }
		public String getAction() { return action; }
		public String getTarget() { return target; }
		public long getTimestamp() { return timestamp; }
		public String getDetails() { return details; }
		
		@Override
		public String toString()
		{
			return String.format("[%s] %s -> %s: %s (%s)", 
				new java.util.Date(timestamp), adminName, target, action, details);
		}
	}
	
	/**
	 * Log an admin action
	 */
	public void logAdminAction(PlayerInstance admin, String action, String target, String details)
	{
		String key = admin.getName() + "_" + System.currentTimeMillis();
		AdminAction adminAction = new AdminAction(admin.getName(), action, target, details);
		_recentActions.put(key, adminAction);
		
		// Keep only last 100 actions
		if (_recentActions.size() > 100)
		{
			String oldestKey = _recentActions.keySet().iterator().next();
			_recentActions.remove(oldestKey);
		}
		
		// Log to console if debug mode
		if (_debugMode)
		{
			LOGGER.info("ADMIN ACTION: " + adminAction.toString());
		}
	}
	
	/**
	 * Get recent admin actions
	 */
	public List<AdminAction> getRecentActions(int limit)
	{
		List<AdminAction> actions = new ArrayList<>(_recentActions.values());
		actions.sort((a, b) -> Long.compare(b.getTimestamp(), a.getTimestamp()));
		
		if (limit > 0 && actions.size() > limit)
		{
			return actions.subList(0, limit);
		}
		return actions;
	}
	
	/**
	 * Get system statistics for admin monitoring
	 */
	public SystemStats getSystemStats()
	{
		SystemStats stats = new SystemStats();
		
		// Basic system info
		stats.systemUptime = System.currentTimeMillis() - _systemStartTime;
		stats.currentState = FactionLeaderManager.getInstance().getState();
		stats.currentCycle = FactionLeaderManager.getInstance().getCycle();
		stats.debugMode = _debugMode;
		
		// Count active leaders and progressions
		stats.activeLeaders = 0;
		stats.totalProgressions = 0;
		stats.maxLevel = 0;
		stats.totalLP = 0;
		stats.totalAchievements = 0;
		stats.totalSkills = 0;
		
		for (Faction faction : Faction.values())
		{
			if (faction == Faction.NONE) continue;
			
			PlayerInstance leader = FactionLeaderManager.getInstance().getLeader(faction);
			if (leader != null)
			{
				stats.activeLeaders++;
				
				FactionLeaderProgression progression = FactionLeaderProgressionManager.getInstance().getProgression(leader.getObjectId());
				if (progression != null)
				{
					stats.totalProgressions++;
					stats.maxLevel = Math.max(stats.maxLevel, progression.getLeadershipLevel());
					stats.totalLP += progression.getLeadershipPoints();
					stats.totalAchievements += progression.getAchievementCount();
					stats.totalSkills += progression.getTotalSkillsLearned();
				}
			}
		}
		
		// Recent activity
		stats.recentActions = _recentActions.size();
		stats.lastActionTime = _recentActions.values().stream()
			.mapToLong(AdminAction::getTimestamp)
			.max()
			.orElse(0);
		
		return stats;
	}
	
	/**
	 * System statistics class
	 */
	public static class SystemStats
	{
		public long systemUptime;
		public FactionLeaderStateType currentState;
		public int currentCycle;
		public boolean debugMode;
		public int activeLeaders;
		public int totalProgressions;
		public int maxLevel;
		public long totalLP;
		public int totalAchievements;
		public int totalSkills;
		public int recentActions;
		public long lastActionTime;
		
		public String getUptimeString()
		{
			long hours = systemUptime / (60 * 60 * 1000);
			long minutes = (systemUptime % (60 * 60 * 1000)) / (60 * 1000);
			return hours + "h " + minutes + "m";
		}
		
		public String getLastActionString()
		{
			if (lastActionTime == 0) return "Never";
			long ago = System.currentTimeMillis() - lastActionTime;
			long minutes = ago / (60 * 1000);
			if (minutes < 1) return "Just now";
			if (minutes < 60) return minutes + " minutes ago";
			long hours = minutes / 60;
			return hours + " hours ago";
		}
	}
	
	/**
	 * Force election state change (admin only)
	 */
	public boolean forceElectionState(PlayerInstance admin, FactionLeaderStateType newState)
	{
		try
		{
			FactionLeaderStateType oldState = FactionLeaderManager.getInstance().getState();
			// This would need to be implemented in FactionLeaderManager
			// FactionLeaderManager.getInstance().setState(newState);
			
			logAdminAction(admin, "FORCE_STATE_CHANGE", "SYSTEM", 
				"Changed from " + oldState + " to " + newState);
			
			admin.sendMessage("✅ Election state changed to: " + newState);
			return true;
		}
		catch (Exception e)
		{
			admin.sendMessage("❌ Failed to change election state: " + e.getMessage());
			return false;
		}
	}
	
	/**
	 * Force new election cycle (admin only)
	 */
	public boolean forceNewElection(PlayerInstance admin)
	{
		try
		{
			int oldCycle = FactionLeaderManager.getInstance().getCycle();
			// This would need to be implemented in FactionLeaderManager
			// FactionLeaderManager.getInstance().startNewCycle();
			
			logAdminAction(admin, "FORCE_NEW_ELECTION", "SYSTEM", 
				"Started new election cycle from " + oldCycle);
			
			admin.sendMessage("✅ New election cycle started!");
			return true;
		}
		catch (Exception e)
		{
			admin.sendMessage("❌ Failed to start new election: " + e.getMessage());
			return false;
		}
	}
	
	/**
	 * Reset election cycle (admin only)
	 */
	public boolean resetElectionCycle(PlayerInstance admin)
	{
		try
		{
			// This would need to be implemented in FactionLeaderManager
			// FactionLeaderManager.getInstance().resetCycle();
			
			logAdminAction(admin, "RESET_ELECTION_CYCLE", "SYSTEM", "Reset election cycle");
			
			admin.sendMessage("✅ Election cycle reset!");
			return true;
		}
		catch (Exception e)
		{
			admin.sendMessage("❌ Failed to reset election cycle: " + e.getMessage());
			return false;
		}
	}
	
	/**
	 * Set faction leader manually (admin only)
	 */
	public boolean setFactionLeader(PlayerInstance admin, PlayerInstance target, Faction faction)
	{
		try
		{
			// This would need to be implemented in FactionLeaderManager
			// FactionLeaderManager.getInstance().setLeader(target, faction);
			
			logAdminAction(admin, "SET_FACTION_LEADER", target.getName(), 
				"Set as leader of " + faction.name());
			
			admin.sendMessage("✅ Set " + target.getName() + " as leader of " + faction.name());
			target.sendMessage("👑 You have been appointed as faction leader by an administrator!");
			return true;
		}
		catch (Exception e)
		{
			admin.sendMessage("❌ Failed to set faction leader: " + e.getMessage());
			return false;
		}
	}
	
	/**
	 * Remove faction leader (admin only)
	 */
	public boolean removeFactionLeader(PlayerInstance admin, Faction faction)
	{
		try
		{
			PlayerInstance currentLeader = FactionLeaderManager.getInstance().getLeader(faction);
			String leaderName = currentLeader != null ? currentLeader.getName() : "Unknown";
			
			// This would need to be implemented in FactionLeaderManager
			// FactionLeaderManager.getInstance().removeLeader(faction);
			
			logAdminAction(admin, "REMOVE_FACTION_LEADER", leaderName, 
				"Removed from " + faction.name());
			
			admin.sendMessage("✅ Removed leader from " + faction.name());
			if (currentLeader != null)
			{
				currentLeader.sendMessage("👑 You have been removed as faction leader by an administrator.");
			}
			return true;
		}
		catch (Exception e)
		{
			admin.sendMessage("❌ Failed to remove faction leader: " + e.getMessage());
			return false;
		}
	}
	
	/**
	 * Emergency stop all faction leader activities
	 */
	public boolean emergencyStop(PlayerInstance admin)
	{
		try
		{
			// Stop all faction leader activities
			// This would need to be implemented
			
			logAdminAction(admin, "EMERGENCY_STOP", "SYSTEM", "Emergency stop activated");
			
			admin.sendMessage("🚨 Emergency stop activated! All faction leader activities halted.");
			return true;
		}
		catch (Exception e)
		{
			admin.sendMessage("❌ Failed to execute emergency stop: " + e.getMessage());
			return false;
		}
	}
	
	/**
	 * Backup progression data
	 */
	public boolean backupProgressionData(PlayerInstance admin)
	{
		try
		{
			// Force save all progression data
			FactionLeaderProgressionManager.getInstance().save();
			
			logAdminAction(admin, "BACKUP_DATA", "SYSTEM", "Manual backup triggered");
			
			admin.sendMessage("✅ Progression data backed up successfully!");
			return true;
		}
		catch (Exception e)
		{
			admin.sendMessage("❌ Failed to backup data: " + e.getMessage());
			return false;
		}
	}
	
	/**
	 * Toggle debug mode
	 */
	public void toggleDebugMode(PlayerInstance admin)
	{
		_debugMode = !_debugMode;
		
		logAdminAction(admin, "TOGGLE_DEBUG", "SYSTEM", "Debug mode: " + _debugMode);
		
		admin.sendMessage("🔧 Debug mode " + (_debugMode ? "enabled" : "disabled"));
		
		if (_debugMode)
		{
			admin.sendMessage("📝 Debug logging is now active. Check console for detailed logs.");
		}
	}
	
	/**
	 * Get debug mode status
	 */
	public boolean isDebugMode()
	{
		return _debugMode;
	}
	
	/**
	 * Send system report to admin
	 */
	public void sendSystemReport(PlayerInstance admin)
	{
		SystemStats stats = getSystemStats();
		
		StringBuilder report = new StringBuilder();
		report.append("=== FACTION LEADER SYSTEM REPORT ===\n");
		report.append("🕐 System Uptime: ").append(stats.getUptimeString()).append("\n");
		report.append("🔄 Current State: ").append(stats.currentState).append("\n");
		report.append("📊 Current Cycle: ").append(stats.currentCycle).append("\n");
		report.append("🔧 Debug Mode: ").append(stats.debugMode ? "ON" : "OFF").append("\n");
		report.append("\n=== STATISTICS ===\n");
		report.append("👑 Active Leaders: ").append(stats.activeLeaders).append("/").append(Faction.values().length - 1).append("\n");
		report.append("📈 Total Progressions: ").append(stats.totalProgressions).append("\n");
		report.append("🏆 Highest Level: ").append(stats.maxLevel).append("\n");
		report.append("💎 Total LP: ").append(stats.totalLP).append("\n");
		report.append("🏅 Total Achievements: ").append(stats.totalAchievements).append("\n");
		report.append("✨ Total Skills: ").append(stats.totalSkills).append("\n");
		report.append("\n=== ADMIN ACTIVITY ===\n");
		report.append("📝 Recent Actions: ").append(stats.recentActions).append("\n");
		report.append("⏰ Last Action: ").append(stats.getLastActionString()).append("\n");
		
		// Recent actions
		List<AdminAction> recentActions = getRecentActions(5);
		if (!recentActions.isEmpty())
		{
			report.append("\n=== LAST 5 ADMIN ACTIONS ===\n");
			for (AdminAction action : recentActions)
			{
				report.append("• ").append(action.toString()).append("\n");
			}
		}
		
		admin.sendMessage(report.toString());
	}
	
	/**
	 * Track progression change for monitoring
	 */
	public void trackProgressionChange(int objId)
	{
		_lastProgressionChange.put(objId, System.currentTimeMillis());
	}
	
	/**
	 * Get last progression change time for a player
	 */
	public long getLastProgressionChange(int objId)
	{
		return _lastProgressionChange.getOrDefault(objId, 0L);
	}
}
