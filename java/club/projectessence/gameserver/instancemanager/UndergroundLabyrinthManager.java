/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.instancemanager;

import club.projectessence.commons.concurrent.ThreadPool;
import club.projectessence.gameserver.model.Location;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.model.effects.EffectFlag;
import club.projectessence.gameserver.model.holders.SkillHolder;
import club.projectessence.gameserver.model.zone.ZoneId;
import club.projectessence.gameserver.network.SystemMessageId;
import club.projectessence.gameserver.network.serverpackets.SystemMessage;

/**
 * <AUTHOR>
 */
public class UndergroundLabyrinthManager {
	// Const
	public static final int KARMA_FOR_PRISON = -150_000;
	public static final int KARMA_AFTER_PRISON = -135_000;
	public static final int KARMA_FOR_WARNING = -100_000; // retail value unknown
	public static final int TOKEN_OF_PROOF = 97783;
	public static final int TOKEN_OF_PROOF_FOR_EXIT = 100;

	// Locations
	private static final Location TELEPORT_LOC = new Location(-81069, -46978, -11472);

	// Skills
	private static final SkillHolder PRISONER_UNIFORM = new SkillHolder(54241, 1);

	protected UndergroundLabyrinthManager() {
	}

	// Called from setReputation
	public void onKarmaReached(PlayerInstance player) {
		final SystemMessage sm = new SystemMessage(SystemMessageId.YOUR_KARMA_HAS_REACHED_S1_YOU_WILL_BE_TRANSPORTED_TO_THE_UNDERGROUND_LABYRINTH);
		sm.addInt(KARMA_FOR_PRISON);
		player.sendPacket(sm);
		PRISONER_UNIFORM.getSkill().applyEffects(player, player); // Apply here, in case player escapes before zone's onEnter triggers
		teleportPlayerToLabyrinth(player);
	}

	private void verifyPlayerInZone(PlayerInstance player) {
		if (!player.isInsideZone(ZoneId.UNDERGROUND_LABYRINTH)) {
			teleportPlayerToLabyrinth(player);
			ThreadPool.get().schedule(() -> verifyPlayerInZone(player), 10000);
		}
	}

	private void teleportPlayerToLabyrinth(PlayerInstance player) {
		player.teleToLocation(TELEPORT_LOC);
		if (player.isDead()) {
			player.doRevive(100);
		}
		ThreadPool.get().schedule(() -> verifyPlayerInZone(player), 10000);
	}

	// Called by UndergroundLabyrinthZone
	public void onPlayerEnterLabyrinth(PlayerInstance player) {
		if (player.getReputation() <= KARMA_FOR_PRISON) {
			PRISONER_UNIFORM.getSkill().applyEffects(player, player);
		}
	}

	// Called by UndergroundLabyrinthZone
	public void onPlayerExitLabyrinth(PlayerInstance player) {
		if (player.isAffected(EffectFlag.UNDERGROUND_LABYRINTH) && player.isOnline() && player.getClient() != null && !player.getClient().isDetached()) {
			player.teleToLocation(TELEPORT_LOC);
		}
	}

	public static UndergroundLabyrinthManager getInstance() {
		return SingletonHolder.INSTANCE;
	}

	private static class SingletonHolder {
		protected static final UndergroundLabyrinthManager INSTANCE = new UndergroundLabyrinthManager();
	}
}
