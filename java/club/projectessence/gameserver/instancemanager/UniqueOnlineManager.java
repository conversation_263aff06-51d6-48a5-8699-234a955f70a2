/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.instancemanager;

import club.projectessence.commons.database.DatabaseFactory;
import club.projectessence.gameserver.model.World;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.network.GameClient;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 */
public class UniqueOnlineManager {
	private static final Set<String> _hwids = ConcurrentHashMap.newKeySet();

	protected UniqueOnlineManager() {
		try (Connection conn = DatabaseFactory.getConnection();
		     PreparedStatement ps = conn.prepareStatement("SELECT * FROM unique_online_hwids")) {
			ResultSet rs = ps.executeQuery();
			while (rs.next()) {
				_hwids.add(rs.getString("hwids"));
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	public static UniqueOnlineManager getInstance() {
		return SingletonHolder.INSTANCE;
	}

	public void addNewHwid(String hwid) {
		if (!_hwids.contains(hwid)) {
			_hwids.add(hwid);
		}
	}

	public void dayReset() {
		SimpleDateFormat formatter = new SimpleDateFormat("dd/MM/yyyy HH:mm:ss");
		Date date = new Date();

		try (Connection conn = DatabaseFactory.getConnection()) {
			try (PreparedStatement ps = conn.prepareStatement("INSERT INTO unique_online (day, val) VALUES (?, ?)")) {
				ps.setString(1, formatter.format(date));
				ps.setInt(2, _hwids.size());
				ps.execute();
			}
			try (PreparedStatement ps = conn.prepareStatement("TRUNCATE TABLE unique_online_hwids")) {
				ps.execute();
			}
		} catch (Exception e) {
			e.printStackTrace();
		}

		_hwids.clear();
		// Use getMacAddress instead of AAC HWID
		for (PlayerInstance player : World.getInstance().getPlayers()) {
			if (player != null) {
				final GameClient client = player.getClient();
				if ((client != null) && !client.isDetached() && (client.getHardwareInfo() != null)) {
					final String hwid = client.getHardwareInfo().getMacAddress();
					if (hwid != null) {
						addNewHwid(hwid);
					}
				}
			}
		}

		// TODO[K] - Guard section start

		// if (StrixPlatform.getInstance().isPlatformEnabled())
		// {
		// for (PlayerInstance player : World.getInstance().getPlayers())
		// {
		// if (player != null)
		// {
		// final GameClient client = player.getClient();
		// if ((client != null) && !client.isDetached())
		// {
		// final StrixClientData strixClientData = client.getStrixClientData();
		// if (strixClientData != null)
		// {
		// addNewHwid(strixClientData.getClientHWID());
		// }
		// }
		// }
		// }
		// }
		// TODO[K] - Guard section end
	}

	public void storeTemp() {
		try (Connection conn = DatabaseFactory.getConnection()) {
			try (PreparedStatement ps = conn.prepareStatement("TRUNCATE TABLE unique_online_hwids")) {
				ps.execute();
			}
			try (PreparedStatement ps = conn.prepareStatement("INSERT INTO unique_online_hwids (hwids) VALUES (?)")) {
				for (String hwid : _hwids) {
					ps.clearParameters();
					ps.setString(1, hwid);
					ps.addBatch();
				}
				ps.executeBatch();
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	private static class SingletonHolder {
		protected static final UniqueOnlineManager INSTANCE = new UniqueOnlineManager();
	}
}