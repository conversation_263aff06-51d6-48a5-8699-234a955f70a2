/*
 * This file is part of the L2J Mobius project.
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.instancemanager;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.Calendar;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.logging.Level;
import java.util.logging.Logger;

import club.projectessence.Config;
import club.projectessence.commons.concurrent.ThreadPool;
import club.projectessence.commons.database.DatabaseFactory;
import club.projectessence.gameserver.data.sql.ClanTable;
import club.projectessence.gameserver.data.xml.DailyMissionData;
import club.projectessence.gameserver.data.xml.L2PassData;
import club.projectessence.gameserver.data.xml.LCoinShopData;
import club.projectessence.gameserver.data.xml.MableGameEventData;
import club.projectessence.gameserver.data.xml.MissionLevelData;
import club.projectessence.gameserver.data.xml.PrimeShopData;
import club.projectessence.gameserver.data.xml.SpecialHuntingZoneData;
import club.projectessence.gameserver.data.xml.VipSystemData;
import club.projectessence.gameserver.enums.SpecialHuntingZoneResetType;
import club.projectessence.gameserver.enums.SpecialHuntingZoneType;
import club.projectessence.gameserver.model.DailyMissionDataHolder;
import club.projectessence.gameserver.model.World;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.model.actor.stat.PlayerStat;
import club.projectessence.gameserver.model.clan.Clan;
import club.projectessence.gameserver.model.clan.ClanMember;
import club.projectessence.gameserver.model.clan.dkp.DkpPlayerInfo;
import club.projectessence.gameserver.model.eventengine.AbstractEvent;
import club.projectessence.gameserver.model.eventengine.AbstractEventManager;
import club.projectessence.gameserver.model.eventengine.ScheduleTarget;
import club.projectessence.gameserver.model.holders.ItemHolder;
import club.projectessence.gameserver.model.holders.SkillHolder;
import club.projectessence.gameserver.model.holders.SpecialHuntingZoneHolder;
import club.projectessence.gameserver.model.holders.SubClassHolder;
import club.projectessence.gameserver.model.itemcontainer.PlayerL2Pass;
import club.projectessence.gameserver.model.itemcontainer.PlayerMissionLevel;
import club.projectessence.gameserver.model.items.instance.ItemInstance;
import club.projectessence.gameserver.model.olympiad.Olympiad;
import club.projectessence.gameserver.model.variables.AccountVariables;
import club.projectessence.gameserver.model.variables.ClanVariables;
import club.projectessence.gameserver.model.variables.PlayerVariables;
import club.projectessence.gameserver.network.serverpackets.ExVoteSystemInfo;
import club.projectessence.gameserver.network.serverpackets.ExWorldChatCnt;
import club.projectessence.gameserver.network.serverpackets.attendance.ExVipAttendanceItemList;
import club.projectessence.gameserver.network.serverpackets.specialhuntingzones.ExTimeRestrictFieldUserChargeResult;

/**
 * <AUTHOR>
 */
public class DailyTaskManager extends AbstractEventManager<AbstractEvent<?>>
{
	private static final Logger	LOGGER		= Logger.getLogger(DailyTaskManager.class.getName());
	private boolean				_isEnabled	= true;
	
	protected DailyTaskManager()
	{}
	
	public static DailyTaskManager getInstance()
	{
		return SingletonHolder.INSTANCE;
	}
	
	@Override
	public void onInitialized()
	{}
	
	public void disable()
	{
		_isEnabled = false;
	}
	
	public void enable()
	{
		_isEnabled = true;
	}
	
	@ScheduleTarget
	public void onReset()
	{
		if (!_isEnabled)
		{
			return;
		}
		long begin = System.currentTimeMillis();
		// @formatter:off
		try
		{
			GlobalVariables.getInstance().set(PlayerVariables.PET_PENDANT_DROP, "N");
			GlobalVariables.getInstance().storeMe();
		}
		catch (Exception e)
		{
			e.printStackTrace();
		}
		try
		{
			GlobalVariables.getInstance().set("lastDailyTask", Calendar.getInstance().get(Calendar.DAY_OF_YEAR));
			GlobalVariables.getInstance().storeMe();
		}
		catch (Exception e)
		{
			e.printStackTrace();
		}
		try
		{
			resetDailyMissionRewards();
		}
		catch (Exception e)
		{
			e.printStackTrace();
		}
		try
		{
			resetDailySkills();
		}
		catch (Exception e)
		{
			e.printStackTrace();
		}
		try
		{
			resetDailyItems();
		}
		catch (Exception e)
		{
			e.printStackTrace();
		}
		try
		{
			resetRecommends();
		}
		catch (Exception e)
		{
			e.printStackTrace();
		}
		try
		{
			resetWorldChatPoints();
		}
		catch (Exception e)
		{
			e.printStackTrace();
		}
		try
		{
			resetTrainingCamp();
		}
		catch (Exception e)
		{
			e.printStackTrace();
		}
		try
		{
			resetLCoinRespawn();
		}
		catch (Exception e)
		{
			e.printStackTrace();
		}
		try
		{
			resetRaidTeleport();
		}
		catch (Exception e)
		{
			e.printStackTrace();
		}
		try
		{
			resetSayhaGrace();
		}
		catch (Exception e)
		{
			e.printStackTrace();
		}
		try
		{
			RankManager.getInstance().dailyUpdate();
		}
		catch (Exception e)
		{
			e.printStackTrace();
		}
		try
		{
			LCoinShopData.getInstance().dailyLimitsReset();
			LCoinShopData.getInstance().monthlyLimitsReset();
		}
		catch (Exception e)
		{
			e.printStackTrace();
		}
		try
		{
			PrimeShopData.getInstance().dailyLimitsReset();
		}
		catch (Exception e)
		{
			e.printStackTrace();
		}
		try
		{
			resetSpecialHuntingZone();
		}
		catch (Exception e)
		{
			e.printStackTrace();
		}
		try
		{
			resetAttendance();
		}
		catch (Exception e)
		{
			e.printStackTrace();
		}
		try
		{
			resetWeeklyClanContribution();
		}
		catch (Exception e)
		{
			e.printStackTrace();
		}
		try
		{
			resetClanDonationLimits();
		}
		catch (Exception e)
		{
			e.printStackTrace();
		}
		try
		{
			resetPetPendantDrop();
		}
		catch (Exception e)
		{
			e.printStackTrace();
		}
		try
		{
			removeClanJoinLimits();
		}
		catch (Exception e)
		{
			e.printStackTrace();
		}
		try
		{
			UniqueOnlineManager.getInstance().dayReset();
		}
		catch (Exception e)
		{
			e.printStackTrace();
		}
		try
		{
			resetL2PassMonth();
		}
		catch (Exception e)
		{
			e.printStackTrace();
		}
		try
		{
			resetDkpActivity();
		}
		catch (Exception e)
		{
			e.printStackTrace();
		}
		try
		{
			resetPrivateStoreHistory();
		}
		catch (Exception e)
		{
			e.printStackTrace();
		}
		try
		{
			resetDyePotenDaily();
		}
		catch (Exception e)
		{
			e.printStackTrace();
		}
		try
		{
			resetMissionLevel();
		}
		catch (Exception e)
		{
			e.printStackTrace();
		}
		try
		{
			resetVip();
		}
		catch (Exception e)
		{
			e.printStackTrace();
		}
		try
		{
			resetItemsReuse();
		}
		catch (Exception e)
		{
			e.printStackTrace();
		}
		try
		{
			resetDailyRewardCTV();
		}
		catch (Exception e)
		{
			e.printStackTrace();
		}
		try
		{
			resetDailyRewardLeaderCTV();
		}
		catch (Exception e)
		{
			e.printStackTrace();
		}
		try
		{
			resetDailyRewardVIP();
		}
		catch (Exception e)
		{
			e.printStackTrace();
		}
		try
		{
			resetAutoResurrectionUsage();
		}
		catch (Exception e)
		{
			e.printStackTrace();
		}
		// @formatter:on
		ThreadPool.get().schedule(() -> onReset(), Math.max(0, 86_400_000 - (System.currentTimeMillis() - begin)));
	}
	
	@ScheduleTarget
	public void onSave()
	{
		if (!_isEnabled)
		{
			return;
		}
		// Avoid conflict with daily reset task (6:30)
		final Calendar c = Calendar.getInstance();
		if (c.get(Calendar.HOUR_OF_DAY) == 6 && (c.get(Calendar.MINUTE) >= 25 && c.get(Calendar.MINUTE) <= 35))
		{
			ThreadPool.get().schedule(this::onSave, 1_800_000);
			return;
		}
		long begin = System.currentTimeMillis();
		Calendar cal = Calendar.getInstance();
		int lastReset = GlobalVariables.getInstance().getInt("lastDailyTask", -1);
		if (lastReset < 0)
		{
			GlobalVariables.getInstance().set("lastDailyTask", cal.get(Calendar.DAY_OF_YEAR));
		}
		else if ((cal.get(Calendar.HOUR_OF_DAY) >= 7) && (lastReset < cal.get(Calendar.DAY_OF_YEAR)))
		{
			LOGGER.info("calling DailyTaskManager.onReset(), because it was not called at 6:30");
			onReset();
		}
		long startTime = System.currentTimeMillis();
		VengeanceManager.getInstance().save();
		LOGGER.info("VengeanceManager.getInstance().save() took " + (System.currentTimeMillis() - startTime) + " ms.");
		startTime = System.currentTimeMillis();
		LCoinShopData.getInstance().save();
		LOGGER.info("LCoinShopData.getInstance().save() took " + (System.currentTimeMillis() - startTime) + " ms.");
		startTime = System.currentTimeMillis();
		PrimeShopData.getInstance().save();
		LOGGER.info("PrimeShopData.getInstance().save() took " + (System.currentTimeMillis() - startTime) + " ms.");
		startTime = System.currentTimeMillis();
		GlobalVariables.getInstance().set("LOOT_BOXES_OPEN", World.LOOT_BOXES_OPENED);
		GlobalVariables.getInstance().set("L_COINS_FARMED", World.L_COINS_FARMED);
		// Event Sibi Stats
		GlobalVariables.getInstance().set("BALTHUS_KNIGHT_MARK_FARMED", World.BALTHUS_KNIGHT_MARK_FARMED);
		GlobalVariables.getInstance().set("LIFE_CONTROL_TOWER_FARMED", World.LIFE_CONTROL_TOWER_FARMED);
		GlobalVariables.getInstance().set("MID_GRADE_HP_POTION_FARMED", World.MID_GRADE_HP_POTION_FARMED);
		GlobalVariables.getInstance().set("SCROLL_BOOST_ATTACK_FARMED", World.SCROLL_BOOST_ATTACK_FARMED);
		GlobalVariables.getInstance().set("SCROLL_BOOST_DEFENSE_FARMED", World.SCROLL_BOOST_DEFENSE_FARMED);
		GlobalVariables.getInstance().set("SAYHA_COOKIE_FARMED", World.SAYHA_COOKIE_FARMED);
		GlobalVariables.getInstance().set("SAYHA_BLESSING_FARMED", World.SAYHA_BLESSING_FARMED);
		GlobalVariables.getInstance().set("ADENA_TAKE", World.ADENA_TAKE);
		GlobalVariables.getInstance().set("L_COINS_TAKE", World.L_COINS_TAKE);
		GlobalVariables.getInstance().set("SIBI_COIN_TAKE", World.SIBI_COIN_TAKE);
		// GlobalVariables.getInstance().set("WATERMELON_EVENT_ADENA_TAKE", World.WATERMELON_EVENT_ADENA_TAKE);
		// GlobalVariables.getInstance().set("WATERMELON_EVENT_L_COINS_TAKE", World.WATERMELON_EVENT_L_COINS_TAKE);
		GlobalVariables.getInstance().storeMe();
		LOGGER.info("GlobalVariablesManager.getInstance().storeMe() took " + (System.currentTimeMillis() - startTime) + " ms.");
		if (Config.WORLD_EXCHANGE_LAZY_UPDATE)
		{
			WorldExchangeManager.getInstance().storeMe();
		}
		if (Olympiad.getInstance().inCompPeriod())
		{
			startTime = System.currentTimeMillis();
			Olympiad.getInstance().saveOlympiadStatus();
			LOGGER.info("Olympiad System: Data updated.");
			LOGGER.info("Olympiad.getInstance().saveOlympiadStatus() took " + (System.currentTimeMillis() - startTime) + " ms.");
		}
		startTime = System.currentTimeMillis();
		RankManager.getInstance().updateVisualList();
		LOGGER.info("RankManager.getInstance().updateVisualList() took " + (System.currentTimeMillis() - startTime) + " ms.");
		startTime = System.currentTimeMillis();
		MableGameEventData.getInstance().save();
		LOGGER.info("MableGameEventData.getInstance().save() took " + (System.currentTimeMillis() - startTime) + " ms.");
		ThreadPool.get().schedule(this::onSave, Math.max(0, 1_800_000 - (System.currentTimeMillis() - begin)));
	}
	
	@ScheduleTarget
	public void onManualReset()
	{
		GlobalVariables.getInstance().set("lastDailyTask", Calendar.getInstance().get(Calendar.DAY_OF_YEAR));
		GlobalVariables.getInstance().storeMe();
		resetDailyMissionRewards();
		resetDailySkills();
		resetDailyItems();
		resetRecommends();
		resetWorldChatPoints();
		resetTrainingCamp();
		resetLCoinRespawn();
		resetRaidTeleport();
		resetSayhaGrace();
		RankManager.getInstance().dailyUpdate();
		LCoinShopData.getInstance().dailyLimitsReset();
		LCoinShopData.getInstance().monthlyLimitsReset();
		PrimeShopData.getInstance().dailyLimitsReset();
		resetSpecialHuntingZone();
		resetAttendance();
		resetWeeklyClanContribution();
		resetClanDonationLimits();
		resetPetPendantDrop();
		removeClanJoinLimits();
		UniqueOnlineManager.getInstance().dayReset();
		resetVip();
		resetDailyRewardVIP();
		resetDailyRewardCTV();
		resetDailyRewardLeaderCTV();
		resetAutoResurrectionUsage();
	}
	
	@ScheduleTarget
	public void onManualVIPRewardDailyReset()
	{
		GlobalVariables.getInstance().set("lastDailyTask", Calendar.getInstance().get(Calendar.DAY_OF_YEAR));
		GlobalVariables.getInstance().storeMe();
		resetDailyRewardVIP();
	}
	
	public void onManualSave()
	{
		Calendar cal = Calendar.getInstance();
		int lastReset = GlobalVariables.getInstance().getInt("lastDailyTask", -1);
		if (lastReset < 0)
		{
			GlobalVariables.getInstance().set("lastDailyTask", cal.get(Calendar.DAY_OF_YEAR));
		}
		else if ((cal.get(Calendar.HOUR_OF_DAY) >= 7) && (lastReset < cal.get(Calendar.DAY_OF_YEAR)))
		{
			LOGGER.info("calling DailyTaskManager.onReset(), because it was not called at 6:30");
			onReset();
		}
		long startTime = System.currentTimeMillis();
		VengeanceManager.getInstance().save();
		LOGGER.info("VengeanceManager.getInstance().save() took " + (System.currentTimeMillis() - startTime) + " ms.");
		startTime = System.currentTimeMillis();
		LCoinShopData.getInstance().save();
		LOGGER.info("LCoinShopData.getInstance().save() took " + (System.currentTimeMillis() - startTime) + " ms.");
		startTime = System.currentTimeMillis();
		PrimeShopData.getInstance().save();
		LOGGER.info("PrimeShopData.getInstance().save() took " + (System.currentTimeMillis() - startTime) + " ms.");
		startTime = System.currentTimeMillis();
		GlobalVariables.getInstance().storeMe();
		LOGGER.info("GlobalVariablesManager.getInstance().storeMe() took " + (System.currentTimeMillis() - startTime) + " ms.");
		if (Olympiad.getInstance().inCompPeriod())
		{
			startTime = System.currentTimeMillis();
			Olympiad.getInstance().saveOlympiadStatus();
			LOGGER.info("Olympiad System: Data updated.");
			LOGGER.info("Olympiad.getInstance().saveOlympiadStatus() took " + (System.currentTimeMillis() - startTime) + " ms.");
		}
		startTime = System.currentTimeMillis();
		RankManager.getInstance().updateVisualList();
		LOGGER.info("RankManager.getInstance().updateVisualList() took " + (System.currentTimeMillis() - startTime) + " ms.");
	}
	
	@ScheduleTarget
	private void onClanLeaderApply()
	{
		if (!_isEnabled)
		{
			return;
		}
		for (Clan clan : ClanTable.getInstance().getClans())
		{
			if (clan.getNewLeaderId() != 0)
			{
				final ClanMember member = clan.getClanMember(clan.getNewLeaderId());
				if (member == null)
				{
					continue;
				}
				clan.setNewLeader(member);
			}
		}
		LOGGER.info("Clan leaders has been updated.");
	}
	
	private void resetSayhaGrace()
	{
		if (!Config.ENABLE_SAYHA_GRACE)
		{
			return;
		}
		for (PlayerInstance player : World.getInstance().getPlayers())
		{
			player.addSayhaGracePoints(Config.STARTING_SAYHA_GRACE_POINTS, false);
			for (SubClassHolder subclass : player.getSubClasses().values())
			{
				subclass.addSayhaGracePoints(Config.STARTING_SAYHA_GRACE_POINTS);
			}
		}
		try (Connection con = DatabaseFactory.getConnection())
		{
			Map<Integer, Integer> currPoints = new HashMap<>();
			try (PreparedStatement st = con.prepareStatement("SELECT charId, sayha_grace_points FROM character_subclasses"))
			{
				ResultSet rs = st.executeQuery();
				while (rs.next())
				{
					currPoints.put(rs.getInt(1), rs.getInt(2));
				}
			}
			try (PreparedStatement st = con.prepareStatement("UPDATE character_subclasses SET sayha_grace_points = ? WHERE charId = ?"))
			{
				for (Entry<Integer, Integer> entry : currPoints.entrySet())
				{
					st.setInt(1, Math.min(PlayerStat.MAX_SAYHA_GRACE_POINTS, entry.getValue() + Config.STARTING_SAYHA_GRACE_POINTS));
					st.setInt(2, entry.getKey());
					st.addBatch();
				}
				st.executeBatch();
			}
			currPoints.clear();
			try (PreparedStatement st = con.prepareStatement("SELECT charId, sayha_grace_points FROM characters"))
			{
				ResultSet rs = st.executeQuery();
				while (rs.next())
				{
					currPoints.put(rs.getInt(1), rs.getInt(2));
				}
			}
			try (PreparedStatement st = con.prepareStatement("UPDATE characters SET sayha_grace_points = ? WHERE charId = ?"))
			{
				for (Entry<Integer, Integer> entry : currPoints.entrySet())
				{
					st.setInt(1, Math.min(PlayerStat.MAX_SAYHA_GRACE_POINTS, entry.getValue() + Config.STARTING_SAYHA_GRACE_POINTS));
					st.setInt(2, entry.getKey());
					st.addBatch();
				}
				st.executeBatch();
			}
		}
		catch (Exception e)
		{
			LOGGER.log(Level.WARNING, "Error while updating Sayha's Grace", e);
		}
		LOGGER.info("Sayha's Grace resetted");
	}
	
	private void resetSpecialHuntingZone()
	{
		for (SpecialHuntingZoneHolder zone : SpecialHuntingZoneData.getInstance().getSpecialHuntingZones())
		{
			if (zone.getType() != SpecialHuntingZoneType.PUBLIC)
			{
				continue;
			}
			boolean shouldReset = (zone.getResetType() == SpecialHuntingZoneResetType.DAILY) || ((zone.getResetType() == SpecialHuntingZoneResetType.WEEKLY) && (Calendar.getInstance().get(Calendar.DAY_OF_WEEK) == Calendar.MONDAY));
			if (shouldReset)
			{
				try (Connection con = DatabaseFactory.getConnection())
				{
					try (PreparedStatement ps = con.prepareStatement("UPDATE character_variables SET val = '-1' WHERE var = 'SPECIAL_HUNTING_ZONE_REMAINING_TIME_" + zone.getId() + "' AND CAST(val AS SIGNED) < " + (zone.getDefaultTime() * 1000)))
					{
						ps.executeUpdate();
					}
					try (PreparedStatement ps = con.prepareStatement("UPDATE character_variables SET val = '-1' WHERE var = 'SPECIAL_HUNTING_ZONE_EXTENDED_TIME_" + zone.getId() + "'"))
					{
						ps.executeUpdate();
					}
				}
				catch (Exception e)
				{
					LOGGER.warning("Could not reset Special Hunting Zones");
					e.printStackTrace();
				}
				for (PlayerInstance player : World.getInstance().getPlayers())
				{
					if (player.getVariables().getLong("SPECIAL_HUNTING_ZONE_REMAINING_TIME_" + zone.getId(), zone.getDefaultTime() * 1000) < (zone.getDefaultTime() * 1000))
					{
						player.getVariables().set("SPECIAL_HUNTING_ZONE_REMAINING_TIME_" + zone.getId(), -1);
					}
					player.getVariables().set("SPECIAL_HUNTING_ZONE_EXTENDED_TIME_" + zone.getId(), -1);
					if (player.isInSpecialHuntingZone(zone.getId()))
					{
						long remainingTime = player.getVariables().getLong("SPECIAL_HUNTING_ZONE_REMAINING_TIME_" + zone.getId(), zone.getDefaultTime() * 1000);
						remainingTime = remainingTime == -1 ? zone.getDefaultTime() * 1000 : remainingTime;
						if (player.isInSpecialHuntingZone(zone.getId()))
						{
							player.sendPacket(new ExTimeRestrictFieldUserChargeResult(zone.getId(), remainingTime, zone.getExtensionTime(), 0));
						}
					}
				}
			}
		}
		LOGGER.info("Special Hunting zones reset.");
		try (Connection con = DatabaseFactory.getConnection())
		{
			try (PreparedStatement ps = con.prepareStatement("UPDATE character_variables SET val = '0' WHERE var IN ('" + PlayerVariables.TRANSCENDENT_ZONE_USED + "', '" + PlayerVariables.TRANSCENDENT_ZONE_RESETTED + "', '" + PlayerVariables.TRAINING_ZONE_USED + "', '" + PlayerVariables.TRAINING_ZONE_RESETTED + "')"))
			{
				ps.executeUpdate();
			}
		}
		catch (Exception e)
		{
			LOGGER.warning("Could not reset Transcendent Instance Zones");
			e.printStackTrace();
		}
		for (PlayerInstance player : World.getInstance().getPlayers())
		{
			player.getVariables().set(PlayerVariables.TRANSCENDENT_ZONE_USED, false);
			player.getVariables().set(PlayerVariables.TRANSCENDENT_ZONE_RESETTED, 0);
			player.getVariables().set(PlayerVariables.TRAINING_ZONE_USED, false);
			player.getVariables().set(PlayerVariables.TRAINING_ZONE_RESETTED, 0);
		}
		LOGGER.info("Transcendent / Training instance zones reset.");
		try (Connection con = DatabaseFactory.getConnection())
		{
			try (PreparedStatement ps = con.prepareStatement("UPDATE character_variables SET val = '0' WHERE var = '" + PlayerVariables.TARBA_TELEPORTATION_USED + "'"))
			{
				ps.executeUpdate();
			}
			try (PreparedStatement ps = con.prepareStatement("UPDATE character_variables SET val = '0' WHERE var = '" + PlayerVariables.YAND_TELEPORTATION_USED + "'"))
			{
				ps.executeUpdate();
			}
		}
		catch (Exception e)
		{
			LOGGER.warning("Could not reset Tarba / Yand Teleport Variable");
			e.printStackTrace();
		}
		for (PlayerInstance player : World.getInstance().getPlayers())
		{
			player.getVariables().set(PlayerVariables.TARBA_TELEPORTATION_USED, false);
			player.getVariables().set(PlayerVariables.YAND_TELEPORTATION_USED, false);
		}
		LOGGER.info("Tarba / Yand Teleport Variable reset.");
	}
	
	private void resetAttendance()
	{
		for (PlayerInstance player : World.getInstance().getPlayers())
		{
			player.getAccountVariables().remove(PlayerInstance.ATTENDANCE_DATE_VAR);
			if (player.hasPremiumStatus())
			{
				if (Config.ATTENDANCE_POPUP_WINDOW)
				{
					player.sendPacket(new ExVipAttendanceItemList(player));
				}
			}
		}
		try (Connection con = DatabaseFactory.getConnection())
		{
			try (PreparedStatement ps = con.prepareStatement("DELETE FROM account_gsdata WHERE var = ?"))
			{
				ps.setString(1, PlayerInstance.ATTENDANCE_DATE_VAR);
				ps.executeUpdate();
			}
		}
		catch (Exception e)
		{
			LOGGER.warning("Could not reset Special Hunting Zones");
			e.printStackTrace();
		}
		LOGGER.info("Attendance Reset.");
	}
	
	private void resetWeeklyClanContribution()
	{
		if (Calendar.getInstance().get(Calendar.DAY_OF_WEEK) == Calendar.MONDAY)
		{
			for (Clan clan : ClanTable.getInstance().getClans())
			{
				for (ClanMember clanMember : clan.getMembers())
				{
					clan.getVariables().remove(ClanVariables.CLAN_WEEKLY_CONTRIBUTION_VAR + clanMember.getObjectId());
				}
			}
			try (Connection con = DatabaseFactory.getConnection())
			{
				try (PreparedStatement ps = con.prepareStatement("DELETE FROM clan_variables WHERE var LIKE ?"))
				{
					ps.setString(1, ClanVariables.CLAN_WEEKLY_CONTRIBUTION_VAR);
					ps.executeUpdate();
				}
			}
			catch (Exception e)
			{
				LOGGER.warning("Could not reset Weekly clan contribution");
				e.printStackTrace();
			}
			LOGGER.info("Weekly clan contribution reset.");
		}
	}
	
	private void resetClanDonationLimits()
	{
		try (Connection con = DatabaseFactory.getConnection(); PreparedStatement ps = con.prepareStatement("UPDATE character_variables SET val = '0' WHERE var = '" + PlayerVariables.CLAN_DONATIONS_MADE + "'"))
		{
			ps.executeUpdate();
		}
		catch (Exception e)
		{
			LOGGER.log(Level.SEVERE, "Could not reset daily clan donation limits.");
			e.printStackTrace();
		}
		World.getInstance().getPlayers().stream().forEach(player ->
		{
			player.getVariables().set(PlayerVariables.CLAN_DONATIONS_MADE, 0);
		});
		LOGGER.info("Clan donation limits has been resetted.");
	}
	
	private void resetPetPendantDrop()
	{
		try (Connection con = DatabaseFactory.getConnection(); PreparedStatement ps = con.prepareStatement("UPDATE character_variables SET val = ? WHERE var = '" + PlayerVariables.PET_PENDANT_DROP + "'"))
		{
			ps.setString(1, "N");
			ps.executeUpdate();
		}
		catch (Exception e)
		{
			LOGGER.log(Level.SEVERE, "Could not reset daily pet pendant drop.");
			e.printStackTrace();
		}
		World.getInstance().getPlayers().stream().forEach(player ->
		{
			player.getVariables().set(PlayerVariables.PET_PENDANT_DROP, "N");
		});
		LOGGER.info("Clan donation limits has been resetted.");
	}
	
	private void removeClanJoinLimits()
	{
		try (Connection con = DatabaseFactory.getConnection(); PreparedStatement ps = con.prepareStatement("UPDATE character_variables SET val = '0' WHERE var = '" + PlayerVariables.CLAN_JOINED_TODAY + "'"))
		{
			ps.executeUpdate();
		}
		catch (Exception e)
		{
			LOGGER.log(Level.SEVERE, "Could not remove clan join limits.");
			e.printStackTrace();
		}
		World.getInstance().getPlayers().stream().forEach(player ->
		{
			player.getVariables().set(PlayerVariables.CLAN_JOINED_TODAY, false);
		});
		LOGGER.info("Clan join limits has been removed.");
	}
	
	private void resetL2PassMonth()
	{
		Calendar calendar = Calendar.getInstance();
		if (calendar.get(Calendar.DAY_OF_MONTH) != L2PassData.getMonthChangeDay())
		{
			return;
		}
		L2PassData.getInstance().updateSeasonEndDate();
		try (Connection con = DatabaseFactory.getConnection(); PreparedStatement ps = con.prepareStatement("UPDATE l2pass SET points=0, premium=0, sayhas_sustention_time_earned=0, sayhas_sustention_time_used=0, reward_step=0, premium_reward_step=0"))
		{
			ps.executeUpdate();
		}
		catch (Exception e)
		{
			LOGGER.log(Level.SEVERE, "Could not reset L2Pass month.");
			e.printStackTrace();
		}
		World.getInstance().getPlayers().stream().forEach(player ->
		{
			final PlayerL2Pass l2Pass = player.getL2Pass();
			l2Pass.setSayhasSustention(false, false);
			l2Pass.setPoints(0);
			l2Pass.setPremium(false);
			l2Pass.setSayhasSustentionTimeEarned(0);
			l2Pass.setSayhasSustentionTimeUsed(0);
			l2Pass.setRewardStep(0);
			l2Pass.setPremiumRewardStep(0);
		});
		LOGGER.info("L2Pass has been reset.");
	}
	
	public void resetDkpActivity()
	{
		Calendar calendar = Calendar.getInstance();
		if (calendar.get(Calendar.DAY_OF_WEEK) != Calendar.MONDAY)
		{
			return;
		}
		for (Clan clan : ClanTable.getInstance().getClans())
		{
			clan.getDkp().setActivity(0);
			for (DkpPlayerInfo p : clan.getDkp().getPlayersInfo().values())
			{
				p.setActivity(0);
			}
		}
		try (Connection con = DatabaseFactory.getConnection())
		{
			try (PreparedStatement ps = con.prepareStatement("UPDATE clan_dkp_data SET activity=0"))
			{
				ps.executeUpdate();
			}
			try (PreparedStatement ps = con.prepareStatement("UPDATE clan_dkp_player_data SET activity=0"))
			{
				ps.executeUpdate();
			}
		}
		catch (Exception e)
		{
			LOGGER.log(Level.SEVERE, "Could not reset L2Pass month.");
			e.printStackTrace();
		}
	}
	
	public void resetPrivateStoreHistory()
	{
		Calendar calendar = Calendar.getInstance();
		if (calendar.get(Calendar.DAY_OF_WEEK) != Calendar.MONDAY)
		{
			return;
		}
		try
		{
			PrivateStoreHistoryManager.getInstance().reset();
		}
		catch (Exception e)
		{
			LOGGER.log(Level.SEVERE, "Could not reset PrivateStoreHistoryManager Week.");
			e.printStackTrace();
		}
	}
	
	private void resetDailySkills()
	{
		try (Connection con = DatabaseFactory.getConnection())
		{
			final List<SkillHolder> dailySkills = getVariables().getList("reset_skills", SkillHolder.class, Collections.emptyList());
			for (SkillHolder skill : dailySkills)
			{
				try (PreparedStatement ps = con.prepareStatement("DELETE FROM character_skills_save WHERE skill_id=?;"))
				{
					ps.setInt(1, skill.getSkillId());
					ps.execute();
				}
			}
		}
		catch (Exception e)
		{
			LOGGER.log(Level.SEVERE, "Could not reset daily skill reuse: ", e);
		}
		LOGGER.info("Daily skill reuse cleaned.");
		ThreadPool.get().schedule(() -> resetDailySkills(), 86_400_000);
	}
	
	private void resetDailyItems()
	{
		if (!_isEnabled)
		{
			return;
		}
		try (Connection con = DatabaseFactory.getConnection())
		{
			final List<ItemHolder> dailyItems = getVariables().getList("reset_items", ItemHolder.class, Collections.emptyList());
			for (ItemHolder item : dailyItems)
			{
				try (PreparedStatement ps = con.prepareStatement("DELETE FROM character_item_reuse_save WHERE itemId=?;"))
				{
					ps.setInt(1, item.getId());
					ps.execute();
				}
			}
		}
		catch (Exception e)
		{
			LOGGER.log(Level.SEVERE, "Could not reset daily item reuse: ", e);
		}
		LOGGER.info("Daily item reuse cleaned.");
		ThreadPool.get().schedule(() -> resetDailyItems(), 86_400_000);
	}
	
	private void resetWorldChatPoints()
	{
		if (!Config.ENABLE_WORLD_CHAT)
		{
			return;
		}
		// Update data for offline players.
		try (Connection con = DatabaseFactory.getConnection(); PreparedStatement ps = con.prepareStatement("UPDATE character_variables SET val = '0' WHERE var = '" + PlayerVariables.WORLD_CHAT_VARIABLE_NAME + "'"))
		{
			ps.executeUpdate();
		}
		catch (Exception e)
		{
			LOGGER.log(Level.SEVERE, "Could not reset daily world chat points: ", e);
		}
		// Update data for online players.
		World.getInstance().getPlayers().stream().forEach(player ->
		{
			player.setWorldChatUsed(0);
			player.sendPacket(new ExWorldChatCnt(player));
			player.getVariables().storeMe();
		});
		LOGGER.info("Daily world chat points has been resetted.");
	}
	
	private void resetDailyRewardCTV()
	{
		if (!Config.ENABLE_CUSTOM_REWARD_CTV)
		{
			return;
		}
		// Update data for offline players.
		try (Connection con = DatabaseFactory.getConnection(); PreparedStatement ps = con.prepareStatement("UPDATE character_variables SET val = ? WHERE var = '" + PlayerVariables.SALARY_CTV + "'"))
		{
			ps.setString(1, "N");
			ps.executeUpdate();
		}
		catch (Exception e)
		{
			LOGGER.log(Level.SEVERE, "Could not reset daily salary CTV: ", e);
		}
		// Update data for online players.
		World.getInstance().getPlayers().stream().forEach(player ->
		{
			player.getVariables().set(PlayerVariables.SALARY_CTV, "N");
			player.getVariables().storeMe();
		});
		LOGGER.info("Daily reward CTV has been resetted.");
	}
	
	private void resetDailyRewardLeaderCTV()
	{
		if (!Config.ENABLE_CUSTOM_REWARD_CTV)
		{
			return;
		}
		// Update data for offline players.
		try (Connection con = DatabaseFactory.getConnection(); PreparedStatement ps = con.prepareStatement("UPDATE character_variables SET val = ? WHERE var = '" + PlayerVariables.SALARY_LEADER_CTV + "'"))
		{
			ps.setString(1, "N");
			ps.executeUpdate();
		}
		catch (Exception e)
		{
			LOGGER.log(Level.SEVERE, "Could not reset daily salary Leader CTV: ", e);
		}
		// Update data for online players.
		World.getInstance().getPlayers().stream().forEach(player ->
		{
			player.getVariables().set(PlayerVariables.SALARY_LEADER_CTV, "N");
			player.getVariables().storeMe();
		});
		LOGGER.info("Daily reward leader CTV has been resetted.");
	}
	
	private void resetRecommends()
	{
		try (Connection con = DatabaseFactory.getConnection())
		{
			try (PreparedStatement ps = con.prepareStatement("UPDATE character_reco_bonus SET rec_left = ?, rec_have = 0 WHERE rec_have <= 20"))
			{
				ps.setInt(1, 0); // Rec left = 0
				ps.execute();
			}
			try (PreparedStatement ps = con.prepareStatement("UPDATE character_reco_bonus SET rec_left = ?, rec_have = GREATEST(rec_have - 20,0) WHERE rec_have > 20"))
			{
				ps.setInt(1, 0); // Rec left = 0
				ps.execute();
			}
		}
		catch (Exception e)
		{
			LOGGER.log(Level.SEVERE, "Could not reset Recommendations System: ", e);
		}
		World.getInstance().getPlayers().stream().forEach(player ->
		{
			player.setRecomLeft(0);
			player.setRecomHave(player.getRecomHave() - 20);
			player.sendPacket(new ExVoteSystemInfo(player));
			player.broadcastUserInfo();
		});
	}
	
	private void resetTrainingCamp()
	{
		if (Config.TRAINING_CAMP_ENABLE)
		{
			// Update data for offline players.
			try (Connection con = DatabaseFactory.getConnection(); PreparedStatement ps = con.prepareStatement("DELETE FROM account_gsdata WHERE var = ?"))
			{
				ps.setString(1, "TRAINING_CAMP_DURATION");
				ps.executeUpdate();
			}
			catch (Exception e)
			{
				LOGGER.log(Level.SEVERE, "Could not reset Training Camp: ", e);
			}
			// Update data for online players.
			World.getInstance().getPlayers().stream().forEach(player ->
			{
				player.resetTraingCampDuration();
				player.getAccountVariables().storeMe();
			});
			LOGGER.info("Training Camp daily time has been resetted.");
		}
	}
	
	private void resetLCoinRespawn()
	{
		try (Connection con = DatabaseFactory.getConnection(); PreparedStatement ps = con.prepareStatement("UPDATE character_variables SET val = '2' WHERE var IN ('" + PlayerVariables.RESURRECTION_COUNT_FREE + "', '" + PlayerVariables.RESURRECTION_COUNT_ADENA + "', '" + PlayerVariables.RESURRECTION_COUNT_LCOIN + "')"))
		{
			ps.executeQuery();
		}
		catch (Exception e)
		{
			LOGGER.warning("Could not reset L Coin Respawn variables");
			e.printStackTrace();
		}
		for (PlayerInstance player : World.getInstance().getPlayers())
		{
			player.getVariables().set(PlayerVariables.RESURRECTION_COUNT_FREE, 2);
			player.getVariables().set(PlayerVariables.RESURRECTION_COUNT_ADENA, 2);
			player.getVariables().set(PlayerVariables.RESURRECTION_COUNT_LCOIN, 2);
		}
		LOGGER.info("Free L-Coin Resurrection resetted");
	}
	
	private void resetRaidTeleport()
	{
		try (Connection con = DatabaseFactory.getConnection(); PreparedStatement ps = con.prepareStatement("UPDATE character_variables SET val = '0' WHERE var = '" + PlayerVariables.FREE_RAID_TELEPORT_USED + "'"))
		{
			ps.executeUpdate();
		}
		catch (Exception e)
		{
			LOGGER.warning("Could not reset Raid Teleportation variables");
			e.printStackTrace();
		}
		for (PlayerInstance player : World.getInstance().getPlayers())
		{
			player.getVariables().set(PlayerVariables.FREE_RAID_TELEPORT_USED, false);
		}
		LOGGER.info("Free Raid Teleportation resetted");
	}
	
	private void resetDyePotenDaily()
	{
		String[] temp1 = new String[4];
		String[] temp2 = new String[4];
		for (int slot = 0; slot < 4; slot++)
		{
			temp1[slot] = AccountVariables.DYE_POTENTIAL_LOCAL_STEP + (slot + 1);
			temp2[slot] = AccountVariables.DYE_POTENTIAL_LOCAL_COUNT + (slot + 1);
		}
		try (Connection con = DatabaseFactory.getConnection(); PreparedStatement ps = con.prepareStatement("DELETE FROM account_gsdata WHERE `var` IN (?,?,?,?,?,?,?,?,?,?)"))
		{
			ps.setString(1, AccountVariables.DYE_POTENTIAL_DAILY_STEP);
			ps.setString(2, AccountVariables.DYE_POTENTIAL_DAILY_COUNT);
			for (int slot = 0; slot < 4; slot++)
			{
				ps.setString(3 + (slot * 2), temp1[slot]);
				ps.setString(4 + (slot * 2), temp2[slot]);
			}
			ps.execute();
		}
		catch (Exception e)
		{
			LOGGER.warning("Could not reset Dye Potential variables");
			e.printStackTrace();
		}
		for (PlayerInstance player : World.getInstance().getPlayers())
		{
			player.getAccountVariables().remove(AccountVariables.DYE_POTENTIAL_DAILY_STEP);
			player.getAccountVariables().remove(AccountVariables.DYE_POTENTIAL_DAILY_COUNT);
			for (int slot = 0; slot < 4; slot++)
			{
				player.getAccountVariables().remove(temp1[slot]);
				player.getAccountVariables().remove(temp2[slot]);
			}
		}
		LOGGER.info("Dye Potential variables reset.");
	}
	
	private void resetMissionLevel()
	{
		final Calendar c = Calendar.getInstance();
		if (c.get(Calendar.DAY_OF_MONTH) != 1)
		{
			return;
		}
		try (Connection con = DatabaseFactory.getConnection(); PreparedStatement ps = con.prepareStatement("UPDATE character_mission_level SET points=0, level=0, baseRewardLevelClaimed=0, keyRewardLevelClaimed=0, extraRewardClaimed=0, specialRewardClaimed=0"))
		{
			ps.executeUpdate();
		}
		catch (Exception e)
		{
			LOGGER.warning("Could not reset Mission Level.");
			e.printStackTrace();
		}
		for (PlayerInstance player : World.getInstance().getPlayers())
		{
			PlayerMissionLevel data = player.getMissionLevel();
			data.setPoints(0);
			data.setLevel(0);
			data.setBaseRewardLevelClaimed(0);
			data.setKeyRewardLevelClaimed(0);
			data.setExtraRewardLevelClaimed(0);
			data.setSpecialRewardClaimed(false);
		}
		MissionLevelData.getInstance().updateSeason();
		LOGGER.info("Mission Level reset.");
	}
	
	private void resetVip()
	{
		// Delete all entries for received gifts
		AccountVariables.deleteVipPurchases(AccountVariables.VIP_ITEM_BOUGHT);
		// Checks the tier expiration for online players
		// offline players get handled on next time they log in.
		for (PlayerInstance player : World.getInstance().getPlayers())
		{
			if (player.getVipTier() > 0)
			{
				VipSystemManager.getInstance().checkVipTierExpiration(player);
			}
			player.getAccountVariables().restoreMe();
		}
	}
	
	private void resetDailyRewardVIP()
	{
		if (!VipSystemData.getInstance().isEnabled())
		{
			return;
		}
		// Update data for offline players.
		try (Connection con = DatabaseFactory.getConnection(); PreparedStatement ps = con.prepareStatement("DELETE FROM account_gsdata WHERE var = ?"))
		{
			ps.setString(1, AccountVariables.VIP_ITEM_REWARD);
			ps.executeUpdate();
		}
		catch (Exception e)
		{
			LOGGER.log(Level.SEVERE, "Could not reset Daily VIP Reward: ", e);
		}
		// Update data for online players.
		World.getInstance().getPlayers().stream().forEach(player ->
		{
			player.resetDailyVIPReward();
			player.getAccountVariables().storeMe();
		});
		LOGGER.info("VIP Reward daily time has been resetted.");
	}
	
	private void resetItemsReuse()
	{
		int itemId = 49782;
		try (Connection con = DatabaseFactory.getConnection(); PreparedStatement ps = con.prepareStatement("DELETE FROM character_item_reuse_save WHERE itemId=?"))
		{
			ps.setInt(1, itemId);
			ps.execute();
		}
		catch (Exception e)
		{
			LOGGER.warning("Could not reset Mission Level.");
			e.printStackTrace();
		}
		for (PlayerInstance player : World.getInstance().getPlayers())
		{
			ItemInstance item = player.getInventory().getItemByItemId(itemId);
			if (item == null)
			{
				item = player.getWarehouse().getItemByItemId(itemId);
			}
			if (item != null)
			{
				player.addTimeStampItem(item, 0, System.currentTimeMillis());
			}
		}
		LOGGER.info("Items reuse reset.");
	}
	
	private void resetDailyMissionRewards()
	{
		DailyMissionData.getInstance().getDailyMissionData().forEach(DailyMissionDataHolder::reset);
	}

	/**
	 * Resets auto-resurrection daily usage for all players.
	 */
	private void resetAutoResurrectionUsage()
	{
		if (!club.projectessence.Config.AUTOPLAY_AUTO_RESURRECTION_ENABLED)
		{
			return;
		}

		// Reset for all players in database
		try (Connection con = DatabaseFactory.getConnection())
		{
			// Reset daily usage count for all players
			try (PreparedStatement ps = con.prepareStatement("UPDATE character_variables SET val = '0' WHERE var = ?"))
			{
				ps.setString(1, club.projectessence.gameserver.model.variables.PlayerVariables.AUTO_RESURRECTION_USES_TODAY);
				ps.executeUpdate();
			}

			// Note: Consecutive deaths tracking removed - no longer needed
		}
		catch (Exception e)
		{
			LOGGER.log(Level.SEVERE, "Could not reset auto-resurrection usage in database: ", e);
		}

		int resetCount = 0;

		// Update data for online players
		for (club.projectessence.gameserver.model.actor.instance.PlayerInstance player : club.projectessence.gameserver.model.World.getInstance().getPlayers())
		{
			if (player != null && player.isOnline())
			{
				player.getAutoPlaySettings().setAutoResurrectionUsesToday(player, 0);
				resetCount++;
			}
		}

		LOGGER.info("Auto-resurrection daily usage reset completed for " + resetCount + " online players and all offline players in database.");
	}

	private static class SingletonHolder
	{
		protected static final DailyTaskManager INSTANCE = new DailyTaskManager();
	}
}
