/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.network;

import club.projectessence.Config;
import club.projectessence.commons.concurrent.ThreadPool;

import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArraySet;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledFuture;
import java.util.function.Consumer;
import java.util.logging.Logger;

/**
 * <AUTHOR>
 */
public class IpBlockManager {
	protected static final Logger LOGGER = Logger.getLogger(IpBlockManager.class.getName());

	private static final Map<String, Integer> _maliciousIps = new ConcurrentHashMap<>();
	private static final Set<String> _queue = new CopyOnWriteArraySet<>();
	private static final Set<String> _blocked = new CopyOnWriteArraySet<>();
	private static final String VARITI[] =
			{
					// "************/24",
					// "*************/24",
					// "************/24",
					// "************/24"
					"91.191.183.",
					"185.165.120.",
					"185.203.74.",
					"185.203.75."
			};
	public static boolean IS_ENABLED = false;
	public static long INTERVAL = 1000;
	private ScheduledFuture<?> _task = null;

	private IpBlockManager() {
		restartTask();
	}

	public static IpBlockManager getInstance() {
		return SingletonHolder.INSTANCE;
	}

	public void restartTask() {
		if (_task != null) {
			_task.cancel(true);
			_task = null;
		}
		_task = ThreadPool.get().scheduleAtFixedRate(() ->
		{
			Set<String> removed = new HashSet<>();
			for (String ip : _queue) {
				if (!_blocked.contains(ip)) {
					blockIpTable(ip);
					removed.add(ip);
					_blocked.add(ip);
				}
			}
			_queue.removeAll(removed);
		}, INTERVAL, INTERVAL);
	}

	public void takeActions(String ip) {
		if (IS_ENABLED) {
			if ((ip != null) && !ip.isEmpty()) {
				Integer count = _maliciousIps.get(ip);
				count = count == null ? 1 : count + 1;
				if (count >= 3) {
					blockIp(ip);
				}
				_maliciousIps.put(ip, count);
			}
		}
	}

	private void blockIp(String ip) {
		if (!isVariti(ip)) {
			if (!_queue.contains(ip)) {
				_queue.add(ip);
			}
		}
	}

	private void blockIpTable(String ip) {
		try {
			Process process;
			if (Config.IS_WINDOWS) {
				return;
				// process = Runtime.getRuntime().exec(String.format("cmd.exe /c 123", homeDirectory));
			}
			// else
			// {
			process = Runtime.getRuntime().exec(String.format("sudo iptables -I INPUT -s %s -j DROP", ip));
			// }
			StreamGobbler streamGobbler = new StreamGobbler(process.getInputStream(), System.out::println);
			Executors.newSingleThreadExecutor().submit(streamGobbler);
			LOGGER.warning("Blocked IP: " + ip);
		} catch (Exception e) {
			LOGGER.warning("Failed blocking IP: " + ip);
			e.printStackTrace();
		}
	}

	private boolean isVariti(String ip) {
		for (String varitiIp : VARITI) {
			if (ip.contains(varitiIp)) {
				return true;
			}
		}
		return false;
	}

	private static class StreamGobbler implements Runnable {
		private final InputStream inputStream;
		private final Consumer<String> consumer;

		public StreamGobbler(InputStream inputStream, Consumer<String> consumer) {
			this.inputStream = inputStream;
			this.consumer = consumer;
		}

		@Override
		public void run() {
			new BufferedReader(new InputStreamReader(inputStream)).lines().forEach(consumer);
		}
	}

	private static class SingletonHolder {
		protected static final IpBlockManager INSTANCE = new IpBlockManager();
	}

}