/*
 * Copyright © 2019-2021 L2JOrg
 *
 * This file is part of the L2JOrg project.
 *
 * L2JOrg is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * L2JOrg is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.network;

import club.projectessence.Config;
import club.projectessence.commons.network.codecs.ClientPacketError;
import club.projectessence.gameserver.enums.ChatType;
import club.projectessence.gameserver.network.serverpackets.CreatureSay;
import io.github.joealisson.mmocore.PacketHandler;
import io.github.joealisson.mmocore.ReadableBuffer;
import io.github.joealisson.mmocore.ReadablePacket;

import java.util.logging.Logger;

import static java.lang.Byte.toUnsignedInt;
import static java.util.Objects.isNull;
import static java.util.Objects.nonNull;

/**
 * <AUTHOR>
 */
public class ClientPacketHandler implements PacketHandler<GameClient> {

	private static final Logger LOGGER = Logger.getLogger(ClientPacketHandler.class.getSimpleName());

	@Override
	public ReadablePacket<GameClient> handlePacket(ReadableBuffer buffer, GameClient client) {
		int opcode = -1;
		ReadablePacket<GameClient> packet = null;
		try {
			opcode = toUnsignedInt(buffer.readByte());
			boolean isExtendedPacket = opcode == 0xD0;
			if (!isExtendedPacket && !client.canUsePacket(opcode, 0)) {
				readPacketData(client, buffer, opcode, null);
			} else if (opcode >= IncomingPackets.PACKET_ARRAY.length) {
				readPacketData(client, buffer, opcode, null);
				if (client != null) {
					client.onPacketError(ClientPacketError.UNKNOWN, opcode);
				}
			} else {
				PacketFactory packetFactory = getPacketFactory(client, opcode, buffer);
				packet = makePacketWithFactory(buffer, client, opcode, packetFactory);
			}
		} catch (Exception e) {
			if (!GameClient.ALLOW_CORRUPTED_PACKET && (client != null)) {
				client.onPacketError(ClientPacketError.CORRUPTED, opcode);
			}
			LOGGER.warning("Error handling packet: " + e.getMessage());
			e.printStackTrace();
		}

		if ((packet == null) && !Config.ASYNC_MMOCORE_AUTO_READ && (client != null)) {
			client.readNextPacket();
		}
		return packet;
	}

	private ReadablePacket<GameClient> makePacketWithFactory(ReadableBuffer buffer, GameClient client, int opcode, PacketFactory packetFactory) {
		ReadablePacket<GameClient> packet;
		if (isNull(packetFactory)) {
			readPacketData(client, buffer, opcode, packetFactory);
			return null;
		}
		if (isNull((packet = packetFactory.newIncomingPacket()))) {
			readPacketData(client, buffer, opcode, packetFactory);
			sendDebug(client, opcode, packetFactory, packet);
			return null;
		}

		if (packet instanceof DiscardPacket) {
			return null;
		}

		final ConnectionState connectionState = client.getConnectionState();
		if (!packetFactory.canHandleState(client.getConnectionState())) {
			final byte[] data = new byte[buffer.remaining()];
			buffer.readBytes(data);
			// try
			// {
			// LOGGER.warning("Client " + client + " sent packet " + packetFactory + " at invalid state " + connectionState + " Required States: " + packetFactory.getConnectionStates() + " - [" + toHexString(opcode) + "]: " + CommonUtil.printData(data));
			// }
			// catch (Exception e)
			// {
			// LOGGER.warning("Client " + client + " sent packet " + packetFactory + " at invalid state " + connectionState + " Required States: " + packetFactory.getConnectionStates() + " - [" + toHexString(opcode) + "]");
			// }
			client.onPacketError(ClientPacketError.WRONG_CONNECTION_STATE, opcode);
			return null;
		}
		if (ConnectionState.ENTERING_GAME_AND_IN_GAME.contains(connectionState) && isNull(client.getPlayer())) {
			LOGGER.warning("Client " + client + " sent IN_GAME packet " + packetFactory + " without a player");
			return null;
		}

		sendDebug(client, opcode, packetFactory, packet);

		return packet;
	}

	private void sendDebug(GameClient client, int opcode, PacketFactory packetFactory, ReadablePacket<GameClient> packet) {
		if (client.getPlayer() != null) {
			if (client.getPlayer().isGM() && client.getPlayer().getAdminDebug()) {
				String packetId = String.format("0x%02X", opcode);
				if (packetFactory instanceof ExIncomingPackets) {
					packetId += ", " + String.format("0x%02X", packetFactory.getPacketId());
				}

				String name;
				if (packet != null) {
					name = packet.getClass().getSimpleName();
				} else {
					if (packetFactory instanceof ExIncomingPackets) {
						name = ExIncomingPackets.PACKET_ARRAY[packetFactory.getPacketId()].name();
					} else {
						name = IncomingPackets.PACKET_ARRAY[opcode].name();
					}
				}

				if (!packetId.equals("0x67")) {
					CreatureSay pkt = new CreatureSay(null, ChatType.WORLD, "C", "(" + packetId + ") " + name, 0);
					client.getPlayer().sendPacket(pkt);
				}
			}

			if (Config.PACKET_TRACK_ENABLED && Config.PACKET_TRACK_CLIENT) {
				if (client.getPlayer().getName().equalsIgnoreCase(Config.PACKET_TRACK_NICK)) {
					String name;
					if (packet != null) {
						name = packet.getClass().getSimpleName();
					} else {
						if (packetFactory instanceof ExIncomingPackets) {
							name = ExIncomingPackets.PACKET_ARRAY[packetFactory.getPacketId()].name();
						} else {
							name = IncomingPackets.PACKET_ARRAY[opcode].name();
						}
					}

					LOGGER.info("C -> " + name);
				}
			}
		}
	}

	private void readPacketData(GameClient client, ReadableBuffer buffer, int opcode, PacketFactory packetFactory) {
		final byte[] data = new byte[buffer.remaining()];
		buffer.readBytes(data);
	}

	private PacketFactory getPacketFactory(GameClient client, int opcode, ReadableBuffer buffer) {
		IncomingPackets packetFactory = IncomingPackets.PACKET_ARRAY[opcode];
		if (nonNull(packetFactory) && packetFactory.hasExtension()) {
			return packetFactory.handleExtension(client, buffer);
		}
		return packetFactory;
	}
}
