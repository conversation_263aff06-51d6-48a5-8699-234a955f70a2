/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.network;

import club.projectessence.commons.network.IConnectionState;

import java.util.EnumSet;

/**
 * <AUTHOR>
 */
public enum ConnectionState implements IConnectionState {
	CONNECTED,
	DISCONNECTED,
	CLOSING,
	AUTHENTICATED,
	ENTERING,
	IN_GAME;

	static final EnumSet<ConnectionState> IN_GAME_STATES = EnumSet.of(IN_GAME);
	static final EnumSet<ConnectionState> AUTHENTICATED_STATES = EnumSet.of(AUTHENTICATED);
	static final EnumSet<ConnectionState> CONNECTED_STATES = EnumSet.of(CONNECTED);
	static final EnumSet<ConnectionState> ENTERING_GAME_STATES = EnumSet.of(ENTERING);
	static final EnumSet<ConnectionState> AUTHENTICATED_AND_IN_GAME = EnumSet.of(AUTHENTICATED, IN_GAME);
	static final EnumSet<ConnectionState> ENTERING_GAME_AND_IN_GAME = EnumSet.of(ENTERING, IN_GAME);
	static final EnumSet<ConnectionState> ALL = EnumSet.allOf(ConnectionState.class);
	static final EnumSet<ConnectionState> EMPTY = EnumSet.noneOf(ConnectionState.class);
}
