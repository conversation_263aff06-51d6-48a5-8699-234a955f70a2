/*
 * This file is part of the L2J Mobius project.
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.network;

import static java.util.Objects.nonNull;

import java.util.logging.Logger;

import club.projectessence.commons.concurrent.ThreadPool;
import club.projectessence.gameserver.discord.DiscordBotManager;
import club.projectessence.gameserver.instancemanager.AntiFeedManager;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.model.events.EventDispatcher;
import club.projectessence.gameserver.model.events.impl.creature.player.OnPlayerLogout;
import club.projectessence.gameserver.taskmanager.AttackStanceTaskManager;
import club.projectessence.gameserver.telegram.TelegramBotApi;

/**
 * <AUTHOR>
 */
public class Disconnection
{
	protected static final Logger	LOGGER_ACCOUNTING	= Logger.getLogger("accounting");
	private static final Logger		LOGGER				= Logger.getLogger(Disconnection.class.getName());
	private final GameClient		client;
	private final PlayerInstance	player;
	
	private Disconnection(GameClient client)
	{
		this(client, client.getPlayer());
	}
	
	private Disconnection(PlayerInstance player)
	{
		this(player.getClient(), player);
	}
	
	private Disconnection(GameClient client, PlayerInstance player)
	{
		this.client = getClient(client, player);
		this.player = getPlayer(client, player);
		AntiFeedManager.getInstance().onDisconnect(this.client);
	}
	
	public static Disconnection of(GameClient client)
	{
		return new Disconnection(client);
	}
	
	public static Disconnection of(PlayerInstance player)
	{
		return new Disconnection(player);
	}
	
	public static Disconnection of(GameClient client, PlayerInstance player)
	{
		return new Disconnection(client, player);
	}
	
	private GameClient getClient(GameClient client, PlayerInstance player)
	{
		if (nonNull(client))
		{
			return client;
		}
		if (nonNull(player))
		{
			return player.getClient();
		}
		return null;
	}
	
	private PlayerInstance getPlayer(GameClient client, PlayerInstance player)
	{
		if (nonNull(player))
		{
			return player;
		}
		if (nonNull(client))
		{
			return client.getPlayer();
		}
		return null;
	}
	
	public void logout(boolean toLoginScreen, boolean dontDelete, boolean byPlayersRequest)
	{
		defaultSequence(dontDelete, byPlayersRequest);
		close(toLoginScreen);
	}
	
	public void logout(boolean toLoginScreen, boolean byPlayersRequest)
	{
		logout(toLoginScreen, false, byPlayersRequest);
	}
	
	private void close(boolean toLoginScreen)
	{
		if (nonNull(client))
		{
			client.close(toLoginScreen);
		}
	}
	
	public void restart()
	{
		defaultSequence(true, true);
	}
	
	private void defaultSequence(boolean dontDelete, boolean byPlayersRequest)
	{
		if (!byPlayersRequest && (player != null))
		{
			DiscordBotManager.getInstance().sendDisconnectNotification(player);
			TelegramBotApi.getInstance().getBot().sendDisconnectNotification(player);
		}
		if (nonNull(player))
		{
			EventDispatcher.getInstance().notifyEvent(new OnPlayerLogout(player), player);
		}
		storeMe();
		if (!dontDelete || !client.isDetached())
		{
			deleteMe(true);
		}
	}
	
	private void defaultSequence()
	{
		defaultSequence(true, false);
	}
	
	private void storeMe()
	{
		if (nonNull(player))
		{
			try
			{
				player.storeMe();
			}
			catch (Exception e)
			{
				LOGGER.warning("GameClient storeMe(): " + e.getMessage());
				e.printStackTrace();
			}
		}
	}
	
	private void deleteMe(boolean force)
	{
		if (client != null && !client.isDetached() || force)
		{
			if (nonNull(player) && player.isOnline())
			{
				try
				{
					player.deleteMe();
				}
				catch (Exception e)
				{
					LOGGER.warning("GameClient deleteMe(): " + e.getMessage());
					e.printStackTrace();
				}
			}
			detachPlayerFromClient();
		}
	}
	
	private void detachPlayerFromClient()
	{
		if (nonNull(client))
		{
			client.setPlayer(null);
		}
	}
	
	public void onDisconnection()
	{
		if (player != null)
		{
			ThreadPool.get().schedule(this::defaultSequence, player.canLogout() ? 0 : AttackStanceTaskManager.COMBAT_TIME);
		}
	}
}