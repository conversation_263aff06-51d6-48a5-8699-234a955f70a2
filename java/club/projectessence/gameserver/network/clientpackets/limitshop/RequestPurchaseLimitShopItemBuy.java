/*
 * This file is part of the L2J Mobius project.
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.network.clientpackets.limitshop;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Logger;

import club.projectessence.Config;
import club.projectessence.gameserver.data.ItemTable;
import club.projectessence.gameserver.data.xml.LCoinShopData;
import club.projectessence.gameserver.data.xml.VipSystemData;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.model.actor.request.PrimeShopRequest;
import club.projectessence.gameserver.model.holders.ItemChanceHolder;
import club.projectessence.gameserver.model.holders.LCoinShopProductHolder;
import club.projectessence.gameserver.model.itemcontainer.Inventory;
import club.projectessence.gameserver.model.items.Item;
import club.projectessence.gameserver.model.items.instance.ItemInstance;

import club.projectessence.gameserver.model.variables.PlayerVariables;
import club.projectessence.gameserver.network.SystemMessageId;
import club.projectessence.gameserver.network.clientpackets.ClientPacket;
import club.projectessence.gameserver.network.serverpackets.ExItemAnnounce;
import club.projectessence.gameserver.network.serverpackets.limitshop.ExPurchaseLimitShopItemBuy;
import club.projectessence.gameserver.util.Broadcast;

/**
 * <AUTHOR> Benetis
 */
public class RequestPurchaseLimitShopItemBuy extends ClientPacket
{
	private static final int	L_COIN_SHOP_CATEGORY	= 3;
	private static final int	SPECIAL_CRAFT_CATEGORY	= 4;
	private static final int	CLAN_SHOP_CATEGORY		= 100;
	private int					_category;
	private int					_productId;
	private int					_amount;
	private int					_successItemObjId;
	private int					_materialItemObjId;
	private static Logger		LOGGER_ITEMS			= Logger.getLogger("item");
	
	@Override
	public void readImpl()
	{
		_category = readByte(); // cShopIndex
		_productId = readInt(); // nSlotNum
		_amount = readInt(); // nItemAmount
		_successItemObjId = readInt(); // nSuccessionItemSID (388)
		_materialItemObjId = readInt(); // nMaterialItemSID (388)
	}
	
	@Override
	public void runImpl()
	{
		final PlayerInstance player = client.getPlayer();
		if (player == null)
		{
			return;
		}
		// if (Config.ONLY_CHARACTER_CREATE && !player.isGM())
		// {
		// // player.sendMessage("Server has not started yet!");
		// player.sendPacket(new ExPurchaseLimitShopItemBuy(ExPurchaseLimitShopItemBuy.ExBuyLimitShopReply.ERROR, _category, _productId, null));
		// player.sendPacket(new ExShowScreenMessage("Server has not started yet!", 2, 2000, 0, true, false));
		// // Disconnection.of(client).defaultSequence(true);
		// return;
		// }
		Map<Integer, Integer> results = new HashMap<>();
		if (_amount < 1)
		{
			player.sendPacket(new ExPurchaseLimitShopItemBuy(ExPurchaseLimitShopItemBuy.ExBuyLimitShopReply.ERROR, _category, _productId, results));
			return;
		}
		final LCoinShopProductHolder product = LCoinShopData.getInstance().getProduct(_productId);
		if (product == null)
		{
			player.sendPacket(new ExPurchaseLimitShopItemBuy(ExPurchaseLimitShopItemBuy.ExBuyLimitShopReply.ERROR, _category, _productId, results));
			return;
		}
		if (player.hasItemRequest() || player.hasRequest(PrimeShopRequest.class))
		{
			player.sendPacket(new ExPurchaseLimitShopItemBuy(ExPurchaseLimitShopItemBuy.ExBuyLimitShopReply.ERROR, _category, _productId, results));
			return;
		}
		if (player.isAccountLockedDown())
		{
			player.sendPacket(new ExPurchaseLimitShopItemBuy(ExPurchaseLimitShopItemBuy.ExBuyLimitShopReply.ERROR, _category, _productId, results));
			player.sendMessage("Your account is in lockdown");
			return;
		}
		// Add request.
		player.addRequest(new PrimeShopRequest(player));
		// Check account daily limit.
		if (product.getAccountDailyLimit() > 0)
		{
			int usedLimit = LCoinShopData.getInstance().getDailyLimitsUsed(player.getAccountName(), product.getId());
			if ((product.getAccountDailyLimit() - usedLimit) < _amount)
			{
				player.sendPacket(new ExPurchaseLimitShopItemBuy(ExPurchaseLimitShopItemBuy.ExBuyLimitShopReply.ERROR, _category, _productId, results));
				player.removeRequest(PrimeShopRequest.class);
				return;
			}
		}
		// Check account monthly limit.
		if (product.getAccountMonthlyLimit() > 0)
		{
			int usedLimit = LCoinShopData.getInstance().getMonthlyLimitsUsed(player.getAccountName(), product.getId());
			if ((product.getAccountMonthlyLimit() - usedLimit) < _amount)
			{
				player.sendPacket(new ExPurchaseLimitShopItemBuy(ExPurchaseLimitShopItemBuy.ExBuyLimitShopReply.ERROR, _category, _productId, results));
				player.removeRequest(PrimeShopRequest.class);
				return;
			}
		}
		// Check account limit.
		if (product.getAccountLimit() > 0)
		{
			int usedLimit = LCoinShopData.getInstance().getLimitsUsed(player.getAccountName(), product.getId());
			if ((product.getAccountLimit() - usedLimit) < _amount)
			{
				player.sendPacket(new ExPurchaseLimitShopItemBuy(ExPurchaseLimitShopItemBuy.ExBuyLimitShopReply.ERROR, _category, _productId, results));
				player.removeRequest(PrimeShopRequest.class);
				return;
			}
		}
		// Check level
		if ((product.getMinLevel() > player.getLevel()) || (product.getMaxLevel() < player.getLevel()))
		{
			player.sendPacket(new ExPurchaseLimitShopItemBuy(ExPurchaseLimitShopItemBuy.ExBuyLimitShopReply.ERROR, _category, _productId, results));
			player.removeRequest(PrimeShopRequest.class);
			return;
		}
		// Check clan level
		if (product.getClanLevel() > 0)
		{
			int playerClanLevel = player.getClan() == null ? 0 : player.getClan().getLevel();
			if (product.getClanLevel() > playerClanLevel)
			{
				player.sendPacket(new ExPurchaseLimitShopItemBuy(ExPurchaseLimitShopItemBuy.ExBuyLimitShopReply.ERROR, _category, _productId, results));
				player.removeRequest(PrimeShopRequest.class);
				return;
			}
		}
		if ((product.getSalePeriodEnd() > 0) && (product.getSalePeriodEnd() < System.currentTimeMillis()))
		{
			player.sendPacket(new ExPurchaseLimitShopItemBuy(ExPurchaseLimitShopItemBuy.ExBuyLimitShopReply.ERROR, _category, _productId, results));
			player.removeRequest(PrimeShopRequest.class);
			return;
		}
		// Check existing items.
		for (int i = 0; i < 5; i++)
		{
			if (product.getIngredientIds()[i] == 0)
			{
				continue;
			}
			if (product.getIngredientIds()[i] == Inventory.ADENA_ID)
			{
				if (player.getAdena() < (product.getIngredientQuantities()[i] * _amount))
				{
					player.sendPacket(new ExPurchaseLimitShopItemBuy(ExPurchaseLimitShopItemBuy.ExBuyLimitShopReply.ERROR, _category, _productId, results));
					player.removeRequest(PrimeShopRequest.class);
					return;
				}
			}
			else if (product.getIngredientIds()[i] == -700) // Honor Coins
			{
				if (player.getHonorCoins() < (product.getIngredientQuantities()[i] * _amount))
				{
					player.sendPacket(new ExPurchaseLimitShopItemBuy(ExPurchaseLimitShopItemBuy.ExBuyLimitShopReply.ERROR, _category, _productId, results));
					player.removeRequest(PrimeShopRequest.class);
					return;
				}
			}
			else if (player.getInventory().getInventoryItemCount(product.getIngredientIds()[i], product.getIngredientEnchants()[i], true) < (product.getIngredientQuantities()[i] * _amount))
			{
				player.sendPacket(new ExPurchaseLimitShopItemBuy(ExPurchaseLimitShopItemBuy.ExBuyLimitShopReply.ERROR, _category, _productId, results));
				player.removeRequest(PrimeShopRequest.class);
				return;
			}
		}
		long slots = 0;
		long weight = 0;
		List<ItemChanceHolder> items = new ArrayList<>();
		if ((_category == L_COIN_SHOP_CATEGORY) || (_category == CLAN_SHOP_CATEGORY))
		{
			ItemChanceHolder production = ItemChanceHolder.getRandomHolder(product.getProduction());
			if (production != null)
			{
				items.add(production);
			}
			else
			{
				LOGGER.warning(player + " didn't receive reward for LimitShop productId: " + _productId);
			}
		}
		else if (_category == SPECIAL_CRAFT_CATEGORY)
		{
			for (int i = 0; i < _amount; i++)
			{
				ItemChanceHolder production = ItemChanceHolder.getRandomHolder(player, product.getProduction());
				if (production != null)
				{
					items.add(production);
				}
				else
				{
					LOGGER.warning(player + " didn't receive reward for LimitShop productId: " + _productId);
				}
			}
		}
		else
		{
			LOGGER.warning(player + " unknown category purchase request for Limit Shop: " + _category);
			return;
		}
		Collection<Integer> stackableItems = new ArrayList<>();
		for (ItemChanceHolder i : items)
		{
			Item item = ItemTable.getInstance().getTemplate(i.getId());
			weight += i.getCount() * _amount * item.getWeight();
			if (!item.isStackable())
			{
				// Special craft unstackable items are calculated separately
				if (_category == 4)
				{
					slots += i.getCount();
				}
				else
				{
					slots += i.getCount() * _amount;
				}
			}
			else if ((player.getInventory().getItemByItemId(i.getId()) == null) && !stackableItems.contains(i.getId()))
			{
				slots++;
				stackableItems.add(i.getId());
			}
		}
		stackableItems = null;
		if ((weight > Integer.MAX_VALUE) || (weight < 0) || !player.getInventory().validateWeight((int) weight))
		{
			client.sendPacket(SystemMessageId.YOU_HAVE_EXCEEDED_THE_WEIGHT_LIMIT);
			player.sendPacket(new ExPurchaseLimitShopItemBuy(ExPurchaseLimitShopItemBuy.ExBuyLimitShopReply.FULL_INVENTORY, _category, _productId, results));
			player.sendPacket(SystemMessageId.WEIGHT_LIMIT_NUMBER_OF_ITEMS_LIMIT_HAS_BEEN_EXCEEDED_CANNOT_OBTAIN_THE_ITEM);
			player.removeRequest(PrimeShopRequest.class);
			return;
		}
		if ((slots > Integer.MAX_VALUE) || (slots < 0) || !player.getInventory().validateCapacity((int) slots))
		{
			client.sendPacket(SystemMessageId.YOUR_INVENTORY_IS_FULL);
			player.sendPacket(new ExPurchaseLimitShopItemBuy(ExPurchaseLimitShopItemBuy.ExBuyLimitShopReply.FULL_INVENTORY, _category, _productId, results));
			player.sendPacket(SystemMessageId.WEIGHT_LIMIT_NUMBER_OF_ITEMS_LIMIT_HAS_BEEN_EXCEEDED_CANNOT_OBTAIN_THE_ITEM);
			player.removeRequest(PrimeShopRequest.class);
			return;
		}
		// if (!player.isInventoryUnder80(false))
		// {
		// client.sendPacket(SystemMessageId.YOUR_INVENTORY_IS_FULL);
		// player.sendPacket(new ExPurchaseLimitShopItemBuy(ExPurchaseLimitShopItemBuy.ExBuyLimitShopReply.FULL_INVENTORY, _category, _productId, results));
		// player.sendPacket(SystemMessageId.WEIGHT_LIMIT_NUMBER_OF_ITEMS_LIMIT_HAS_BEEN_EXCEEDED_CANNOT_OBTAIN_THE_ITEM);
		// player.removeRequest(PrimeShopRequest.class);
		// return;
		// }
		// Remove items.
		for (int i = 0; i < 5; i++)
		{
			if (product.getIngredientIds()[i] == 0)
			{
				continue;
			}
			if (product.getIngredientIds()[i] == Inventory.ADENA_ID)
			{
				player.reduceAdena("LCoinShop", product.getIngredientQuantities()[i] * _amount, player, true);
			}
			else if (product.getIngredientIds()[i] == -700) // Honor Coins
			{
				player.addHonorCoins(-((int) (product.getIngredientQuantities()[i] * _amount)));
			}
			else
			{
				if (ItemTable.getInstance().getTemplate(product.getIngredientIds()[i]).isStackable())
				{
					player.destroyItemByItemId("LCoinShop", product.getIngredientIds()[i], product.getIngredientQuantities()[i] * _amount, player, true);
				}
				else
				{
					for (int j = 0; j < (product.getIngredientQuantities()[i] * _amount); j++)
					{
						ItemInstance item = player.getInventory().getAllItemsByItemId(product.getIngredientIds()[i], product.getIngredientEnchants()[i], true).iterator().next();
						player.getInventory().destroyItem("LCoinShop", item, 1, player, player);
					}
				}
			}
		}
		// Custom for Pet Guide Training
		if (_category == SPECIAL_CRAFT_CATEGORY && product.getId() == 3393)
		{
			long giranSealCount = product.getIngredientQuantities()[0] * _amount;
			long oldgiranSealCountVIP = player.getVariables().getLong(PlayerVariables.SPECIAL_CRAFT_TAKE_GIRAN_SEALED, 0);
			player.getVariables().set(PlayerVariables.SPECIAL_CRAFT_TAKE_GIRAN_SEALED, product.getIngredientIds()[0] == Inventory.GIRAN_SEALED_ID ? (oldgiranSealCountVIP + giranSealCount) : 0);
			player.getVariables().storeMe();
		}
		if (_category == SPECIAL_CRAFT_CATEGORY && product.getId() == 3394)
		{
			long giranSealCount2 = product.getIngredientQuantities()[0] * _amount;
			long oldgiranSealCountVIP2 = player.getVariables().getLong(PlayerVariables.SPECIAL_CRAFT_TAKE_GIRAN_SEALED_ADVANCE_PET, 0);
			player.getVariables().set(PlayerVariables.SPECIAL_CRAFT_TAKE_GIRAN_SEALED_ADVANCE_PET, product.getIngredientIds()[0] == Inventory.GIRAN_SEALED_ID ? (oldgiranSealCountVIP2 + giranSealCount2) : 0);
		}
		if (_category == SPECIAL_CRAFT_CATEGORY && product.getId() == 29009)
		{
			long lcoinCount = product.getIngredientQuantities()[2] * _amount;
			long oldLCoinCountVIP = player.getVariables().getLong(PlayerVariables.WATERMELON_LCOIN_USED_BY_PLAYER_VIP, 0);
			player.getVariables().set(PlayerVariables.WATERMELON_LCOIN_USED_BY_PLAYER_VIP, product.getIngredientIds()[2] == Inventory.LCOIN_ID ? (oldLCoinCountVIP + lcoinCount) : 0);
			player.getVariables().storeMe();
		}
		if (_category == L_COIN_SHOP_CATEGORY && VipSystemData.getInstance().getLcoinShopAffect())
		{
			player.updateVipPoints(_amount * 2);
		}
		boolean success = false;
		// Reward.
		if ((_category == L_COIN_SHOP_CATEGORY) || (_category == CLAN_SHOP_CATEGORY))
		{
			for (ItemChanceHolder production : items)
			{
				if (production != null)
				{
					ItemInstance addItem = player.addItem(_category == 100 ? "Clan Shop" : "LCoinShop", production.getId(), production.getCount() * _amount, player, true);
					success = true;
					if (Config.LOG_ITEMS)
					{
						LOGGER_ITEMS.info("CUSTOM_LOG_LCOIN_CLAN_SHOP:" + String.valueOf(_category == L_COIN_SHOP_CATEGORY ? "LCoinShop" : _category == CLAN_SHOP_CATEGORY ? "ClanShop" : "") // in case of null
						+ ", item " + addItem.getObjectId() //
						+ ":" + addItem.getName() + (addItem.getItem().getAdditionalName() == null ? "" : " " + addItem.getItem().getAdditionalName()) //
						+ "[" + addItem.getId() + "]" //
						+ "(" + (production.getCount() * _amount) + "), PrevCount(" //
						+ String.valueOf(player.getInventory().getInventoryItemCount(addItem.getId(), 0) - (production.getCount() * _amount)) + "), " // in case of null
						+ "New Count(" + String.valueOf(player.getInventory().getInventoryItemCount(addItem.getId(), 0)) + "), " + String.valueOf(player) + ", " // in case of null
						+ String.valueOf(player)); // in case of null
					}
				}
			}
		}
		else if (_category == SPECIAL_CRAFT_CATEGORY)
		{
			// final List<ItemChanceHolder> mergedItems = new ArrayList<>();
			// final Map<Integer, Boolean> cache = new HashMap<>();
			// for (ItemChanceHolder production : items)
			// {
			// Boolean isStackable = cache.get(production.getId());
			// if (isStackable == null)
			// {
			// isStackable = ItemTable.getInstance().getTemplate(production.getId()).isStackable();
			// }
			// if (isStackable)
			// {
			// final Optional<ItemChanceHolder> existingOptional = mergedItems.stream().filter(i -> i.getId() == production.getId()).findFirst();
			// if (existingOptional.isPresent())
			// {
			// final ItemChanceHolder existing = existingOptional.get();
			// existing.setCount(existing.getCount() + 1);
			// }
			// else
			// {
			// mergedItems.add(new ItemChanceHolder(production));
			// }
			// }
			// else
			// {
			// mergedItems.add(new ItemChanceHolder(production));
			// }
			// int index = 0;
			// for (ItemChanceHolder holder : product.getProduction())
			// {
			// if ((holder.getId() == production.getId()) && (holder.getCount() == production.getCount()))
			// {
			// break;
			// }
			// index++;
			// }
			// int count = results.getOrDefault(index, 0);
			// results.put(index, count + 1);
			// }
			for (ItemChanceHolder production : items)
			{
				if (production != null)
				{
					ItemInstance newItem = player.addItem("LCoinShop", production.getId(), production.getCount(), player, true);
					success = true;
					if (production.isAnnounced())
					{
						Broadcast.toAllOnlinePlayers(new ExItemAnnounce(player, newItem, ExItemAnnounce.ItemAnnounceType.SPECIAL_CRAFT));
					}
					int index = 0;
					for (ItemChanceHolder holder : product.getProduction())
					{
						if ((holder.getId() == production.getId()) && (holder.getCount() == production.getCount()))
						{
							break;
						}
						index++;
					}
					int count = results.getOrDefault(index, 0);
					results.put(index, count + 1);
					if (Config.LOG_ITEMS)
					{
						LOGGER_ITEMS.info("CUSTOM_LOG_SPECIAL_CRAFT_SHOP:" + String.valueOf("Special Craft") // in case of null
						+ ", item " + newItem.getObjectId() //
						+ ":" + newItem.getName() + (newItem.getItem().getAdditionalName() == null ? "" : " " + newItem.getItem().getAdditionalName()) //
						+ "[" + newItem.getId() + "]" //
						+ "(" + production.getCount() + "), PrevCount(" //
						+ String.valueOf(player.getInventory().getInventoryItemCount(newItem.getId(), 0) - production.getCount()) + "), " // in case of null
						+ "New Count(" + String.valueOf(player.getInventory().getInventoryItemCount(newItem.getId(), 0)) + "), " + String.valueOf(player) + ", " // in case of null
						+ String.valueOf(player)); // in case of null
					}
				}
			}
		}
		// Update player variables.
		if (product.getAccountDailyLimit() > 0)
		{
			LCoinShopData.getInstance().increaseDailyLimitUsed(player.getAccountName(), product.getId(), _amount);
		}
		if (product.getAccountMonthlyLimit() > 0)
		{
			LCoinShopData.getInstance().increaseMonthlyLimitUsed(player.getAccountName(), product.getId(), _amount);
		}
		if (product.getAccountLimit() > 0)
		{
			LCoinShopData.getInstance().increaseLimitUsed(player.getAccountName(), product.getId(), _amount);
		}
		if (success)
		{
			player.sendPacket(new ExPurchaseLimitShopItemBuy(ExPurchaseLimitShopItemBuy.ExBuyLimitShopReply.SUCCESS, _category, _productId, results));
		}
		else
		{
			player.sendPacket(new ExPurchaseLimitShopItemBuy(ExPurchaseLimitShopItemBuy.ExBuyLimitShopReply.ERROR, _category, _productId, results));
		}
		// player.sendPacket(new ExPurchaseLimitShopItemListNew(_category, player));
		// Remove request.
		player.removeRequest(PrimeShopRequest.class);
	}
}
