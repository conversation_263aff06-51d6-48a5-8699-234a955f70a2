/*
 * This file is part of the L2J Mobius project.
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.network;

import static java.util.Objects.isNull;
import static java.util.Objects.nonNull;

import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.locks.ReentrantLock;
import java.util.logging.Level;
import java.util.logging.Logger;

import club.projectessence.Config;
import club.projectessence.commons.concurrent.ThreadPool;
import club.projectessence.commons.database.DatabaseFactory;
import club.projectessence.commons.network.codecs.ClientPacketError;
import club.projectessence.commons.util.HexUtils;
import club.projectessence.gameserver.LoginServerThread;
import club.projectessence.gameserver.LoginServerThread.SessionKey;
import club.projectessence.gameserver.data.sql.CharNameTable;
import club.projectessence.gameserver.data.sql.ClanTable;
import club.projectessence.gameserver.data.xml.SecondaryAuthData;
import club.projectessence.gameserver.enums.CharacterDeleteFailType;
import club.projectessence.gameserver.enums.ClientLanguage;
import club.projectessence.gameserver.instancemanager.CommissionManager;
import club.projectessence.gameserver.instancemanager.MailManager;
import club.projectessence.gameserver.instancemanager.MentorManager;
import club.projectessence.gameserver.instancemanager.QueueManager;
import club.projectessence.gameserver.model.CharSelectInfoPackage;
import club.projectessence.gameserver.model.World;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.model.clan.Clan;
import club.projectessence.gameserver.model.holders.ClientHardwareInfoHolder;
import club.projectessence.gameserver.network.serverpackets.ActionFailed;
import club.projectessence.gameserver.network.serverpackets.CharInfo;
import club.projectessence.gameserver.network.serverpackets.LeaveWorld;
import club.projectessence.gameserver.network.serverpackets.ServerClose;
import club.projectessence.gameserver.network.serverpackets.ServerPacket;
import club.projectessence.gameserver.network.serverpackets.SystemMessage;
import club.projectessence.gameserver.security.SecondaryPasswordAuth;
import club.projectessence.gameserver.util.FloodProtectors;
import club.projectessence.gameserver.util.Util;
import io.github.joealisson.mmocore.Buffer;
import io.github.joealisson.mmocore.Client;
import io.github.joealisson.mmocore.Connection;

/**
 * Represents a client connected on Game Server.
 *
 * <AUTHOR>
 */
public final class GameClient extends Client<Connection<GameClient>>
{
	// Spam protection
	public static final int				ALLOWED_PACKET_COUNT			= 10;
	public static final int				PACKET_TIMEOUT					= 500;
	public static final int				DISCONNECT_IN_CHAR_SELECT_DELAY	= 120_000;
	public static final int				DISCONNECT_IN_CHAR_CREATE_DELAY	= 900_000;
	protected static final Logger		LOGGER							= Logger.getLogger(GameClient.class.getName());
	protected static final Logger		LOGGER_ACCOUNTING				= Logger.getLogger("accounting");
	private static final int			MAX_SPAMED_PACKETS				= 1000;
	private static final int			MAX_PACKETS						= 10000;
	public static boolean				ALLOW_ALL_PACKETS				= true;
	public static boolean				ALLOW_WRONG_CONNECTION_STATE	= true;
	public static boolean				ALLOW_CORRUPTED_PACKET			= false;
	private final FloodProtectors		_floodProtectors				= new FloodProtectors(this);
	private final ReentrantLock			_playerLock						= new ReentrantLock();
	private final Crypt					_crypt;
	private final int					_mergeNameChangeInfo[]			=
	{
		-1,																												// type (0 - char, 1 - clan)
		-1																												// charSelectionSlot
	};
	public boolean						_canSendPackets					= true;
	private String						_accountName;
	private SessionKey					_sessionKey;
	private ConnectionState				_state;
	private PlayerInstance				_player;
	private SecondaryPasswordAuth		_secondaryAuth;
	// TODO[K] - Guard section start
	// public StrixGameCrypt _crypt = null;
	// private StrixClientData clientData;
	// TODO[K] - Guard section end
	private ClientHardwareInfoHolder	_hardwareInfo;
	private List<CharSelectInfoPackage>	_charactersInfo					= null;
	private volatile boolean			_isDetached						= false;
	private boolean						_isAuthedGG;
	private boolean						_protocolOk;
	private int							_protocolVersion;
	private int							_proxyServerId;
	private int[][]						_trace;
	private int							_charSlot						= 0;
	private boolean						_canSkipQueue					= false;
	private ClientLanguage				_lang							= ClientLanguage.EUROPE;
	private Map<Integer, long[]>		_packetsTimes					= new HashMap<>();
	private ScheduledFuture<?>			_limitResetTask;
	private ScheduledFuture<?>			_noPlayerKickTask				= null;
	private int							_spammedPackets					= 0;
	private int							_packetsCount					= 0;
	private long						_lastPacketError				= 0;
	private long						_lastScheduleReset				= System.currentTimeMillis();
	// AAC
	private String						_hwid							= null;
	private String						_fullHwid;
	// Custom Pride.
	private String						_password;
	
	public GameClient(Connection<GameClient> connection)
	{
		super(connection);
		_crypt = new Crypt();
		// TODO[K] - Guard section start
		// _crypt = new StrixGameCrypt();
		// TODO[K] - Guard section end
		_limitResetTask = ThreadPool.get().scheduleAtFixedRate(this::resetLimits, 20_000, 20_000);
		startNoPlayerDisconnectionTask(GameClient.DISCONNECT_IN_CHAR_SELECT_DELAY);
	}
	
	public static void deleteCharByObjId(int objid)
	{
		if (objid < 0)
		{
			return;
		}
		CharNameTable.getInstance().removeName(objid);
		try (java.sql.Connection con = DatabaseFactory.getConnection())
		{
			try (PreparedStatement ps = con.prepareStatement("DELETE FROM character_contacts WHERE charId=? OR contactId=?"))
			{
				ps.setInt(1, objid);
				ps.setInt(2, objid);
				ps.execute();
			}
			try (PreparedStatement ps = con.prepareStatement("DELETE FROM character_friends WHERE charId=? OR friendId=?"))
			{
				ps.setInt(1, objid);
				ps.setInt(2, objid);
				ps.execute();
			}
			try (PreparedStatement ps = con.prepareStatement("DELETE FROM character_hennas WHERE charId=?"))
			{
				ps.setInt(1, objid);
				ps.execute();
			}
			try (PreparedStatement ps = con.prepareStatement("DELETE FROM character_macroses WHERE charId=?"))
			{
				ps.setInt(1, objid);
				ps.execute();
			}
			try (PreparedStatement ps = con.prepareStatement("DELETE FROM character_quests WHERE charId=?"))
			{
				ps.setInt(1, objid);
				ps.execute();
			}
			try (PreparedStatement ps = con.prepareStatement("DELETE FROM character_recipebook WHERE charId=?"))
			{
				ps.setInt(1, objid);
				ps.execute();
			}
			try (PreparedStatement ps = con.prepareStatement("DELETE FROM character_shortcuts WHERE charId=?"))
			{
				ps.setInt(1, objid);
				ps.execute();
			}
			try (PreparedStatement ps = con.prepareStatement("DELETE FROM character_skills WHERE charId=?"))
			{
				ps.setInt(1, objid);
				ps.execute();
			}
			try (PreparedStatement ps = con.prepareStatement("DELETE FROM character_skills_save WHERE charId=?"))
			{
				ps.setInt(1, objid);
				ps.execute();
			}
			try (PreparedStatement ps = con.prepareStatement("DELETE FROM character_subclasses WHERE charId=?"))
			{
				ps.setInt(1, objid);
				ps.execute();
			}
			try (PreparedStatement ps = con.prepareStatement("DELETE FROM heroes WHERE charId=?"))
			{
				ps.setInt(1, objid);
				ps.execute();
			}
			try (PreparedStatement ps = con.prepareStatement("DELETE FROM olympiad_nobles WHERE charId=?"))
			{
				ps.setInt(1, objid);
				ps.execute();
			}
			try (PreparedStatement ps = con.prepareStatement("DELETE FROM pets WHERE item_obj_id IN (SELECT object_id FROM items WHERE items.owner_id=?)"))
			{
				ps.setInt(1, objid);
				ps.execute();
			}
			try (PreparedStatement ps = con.prepareStatement("DELETE FROM pet_skills WHERE item_obj_id IN (SELECT object_id FROM items WHERE items.owner_id=?)"))
			{
				ps.setInt(1, objid);
				ps.execute();
			}
			try (PreparedStatement ps = con.prepareStatement("DELETE FROM item_variations WHERE itemId IN (SELECT object_id FROM items WHERE items.owner_id=?)"))
			{
				ps.setInt(1, objid);
				ps.execute();
			}
			try (PreparedStatement ps = con.prepareStatement("DELETE FROM item_special_abilities WHERE objectId IN (SELECT object_id FROM items WHERE items.owner_id=?)"))
			{
				ps.setInt(1, objid);
				ps.execute();
			}
			try (PreparedStatement ps = con.prepareStatement("DELETE FROM item_variables WHERE id IN (SELECT object_id FROM items WHERE items.owner_id=?)"))
			{
				ps.setInt(1, objid);
				ps.execute();
			}
			try (PreparedStatement ps = con.prepareStatement("DELETE FROM items WHERE owner_id=?"))
			{
				ps.setInt(1, objid);
				ps.execute();
			}
			try (PreparedStatement ps = con.prepareStatement("DELETE FROM merchant_lease WHERE player_id=?"))
			{
				ps.setInt(1, objid);
				ps.execute();
			}
			try (PreparedStatement ps = con.prepareStatement("DELETE FROM character_reco_bonus WHERE charId=?"))
			{
				ps.setInt(1, objid);
				ps.execute();
			}
			try (PreparedStatement ps = con.prepareStatement("DELETE FROM character_instance_time WHERE charId=?"))
			{
				ps.setInt(1, objid);
				ps.execute();
			}
			try (PreparedStatement ps = con.prepareStatement("DELETE FROM player_variables WHERE charId=?"))
			{
				ps.setInt(1, objid);
				ps.execute();
			}
			try (PreparedStatement ps = con.prepareStatement("DELETE FROM characters WHERE charId=?"))
			{
				ps.setInt(1, objid);
				ps.execute();
			}
			try (PreparedStatement ps = con.prepareStatement("DELETE FROM character_daily_rewards WHERE charId=?"))
			{
				ps.setInt(1, objid);
				ps.execute();
			}
			/*
			 * try (PreparedStatement ps = con.prepareStatement("DELETE FROM character_henna_potens WHERE charId=?")) {
			 * ps.setInt(1, objid);
			 * ps.execute();
			 * }
			 */
			try (PreparedStatement ps = con.prepareStatement("DELETE FROM enchant_challenge_points WHERE charId=?"))
			{
				ps.setInt(1, objid);
				ps.execute();
			}
			try (PreparedStatement ps = con.prepareStatement("DELETE FROM enchant_challenge_points_recharges WHERE charId=?"))
			{
				ps.setInt(1, objid);
				ps.execute();
			}
		}
		catch (Exception e)
		{
			LOGGER.log(Level.SEVERE, "Error deleting character.", e);
		}
	}
	
	@Override
	public boolean encrypt(Buffer data, int offset, int size)
	{
		return _crypt.encrypt(data, offset, size);
	}
	
	@Override
	public boolean decrypt(Buffer data, int offset, int size)
	{
		return _crypt.decrypt(data, offset, size);
	}
	
	@Override
	protected void onDisconnection()
	{
		QueueManager.getInstance().removeFromQueue(this);
		LOGGER_ACCOUNTING.finer("Client Disconnected: " + this);
		if (nonNull(_accountName))
		{
			LoginServerThread.getInstance().sendLogout(getAccountName());
		}
		_state = ConnectionState.DISCONNECTED;
		Disconnection.of(this).onDisconnection();
		if (_limitResetTask != null)
		{
			_limitResetTask.cancel(true);
			_limitResetTask = null;
			_packetsTimes.clear();
			_packetsTimes = null;
		}
		if (_noPlayerKickTask != null)
		{
			_noPlayerKickTask.cancel(false);
			_noPlayerKickTask = null;
		}
	}
	
	@Override
	public void onConnected()
	{
		setConnectionState(ConnectionState.CONNECTED);
		LOGGER_ACCOUNTING.finer("Client Connected: " + this);
	}
	
	public void close(boolean toLoginScreen)
	{
		// sendPacket(toLoginScreen ? ServerClose.STATIC_PACKET : LeaveWorld.STATIC_PACKET);
		// _state = ConnectionState.DISCONNECTED;
		// close();
		sendPacket(toLoginScreen ? ServerClose.STATIC_PACKET : LeaveWorld.STATIC_PACKET);
		_state = ConnectionState.DISCONNECTED;
		if (Config.CLIENT_CLOSE_DELAY > 0)
		{
			_canSendPackets = false;
			ThreadPool.get().schedule(this::close, Config.CLIENT_CLOSE_DELAY);
		}
		else
		{
			close();
		}
	}
	
	public byte[] enableCrypt()
	{
		final byte[] key = BlowFishKeygen.getRandomKey();
		_crypt.setKey(key);
		return key;
	}
	
	public PlayerInstance getPlayer()
	{
		return _player;
	}
	
	public void setPlayer(PlayerInstance player)
	{
		_player = player;
		if (_player == null)
		{
			startNoPlayerDisconnectionTask(GameClient.DISCONNECT_IN_CHAR_SELECT_DELAY);
		}
		else
		{
			if (_noPlayerKickTask != null)
			{
				_noPlayerKickTask.cancel(false);
				_noPlayerKickTask = null;
			}
		}
	}
	
	public void startNoPlayerDisconnectionTask(long delay)
	{
		if (_noPlayerKickTask != null)
		{
			_noPlayerKickTask.cancel(false);
			_noPlayerKickTask = null;
		}
		if (delay <= 0)
		{
			disconnectNoPlayer();
		}
		else
		{
			_noPlayerKickTask = ThreadPool.get().schedule(this::disconnectNoPlayer, delay);
		}
	}
	
	private void resetLimits()
	{
		_spammedPackets = 0;
		_packetsCount = 0;
		_packetsTimes.clear();
		_lastScheduleReset = System.currentTimeMillis();
	}
	
	private void disconnectNoPlayer()
	{
		if (QueueManager.getInstance().isInQueue(this))
		{
			return;
		}
		if (_player == null)
		{
			close(true);
		}
	}
	
	public ReentrantLock getPlayerLock()
	{
		return _playerLock;
	}
	
	public FloodProtectors getFloodProtectors()
	{
		return _floodProtectors;
	}
	
	public void setGameGuardOk(boolean value)
	{
		_isAuthedGG = value;
	}
	
	public String getAccountName()
	{
		return _accountName;
	}
	
	public void setAccountName(String activeChar)
	{
		_accountName = activeChar;
		if (SecondaryAuthData.getInstance().isEnabled())
		{
			_secondaryAuth = new SecondaryPasswordAuth(this);
		}
	}
	
	public boolean canUsePacket(int packetId, int extendedId)
	{
		// if (packetId == 0x8B)
		// {
		// return true; // Smart Guard RequestGMList
		// }
		if (!_canSendPackets)
		{
			return false;
		}
		if (ALLOW_ALL_PACKETS)
		{
			return true;
		}
		_packetsCount++;
		if (_packetsCount > MAX_PACKETS)
		{
			onPacketError(ClientPacketError.TOO_MANY_SPAMMED, packetId, extendedId);
		}
		long currTime = System.currentTimeMillis();
		long[] ta = _packetsTimes.get(packetId + extendedId);
		if (ta == null)
		{
			ta = new long[packetId == 0x19 ? 50 // UseItem (Custom Interface Auto Enchant unpack)
			: packetId == 0x23 ? 40 // RequestBypassToServer (Custom Interface)
			: packetId == 0x0F ? 10 // MoveBackwardToLocation (for WASD movement)
			: (packetId == 0xD0) && (extendedId == 0x180) ? 1 // ExRequestSpecialHuntingZoneEnter multiple cost prevention
			: ALLOWED_PACKET_COUNT];
			_packetsTimes.put(packetId + extendedId, ta);
		}
		// @formatter:off
		int timeout = packetId == 0x0F ? 100 // MoveBackwardToLocation (for WASD movement)
		// packetId == 0x19 ? 270 // UseItem
		// : packetId == 0x1F ? 300 // Pickup, and many other actions
		: packetId == 0x3D ? 1000 // RequestShortcutReg
		: packetId == 0x3F ? 1000 // RequestShortcutDel
		: packetId == 0x97 ? 10000 // SetPrivateStoreMsgSell Exploit
		: packetId == 0x6E ? 60000 // RequestRecordInfo Exploit
		// : packetId == 0xB0 ? 5000 // MultiSellChoose
		: packetId == 0x60 ? 5000 // RequestDestroyItem
		: (packetId == 0xD0) && (extendedId == 0x167) ? 8000 // ExRequestTeleport (Avoid Info packets spam)
		: (packetId == 0xD0) && (extendedId == 0x11D) ? 5000 // RequestTodoList
		: (packetId == 0xD0) && (extendedId == 0x11F) ? 1250 // RequestOneDayRewardReceive
		: (packetId == 0xD0) && (extendedId == 0x171) ? 1000 // ExRequestActivateAutoShortcut
		: (packetId == 0xD0) && (extendedId == 0x180) ? 2000 // ExRequestSpecialHuntingZoneEnter multiple cost prevention
		// : (packetId == 0xD0) && (extendedId == 0x4E) ? 15000 // ExBookmarkPacket
		: packetId == 0xAD ? 30000 // RequestJoinSiege (sql spam)
		: packetId == 0x3A ? 10000 // Appearing
		: packetId == 0x59 ? 1000 // ValidatePosition
		: PACKET_TIMEOUT;
		// @formatter:on
		for (int i = 0; i < ta.length; i++)
		{
			if ((ta[i] + timeout) < currTime)
			{
				ta[i] = currTime;
				return true;
			}
		}
		_spammedPackets++;
		if (_spammedPackets > MAX_SPAMED_PACKETS)
		{
			onPacketError(ClientPacketError.TOO_MANY_SPAMMED_SAME, packetId, extendedId);
		}
		// LOGGER.info("Blocking: " + String.format("0x%02X", packetId) + " " + String.format("0x%02X", extendedId));
		return false;
	}
	
	public SessionKey getSessionKey()
	{
		return _sessionKey;
	}
	
	public void setSessionKey(SessionKey sk)
	{
		_sessionKey = sk;
	}
	
	public void sendPacket(ServerPacket packet)
	{
		if (isNull(packet) || (_state == ConnectionState.DISCONNECTED) || _isDetached)
		{
			return;
		}
		if (_player != null)
		{
			if (Config.PACKET_TRACK_ENABLED && Config.PACKET_TRACK_SERVER)
			{
				if (_player.getName().equalsIgnoreCase(Config.PACKET_TRACK_NICK))
				{
					if (packet instanceof CharInfo)
					{
						Thread.dumpStack();
					}
					LOGGER.info("S -> " + packet.getClass().getSimpleName());
				}
			}
		}
		writePacket(packet);
		packet.runImpl(_player);
	}
	
	public void sendPackets(ServerPacket... packets)
	{
		if (nonNull(packets))
		{
			writePackets(List.of(packets));
			for (ServerPacket packet : packets)
			{
				packet.runImpl(_player);
			}
		}
	}
	
	public void sendPacket(SystemMessageId smId)
	{
		sendPacket(new SystemMessage(smId));
	}
	
	public CharacterDeleteFailType markToDeleteChar(int characterSlot)
	{
		final int objectId = getObjectIdForSlot(characterSlot);
		if (objectId < 0)
		{
			return CharacterDeleteFailType.UNKNOWN;
		}
		if (MentorManager.getInstance().isMentor(objectId))
		{
			return CharacterDeleteFailType.MENTOR;
		}
		else if (MentorManager.getInstance().isMentee(objectId))
		{
			return CharacterDeleteFailType.MENTEE;
		}
		else if (CommissionManager.getInstance().hasCommissionItems(objectId))
		{
			return CharacterDeleteFailType.COMMISSION;
		}
		else if (MailManager.getInstance().getMailsInProgress(objectId) > 0)
		{
			return CharacterDeleteFailType.MAIL;
		}
		else
		{
			final int clanId = CharNameTable.getInstance().getClassIdById(objectId);
			if (clanId > 0)
			{
				final Clan clan = ClanTable.getInstance().getClan(clanId);
				if (clan != null)
				{
					if (clan.getLeaderId() == objectId)
					{
						return CharacterDeleteFailType.PLEDGE_MASTER;
					}
					return CharacterDeleteFailType.PLEDGE_MEMBER;
				}
			}
		}
		if (Config.DELETE_HOURS == 0)
		{
			deleteCharByObjId(objectId);
		}
		else
		{
			try (java.sql.Connection con = DatabaseFactory.getConnection(); PreparedStatement ps2 = con.prepareStatement("UPDATE characters SET deletetime=? WHERE charId=?"))
			{
				ps2.setLong(1, System.currentTimeMillis() + (Config.DELETE_HOURS * 3_600_000)); // 60*60*1000 = 3600000
				ps2.setInt(2, objectId);
				ps2.execute();
			}
			catch (SQLException e)
			{
				LOGGER.log(Level.WARNING, "Failed to update char delete time: ", e);
			}
		}
		LOGGER_ACCOUNTING.info("Delete, " + objectId + ", " + this);
		return CharacterDeleteFailType.NONE;
	}
	
	public void restore(int characterSlot)
	{
		final int objectId = getObjectIdForSlot(characterSlot);
		if (objectId < 0)
		{
			return;
		}
		try (java.sql.Connection con = DatabaseFactory.getConnection(); PreparedStatement statement = con.prepareStatement("UPDATE characters SET deletetime=0 WHERE charId=?"))
		{
			statement.setInt(1, objectId);
			statement.execute();
		}
		catch (Exception e)
		{
			LOGGER.log(Level.SEVERE, "Error restoring character.", e);
		}
		LOGGER_ACCOUNTING.info("Restore, " + objectId + ", " + this);
	}
	
	public PlayerInstance load(int characterSlot)
	{
		final int objectId = getObjectIdForSlot(characterSlot);
		if (objectId < 0)
		{
			return null;
		}
		PlayerInstance player = World.getInstance().getPlayer(objectId);
		if (player != null)
		{
			// exploit prevention, should not happens in normal way
			if (player.isOnlineInt() == 1)
			{
				LOGGER.severe("Attempt of double login: " + player.getName() + "(" + objectId + ") " + _accountName);
			}
			if (player.getClient() != null)
			{
				Disconnection.of(player).logout(false, false);
			}
			else
			{
				player.storeMe();
				player.deleteMe();
			}
			return null;
		}
		player = PlayerInstance.load(objectId);
		if (player == null)
		{
			LOGGER.severe("Could not restore in slot: " + characterSlot);
		}
		return player;
	}
	
	public CharSelectInfoPackage getCharSelection(int slot)
	{
		if (isNull(_charactersInfo) || (slot < 0) || (slot >= _charactersInfo.size()))
		{
			return null;
		}
		return _charactersInfo.get(slot);
	}
	
	public int getCharSelectionSize()
	{
		return _charactersInfo.size();
	}
	
	private int getObjectIdForSlot(int characterSlot)
	{
		final CharSelectInfoPackage info = getCharSelection(characterSlot);
		if (info == null)
		{
			LOGGER.warning(toString() + " tried to delete Character in slot " + characterSlot + " but no characters exits at that slot.");
			return -1;
		}
		return info.getObjectId();
	}
	
	public boolean isProtocolOk()
	{
		return _protocolOk;
	}
	
	public void setProtocolOk(boolean value)
	{
		_protocolOk = value;
	}
	
	public void setClientTracert(int[][] tracert)
	{
		_trace = tracert;
	}
	
	public int[][] getTrace()
	{
		return _trace;
	}
	
	public int getCharSlot()
	{
		return _charSlot;
	}
	
	public void setCharSlot(int charSlot)
	{
		_charSlot = charSlot;
	}
	
	public void setCanSkipQueue(boolean val)
	{
		_canSkipQueue = val;
	}
	
	public boolean canSkipQueue()
	{
		return _canSkipQueue || (getCharSelection(_charSlot).getAccessLevel() > 0);
	}
	
	public ClientHardwareInfoHolder getHardwareInfo()
	{
		return _hardwareInfo;
	}
	
	public void setHardwareInfo(ClientHardwareInfoHolder hardwareInfo)
	{
		_hardwareInfo = hardwareInfo;
	}
	
	public ConnectionState getConnectionState()
	{
		return _state;
	}
	
	public void setConnectionState(ConnectionState state)
	{
		_state = state;
	}
	
	@Override
	public String toString()
	{
		try
		{
			final String address = getHostAddress();
			final ConnectionState state = getConnectionState();
			return switch (state)
			{
				case CONNECTED, CLOSING, DISCONNECTED -> "[Account: " + _accountName + " -IP: " + (Util.isNullOrEmpty(address) ? "disconnected" : address) + "]";
				case AUTHENTICATED -> "[Account: " + _accountName + " - IP: " + (Util.isNullOrEmpty(address) ? "disconnected" : address) + "]";
				case IN_GAME, ENTERING -> "[Player: " + (isNull(_player) ? "disconnected" : _player) + " - Account: " + _accountName + " - IP: " + (Util.isNullOrEmpty(address) ? "disconnected" : address) + "]";
			};
		}
		catch (NullPointerException e)
		{
			return "[Character read failed due to disconnect]";
		}
	}
	
	public boolean isAuthedGG()
	{
		return _isAuthedGG;
	}
	
	public boolean isDetached()
	{
		return _isDetached;
	}
	
	public void setDetached(boolean value)
	{
		_isDetached = value;
	}
	
	public void setCharactersInfo(List<CharSelectInfoPackage> infos)
	{
		_charactersInfo = null;
		_charactersInfo = infos;
	}
	
	public void detachCharactersInfo()
	{
		_charactersInfo = null;
	}
	
	public SecondaryPasswordAuth getSecondaryAuth()
	{
		return _secondaryAuth;
	}
	
	public int getProtocolVersion()
	{
		return _protocolVersion;
	}
	
	public void setProtocolVersion(int version)
	{
		_protocolVersion = version;
	}
	
	public int getProxyServerId()
	{
		return _proxyServerId;
	}
	
	public void setProxyServerId(int proxyServerId)
	{
		_proxyServerId = proxyServerId;
	}
	
	public void sendActionFailed()
	{
		sendPacket(ActionFailed.STATIC_PACKET);
	}
	// TODO[K] - Guard section start
	// private int revision = 0;
	//
	// public String getIpAddr()
	// {
	// return getHostAddress();
	// }
	//
	// public int getRevision()
	// {
	// return revision;
	// }
	//
	// public void setRevision(int revision)
	// {
	// this.revision = revision;
	// }
	//
	// @Override
	// public void setStrixClientData(final StrixClientData clientData)
	// {
	// this.clientData = clientData;
	// }
	//
	// @Override
	// public StrixClientData getStrixClientData()
	// {
	// return clientData;
	// }
	// TODO[K] - Guard section end
	
	public void onPacketError(ClientPacketError error, int packetId, int extendedId)
	{
		if ((error != ClientPacketError.WRONG_CONNECTION_STATE) || !ALLOW_WRONG_CONNECTION_STATE)
		{
			_canSendPackets = false;
			close();
		}
		PlayerInstance player = getPlayer();
		String ip = getHostAddress();
		String err = "[" + getConnectionState() + "] " + error.getMessage();
		if ((error == ClientPacketError.TOO_MANY_SPAMMED) || (error == ClientPacketError.TOO_MANY_SPAMMED_SAME))
		{
			err += " (in " + ((System.currentTimeMillis() - _lastScheduleReset) / 1000.0) + " s)";
		}
		if ((_lastPacketError + 5000) < System.currentTimeMillis())
		{
			_lastPacketError = System.currentTimeMillis();
			if (getHostAddress() != null)
			{
				if (ip != null)
				{
					LOGGER.warning(err + ": [Player: " + (player == null ? "null" : player.getName()) + ", Account: " + getAccountName() + "] [Last Packet: " + String.format("0x%02X", packetId) + (extendedId != 0 ? ", " + String.format("0x%02X", extendedId) : "") + "] [IP: " + ip + "]");
				}
				else
				{
					LOGGER.warning(err + ": [Player: " + (player == null ? "null" : player.getName()) + ", Account: " + getAccountName() + "] [Last Packet: " + String.format("0x%02X", packetId) + (extendedId != 0 ? ", " + String.format("0x%02X", extendedId) : "") + "] [IP: " + getHostAddress() + "]");
				}
			}
			else
			{
				LOGGER.warning(err + ": [Player: " + (player == null ? "null" : player.getName()) + ", Account: " + getAccountName() + "] [Last Packet: " + String.format("0x%02X", packetId) + (extendedId != 0 ? ", " + String.format("0x%02X", extendedId) : "") + "] [IP: UNKNOWN]");
			}
		}
		if ((error != ClientPacketError.WRONG_CONNECTION_STATE) || !ALLOW_WRONG_CONNECTION_STATE)
		{
			IpBlockManager.getInstance().takeActions(ip);
		}
	}
	
	public void onPacketError(ClientPacketError error, int packetId)
	{
		onPacketError(error, packetId, 0);
	}
	
	public void setMergeNameChangeInfo(int type, int charSlot)
	{
		_mergeNameChangeInfo[0] = type;
		_mergeNameChangeInfo[1] = charSlot;
	}
	
	public int[] getMergeNameChangeInfo()
	{
		return _mergeNameChangeInfo;
	}
	
	public ClientLanguage getLang()
	{
		return _lang;
	}
	
	public void setLang(ClientLanguage lang)
	{
		_lang = lang;
	}
	
	public String getHwid()
	{
		return _hwid;
	}
	
	// AAC
	public void setHwid(String hwid)
	{
		_hwid = hwid;
	}
	
	public void setFullHwid(byte[] hwid)
	{
		_fullHwid = HexUtils.hexstr(hwid);
	}
	
	public String getFullHwid()
	{
		return _fullHwid;
	}
	
	// Custom Pride.
	public final String getPassword()
	{
		return _password;
	}
	
	public final void setPassword(String password)
	{
		_password = password;
	}
}
