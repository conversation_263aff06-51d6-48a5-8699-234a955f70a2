/*
 * This file is part of the L2J Mobius project.
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.network;

import java.util.Arrays;
import java.util.EnumSet;
import java.util.Objects;
import java.util.function.Supplier;

import club.projectessence.gameserver.network.clientpackets.*;
import club.projectessence.gameserver.network.clientpackets.adenadistribution.RequestDivideAdena;
import club.projectessence.gameserver.network.clientpackets.adenadistribution.RequestDivideAdenaCancel;
import club.projectessence.gameserver.network.clientpackets.adenadistribution.RequestDivideAdenaStart;
import club.projectessence.gameserver.network.clientpackets.appearance.RequestExCancelShape_Shifting_Item;
import club.projectessence.gameserver.network.clientpackets.appearance.RequestExTryToPutShapeShiftingEnchantSupportItem;
import club.projectessence.gameserver.network.clientpackets.appearance.RequestExTryToPutShapeShiftingTargetItem;
import club.projectessence.gameserver.network.clientpackets.appearance.RequestShapeShiftingItem;
import club.projectessence.gameserver.network.clientpackets.attendance.RequestVipAttendanceCheck;
import club.projectessence.gameserver.network.clientpackets.attendance.RequestVipAttendanceItemList;
import club.projectessence.gameserver.network.clientpackets.attributechange.RequestChangeAttributeCancel;
import club.projectessence.gameserver.network.clientpackets.attributechange.RequestChangeAttributeItem;
import club.projectessence.gameserver.network.clientpackets.attributechange.SendChangeAttributeTargetItem;
import club.projectessence.gameserver.network.clientpackets.augmentation.ExRequestApplyVariationOption;
import club.projectessence.gameserver.network.clientpackets.augmentation.ExRequestVariationCloseUI;
import club.projectessence.gameserver.network.clientpackets.augmentation.ExRequestVariationOpenUI;
import club.projectessence.gameserver.network.clientpackets.augmentation.RequestRefine;
import club.projectessence.gameserver.network.clientpackets.augmentation.RequestRefineCancel;
import club.projectessence.gameserver.network.clientpackets.autoplay.ExAutoPlaySetting;
import club.projectessence.gameserver.network.clientpackets.autoplay.ExRequestActivateAutoShortcut;
import club.projectessence.gameserver.network.clientpackets.balok.ExBalrogWarGetReward;
import club.projectessence.gameserver.network.clientpackets.balok.ExBalrogWarShowRanking;
import club.projectessence.gameserver.network.clientpackets.balok.ExBalrogWarShowUI;
import club.projectessence.gameserver.network.clientpackets.balok.ExBalrogWarTeleport;
import club.projectessence.gameserver.network.clientpackets.balthusevent.RequestEventBalthusToken;
import club.projectessence.gameserver.network.clientpackets.blessing.ExRequestBlessOptionCancel;
import club.projectessence.gameserver.network.clientpackets.blessing.ExRequestBlessOptionEnchant;
import club.projectessence.gameserver.network.clientpackets.blessing.ExRequestBlessOptionPutItem;
import club.projectessence.gameserver.network.clientpackets.castlesiege.ExRequestMercenaryCastleWarCastleInfo;
import club.projectessence.gameserver.network.clientpackets.castlesiege.ExRequestMercenaryCastleWarCastleSiegeAttackerList;
import club.projectessence.gameserver.network.clientpackets.castlesiege.ExRequestMercenaryCastleWarCastleSiegeDefenderList;
import club.projectessence.gameserver.network.clientpackets.castlesiege.ExRequestMercenaryCastleWarCastleSiegeInfo;
import club.projectessence.gameserver.network.clientpackets.ceremonyofchaos.RequestCancelCuriousHouse;
import club.projectessence.gameserver.network.clientpackets.ceremonyofchaos.RequestCuriousHouseHtml;
import club.projectessence.gameserver.network.clientpackets.ceremonyofchaos.RequestJoinCuriousHouse;
import club.projectessence.gameserver.network.clientpackets.classchange.ExRequestClassChange;
import club.projectessence.gameserver.network.clientpackets.classchange.ExRequestClassChangeVerifying;
import club.projectessence.gameserver.network.clientpackets.collection.RequestCollectionCloseUI;
import club.projectessence.gameserver.network.clientpackets.collection.RequestCollectionFavoriteList;
import club.projectessence.gameserver.network.clientpackets.collection.RequestCollectionReceiveReward;
import club.projectessence.gameserver.network.clientpackets.collection.RequestCollectionRegister;
import club.projectessence.gameserver.network.clientpackets.collection.RequestCollectionUpdateFavorite;
import club.projectessence.gameserver.network.clientpackets.collection.RequestExCollectionList;
import club.projectessence.gameserver.network.clientpackets.collection.RequestExCollectionOpenUI;
import club.projectessence.gameserver.network.clientpackets.commission.RequestCommissionBuyInfo;
import club.projectessence.gameserver.network.clientpackets.commission.RequestCommissionBuyItem;
import club.projectessence.gameserver.network.clientpackets.commission.RequestCommissionCancel;
import club.projectessence.gameserver.network.clientpackets.commission.RequestCommissionDelete;
import club.projectessence.gameserver.network.clientpackets.commission.RequestCommissionInfo;
import club.projectessence.gameserver.network.clientpackets.commission.RequestCommissionList;
import club.projectessence.gameserver.network.clientpackets.commission.RequestCommissionRegister;
import club.projectessence.gameserver.network.clientpackets.commission.RequestCommissionRegisteredItem;
import club.projectessence.gameserver.network.clientpackets.commission.RequestCommissionRegistrableItemList;
import club.projectessence.gameserver.network.clientpackets.compound.RequestNewEnchantClose;
import club.projectessence.gameserver.network.clientpackets.compound.RequestNewEnchantPushOne;
import club.projectessence.gameserver.network.clientpackets.compound.RequestNewEnchantPushTwo;
import club.projectessence.gameserver.network.clientpackets.compound.RequestNewEnchantRemoveOne;
import club.projectessence.gameserver.network.clientpackets.compound.RequestNewEnchantRemoveTwo;
import club.projectessence.gameserver.network.clientpackets.compound.RequestNewEnchantRetryToPutItems;
import club.projectessence.gameserver.network.clientpackets.compound.RequestNewEnchantTry;
import club.projectessence.gameserver.network.clientpackets.crystalization.RequestCrystallizeEstimate;
import club.projectessence.gameserver.network.clientpackets.crystalization.RequestCrystallizeItemCancel;
import club.projectessence.gameserver.network.clientpackets.dailymission.RequestOneDayRewardReceive;
import club.projectessence.gameserver.network.clientpackets.dailymission.RequestTodoList;
import club.projectessence.gameserver.network.clientpackets.elementalspirits.ExElementalInitTalent;
import club.projectessence.gameserver.network.clientpackets.elementalspirits.ExElementalSpiritAbsorb;
import club.projectessence.gameserver.network.clientpackets.elementalspirits.ExElementalSpiritAbsorbInfo;
import club.projectessence.gameserver.network.clientpackets.elementalspirits.ExElementalSpiritChangeType;
import club.projectessence.gameserver.network.clientpackets.elementalspirits.ExElementalSpiritEvolution;
import club.projectessence.gameserver.network.clientpackets.elementalspirits.ExElementalSpiritEvolutionInfo;
import club.projectessence.gameserver.network.clientpackets.elementalspirits.ExElementalSpiritExtract;
import club.projectessence.gameserver.network.clientpackets.elementalspirits.ExElementalSpiritExtractInfo;
import club.projectessence.gameserver.network.clientpackets.elementalspirits.ExElementalSpiritInfo;
import club.projectessence.gameserver.network.clientpackets.elementalspirits.ExElementalSpiritSetTalent;
import club.projectessence.gameserver.network.clientpackets.enchantingnew.ExRequestReqEnchantFailRewardInfo;
import club.projectessence.gameserver.network.clientpackets.enchantingnew.ExRequestReqFinishMultiEnchantScroll;
import club.projectessence.gameserver.network.clientpackets.enchantingnew.ExRequestReqMultiEnchantItemList;
import club.projectessence.gameserver.network.clientpackets.enchantingnew.ExRequestReqSetMultiEnchantItemList;
import club.projectessence.gameserver.network.clientpackets.enchantingnew.ExRequestReqStartMultiEnchantScroll;
import club.projectessence.gameserver.network.clientpackets.enchantingnew.ExRequestReqViewEnchantResult;
import club.projectessence.gameserver.network.clientpackets.enchantingnew.ExRequestReqViewMultiEnchantResult;
import club.projectessence.gameserver.network.clientpackets.enchantingnew.challengepoint.ExRequestResetEnchantChallengePoint;
import club.projectessence.gameserver.network.clientpackets.enchantingnew.challengepoint.ExRequestSetEnchantChallengePoint;
import club.projectessence.gameserver.network.clientpackets.ensoul.RequestItemEnsoul;
import club.projectessence.gameserver.network.clientpackets.ensoul.RequestTryEnSoulExtraction;
import club.projectessence.gameserver.network.clientpackets.festivalbm.ExRequestFestivalBmGame;
import club.projectessence.gameserver.network.clientpackets.festivalbm.ExRequestFestivalBmInfo;
import club.projectessence.gameserver.network.clientpackets.friend.RequestFriendDetailInfo;
import club.projectessence.gameserver.network.clientpackets.gameassistant.ExOpenHtml;
import club.projectessence.gameserver.network.clientpackets.gameassistant.ExPremiumManagerLinkHtml;
import club.projectessence.gameserver.network.clientpackets.gameassistant.ExPremiumManagerPassCmdToServer;
import club.projectessence.gameserver.network.clientpackets.itemautopeel.ExRequestItemAutoPeel;
import club.projectessence.gameserver.network.clientpackets.itemautopeel.ExRequestReadyItemAutoPeel;
import club.projectessence.gameserver.network.clientpackets.itemautopeel.ExRequestStopItemAutoPeel;
import club.projectessence.gameserver.network.clientpackets.l2pass.RequestL2PassBuyPremium;
import club.projectessence.gameserver.network.clientpackets.l2pass.RequestL2PassInfo;
import club.projectessence.gameserver.network.clientpackets.l2pass.RequestL2PassRequestReward;
import club.projectessence.gameserver.network.clientpackets.l2pass.RequestL2PassRequestRewardAll;
import club.projectessence.gameserver.network.clientpackets.l2pass.RequestSayhasSupportToggle;
import club.projectessence.gameserver.network.clientpackets.lettercollector.ExRequestLetterCollectorTakeReward;
import club.projectessence.gameserver.network.clientpackets.levelmission.ExRequestMissionLevelReceiveReward;
import club.projectessence.gameserver.network.clientpackets.levelmission.ExRequestMissionLevelRewardList;
import club.projectessence.gameserver.network.clientpackets.limitshop.RequestPurchaseLimitShopItemBuy;
import club.projectessence.gameserver.network.clientpackets.limitshop.RequestPurchaseLimitShopItemList;
import club.projectessence.gameserver.network.clientpackets.luckygame.RequestLuckyGamePlay;
import club.projectessence.gameserver.network.clientpackets.luckygame.RequestLuckyGameStartInfo;
import club.projectessence.gameserver.network.clientpackets.mablegame.ExRequestMableGameClose;
import club.projectessence.gameserver.network.clientpackets.mablegame.ExRequestMableGameOpen;
import club.projectessence.gameserver.network.clientpackets.mablegame.ExRequestMableGamePopupOk;
import club.projectessence.gameserver.network.clientpackets.mablegame.ExRequestMableGameReset;
import club.projectessence.gameserver.network.clientpackets.mablegame.ExRequestMableGameRollDice;
import club.projectessence.gameserver.network.clientpackets.magiclamp.RequestMagicLampGameInfo;
import club.projectessence.gameserver.network.clientpackets.magiclamp.RequestMagicLampGameStart;
import club.projectessence.gameserver.network.clientpackets.mentoring.ConfirmMenteeAdd;
import club.projectessence.gameserver.network.clientpackets.mentoring.RequestMenteeAdd;
import club.projectessence.gameserver.network.clientpackets.mentoring.RequestMenteeWaitingList;
import club.projectessence.gameserver.network.clientpackets.mentoring.RequestMentorCancel;
import club.projectessence.gameserver.network.clientpackets.mentoring.RequestMentorList;
import club.projectessence.gameserver.network.clientpackets.newhenna.ExRequestNewHennaCompose;
import club.projectessence.gameserver.network.clientpackets.newhenna.ExRequestNewHennaEquip;
import club.projectessence.gameserver.network.clientpackets.newhenna.ExRequestNewHennaList;
import club.projectessence.gameserver.network.clientpackets.newhenna.ExRequestNewHennaPotenEnchant;
import club.projectessence.gameserver.network.clientpackets.newhenna.ExRequestNewHennaPotenSelect;
import club.projectessence.gameserver.network.clientpackets.newhenna.ExRequestNewHennaUnequip;
import club.projectessence.gameserver.network.clientpackets.newhenna.ExRequestNewHennaUnequipInfo;
import club.projectessence.gameserver.network.clientpackets.olympiad.ExRequestOlympiadMatchMaking;
import club.projectessence.gameserver.network.clientpackets.olympiad.ExRequestOlympiadMatchMakingCancel;
import club.projectessence.gameserver.network.clientpackets.olympiad.ExRequestOlympiadUI;
import club.projectessence.gameserver.network.clientpackets.olympiad.RequestExOlympiadMatchListRefresh;
import club.projectessence.gameserver.network.clientpackets.olympiad.RequestOlympiadMatchList;
import club.projectessence.gameserver.network.clientpackets.olympiad.RequestOlympiadObserverEnd;
import club.projectessence.gameserver.network.clientpackets.pet.ExRequestAcquirePetSkill;
import club.projectessence.gameserver.network.clientpackets.pet.ExRequestEvolvePet;
import club.projectessence.gameserver.network.clientpackets.pet.ExRequestPetEquipItem;
import club.projectessence.gameserver.network.clientpackets.pet.ExRequestPetUnEquipItem;
import club.projectessence.gameserver.network.clientpackets.pet.ExTryPetExtractSystem;
import club.projectessence.gameserver.network.clientpackets.pkpenalty.ExRequestPkPenaltyList;
import club.projectessence.gameserver.network.clientpackets.pkpenalty.ExRequestPkPenaltyListOnlyLoc;
import club.projectessence.gameserver.network.clientpackets.pledge.ExRequestPledgeDonationInfo;
import club.projectessence.gameserver.network.clientpackets.pledge.ExRequestPledgeDonationRequest;
import club.projectessence.gameserver.network.clientpackets.pledge.RequestExPledgeContributionList;
import club.projectessence.gameserver.network.clientpackets.pledge.RequestExPledgeCrestLarge;
import club.projectessence.gameserver.network.clientpackets.pledge.RequestExPledgeEnemyDelete;
import club.projectessence.gameserver.network.clientpackets.pledge.RequestExPledgeEnemyInfoList;
import club.projectessence.gameserver.network.clientpackets.pledge.RequestExPledgeEnemyRegister;
import club.projectessence.gameserver.network.clientpackets.pledge.RequestExPledgeV3Info;
import club.projectessence.gameserver.network.clientpackets.pledge.RequestExPledgeV3SetAnnounce;
import club.projectessence.gameserver.network.clientpackets.primeshop.RequestBRBuyProduct;
import club.projectessence.gameserver.network.clientpackets.primeshop.RequestBRGamePoint;
import club.projectessence.gameserver.network.clientpackets.primeshop.RequestBRPresentBuyProduct;
import club.projectessence.gameserver.network.clientpackets.primeshop.RequestBRProductInfo;
import club.projectessence.gameserver.network.clientpackets.primeshop.RequestBRProductList;
import club.projectessence.gameserver.network.clientpackets.primeshop.RequestBRRecentProductList;
import club.projectessence.gameserver.network.clientpackets.privatestore.ExRequestPrivateStoreSearchList;
import club.projectessence.gameserver.network.clientpackets.privatestore.ExRequestPrivateStoreSearchStatistics;
import club.projectessence.gameserver.network.clientpackets.raidbossinfo.RequestRaidBossSpawnInfo;
import club.projectessence.gameserver.network.clientpackets.raidbossinfo.RequestRaidServerInfo;
import club.projectessence.gameserver.network.clientpackets.raidbossinfo.RequestRaidTeleportInfo;
import club.projectessence.gameserver.network.clientpackets.raidbossinfo.RequestTeleportToRaidPosition;
import club.projectessence.gameserver.network.clientpackets.randomcraft.ExRequestRandomCraftExtract;
import club.projectessence.gameserver.network.clientpackets.randomcraft.ExRequestRandomCraftInfo;
import club.projectessence.gameserver.network.clientpackets.randomcraft.ExRequestRandomCraftLockSlot;
import club.projectessence.gameserver.network.clientpackets.randomcraft.ExRequestRandomCraftMake;
import club.projectessence.gameserver.network.clientpackets.randomcraft.ExRequestRandomCraftRefresh;
import club.projectessence.gameserver.network.clientpackets.ranking.RequestExPledgeRankingList;
import club.projectessence.gameserver.network.clientpackets.ranking.RequestExPledgeRankingMyInfo;
import club.projectessence.gameserver.network.clientpackets.ranking.RequestOlympiadHeroAndLegendInfo;
import club.projectessence.gameserver.network.clientpackets.ranking.RequestOlympiadMyRankingInfo;
import club.projectessence.gameserver.network.clientpackets.ranking.RequestOlympiadRankingInfo;
import club.projectessence.gameserver.network.clientpackets.ranking.RequestPetRankingList;
import club.projectessence.gameserver.network.clientpackets.ranking.RequestPetRankingMyInfo;
import club.projectessence.gameserver.network.clientpackets.ranking.RequestPvpRankingList;
import club.projectessence.gameserver.network.clientpackets.ranking.RequestPvpRankingMyInfo;
import club.projectessence.gameserver.network.clientpackets.ranking.RequestRankingCharBuffzoneNpcPosition;
import club.projectessence.gameserver.network.clientpackets.ranking.RequestRankingCharHistory;
import club.projectessence.gameserver.network.clientpackets.ranking.RequestRankingCharInfo;
import club.projectessence.gameserver.network.clientpackets.ranking.RequestRankingCharRankers;
import club.projectessence.gameserver.network.clientpackets.ranking.RequestRankingCharSpawnBuffzoneNpc;
import club.projectessence.gameserver.network.clientpackets.resurrectionsettings.ExRequestUserRestartLockerUpdate;
import club.projectessence.gameserver.network.clientpackets.sayune.RequestFlyMove;
import club.projectessence.gameserver.network.clientpackets.sayune.RequestFlyMoveStart;
import club.projectessence.gameserver.network.clientpackets.sharedposition.RequestSharedPositionSharingUI;
import club.projectessence.gameserver.network.clientpackets.sharedposition.RequestSharedPositionTeleport;
import club.projectessence.gameserver.network.clientpackets.sharedposition.RequestSharedPositionTeleportUI;
import club.projectessence.gameserver.network.clientpackets.shuttle.CannotMoveAnymoreInShuttle;
import club.projectessence.gameserver.network.clientpackets.shuttle.MoveToLocationInShuttle;
import club.projectessence.gameserver.network.clientpackets.shuttle.RequestShuttleGetOff;
import club.projectessence.gameserver.network.clientpackets.shuttle.RequestShuttleGetOn;
import club.projectessence.gameserver.network.clientpackets.specialhuntingzones.ExRequestTimeRestrictFieldList;
import club.projectessence.gameserver.network.clientpackets.specialhuntingzones.ExRequestTimeRestrictFieldUserEnter;
import club.projectessence.gameserver.network.clientpackets.specialhuntingzones.ExRequestTimeRestrictFieldUserLeave;
import club.projectessence.gameserver.network.clientpackets.stats.ExResetStatusBonus;
import club.projectessence.gameserver.network.clientpackets.stats.ExSetStatusBonus;
import club.projectessence.gameserver.network.clientpackets.steadybox.ExRequestSteadyBoxLoad;
import club.projectessence.gameserver.network.clientpackets.steadybox.ExRequestSteadyGetReward;
import club.projectessence.gameserver.network.clientpackets.steadybox.ExRequestSteadyOpenBox;
import club.projectessence.gameserver.network.clientpackets.steadybox.ExRequestSteadyOpenSlot;
import club.projectessence.gameserver.network.clientpackets.steadybox.ExRequestTimerCheck;
import club.projectessence.gameserver.network.clientpackets.subjugation.ExRequestSubjugationGacha;
import club.projectessence.gameserver.network.clientpackets.subjugation.ExRequestSubjugationGachaUI;
import club.projectessence.gameserver.network.clientpackets.subjugation.ExRequestSubjugationList;
import club.projectessence.gameserver.network.clientpackets.subjugation.ExRequestSubjugationRanking;
import club.projectessence.gameserver.network.clientpackets.surveillance.ExRequestUserWatcherAdd;
import club.projectessence.gameserver.network.clientpackets.surveillance.ExRequestUserWatcherDelete;
import club.projectessence.gameserver.network.clientpackets.surveillance.ExRequestUserWatcherTargetList;
import club.projectessence.gameserver.network.clientpackets.teleports.ExRequestTeleport;
import club.projectessence.gameserver.network.clientpackets.teleports.ExRequestTeleportFavoriteList;
import club.projectessence.gameserver.network.clientpackets.teleports.ExRequestTeleportFavoritesAddDel;
import club.projectessence.gameserver.network.clientpackets.teleports.ExRequestTeleportFavoritesUIToggle;
import club.projectessence.gameserver.network.clientpackets.teleports.ExRequestTeleportUI;
import club.projectessence.gameserver.network.clientpackets.training.NotifyTrainingRoomEnd;
import club.projectessence.gameserver.network.clientpackets.upgradesystem.RequestUpgradeSystemNormal;
import club.projectessence.gameserver.network.clientpackets.vengeance.RequestExPvpBookShareRevengeKillerLocation;
import club.projectessence.gameserver.network.clientpackets.vengeance.RequestExPvpBookShareRevengeList;
import club.projectessence.gameserver.network.clientpackets.vengeance.RequestExPvpBookShareRevengeReqShareRevengeInfo;
import club.projectessence.gameserver.network.clientpackets.vengeance.RequestExPvpBookShareRevengeSharedTeleportToKiller;
import club.projectessence.gameserver.network.clientpackets.vengeance.RequestExPvpBookShareRevengeTeleportToKiller;
import club.projectessence.gameserver.network.clientpackets.vipsystem.ExRequestVipInfo;
import club.projectessence.gameserver.network.clientpackets.vipsystem.RequestVipLuckGameInfo;
import club.projectessence.gameserver.network.clientpackets.worldexchange.ExWorldExchangeAveragePrice;
import club.projectessence.gameserver.network.clientpackets.worldexchange.ExWorldExchangeBuyItem;
import club.projectessence.gameserver.network.clientpackets.worldexchange.ExWorldExchangeItemList;
import club.projectessence.gameserver.network.clientpackets.worldexchange.ExWorldExchangeRegisterItem;
import club.projectessence.gameserver.network.clientpackets.worldexchange.ExWorldExchangeSettleList;
import club.projectessence.gameserver.network.clientpackets.worldexchange.ExWorldExchangeSettleRecvResult;
import club.projectessence.gameserver.network.clientpackets.worldexchange.ExWorldExchangeTotalList;
import io.github.joealisson.mmocore.ReadableBuffer;

/**
 * <AUTHOR>
 */
public enum ExIncomingPackets implements PacketFactory
{
	REQUEST_GOTO_LOBBY(0x33, RequestGotoLobby::new, ConnectionState.AUTHENTICATED_STATES),
	REQUEST_EX_2ND_PASSWORD_CHECK(0xA6, RequestEx2ndPasswordCheck::new, ConnectionState.AUTHENTICATED_STATES),
	REQUEST_EX_2ND_PASSWORD_VERIFY(0xA7, RequestEx2ndPasswordVerify::new, ConnectionState.AUTHENTICATED_STATES),
	REQUEST_EX_2ND_PASSWORD_REQ(0xA8, RequestEx2ndPasswordReq::new, ConnectionState.AUTHENTICATED_STATES),
	REQUEST_CHARACTER_NAME_CREATABLE(0xA9, RequestCharacterNameCreatable::new, ConnectionState.AUTHENTICATED_STATES),
	REQUEST_MANOR_LIST(0x01, RequestManorList::new, ConnectionState.ENTERING_GAME_AND_IN_GAME),
	REQUEST_PROCEDURE_CROP_LIST(0x02, RequestProcureCropList::new, ConnectionState.IN_GAME_STATES),
	REQUEST_SET_SEED(0x03, RequestSetSeed::new, ConnectionState.IN_GAME_STATES),
	REQUEST_SET_CROP(0x04, RequestSetCrop::new, ConnectionState.IN_GAME_STATES),
	REQUEST_WRITE_HERO_WORDS(0x05, RequestWriteHeroWords::new, ConnectionState.IN_GAME_STATES),
	REQUEST_EX_ASK_JOIN_MPCC(0x06, RequestExAskJoinMPCC::new, ConnectionState.IN_GAME_STATES),
	REQUEST_EX_ACCEPT_JOIN_MPCC(0x07, RequestExAcceptJoinMPCC::new, ConnectionState.IN_GAME_STATES),
	REQUEST_EX_OUST_FROM_MPCC(0x08, RequestExOustFromMPCC::new, ConnectionState.IN_GAME_STATES),
	REQUEST_OUST_FROM_PARTY_ROOM(0x09, RequestOustFromPartyRoom::new, ConnectionState.IN_GAME_STATES),
	REQUEST_DISMISS_PARTY_ROOM(0x0A, RequestDismissPartyRoom::new, ConnectionState.IN_GAME_STATES),
	REQUEST_WITHDRAW_PARTY_ROOM(0x0B, RequestWithdrawPartyRoom::new, ConnectionState.IN_GAME_STATES),
	REQUEST_CHANGE_PARTY_LEADER(0x0C, RequestChangePartyLeader::new, ConnectionState.IN_GAME_STATES),
	REQUEST_AUTO_SOULSHOT(0x0D, RequestAutoSoulShot::new, ConnectionState.IN_GAME_STATES),
	REQUEST_EX_ENCHANT_SKILL_INFO(0x0E, RequestExEnchantSkillInfo::new, ConnectionState.IN_GAME_STATES),
	REQUEST_EX_ENCHANT_SKILL(0x0F, RequestExEnchantSkill::new, ConnectionState.IN_GAME_STATES),
	REQUEST_EX_PLEDGE_CREST_LARGE(0x10, RequestExPledgeCrestLarge::new, ConnectionState.IN_GAME_STATES),
	REQUEST_EX_SET_PLEDGE_CREST_LARGE(0x11, RequestExSetPledgeCrestLarge::new, ConnectionState.IN_GAME_STATES),
	REQUEST_PLEDGE_SET_ACADEMY_MASTER(0x12, RequestPledgeSetAcademyMaster::new, ConnectionState.IN_GAME_STATES),
	REQUEST_PLEDGE_POWER_GRADE_LIST(0x13, RequestPledgePowerGradeList::new, ConnectionState.IN_GAME_STATES),
	REQUEST_PLEDGE_MEMBER_POWER_INFO(0x14, RequestPledgeMemberPowerInfo::new, ConnectionState.IN_GAME_STATES),
	REQUEST_PLEDGE_SET_MEMBER_POWER_GRADE(0x15, RequestPledgeSetMemberPowerGrade::new, ConnectionState.IN_GAME_STATES),
	REQUEST_PLEDGE_MEMBER_INFO(0x16, RequestPledgeMemberInfo::new, ConnectionState.IN_GAME_STATES),
	REQUEST_PLEDGE_WAR_LIST(0x17, RequestPledgeWarList::new, ConnectionState.IN_GAME_STATES),
	REQUEST_EX_FISH_RANKING(0x18, RequestExFishRanking::new, ConnectionState.IN_GAME_STATES),
	REQUEST_PCCAFE_COUPON_USE(0x19, RequestPCCafeCouponUse::new, ConnectionState.IN_GAME_STATES),
	REQUEST_SERVER_LOGIN(0x1A, null, ConnectionState.IN_GAME_STATES),
	REQUEST_DUEL_START(0x1B, RequestDuelStart::new, ConnectionState.IN_GAME_STATES),
	REQUEST_DUAL_ANSWER_START(0x1C, RequestDuelAnswerStart::new, ConnectionState.IN_GAME_STATES),
	REQUEST_EX_SET_TUTORIAL(0x1D, null, ConnectionState.IN_GAME_STATES),
	REQUEST_EX_RQ_ITEM_LINK(0x1E, RequestExRqItemLink::new, ConnectionState.IN_GAME_STATES),
	CANNOT_MOVE_ANYMORE_AIR_SHIP(0x1F, CannotMoveAnymoreAirShip::new, ConnectionState.IN_GAME_STATES),
	MOVE_TO_LOCATION_IN_AIR_SHIP(0x20, MoveToLocationInAirShip::new, ConnectionState.IN_GAME_STATES),
	REQUEST_KEY_MAPPING(0x21, RequestKeyMapping::new, ConnectionState.ENTERING_GAME_AND_IN_GAME),
	REQUEST_SAVE_KEY_MAPPING(0x22, RequestSaveKeyMapping::new, ConnectionState.IN_GAME_STATES),
	REQUEST_EX_REMOVE_ITEM_ATTRIBUTE(0x23, RequestExRemoveItemAttribute::new, ConnectionState.IN_GAME_STATES),
	REQUEST_SAVE_INVENTORY_ORDER(0x24, RequestSaveInventoryOrder::new, ConnectionState.IN_GAME_STATES),
	REQUEST_EXIT_PARTY_MATCHING_WAITING_ROOM(0x25, RequestExitPartyMatchingWaitingRoom::new, ConnectionState.IN_GAME_STATES),
	REQUEST_CONFIRM_TARGET_ITEM(0x26, RequestConfirmTargetItem::new, ConnectionState.IN_GAME_STATES),
	REQUEST_CONFIRM_REFINER_ITEM(0x27, RequestConfirmRefinerItem::new, ConnectionState.IN_GAME_STATES),
	REQUEST_CONFIRM_GEMSTONE(0x28, RequestConfirmGemStone::new, ConnectionState.IN_GAME_STATES),
	REQUEST_OLYMPIAD_OBSERVER_END(0x29, RequestOlympiadObserverEnd::new, ConnectionState.IN_GAME_STATES),
	REQUEST_CURSED_WEAPON_LIST(0x2A, RequestCursedWeaponList::new, ConnectionState.IN_GAME_STATES),
	REQUEST_CURSED_WEAPON_LOCATION(0x2B, RequestCursedWeaponLocation::new, ConnectionState.IN_GAME_STATES),
	REQUEST_PLEDGE_REORGANIZE_MEMBER(0x2C, RequestPledgeReorganizeMember::new, ConnectionState.IN_GAME_STATES),
	REQUEST_EX_MPCC_SHOW_PARTY_MEMBERS_INFO(0x2D, RequestExMPCCShowPartyMembersInfo::new, ConnectionState.IN_GAME_STATES),
	REQUEST_OLYMPIAD_MATCH_LIST(0x2E, RequestOlympiadMatchList::new, ConnectionState.IN_GAME_STATES),
	REQUEST_ASK_JOIN_PARTY_ROOM(0x2F, RequestAskJoinPartyRoom::new, ConnectionState.IN_GAME_STATES),
	ANSWER_JOIN_PARTY_ROOM(0x30, AnswerJoinPartyRoom::new, ConnectionState.IN_GAME_STATES),
	REQUEST_LIST_PARTY_MATCHING_WAITING_ROOM(0x31, RequestListPartyMatchingWaitingRoom::new, ConnectionState.IN_GAME_STATES),
	REQUEST_EX_ENCHANT_ITEM_ATTRIBUTE(0x32, RequestExEnchantItemAttribute::new, ConnectionState.IN_GAME_STATES),
	CANNOT_AIRSHIP_MOVE_ANYMORE(0x34, null, ConnectionState.IN_GAME_STATES),
	MOVE_TO_LOCATION_AIR_SHIP(0x35, MoveToLocationAirShip::new, ConnectionState.IN_GAME_STATES),
	REQUEST_BID_ITEM_AUCTION(0x36, RequestBidItemAuction::new, ConnectionState.IN_GAME_STATES),
	REQUEST_INFO_ITEM_AUCTION(0x37, RequestInfoItemAuction::new, ConnectionState.IN_GAME_STATES),
	REQUEST_EX_CHANGE_NAME(0x38, RequestExChangeName::new, ConnectionState.AUTHENTICATED_AND_IN_GAME),
	REQUEST_ALL_CASTLE_INFO(0x39, RequestAllCastleInfo::new, ConnectionState.IN_GAME_STATES),
	REQUEST_ALL_FORTRESS_INFO(0x3A, RequestAllFortressInfo::new, ConnectionState.IN_GAME_STATES),
	REQUEST_ALL_AGIT_INGO(0x3B, RequestAllAgitInfo::new, ConnectionState.IN_GAME_STATES),
	REQUEST_FORTRESS_SIEGE_INFO(0x3C, RequestFortressSiegeInfo::new, ConnectionState.IN_GAME_STATES),
	REQUEST_GET_BOSS_RECORD(0x3D, RequestGetBossRecord::new, ConnectionState.IN_GAME_STATES),
	REQUEST_REFINE(0x3E, RequestRefine::new, ConnectionState.IN_GAME_STATES),
	REQUEST_CONFIRM_CANCEL_ITEM(0x3F, RequestConfirmCancelItem::new, ConnectionState.IN_GAME_STATES),
	REQUEST_REFINE_CANCEL(0x40, RequestRefineCancel::new, ConnectionState.IN_GAME_STATES),
	REQUEST_EX_MAGIC_SKILL_USE_GROUND(0x41, RequestExMagicSkillUseGround::new, ConnectionState.IN_GAME_STATES),
	REQUEST_DUEL_SURRENDER(0x42, RequestDuelSurrender::new, ConnectionState.IN_GAME_STATES),
	REQUEST_EX_ENCHANT_SKILL_INFO_DETAIL(0x43, RequestExEnchantSkillInfoDetail::new, ConnectionState.IN_GAME_STATES),
	REQUEST_ANTI_FREE_SERVER(0x44, null, ConnectionState.IN_GAME_STATES),
	REQUEST_FORTRESS_MAP_INFO(0x45, RequestFortressMapInfo::new, ConnectionState.IN_GAME_STATES),
	REQUEST_PVP_MATCH_RECORD(0x46, RequestPVPMatchRecord::new, ConnectionState.IN_GAME_STATES),
	SET_PRIVATE_STORE_WHOLE_MSG(0x47, SetPrivateStoreWholeMsg::new, ConnectionState.IN_GAME_STATES),
	REQUEST_DISPEL(0x48, RequestDispel::new, ConnectionState.IN_GAME_STATES),
	REQUEST_EX_TRY_TO_PUT_ENCHANT_TARGET_ITEM(0x49, RequestExTryToPutEnchantTargetItem::new, ConnectionState.IN_GAME_STATES),
	REQUEST_EX_TRY_TO_PUT_ENCHANT_SUPPORT_ITEM(0x4A, RequestExTryToPutEnchantSupportItem::new, ConnectionState.IN_GAME_STATES),
	REQUEST_EX_CANCEL_ENCHANT_ITEM(0x4B, RequestExCancelEnchantItem::new, ConnectionState.IN_GAME_STATES),
	REQUEST_CHANGE_NICKNAME_COLOR(0x4C, null/* RequestChangeNicknameColor::new */, ConnectionState.IN_GAME_STATES),
	REQUEST_RESET_NICKNAME(0x4D, RequestResetNickname::new, ConnectionState.IN_GAME_STATES),
	EX_BOOKMARK_PACKET(0x4E, null, true, ConnectionState.IN_GAME_STATES),
	REQUEST_WITHDRAW_PREMIUM_ITEM(0x4F, RequestWithDrawPremiumItem::new, ConnectionState.IN_GAME_STATES),
	REQUEST_EX_JUMP(0x50, RequestExJump::new, ConnectionState.IN_GAME_STATES),
	REQUEST_EX_START_SHOW_CRATAE_CUBE_RANK(0x51, RequestStartShowKrateisCubeRank::new, ConnectionState.IN_GAME_STATES),
	REQUEST_EX_STOP_SHOW_CRATAE_CUBE_RANK(0x52, RequestStopShowKrateisCubeRank::new, ConnectionState.IN_GAME_STATES),
	NOTIFY_START_MINI_GAME(0x53, null, ConnectionState.IN_GAME_STATES),
	REQUEST_EX_JOIN_DOMINION_WAR(0x54, null, ConnectionState.IN_GAME_STATES),
	REQUEST_EX_DOMINION_INFO(0x55, null, ConnectionState.IN_GAME_STATES),
	REQUEST_EX_CLEFT_ENTER(0x56, null, ConnectionState.IN_GAME_STATES),
	REQUEST_EX_CUBE_GAME_CHANGE_TEAM(0x57, RequestExCubeGameChangeTeam::new, ConnectionState.IN_GAME_STATES),
	END_SCENE_PLAYER(0x58, EndScenePlayer::new, ConnectionState.IN_GAME_STATES),
	REQUEST_EX_CUBE_GAME_READY_ANSWER(0x59, RequestExCubeGameReadyAnswer::new, ConnectionState.IN_GAME_STATES),
	REQUEST_EX_LIST_MPCC_WAITING(0x5A, RequestExListMpccWaiting::new, ConnectionState.IN_GAME_STATES),
	REQUEST_EX_MANAGE_MPCC_ROOM(0x5B, RequestExManageMpccRoom::new, ConnectionState.IN_GAME_STATES),
	REQUEST_EX_JOIN_MPCC_ROOM(0x5C, RequestExJoinMpccRoom::new, ConnectionState.IN_GAME_STATES),
	REQUEST_EX_OUST_FROM_MPCC_ROOM(0x5D, RequestExOustFromMpccRoom::new, ConnectionState.IN_GAME_STATES),
	REQUEST_EX_DISMISS_MPCC_ROOM(0x5E, RequestExDismissMpccRoom::new, ConnectionState.IN_GAME_STATES),
	REQUEST_EX_WITHDRAW_MPCC_ROOM(0x5F, RequestExWithdrawMpccRoom::new, ConnectionState.IN_GAME_STATES),
	REQUEST_SEED_PHASE(0x60, null, ConnectionState.IN_GAME_STATES),
	REQUEST_EX_MPCC_PARTYMASTER_LIST(0x61, RequestExMpccPartymasterList::new, ConnectionState.IN_GAME_STATES),
	REQUEST_POST_ITEM_LIST(0x62, RequestPostItemList::new, ConnectionState.IN_GAME_STATES),
	REQUEST_SEND_POST(0x63, RequestSendPost::new, ConnectionState.IN_GAME_STATES),
	REQUEST_RECEIVED_POST_LIST(0x64, RequestReceivedPostList::new, ConnectionState.IN_GAME_STATES),
	REQUEST_DELETE_RECEIVED_POST(0x65, RequestDeleteReceivedPost::new, ConnectionState.IN_GAME_STATES),
	REQUEST_RECEIVED_POST(0x66, RequestReceivedPost::new, ConnectionState.IN_GAME_STATES),
	REQUEST_POST_ATTACHMENT(0x67, RequestPostAttachment::new, ConnectionState.IN_GAME_STATES),
	REQUEST_REJECT_POST_ATTACHMENT(0x68, RequestRejectPostAttachment::new, ConnectionState.IN_GAME_STATES),
	REQUEST_SENT_POST_LIST(0x69, RequestSentPostList::new, ConnectionState.IN_GAME_STATES),
	REQUEST_DELETE_SENT_POST(0x6A, RequestDeleteSentPost::new, ConnectionState.IN_GAME_STATES),
	REQUEST_SENT_POST(0x6B, RequestSentPost::new, ConnectionState.IN_GAME_STATES),
	REQUEST_CANCEL_POST_ATTACHMENT(0x6C, RequestCancelPostAttachment::new, ConnectionState.IN_GAME_STATES),
	REQUEST_SHOW_NEW_USER_PETITION(0x6D, null, ConnectionState.IN_GAME_STATES),
	REQUEST_SHOW_STEP_TWO(0x6E, null, ConnectionState.IN_GAME_STATES),
	REQUEST_SHOW_STEP_THREE(0x6F, null, ConnectionState.IN_GAME_STATES),
	EX_CONNECT_TO_RAID_SERVER(0x70, null, ConnectionState.IN_GAME_STATES),
	EX_RETURN_FROM_RAID_SERVER(0x71, null, ConnectionState.IN_GAME_STATES),
	REQUEST_REFUND_ITEM(0x72, RequestRefundItem::new, ConnectionState.IN_GAME_STATES),
	REQUEST_BUI_SELL_UI_CLOSE(0x73, RequestBuySellUIClose::new, ConnectionState.IN_GAME_STATES),
	REQUEST_EX_EVENT_MATCH_OBSERVER_END(0x74, null, ConnectionState.IN_GAME_STATES),
	REQUEST_PARTY_LOOT_MODIFICATION(0x75, RequestPartyLootModification::new, ConnectionState.IN_GAME_STATES),
	ANSWER_PARTY_LOOT_MODIFICATION(0x76, AnswerPartyLootModification::new, ConnectionState.IN_GAME_STATES),
	ANSWER_COUPLE_ACTION(0x77, AnswerCoupleAction::new, ConnectionState.IN_GAME_STATES),
	BR_EVENT_RANKER_LIST(0x78, BrEventRankerList::new, ConnectionState.IN_GAME_STATES),
	REQUEST_ASK_MEMBER_SHIP(0x79, null, ConnectionState.IN_GAME_STATES),
	REQUEST_ADD_EXPAND_QUEST_ALARM(0x7A, RequestAddExpandQuestAlarm::new, ConnectionState.IN_GAME_STATES),
	REQUEST_VOTE_NEW(0x7B, RequestVoteNew::new, ConnectionState.IN_GAME_STATES),
	REQUEST_SHUTTLE_GET_ON(0x7C, RequestShuttleGetOn::new, ConnectionState.IN_GAME_STATES),
	REQUEST_SHUTTLE_GET_OFF(0x7D, RequestShuttleGetOff::new, ConnectionState.IN_GAME_STATES),
	MOVE_TO_LOCATION_IN_SHUTTLE(0x7E, MoveToLocationInShuttle::new, ConnectionState.IN_GAME_STATES),
	CANNOT_MOVE_ANYMORE_IN_SHUTTLE(0x7F, CannotMoveAnymoreInShuttle::new, ConnectionState.IN_GAME_STATES),
	REQUEST_AGIT_ACTION(0x80, null, ConnectionState.IN_GAME_STATES), // TODO: Implement / HANDLE SWITCH
	REQUEST_EX_ADD_CONTACT_TO_CONTACT_LIST(0x81, RequestExAddContactToContactList::new, ConnectionState.IN_GAME_STATES),
	REQUEST_EX_DELETE_CONTACT_FROM_CONTACT_LIST(0x82, RequestExDeleteContactFromContactList::new, ConnectionState.IN_GAME_STATES),
	REQUEST_EX_SHOW_CONTACT_LIST(0x83, RequestExShowContactList::new, ConnectionState.IN_GAME_STATES),
	REQUEST_EX_FRIEND_LIST_EXTENDED(0x84, RequestExFriendListExtended::new, ConnectionState.IN_GAME_STATES),
	REQUEST_EX_OLYMPIAD_MATCH_LIST_REFRESH(0x85, RequestExOlympiadMatchListRefresh::new, ConnectionState.IN_GAME_STATES),
	REQUEST_BR_GAME_POINT(0x86, RequestBRGamePoint::new, ConnectionState.IN_GAME_STATES),
	REQUEST_BR_PRODUCT_LIST(0x87, RequestBRProductList::new, ConnectionState.IN_GAME_STATES),
	REQUEST_BR_PRODUCT_INFO(0x88, RequestBRProductInfo::new, ConnectionState.IN_GAME_STATES),
	REQUEST_BR_BUI_PRODUCT(0x89, RequestBRBuyProduct::new, ConnectionState.IN_GAME_STATES),
	REQUEST_BR_RECENT_PRODUCT_LIST(0x8A, RequestBRRecentProductList::new, ConnectionState.IN_GAME_STATES),
	REQUEST_BR_MINI_GAME_LOAD_SCORES(0x8B, null, ConnectionState.IN_GAME_STATES),
	REQUEST_BR_MINI_GAME_INSERT_SCORE(0x8C, null, ConnectionState.IN_GAME_STATES),
	REQUEST_EX_BR_LECTURE_MARK(0x8D, null, ConnectionState.IN_GAME_STATES),
	REQUEST_CRYSTALLIZE_ESTIMATE(0x8E, RequestCrystallizeEstimate::new, ConnectionState.IN_GAME_STATES),
	REQUEST_CRYSTALLIZE_ITEM_CANCEL(0x8F, RequestCrystallizeItemCancel::new, ConnectionState.IN_GAME_STATES),
	REQUEST_SCENE_EX_ESCAPE_SCENE(0x90, RequestExEscapeScene::new, ConnectionState.IN_GAME_STATES),
	REQUEST_FLY_MOVE(0x91, RequestFlyMove::new, ConnectionState.IN_GAME_STATES),
	REQUEST_SURRENDER_PLEDGE_WAR_EX(0x92, null, ConnectionState.IN_GAME_STATES),
	REQUEST_DYNAMIC_QUEST_ACTION(0x93, null, ConnectionState.IN_GAME_STATES), // TODO: Implement / HANDLE SWITCH
	REQUEST_FRIEND_DETAIL_INFO(0x94, RequestFriendDetailInfo::new, ConnectionState.IN_GAME_STATES),
	REQUEST_UPDATE_FRIEND_MEMO(0x95, null, ConnectionState.IN_GAME_STATES),
	REQUEST_UPDATE_BLOCK_MEMO(0x96, null, ConnectionState.IN_GAME_STATES),
	REQUEST_INZONE_PARTY_INFO_HISTORY(0x97, null, ConnectionState.IN_GAME_STATES),
	REQUEST_COMMISSION_REGISTRABLE_ITEM_LIST(0x98, RequestCommissionRegistrableItemList::new, ConnectionState.IN_GAME_STATES),
	REQUEST_COMMISSION_INFO(0x99, RequestCommissionInfo::new, ConnectionState.IN_GAME_STATES),
	REQUEST_COMMISSION_REGISTER(0x9A, RequestCommissionRegister::new, ConnectionState.IN_GAME_STATES),
	REQUEST_COMMISSION_CANCEL(0x9B, RequestCommissionCancel::new, ConnectionState.IN_GAME_STATES),
	REQUEST_COMMISSION_DELETE(0x9C, RequestCommissionDelete::new, ConnectionState.IN_GAME_STATES),
	REQUEST_COMMISSION_LIST(0x9D, RequestCommissionList::new, ConnectionState.IN_GAME_STATES),
	REQUEST_COMMISSION_BUY_INFO(0x9E, RequestCommissionBuyInfo::new, ConnectionState.IN_GAME_STATES),
	REQUEST_COMMISSION_BUY_ITEM(0x9F, RequestCommissionBuyItem::new, ConnectionState.IN_GAME_STATES),
	REQUEST_COMMISSION_REGISTERED_ITEM(0xA0, RequestCommissionRegisteredItem::new, ConnectionState.IN_GAME_STATES),
	REQUEST_CALL_TO_CHANGE_CLASS(0xA1, null, ConnectionState.IN_GAME_STATES),
	REQUEST_CHANGE_TO_AWAKENED_CLASS(0xA2, RequestChangeToAwakenedClass::new, ConnectionState.IN_GAME_STATES),
	REQUEST_WORLD_STATISTICS(0xA3, null, ConnectionState.IN_GAME_STATES),
	REQUEST_USER_STATISTICS(0xA4, null, ConnectionState.IN_GAME_STATES),
	REQUEST_24HZ_SESSION_ID(0xA5, null, ConnectionState.IN_GAME_STATES),
	REQUEST_GOODS_INVENTORY_INFO(0xAA, null, ConnectionState.IN_GAME_STATES),
	REQUEST_GOODS_INVENTORY_ITEM(0xAB, null, ConnectionState.IN_GAME_STATES),
	REQUEST_FIRST_PLAY_START(0xAC, null, ConnectionState.IN_GAME_STATES),
	REQUEST_FLY_MOVE_START(0xAD, RequestFlyMoveStart::new, ConnectionState.IN_GAME_STATES),
	REQUEST_HARDWARE_INFO(0xAE, RequestHardWareInfo::new, ConnectionState.ALL),
	USER_INTERFACE_INFO(0xAF, null, ConnectionState.IN_GAME_STATES),
	SEND_CHANGE_ATTRIBUTE_TARGET_ITEM(0xB0, SendChangeAttributeTargetItem::new, ConnectionState.IN_GAME_STATES),
	REQUEST_CHANGE_ATTRIBUTE_ITEM(0xB1, RequestChangeAttributeItem::new, ConnectionState.IN_GAME_STATES),
	REQUEST_CHANGE_ATTRIBUTE_CANCEL(0xB2, RequestChangeAttributeCancel::new, ConnectionState.IN_GAME_STATES),
	REQUEST_BR_PRESENT_BUY_PRODUCT(0xB3, RequestBRPresentBuyProduct::new, ConnectionState.IN_GAME_STATES),
	CONFIRM_MENTEE_ADD(0xB4, ConfirmMenteeAdd::new, ConnectionState.IN_GAME_STATES),
	REQUEST_MENTOR_CANCEL(0xB5, RequestMentorCancel::new, ConnectionState.IN_GAME_STATES),
	REQUEST_MENTOR_LIST(0xB6, RequestMentorList::new, ConnectionState.IN_GAME_STATES),
	REQUEST_MENTEE_ADD(0xB7, RequestMenteeAdd::new, ConnectionState.IN_GAME_STATES),
	REQUEST_MENTEE_WAITING_LIST(0xB8, RequestMenteeWaitingList::new, ConnectionState.IN_GAME_STATES),
	REQUEST_CLAN_ASK_JOIN_BY_NAME(0xB9, RequestClanAskJoinByName::new, ConnectionState.IN_GAME_STATES),
	REQUEST_IN_ZONE_WAITING_TIME(0xBA, RequestInzoneWaitingTime::new, ConnectionState.IN_GAME_STATES),
	REQUEST_JOIN_CURIOUS_HOUSE(0xBB, RequestJoinCuriousHouse::new, ConnectionState.IN_GAME_STATES),
	REQUEST_CANCEL_CURIOUS_HOUSE(0xBC, RequestCancelCuriousHouse::new, ConnectionState.IN_GAME_STATES),
	REQUEST_LEAVE_CURIOUS_HOUSE(0xBD, null, ConnectionState.IN_GAME_STATES),
	REQUEST_OBSERVING_LIST_CURIOUS_HOUSE(0xBE, null, ConnectionState.IN_GAME_STATES),
	REQUEST_OBSERVING_CURIOUS_HOUSE(0xBF, null, ConnectionState.IN_GAME_STATES),
	REQUEST_LEAVE_OBSERVING_CURIOUS_HOUSE(0xC0, null, ConnectionState.IN_GAME_STATES),
	REQUEST_CURIOUS_HOUSE_HTML(0xC1, RequestCuriousHouseHtml::new, ConnectionState.IN_GAME_STATES),
	REQUEST_CURIOUS_HOUSE_RECORD(0xC2, null, ConnectionState.IN_GAME_STATES),
	EX_SYSSTRING(0xC3, null, ConnectionState.IN_GAME_STATES),
	REQUEST_EX_TRY_TO_PUT_SHAPE_SHIFTING_TARGET_ITEM(0xC4, RequestExTryToPutShapeShiftingTargetItem::new, ConnectionState.IN_GAME_STATES),
	REQUEST_EX_TRY_TO_PUT_SHAPE_SHIFTING_ENCHANT_SUPPORT_ITEM(0xC5, RequestExTryToPutShapeShiftingEnchantSupportItem::new, ConnectionState.IN_GAME_STATES),
	REQUEST_EX_CANCEL_SHAPE_SHIFTING_ITEM(0xC6, RequestExCancelShape_Shifting_Item::new, ConnectionState.IN_GAME_STATES),
	REQUEST_SHAPE_SHIFTING_ITEM(0xC7, RequestShapeShiftingItem::new, ConnectionState.IN_GAME_STATES),
	NC_GUARD_SEND_DATA_TO_SERVER(0xC8, null, ConnectionState.IN_GAME_STATES),
	REQUEST_EVENT_KALIE_TOKEN(0xC9, null, ConnectionState.IN_GAME_STATES),
	REQUEST_SHOW_BEAUTY_LIST(0xCA, RequestShowBeautyList::new, ConnectionState.IN_GAME_STATES),
	REQUEST_REGIST_BEAUTY(0xCB, RequestRegistBeauty::new, ConnectionState.IN_GAME_STATES),
	REQUEST_SHOW_RESET_BEAUTY(0xCC, null, ConnectionState.IN_GAME_STATES),
	REQUEST_SHOW_RESET_SHOP_LIST(0xCD, RequestShowResetShopList::new, ConnectionState.IN_GAME_STATES),
	NET_PING(0xCE, null, ConnectionState.IN_GAME_STATES),
	REQUEST_BR_ADD_BASKET_PRODUCT_INFO(0xCF, null, ConnectionState.IN_GAME_STATES),
	REQUEST_BR_DELETE_BASKET_PRODUCT_INFO(0xD0, null, ConnectionState.IN_GAME_STATES),
	REQUEST_BR_EXIST_NEW_PRODUCT(0xD1, null, ConnectionState.IN_GAME_STATES),
	REQUEST_EX_EVENT_CAMPAIGN_INFO(0xD2, null, ConnectionState.IN_GAME_STATES),
	REQUEST_PLEDGE_RECRUIT_INFO(0xD3, RequestPledgeRecruitInfo::new, ConnectionState.IN_GAME_STATES),
	REQUEST_PLEDGE_RECRUIT_BOARD_SEARCH(0xD4, RequestPledgeRecruitBoardSearch::new, ConnectionState.IN_GAME_STATES),
	REQUEST_PLEDGE_RECRUIT_BOARD_ACCESS(0xD5, RequestPledgeRecruitBoardAccess::new, ConnectionState.IN_GAME_STATES),
	REQUEST_PLEDGE_RECRUIT_BOARD_DETAIL(0xD6, RequestPledgeRecruitBoardDetail::new, ConnectionState.IN_GAME_STATES),
	REQUEST_PLEDGE_WAITING_APPLY(0xD7, RequestPledgeWaitingApply::new, ConnectionState.IN_GAME_STATES),
	REQUEST_PLEDGE_WAITING_APPLIED(0xD8, RequestPledgeWaitingApplied::new, ConnectionState.IN_GAME_STATES),
	REQUEST_PLEDGE_WAITING_LIST(0xD9, RequestPledgeWaitingList::new, ConnectionState.IN_GAME_STATES),
	REQUEST_PLEDGE_WAITING_USER(0xDA, RequestPledgeWaitingUser::new, ConnectionState.IN_GAME_STATES),
	REQUEST_PLEDGE_WAITING_USER_ACCEPT(0xDB, RequestPledgeWaitingUserAccept::new, ConnectionState.IN_GAME_STATES),
	REQUEST_PLEDGE_DRAFT_LIST_SEARCH(0xDC, RequestPledgeDraftListSearch::new, ConnectionState.IN_GAME_STATES),
	REQUEST_PLEDGE_DRAFT_LIST_APPLY(0xDD, RequestPledgeDraftListApply::new, ConnectionState.IN_GAME_STATES),
	REQUEST_PLEDGE_RECRUIT_APPLY_INFO(0xDE, RequestPledgeRecruitApplyInfo::new, ConnectionState.IN_GAME_STATES),
	REQUEST_PLEDGE_JOIN_SYS(0xDF, null, ConnectionState.IN_GAME_STATES),
	RESPONSE_PETITION_ALARM(0xE0, null, ConnectionState.IN_GAME_STATES),
	NOTIFY_EXIT_BEAUTY_SHOP(0xE1, NotifyExitBeautyShop::new, ConnectionState.IN_GAME_STATES),
	REQUEST_REGISTER_XMAS_WISH_CARD(0xE2, null, ConnectionState.IN_GAME_STATES),
	REQUEST_EX_ADD_ENCHANT_SCROLL_ITEM(0xE3, RequestExAddEnchantScrollItem::new, ConnectionState.IN_GAME_STATES),
	REQUEST_EX_REMOVE_ENCHANT_SUPPORT_ITEM(0xE4, RequestExRemoveEnchantSupportItem::new, ConnectionState.IN_GAME_STATES),
	REQUEST_CARD_REWARD(0xE5, null, ConnectionState.IN_GAME_STATES),
	REQUEST_DIVIDE_ADENA_START(0xE6, RequestDivideAdenaStart::new, ConnectionState.IN_GAME_STATES),
	REQUEST_DIVIDE_ADENA_CANCEL(0xE7, RequestDivideAdenaCancel::new, ConnectionState.IN_GAME_STATES),
	REQUEST_DIVIDE_ADENA(0xE8, RequestDivideAdena::new, ConnectionState.IN_GAME_STATES),
	REQUEST_ACQUIRE_ABILITY_LIST(0xE9, null, ConnectionState.IN_GAME_STATES),
	REQUEST_ABILITY_LIST(0xEA, null, ConnectionState.IN_GAME_STATES),
	REQUEST_RESET_ABILITY_POINT(0xEB, null, ConnectionState.IN_GAME_STATES),
	REQUEST_CHANGE_ABILITY_POINT(0xEC, null, ConnectionState.IN_GAME_STATES),
	REQUEST_STOP_MOVE(0xED, RequestStopMove::new, ConnectionState.IN_GAME_STATES),
	REQUEST_ABILITY_WND_OPEN(0xEE, null, ConnectionState.IN_GAME_STATES),
	REQUEST_ABILITY_WND_CLOSE(0xEF, null, ConnectionState.IN_GAME_STATES),
	REQUEST_LUCKY_GAME_START_INFO(0xF0, RequestLuckyGameStartInfo::new, ConnectionState.IN_GAME_STATES),
	REQUEST_LUCKY_GAME_PLAY(0xF1, RequestLuckyGamePlay::new, ConnectionState.IN_GAME_STATES),
	NOTIFY_TRAINING_ROOM_END(0xF2, NotifyTrainingRoomEnd::new, ConnectionState.IN_GAME_STATES),
	REQUEST_NEW_ENCHANT_PUSH_ONE(0xF3, RequestNewEnchantPushOne::new, ConnectionState.IN_GAME_STATES),
	REQUEST_NEW_ENCHANT_REMOVE_ONE(0xF4, RequestNewEnchantRemoveOne::new, ConnectionState.IN_GAME_STATES),
	REQUEST_NEW_ENCHANT_PUSH_TWO(0xF5, RequestNewEnchantPushTwo::new, ConnectionState.IN_GAME_STATES),
	REQUEST_NEW_ENCHANT_REMOVE_TWO(0xF6, RequestNewEnchantRemoveTwo::new, ConnectionState.IN_GAME_STATES),
	REQUEST_NEW_ENCHANT_CLOSE(0xF7, RequestNewEnchantClose::new, ConnectionState.IN_GAME_STATES),
	REQUEST_NEW_ENCHANT_TRY(0xF8, RequestNewEnchantTry::new, ConnectionState.IN_GAME_STATES),
	REQUEST_NEW_ENCHANT_RETRY_TO_PUT_ITEMS(0xF9, RequestNewEnchantRetryToPutItems::new, ConnectionState.IN_GAME_STATES),
	EX_REQUEST_CARD_REWARD_LIST(0xFA, null, ConnectionState.IN_GAME_STATES),
	EX_REQUEST_ACCOUNT_ATTENDANCE_INFO(0xFB, null, ConnectionState.IN_GAME_STATES),
	EX_REQUEST_ACCOUNT_ATTENDANCE_REWARD(0xFC, null, ConnectionState.IN_GAME_STATES),
	REQUEST_TARGET_ACTION_MENU(0xFD, RequestTargetActionMenu::new, ConnectionState.IN_GAME_STATES),
	EX_SEND_SELECTED_QUEST_ZONE_ID(0xFE, ExSendSelectedQuestZoneID::new, ConnectionState.IN_GAME_STATES),
	REQUEST_ALCHEMY_SKILL_LIST(0xFF, RequestAlchemySkillList::new, ConnectionState.IN_GAME_STATES),
	REQUEST_ALCHEMY_TRY_MIX_CUBE(0x100, null, ConnectionState.IN_GAME_STATES),
	REQUEST_ALCHEMY_CONVERSION(0x101, null, ConnectionState.IN_GAME_STATES),
	SEND_EXECUTED_UI_EVENTS_COUNT(0x102, null, ConnectionState.IN_GAME_STATES),
	EX_SEND_CLIENT_INI(0x103, null, ConnectionState.AUTHENTICATED_STATES),
	REQUEST_EX_AUTO_FISH(0x104, ExRequestAutoFish::new, ConnectionState.IN_GAME_STATES),
	REQUEST_VIP_ATTENDANCE_ITEM_LIST(0x105, RequestVipAttendanceItemList::new, ConnectionState.IN_GAME_STATES),
	REQUEST_VIP_ATTENDANCE_CHECK(0x106, RequestVipAttendanceCheck::new, ConnectionState.IN_GAME_STATES),
	REQUEST_ITEM_ENSOUL(0x107, RequestItemEnsoul::new, ConnectionState.IN_GAME_STATES),
	REQUEST_CASTLE_WAR_SEASON_REWARD(0x108, null, ConnectionState.IN_GAME_STATES),
	REQUEST_VIP_PRODUCT_LIST(0x109, null, ConnectionState.IN_GAME_STATES),
	REQUEST_VIP_LUCKY_GAME_INFO(0x10A, RequestVipLuckGameInfo::new, ConnectionState.IN_GAME_STATES),
	REQUEST_VIP_LUCKY_GAME_ITEM_LIST(0x10B, null, ConnectionState.IN_GAME_STATES),
	REQUEST_VIP_LUCKY_GAME_BONUS(0x10C, null, ConnectionState.IN_GAME_STATES),
	EX_REQUEST_VIP_INFO(0x10D, ExRequestVipInfo::new, ConnectionState.IN_GAME_STATES),
	REQUEST_CAPTCHA_ANSWER(0x10E, null, ConnectionState.IN_GAME_STATES),
	REQUEST_REFRESH_CAPTCHA_IMAGE(0x10F, null, ConnectionState.IN_GAME_STATES),
	REQUEST_PLEDGE_SIGN_IN_FOR_OPEN_JOINING_METHOD(0x110, RequestPledgeSignInForOpenJoiningMethod::new, ConnectionState.IN_GAME_STATES),
	EX_REQUEST_MATCH_ARENA(0x111, null, ConnectionState.IN_GAME_STATES),
	EX_CONFIRM_MATCH_ARENA(0x112, null, ConnectionState.IN_GAME_STATES),
	EX_CANCEL_MATCH_ARENA(0x113, null, ConnectionState.IN_GAME_STATES),
	EX_CHANGE_CLASS_ARENA(0x114, null, ConnectionState.IN_GAME_STATES),
	EX_CONFIRM_CLASS_ARENA(0x115, null, ConnectionState.IN_GAME_STATES),
	REQUEST_OPEN_DECO_NPCUI(0x116, null, ConnectionState.IN_GAME_STATES),
	REQUEST_CHECK_AGIT_DECO_AVAILABILITY(0x117, null, ConnectionState.IN_GAME_STATES),
	REQUEST_USER_FACTION_INFO(0x118, null, ConnectionState.IN_GAME_STATES),
	EX_EXIT_ARENA(0x119, null, ConnectionState.IN_GAME_STATES),
	REQUEST_EVENT_BALTHUS_TOKEN(0x11A, RequestEventBalthusToken::new, ConnectionState.IN_GAME_STATES),
	REQUEST_PARTY_MATCHING_HISTORY(0x11B, null, ConnectionState.IN_GAME_STATES),
	EX_ARENA_CUSTOM_NOTIFICATION(0x11C, null, ConnectionState.IN_GAME_STATES),
	REQUEST_TODO_LIST(0x11D, RequestTodoList::new, ConnectionState.ENTERING_GAME_AND_IN_GAME),
	REQUEST_TODO_LIST_HTML(0x11E, null, ConnectionState.IN_GAME_STATES),
	REQUEST_ONE_DAY_REWARD_RECEIVE(0x11F, RequestOneDayRewardReceive::new, ConnectionState.IN_GAME_STATES),
	REQUEST_QUEUE_TICKET(0x120, null, ConnectionState.IN_GAME_STATES),
	REQUEST_PLEDGE_BONUS_OPEN(0x121, null, ConnectionState.IN_GAME_STATES),
	REQUEST_PLEDGE_BONUS_REWARD_LIST(0x122, null, ConnectionState.IN_GAME_STATES),
	REQUEST_PLEDGE_BONUS_REWARD(0x123, null, ConnectionState.IN_GAME_STATES),
	REQUEST_SSO_AUTHN_TOKEN(0x124, null, ConnectionState.IN_GAME_STATES),
	REQUEST_QUEUE_TICKET_LOGIN(0x125, null, ConnectionState.IN_GAME_STATES),
	REQUEST_BLOCK_MEMO_INFO(0x126, null, ConnectionState.IN_GAME_STATES),
	REQUEST_TRY_EN_SOUL_EXTRACTION(0x127, RequestTryEnSoulExtraction::new, ConnectionState.IN_GAME_STATES),
	REQUEST_RAIDBOSS_SPAWN_INFO(0x128, RequestRaidBossSpawnInfo::new, ConnectionState.IN_GAME_STATES),
	REQUEST_RAID_SERVER_INFO(0x129, RequestRaidServerInfo::new, ConnectionState.IN_GAME_STATES),
	REQUEST_SHOW_AGIT_SIEGE_INFO(0x12A, null, ConnectionState.IN_GAME_STATES),
	REQUEST_ITEM_AUCTION_STATUS(0x12B, null, ConnectionState.IN_GAME_STATES),
	REQUEST_MONSTER_BOOK_OPEN(0x12C, null, ConnectionState.IN_GAME_STATES),
	REQUEST_MONSTER_BOOK_CLOSE(0x12D, null, ConnectionState.IN_GAME_STATES),
	REQUEST_MONSTER_BOOK_REWARD(0x12E, null, ConnectionState.IN_GAME_STATES),
	EXREQUEST_MATCH_GROUP(0x12F, null, ConnectionState.IN_GAME_STATES),
	EXREQUEST_MATCH_GROUP_ASK(0x130, null, ConnectionState.IN_GAME_STATES),
	EXREQUEST_MATCH_GROUP_ANSWER(0x131, null, ConnectionState.IN_GAME_STATES),
	EXREQUEST_MATCH_GROUP_WITHDRAW(0x132, null, ConnectionState.IN_GAME_STATES),
	EXREQUEST_MATCH_GROUP_OUST(0x133, null, ConnectionState.IN_GAME_STATES),
	EXREQUEST_MATCH_GROUP_CHANGE_MASTER(0x134, null, ConnectionState.IN_GAME_STATES),
	REQUEST_UPGRADE_SYSTEM_RESULT(0x135, null, ConnectionState.IN_GAME_STATES),
	EX_CARD_UPDOWN_PICK_NUMB(0x136, null, ConnectionState.IN_GAME_STATES),
	EX_CARD_UPDOWN_GAME_REWARD_REQUEST(0x137, null, ConnectionState.IN_GAME_STATES),
	EX_CARD_UPDOWN_GAME_RETRY(0x138, null, ConnectionState.IN_GAME_STATES),
	EX_CARD_UPDOWN_GAME_QUIT(0x139, null, ConnectionState.IN_GAME_STATES),
	EX_ARENA_RANK_ALL(0x13A, null, ConnectionState.IN_GAME_STATES),
	EX_ARENA_MYRANK(0x13B, null, ConnectionState.IN_GAME_STATES),
	EX_SWAP_AGATHION_SLOT_ITEMS(0x13C, null, ConnectionState.IN_GAME_STATES),
	EX_PLEDGE_CONTRIBUTION_RANK(0x13D, null, ConnectionState.IN_GAME_STATES),
	EX_PLEDGE_CONTRIBUTION_INFO(0x13E, null, ConnectionState.IN_GAME_STATES),
	EX_PLEDGE_CONTRIBUTION_REWARD(0x13F, null, ConnectionState.IN_GAME_STATES),
	EX_PLEDGE_LEVEL_UP(0x140, null, ConnectionState.IN_GAME_STATES),
	EX_PLEDGE_MISSION_INFO(0x141, null, ConnectionState.IN_GAME_STATES),
	EX_PLEDGE_MISSION_REWARD(0x142, null, ConnectionState.IN_GAME_STATES),
	EX_PLEDGE_MASTERY_INFO(0x143, null, ConnectionState.IN_GAME_STATES),
	EX_PLEDGE_MASTERY_SET(0x144, null, ConnectionState.IN_GAME_STATES),
	EX_PLEDGE_MASTERY_RESET(0x145, null, ConnectionState.IN_GAME_STATES),
	EX_PLEDGE_SKILL_INFO(0x146, null, ConnectionState.IN_GAME_STATES),
	EX_PLEDGE_SKILL_ACTIVATE(0x147, null, ConnectionState.IN_GAME_STATES),
	EX_PLEDGE_ITEM_LIST(0x148, null, ConnectionState.IN_GAME_STATES),
	EX_PLEDGE_ITEM_ACTIVATE(0x149, null, ConnectionState.IN_GAME_STATES),
	EX_PLEDGE_ANNOUNCE(0x14A, null, ConnectionState.IN_GAME_STATES),
	EX_PLEDGE_ANNOUNCE_SET(0x14B, null, ConnectionState.IN_GAME_STATES),
	EX_CREATE_PLEDGE(0x14C, null, ConnectionState.IN_GAME_STATES),
	EX_PLEDGE_ITEM_INFO(0x14D, null, ConnectionState.IN_GAME_STATES),
	EX_PLEDGE_ITEM_BUY(0x14E, null, ConnectionState.IN_GAME_STATES),
	EX_ELEMENTAL_SPIRIT_INFO(0x14F, ExElementalSpiritInfo::new, ConnectionState.IN_GAME_STATES),
	EX_ELEMENTAL_SPIRIT_EXTRACT_INFO(0x150, ExElementalSpiritExtractInfo::new, ConnectionState.IN_GAME_STATES),
	EX_ELEMENTAL_SPIRIT_EXTRACT(0x151, ExElementalSpiritExtract::new, ConnectionState.IN_GAME_STATES),
	EX_ELEMENTAL_SPIRIT_EVOLUTION_INFO(0x152, ExElementalSpiritEvolutionInfo::new, ConnectionState.IN_GAME_STATES),
	EX_ELEMENTAL_SPIRIT_EVOLUTION(0x153, ExElementalSpiritEvolution::new, ConnectionState.IN_GAME_STATES),
	EX_ELEMENTAL_SPIRIT_SET_TALENT(0x154, ExElementalSpiritSetTalent::new, ConnectionState.IN_GAME_STATES),
	EX_ELEMENTAL_SPIRIT_INIT_TALENT(0x155, ExElementalInitTalent::new, ConnectionState.IN_GAME_STATES),
	EX_ELEMENTAL_SPIRIT_ABSORB_INFO(0x156, ExElementalSpiritAbsorbInfo::new, ConnectionState.IN_GAME_STATES),
	EX_ELEMENTAL_SPIRIT_ABSORB(0x157, ExElementalSpiritAbsorb::new, ConnectionState.IN_GAME_STATES),
	EX_REQUEST_LOCKED_ITEM(0x158, null, ConnectionState.IN_GAME_STATES),
	EX_REQUEST_UNLOCKED_ITEM(0x159, null, ConnectionState.IN_GAME_STATES),
	EX_LOCKED_ITEM_CANCEL(0x15A, null, ConnectionState.IN_GAME_STATES),
	EX_UNLOCKED_ITEM_CANCEL(0x15B, null, ConnectionState.IN_GAME_STATES),
	// 152
	EX_ELEMENTAL_SPIRIT_CHANGE_TYPE(0x15C, ExElementalSpiritChangeType::new, ConnectionState.IN_GAME_STATES),
	REQUEST_BLOCK_LIST_FOR_AD(0x15D, null, ConnectionState.IN_GAME_STATES),
	REQUEST_USER_BAN_INFO(0x15E, null, ConnectionState.IN_GAME_STATES),
	EX_INTERACT_MODIFY(0x15F, null, ConnectionState.IN_GAME_STATES),
	EX_TRY_ENCHANT_ARTIFACT(0x160, null, ConnectionState.IN_GAME_STATES),
	EX_UPGRADE_SYSTEM_NORMAL_REQUEST(0x161, RequestUpgradeSystemNormal::new, ConnectionState.IN_GAME_STATES),
	EX_PURCHASE_LIMIT_SHOP_ITEM_LIST(0x162, RequestPurchaseLimitShopItemList::new, ConnectionState.IN_GAME_STATES),
	EX_PURCHASE_LIMIT_SHOP_ITEM_BUY(0x163, RequestPurchaseLimitShopItemBuy::new, ConnectionState.IN_GAME_STATES),
	// 228
	EX_OPEN_HTML(0x164, ExOpenHtml::new, ConnectionState.IN_GAME_STATES),
	EX_REQUEST_CLASS_CHANGE(0x165, ExRequestClassChange::new, ConnectionState.IN_GAME_STATES),
	EX_REQUEST_CLASS_CHANGE_VERIFYING(0x166, ExRequestClassChangeVerifying::new, ConnectionState.IN_GAME_STATES),
	EX_REQUEST_TELEPORT(0x167, ExRequestTeleport::new, ConnectionState.IN_GAME_STATES),
	EX_COSTUME_USE_ITEM(0x168, null, ConnectionState.IN_GAME_STATES),
	EX_COSTUME_LIST(0x169, null, ConnectionState.IN_GAME_STATES),
	EX_COSTUME_COLLECTION_SKILL_ACTIVE(0x16A, null, ConnectionState.IN_GAME_STATES),
	EX_COSTUME_EVOLUTION(0x16B, null, ConnectionState.IN_GAME_STATES),
	EX_COSTUME_EXTRACT(0x16C, null, ConnectionState.IN_GAME_STATES),
	EX_COSTUME_LOCK(0x16D, null, ConnectionState.IN_GAME_STATES),
	EX_COSTUME_CHANGE_SHORTCUT(0x16E, null, ConnectionState.IN_GAME_STATES),
	EX_MAGICLAMP_GAME_INFO(0x16F, RequestMagicLampGameInfo::new, ConnectionState.IN_GAME_STATES),
	EX_MAGICLAMP_GAME_START(0x170, RequestMagicLampGameStart::new, ConnectionState.IN_GAME_STATES),
	EX_ACTIVATE_AUTO_SHORTCUT(0x171, ExRequestActivateAutoShortcut::new, ConnectionState.IN_GAME_STATES),
	EX_PREMIUM_MANAGER_LINK_HTML(0x172, ExPremiumManagerLinkHtml::new, ConnectionState.IN_GAME_STATES),
	EX_PREMIUM_MANAGER_PASS_CMD_TO_SERVER(0x173, ExPremiumManagerPassCmdToServer::new, ConnectionState.IN_GAME_STATES),
	EX_ACTIVATED_CURSED_TREASURE_BOX_LOCATION(0x174, null, ConnectionState.IN_GAME_STATES),
	EX_PAYBACK_LIST(0x175, null, ConnectionState.IN_GAME_STATES),
	EX_PAYBACK_GIVE_REWARD(0x176, null, ConnectionState.IN_GAME_STATES),
	EX_AUTOPLAY_SETTING(0x177, ExAutoPlaySetting::new, ConnectionState.IN_GAME_STATES),
	EX_OLYMPIAD_MATCH_MAKING(0x178, ExRequestOlympiadMatchMaking::new, ConnectionState.IN_GAME_STATES),
	EX_OLYMPIAD_MATCH_MAKING_CANCEL(0x179, ExRequestOlympiadMatchMakingCancel::new, ConnectionState.IN_GAME_STATES),
	EX_FESTIVAL_BM_INFO(0x17A, ExRequestFestivalBmInfo::new, ConnectionState.IN_GAME_STATES),
	EX_FESTIVAL_BM_GAME(0x17B, ExRequestFestivalBmGame::new, ConnectionState.IN_GAME_STATES),
	EX_GACHA_SHOP_INFO(0x17C, null, ConnectionState.IN_GAME_STATES),
	EX_GACHA_SHOP_GACHA_GROUP(0x17D, null, ConnectionState.IN_GAME_STATES),
	EX_GACHA_SHOP_GACHA_ITEM(0x17E, null, ConnectionState.IN_GAME_STATES),
	EX_TIME_RESTRICT_FIELD_LIST(0x17F, ExRequestTimeRestrictFieldList::new, ConnectionState.IN_GAME_STATES),
	EX_TIME_RESTRICT_FIELD_USER_ENTER(0x180, ExRequestTimeRestrictFieldUserEnter::new, ConnectionState.IN_GAME_STATES),
	EX_TIME_RESTRICT_FIELD_USER_LEAVE(0x181, ExRequestTimeRestrictFieldUserLeave::new, ConnectionState.IN_GAME_STATES),
	EX_RANKING_CHAR_INFO(0x182, RequestRankingCharInfo::new, ConnectionState.IN_GAME_STATES),
	EX_RANKING_CHAR_HISTORY(0x183, RequestRankingCharHistory::new, ConnectionState.IN_GAME_STATES),
	EX_RANKING_CHAR_RANKERS(0x184, RequestRankingCharRankers::new, ConnectionState.IN_GAME_STATES),
	EX_RANKING_CHAR_SPAWN_BUFFZONE_NPC(0x185, RequestRankingCharSpawnBuffzoneNpc::new, ConnectionState.IN_GAME_STATES),
	EX_RANKING_CHAR_BUFFZONE_NPC_POSITION(0x186, RequestRankingCharBuffzoneNpcPosition::new, ConnectionState.IN_GAME_STATES),
	EX_PLEDGE_MERCENARY_RECRUIT_INFO_SET(0x187, null, ConnectionState.IN_GAME_STATES),
	EX_MERCENARY_CASTLEWAR_CASTLE_INFO(0x188, ExRequestMercenaryCastleWarCastleInfo::new, ConnectionState.IN_GAME_STATES),
	EX_MERCENARY_CASTLEWAR_CASTLE_SIEGE_INFO(0x189, ExRequestMercenaryCastleWarCastleSiegeInfo::new, ConnectionState.IN_GAME_STATES),
	EX_MERCENARY_CASTLEWAR_CASTLE_SIEGE_ATTACKER_LIST(0x18A, ExRequestMercenaryCastleWarCastleSiegeAttackerList::new, ConnectionState.IN_GAME_STATES),
	EX_MERCENARY_CASTLEWAR_CASTLE_SIEGE_DEFENDER_LIST(0x18B, ExRequestMercenaryCastleWarCastleSiegeDefenderList::new, ConnectionState.IN_GAME_STATES),
	EX_PLEDGE_MERCENARY_MEMBER_LIST(0x18C, null, ConnectionState.IN_GAME_STATES),
	EX_PLEDGE_MERCENARY_MEMBER_JOIN(0x18D, null, ConnectionState.IN_GAME_STATES),
	EX_PVP_BOOK_LIST(0x18E, null, ConnectionState.IN_GAME_STATES),
	EX_PVPBOOK_KILLER_LOCATION(0x18F, null, ConnectionState.IN_GAME_STATES),
	EX_PVPBOOK_TELEPORT_TO_KILLER(0x190, null, ConnectionState.IN_GAME_STATES),
	EX_LETTER_COLLECTOR_TAKE_REWARD(0x191, ExRequestLetterCollectorTakeReward::new, ConnectionState.IN_GAME_STATES),
	EX_SET_STATUS_BONUS(0x192, ExSetStatusBonus::new, ConnectionState.IN_GAME_STATES),
	EX_RESET_STATUS_BONUS(0x193, ExResetStatusBonus::new, ConnectionState.IN_GAME_STATES),
	EX_OLYMPIAD_MY_RANKING_INFO(0x194, RequestOlympiadMyRankingInfo::new, ConnectionState.IN_GAME_STATES),
	EX_OLYMPIAD_RANKING_INFO(0x195, RequestOlympiadRankingInfo::new, ConnectionState.IN_GAME_STATES),
	EX_OLYMPIAD_HERO_AND_LEGEND_INFO(0x196, RequestOlympiadHeroAndLegendInfo::new, ConnectionState.IN_GAME_STATES),
	EX_CASTLEWAR_OBSERVER_START(0x197, null/* ExRequestCastleWarObserverStart::new */, ConnectionState.IN_GAME_STATES),
	EX_RAID_TELEPORT_INFO(0x198, RequestRaidTeleportInfo::new, ConnectionState.IN_GAME_STATES),
	EX_TELEPORT_TO_RAID_POSITION(0x199, RequestTeleportToRaidPosition::new, ConnectionState.IN_GAME_STATES),
	EX_CRAFT_EXTRACT(0x19A, ExRequestRandomCraftExtract::new, ConnectionState.IN_GAME_STATES),
	EX_CRAFT_RANDOM_INFO(0x19B, ExRequestRandomCraftInfo::new, ConnectionState.IN_GAME_STATES),
	EX_CRAFT_RANDOM_LOCK_SLOT(0x19C, ExRequestRandomCraftLockSlot::new, ConnectionState.IN_GAME_STATES),
	EX_CRAFT_RANDOM_REFRESH(0x19D, ExRequestRandomCraftRefresh::new, ConnectionState.IN_GAME_STATES),
	EX_CRAFT_RANDOM_MAKE(0x19E, ExRequestRandomCraftMake::new, ConnectionState.IN_GAME_STATES),
	EX_MULTI_SELL_LIST(0x19F, ExRequestMultisellList::new, ConnectionState.IN_GAME_STATES),
	EX_SAVE_ITEM_ANNOUNCE_SETTING(0x1A0, ExRequestSaveItemAnnounceSetting::new, ConnectionState.IN_GAME_STATES),
	EX_OLYMPIAD_UI(0x1A1, ExRequestOlympiadUI::new, ConnectionState.IN_GAME_STATES),
	// 270
	EX_SHARED_POSITION_SHARING_UI(0x1A2, RequestSharedPositionSharingUI::new, ConnectionState.IN_GAME_STATES),
	EX_SHARED_POSITION_TELEPORT_UI(0x1A3, RequestSharedPositionTeleportUI::new, ConnectionState.IN_GAME_STATES),
	EX_SHARED_POSITION_TELEPORT(0x1A4, RequestSharedPositionTeleport::new, ConnectionState.IN_GAME_STATES),
	EX_AUTH_RECONNECT(0x1A5, null, ConnectionState.IN_GAME_STATES),
	EX_PET_EQUIP_ITEM(0x1A6, ExRequestPetEquipItem::new, ConnectionState.IN_GAME_STATES),
	EX_PET_UNEQUIP_ITEM(0x1A7, ExRequestPetUnEquipItem::new, ConnectionState.IN_GAME_STATES),
	EX_SHOW_HOMUNCULUS_INFO(0x1A8, null, ConnectionState.IN_GAME_STATES),
	EX_HOMUNCULUS_CREATE_START(0x1A9, null, ConnectionState.IN_GAME_STATES),
	EX_HOMUNCULUS_INSERT(0x1AA, null, ConnectionState.IN_GAME_STATES),
	EX_HOMUNCULUS_SUMMON(0x1AB, null, ConnectionState.IN_GAME_STATES),
	EX_DELETE_HOMUNCULUS_DATA(0x1AC, null, ConnectionState.IN_GAME_STATES),
	EX_REQUEST_ACTIVATE_HOMUNCULUS(0x1AD, null, ConnectionState.IN_GAME_STATES),
	EX_HOMUNCULUS_GET_ENCHANT_POINT(0x1AE, null, ConnectionState.IN_GAME_STATES),
	EX_HOMUNCULUS_INIT_POINT(0x1AF, null, ConnectionState.IN_GAME_STATES),
	EX_EVOLVE_PET(0x1B0, ExRequestEvolvePet::new, ConnectionState.IN_GAME_STATES),
	EX_ENCHANT_HOMUNCULUS_SKILL(0x1B1, null, ConnectionState.IN_GAME_STATES),
	EX_HOMUNCULUS_ENCHANT_EXP(0x1B2, null, ConnectionState.IN_GAME_STATES),
	EX_TELEPORT_FAVORITES_LIST(0x1B3, ExRequestTeleportFavoriteList::new, ConnectionState.IN_GAME_STATES),
	EX_TELEPORT_FAVORITES_UI_TOGGLE(0x1B4, ExRequestTeleportFavoritesUIToggle::new, ConnectionState.IN_GAME_STATES),
	EX_TELEPORT_FAVORITES_ADD_DEL(0x1B5, ExRequestTeleportFavoritesAddDel::new, ConnectionState.IN_GAME_STATES),
	EX_ANTIBOT(0x1B6, null, ConnectionState.IN_GAME_STATES),
	EX_DPSVR(0x1B7, null, ConnectionState.IN_GAME_STATES),
	EX_TENPROTECT_DECRYPT_ERROR(0x1B8, null, ConnectionState.IN_GAME_STATES),
	EX_NET_LATENCY(0x1B9, null, ConnectionState.IN_GAME_STATES),
	EX_MABLE_GAME_OPEN(0x1BA, ExRequestMableGameOpen::new, ConnectionState.IN_GAME_STATES),
	EX_MABLE_GAME_ROLL_DICE(0x1BB, ExRequestMableGameRollDice::new, ConnectionState.IN_GAME_STATES),
	EX_MABLE_GAME_POPUP_OK(0x1BC, ExRequestMableGamePopupOk::new, ConnectionState.IN_GAME_STATES),
	EX_MABLE_GAME_RESET(0x1BD, ExRequestMableGameReset::new, ConnectionState.IN_GAME_STATES),
	EX_MABLE_GAME_CLOSE(0x1BE, ExRequestMableGameClose::new, ConnectionState.IN_GAME_STATES),
	EX_RETURN_TO_ORIGIN(0x1BF, null, ConnectionState.IN_GAME_STATES),
	EX_PK_PENALTY_LIST(0x1C0, ExRequestPkPenaltyList::new, ConnectionState.IN_GAME_STATES),
	EX_PK_PENALTY_LIST_ONLY_LOC(0x1C1, ExRequestPkPenaltyListOnlyLoc::new, ConnectionState.IN_GAME_STATES),
	EX_BLESS_OPTION_PUT_ITEM(0x1C2, ExRequestBlessOptionPutItem::new, ConnectionState.IN_GAME_STATES),
	EX_BLESS_OPTION_ENCHANT(0x1C3, ExRequestBlessOptionEnchant::new, ConnectionState.IN_GAME_STATES),
	EX_BLESS_OPTION_CANCEL(0x1C4, ExRequestBlessOptionCancel::new, ConnectionState.IN_GAME_STATES),
	EX_PVP_RANKING_MY_INFO(0x1C5, RequestPvpRankingMyInfo::new, ConnectionState.IN_GAME_STATES),
	EX_PVP_RANKING_LIST(0x1C6, RequestPvpRankingList::new, ConnectionState.IN_GAME_STATES),
	EX_ACQUIRE_PET_SKILL(0x1C7, ExRequestAcquirePetSkill::new, ConnectionState.IN_GAME_STATES),
	EX_PLEDGE_V3_INFO(0x1C8, RequestExPledgeV3Info::new, ConnectionState.IN_GAME_STATES),
	EX_PLEDGE_ENEMY_INFO_LIST(0x1C9, RequestExPledgeEnemyInfoList::new, ConnectionState.IN_GAME_STATES),
	EX_PLEDGE_ENEMY_REGISTER(0x1CA, RequestExPledgeEnemyRegister::new, ConnectionState.IN_GAME_STATES),
	EX_PLEDGE_ENEMY_DELETE(0x1CB, RequestExPledgeEnemyDelete::new, ConnectionState.IN_GAME_STATES),
	EX_TRY_PET_EXTRACT_SYSTEM(0x1CC, ExTryPetExtractSystem::new, ConnectionState.IN_GAME_STATES),
	EX_PLEDGE_V3_SET_ANNOUNCE(0x1CD, RequestExPledgeV3SetAnnounce::new, ConnectionState.IN_GAME_STATES),
	// 306
	EX_RANKING_FESTIVAL_OPEN(0x1CE, null, ConnectionState.IN_GAME_STATES),
	EX_RANKING_FESTIVAL_BUY(0x1CF, null, ConnectionState.IN_GAME_STATES),
	EX_RANKING_FESTIVAL_BONUS(0x1D0, null, ConnectionState.IN_GAME_STATES),
	EX_RANKING_FESTIVAL_RANKING(0x1D1, null, ConnectionState.IN_GAME_STATES),
	EX_RANKING_FESTIVAL_MY_RECEIVED_BONUS(0x1D2, null, ConnectionState.IN_GAME_STATES),
	EX_RANKING_FESTIVAL_REWARD(0x1D3, null, ConnectionState.IN_GAME_STATES),
	EX_TIMER_CHECK(0x1D4, ExRequestTimerCheck::new, ConnectionState.IN_GAME_STATES),
	EX_STEADY_BOX_LOAD(0x1D5, ExRequestSteadyBoxLoad::new, ConnectionState.IN_GAME_STATES),
	EX_STEADY_OPEN_SLOT(0x1D6, ExRequestSteadyOpenSlot::new, ConnectionState.IN_GAME_STATES),
	EX_STEADY_OPEN_BOX(0x1D7, ExRequestSteadyOpenBox::new, ConnectionState.IN_GAME_STATES),
	EX_STEADY_GET_REWARD(0x1D8, ExRequestSteadyGetReward::new, ConnectionState.IN_GAME_STATES),
	EX_PET_RANKING_MY_INFO(0x1D9, RequestPetRankingMyInfo::new, ConnectionState.IN_GAME_STATES),
	EX_PET_RANKING_LIST(0x1DA, RequestPetRankingList::new, ConnectionState.IN_GAME_STATES),
	EX_COLLECTION_OPEN_UI(0x1DB, RequestExCollectionOpenUI::new, ConnectionState.IN_GAME_STATES),
	EX_COLLECTION_CLOSE_UI(0x1DC, RequestCollectionCloseUI::new, ConnectionState.IN_GAME_STATES),
	EX_COLLECTION_LIST(0x1DD, RequestExCollectionList::new, ConnectionState.IN_GAME_STATES),
	EX_COLLECTION_UPDATE_FAVORITE(0x1DE, RequestCollectionUpdateFavorite::new, ConnectionState.IN_GAME_STATES),
	EX_COLLECTION_FAVORITE_LIST(0x1DF, RequestCollectionFavoriteList::new, ConnectionState.IN_GAME_STATES),
	EX_COLLECTION_SUMMARY(0x1E0, null, ConnectionState.IN_GAME_STATES),
	EX_COLLECTION_REGISTER(0x1E1, RequestCollectionRegister::new, ConnectionState.IN_GAME_STATES),
	EX_COLLECTION_RECEIVE_REWARD(0x1E2, RequestCollectionReceiveReward::new, ConnectionState.IN_GAME_STATES),
	EX_PVPBOOK_SHARE_REVENGE_LIST(0x1E3, RequestExPvpBookShareRevengeList::new, ConnectionState.IN_GAME_STATES),
	EX_PVPBOOK_SHARE_REVENGE_REQ_SHARE_REVENGEINFO(0x1E4, RequestExPvpBookShareRevengeReqShareRevengeInfo::new, ConnectionState.IN_GAME_STATES),
	EX_PVPBOOK_SHARE_REVENGE_KILLER_LOCATION(0x1E5, RequestExPvpBookShareRevengeKillerLocation::new, ConnectionState.IN_GAME_STATES),
	EX_PVPBOOK_SHARE_REVENGE_TELEPORT_TO_KILLER(0x1E6, RequestExPvpBookShareRevengeTeleportToKiller::new, ConnectionState.IN_GAME_STATES),
	EX_PVPBOOK_SHARE_REVENGE_SHARED_TELEPORT_TO_KILLER(0x1E7, RequestExPvpBookShareRevengeSharedTeleportToKiller::new, ConnectionState.IN_GAME_STATES),
	EX_PENALTY_ITEM_LIST(0x1E8, null, ConnectionState.IN_GAME_STATES),
	EX_PENALTY_ITEM_RESTORE(0x1E9, null, ConnectionState.IN_GAME_STATES),
	EX_USER_WATCHER_TARGET_LIST(0x1EA, ExRequestUserWatcherTargetList::new, ConnectionState.IN_GAME_STATES),
	EX_USER_WATCHER_ADD(0x1EB, ExRequestUserWatcherAdd::new, ConnectionState.IN_GAME_STATES),
	EX_USER_WATCHER_DELETE(0x1EC, ExRequestUserWatcherDelete::new, ConnectionState.IN_GAME_STATES),
	EX_HOMUNCULUS_ACTIVATE_SLOT(0x1ED, null, ConnectionState.IN_GAME_STATES),
	EX_SUMMON_HOMUNCULUS_COUPON(0x1EE, null, ConnectionState.IN_GAME_STATES),
	EX_SUBJUGATION_LIST(0x1EF, ExRequestSubjugationList::new, ConnectionState.IN_GAME_STATES),
	EX_SUBJUGATION_RANKING(0x1F0, ExRequestSubjugationRanking::new, ConnectionState.IN_GAME_STATES),
	EX_SUBJUGATION_GACHA_UI(0x1F1, ExRequestSubjugationGachaUI::new, ConnectionState.IN_GAME_STATES),
	EX_SUBJUGATION_GACHA(0x1F2, ExRequestSubjugationGacha::new, ConnectionState.IN_GAME_STATES),
	EX_PLEDGE_DONATION_INFO(0x1F3, ExRequestPledgeDonationInfo::new, ConnectionState.IN_GAME_STATES),
	EX_PLEDGE_DONATION_REQUEST(0x1F4, ExRequestPledgeDonationRequest::new, ConnectionState.IN_GAME_STATES),
	EX_PLEDGE_CONTRIBUTION_LIST(0x1F5, RequestExPledgeContributionList::new, ConnectionState.IN_GAME_STATES),
	EX_PLEDGE_RANKING_MY_INFO(0x1F6, RequestExPledgeRankingMyInfo::new, ConnectionState.IN_GAME_STATES),
	EX_PLEDGE_RANKING_LIST(0x1F7, RequestExPledgeRankingList::new, ConnectionState.IN_GAME_STATES),
	EX_ITEM_RESTORE_LIST(0x1F8, null, ConnectionState.IN_GAME_STATES),
	EX_ITEM_RESTORE(0x1F9, null, ConnectionState.IN_GAME_STATES),
	// 338
	EX_DETHRONE_INFO(0x1FA, null, ConnectionState.IN_GAME_STATES),
	EX_DETHRONE_RANKING_INFO(0x1FB, null, ConnectionState.IN_GAME_STATES),
	EX_DETHRONE_SERVER_INFO(0x1FC, null, ConnectionState.IN_GAME_STATES),
	EX_DETHRONE_DISTRICT_OCCUPATION_INFO(0x1FD, null, ConnectionState.IN_GAME_STATES),
	EX_DETHRONE_DAILY_MISSION_INFO(0x1FE, null, ConnectionState.IN_GAME_STATES),
	EX_DETHRONE_DAILY_MISSION_GET_REWARD(0x1FF, null, ConnectionState.IN_GAME_STATES),
	EX_DETHRONE_PREV_SEASON_INFO(0x200, null, ConnectionState.IN_GAME_STATES),
	EX_DETHRONE_GET_REWARD(0x201, null, ConnectionState.IN_GAME_STATES),
	EX_DETHRONE_ENTER(0x202, null, ConnectionState.IN_GAME_STATES),
	EX_DETHRONE_LEAVE(0x203, null, ConnectionState.IN_GAME_STATES),
	EX_DETHRONE_CHECK_NAME(0x204, null, ConnectionState.IN_GAME_STATES),
	EX_DETHRONE_CHANGE_NAME(0x205, null, ConnectionState.IN_GAME_STATES),
	EX_DETHRONE_CONNECT_CASTLE(0x206, null, ConnectionState.IN_GAME_STATES),
	EX_DETHRONE_DISCONNECT_CASTLE(0x207, null, ConnectionState.IN_GAME_STATES),
	EX_CHANGE_NICKNAME_COLOR_ICON(0x208, RequestExChangeNicknameColorIcon::new, ConnectionState.IN_GAME_STATES),
	EX_WORLDCASTLEWAR_MOVE_TO_HOST(0x209, null, ConnectionState.IN_GAME_STATES),
	EX_WORLDCASTLEWAR_RETURN_TO_ORIGIN_PEER(0x20A, null, ConnectionState.IN_GAME_STATES),
	EX_WORLDCASTLEWAR_CASTLE_INFO(0x20B, null, ConnectionState.IN_GAME_STATES),
	EX_WORLDCASTLEWAR_CASTLE_SIEGE_INFO(0x20C, null, ConnectionState.IN_GAME_STATES),
	EX_WORLDCASTLEWAR_CASTLE_SIEGE_JOIN(0x20D, null, ConnectionState.IN_GAME_STATES),
	EX_WORLDCASTLEWAR_CASTLE_SIEGE_ATTACKER_LIST(0x20E, null, ConnectionState.IN_GAME_STATES),
	EX_WORLDCASTLEWAR_PLEDGE_MERCENARY_RECRUIT_INFO_SET(0x20F, null, ConnectionState.IN_GAME_STATES),
	EX_WORLDCASTLEWAR_PLEDGE_MERCENARY_MEMBER_LIST(0x210, null, ConnectionState.IN_GAME_STATES),
	EX_WORLDCASTLEWAR_PLEDGE_MERCENARY_MEMBER_JOIN(0x211, null, ConnectionState.IN_GAME_STATES),
	EX_WORLDCASTLEWAR_TELEPORT(0x212, null, ConnectionState.IN_GAME_STATES),
	EX_WORLDCASTLEWAR_OBSERVER_START(0x213, null, ConnectionState.IN_GAME_STATES),
	EX_PRIVATE_STORE_SEARCH_LIST(0x214, ExRequestPrivateStoreSearchList::new, ConnectionState.IN_GAME_STATES),
	EX_PRIVATE_STORE_SEARCH_STATISTICS(0x215, ExRequestPrivateStoreSearchStatistics::new, ConnectionState.IN_GAME_STATES),
	EX_WORLDCASTLEWAR_HOST_CASTLE_SIEGE_RANKING_INFO(0x216, null, ConnectionState.IN_GAME_STATES),
	EX_WORLDCASTLEWAR_CASTLE_SIEGE_RANKING_INFO(0x217, null, ConnectionState.IN_GAME_STATES),
	EX_WORLDCASTLEWAR_SIEGE_MAINBATTLE_HUD_INFO(0x218, null, ConnectionState.IN_GAME_STATES),
	EX_NEW_HENNA_LIST(0x219, ExRequestNewHennaList::new, ConnectionState.IN_GAME_STATES),
	EX_NEW_HENNA_EQUIP(0x21A, ExRequestNewHennaEquip::new, ConnectionState.IN_GAME_STATES),
	EX_NEW_HENNA_UNEQUIP(0x21B, ExRequestNewHennaUnequip::new, ConnectionState.IN_GAME_STATES),
	EX_NEW_HENNA_POTEN_SELECT(0x21C, ExRequestNewHennaPotenSelect::new, ConnectionState.IN_GAME_STATES),
	EX_NEW_HENNA_POTEN_ENCHANT(0x21D, ExRequestNewHennaPotenEnchant::new, ConnectionState.IN_GAME_STATES),
	EX_NEW_HENNA_COMPOSE(0x21E, ExRequestNewHennaCompose::new, ConnectionState.IN_GAME_STATES),
	EX_REQUEST_INVITE_PARTY(0x21F, RequestExRequestInviteParty::new, ConnectionState.IN_GAME_STATES),
	EX_ITEM_USABLE_LIST(0x220, RequestExItemUsableList::new, ConnectionState.IN_GAME_STATES),
	EX_PACKETREADCOUNTPERSECOND(0x221, null, ConnectionState.IN_GAME_STATES),
	EX_SELECT_GLOBAL_EVENT_UI(0x222, null, ConnectionState.IN_GAME_STATES),
	EX_L2PASS_INFO(0x223, RequestL2PassInfo::new, ConnectionState.IN_GAME_STATES),
	EX_L2PASS_REQUEST_REWARD(0x224, RequestL2PassRequestReward::new, ConnectionState.IN_GAME_STATES),
	EX_L2PASS_REQUEST_REWARD_ALL(0x225, RequestL2PassRequestRewardAll::new, ConnectionState.IN_GAME_STATES),
	EX_L2PASS_BUY_PREMIUM(0x226, RequestL2PassBuyPremium::new, ConnectionState.IN_GAME_STATES),
	EX_SAYHAS_SUPPORT_TOGGLE(0x227, RequestSayhasSupportToggle::new, ConnectionState.IN_GAME_STATES),
	EX_REQ_ENCHANT_FAIL_REWARD_INFO(0x228, ExRequestReqEnchantFailRewardInfo::new, ConnectionState.IN_GAME_STATES),
	EX_SET_ENCHANT_CHALLENGE_POINT(0x229, ExRequestSetEnchantChallengePoint::new, ConnectionState.IN_GAME_STATES),
	EX_RESET_ENCHANT_CHALLENGE_POINT(0x22A, ExRequestResetEnchantChallengePoint::new, ConnectionState.IN_GAME_STATES),
	EX_REQ_VIEW_ENCHANT_RESULT(0x22B, ExRequestReqViewEnchantResult::new, ConnectionState.IN_GAME_STATES),
	EX_REQ_START_MULTI_ENCHANT_SCROLL(0x22C, ExRequestReqStartMultiEnchantScroll::new, ConnectionState.IN_GAME_STATES),
	EX_REQ_VIEW_MULTI_ENCHANT_RESULT(0x22D, ExRequestReqViewMultiEnchantResult::new, ConnectionState.IN_GAME_STATES),
	EX_REQ_FINISH_MULTI_ENCHANT_SCROLL(0x22E, ExRequestReqFinishMultiEnchantScroll::new, ConnectionState.IN_GAME_STATES),
	EX_REQ_CHANGE_MULTI_ENCHANT_SCROLL(0x22F, null, ConnectionState.IN_GAME_STATES),
	EX_REQ_SET_MULTI_ENCHANT_ITEM_LIST(0x230, ExRequestReqSetMultiEnchantItemList::new, ConnectionState.IN_GAME_STATES),
	EX_REQ_MULTI_ENCHANT_ITEM_LIST(0x231, ExRequestReqMultiEnchantItemList::new, ConnectionState.IN_GAME_STATES),
	EX_WORLDCASTLEWAR_SUPPORT_PLEDGE_FLAG_SET(0x232, null, ConnectionState.IN_GAME_STATES),
	EX_WORLDCASTLEWAR_SUPPORT_PLEDGE_INFO_SET(0x233, null, ConnectionState.IN_GAME_STATES),
	EX_REQ_HOMUNCULUS_PROB_LIST(0x234, null, ConnectionState.IN_GAME_STATES),
	EX_WORLDCASTLEWAR_HOST_CASTLE_SIEGE_ALL_RANKING_INFO(0x235, null, ConnectionState.IN_GAME_STATES),
	EX_WORLDCASTLEWAR_CASTLE_SIEGE_ALL_RANKING_INFO(0x236, null, ConnectionState.IN_GAME_STATES),
	EX_MISSION_LEVEL_REWARD_LIST(0x2377, ExRequestMissionLevelRewardList::new, ConnectionState.IN_GAME_STATES),
	EX_MISSION_LEVEL_RECEIVE_REWARD(0x238, ExRequestMissionLevelReceiveReward::new, ConnectionState.IN_GAME_STATES),
	EX_BALROGWAR_TELEPORT(0x239, ExBalrogWarTeleport::new, ConnectionState.IN_GAME_STATES),
	EX_BALROGWAR_SHOW_UI(0x23A, ExBalrogWarShowUI::new, ConnectionState.IN_GAME_STATES),
	EX_BALROGWAR_SHOW_RANKING(0x23B, ExBalrogWarShowRanking::new, ConnectionState.IN_GAME_STATES),
	EX_BALROGWAR_GET_REWARD(0x23C, ExBalrogWarGetReward::new, ConnectionState.IN_GAME_STATES),
	EX_USER_RESTART_LOCKER_UPDATE(0x23D, ExRequestUserRestartLockerUpdate::new, ConnectionState.IN_GAME_STATES),
	EX_WORLD_EXCHANGE_ITEM_LIST(0x23E, ExWorldExchangeItemList::new, ConnectionState.IN_GAME_STATES),
	EX_WORLD_EXCHANGE_REGI_ITEM(0x23F, ExWorldExchangeRegisterItem::new, ConnectionState.IN_GAME_STATES),
	EX_WORLD_EXCHANGE_BUY_ITEM(0x240, ExWorldExchangeBuyItem::new, ConnectionState.IN_GAME_STATES),
	EX_WORLD_EXCHANGE_SETTLE_LIST(0x241, ExWorldExchangeSettleList::new, ConnectionState.IN_GAME_STATES),
	EX_WORLD_EXCHANGE_SETTLE_RECV_RESULT(0x242, ExWorldExchangeSettleRecvResult::new, ConnectionState.IN_GAME_STATES),
	EX_READY_ITEM_AUTO_PEEL(0x243, ExRequestReadyItemAutoPeel::new, ConnectionState.IN_GAME_STATES),
	EX_REQUEST_ITEM_AUTO_PEEL(0x244, ExRequestItemAutoPeel::new, ConnectionState.IN_GAME_STATES),
	EX_STOP_ITEM_AUTO_PEEL(0x245, ExRequestStopItemAutoPeel::new, ConnectionState.IN_GAME_STATES),
	EX_VARIATION_OPEN_UI(0x246, ExRequestVariationOpenUI::new, ConnectionState.IN_GAME_STATES),
	EX_VARIATION_CLOSE_UI(0x247, ExRequestVariationCloseUI::new, ConnectionState.IN_GAME_STATES),
	EX_APPLY_VARIATION_OPTION(0x248, ExRequestApplyVariationOption::new, ConnectionState.IN_GAME_STATES),
	EX_REQUEST_AUDIO_LOG_SAVE(0x249, null, ConnectionState.IN_GAME_STATES),
	EX_BR_VERSION(0x24A, RequestBRVersion::new, ConnectionState.AUTHENTICATED_AND_IN_GAME),
	EX_WRANKING_FESTIVAL_INFO(0x24B, null, ConnectionState.IN_GAME_STATES),
	EX_WRANKING_FESTIVAL_OPEN(0x24C, null, ConnectionState.IN_GAME_STATES),
	EX_WRANKING_FESTIVAL_BUY(0x24D, null, ConnectionState.IN_GAME_STATES),
	EX_WRANKING_FESTIVAL_BONUS(0x24E, null, ConnectionState.IN_GAME_STATES),
	EX_WRANKING_FESTIVAL_RANKING(0x24F, null, ConnectionState.IN_GAME_STATES),
	EX_WRANKING_FESTIVAL_MY_RECEIVED_BONUS(0x250, null, ConnectionState.IN_GAME_STATES),
	EX_WRANKING_FESTIVAL_REWARD(0x251, null, ConnectionState.IN_GAME_STATES),
	EX_HENNA_UNEQUIP_INFO(0x252, ExRequestNewHennaUnequipInfo::new, ConnectionState.IN_GAME_STATES),
	EX_HERO_BOOK_CHARGE(0x253, null, ConnectionState.IN_GAME_STATES),
	EX_HERO_BOOK_ENCHANT(0x254, null, ConnectionState.IN_GAME_STATES),
	EX_TELEPORT_UI(0x255, ExRequestTeleportUI::new, ConnectionState.IN_GAME_STATES),
	EX_GOODS_GIFT_LIST_INFO(0x256, null, ConnectionState.IN_GAME_STATES),
	EX_GOODS_GIFT_ACCEPT(0x257, null, ConnectionState.IN_GAME_STATES),
	EX_GOODS_GIFT_REFUSE(0x258, null, ConnectionState.IN_GAME_STATES),
	EX_WORLD_EXCHANGE_AVERAGE_PRICE(0x259, ExWorldExchangeAveragePrice::new, ConnectionState.IN_GAME_STATES),
	EX_WORLD_EXCHANGE_TOTAL_LIST(0x25A, ExWorldExchangeTotalList::new, ConnectionState.IN_GAME_STATES),
	EX_PRISON_USER_INFO(0x25B, null, ConnectionState.IN_GAME_STATES),
	EX_PRISON_USER_DONATION(0x25C, null, ConnectionState.IN_GAME_STATES),
	EX_MAX(0x25D, null, ConnectionState.IN_GAME_STATES);
	
	public static final ExIncomingPackets[]	PACKET_ARRAY;
	private static final PacketFactory		REQUEST_BOOK_MARK_SLOT_INFO		= new DynamicPacketFactory(RequestBookMarkSlotInfo::new);
	private static final PacketFactory		REQUEST_SAVE_BOOK_MARK_SLOT		= new DynamicPacketFactory(RequestSaveBookMarkSlot::new);
	private static final PacketFactory		REQUEST_MODIFY_BOOK_MARK_SLOT	= new DynamicPacketFactory(RequestModifyBookMarkSlot::new);
	private static final PacketFactory		REQUEST_DELETE_BOOK_MARK_SLOT	= new DynamicPacketFactory(RequestDeleteBookMarkSlot::new);
	private static final PacketFactory		REQUEST_TELEPORT_BOOK_MARK		= new DynamicPacketFactory(RequestTeleportBookMark::new);
	private static final PacketFactory		REQUEST_CHANCE_BOOK_MARK_SLOT	= new DynamicPacketFactory(RequestChangeBookMarkSlot::new);
	static
	{
		final short maxPacketId = (short) Arrays.stream(values()).mapToInt(PacketFactory::getPacketId).max().orElse(0);
		PACKET_ARRAY = new ExIncomingPackets[maxPacketId + 1];
		for (ExIncomingPackets incomingPacket : values())
		{
			if (incomingPacket._packetId < 0)
			{
				continue;
			}
			PACKET_ARRAY[incomingPacket.getPacketId()] = incomingPacket;
		}
	}
	private final boolean					_hasExtension;
	private final Supplier<ClientPacket>	_incomingPacketFactory;
	private final EnumSet<ConnectionState>	_connectionStates;
	private int								_packetId;
	
	ExIncomingPackets(int packetId, Supplier<ClientPacket> incomingPacketFactory, boolean hasExtension, EnumSet<ConnectionState> connectionStates)
	{
		_packetId = packetId;
		_incomingPacketFactory = Objects.requireNonNullElse(incomingPacketFactory, NULL_PACKET_SUPLIER);
		_connectionStates = connectionStates;
		_hasExtension = hasExtension;
	}
	
	ExIncomingPackets(int packetId, Supplier<ClientPacket> incomingPacketFactory, EnumSet<ConnectionState> connectionStates)
	{
		this(packetId, incomingPacketFactory, false, connectionStates);
	}
	
	@Override
	public int getPacketId()
	{
		return _packetId;
	}
	
	@Override
	public ClientPacket newIncomingPacket()
	{
		return _incomingPacketFactory.get();
	}
	
	@Override
	public boolean canHandleState(ConnectionState state)
	{
		return _connectionStates.contains(state);
	}
	
	@Override
	public boolean hasExtension()
	{
		return _hasExtension;
	}
	
	@Override
	public PacketFactory handleExtension(GameClient client, ReadableBuffer buffer)
	{
		if (EX_BOOKMARK_PACKET == this)
		{
			return handleBookMarkPaket(buffer);
		}
		return NULLABLE_PACKET_FACTORY;
	}
	
	private PacketFactory handleBookMarkPaket(ReadableBuffer packet)
	{
		return switch (packet.readInt())
		{
			case 0 -> REQUEST_BOOK_MARK_SLOT_INFO;
			case 1 -> REQUEST_SAVE_BOOK_MARK_SLOT;
			case 2 -> REQUEST_MODIFY_BOOK_MARK_SLOT;
			case 3 -> REQUEST_DELETE_BOOK_MARK_SLOT;
			case 4 -> REQUEST_TELEPORT_BOOK_MARK;
			case 5 -> REQUEST_CHANCE_BOOK_MARK_SLOT;
			default -> NULLABLE_PACKET_FACTORY;
		};
	}
	
	@Override
	public EnumSet<ConnectionState> getConnectionStates()
	{
		return _connectionStates;
	}
	
	record DynamicPacketFactory(Supplier<ClientPacket> supplier) implements PacketFactory
	{
		@Override
		public ClientPacket newIncomingPacket()
		{
			return supplier.get();
		}
		
		@Override
		public boolean canHandleState(ConnectionState state)
		{
			return true;
		}
	}
}
