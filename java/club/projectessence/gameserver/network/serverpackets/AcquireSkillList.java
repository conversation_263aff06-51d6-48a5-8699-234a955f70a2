/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.network.serverpackets;

import club.projectessence.commons.network.ServerPacketId;
import club.projectessence.gameserver.data.xml.SkillData;
import club.projectessence.gameserver.data.xml.SkillTreeData;
import club.projectessence.gameserver.model.SkillLearn;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.model.holders.AlternativeItemHolder;
import club.projectessence.gameserver.model.skills.Skill;
import club.projectessence.gameserver.network.GameClient;
import io.github.joealisson.mmocore.WritableBuffer;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> Mobius
 */
public class AcquireSkillList extends ServerPacket {
	private PlayerInstance _player;
	private List<SkillLearn> _learnable;

	public AcquireSkillList(PlayerInstance player) {
		if (!player.isSubclassLocked()) // Changing class.
		{
			_player = player;
			_learnable = SkillTreeData.getInstance().getAvailableSkills(player, player.getClassId(), false, false);
			_learnable.addAll(SkillTreeData.getInstance().getNextAvailableSkills(player, player.getClassId(), false, false));
		}
	}

	@Override
	public void writeImpl(GameClient client, WritableBuffer buffer) {
		if (_player == null) {
			return;
		}

		writeId(ServerPacketId.ACQUIRE_SKILL_LIST, buffer);

		List<SkillLearn> toRemove = new ArrayList<>();
		for (SkillLearn skill : _learnable) {
			if (skill == null) {
				toRemove.add(skill);
				continue;
			}

			Integer skillId = _player.getReplacedSkills().get(skill.getSkillId());
			if (skillId != null) {
				final Skill knownSkill = _player.getKnownSkill(skillId);
				if (knownSkill == null) {
					_player.removeReplacedSkill(skillId);
					toRemove.add(skill);
					continue;
				}
				if (knownSkill.getLevel() == SkillData.getInstance().getMaxLevel(skillId)) {
					toRemove.add(skill);
					continue;
				}
			}
		}
		_learnable.removeAll(toRemove);
		buffer.writeShort(_learnable.size());
		for (SkillLearn skill : _learnable) {
			if (skill == null) {
				continue;
			}

			int skillId = skill.getSkillId();
			boolean replacedSkill = false;
			if (_player.getReplacedSkills().containsKey(skill.getSkillId())) {
				skillId = _player.getReplacedSkills().get(skill.getSkillId());
				replacedSkill = true;
			}
			buffer.writeInt(skillId);
			buffer.writeShort(replacedSkill ? _player.getKnownSkill(skillId).getLevel() + 1 : skill.getSkillLevel());
			buffer.writeLong(skill.getLevelUpSp());
			buffer.writeByte(skill.getGetLevel());
			buffer.writeByte(0x00); // Skill dual class level.
			buffer.writeByte(_player.getKnownSkill(skillId) != null ? 0x00 : 0x01);
			if (skill.getRequiredItems().size() > 0) {
				for (AlternativeItemHolder item : skill.getRequiredItems()) {
					buffer.writeByte(0x01);
					buffer.writeInt(item.getIds().get(0));
					buffer.writeLong(item.getCount());
				}
			} else {
				buffer.writeByte(0x00);
			}
			buffer.writeByte(0x00);
		}
	}
}
