package club.projectessence.gameserver.network.serverpackets.festivalbm;

import club.projectessence.commons.network.ServerPacketId;
import club.projectessence.gameserver.instancemanager.events.GoldFestival.GoldFestivalEventReward;
import club.projectessence.gameserver.network.GameClient;
import club.projectessence.gameserver.network.serverpackets.ServerPacket;
import io.github.joealisson.mmocore.WritableBuffer;

import java.util.List;

/**
 * <AUTHOR>
 */
public class ExFestivalBmTopItemInfo extends ServerPacket {
	private final long _endTime;
	private final boolean _isUseFestivalBm;
	private final List<GoldFestivalEventReward> _activeRewards;

	public ExFestivalBmTopItemInfo(long endTime, boolean isUseFestivalBm, List<GoldFestivalEventReward> activeRewards) {
		_endTime = endTime;
		_isUseFestivalBm = isUseFestivalBm;
		_activeRewards = activeRewards;
	}

	@Override
	public void writeImpl(GameClient client, WritableBuffer buffer) {
		writeId(ServerPacketId.EX_FESTIVAL_BM_TOP_ITEM_INFO, buffer);
		buffer.writeByte(_isUseFestivalBm ? 1 : 2);
		buffer.writeInt(20); // nFestivalBmSeason
		buffer.writeInt((int) _endTime / 1000);
		buffer.writeInt(3); // nSize
		int written = 0;
		if (_activeRewards != null) {
			for (GoldFestivalEventReward reward : _activeRewards) {
				if (reward.isTopGrade()) {
					written++;
					buffer.writeByte(reward._grade);
					buffer.writeInt(reward._id);
					buffer.writeInt((int) reward._currentAmount);
					buffer.writeInt((int) reward._totalAmount);
				}
			}
		}
		for (; written < 3; written++) {
			buffer.writeByte(0);
			buffer.writeInt(0);
			buffer.writeInt(0);
			buffer.writeInt(0);
		}
	}
}