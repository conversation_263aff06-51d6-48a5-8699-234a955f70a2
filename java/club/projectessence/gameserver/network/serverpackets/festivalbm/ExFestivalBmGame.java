package club.projectessence.gameserver.network.serverpackets.festivalbm;

import club.projectessence.commons.network.ServerPacketId;
import club.projectessence.gameserver.network.GameClient;
import club.projectessence.gameserver.network.serverpackets.ServerPacket;
import io.github.joealisson.mmocore.WritableBuffer;

/**
 * <AUTHOR>
 */
public class ExFestivalBmGame extends ServerPacket {
	private final int _ticketId;
	private final int _ticketAmount;
	private final int _ticketAmountPerGame;
	private final int _rewardGrade;
	private final int _rewardId;
	private final int _rewardCount;

	public ExFestivalBmGame(int ticketId, int ticketAmount, int ticketAmountPerGame, int rewardGrade, int rewardId, int rewardCount) {
		_ticketId = ticketId;
		_ticketAmount = ticketAmount;
		_ticketAmountPerGame = ticketAmountPerGame;
		_rewardGrade = rewardGrade;
		_rewardId = rewardId;
		_rewardCount = rewardCount;
	}

	@Override
	public void writeImpl(GameClient client, WritableBuffer buffer) {
		writeId(ServerPacketId.EX_FESTIVAL_BM_GAME, buffer);
		buffer.writeByte(20); // cFestivalBmGameResult
		buffer.writeInt(_ticketId);
		buffer.writeLong(_ticketAmount);
		buffer.writeInt(_ticketAmountPerGame);
		buffer.writeByte(_rewardGrade);
		buffer.writeInt(_rewardId);
		buffer.writeInt(_rewardCount);
	}
}