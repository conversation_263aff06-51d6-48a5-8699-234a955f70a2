package club.projectessence.gameserver.network.serverpackets.festivalbm;

import club.projectessence.commons.network.ServerPacketId;
import club.projectessence.gameserver.instancemanager.events.GoldFestival.GoldFestivalEventReward;
import club.projectessence.gameserver.network.GameClient;
import club.projectessence.gameserver.network.serverpackets.ServerPacket;
import io.github.joealisson.mmocore.WritableBuffer;

import java.util.List;

/**
 * <AUTHOR>
 */
public class ExFestivalBmAllItemInfo extends ServerPacket {
	private final List<GoldFestivalEventReward> _activeRewards;

	public ExFestivalBmAllItemInfo(List<GoldFestivalEventReward> activeRewards) {
		_activeRewards = activeRewards;
	}

	@Override
	public void writeImpl(GameClient client, WritableBuffer buffer) {
		writeId(ServerPacketId.EX_FESTIVAL_BM_ALL_ITEM_INFO, buffer);
		buffer.writeInt(20); // nFestivalBmSeason ( ??? )
		buffer.writeInt(_activeRewards.size());

		for (GoldFestivalEventReward reward : _activeRewards) {
			if (reward.isTopGrade()) {
				buffer.writeByte(reward._grade);
				buffer.writeInt(reward._id);
				buffer.writeInt((int) reward._currentAmount);
				buffer.writeInt((int) reward._totalAmount);
			}
		}
		for (GoldFestivalEventReward reward : _activeRewards) {
			if (reward.isMiddleGrade()) {
				buffer.writeByte(reward._grade);
				buffer.writeInt(reward._id);
				buffer.writeInt((int) reward._currentAmount);
				buffer.writeInt((int) reward._totalAmount);
			}
		}
		for (GoldFestivalEventReward reward : _activeRewards) {
			if (reward.isLowGrade()) {
				buffer.writeByte(reward._grade);
				buffer.writeInt(reward._id);
				buffer.writeInt((int) reward._currentAmount);
				buffer.writeInt((int) reward._totalAmount);
			}
		}
	}
}