package club.projectessence.gameserver.network.serverpackets.festivalbm;

import club.projectessence.commons.network.ServerPacketId;
import club.projectessence.gameserver.network.GameClient;
import club.projectessence.gameserver.network.serverpackets.ServerPacket;
import io.github.joealisson.mmocore.WritableBuffer;

/*
 * <AUTHOR>
 */
public class ExFestivalBmInfo extends ServerPacket {
	private final int _itemId;
	private final int _itemAmount;
	private final int _itemAmountPerGame;

	public ExFestivalBmInfo(int itemId, int itemAmount, int itemAmountPerGame) {
		_itemId = itemId;
		_itemAmount = itemAmount;
		_itemAmountPerGame = itemAmountPerGame;
	}

	@Override
	public void writeImpl(GameClient client, WritableBuffer buffer) {
		writeId(ServerPacketId.EX_FESTIVAL_BM_INFO, buffer);
		buffer.writeInt(_itemId);
		buffer.writeLong(_itemAmount);
		buffer.writeInt(_itemAmountPerGame);
	}
}