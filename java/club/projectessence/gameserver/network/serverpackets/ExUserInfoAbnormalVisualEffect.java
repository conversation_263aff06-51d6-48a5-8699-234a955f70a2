/*
 * This file is part of the L2J Mobius project.
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.network.serverpackets;

import java.util.Set;

import club.projectessence.commons.network.ServerPacketId;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.model.skills.AbnormalVisualEffect;
import club.projectessence.gameserver.model.skills.CommonSkill;
import club.projectessence.gameserver.network.GameClient;
import io.github.joealisson.mmocore.WritableBuffer;

/**
 * <AUTHOR>
 */
public class ExUserInfoAbnormalVisualEffect extends ServerPacket
{
	private final PlayerInstance _player;
	
	public ExUserInfoAbnormalVisualEffect(PlayerInstance player)
	{
		_player = player;
	}
	
	@Override
	public void writeImpl(GameClient client, WritableBuffer buffer)
	{
		writeId(ServerPacketId.EX_USER_INFO_ABNORMAL_VISUAL_EFFECT, buffer);
		buffer.writeInt(_player.getObjectId());
		buffer.writeInt(_player.getTransformationId());
		final Set<AbnormalVisualEffect> abnormalVisualEffects = _player.getEffectList().getAbnormalVisualEffectsFiltered();
		final boolean isInvisible = _player.isInvisible();
		buffer.writeInt(abnormalVisualEffects.size() + (isInvisible ? 1 : 0));
		for (AbnormalVisualEffect abnormalVisualEffect : abnormalVisualEffects)
		{
			final int id = abnormalVisualEffect.getClientId();
			buffer.writeShort(CommonSkill.VISUAL_DEBUFF_ID_TEMPFIX.getOrDefault(id, id));
		}
		if (isInvisible)
		{
			buffer.writeShort(AbnormalVisualEffect.STEALTH.getClientId());
		}
	}
}
