package club.projectessence.gameserver.network.serverpackets.privatestore;

import club.projectessence.commons.network.ServerPacketId;
import club.projectessence.gameserver.enums.PrivateStoreType;
import club.projectessence.gameserver.instancemanager.PrivateStoreHistoryManager.ItemHistoryTransaction;
import club.projectessence.gameserver.network.GameClient;
import club.projectessence.gameserver.network.serverpackets.AbstractItemPacket;
import io.github.joealisson.mmocore.WritableBuffer;

import java.util.ArrayList;

public class ExPrivateStoreSearchHistory extends AbstractItemPacket {
	private final int _page;
	private final int _maxPage;
	private final ArrayList<ItemHistoryTransaction> _history;

	public ExPrivateStoreSearchHistory(int page, int maxPage, ArrayList<ItemHistoryTransaction> history) {
		_page = page;
		_maxPage = maxPage;
		_history = new ArrayList<>(history);
	}

	/**
	 * 338 struct _S_EX_PRIVATE_STORE_SEARCH_HISTORY { var int cCurrentPage; var int cMaxPage; var array<_pkPSSearchHistory> histories; }; struct _pkPSSearchHistory { var int nClassID; var int cStoreType; var int cEnchant; var INT64 nPrice; var INT64 nAmount; }; // S: FE D502 01 - cPage 01 -
	 * cMaxPage E6000000 - nSize nClassID cStoreType cEnchant nPrice nAmount 4E000000 00 00 7F96980000000000 0100000000000000 4F000000 00 00 7F96980000000000 0100000000000000 5B000000 00 00 80C3C90100000000 0100000000000000 62000000 00 00 002D310100000000 0100000000000000 6E000000 00 00
	 * 80841E0000000000 0100000000000000 C6000000 00 00 FF117A0000000000 0100000000000000
	 */
	@Override
	public void writeImpl(GameClient client, WritableBuffer buffer) {
		writeId(ServerPacketId.EX_PRIVATE_STORE_SEARCH_HISTORY, buffer);

		buffer.writeByte(_page); // cPage
		buffer.writeByte(_maxPage); // cMaxPage

		buffer.writeInt(_history.size()); // nSize -> Items count for loop below

		for (int i = 0; i < _history.size(); i++) {
			// LOGGER.info("Writing page " + _page + " of " + _maxPage + " transaction: " + transaction);
			final ItemHistoryTransaction transaction = _history.get(i);
			buffer.writeInt(transaction.getItemId()); // itemId
			buffer.writeByte(transaction.getTransactionType() == PrivateStoreType.SELL ? 0x00 : 0x01); // cStoreType
			buffer.writeByte(transaction.getEnchantLevel()); // cEnchant
			buffer.writeLong(transaction.getPrice() / transaction.getCount()); // nPrice
			buffer.writeLong(transaction.getCount()); // nAmount
		}
	}
}