package club.projectessence.gameserver.network.serverpackets.privatestore;

import club.projectessence.commons.network.ServerPacketId;
import club.projectessence.gameserver.instancemanager.PrivateStoreHistoryManager;
import club.projectessence.gameserver.model.ItemInfo;
import club.projectessence.gameserver.model.items.instance.ItemInstance;
import club.projectessence.gameserver.network.GameClient;
import club.projectessence.gameserver.network.serverpackets.AbstractItemPacket;
import io.github.joealisson.mmocore.WritableBuffer;

import java.util.LinkedList;

public class RequestPrivateStoreSearchStatistics extends AbstractItemPacket {

	public RequestPrivateStoreSearchStatistics() {

	}

	@Override
	public void writeImpl(GameClient client, WritableBuffer buffer) {
		writeId(ServerPacketId.EX_PRIVATE_STORE_SEARCH_STATISTICS, buffer);

		LinkedList<PrivateStoreHistoryManager.ItemHistoryTransaction> mostItems = PrivateStoreHistoryManager.getInstance().getTopMostItem();
		LinkedList<PrivateStoreHistoryManager.ItemHistoryTransaction> highestItems = PrivateStoreHistoryManager.getInstance().getTopHighestItem();

		buffer.writeInt(Math.min(mostItems.size(), 5));
		for (int i = 0; i < Math.min(mostItems.size(), 5); i++) {
			buffer.writeInt((int) mostItems.get(i).getCount());
			ItemInfo itemInfo = new ItemInfo(new ItemInstance(mostItems.get(i).getItemId()));
			buffer.writeInt(calculatePacketSize(itemInfo, mostItems.get(i).getCount()));
			writeItem(itemInfo, mostItems.get(i).getCount(), buffer);
		}

		buffer.writeInt(Math.min(highestItems.size(), 5));
		for (int i = 0; i < Math.min(highestItems.size(), 5); i++) {
			buffer.writeLong(highestItems.get(i).getPrice());
			ItemInfo itemInfo = new ItemInfo(new ItemInstance(highestItems.get(i).getItemId()));
			buffer.writeInt(calculatePacketSize(itemInfo, highestItems.get(i).getCount()));
			writeItem(itemInfo, highestItems.get(i).getCount(), buffer);
		}
	}
}
