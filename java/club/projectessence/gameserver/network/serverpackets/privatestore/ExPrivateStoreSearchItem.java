package club.projectessence.gameserver.network.serverpackets.privatestore;

import club.projectessence.commons.network.ServerPacketId;
import club.projectessence.gameserver.enums.PrivateStoreType;
import club.projectessence.gameserver.network.GameClient;
import club.projectessence.gameserver.network.clientpackets.privatestore.ExRequestPrivateStoreSearchList;
import club.projectessence.gameserver.network.clientpackets.privatestore.ExRequestPrivateStoreSearchList.ShopItem;
import club.projectessence.gameserver.network.serverpackets.AbstractItemPacket;
import io.github.joealisson.mmocore.WritableBuffer;

import java.util.List;

public class ExPrivateStoreSearchItem extends AbstractItemPacket {
	private final int _page;
	private final int _maxPage;
	private final int _nSize;
	private final List<ShopItem> _items;

	public ExPrivateStoreSearchItem(int page, int maxPage, int nSize, List<ShopItem> items) {
		_page = page;
		_maxPage = maxPage;
		_nSize = nSize;
		_items = items;
	}

	@Override
	public void writeImpl(GameClient client, WritableBuffer buffer) {
		writeId(ServerPacketId.EX_PRIVATE_STORE_SEARCH_ITEM, buffer);

		buffer.writeByte(_page); // cPage
		buffer.writeByte(_maxPage); // cMaxPage
		buffer.writeInt(_nSize); // nSize

		if (_nSize > 0) {
			for (int itemIndex = (_page - 1) * ExRequestPrivateStoreSearchList.MAX_ITEM_PER_PAGE; (itemIndex < (_page * ExRequestPrivateStoreSearchList.MAX_ITEM_PER_PAGE)) && (itemIndex < _items.size()); itemIndex++) {
				// LOGGER.info("Writing page " + _page + " of " + _maxPage + " itemIndex: " + itemIndex + " (" + _items.get(itemIndex).getItemInfo().getItem().getName() + ")");
				final ShopItem shopItem = _items.get(itemIndex);

				buffer.writeSizedString(shopItem.getOwner().getName()); // Vendor name
				buffer.writeByte(shopItem.getStoreType() == PrivateStoreType.PACKAGE_SELL ? 0x02 : shopItem.getStoreType() == PrivateStoreType.SELL ? 0x00 : 0x01); // store type (maybe "sold"/buy/Package (translated as Total Score...))
				buffer.writeLong(shopItem.getPrice()); // Price
				buffer.writeInt(shopItem.getOwner().getX()); // X
				buffer.writeInt(shopItem.getOwner().getY()); // Y
				buffer.writeInt(shopItem.getOwner().getZ()); // Z
				buffer.writeInt(calculatePacketSize(shopItem.getItemInfo(), shopItem.getCount())); // size
				writeItem(shopItem.getItemInfo(), shopItem.getCount(), buffer); // itemAssemble
			}
		}
	}
}