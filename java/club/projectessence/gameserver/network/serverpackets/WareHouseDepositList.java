/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.network.serverpackets;

import club.projectessence.Config;
import club.projectessence.commons.network.ServerPacketId;
import club.projectessence.gameserver.enums.ItemLocation;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.model.items.instance.ItemInstance;
import club.projectessence.gameserver.network.GameClient;
import io.github.joealisson.mmocore.WritableBuffer;

import java.util.Collection;

public class WareHouseDepositList extends AbstractItemPacket {
	public static final int PRIVATE = 1;
	public static final int CLAN = 2;
	public static final int CASTLE = 3;
	public static final int FREIGHT = 1;
	private static final int DKP_SHOP = 100;
	private static final int DKP_AUCTION = 101;
	private final int _sendType;
	private final long _playerAdena;
	private final Collection<ItemInstance> _items;
	private final int _depositedItemsCount;
	/**
	 * <ul>
	 * <li>0x01-Private Warehouse</li>
	 * <li>0x02-Clan Warehouse</li>
	 * <li>0x03-Castle Warehouse</li>
	 * <li>0x04-Warehouse</li>
	 * </ul>
	 */
	private int _whType;
	private DkpWhType _dkpWhType = null;

	public WareHouseDepositList(int sendType, PlayerInstance player, int type) {
		_sendType = sendType;
		_whType = type;
		_playerAdena = player.getAdena();

		_items = player.getInventory().getItems(item -> item.isDepositable(_whType == PRIVATE) && !item.isEquipped() && !item.isLocked() && (item.getItemLocation() == ItemLocation.INVENTORY));

		switch (_whType) {
			case PRIVATE:
				_depositedItemsCount = player.getWarehouse().getSize();
				break;
			case CLAN:
			case CASTLE:
				_depositedItemsCount = player.getClan().getWarehouse().getSize();
				break;
			case DKP_SHOP:
				_depositedItemsCount = player.getClan().getDkpShopWarehouse().getSize();
				_whType = CLAN;
				break;
			case DKP_AUCTION:
				_depositedItemsCount = player.getClan().getDkpAuctionWarehouse().getSize();
				_whType = CLAN;
				break;
			default:
				_depositedItemsCount = 0;
				return;
		}
	}

	public static void sendDkpDeposit(PlayerInstance player, DkpWhType dkpWhType) {
		final WareHouseDepositList packet1 = new WareHouseDepositList(1, player, dkpWhType.getId());
		final WareHouseDepositList packet2 = new WareHouseDepositList(2, player, dkpWhType.getId());
		packet1._dkpWhType = dkpWhType;
		packet2._dkpWhType = dkpWhType;
		player.sendPacket(packet1);
		player.sendPacket(packet2);
	}

	@Override
	public void writeImpl(GameClient client, WritableBuffer buffer) {
		writeId(ServerPacketId.WAREHOUSE_DEPOSIT_LIST, buffer);
		buffer.writeByte(_sendType);
		if (_sendType == 2) {
			buffer.writeInt(_whType);
			buffer.writeInt(_items.size());
			for (ItemInstance item : _items) {
				writeItem(item, buffer);
				buffer.writeInt(item.getObjectId());
			}
		} else {
			buffer.writeShort(_whType);
			buffer.writeLong(_playerAdena);
			int depositedCount = _depositedItemsCount;
			if (_dkpWhType == DkpWhType.SHOP) {
				depositedCount = Math.min(0, Config.WAREHOUSE_SLOTS_CLAN - Config.DKP_WAREHOUSE_SLOTS_CLAN) + _depositedItemsCount;
			} else if (_dkpWhType == DkpWhType.AUCTION) {
				depositedCount = Math.max(0, Config.WAREHOUSE_SLOTS_CLAN - Config.DKP_AUCTION_WAREHOUSE_SLOTS_CLAN) + _depositedItemsCount;
			}
			buffer.writeInt(depositedCount);
			buffer.writeInt(_items.size());
		}
	}

	public enum DkpWhType {
		SHOP(DKP_SHOP),
		AUCTION(DKP_AUCTION);

		private final int _id;

		DkpWhType(int id) {
			_id = id;
		}

		public int getId() {
			return _id;
		}
	}
}
