/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.network.serverpackets;

import club.projectessence.commons.network.ServerPacketId;
import club.projectessence.gameserver.data.sql.ClanTable;
import club.projectessence.gameserver.enums.TaxType;
import club.projectessence.gameserver.instancemanager.CastleManager;
import club.projectessence.gameserver.model.siege.Castle;
import club.projectessence.gameserver.network.GameClient;
import io.github.joealisson.mmocore.WritableBuffer;

import java.util.Collection;

/**
 * <AUTHOR>
 */
public class ExShowCastleInfo extends ServerPacket {
	public static final ExShowCastleInfo STATIC_PACKET = new ExShowCastleInfo();

	private ExShowCastleInfo() {
	}

	@Override
	public void writeImpl(GameClient client, WritableBuffer buffer) {
		writeId(ServerPacketId.EX_SHOW_CASTLE_INFO, buffer);

		final Collection<Castle> castles = CastleManager.getInstance().getCastles();
		buffer.writeInt(castles.size());
		for (Castle castle : castles) {
			buffer.writeInt(castle.getResidenceId());
			if (castle.getOwnerId() > 0) {
				if (ClanTable.getInstance().getClan(castle.getOwnerId()) != null) {
					buffer.writeString(ClanTable.getInstance().getClan(castle.getOwnerId()).getName());
				} else {
					LOGGER.warning("Castle owner with no name! Castle: " + castle.getName() + " has an OwnerId = " + castle.getOwnerId() + " who does not have a  name!");
					buffer.writeString("");
				}
			} else {
				buffer.writeString("");
			}
			buffer.writeInt(castle.getTaxPercent(TaxType.BUY));
			buffer.writeInt((int) (castle.getSiege().getSiegeDate().getTimeInMillis() / 1000));

			buffer.writeByte(castle.getSiege().isInProgress() ? 0x01 : 0x00); // Grand Crusade
			buffer.writeByte(castle.getSide().ordinal()); // Grand Crusade
		}
	}
}
