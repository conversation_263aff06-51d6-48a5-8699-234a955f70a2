/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.network.serverpackets;

import club.projectessence.commons.network.ServerPacketId;
import club.projectessence.gameserver.enums.AcquireSkillType;
import club.projectessence.gameserver.model.SkillLearn;
import club.projectessence.gameserver.network.GameClient;
import io.github.joealisson.mmocore.WritableBuffer;

import java.util.List;

/**
 * <AUTHOR>
 */
public class ExAcquirableSkillListByClass extends ServerPacket {
	final List<SkillLearn> _learnable;
	final AcquireSkillType _type;

	public ExAcquirableSkillListByClass(List<SkillLearn> learnable, AcquireSkillType type) {
		_learnable = learnable;
		_type = type;
	}

	@Override
	public void writeImpl(GameClient client, WritableBuffer buffer) {
		writeId(ServerPacketId.EX_ACQUIRABLE_SKILL_LIST_BY_CLASS, buffer);

		buffer.writeShort(_type.getId());
		buffer.writeShort(_learnable.size());
		for (SkillLearn skill : _learnable) {
			buffer.writeInt(skill.getSkillId());
			buffer.writeShort(skill.getSkillLevel());
			buffer.writeShort(skill.getSkillLevel());
			buffer.writeByte(skill.getGetLevel());
			buffer.writeLong(skill.getLevelUpSp());
			buffer.writeByte(skill.getRequiredItems().size());
			if (_type == AcquireSkillType.SUBPLEDGE) {
				buffer.writeShort(0x00);
			}
		}
	}
}
