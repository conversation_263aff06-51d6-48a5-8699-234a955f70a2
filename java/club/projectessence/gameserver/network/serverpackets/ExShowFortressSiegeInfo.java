/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.network.serverpackets;

import club.projectessence.commons.network.ServerPacketId;
import club.projectessence.gameserver.model.siege.Fort;
import club.projectessence.gameserver.network.GameClient;
import io.github.joealisson.mmocore.WritableBuffer;

/**
 * TODO: Rewrite!!!
 *
 * <AUTHOR>
 */
public class ExShowFortressSiegeInfo extends ServerPacket {
	private final int _fortId;
	private final int _size;
	private final int _csize;
	private final int _csize2;

	/**
	 * @param fort
	 */
	public ExShowFortressSiegeInfo(Fort fort) {
		_fortId = fort.getResidenceId();
		_size = fort.getFortSize();
		// final List<FortSiegeSpawn> commanders = null; // TODO: see what todo here // FortSiegeManager.getInstance().getCommanderSpawnList(_fortId);
		_csize = 0;// ((commanders == null) ? 0 : commanders.size());
		// _csize2 = fort.getSiege().getCommanders().size();
		_csize2 = 0;
	}

	@Override
	public void writeImpl(GameClient client, WritableBuffer buffer) {
		writeId(ServerPacketId.EX_SHOW_FORTRESS_SIEGE_INFO, buffer);

		buffer.writeInt(_fortId); // Fortress Id
		buffer.writeInt(_size); // Total Barracks Count
		if (_csize > 0) {
			switch (_csize) {
				case 3: {
					switch (_csize2) {
						case 0: {
							buffer.writeInt(0x03);
							break;
						}
						case 1: {
							buffer.writeInt(0x02);
							break;
						}
						case 2: {
							buffer.writeInt(0x01);
							break;
						}
						case 3: {
							buffer.writeInt(0x00);
							break;
						}
					}
					break;
				}
				case 4: // TODO: change 4 to 5 once control room supported
				{
					switch (_csize2) {
						// TODO: once control room supported, update buffer.writeInt(0x0x) to support 5th room
						case 0: {
							buffer.writeInt(0x05);
							break;
						}
						case 1: {
							buffer.writeInt(0x04);
							break;
						}
						case 2: {
							buffer.writeInt(0x03);
							break;
						}
						case 3: {
							buffer.writeInt(0x02);
							break;
						}
						case 4: {
							buffer.writeInt(0x01);
							break;
						}
					}
					break;
				}
			}
		} else {
			for (int i = 0; i < _size; i++) {
				buffer.writeInt(0x00);
			}
		}
	}
}
