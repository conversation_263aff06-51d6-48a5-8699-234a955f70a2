/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.network.serverpackets.raidbossinfo;

import club.projectessence.commons.network.ServerPacketId;
import club.projectessence.gameserver.data.xml.NpcData;
import club.projectessence.gameserver.instancemanager.GrandBossManager;
import club.projectessence.gameserver.instancemanager.RaidSpawnManager;
import club.projectessence.gameserver.model.actor.templates.NpcTemplate;
import club.projectessence.gameserver.network.GameClient;
import club.projectessence.gameserver.network.serverpackets.ServerPacket;
import io.github.joealisson.mmocore.WritableBuffer;

import java.util.List;

/**
 * <AUTHOR>
 */
public class ExRaidBossSpawnInfo extends ServerPacket {
	private final List<Integer> _bossIds;

	public ExRaidBossSpawnInfo(List<Integer> bossIds) {
		_bossIds = bossIds;
	}

	@Override
	public void writeImpl(GameClient client, WritableBuffer buffer) {
		writeId(ServerPacketId.EX_RAID_BOSS_SPAWN_INFO, buffer);
		buffer.writeInt(0x00); // nBossRespawnFactor (388)
		buffer.writeInt(_bossIds.size());
		for (int id : _bossIds) {
			buffer.writeInt(id);
			int status = getBossStatus(id);
			buffer.writeInt(status);
			buffer.writeInt(getBossDeathTime(id));
		}
	}

	private int getBossStatus(int id) {
		int status = GrandBossManager.getInstance().getBossVisualStatus(id);
		if (status <= 0) {
			status = RaidSpawnManager.getInstance().getNpcVisualStatus(id);
			if (status < 0) {
				String message = "Could not find spawn info for boss " + id;
				final NpcTemplate template = NpcData.getInstance().getTemplate(id);
				if (template != null) {
					message += " - " + template.getName() + ".";
				} else {
					message += " - NPC template not found.";
				}
				LOGGER.warning(message);
				status = 0;
			}
		}
		return status;
	}

	private int getBossDeathTime(int id) {
		int time = -1;
		// Epics do not display respawn time (except baium and octavis)
		if ((id == 29020) || (id == 10002)) {
			time = (int) (GrandBossManager.getInstance().getDeathTime(id) / 1000);
		} else {
			time = (int) (RaidSpawnManager.getInstance().getDeathTime(id) / 1000);
		}
		return time;
	}
}
