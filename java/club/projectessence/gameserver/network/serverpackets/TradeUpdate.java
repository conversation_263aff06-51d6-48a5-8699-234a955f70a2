/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.network.serverpackets;

import club.projectessence.commons.network.ServerPacketId;
import club.projectessence.gameserver.model.TradeItem;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.network.GameClient;
import io.github.joealisson.mmocore.WritableBuffer;

/**
 * <AUTHOR>
 */
public class TradeUpdate extends AbstractItemPacket {
	private final int _sendType;
	private final TradeItem _item;
	private final long _newCount;
	private final long _count;

	public TradeUpdate(int sendType, PlayerInstance player, TradeItem item, long count) {
		_sendType = sendType;
		_count = count;
		_item = item;
		_newCount = player == null ? 0 : player.getInventory().getItemByObjectId(item.getObjectId()).getCount() - item.getCount();
	}

	@Override
	public void writeImpl(GameClient client, WritableBuffer buffer) {
		writeId(ServerPacketId.TRADE_UPDATE, buffer);
		buffer.writeByte(_sendType);
		buffer.writeInt(0x01);
		if (_sendType == 2) {
			buffer.writeInt(0x01);
			buffer.writeShort((_newCount > 0) && _item.getItem().isStackable() ? 3 : 2);
			writeItem(_item, _count, buffer);
		}
	}
}
