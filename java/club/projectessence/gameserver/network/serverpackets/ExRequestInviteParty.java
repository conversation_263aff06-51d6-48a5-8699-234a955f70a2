/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.network.serverpackets;

import club.projectessence.commons.network.ServerPacketId;
import club.projectessence.gameserver.network.GameClient;
import io.github.joealisson.mmocore.WritableBuffer;

/**
 * <AUTHOR>
 */
public class ExRequestInviteParty extends ServerPacket {
	private final String _name;
	private final byte _reqType;
	private final byte _sayType;
	private final byte _charRankGrade;
	private final byte _castleId;
	private final int _objId;

	public ExRequestInviteParty(String name, byte reqType, byte sayType, byte charRankGrade, byte castleId, int objId) {
		_name = name;
		_reqType = reqType;
		_sayType = sayType;
		_charRankGrade = charRankGrade;
		_castleId = castleId;
		_objId = objId;
	}

	@Override
	public void writeImpl(GameClient client, WritableBuffer buffer) {
		writeId(ServerPacketId.EX_REQUEST_INVITE_PARTY, buffer);

		buffer.writeSizedString(_name); // sName
		buffer.writeByte(_reqType); // cReqType
		buffer.writeByte(_sayType); // cSayType
		buffer.writeByte(_charRankGrade); // cCharRankGrade
		buffer.writeByte(_castleId); // cPledgeCastleDBID
		buffer.writeByte(0x00); // cEventEmblemID (388)
		buffer.writeInt(_objId); // nUserSID
	}
}
