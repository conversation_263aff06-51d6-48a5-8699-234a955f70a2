/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.network.serverpackets.ceremonyofchaos;

import club.projectessence.commons.network.ServerPacketId;
import club.projectessence.gameserver.enums.CeremonyOfChaosResult;
import club.projectessence.gameserver.instancemanager.CeremonyOfChaosManager;
import club.projectessence.gameserver.model.ceremonyofchaos.CeremonyOfChaosEvent;
import club.projectessence.gameserver.network.GameClient;
import club.projectessence.gameserver.network.serverpackets.ServerPacket;
import io.github.joealisson.mmocore.WritableBuffer;

/**
 * <AUTHOR>
 */
public class ExCuriousHouseResult extends ServerPacket {
	private final CeremonyOfChaosResult _result;
	private final CeremonyOfChaosEvent _event;

	public ExCuriousHouseResult(CeremonyOfChaosResult result, CeremonyOfChaosEvent event) {
		_result = result;
		_event = event;
	}

	@Override
	public void writeImpl(GameClient client, WritableBuffer buffer) {
		writeId(ServerPacketId.EX_CURIOUS_HOUSE_RESULT, buffer);
		buffer.writeInt(_event.getId());
		buffer.writeShort(_result.ordinal());
		buffer.writeInt(CeremonyOfChaosManager.getInstance().getMaxPlayersInArena());
		buffer.writeInt(_event.getMembers().size());
		_event.getMembers().values().forEach(m ->
		{
			buffer.writeInt(m.getObjectId());
			buffer.writeInt(m.getPosition());
			buffer.writeInt(m.getClassId());
			buffer.writeInt(m.getLifeTime());
			buffer.writeInt(m.getScore());
		});
	}
}
