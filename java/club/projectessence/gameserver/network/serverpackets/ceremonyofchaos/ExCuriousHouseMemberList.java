/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.network.serverpackets.ceremonyofchaos;

import club.projectessence.commons.network.ServerPacketId;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.model.ceremonyofchaos.CeremonyOfChaosMember;
import club.projectessence.gameserver.network.GameClient;
import club.projectessence.gameserver.network.serverpackets.ServerPacket;
import io.github.joealisson.mmocore.WritableBuffer;

import java.util.Collection;

/**
 * <AUTHOR>
 */
public class ExCuriousHouseMemberList extends ServerPacket {
	private final int _id;
	private final int _maxPlayers;
	private final Collection<CeremonyOfChaosMember> _players;

	public ExCuriousHouseMemberList(int id, int maxPlayers, Collection<CeremonyOfChaosMember> players) {
		_id = id;
		_maxPlayers = maxPlayers;
		_players = players;
	}

	@Override
	public void writeImpl(GameClient client, WritableBuffer buffer) {
		writeId(ServerPacketId.EX_CURIOUS_HOUSE_MEMBER_LIST, buffer);

		buffer.writeInt(_id);
		buffer.writeInt(_maxPlayers);
		buffer.writeInt(_players.size());
		for (CeremonyOfChaosMember cocPlayer : _players) {
			final PlayerInstance player = cocPlayer.getPlayer();
			buffer.writeInt(cocPlayer.getObjectId());
			buffer.writeInt(cocPlayer.getPosition());
			if (player != null) {
				buffer.writeInt(player.getMaxHp());
				buffer.writeInt(player.getMaxCp());
				buffer.writeInt((int) player.getCurrentHp());
				buffer.writeInt((int) player.getCurrentCp());
			} else {
				buffer.writeInt(0x00);
				buffer.writeInt(0x00);
				buffer.writeInt(0x00);
				buffer.writeInt(0x00);
			}
		}
	}
}
