/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.network.serverpackets;

import club.projectessence.commons.network.ServerPacketId;
import club.projectessence.gameserver.enums.HtmlActionScope;
import club.projectessence.gameserver.network.GameClient;
import io.github.joealisson.mmocore.WritableBuffer;

/**
 * NpcHtmlMessage server packet implementation.
 *
 * <AUTHOR>
 */
public class NpcHtmlMessage extends AbstractHtmlPacket {
	private final int _itemId;

	public NpcHtmlMessage() {
		_itemId = 0;
	}

	public NpcHtmlMessage(int npcObjId) {
		super(npcObjId);
		_itemId = 0;
	}

	public NpcHtmlMessage(String html) {
		super(html);
		_itemId = 0;
	}

	public NpcHtmlMessage(int npcObjId, String html) {
		super(npcObjId, html);
		_itemId = 0;
	}

	public NpcHtmlMessage(int npcObjId, int itemId) {
		super(npcObjId);

		if (itemId < 0) {
			throw new IllegalArgumentException();
		}

		_itemId = itemId;
	}

	public NpcHtmlMessage(int npcObjId, int itemId, String html) {
		super(npcObjId, html);

		if (itemId < 0) {
			throw new IllegalArgumentException();
		}

		_itemId = itemId;
	}

	@Override
	public void writeImpl(GameClient client, WritableBuffer buffer) {
		writeId(ServerPacketId.NPC_HTML_MESSAGE, buffer);

		buffer.writeInt(getNpcObjId());
		buffer.writeString(getHtml());
		buffer.writeInt(_itemId);
		buffer.writeInt(0x00); // TODO: Find me!
	}

	@Override
	public HtmlActionScope getScope() {
		return _itemId == 0 ? HtmlActionScope.NPC_HTML : HtmlActionScope.NPC_ITEM_HTML;
	}
}
