/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.network.serverpackets.sharedposition;

import club.projectessence.Config;
import club.projectessence.commons.network.ServerPacketId;
import club.projectessence.gameserver.instancemanager.PositionShareManager;
import club.projectessence.gameserver.instancemanager.PositionShareManager.SharedPosition;
import club.projectessence.gameserver.model.Location;
import club.projectessence.gameserver.network.GameClient;
import club.projectessence.gameserver.network.serverpackets.ServerPacket;
import io.github.joealisson.mmocore.WritableBuffer;

/**
 * <AUTHOR>
 */
public class ExSharedPositionTeleportUI extends ServerPacket {
	private final int _id;

	public ExSharedPositionTeleportUI(int id) {
		_id = id;
	}

	@Override
	public void writeImpl(GameClient client, WritableBuffer buffer) {
		writeId(ServerPacketId.EX_SHARED_POSITION_TELEPORT_UI, buffer);
		SharedPosition sharedPosition = PositionShareManager.getInstance().getSharedPosition(_id);
		if (sharedPosition == null) {
			buffer.writeInt(0x00);
			return;
		}
		buffer.writeSizedString(sharedPosition.getPlayerName());
		buffer.writeInt(sharedPosition.getId());
		buffer.writeInt(sharedPosition.getTeleportsLeft());
		buffer.writeShort(0x0E); // Size (3 * 4 + 2)
		final Location loc = sharedPosition.getLocation();
		buffer.writeInt(loc.getX());
		buffer.writeInt(loc.getY());
		buffer.writeInt(loc.getZ());
		buffer.writeLong(Config.SHARE_POSITION_TELEPORT_PRICE); // nTeleportCostLCoin (388)
	}
}