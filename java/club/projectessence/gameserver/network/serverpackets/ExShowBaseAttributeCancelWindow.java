/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.network.serverpackets;

import club.projectessence.commons.network.ServerPacketId;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.model.items.instance.ItemInstance;
import club.projectessence.gameserver.network.GameClient;
import io.github.joealisson.mmocore.WritableBuffer;

import java.util.Collection;

public class ExShowBaseAttributeCancelWindow extends ServerPacket {
	private final Collection<ItemInstance> _items;
	private long _price;

	public ExShowBaseAttributeCancelWindow(PlayerInstance player) {
		_items = player.getInventory().getItems(ItemInstance::hasAttributes);
	}

	@Override
	public void writeImpl(GameClient client, WritableBuffer buffer) {
		writeId(ServerPacketId.EX_SHOW_BASE_ATTRIBUTE_CANCEL_WINDOW, buffer);

		buffer.writeInt(_items.size());
		for (ItemInstance item : _items) {
			buffer.writeInt(item.getObjectId());
			buffer.writeLong(getPrice(item));
		}
	}

	/**
	 * TODO: Unhardcode! Update prices for Top/Mid/Low S80/S84
	 *
	 * @param item
	 * @return
	 */
	private long getPrice(ItemInstance item) {
		switch (item.getItem().getCrystalType()) {
			case S: {
				if (item.isWeapon()) {
					_price = 50000;
				} else {
					_price = 40000;
				}
				break;
			}
			case S80: {
				if (item.isWeapon()) {
					_price = 100000;
				} else {
					_price = 80000;
				}
				break;
			}
			case S84: {
				if (item.isWeapon()) {
					_price = 200000;
				} else {
					_price = 160000;
				}
				break;
			}
		}
		return _price;
	}
}
