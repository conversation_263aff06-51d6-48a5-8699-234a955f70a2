/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.network.serverpackets.castlesiege;

import club.projectessence.commons.network.ServerPacketId;
import club.projectessence.gameserver.data.sql.ClanTable;
import club.projectessence.gameserver.enums.SiegeClanType;
import club.projectessence.gameserver.instancemanager.CastleManager;
import club.projectessence.gameserver.model.SiegeClan;
import club.projectessence.gameserver.model.clan.Clan;
import club.projectessence.gameserver.model.siege.Castle;
import club.projectessence.gameserver.network.GameClient;
import club.projectessence.gameserver.network.serverpackets.ServerPacket;
import io.github.joealisson.mmocore.WritableBuffer;

/**
 * <AUTHOR>
 */
public class ExMercenaryCastleWarCastleSiegeDefenderList extends ServerPacket {
	private final int _castleId;

	public ExMercenaryCastleWarCastleSiegeDefenderList(int castleId) {
		_castleId = castleId;
	}

	@Override
	public void writeImpl(GameClient client, WritableBuffer buffer) {
		writeId(ServerPacketId.EX_MERCENARY_CASTLEWAR_CASTLE_SIEGE_DEFENDER_LIST, buffer);
		Castle castle = CastleManager.getInstance().getCastleById(_castleId);

		// 03000000 Castle Id
		// 00000000 unk
		// 01000000 unk
		// 00000000 unk
		// 01000000 size
		// 01000000 size
		// 57001060 Clan Id
		// 440069006E0061007300740069006100440065006C004C0065006F006E000000 Clan Name
		// 590075007200610073000000 Clan Leader Name
		// 0000000000000000010000000000000000000000000000000000000039000000440069006E00610073007400690061007300000059007500720061007300000000000000

		buffer.writeInt(_castleId);
		buffer.writeInt(0x00); // unk
		buffer.writeInt(0x01); // unk
		buffer.writeInt(0x00); // unk
		if (castle == null) {
			buffer.writeInt(0x00);
			buffer.writeInt(0x00);
		} else {
			final int size = castle.getSiege().getDefenderWaitingClans().size() + castle.getSiege().getDefenderClans().size() + (castle.getOwner() != null ? 1 : 0);
			buffer.writeInt(size);
			buffer.writeInt(size);

			// Add owners
			final Clan ownerClan = castle.getOwner();
			if (ownerClan != null) {
				buffer.writeInt(ownerClan.getId());
				buffer.writeString(ownerClan.getName());
				buffer.writeString(ownerClan.getLeaderName());
				buffer.writeInt(ownerClan.getAllyCrestId());
				buffer.writeInt(0x00); // signed time (seconds) (not stored by L2J)
				buffer.writeInt(SiegeClanType.OWNER.ordinal());

				buffer.writeInt(0x00); // 286
				buffer.writeInt(0x00); // 286
				buffer.writeInt(0x00); // 286
				buffer.writeInt(0x00); // 286

				buffer.writeInt(ownerClan.getAllyId());
				buffer.writeString(ownerClan.getAllyName());
				buffer.writeString(""); // Ally Leader Name
				buffer.writeInt(ownerClan.getAllyCrestId());
			}

			// List of confirmed defenders
			for (SiegeClan siegeClan : castle.getSiege().getDefenderClans()) {
				final Clan defendingClan = ClanTable.getInstance().getClan(siegeClan.getClanId());
				if ((defendingClan == null) || (defendingClan == castle.getOwner())) {
					continue;
				}

				buffer.writeInt(defendingClan.getId());
				buffer.writeString(defendingClan.getName());
				buffer.writeString(defendingClan.getLeaderName());
				buffer.writeInt(defendingClan.getCrestId());
				buffer.writeInt(0x00); // signed time (seconds) (not stored by L2J)
				buffer.writeInt(SiegeClanType.DEFENDER.ordinal());

				buffer.writeInt(0x00); // 286
				buffer.writeInt(0x00); // 286
				buffer.writeInt(0x00); // 286
				buffer.writeInt(0x00); // 286

				buffer.writeInt(defendingClan.getAllyId());
				buffer.writeString(defendingClan.getAllyName());
				buffer.writeString(""); // AllyLeaderName
				buffer.writeInt(defendingClan.getAllyCrestId());
			}

			// List of not confirmed defenders
			for (SiegeClan siegeClan : castle.getSiege().getDefenderWaitingClans()) {
				final Clan defendingClan = ClanTable.getInstance().getClan(siegeClan.getClanId());
				if (defendingClan == null) {
					continue;
				}

				buffer.writeInt(defendingClan.getId());
				buffer.writeString(defendingClan.getName());
				buffer.writeString(defendingClan.getLeaderName());
				buffer.writeInt(defendingClan.getCrestId());
				buffer.writeInt(0x00); // signed time (seconds) (not stored by L2J)
				buffer.writeInt(SiegeClanType.DEFENDER_PENDING.ordinal());

				buffer.writeInt(0x00); // 286
				buffer.writeInt(0x00); // 286
				buffer.writeInt(0x00); // 286
				buffer.writeInt(0x00); // 286

				buffer.writeInt(defendingClan.getAllyId());
				buffer.writeString(defendingClan.getAllyName());
				buffer.writeString(""); // AllyLeaderName
				buffer.writeInt(defendingClan.getAllyCrestId());
			}
		}
	}
}
