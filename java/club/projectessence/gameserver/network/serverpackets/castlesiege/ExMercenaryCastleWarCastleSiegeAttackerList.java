/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.network.serverpackets.castlesiege;

import club.projectessence.commons.network.ServerPacketId;
import club.projectessence.gameserver.data.sql.ClanTable;
import club.projectessence.gameserver.instancemanager.CastleManager;
import club.projectessence.gameserver.model.SiegeClan;
import club.projectessence.gameserver.model.clan.Clan;
import club.projectessence.gameserver.model.siege.Castle;
import club.projectessence.gameserver.network.GameClient;
import club.projectessence.gameserver.network.serverpackets.ServerPacket;
import io.github.joealisson.mmocore.WritableBuffer;

import java.util.Collection;

/**
 * <AUTHOR>
 */
public class ExMercenaryCastleWarCastleSiegeAttackerList extends ServerPacket {
	private final int _castleId;

	public ExMercenaryCastleWarCastleSiegeAttackerList(int castleId) {
		_castleId = castleId;
	}

	@Override
	public void writeImpl(GameClient client, WritableBuffer buffer) {
		writeId(ServerPacketId.EX_MERCENARY_CASTLEWAR_CASTLE_SIEGE_ATTACKER_LIST, buffer);
		Castle castle = CastleManager.getInstance().getCastleById(_castleId);

		buffer.writeInt(_castleId);
		buffer.writeInt(0x00); // unk
		buffer.writeInt(0x01); // unk
		buffer.writeInt(0x00); // unk
		if (castle == null) {
			buffer.writeInt(0x00);
			buffer.writeInt(0x00);
		} else {
			Collection<SiegeClan> attackers = castle.getSiege().getAttackerClans();
			buffer.writeInt(attackers.size());
			buffer.writeInt(attackers.size());
			for (SiegeClan siegeClan : attackers) {
				Clan clan = ClanTable.getInstance().getClan(siegeClan.getClanId());
				if (clan == null) {
					continue;
				}
				buffer.writeInt(clan.getId());
				buffer.writeString(clan.getName());
				buffer.writeString(clan.getLeaderName());
				buffer.writeInt(clan.getCrestId());
				buffer.writeInt(0x00); // signed time (seconds) (not stored by L2J)

				buffer.writeInt(0x00); // 286
				buffer.writeInt(0x00); // 286
				buffer.writeInt(0x00); // 286
				buffer.writeInt(0x00); // 286

				buffer.writeInt(clan.getAllyId());
				buffer.writeString(clan.getAllyName());
				buffer.writeString(""); // Ally Leader name
				buffer.writeInt(clan.getAllyCrestId());
			}
		}
	}
}
