/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.network.serverpackets.castlesiege;

import club.projectessence.commons.network.ServerPacketId;
import club.projectessence.gameserver.instancemanager.CastleManager;
import club.projectessence.gameserver.model.siege.Castle;
import club.projectessence.gameserver.network.GameClient;
import club.projectessence.gameserver.network.serverpackets.ServerPacket;
import io.github.joealisson.mmocore.WritableBuffer;

/**
 * <AUTHOR>
 */
public class ExMercenaryCastleWarCastleSiegeHUDInfo extends ServerPacket {
	private final int _castleId;

	public ExMercenaryCastleWarCastleSiegeHUDInfo(int castleId) {
		_castleId = castleId;
	}

	@Override
	public void writeImpl(GameClient client, WritableBuffer buffer) {
		writeId(ServerPacketId.EX_MERCENARY_CASTLEWAR_CASTLE_SIEGE_HUD_INFO, buffer);
		Castle castle = CastleManager.getInstance().getCastleById(_castleId);
		if (castle == null) {
			return;
		}
		buffer.writeInt(_castleId);
		if (castle.getSiege().isInProgress()) {
			buffer.writeInt(0x01);
			buffer.writeInt(0x00);
			buffer.writeInt((int) ((CastleManager.getInstance().getCastleById(_castleId).getSiege().getSiegeEndMilis() - System.currentTimeMillis()) / 1000));
			// buffer.writeInt((int) ((((CastleManager.getInstance().getCastleById(_castleId).getSiegeDate().getTimeInMillis() + (SiegeManager.getInstance().getSiegeLength() * 60 * 1000)) - System.currentTimeMillis()) / 1000)));
		} else {
			buffer.writeInt(0x00);
			buffer.writeInt(0x00);
			buffer.writeInt((int) ((CastleManager.getInstance().getCastleById(_castleId).getSiegeDate().getTimeInMillis() - System.currentTimeMillis()) / 1000)); // Countdown seconds
		}
	}
}
