/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.network.serverpackets.castlesiege;

import club.projectessence.commons.network.ServerPacketId;
import club.projectessence.gameserver.enums.TaxType;
import club.projectessence.gameserver.instancemanager.CastleManager;
import club.projectessence.gameserver.model.siege.Castle;
import club.projectessence.gameserver.network.GameClient;
import club.projectessence.gameserver.network.serverpackets.ServerPacket;
import io.github.joealisson.mmocore.WritableBuffer;

/**
 * <AUTHOR>
 * rev 338
 * S_EX_MERCENARY_CASTLEWAR_CASTLE_INFO
 * dddSSdQQd
 * d = nCastleID
 * d = nCastleOwnerPledgeSID
 * d = nCastleOwnerPledgeCrestDBID
 * S = wstrCastleOwnerPledgeName
 * S = wstrCastleOwnerPledgeMasterName
 * d = nCastleTaxRate
 * Q = nCurrentIncome
 * Q = nTotalIncome
 * d = nNextSiegeTime
 */
public class ExMercenaryCastleWarCastleInfo extends ServerPacket {
	private final int _castleId;

	public ExMercenaryCastleWarCastleInfo(int castleId) {
		_castleId = castleId;
	}

	@Override
	public void writeImpl(GameClient client, WritableBuffer buffer) {
		writeId(ServerPacketId.EX_MERCENARY_CASTLEWAR_CASTLE_INFO, buffer);

		Castle castle = CastleManager.getInstance().getCastleById(_castleId);
		if (castle == null) {
			buffer.writeInt(_castleId); // nCastleID
			buffer.writeInt(0x00); // nCastleOwnerPledgeSID
			buffer.writeInt(0x00); // nCastleOwnerPledgeCrestDBID
			buffer.writeSizedString(""); // wstrCastleOwnerPledgeName
			buffer.writeSizedString(""); // wstrCastleOwnerPledgeMasterName
			buffer.writeInt(0x00); // nCastleTaxRate
			buffer.writeLong(0x00); // nCurrentIncome
			buffer.writeLong(0x00); // nTotalIncome
			buffer.writeInt(0x00); // nNextSiegeTime
			return;
		}

		buffer.writeInt(_castleId); // nCastleID

		buffer.writeInt(castle.getOwner() != null ? castle.getOwner().getCrestId() : 0); // nCastleOwnerPledgeSID
		buffer.writeInt(castle.getOwner() != null ? castle.getOwner().getCrestLargeId() : 0); // nCastleOwnerPledgeCrestDBID

		buffer.writeSizedString(castle.getOwner() != null ? castle.getOwner().getName() : "-"); // wstrCastleOwnerPledgeName
		buffer.writeSizedString(castle.getOwner() != null ? castle.getOwner().getLeaderName() : "-"); // wstrCastleOwnerPledgeMasterName

		buffer.writeInt(castle.getTaxPercent(TaxType.BUY));// nCastleTaxRate
		buffer.writeLong(castle.getTreasury()); // nCurrentIncome
		buffer.writeLong(castle.getTreasury()); // nTotalIncome
		buffer.writeInt(castle.getSiegeDate() != null ? (int) (castle.getSiegeDate().getTimeInMillis() / 1000) : 0);// nNextSiegeTime
	}
}
