/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.network.serverpackets.castlesiege;

import club.projectessence.commons.network.ServerPacketId;
import club.projectessence.gameserver.instancemanager.CastleManager;
import club.projectessence.gameserver.model.siege.Castle;
import club.projectessence.gameserver.network.GameClient;
import club.projectessence.gameserver.network.serverpackets.ServerPacket;
import io.github.joealisson.mmocore.WritableBuffer;

/**
 * <AUTHOR>
 */
public class ExMercenaryCastleWarCastleSiegeInfo extends ServerPacket {
	private final int _castleId;

	public ExMercenaryCastleWarCastleSiegeInfo(int castleId) {
		_castleId = castleId;
	}

	@Override
	public void writeImpl(GameClient client, WritableBuffer buffer) {
		writeId(ServerPacketId.EX_MERCENARY_CASTLEWAR_CASTLE_SIEGE_INFO, buffer);
		Castle castle = CastleManager.getInstance().getCastleById(_castleId);

		buffer.writeInt(_castleId);
		if (castle == null) {
			buffer.writeInt(0x00);
			buffer.writeInt(0x00);
			buffer.writeSizedString("-");
			buffer.writeSizedString("-");
			buffer.writeInt(0x00);
			buffer.writeInt(0x00);
			buffer.writeInt(0x00);
		} else {
			buffer.writeInt(0x00); // seconds ?
			buffer.writeInt(0x00); // Is that crest?

			buffer.writeSizedString(castle.getOwner() != null ? castle.getOwner().getName() : "-");
			buffer.writeSizedString(castle.getOwner() != null ? castle.getOwner().getLeaderName() : "-");

			buffer.writeInt(0x00); // Is that crest?
			buffer.writeInt(castle.getSiege().getAttackerClans().size());
			buffer.writeInt(castle.getSiege().getDefenderClans().size() + castle.getSiege().getDefenderWaitingClans().size());
		}
	}
}
