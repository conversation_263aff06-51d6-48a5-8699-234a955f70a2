/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.network.serverpackets;

import club.projectessence.commons.network.ServerPacketId;
import club.projectessence.gameserver.model.actor.Summon;
import club.projectessence.gameserver.network.GameClient;
import io.github.joealisson.mmocore.WritableBuffer;

/**
 * <AUTHOR>
 */
public class ExPartyPetWindowUpdate extends ServerPacket {
	private final Summon _summon;

	public ExPartyPetWindowUpdate(Summon summon) {
		_summon = summon;
	}

	@Override
	public void writeImpl(GameClient client, WritableBuffer buffer) {
		writeId(ServerPacketId.EX_PARTY_PET_WINDOW_UPDATE, buffer);

		buffer.writeInt(_summon.getObjectId());
		buffer.writeInt(_summon.getTemplate().getDisplayId() + 1000000);
		buffer.writeByte(_summon.getSummonType());
		buffer.writeInt(_summon.getOwner().getObjectId());
		buffer.writeInt((int) _summon.getCurrentHp());
		buffer.writeInt(_summon.getMaxHp());
		buffer.writeInt((int) _summon.getCurrentMp());
		buffer.writeInt(_summon.getMaxMp());
	}
}
