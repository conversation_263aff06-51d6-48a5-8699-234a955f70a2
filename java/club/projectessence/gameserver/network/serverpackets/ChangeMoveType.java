/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.network.serverpackets;

import club.projectessence.commons.network.ServerPacketId;
import club.projectessence.gameserver.model.actor.Creature;
import club.projectessence.gameserver.network.GameClient;
import io.github.joealisson.mmocore.WritableBuffer;

public class ChangeMoveType extends ServerPacket {
	public static final int WALK = 0;
	public static final int RUN = 1;

	private final int _objectId;
	private final boolean _running;

	public ChangeMoveType(Creature creature) {
		_objectId = creature.getObjectId();
		_running = creature.isRunning();
	}

	@Override
	public void writeImpl(GameClient client, WritableBuffer buffer) {
		writeId(ServerPacketId.CHANGE_MOVE_TYPE, buffer);

		buffer.writeInt(_objectId);
		buffer.writeInt(_running ? RUN : WALK);
		buffer.writeInt(0); // c2
	}
}
