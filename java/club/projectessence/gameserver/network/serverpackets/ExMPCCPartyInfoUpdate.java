/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.network.serverpackets;

import club.projectessence.commons.network.ServerPacketId;
import club.projectessence.gameserver.model.CommandChannel;
import club.projectessence.gameserver.model.Party;
import club.projectessence.gameserver.network.GameClient;
import io.github.joealisson.mmocore.WritableBuffer;

import java.util.Map;

/**
 * <AUTHOR>
 */
public class ExMPCCPartyInfoUpdate extends ServerPacket {
	private final int _mode;
	private final int _LeaderOID;
	private final int _memberCount;
	private final String _name;

	private int _partyKills;
	private int _partyDeaths;

	/**
	 * @param party
	 * @param mode  0 = Remove, 1 = Add
	 */
	public ExMPCCPartyInfoUpdate(Party party, int mode) {
		_name = party.getLeader().getName();
		_LeaderOID = party.getLeaderObjectId();
		_memberCount = party.getMemberCount();
		_mode = mode;
		final CommandChannel cc = party.getCommandChannel();
		if (cc != null) {
			final Map<Integer, CommandChannel.CommandChannelPlayerKdInfo> kdInfo = cc.getPartyKDInfo(party);
			if (kdInfo != null) {
				kdInfo.values().forEach(kd -> {
					_partyKills += kd.getKills();
					_partyDeaths += kd.getDeaths();
				});
			}
		}
	}

	@Override
	public void writeImpl(GameClient client, WritableBuffer buffer) {
		writeId(ServerPacketId.EX_MPCCPARTY_INFO_UPDATE, buffer);

		buffer.writeString(_name + ";" + _partyKills + ";" + _partyDeaths);
		buffer.writeInt(_LeaderOID);
		buffer.writeInt(_memberCount);
		buffer.writeInt(_mode); // mode 0 = Remove Party, 1 = AddParty, maybe more...
	}
}