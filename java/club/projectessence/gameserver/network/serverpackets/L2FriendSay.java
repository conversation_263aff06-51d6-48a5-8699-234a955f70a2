/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.network.serverpackets;

import club.projectessence.commons.network.ServerPacketId;
import club.projectessence.gameserver.network.GameClient;
import io.github.joealisson.mmocore.WritableBuffer;

/**
 * Send Private (Friend) Message
 *
 * <AUTHOR>
 */
public class L2FriendSay extends ServerPacket {
	private final String _sender;
	private final String _receiver;
	private final String _message;

	public L2FriendSay(String sender, String reciever, String message) {
		_sender = sender;
		_receiver = reciever;
		_message = message;
	}

	@Override
	public void writeImpl(GameClient client, WritableBuffer buffer) {
		writeId(ServerPacketId.L2_FRIEND_SAY, buffer);

		buffer.writeInt(0); // ??
		buffer.writeString(_receiver);
		buffer.writeString(_sender);
		buffer.writeString(_message);
	}
}
