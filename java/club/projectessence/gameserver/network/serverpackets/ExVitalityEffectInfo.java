/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.network.serverpackets;

import club.projectessence.Config;
import club.projectessence.commons.network.ServerPacketId;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.network.GameClient;
import io.github.joealisson.mmocore.WritableBuffer;

/**
 * <AUTHOR>
 */
public class ExVitalityEffectInfo extends ServerPacket {
	private final int _sayhaGraceBonus;
	private final int _sayhaGraceItemsRemaining;
	private final int _points;

	public ExVitalityEffectInfo(PlayerInstance player) {
		_points = player.getSayhaGracePoints();
		_sayhaGraceBonus = (int) (player.getStat().getSayhaGraceExpBonus() * 100);
		_sayhaGraceItemsRemaining = Config.SAYHA_GRACE_MAX_ITEMS_ALLOWED - player.getSayhaGraceItemsUsed();
	}

	@Override
	public void writeImpl(GameClient client, WritableBuffer buffer) {
		writeId(ServerPacketId.EX_VITALITY_EFFECT_INFO, buffer);

		buffer.writeInt(_points);
		buffer.writeInt(_sayhaGraceBonus); // Sayha's Grace Bonus
		buffer.writeShort(0x00); // Sayha's Grace additional bonus in %
		buffer.writeShort(_sayhaGraceItemsRemaining); // How much Sayha's Grace items remaining for use
		buffer.writeShort(Config.SAYHA_GRACE_MAX_ITEMS_ALLOWED); // Max number of items for use
	}
}