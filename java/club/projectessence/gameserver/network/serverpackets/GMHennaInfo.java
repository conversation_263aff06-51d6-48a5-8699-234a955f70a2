/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.network.serverpackets;

import club.projectessence.commons.network.ServerPacketId;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.model.henna.Henna;
import club.projectessence.gameserver.model.henna.HennaPoten;
import club.projectessence.gameserver.model.stats.BaseStat;
import club.projectessence.gameserver.network.GameClient;
import io.github.joealisson.mmocore.WritableBuffer;

import java.util.ArrayList;
import java.util.List;

/**
 * This server packet sends the player's henna information using the Game Master's UI.
 *
 * <AUTHOR> Zoey76
 */
public class GMHennaInfo extends ServerPacket {
	private final PlayerInstance _player;
	private final List<Henna> _hennas = new ArrayList<>();

	public GMHennaInfo(PlayerInstance player) {
		_player = player;
		for (HennaPoten hennaPoten : _player.getHennaPotenList()) {
			final Henna henna = hennaPoten.getHenna();
			if (henna != null) {
				_hennas.add(henna);
			}
		}
	}

	@Override
	public void writeImpl(GameClient client, WritableBuffer buffer) {
		writeId(ServerPacketId.GMHENNA_INFO, buffer);

		buffer.writeShort(_player.getHennaValue(BaseStat.INT)); // equip INT
		buffer.writeShort(_player.getHennaValue(BaseStat.STR)); // equip STR
		buffer.writeShort(_player.getHennaValue(BaseStat.CON)); // equip CON
		buffer.writeShort(_player.getHennaValue(BaseStat.MEN)); // equip MEN
		buffer.writeShort(_player.getHennaValue(BaseStat.DEX)); // equip DEX
		buffer.writeShort(_player.getHennaValue(BaseStat.WIT)); // equip WIT
		buffer.writeShort(0x00); // equip LUC
		buffer.writeShort(0x00); // equip CHA
		buffer.writeInt(3); // Slots
		buffer.writeInt(_hennas.size()); // Size
		for (Henna henna : _hennas) {
			buffer.writeInt(henna.getDyeId());
			buffer.writeInt(0x01);
		}
		buffer.writeInt(0x00);
		buffer.writeInt(0x00);
		buffer.writeInt(0x00);
	}
}
