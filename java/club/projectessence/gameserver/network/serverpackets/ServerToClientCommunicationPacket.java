package club.projectessence.gameserver.network.serverpackets;

import club.projectessence.commons.network.ServerPacketId;
import club.projectessence.gameserver.network.GameClient;
import io.github.joealisson.mmocore.WritableBuffer;

public class ServerToClientCommunicationPacket extends ServerPacket {
	private final ServerRequest serverRequest;
	private String drawText;
	private String url;
	private WarnWindowType warnWindowType;

	public ServerToClientCommunicationPacket(final String drawText) {
		this.serverRequest = ServerRequest.SC_SERVER_REQUEST_SET_DRAW_TEXT;
		this.drawText = drawText;
	}

	public ServerToClientCommunicationPacket(final String strValue, ServerRequest serverRequest) {
		this.serverRequest = serverRequest;
		if (serverRequest == ServerRequest.SC_SERVER_REQUEST_OPEN_URL) {
			this.url = strValue;
		} else if (serverRequest == ServerRequest.SC_SERVER_REQUEST_SET_DRAW_TEXT) {
			this.drawText = strValue;
		}
	}

	public ServerToClientCommunicationPacket(final WarnWindowType warnWindowType, final String warnMessage) {
		this.serverRequest = ServerRequest.SC_SERVER_REQUEST_SHOW_CUSTOM_WARN_MESSAGE;
		this.warnWindowType = warnWindowType;
		this.drawText = warnMessage;
	}

	@Override
	public void writeImpl(GameClient client, WritableBuffer buffer) {
		writeId(ServerPacketId.DUMMY, buffer);

		buffer.writeByte(serverRequest.ordinal()); // Needed server request
		switch (serverRequest) {
			case SC_SERVER_REQUEST_SET_DRAW_TEXT:
				buffer.writeString(drawText);
				break;
			case SC_SERVER_REQUEST_SHOW_CUSTOM_WARN_MESSAGE:
				buffer.writeByte(warnWindowType.ordinal());
				buffer.writeString(drawText);
				break;
			case SC_SERVER_REQUEST_OPEN_URL:
				buffer.writeString(url);
				break;
		}
	}

	public static enum ServerRequest {
		SC_SERVER_REQUEST_SET_DRAW_TEXT,
		SC_SERVER_REQUEST_SHOW_CUSTOM_WARN_MESSAGE,
		SC_SERVER_REQUEST_OPEN_URL,
	}

	public static enum WarnWindowType {
		UL2CW_DEFAULT,
		UL2CW_CLOSE_WINDOW,
	}
}