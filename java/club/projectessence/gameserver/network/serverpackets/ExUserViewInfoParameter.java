/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.network.serverpackets;

import club.projectessence.commons.network.ServerPacketId;
import club.projectessence.gameserver.enums.StatModifierType;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.model.stats.Stat;
import club.projectessence.gameserver.model.stats.TraitType;
import club.projectessence.gameserver.model.stats.finalizers.ShotsBonusFinalizer;
import club.projectessence.gameserver.network.GameClient;
import io.github.joealisson.mmocore.WritableBuffer;

/**
 * <AUTHOR>
 */
public class ExUserViewInfoParameter extends ServerPacket {
	private final int _data[];

	public ExUserViewInfoParameter(PlayerInstance player) {
		_data = new int[StatInfo.values().length];
		// Add deadlock monitoring - track start time
		final long startTime = System.currentTimeMillis();

		for (int i = 0; i < _data.length; i++) {
			final StatInfo info = StatInfo.values()[i];
			try {
				_data[i] = info._stat == null ? handleNullStat(player, info) //
						: info._stat.getFinalizer() instanceof ShotsBonusFinalizer ? (int) ((player.getStat().getValue(Stat.SHOTS_BONUS) - 1) * 100 * 100) //
						: info._mode == StatModifierType.PER ? (int) ((player.getStat().getMul(info._stat) - 1) * 100 * 100) //
						: (int) player.getStat().getAdd(info._stat);// : (int) player.getStat().getValue(info._stat);
			} catch (Exception e) {
				// If stat calculation fails, use default value to prevent deadlock
				_data[i] = 0;
				if (System.currentTimeMillis() - startTime > 5000) { // 5 second timeout
					player.sendMessage("Warning: Stat calculation timeout detected for " + (info._stat != null ? info._stat.name() : "null stat"));
					break; // Exit loop to prevent further delays
				}
			}
		}
	}

	@Override
	public void writeImpl(GameClient client, WritableBuffer buffer) {
		writeId(ServerPacketId.EX_USER_VIEW_INFO_PARAMETER, buffer);

		buffer.writeInt(_data.length);
		for (int i = 0; i < _data.length; i++) {
			buffer.writeShort(i);
			buffer.writeInt(_data[i]);
		}
	}

	private int handleNullStat(PlayerInstance player, StatInfo info) {
		switch (info) {
			// Attack
			case DAMAGE_BONUS_SWORD_1:
			case DAMAGE_BONUS_SWORD_2:
			case DAMAGE_BONUS_SWORD_3:
				return (int) ((player.getStat().getAttackTrait(TraitType.SWORD) - 1) * 100 * 100);
			case DAMAGE_BONUS_ANCIENT_SWORD:
				return (int) ((player.getStat().getAttackTrait(TraitType.ANCIENTSWORD) - 1) * 100 * 100);
			case DAMAGE_BONUS_DAGGER:
				return (int) ((player.getStat().getAttackTrait(TraitType.DAGGER) - 1) * 100 * 100);
			case DAMAGE_BONUS_RAPIER:
				return (int) ((player.getStat().getAttackTrait(TraitType.RAPIER) - 1) * 100 * 100);
			case DAMAGE_BONUS_BLUNT_1:
			case DAMAGE_BONUS_BLUNT_2:
			case DAMAGE_BONUS_BLUNT_3:
			case DAMAGE_BONUS_BLUNT_4:
				return (int) ((player.getStat().getAttackTrait(TraitType.BLUNT) - 1) * 100 * 100);
			case DAMAGE_BONUS_SPEAR:
				return (int) ((player.getStat().getAttackTrait(TraitType.POLE) - 1) * 100 * 100);
			case DAMAGE_BONUS_FISTS:
				return (int) ((player.getStat().getAttackTrait(TraitType.DUALFIST) - 1) * 100 * 100);
			case DAMAGE_BONUS_DUAL_SWORDS:
				return (int) ((player.getStat().getAttackTrait(TraitType.DUAL) - 1) * 100 * 100);
			case DAMAGE_BONUS_BOW:
				return (int) ((player.getStat().getAttackTrait(TraitType.BOW) - 1) * 100 * 100);
			case DAMAGE_BONUS_FIREARMS:
				return (int) ((player.getStat().getAttackTrait(TraitType.PISTOLS) - 1) * 100 * 100);
			// Defense
			case DAMAGE_RESISTANCE_BONUS_SWORD_1:
			case DAMAGE_RESISTANCE_BONUS_SWORD_2:
			case DAMAGE_RESISTANCE_BONUS_SWORD_3:
				return (int) (player.getStat().getDefenceTrait(TraitType.SWORD) * 100 * 100);
			case DAMAGE_RESISTANCE_BONUS_ANCIENTSWORD:
				return (int) (player.getStat().getDefenceTrait(TraitType.ANCIENTSWORD) * 100 * 100);
			case DAMAGE_RESISTANCE_BONUS_DAGGER:
				return (int) (player.getStat().getDefenceTrait(TraitType.DAGGER) * 100 * 100);
			case DAMAGE_RESISTANCE_BONUS_RAPIER:
				return (int) (player.getStat().getDefenceTrait(TraitType.RAPIER) * 100 * 100);
			case DAMAGE_RESISTANCE_BONUS_BLUNT_1:
			case DAMAGE_RESISTANCE_BONUS_BLUNT_2:
			case DAMAGE_RESISTANCE_BONUS_BLUNT_3:
			case DAMAGE_RESISTANCE_BONUS_BLUNT_4:
				return (int) (player.getStat().getDefenceTrait(TraitType.BLUNT) * 100 * 100);
			case DAMAGE_RESISTANCE_BONUS_SPEAR:
				return (int) (player.getStat().getDefenceTrait(TraitType.POLE) * 100 * 100);
			case DAMAGE_RESISTANCE_BONUS_FISTS:
				return (int) (player.getStat().getDefenceTrait(TraitType.DUALFIST) * 100 * 100);
			case DAMAGE_RESISTANCE_BONUS_DUALSWORDS:
				return (int) (player.getStat().getDefenceTrait(TraitType.DUAL) * 100 * 100);
			case DAMAGE_RESISTANCE_BONUS_BOW:
				return (int) (player.getStat().getDefenceTrait(TraitType.BOW) * 100 * 100);
			case DAMAGE_RESISTANCE_BONUS_FIREARMS:
				return (int) (player.getStat().getDefenceTrait(TraitType.PISTOLS) * 100 * 100);
			// Evasion
			case P_SKILL_EVASION:
				return (int) ((player.getStat().getSkillEvasionTypeValue(0)) * 100);
			case M_SKILL_EVASION:
				return (int) ((player.getStat().getSkillEvasionTypeValue(1)) * 100);
			// Skill Cooldown
			case P_SKILL_COOLDOWN:
				return (int) ((player.getStat().getReuseTypeValue(0) - 1) * 100 * 100);
			case M_SKILL_COOLDOWN:
				return (int) ((player.getStat().getReuseTypeValue(1) - 1) * 100 * 100);
			case SD_SKILL_COOLDOWN:
				return (int) ((player.getStat().getReuseTypeValue(2) - 1) * 100 * 100);
			// MP Consumption
			case P_SKILL_MP_CONSUMPTION_PER:
				return (int) ((player.getStat().getMpConsumeTypeValue(0)) * 100) * 100 - 100_00;
			case M_SKILL_MP_CONSUMPTION_PER:
				return (int) ((player.getStat().getMpConsumeTypeValue(1)) * 100) * 100 - 100_00;
			case SD_SKILL_MP_CONSUMPTION_PER:
				return (int) ((player.getStat().getMpConsumeTypeValue(2)) * 100) * 100 - 100_00;
			// Anomalies
			case PARALYSIS_ATK_RATE:
				return (int) ((player.getStat().getAttackTrait(TraitType.PARALYZE) - 1) * 100 * 100);
			case SHOCK_ATK_RATE:
				return (int) ((player.getStat().getAttackTrait(TraitType.SHOCK) - 1) * 100 * 100);
			case KNOCKBACK_ATK_RATE:
				return (int) ((player.getStat().getAttackTrait(TraitType.KNOCKDOWN) - 1) * 100 * 100);
			case SLEEP_ATK_RATE:
				return (int) ((player.getStat().getAttackTrait(TraitType.SLEEP) - 1) * 100 * 100);
			case IMPRISOMENT_ATK_RATE:
				return (int) ((player.getStat().getAttackTrait(TraitType.IMPRISONMENT) - 1) * 100 * 100);
			case PULL_ATK_RATE:
				return (int) ((player.getStat().getAttackTrait(TraitType.PULL) - 1) * 100 * 100);
			case FEAR_ATK_RATE:
				return (int) ((player.getStat().getAttackTrait(TraitType.DERANGEMENT) - 1) * 100 * 100);
			case SILENCE_ATK_RATE:
				return (int) ((player.getStat().getAttackTrait(TraitType.DERANGEMENT) - 1) * 100 * 100);
			case HOLD_ATK_RATE:
				return (int) ((player.getStat().getAttackTrait(TraitType.HOLD) - 1) * 100 * 100);
			case SUPPRESSION_ATK_RATE:
				return (int) ((player.getStat().getAttackTrait(TraitType.SUPPRESSION) - 1) * 100 * 100);
			case INFECTION_ATK_RATE:
				return (int) ((player.getStat().getAttackTrait(TraitType.POISON) - 1) * 100 * 100);
			case PARALYSIS_RESISTANCE:
				return (int) (player.getStat().getDefenceTrait(TraitType.PARALYZE) * 100 * 100);
			case SHOCK_RESISTANCE:
				return (int) (player.getStat().getDefenceTrait(TraitType.SHOCK) * 100 * 100);
			case KNOCKBACK_RESISTANCE:
				return (int) (player.getStat().getDefenceTrait(TraitType.KNOCKDOWN) * 100 * 100);
			case SLEEP_RESISTANCE:
				return (int) (player.getStat().getDefenceTrait(TraitType.SLEEP) * 100 * 100);
			case IMPRISOMENT_RESISTANCE:
				return (int) (player.getStat().getDefenceTrait(TraitType.IMPRISONMENT) * 100 * 100);
			case PULL_RESISTANCE:
				return (int) (player.getStat().getDefenceTrait(TraitType.PULL) * 100 * 100);
			case FEAR_RESISTANCE:
				return (int) (player.getStat().getDefenceTrait(TraitType.DERANGEMENT) * 100 * 100);
			case SILENCE_RESISTANCE:
				return (int) (player.getStat().getDefenceTrait(TraitType.DERANGEMENT) * 100 * 100);
			case HOLD_RESISTANCE:
				return (int) (player.getStat().getDefenceTrait(TraitType.HOLD) * 100 * 100);
			case SUPPRESSION_RESISTANCE:
				return (int) (player.getStat().getDefenceTrait(TraitType.SUPPRESSION) * 100 * 100);
			case INFECTION_RESISTANCE:
				return (int) (player.getStat().getDefenceTrait(TraitType.POISON) * 100 * 100);
			default:
				return 0;
		}
	}

	private enum StatInfo {
		// Attack
		P_ATK_PER(Stat.PHYSICAL_ATTACK, StatModifierType.PER),
		P_ATK_DIFF(Stat.PHYSICAL_ATTACK, StatModifierType.DIFF),
		M_ATK_PER(Stat.MAGIC_ATTACK, StatModifierType.PER),
		M_ATK_DIFF(Stat.MAGIC_ATTACK, StatModifierType.DIFF),
		SOULSHOT_DAMAGE(Stat.SOULSHOT_DAMAGE, StatModifierType.PER),
		SPIRITSHOT_DAMAGE(Stat.SPIRITSHOT_DAMAGE, StatModifierType.PER),
		SOULSHOT_DAMAGE_WEAP(Stat.SHOTS_BONUS, StatModifierType.PER),
		SPIRITSHOT_DAMAGE_WEAP(Stat.SHOTS_BONUS, StatModifierType.PER),
		SOULSHOT_DAMAGE_SKILL(Stat.SOULSHOT_DAMAGE, StatModifierType.PER),
		SPIRITSHOT_DAMAGE_SKILL(Stat.SPIRITSHOT_DAMAGE, StatModifierType.PER),
		PVP_DAMAGE(Stat.PVP_PHYSICAL_ATTACK_DAMAGE, StatModifierType.PER),
		PVP_SKILL_DAMAGE(Stat.PVP_PHYSICAL_SKILL_DAMAGE, StatModifierType.PER),
		PVP_MAGIC_SKILL_DAMAGE(Stat.PVP_MAGICAL_SKILL_DAMAGE, StatModifierType.PER),
		PVP_DAMAGE_2(Stat.PVP_PHYSICAL_ATTACK_DAMAGE, StatModifierType.PER),
		PVP_DAMAGE_DECREASE_IGNORE(Stat.PVP_DAMAGE_DECREASE_IGNORE, StatModifierType.DIFF),
		PVE_DAMAGE(Stat.PVE_PHYSICAL_ATTACK_DAMAGE, StatModifierType.PER),
		PVE_SKILL_DAMAGE(Stat.PVE_PHYSICAL_SKILL_DAMAGE, StatModifierType.PER),
		PVE_MAGIC_SKILL_DAMAGE(Stat.PVE_MAGICAL_SKILL_DAMAGE, StatModifierType.PER),
		PVE_DAMAGE_2(Stat.PVE_PHYSICAL_ATTACK_DAMAGE, StatModifierType.PER),
		PVE_DAMAGE_DECREASE_IGNORE(null, null), // TODO
		BASIC_POWER(null, null), // TODO
		P_SKILL_POWER(Stat.PHYSICAL_SKILL_POWER, StatModifierType.PER),
		M_SKILL_POWER(Stat.MAGICAL_SKILL_POWER, StatModifierType.PER),
		AOE_SKILL_DAMAGE(null, null), // TODO
		DAMAGE_BONUS_SWORD_1(null, null),
		DAMAGE_BONUS_SWORD_2(null, null),
		DAMAGE_BONUS_SWORD_3(null, null),
		DAMAGE_BONUS_ANCIENT_SWORD(null, null),
		DAMAGE_BONUS_DAGGER(null, null),
		DAMAGE_BONUS_RAPIER(null, null),
		DAMAGE_BONUS_BLUNT_1(null, null),
		DAMAGE_BONUS_BLUNT_2(null, null),
		DAMAGE_BONUS_BLUNT_3(null, null),
		DAMAGE_BONUS_BLUNT_4(null, null),
		DAMAGE_BONUS_SPEAR(null, null),
		DAMAGE_BONUS_FISTS(null, null),
		DAMAGE_BONUS_DUAL_SWORDS(null, null),
		DAMAGE_BONUS_BOW(null, null),
		DAMAGE_BONUS_FIREARMS(null, null),
		// Defense
		P_DEF_PER(Stat.PHYSICAL_DEFENCE, StatModifierType.PER),
		P_DEF_DIFF(Stat.PHYSICAL_DEFENCE, StatModifierType.DIFF),
		M_DEF_PER(Stat.MAGICAL_DEFENCE, StatModifierType.PER),
		M_DEF_DIFF(Stat.MAGICAL_DEFENCE, StatModifierType.DIFF),
		SOULSHOT_DAMAGE_RESISTANCE(Stat.SOULSHOT_DAMAGE_RESISTANCE, StatModifierType.PER),
		SPIRITSHOT_DAMAGE_RESISTANCE(Stat.SPIRITSHOT_DAMAGE_RESISTANCE, StatModifierType.PER), // TODO
		RECEIVED_PVP_DAMAGE(Stat.PVP_PHYSICAL_ATTACK_DEFENCE, StatModifierType.PER),
		RECEIVED_PVP_SKILL_DAMAGE(Stat.PVP_PHYSICAL_SKILL_DEFENCE, StatModifierType.PER),
		RECEIVED_PVP_MAGIC_SKILLDAMAGE(Stat.PVP_MAGICAL_SKILL_DEFENCE, StatModifierType.PER),
		RECEIVED_PVP_DAMAGE_2(null, null), // TODO
		RECEIVED_PVE_DAMAGE_DECREASE(null, null), // TODO
		RECEIVED_PVE_DAMAGE(Stat.PVE_PHYSICAL_ATTACK_DEFENCE, StatModifierType.PER),
		RECEIVED_PVE_SKILL_DAMAGE(Stat.PVE_PHYSICAL_SKILL_DEFENCE, StatModifierType.PER),
		RECEIVED_PVE_MAGIC_SKILL_DAMAGE(Stat.PVE_MAGICAL_SKILL_DEFENCE, StatModifierType.PER),
		RECEIVED_PVE_DAMAGE_2(null, null), // TODO
		PVP_DAMAGE_DECREASE(Stat.PVP_DAMAGE_DECREASE, StatModifierType.DIFF),
		RECEIVED_BASIC_DAMAGE_POWER(null, null), // TODO
		P_SKILL_POWER_WHEN_TAKING_DAMAGE(null, null), // TODO
		M_SKILL_POWER_WHEN_TAKING_DAMAGE(null, null), // TODO
		RECEIVED_AOE_DAMAGE(Stat.DEFENCE_AOE_SKILL_DAMAGE_PER, StatModifierType.PER),
		DAMAGE_RESISTANCE_BONUS_SWORD_1(null, null),
		DAMAGE_RESISTANCE_BONUS_SWORD_2(null, null),
		DAMAGE_RESISTANCE_BONUS_SWORD_3(null, null),
		DAMAGE_RESISTANCE_BONUS_ANCIENTSWORD(null, null),
		DAMAGE_RESISTANCE_BONUS_DAGGER(null, null),
		DAMAGE_RESISTANCE_BONUS_RAPIER(null, null),
		DAMAGE_RESISTANCE_BONUS_BLUNT_1(null, null),
		DAMAGE_RESISTANCE_BONUS_BLUNT_2(null, null),
		DAMAGE_RESISTANCE_BONUS_BLUNT_3(null, null),
		DAMAGE_RESISTANCE_BONUS_BLUNT_4(null, null),
		DAMAGE_RESISTANCE_BONUS_SPEAR(null, null),
		DAMAGE_RESISTANCE_BONUS_FISTS(null, null),
		DAMAGE_RESISTANCE_BONUS_DUALSWORDS(null, null),
		DAMAGE_RESISTANCE_BONUS_BOW(null, null),
		DAMAGE_RESISTANCE_BONUS_FIREARMS(null, null),
		SHIELD_DEFENSE_PER(Stat.SHIELD_DEFENCE, StatModifierType.PER),
		SHIELD_DEFENSE_DIFF(Stat.SHIELD_DEFENCE, StatModifierType.DIFF),
		SHIELD_DEFENSE_RATE(Stat.SHIELD_DEFENCE_RATE, StatModifierType.PER),
		MAGIC_DAMAGE_RESISTANCE_PER(Stat.MAGIC_SUCCESS_RES, StatModifierType.PER),
		MAGIC_DAMAGE_RESISTANCE_DIFF(Stat.MAGIC_SUCCESS_RES, StatModifierType.DIFF),
		MAGIC_REFLECT(Stat.REFLECT_SKILL_MAGIC, StatModifierType.PER),
		MAGIC_REFLECT_RESISTANCE(Stat.REFLECT_SKILL_MAGIC, StatModifierType.PER),
		RECEIVED_FIXED_DAMAGE_RESISTANCE(Stat.REAL_DAMAGE_RESIST, StatModifierType.PER),
		CASTING_INTERUPTION_RATE_PER(null, null), // TODO
		CASTING_INTERUPTION_RATE_DIFF(null, null), // TODO
		// Accuracy
		P_ACCURACY_PER(Stat.ACCURACY_COMBAT, StatModifierType.PER),
		P_ACCURACY_DIFF(Stat.ACCURACY_COMBAT, StatModifierType.DIFF),
		M_ACCURACY_PER(Stat.ACCURACY_MAGIC, StatModifierType.PER),
		M_ACCURACY_DIFF(Stat.ACCURACY_MAGIC, StatModifierType.DIFF),
		VITAL_POINT_ATTACK_RATE_PER(Stat.BLOW_RATE, StatModifierType.PER),
		VITAL_POINT_ATTACK_RATE_DIFF(Stat.BLOW_RATE, StatModifierType.DIFF),
		// Evasion
		P_EVASION_PER(Stat.EVASION_RATE, StatModifierType.PER),
		P_EVASION_DIFF(Stat.EVASION_RATE, StatModifierType.DIFF),
		M_EVASION_PER(Stat.MAGIC_EVASION_RATE, StatModifierType.PER),
		M_EVASION_DIFF(Stat.MAGIC_EVASION_RATE, StatModifierType.DIFF),
		RECEIVED_VITAL_POINT_ATTACK_RATE_PER(Stat.BLOW_RATE_DEFENCE, StatModifierType.PER),
		RECEIVED_VITAL_POINT_ATTACK_RATE_DIFF(Stat.BLOW_RATE_DEFENCE, StatModifierType.DIFF),
		P_SKILL_EVASION(null, null),
		M_SKILL_EVASION(null, null),
		// Speed
		ATK_SPEED_PER(Stat.PHYSICAL_ATTACK_SPEED, StatModifierType.PER),
		ATK_SPEED_DIFF(Stat.PHYSICAL_ATTACK_SPEED, StatModifierType.DIFF),
		CAST_SPEED_PER(Stat.MAGIC_ATTACK_SPEED, StatModifierType.PER),
		CAST_SPEED_DIFF(Stat.MAGIC_ATTACK_SPEED, StatModifierType.DIFF),
		SPEED_PER(Stat.RUN_SPEED, StatModifierType.PER),
		SPEED_DIFF(Stat.RUN_SPEED, StatModifierType.DIFF),
		// Critical Rate
		CRIT_RATE_PER(Stat.CRITICAL_RATE, StatModifierType.PER),
		CRIT_RATE_DIFF(Stat.CRITICAL_RATE, StatModifierType.DIFF),
		SKILL_CRITICAL_RATE_PER(Stat.SKILL_CRITICAL_PROBABILITY, StatModifierType.PER),
		SKILL_CRITICAL_RATE_DIFF(Stat.SKILL_CRITICAL_PROBABILITY, StatModifierType.DIFF),
		M_SKILL_CRITICAL_RATE_PER(Stat.MAGIC_CRITICAL_RATE, StatModifierType.PER),
		M_SKILL_CRITICAL_RATE_DIFF(Stat.MAGIC_CRITICAL_RATE, StatModifierType.DIFF),
		RECEIVED_P_CRITICAL_RATE_PER(Stat.DEFENCE_CRITICAL_RATE, StatModifierType.PER),
		RECEIVED_P_CRITICAL_RATE_DIFF(Stat.DEFENCE_CRITICAL_RATE_ADD, StatModifierType.DIFF),
		RECEIVED_P_SKILL_CRITICAL_RATE_PER(Stat.DEFENCE_PHYSICAL_SKILL_CRITICAL_RATE, StatModifierType.PER),
		RECEIVED_P_SKILL_CRITICAL_RATE_DIFF(Stat.DEFENCE_PHYSICAL_SKILL_CRITICAL_RATE_ADD, StatModifierType.DIFF),
		RECEIVED_M_SKILL_CRITICAL_RATE_PER(Stat.DEFENCE_MAGIC_CRITICAL_RATE, StatModifierType.PER),
		RECEIVED_M_SKILL_CRITICAL_RATE_DIFF(Stat.DEFENCE_MAGIC_CRITICAL_RATE_ADD, StatModifierType.DIFF),
		// Critical Damage
		CRIT_DAMAGE_PER(Stat.CRITICAL_DAMAGE, StatModifierType.PER),
		CRIT_DAMAGE_DIFF(Stat.CRITICAL_DAMAGE_ADD, StatModifierType.DIFF),
		SKILL_CRITICAL_DAMAGE_PER(Stat.CRITICAL_DAMAGE_SKILL, StatModifierType.PER),
		SKILL_CRITICAL_DAMAGE_DIFF(Stat.CRITICAL_DAMAGE_SKILL_ADD, StatModifierType.DIFF),
		M_SKILL_CRITICAL_DAMAGE_PER(Stat.MAGIC_CRITICAL_DAMAGE, StatModifierType.PER),
		M_SKILL_CRITICAL_DAMAGE_DIFF(Stat.MAGIC_CRITICAL_DAMAGE_ADD, StatModifierType.DIFF),
		RECEIVED_CRITICAL_DAMAGE_PER(Stat.DEFENCE_CRITICAL_DAMAGE, StatModifierType.PER),
		RECEIVED_CRITICAL_DAMAGE_DIFF(Stat.DEFENCE_CRITICAL_DAMAGE_ADD, StatModifierType.DIFF),
		RECEIVED_SKILL_CRITICAL_DAMAGE_PER(Stat.DEFENCE_CRITICAL_DAMAGE_SKILL, StatModifierType.PER),
		RECEIVED_SKILL_CRITICAL_DAMAGE_DIFF(Stat.DEFENCE_CRITICAL_DAMAGE_SKILL_ADD, StatModifierType.DIFF),
		RECEIVED_MAGIC_CRITICAL_DAMAGE_PER(Stat.DEFENCE_MAGIC_CRITICAL_DAMAGE, StatModifierType.PER),
		RECEIVED_MAGIC_CRITICAL_DAMAGE_DIFF(Stat.DEFENCE_MAGIC_CRITICAL_DAMAGE_ADD, StatModifierType.DIFF),
		// Recovery
		HP_RECOVERY_POTIONS_EFFECT_PER(Stat.ADDITIONAL_POTION_HP_PER, StatModifierType.PER),
		HP_RECOVERY_POTIONS_EFFECT_DIFF(Stat.ADDITIONAL_POTION_HP, StatModifierType.DIFF),
		MP_RECOVERY_POTIONS_EFFECT_PER(Stat.ADDITIONAL_POTION_MP_PER, StatModifierType.PER),
		MP_RECOVERY_POTIONS_EFFECT_DIFF(Stat.ADDITIONAL_POTION_MP, StatModifierType.DIFF),
		HP_RECOVERY_RATE_PER(Stat.REGENERATE_HP_RATE, StatModifierType.PER),
		HP_RECOVERY_RATE_DIFF(Stat.REGENERATE_HP_RATE, StatModifierType.DIFF),
		HP_RECOVERY_RATE_STAND_PER(Stat.REGENERATE_HP_RATE, StatModifierType.PER),
		HP_RECOVERY_RATE_STAND_DIFF(Stat.REGENERATE_HP_RATE, StatModifierType.DIFF),
		HP_RECOVERY_RATE_SIT_PER(Stat.REGENERATE_HP_RATE, StatModifierType.PER),
		HP_RECOVERY_RATE_SIT_DIFF(Stat.REGENERATE_HP_RATE, StatModifierType.DIFF),
		HP_RECOVERY_RATE_WALK_PER(Stat.REGENERATE_HP_RATE, StatModifierType.PER),
		HP_RECOVERY_RATE_WALK_DIFF(Stat.REGENERATE_HP_RATE, StatModifierType.DIFF),
		HP_RECOVERY_RATE_RUN_PER(Stat.REGENERATE_HP_RATE, StatModifierType.PER),
		HP_RECOVERY_RATE_RUN_DIFF(Stat.REGENERATE_HP_RATE, StatModifierType.DIFF),
		MP_RECOVERY_RATE_PER(Stat.REGENERATE_MP_RATE, StatModifierType.PER),
		MP_RECOVERY_RATE_DIFF(Stat.REGENERATE_MP_RATE, StatModifierType.DIFF),
		MP_RECOVERY_RATE_STAND_PER(Stat.REGENERATE_MP_RATE, StatModifierType.PER),
		MP_RECOVERY_RATE_STAND_DIFF(Stat.REGENERATE_MP_RATE, StatModifierType.DIFF),
		MP_RECOVERY_RATE_SIT_PER(Stat.REGENERATE_MP_RATE, StatModifierType.PER),
		MP_RECOVERY_RATE_SIT_DIFF(Stat.REGENERATE_MP_RATE, StatModifierType.DIFF),
		MP_RECOVERY_RATE_WALK_PER(Stat.REGENERATE_MP_RATE, StatModifierType.PER),
		MP_RECOVERY_RATE_WALK_DIFF(Stat.REGENERATE_MP_RATE, StatModifierType.DIFF),
		MP_RECOVERY_RATE_RUN_PER(Stat.REGENERATE_MP_RATE, StatModifierType.PER),
		MP_RECOVERY_RATE_RUN_DIFF(Stat.REGENERATE_MP_RATE, StatModifierType.DIFF),
		CP_RECOVERY_RATE_PER(Stat.REGENERATE_CP_RATE, StatModifierType.PER),
		CP_RECOVERY_RATE_DIFF(Stat.REGENERATE_CP_RATE, StatModifierType.DIFF),
		CP_RECOVERY_RATE_STAND_PER(Stat.REGENERATE_CP_RATE, StatModifierType.PER),
		CP_RECOVERY_RATE_STAND_DIFF(Stat.REGENERATE_CP_RATE, StatModifierType.DIFF),
		CP_RECOVERY_RATE_SIT_PER(Stat.REGENERATE_CP_RATE, StatModifierType.PER),
		CP_RECOVERY_RATE_SIT_DIFF(Stat.REGENERATE_CP_RATE, StatModifierType.DIFF),
		CP_RECOVERY_RATE_WALK_PER(Stat.REGENERATE_CP_RATE, StatModifierType.PER),
		CP_RECOVERY_RATE_WALK_DIFF(Stat.REGENERATE_CP_RATE, StatModifierType.DIFF),
		CP_RECOVERY_RATE_RUN_PER(Stat.REGENERATE_CP_RATE, StatModifierType.PER),
		CP_RECOVERY_RATE_RUN_DIFF(Stat.REGENERATE_CP_RATE, StatModifierType.DIFF),
		// Skill Cooldown
		P_SKILL_COOLDOWN(null, null),
		M_SKILL_COOLDOWN(null, null),
		SD_SKILL_COOLDOWN(null, null),
		// MP Consumption
		P_SKILL_MP_CONSUMPTION_PER(null, null),
		M_SKILL_MP_CONSUMPTION_PER(null, null),
		SD_SKILL_MP_CONSUMPTION_PER(null, null),
		P_SKILL_MP_CONSUMPTION_DIFF(null, null), // TODO
		M_SKILL_MP_CONSUMPTION_DIFF(null, null), // TODO
		SD_SKILL_MP_CONSUMPTION_DIFF(null, null), // TODO
		// Anomalies
		BUFF_CANCEL_RESISTANCE_BONUS(Stat.RESIST_DISPEL_BUFF, StatModifierType.PER),
		DEBUFF_ANOMALY_RESISTANCE_BONUS(Stat.RESIST_ABNORMAL_DEBUFF, StatModifierType.PER),
		PARALYSIS_ATK_RATE(null, null),
		SHOCK_ATK_RATE(null, null),
		KNOCKBACK_ATK_RATE(null, null),
		SLEEP_ATK_RATE(null, null),
		IMPRISOMENT_ATK_RATE(null, null), // TODO
		PULL_ATK_RATE(null, null),
		FEAR_ATK_RATE(null, null),
		SILENCE_ATK_RATE(null, null),
		HOLD_ATK_RATE(null, null),
		SUPPRESSION_ATK_RATE(null, null),
		INFECTION_ATK_RATE(null, null),
		PARALYSIS_RESISTANCE(null, null),
		SHOCK_RESISTANCE(null, null),
		KNOCKBACK_RESISTANCE(null, null),
		SLEEP_RESISTANCE(null, null),
		IMPRISOMENT_RESISTANCE(null, null), // TODO
		PULL_RESISTANCE(null, null),
		FEAR_RESISTANCE(null, null),
		SILENCE_RESISTANCE(null, null),
		HOLD_RESISTANCE(null, null),
		SUPPRESSION_RESISTANCE(null, null),
		INFECTION_RESISTANCE(null, null);

		private final Stat _stat;
		private final StatModifierType _mode;

		StatInfo(Stat stat, StatModifierType mode) {
			_stat = stat;
			_mode = mode;
		}
	}
}
