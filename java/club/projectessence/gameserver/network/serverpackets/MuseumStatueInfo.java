package club.projectessence.gameserver.network.serverpackets;

import club.projectessence.commons.network.ServerPacketId;
import club.projectessence.gameserver.features.museum.MuseumStatueInstance;
import club.projectessence.gameserver.model.CharSelectInfoPackage;
import club.projectessence.gameserver.model.actor.Creature;
import club.projectessence.gameserver.model.itemcontainer.Inventory;
import club.projectessence.gameserver.network.GameClient;
import io.github.joealisson.mmocore.WritableBuffer;

/**
 * Museum Statue Info packet - displays player appearance instead of NPC
 * Based on L2J Sunrise core.diff implementation
 */
public class MuseumStatueInfo extends ServerPacket
{
    private static final int[] PAPERDOLL_ORDER = new int[]
    {
        Inventory.PAPERDOLL_RHAND,
        Inventory.PAPERDOLL_LHAND,
        Inventory.PAPERDOLL_GLOVES,
        Inventory.PAPERDOLL_CHEST,
        Inventory.PAPERDOLL_LEGS,
        Inventory.PAPERDOLL_FEET,
        Inventory.PAPERDOLL_RHAND,
        Inventory.PAPERDOLL_HAIR,
        Inventory.PAPERDOLL_HAIR2,
    };
    
    private final MuseumStatueInstance _statue;
    private final CharSelectInfoPackage _charLooks;
    
    public MuseumStatueInfo(MuseumStatueInstance statue, Creature attacker)
    {
        _statue = statue;
        _charLooks = statue.getCharLooks();
    }
    
    @Override
    public void writeImpl(GameClient client, WritableBuffer buffer)
    {
        if (_charLooks == null)
        {
            // Fallback to normal NPC display if no character data
            writeNormalNpcInfo(buffer);
            return;
        }
        
        // Write as player-like appearance (CharInfo-style)
        writeId(ServerPacketId.NPC_INFO, buffer);
        
        // Basic NPC info structure but with player data
        buffer.writeInt(_statue.getObjectId());
        buffer.writeInt(_charLooks.getBaseClassId() + 1000000); // Use player class as display ID
        buffer.writeInt(0); // Not attackable
        buffer.writeInt(_statue.getX());
        buffer.writeInt(_statue.getY());
        buffer.writeInt(_statue.getZ());
        buffer.writeInt(_statue.getHeading());
        buffer.writeInt(0); // Unknown
        buffer.writeInt(0); // mAtkSpd
        buffer.writeInt(0); // pAtkSpd
        buffer.writeInt(0); // runSpd
        buffer.writeInt(0); // walkSpd
        buffer.writeInt(0); // swimRunSpd
        buffer.writeInt(0); // swimWalkSpd
        buffer.writeInt(0); // flyRunSpd
        buffer.writeInt(0); // flyWalkSpd
        buffer.writeInt(0); // flyRunSpd
        buffer.writeInt(0); // flyWalkSpd
        buffer.writeDouble(1.0); // moveMultiplier
        buffer.writeDouble(1.0); // attackSpeedMultiplier
        buffer.writeDouble(_statue.getTemplate().getFCollisionRadius()); // collisionRadius
        buffer.writeDouble(_statue.getTemplate().getFCollisionHeight()); // collisionHeight
        
        // Equipment from player data
        buffer.writeInt(_charLooks.getPaperdollItemId(Inventory.PAPERDOLL_RHAND)); // right hand
        buffer.writeInt(_charLooks.getPaperdollItemId(Inventory.PAPERDOLL_CHEST)); // chest
        buffer.writeInt(_charLooks.getPaperdollItemId(Inventory.PAPERDOLL_LHAND)); // left hand
        
        buffer.writeByte(1); // name above char
        buffer.writeByte(0); // not running
        buffer.writeByte(0); // not in combat
        buffer.writeByte(0); // not dead
        buffer.writeByte(0); // not summoned
        
        buffer.writeInt(-1); // NPCString ID for name
        buffer.writeString(_charLooks.getName()); // Player name
        buffer.writeInt(-1); // NPCString ID for title
        buffer.writeString(_statue.getTitle()); // Category title
        
        buffer.writeInt(0); // title color
        buffer.writeInt(0); // pvp flag
        buffer.writeInt(0); // karma
        buffer.writeInt(0); // abnormal effects
        buffer.writeInt(0); // clan id
        buffer.writeInt(0); // crest id
        buffer.writeInt(0); // ally id
        buffer.writeInt(0); // ally crest
        buffer.writeByte(0); // in water/flying
        buffer.writeByte(0); // team
        buffer.writeDouble(_statue.getTemplate().getFCollisionRadius());
        buffer.writeDouble(_statue.getTemplate().getFCollisionHeight());
        buffer.writeInt(0); // enchant effect
        buffer.writeInt(0); // flying
        buffer.writeInt(0); // unknown
        buffer.writeInt(0); // color effect
        buffer.writeByte(1); // targetable
        buffer.writeByte(1); // show name
        buffer.writeInt(0); // abnormal visual effect special
        buffer.writeInt(0); // display effect
    }
    
    private void writeNormalNpcInfo(WritableBuffer buffer)
    {
        // Fallback to basic NPC info if no character data
        writeId(ServerPacketId.NPC_INFO, buffer);
        buffer.writeInt(_statue.getObjectId());
        buffer.writeInt(_statue.getTemplate().getDisplayId() + 1000000);
        buffer.writeInt(0); // not attackable
        buffer.writeInt(_statue.getX());
        buffer.writeInt(_statue.getY());
        buffer.writeInt(_statue.getZ());
        buffer.writeInt(_statue.getHeading());
        buffer.writeInt(0);
        buffer.writeInt(0);
        buffer.writeInt(0);
        buffer.writeInt(0);
        buffer.writeInt(0);
        buffer.writeInt(0);
        buffer.writeInt(0);
        buffer.writeInt(0);
        buffer.writeInt(0);
        buffer.writeInt(0);
        buffer.writeInt(0);
        buffer.writeDouble(1.0);
        buffer.writeDouble(1.0);
        buffer.writeDouble(_statue.getTemplate().getFCollisionRadius());
        buffer.writeDouble(_statue.getTemplate().getFCollisionHeight());
        buffer.writeInt(0);
        buffer.writeInt(0);
        buffer.writeInt(0);
        buffer.writeByte(1);
        buffer.writeByte(0);
        buffer.writeByte(0);
        buffer.writeByte(0);
        buffer.writeByte(0);
        buffer.writeInt(-1);
        buffer.writeString(_statue.getName());
        buffer.writeInt(-1);
        buffer.writeString(_statue.getTitle());
        buffer.writeInt(0);
        buffer.writeInt(0);
        buffer.writeInt(0);
        buffer.writeInt(0);
        buffer.writeInt(0);
        buffer.writeInt(0);
        buffer.writeInt(0);
        buffer.writeInt(0);
        buffer.writeByte(0);
        buffer.writeByte(0);
        buffer.writeDouble(_statue.getTemplate().getFCollisionRadius());
        buffer.writeDouble(_statue.getTemplate().getFCollisionHeight());
        buffer.writeInt(0);
        buffer.writeInt(0);
        buffer.writeInt(0);
        buffer.writeInt(0);
        buffer.writeByte(1);
        buffer.writeByte(1);
        buffer.writeInt(0);
        buffer.writeInt(0);
    }
}
