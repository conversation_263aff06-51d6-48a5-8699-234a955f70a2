/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.network.serverpackets;

import club.projectessence.Config;
import club.projectessence.commons.network.ServerPacketId;
import club.projectessence.gameserver.model.buylist.Product;
import club.projectessence.gameserver.model.buylist.ProductList;
import club.projectessence.gameserver.model.items.Item;
import club.projectessence.gameserver.network.GameClient;
import io.github.joealisson.mmocore.WritableBuffer;

import java.util.Collection;

public class ShopPreviewList extends ServerPacket {
	private final int _listId;
	private final Collection<Product> _list;
	private final long _money;

	public ShopPreviewList(ProductList list, long currentMoney) {
		_listId = list.getListId();
		_list = list.getProducts();
		_money = currentMoney;
	}

	public ShopPreviewList(Collection<Product> lst, int listId, long currentMoney) {
		_listId = listId;
		_list = lst;
		_money = currentMoney;
	}

	@Override
	public void writeImpl(GameClient client, WritableBuffer buffer) {
		writeId(ServerPacketId.SHOP_PREVIEW_LIST, buffer);

		buffer.writeInt(5056);
		buffer.writeLong(_money); // current money
		buffer.writeInt(_listId);

		int newlength = 0;
		for (Product product : _list) {
			if (product.getItem().isEquipable()) {
				newlength++;
			}
		}
		buffer.writeShort(newlength);

		for (Product product : _list) {
			if (product.getItem().isEquipable()) {
				buffer.writeInt(product.getItemId());
				buffer.writeShort(product.getItem().getType2()); // item type2

				if (product.getItem().getType1() != Item.TYPE1_ITEM_QUESTITEM_ADENA) {
					buffer.writeLong(product.getItem().getBodyPart()); // rev 415 slot 0006-lr.ear 0008-neck 0030-lr.finger 0040-head 0080-?? 0100-l.hand 0200-gloves 0400-chest 0800-pants 1000-feet 2000-?? 4000-r.hand 8000-r.hand
				} else {
					buffer.writeLong(0x00); // rev 415 slot 0006-lr.ear 0008-neck 0030-lr.finger 0040-head 0080-?? 0100-l.hand 0200-gloves 0400-chest 0800-pants 1000-feet 2000-?? 4000-r.hand 8000-r.hand
				}

				buffer.writeLong(Config.WEAR_PRICE);
			}
		}
	}
}
