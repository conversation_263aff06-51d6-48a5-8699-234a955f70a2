/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.network.serverpackets;

import club.projectessence.commons.network.ServerPacketId;
import club.projectessence.gameserver.enums.AttributeType;
import club.projectessence.gameserver.model.Elementals;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.model.items.instance.ItemInstance;
import club.projectessence.gameserver.network.GameClient;
import io.github.joealisson.mmocore.WritableBuffer;

import java.util.HashSet;
import java.util.Set;

/**
 * <AUTHOR>
 */
public class ExChooseInventoryAttributeItem extends ServerPacket {
	private final int _itemId;
	private final long _count;
	private final AttributeType _atribute;
	private final int _level;
	private final Set<Integer> _items = new HashSet<>();

	public ExChooseInventoryAttributeItem(PlayerInstance player, ItemInstance stone) {
		_itemId = stone.getDisplayId();
		_count = stone.getCount();
		_atribute = AttributeType.findByClientId(Elementals.getItemElement(_itemId));
		if (_atribute == AttributeType.NONE) {
			throw new IllegalArgumentException("Undefined Atribute item: " + stone);
		}
		_level = Elementals.getMaxElementLevel(_itemId);

		// Register only items that can be put an attribute stone/crystal
		for (ItemInstance item : player.getInventory().getItems()) {
			if (item.isElementable()) {
				_items.add(item.getObjectId());
			}
		}
	}

	@Override
	public void writeImpl(GameClient client, WritableBuffer buffer) {
		writeId(ServerPacketId.EX_CHOOSE_INVENTORY_ATTRIBUTE_ITEM, buffer);

		buffer.writeInt(_itemId);
		buffer.writeLong(_count);
		buffer.writeInt(_atribute == AttributeType.FIRE ? 1 : 0); // Fire
		buffer.writeInt(_atribute == AttributeType.WATER ? 1 : 0); // Water
		buffer.writeInt(_atribute == AttributeType.WIND ? 1 : 0); // Wind
		buffer.writeInt(_atribute == AttributeType.EARTH ? 1 : 0); // Earth
		buffer.writeInt(_atribute == AttributeType.HOLY ? 1 : 0); // Holy
		buffer.writeInt(_atribute == AttributeType.DARK ? 1 : 0); // Unholy
		buffer.writeInt(_level); // Item max attribute level
		buffer.writeInt(_items.size());
		_items.forEach(buffer::writeInt);
	}
}
