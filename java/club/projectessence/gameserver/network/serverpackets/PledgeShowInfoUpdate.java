/*
 * This file is part of the L2J Mobius project.
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.network.serverpackets;

import club.projectessence.Config;
import club.projectessence.commons.network.ServerPacketId;
import club.projectessence.gameserver.model.clan.Clan;
import club.projectessence.gameserver.network.GameClient;
import io.github.joealisson.mmocore.WritableBuffer;

public class PledgeShowInfoUpdate extends ServerPacket
{
	private final Clan _clan;
	
	public PledgeShowInfoUpdate(Clan clan)
	{
		_clan = clan;
	}
	
	@Override
	public void writeImpl(GameClient client, WritableBuffer buffer)
	{
		writeId(ServerPacketId.PLEDGE_SHOW_INFO_UPDATE, buffer);
		// sending empty data so client will ask all the info in response ;)
		buffer.writeInt(_clan.getId());
		buffer.writeInt(Config.SERVER_ID);
		buffer.writeInt(_clan.getCrestId());
		buffer.writeInt(_clan.getLevel()); // clan level
		buffer.writeInt(_clan.getCastleId());
		buffer.writeInt(0x00); // castle state ?
		buffer.writeInt(_clan.getHideoutId());
		buffer.writeInt(_clan.getFortId());
		buffer.writeInt(_clan.getRank());
		buffer.writeInt(0x00); // clan reputation score
		buffer.writeInt(0x00); // ?
		buffer.writeInt(0x00); // ?
		buffer.writeInt(_clan.getAllyId());
		buffer.writeString(_clan.getAllyName()); // c5
		buffer.writeInt(_clan.getAllyCrestId()); // c5
		buffer.writeInt(_clan.isAtWar() ? 1 : 0); // c5
		buffer.writeInt(0x00); // TODO: Find me!
		buffer.writeInt(0x00); // TODO: Find me!
	}
}
