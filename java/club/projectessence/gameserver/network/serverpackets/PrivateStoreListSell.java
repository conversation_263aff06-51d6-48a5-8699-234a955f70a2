/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.network.serverpackets;

import club.projectessence.commons.network.ServerPacketId;
import club.projectessence.gameserver.instancemanager.SellBuffsManager;
import club.projectessence.gameserver.model.TradeItem;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.network.GameClient;
import io.github.joealisson.mmocore.WritableBuffer;

public class PrivateStoreListSell extends AbstractItemPacket {
	private final PlayerInstance _player;
	private final PlayerInstance _seller;

	public PrivateStoreListSell(PlayerInstance player, PlayerInstance seller) {
		_player = player;
		_seller = seller;
	}

	@Override
	public void writeImpl(GameClient client, WritableBuffer buffer) {
		if (_seller.isSellingBuffs()) {
			SellBuffsManager.getInstance().sendBuffMenu(_player, _seller, 0);
		} else {
			writeId(ServerPacketId.PRIVATE_STORE_LIST, buffer);

			buffer.writeInt(_seller.getObjectId());
			buffer.writeInt(_seller.getSellList().isPackaged() ? 1 : 0);
			buffer.writeLong(_player.getAdena());
			buffer.writeInt(0x00);
			buffer.writeInt(_seller.getSellList().getItems().size());
			for (TradeItem item : _seller.getSellList().getItems()) {
				writeItem(item, buffer);
				buffer.writeLong(item.getPrice());
				buffer.writeLong(item.getItem().getReferencePrice() * 2);
			}
		}
	}
}
