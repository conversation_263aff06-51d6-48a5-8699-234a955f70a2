/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.network.serverpackets;

import club.projectessence.commons.network.ServerPacketId;
import club.projectessence.gameserver.model.actor.Creature;
import club.projectessence.gameserver.model.skills.BuffInfo;
import club.projectessence.gameserver.network.GameClient;
import io.github.joealisson.mmocore.WritableBuffer;

import java.util.ArrayList;
import java.util.List;

public class ExAbnormalStatusUpdateFromTarget extends ServerPacket {
	private final Creature _creature;
	private final List<BuffInfo> _effects = new ArrayList<>();

	public ExAbnormalStatusUpdateFromTarget(Creature creature) {
		_creature = creature;

		if ((creature != null) && creature.isPlayable() && (creature.getActingPlayer() != null) && creature.getActingPlayer().isInOlympiadMode()) {
			for (BuffInfo info : creature.getEffectList().getEffects()) {
				if ((info != null) && info.isInUse() && !info.getSkill().isToggle()) {
					_effects.add(info);
				}
			}
		} else if (creature != null) {
			for (BuffInfo info : creature.getEffectList().getDebuffs()) {
				if ((info != null) && info.isInUse() && !info.getSkill().isToggle()) {
					_effects.add(info);
				}
			}
		}
	}

	@Override
	public void writeImpl(GameClient client, WritableBuffer buffer) {
		writeId(ServerPacketId.EX_ABNORMAL_STATUS_UPDATE_FROM_TARGET, buffer);

		buffer.writeInt(_creature.getObjectId());
		buffer.writeShort(_effects.size());

		for (BuffInfo info : _effects) {
			buffer.writeInt(info.getSkill().getDisplayId());
			buffer.writeShort(info.getSkill().getDisplayLevel());
			// buffer.writeShort(info.getSkill().getSubLevel());
			buffer.writeShort(info.getSkill().getAbnormalType().getClientId());
			writeOptionalD(info.getSkill().isAura() ? -1 : info.getTime(), buffer);
			buffer.writeInt(info.getEffectorObjectId());
		}
	}
}
