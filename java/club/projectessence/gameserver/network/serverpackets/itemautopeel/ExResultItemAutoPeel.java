/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.network.serverpackets.itemautopeel;

import club.projectessence.commons.network.ServerPacketId;
import club.projectessence.gameserver.model.holders.ItemHolder;
import club.projectessence.gameserver.network.GameClient;
import club.projectessence.gameserver.network.serverpackets.ServerPacket;
import io.github.joealisson.mmocore.WritableBuffer;

import java.util.List;

/**
 * <AUTHOR>
 */
public class ExResultItemAutoPeel extends ServerPacket {
	private final boolean _result;
	private final long _totalPeelCount;
	private final long _remainPeelCount;
	private final List<ItemHolder> _resultItemList;

	public ExResultItemAutoPeel(boolean result, long totalPeelCount, long remainPeelCount, List<ItemHolder> resultItemList) {
		_result = result;
		_totalPeelCount = totalPeelCount;
		_remainPeelCount = remainPeelCount;
		_resultItemList = resultItemList;
	}

	@Override
	public void writeImpl(GameClient client, WritableBuffer buffer) {
		writeId(ServerPacketId.EX_RESULT_ITEM_AUTO_PEEL, buffer);

		buffer.writeByte(_result); // bResult
		buffer.writeLong(_totalPeelCount); // nTotalPeelCount
		buffer.writeLong(_remainPeelCount); // nRemainPeelCount

		if (_resultItemList == null) {
			buffer.writeInt(0x00);
			return;
		}
		buffer.writeInt(_resultItemList.size());// vResultItemList
		for (ItemHolder holder : _resultItemList) {
			buffer.writeInt(holder.getId()); // nItemClassID
			buffer.writeLong(holder.getCount()); // nAmount
			buffer.writeInt(holder.getEnchant()); // AnnounceLevel
		}
	}
}
