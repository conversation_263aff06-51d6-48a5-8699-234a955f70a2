/*
 * This file is part of the L2J Mobius project.
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.network.serverpackets;

import java.util.Collection;

import club.projectessence.Config;
import club.projectessence.commons.network.ServerPacketId;
import club.projectessence.gameserver.data.sql.CharNameTable;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.model.clan.Clan;
import club.projectessence.gameserver.model.clan.Clan.SubPledge;
import club.projectessence.gameserver.model.clan.ClanMember;
import club.projectessence.gameserver.network.GameClient;
import io.github.joealisson.mmocore.WritableBuffer;

public class PledgeShowMemberListAll extends ServerPacket
{
	private final Clan						_clan;
	private final SubPledge					_pledge;
	private final String					_name;
	private final String					_leaderName;
	private final Collection<ClanMember>	_members;
	private final int						_pledgeId;
	private final boolean					_isSubPledge;
	
	private PledgeShowMemberListAll(Clan clan, SubPledge pledge, boolean isSubPledge)
	{
		_clan = clan;
		_pledge = pledge;
		_pledgeId = _pledge == null ? 0x00 : _pledge.getId();
		_leaderName = pledge == null ? clan.getLeaderName() : CharNameTable.getInstance().getNameById(pledge.getLeaderId());
		_name = pledge == null ? clan.getName() : pledge.getName();
		_members = _clan.getMembers();
		_isSubPledge = isSubPledge;
	}
	
	public static void sendAllTo(PlayerInstance player)
	{
		final Clan clan = player.getClan();
		if (clan != null)
		{
			for (SubPledge subPledge : clan.getAllSubPledges())
			{
				player.sendPacket(new PledgeShowMemberListAll(clan, subPledge, false));
			}
			player.sendPacket(new PledgeShowMemberListAll(clan, null, true));
		}
	}
	
	@Override
	public void writeImpl(GameClient client, WritableBuffer buffer)
	{
		writeId(ServerPacketId.PLEDGE_SHOW_MEMBER_LIST_ALL, buffer);
		buffer.writeInt(_isSubPledge ? 0x00 : 0x01);
		buffer.writeInt(_clan.getId());
		buffer.writeInt(Config.SERVER_ID);
		buffer.writeInt(_pledgeId);
		buffer.writeString(_name);
		buffer.writeString(_leaderName);
		buffer.writeInt(_clan.getCrestId()); // crest id .. is used again
		buffer.writeInt(_clan.getLevel());
		buffer.writeInt(_clan.getCastleId());
		buffer.writeInt(0x00);
		buffer.writeInt(_clan.getHideoutId());
		buffer.writeInt(_clan.getFortId());
		buffer.writeInt(_clan.getRank());
		buffer.writeInt(0x00); // clan rep
		buffer.writeInt(0x00); // 0
		buffer.writeInt(0x00); // 0
		buffer.writeInt(_clan.getAllyId());
		buffer.writeString(_clan.getAllyName());
		buffer.writeInt(_clan.getAllyCrestId());
		buffer.writeInt(_clan.isAtWar() ? 1 : 0); // new c3
		buffer.writeInt(0x00); // Territory castle ID
		buffer.writeInt(_clan.getSubPledgeMembersCount(_pledgeId));
		for (ClanMember m : _members)
		{
			if (m.getPledgeType() != _pledgeId)
			{
				continue;
			}
			buffer.writeString(m.getName());
			buffer.writeInt(m.getLevel());
			buffer.writeInt(m.getClassId());
			final PlayerInstance player = m.getPlayerInstance();
			if (player != null)
			{
				buffer.writeInt(player.getAppearance().isFemale() ? 1 : 0); // no visible effect
				buffer.writeInt(player.getRace().ordinal()); // buffer.writeInt(1);
			}
			else
			{
				buffer.writeInt(0x01); // no visible effect
				buffer.writeInt(0x01); // buffer.writeInt(1);
			}
			buffer.writeInt(m.isOnline() ? m.getObjectId() : 0); // objectId = online 0 = offline
			buffer.writeInt(m.getSponsor() != 0 ? 1 : 0);
			buffer.writeByte(m.getOnlineStatus());
		}
	}
}
