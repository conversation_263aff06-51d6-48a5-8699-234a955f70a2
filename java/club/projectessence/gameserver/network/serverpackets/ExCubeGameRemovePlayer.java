/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.network.serverpackets;

import club.projectessence.commons.network.ServerPacketId;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.network.GameClient;
import io.github.joealisson.mmocore.WritableBuffer;

/**
 * <AUTHOR>
 */
public class ExCubeGameRemovePlayer extends ServerPacket {
	PlayerInstance _player;
	boolean _isRedTeam;

	/**
	 * Remove Player from Minigame Waiting List
	 *
	 * @param player    Player to Remove
	 * @param isRedTeam Is Player from Red Team?
	 */
	public ExCubeGameRemovePlayer(PlayerInstance player, boolean isRedTeam) {
		_player = player;
		_isRedTeam = isRedTeam;
	}

	@Override
	public void writeImpl(GameClient client, WritableBuffer buffer) {
		writeId(ServerPacketId.EX_BLOCK_UP_SET_LIST, buffer);

		buffer.writeInt(0x02);

		buffer.writeInt(0xffffffff);

		buffer.writeInt(_isRedTeam ? 0x01 : 0x00);
		buffer.writeInt(_player.getObjectId());
	}
}
