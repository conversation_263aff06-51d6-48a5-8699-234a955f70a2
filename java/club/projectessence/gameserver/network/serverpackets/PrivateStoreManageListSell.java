/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.network.serverpackets;

import club.projectessence.commons.network.ServerPacketId;
import club.projectessence.gameserver.model.TradeItem;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.model.items.instance.ItemInstance;
import club.projectessence.gameserver.network.GameClient;
import io.github.joealisson.mmocore.WritableBuffer;

import java.util.Collection;
import java.util.Iterator;
import java.util.Set;

public class PrivateStoreManageListSell extends AbstractItemPacket {
	private final int _sendType;
	private final int _objId;
	private final long _playerAdena;
	private final boolean _packageSale;
	private final Collection<TradeItem> _itemList;
	private final Set<TradeItem> _sellList;

	public PrivateStoreManageListSell(int sendType, PlayerInstance player, boolean isPackageSale) {
		_sendType = sendType;
		_objId = player.getObjectId();
		_playerAdena = player.getAdena();
		_packageSale = isPackageSale;
		_itemList = player.getInventory().getAvailableItems(player.getSellList());
		_sellList = player.getSellList().getItems();

		final Iterator<TradeItem> iterator = _sellList.iterator();
		while (iterator.hasNext()) {
			final TradeItem tradeItem = iterator.next();
			if (tradeItem.getCount() <= 0) {
				iterator.remove();
				continue;
			}
			final ItemInstance item = player.getInventory().getItemByObjectId(tradeItem.getObjectId());
			if ((item == null) || (item.getCount() <= 0)) {
				iterator.remove();
				continue;
			}
			tradeItem.setCount(Math.min(item.getCount(), tradeItem.getCount()));
		}

		final Iterator<TradeItem> iterator2 = _itemList.iterator();
		IT2:
		while (iterator2.hasNext()) {
			final TradeItem tradeItem = iterator2.next();
			if (!tradeItem.getItem().isStackable()) {
				for (TradeItem sellItem : _sellList) {
					if (sellItem.getObjectId() == tradeItem.getObjectId()) {
						iterator2.remove();
						continue IT2;
					}
				}
			}
		}
	}

	@Override
	public void writeImpl(GameClient client, WritableBuffer buffer) {
		writeId(ServerPacketId.PRIVATE_STORE_MANAGE_LIST, buffer);
		buffer.writeByte(_sendType);
		if (_sendType == 1) {
			buffer.writeInt(_objId);
			buffer.writeInt(_packageSale ? 1 : 0);
			buffer.writeLong(_playerAdena);
			buffer.writeInt(_sellList.size());
			for (TradeItem item2 : _sellList) {
				writeItem(item2, buffer);
				buffer.writeLong(item2.getPrice());
				buffer.writeLong(item2.getItem().getReferencePrice() * 2);
			}
			buffer.writeInt(_itemList.size());
		} else if (_sendType == 2) {
			buffer.writeInt(_itemList.size());
			buffer.writeInt(_itemList.size());
			for (TradeItem item : _itemList) {
				writeItem(item, buffer);
				buffer.writeLong(item.getItem().getReferencePrice() * 2);
			}
		}
	}
}
