/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.network.serverpackets;

import club.projectessence.commons.network.ServerPacketId;
import club.projectessence.gameserver.model.skills.BuffInfo;
import club.projectessence.gameserver.model.skills.CommonSkill;
import club.projectessence.gameserver.network.GameClient;
import io.github.joealisson.mmocore.WritableBuffer;

import java.util.ArrayList;
import java.util.List;

public class AbnormalStatusUpdate extends ServerPacket {
	private static final int BAIUM_TALISMAN = 35020;
	private final List<BuffInfo> _effects = new ArrayList<>();
	private BuffInfo _baiumTalisman = null;

	public void addSkill(BuffInfo info) {
		if (!info.getSkill().isHealingPotionSkill()) {
			_effects.add(info);
		}
		if (info.getSkill().getId() == BAIUM_TALISMAN) {
			_baiumTalisman = info;
		}
	}

	@Override
	public void writeImpl(GameClient client, WritableBuffer buffer) {
		writeId(ServerPacketId.ABNORMAL_STATUS_UPDATE, buffer);

		final List<BuffInfo> sortedEffects = new ArrayList<>();
		final List<BuffInfo> toggles = new ArrayList<>();

		if (_baiumTalisman != null) {
			sortedEffects.add(_baiumTalisman);
		}

		for (BuffInfo info : _effects) {
			if (info.getSkill().getId() == BAIUM_TALISMAN) {
				continue;
			}
			if (info.getSkill().isToggle()) {
				toggles.add(info);
			} else {
				sortedEffects.add(info);
			}
		}
		for (BuffInfo info : toggles) {
			sortedEffects.add(info);
		}

		buffer.writeShort(sortedEffects.size() + (_baiumTalisman != null ? 0x01 : 0x00));
		for (BuffInfo info : sortedEffects) {
			if ((info != null) && info.isInUse()) {
				final int id = info.getSkill().getDisplayId();
				buffer.writeInt(CommonSkill.VISUAL_DEBUFF_ID_TEMPFIX.getOrDefault(id, id));
				buffer.writeShort(info.getSkill().getDisplayLevel());
				// buffer.writeShort(info.getSkill().getSubLevel());
				buffer.writeInt(info.getSkill().getAbnormalType().getClientId());
				writeOptionalD(info.getSkill().isAura() || info.getSkill().isToggle() ? -1 : info.getTime(), buffer);
			}
		}
	}
}
