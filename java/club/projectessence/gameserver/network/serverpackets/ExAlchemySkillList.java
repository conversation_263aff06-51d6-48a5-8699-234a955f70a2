/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.network.serverpackets;

import club.projectessence.commons.network.ServerPacketId;
import club.projectessence.gameserver.data.xml.SkillData;
import club.projectessence.gameserver.data.xml.SkillTreeData;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.model.skills.CommonSkill;
import club.projectessence.gameserver.model.skills.Skill;
import club.projectessence.gameserver.network.GameClient;
import io.github.joealisson.mmocore.WritableBuffer;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
public class ExAlchemySkillList extends ServerPacket {
	private final List<Skill> _skills = new ArrayList<>();

	public ExAlchemySkillList(PlayerInstance player) {
		for (Skill s : player.getAllSkills()) {
			if (SkillTreeData.getInstance().isAlchemySkill(s.getId(), s.getLevel())) {
				_skills.add(s);
			}
		}
		_skills.add(SkillData.getInstance().getSkill(CommonSkill.ALCHEMY_CUBE.getId(), 1));
	}

	@Override
	public void writeImpl(GameClient client, WritableBuffer buffer) {
		writeId(ServerPacketId.EX_ALCHEMY_SKILL_LIST, buffer);

		buffer.writeInt(_skills.size());
		for (Skill skill : _skills) {
			buffer.writeInt(skill.getId());
			buffer.writeInt(skill.getLevel());
			buffer.writeLong(0x00); // Always 0 on Naia, SP i guess?
			buffer.writeByte(skill.getId() == CommonSkill.ALCHEMY_CUBE.getId() ? 0 : 1); // This is type in flash, visible or not
		}
	}
}
