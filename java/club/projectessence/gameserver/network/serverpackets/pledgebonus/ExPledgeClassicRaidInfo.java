/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.network.serverpackets.pledgebonus;

import club.projectessence.commons.network.ServerPacketId;
import club.projectessence.gameserver.instancemanager.GlobalVariables;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.model.clan.Clan;
import club.projectessence.gameserver.network.GameClient;
import club.projectessence.gameserver.network.serverpackets.ServerPacket;
import io.github.joealisson.mmocore.WritableBuffer;

/**
 * <AUTHOR>
 */
public class ExPledgeClassicRaidInfo extends ServerPacket {
	private final PlayerInstance _player;

	public ExPledgeClassicRaidInfo(PlayerInstance player) {
		_player = player;
	}

	@Override
	public void writeImpl(GameClient client, WritableBuffer buffer) {
		final Clan clan = _player.getClan();
		if (clan == null) {
			return;
		}
		writeId(ServerPacketId.EX_PLEDGE_CLASSIC_RAID_INFO, buffer);
		buffer.writeInt(GlobalVariables.getInstance().getInt("MA_C" + clan.getId(), 0)); // Done stage
		int size = 5;
		buffer.writeInt(size);
		for (int i = 1; i <= size; i++) {
			buffer.writeInt(1867); // Skill ID
			buffer.writeInt(i); // Skill Level
		}
	}
}