/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.network.serverpackets;

import club.projectessence.commons.network.ServerPacketId;
import club.projectessence.gameserver.data.xml.HennaData;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.model.henna.Henna;
import club.projectessence.gameserver.network.GameClient;
import io.github.joealisson.mmocore.WritableBuffer;

import java.util.List;

/**
 * <AUTHOR>
 */
public class HennaEquipList extends ServerPacket {
	private final PlayerInstance _player;
	private final List<Henna> _hennaEquipList;

	public HennaEquipList(PlayerInstance player) {
		_player = player;
		_hennaEquipList = HennaData.getInstance().getHennaList(player);
	}

	public HennaEquipList(PlayerInstance player, List<Henna> list) {
		_player = player;
		_hennaEquipList = list;
	}

	@Override
	public void writeImpl(GameClient client, WritableBuffer buffer) {
		writeId(ServerPacketId.HENNA_EQUIP_LIST, buffer);
		buffer.writeLong(_player.getAdena()); // activeChar current amount of Adena
		buffer.writeInt(3); // available equip slot
		buffer.writeInt(_hennaEquipList.size());

		for (Henna henna : _hennaEquipList) {
			// Player must have at least one dye in inventory
			// to be able to see the Henna that can be applied with it.
			if ((_player.getInventory().getItemByItemId(henna.getDyeItemId())) != null) {
				buffer.writeInt(henna.getDyeId()); // dye Id
				buffer.writeInt(henna.getDyeItemId()); // item Id of the dye
				buffer.writeLong(henna.getWearCount()); // amount of dyes required
				buffer.writeLong(henna.getWearFee()); // amount of Adena required
				buffer.writeInt(henna.isAllowedClass(_player) ? 0x01 : 0x00); // meet the requirement or not
				// buffer.writeInt(0x00); // Does not exist in Classic.
			}
		}
	}
}
