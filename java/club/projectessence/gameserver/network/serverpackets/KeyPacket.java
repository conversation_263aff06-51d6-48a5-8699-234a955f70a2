/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.network.serverpackets;

import club.projectessence.Config;
import club.projectessence.commons.network.ServerPacketId;
import club.projectessence.gameserver.network.GameClient;
import io.github.joealisson.mmocore.WritableBuffer;

public class KeyPacket extends ServerPacket {
	private final byte[] _key;
	private final int _result;
	// TODO[K] - Guard section start
	// private final StrixClientData clientData;
	// TODO[K] - Strix section end

	public KeyPacket(byte[] key, int result) {
		_key = key;
		_result = result;
		// TODO[K] - Guard section start
		// this.clientData = null;
		// TODO[K] - Strix section end
	}

	// TODO[K] - Guard section start
	// public KeyPacket(final byte[] key, final StrixClientData clientData)
	// {
	// this._key = key;
	// this.clientData = clientData;
	// }
	// TODO[K] - Strix section end

	@Override
	public void writeImpl(GameClient client, WritableBuffer buffer) {
		writeId(ServerPacketId.VERSION_CHECK, buffer);

		// TODO[K] - Guard section start
		// if (_key == null)
		// {
		// if (StrixPlatform.getInstance().isBackNotificationEnabled() && (clientData != null))
		// {
		// buffer.writeByte(clientData.getServerResponse().ordinal() + 1);
		// }
		// else
		// {
		// buffer.writeByte(0x00);
		// }
		// return;
		// }
		// TODO[K] - Strix section end

		buffer.writeByte(_result); // 0 - wrong protocol, 1 - protocol ok
		for (int i = 0; i < 8; i++) {
			buffer.writeByte(_key[i]); // key
		}
		buffer.writeInt(0x01);
		buffer.writeInt(client.getProxyServerId()); // server id
		buffer.writeByte(0x01);
		buffer.writeInt(0x00); // obfuscation key
		buffer.writeByte((Config.SERVER_LIST_TYPE & 0x400) == 0x400 ? 0x04 : 0x00); // isClassic
	}
}
