/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.network.serverpackets;

import club.projectessence.commons.network.ServerPacketId;
import club.projectessence.gameserver.enums.HtmlActionScope;
import club.projectessence.gameserver.network.GameClient;
import io.github.joealisson.mmocore.WritableBuffer;

/**
 * NpcQuestHtmlMessage server packet implementation.
 *
 * <AUTHOR>
 */
public class NpcQuestHtmlMessage extends AbstractHtmlPacket {
	private final int _questId;

	public NpcQuestHtmlMessage(int npcObjId, int questId) {
		super(npcObjId);
		_questId = questId;
	}

	@Override
	public void writeImpl(GameClient client, WritableBuffer buffer) {
		writeId(ServerPacketId.EX_NPC_QUEST_HTML_MESSAGE, buffer);

		buffer.writeInt(getNpcObjId());
		buffer.writeString(getHtml());
		buffer.writeInt(_questId);
	}

	@Override
	public HtmlActionScope getScope() {
		return HtmlActionScope.NPC_QUEST_HTML;
	}
}
