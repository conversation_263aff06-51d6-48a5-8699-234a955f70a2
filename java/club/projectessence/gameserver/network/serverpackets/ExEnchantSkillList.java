/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.network.serverpackets;

import club.projectessence.commons.network.ServerPacketId;
import club.projectessence.gameserver.enums.SkillEnchantType;
import club.projectessence.gameserver.model.skills.Skill;
import club.projectessence.gameserver.network.GameClient;
import io.github.joealisson.mmocore.WritableBuffer;

import java.util.LinkedList;
import java.util.List;

public class ExEnchantSkillList extends ServerPacket {
	private final SkillEnchantType _type;
	private final List<Skill> _skills = new LinkedList<>();

	public ExEnchantSkillList(SkillEnchantType type) {
		_type = type;
	}

	public void addSkill(Skill skill) {
		_skills.add(skill);
	}

	@Override
	public void writeImpl(GameClient client, WritableBuffer buffer) {
		writeId(ServerPacketId.EX_ENCHANT_SKILL_LIST, buffer);

		buffer.writeInt(_type.ordinal());
		buffer.writeInt(_skills.size());
		for (Skill skill : _skills) {
			buffer.writeInt(skill.getId());
			buffer.writeShort(skill.getLevel());
			buffer.writeShort(skill.getSubLevel());
		}
	}
}