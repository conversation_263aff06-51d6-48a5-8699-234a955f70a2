/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.network.serverpackets.randomcraft;

import club.projectessence.commons.network.ServerPacketId;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.model.holders.RandomCraftRewardItemHolder;
import club.projectessence.gameserver.network.GameClient;
import club.projectessence.gameserver.network.serverpackets.ServerPacket;
import io.github.joealisson.mmocore.WritableBuffer;

import java.util.List;

/**
 * <AUTHOR>
 */
public class ExCraftRandomInfo extends ServerPacket {
	private final PlayerInstance _player;

	public ExCraftRandomInfo(PlayerInstance player) {
		_player = player;
	}

	@Override
	public void writeImpl(GameClient client, WritableBuffer buffer) {
		writeId(ServerPacketId.EX_CRAFT_RANDOM_INFO, buffer);
		List<RandomCraftRewardItemHolder> rewards = _player.getRandomCraft().getRewards();
		int size = 5;
		buffer.writeInt(size); // size
		for (int i = 0; i < rewards.size(); i++) {
			RandomCraftRewardItemHolder holder = rewards.get(i);
			if ((holder != null) && (holder.getItemId() != 0)) {
				buffer.writeByte(holder.isLocked() ? 0x01 : 0x00); // Locked
				buffer.writeInt(holder.getLockLeft()); // Rolls it will stay locked
				buffer.writeInt(holder.getItemId()); // Item id
				buffer.writeLong(holder.getItemCount()); // Item count
			} else {
				buffer.writeByte(0x00);
				buffer.writeInt(0x00);
				buffer.writeInt(0x00);
				buffer.writeLong(0x00);
			}
			size--;
		}
		// Write missing
		for (int i = size; i > 0; i--) {
			buffer.writeByte(0x00);
			buffer.writeInt(0x00);
			buffer.writeInt(0x00);
			buffer.writeLong(0x00);
		}
	}
}
