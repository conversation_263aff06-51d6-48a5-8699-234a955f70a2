/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.network.serverpackets;

import club.projectessence.commons.network.ServerPacketId;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.model.items.instance.ItemInstance;
import club.projectessence.gameserver.model.variables.PlayerVariables;
import club.projectessence.gameserver.network.GameClient;
import io.github.joealisson.mmocore.WritableBuffer;

/**
 * <AUTHOR>
 */
public class ExItemAnnounce extends ServerPacket {
	private final String _playerName;
	private final int _itemId;
	private final int _enchantLevel;
	private final ItemAnnounceType _type;

	public ExItemAnnounce(PlayerInstance player, ItemInstance item, ItemAnnounceType type) {
		_playerName = player.getVariables().getBoolean(PlayerVariables.ITEM_ANNOUNCE_HIDE_NAME, false) ? "Somebody" : player.getName();
		_itemId = item.getId();
		_enchantLevel = item.getEnchantLevel();
		_type = type;
	}

	public ExItemAnnounce(PlayerInstance player, int itemId, int enchantLevel, ItemAnnounceType type) {
		_playerName = player.getVariables().getBoolean(PlayerVariables.ITEM_ANNOUNCE_HIDE_NAME, false) ? "Somebody" : player.getName();
		_itemId = itemId;
		_enchantLevel = enchantLevel;
		_type = type;
	}

	@Override
	public void writeImpl(GameClient client, WritableBuffer buffer) {
		writeId(ServerPacketId.EX_ITEM_ANNOUNCE, buffer);
		buffer.writeByte(_type.ordinal());
		buffer.writeSizedString(_playerName);
		buffer.writeInt(_itemId);
		if (_type == ItemAnnounceType.LOOT_BOX) {
			buffer.writeByte(0x00);
		}
		buffer.writeInt(_enchantLevel); // loot box id
		buffer.writeByte(0x00); // ?
	}

	public static enum ItemAnnounceType {
		ENCHANT_SUCCESS,
		LOOT_BOX,
		RANDOM_CRAFT,
		SPECIAL_CRAFT,
		WORKSHOP,
		EVENT_PARTICIPATE,
		NONE_06,
		LIMITED_SPECIAL_CRAFT;
	}
}