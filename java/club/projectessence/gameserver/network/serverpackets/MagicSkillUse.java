/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.network.serverpackets;

import club.projectessence.commons.network.ServerPacketId;
import club.projectessence.gameserver.model.Location;
import club.projectessence.gameserver.model.WorldObject;
import club.projectessence.gameserver.model.actor.Creature;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.model.interfaces.IPositionable;
import club.projectessence.gameserver.model.skills.SkillCastingType;
import club.projectessence.gameserver.network.GameClient;
import io.github.joealisson.mmocore.WritableBuffer;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * MagicSkillUse server packet implementation.
 *
 * <AUTHOR> NosBit
 */
public class MagicSkillUse extends ServerPacket {
	private final int _skillId;
	private final int _skillLevel;
	private final int _hitTime;
	private final int _reuseGroup;
	private final int _reuseDelay;
	private final int _actionId; // If skill is called from RequestActionUse, use that ID.
	private final SkillCastingType _castingType; // Defines which client bar is going to use.
	private final boolean _isChargeSkill;
	private final Creature _creature;
	private final WorldObject _target;
	private final List<Location> _groundLocations;

	public MagicSkillUse(Creature creature, WorldObject target, int skillId, int skillLevel, int hitTime, int reuseDelay, int reuseGroup, int actionId, SkillCastingType castingType, boolean isChargeSkill) {
		_creature = creature;
		_target = target;
		_skillId = skillId;
		_skillLevel = skillLevel;
		_hitTime = hitTime;
		_reuseGroup = reuseGroup;
		_reuseDelay = reuseDelay;
		_actionId = actionId;
		_castingType = castingType;
		_isChargeSkill = isChargeSkill;
		Location skillWorldPos = null;
		if (creature.isPlayer()) {
			final PlayerInstance player = creature.getActingPlayer();
			if (player.getCurrentSkillWorldPosition() != null) {
				skillWorldPos = player.getCurrentSkillWorldPosition();
			}
		}
		_groundLocations = skillWorldPos != null ? Arrays.asList(skillWorldPos) : Collections.emptyList();
	}

	public MagicSkillUse(Creature creature, WorldObject target, int skillId, int skillLevel, int hitTime, int reuseDelay) {
		this(creature, target, skillId, skillLevel, hitTime, reuseDelay, -1, -1, SkillCastingType.NORMAL, false);
	}

	public MagicSkillUse(Creature creature, int skillId, int skillLevel, int hitTime, int reuseDelay) {
		this(creature, creature, skillId, skillLevel, hitTime, reuseDelay, -1, -1, SkillCastingType.NORMAL, false);
	}

	@Override
	public void writeImpl(GameClient client, WritableBuffer buffer) {
		writeId(ServerPacketId.MAGIC_SKILL_USE, buffer);

		buffer.writeInt(_castingType.getClientBarId()); // Casting bar type: 0 - default, 1 - default up, 2 - blue, 3 - green, 4 - red.
		buffer.writeInt(_creature.getObjectId());
		buffer.writeInt(_target.getObjectId());
		buffer.writeInt(_skillId);
		buffer.writeInt(_skillLevel);
		buffer.writeInt(_hitTime);
		buffer.writeInt(_reuseGroup);
		buffer.writeInt(_reuseDelay);
		buffer.writeInt(_creature.getX());
		buffer.writeInt(_creature.getY());
		buffer.writeInt(_creature.getZ());
		buffer.writeInt(_isChargeSkill ? 0x10000 : 0x00); // Benetis: Korean magic
		if (_groundLocations.size() > 0) {
			IPositionable target = _groundLocations.get(0);
			buffer.writeInt(target.getX());
			buffer.writeInt(target.getY());
			buffer.writeInt(target.getZ());
		} else {
			buffer.writeInt(_target.getX());
			buffer.writeInt(_target.getY());
			buffer.writeInt(_target.getZ());
		}
		buffer.writeInt(_actionId >= 0 ? 0x01 : 0x00); // 1 when ID from RequestActionUse is used
		buffer.writeInt(_actionId >= 0 ? _actionId : 0); // ID from RequestActionUse. Used to set cooldown on summon skills.
		if (_groundLocations.isEmpty()) {
			buffer.writeInt(-1);
		}
	}
}
