/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.network.serverpackets.specialhuntingzones;

import club.projectessence.commons.network.ServerPacketId;
import club.projectessence.gameserver.data.xml.SpecialHuntingZoneData;
import club.projectessence.gameserver.enums.SpecialHuntingZoneType;
import club.projectessence.gameserver.instancemanager.InstanceManager;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.model.holders.ItemHolder;
import club.projectessence.gameserver.model.holders.SpecialHuntingZoneHolder;
import club.projectessence.gameserver.model.instancezone.Instance;
import club.projectessence.gameserver.model.variables.PlayerVariables;
import club.projectessence.gameserver.network.GameClient;
import club.projectessence.gameserver.network.serverpackets.ServerPacket;
import io.github.joealisson.mmocore.WritableBuffer;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 */
public class ExTimeRestrictFieldList extends ServerPacket {
	private final PlayerInstance _player;

	public ExTimeRestrictFieldList(PlayerInstance player) {
		_player = player;
	}

	@Override
	public void writeImpl(GameClient client, WritableBuffer buffer) {
		writeId(ServerPacketId.EX_TIME_RESTRICT_FIELD_LIST, buffer);

		Collection<SpecialHuntingZoneHolder> zones = SpecialHuntingZoneData.getInstance().getSpecialHuntingZones();
		buffer.writeInt(zones.size()); // zone count
		for (SpecialHuntingZoneHolder zone : zones) {
			List<ItemHolder> fees = zone.getFees();
			buffer.writeInt(fees.size());
			for (ItemHolder fee : fees) {
				buffer.writeInt(fee.getId());
				if (((zone.getType() == SpecialHuntingZoneType.TRANSCENDENT) && _player.getVariables().getBoolean(PlayerVariables.TRANSCENDENT_ZONE_USED, false)) //
						|| ((zone.getType() == SpecialHuntingZoneType.TRAINING) && _player.getVariables().getBoolean(PlayerVariables.TRAINING_ZONE_USED, false))) {
					buffer.writeLong(0);
				} else {
					buffer.writeLong(fee.getCount());
				}
			}
			final boolean zeroes = zone.getType() != SpecialHuntingZoneType.PUBLIC;
			buffer.writeInt(zone.getResetType().getClientId());
			buffer.writeInt(zone.getId());
			buffer.writeInt(zone.getMinLevel());
			buffer.writeInt(zone.getMaxLevel());
			buffer.writeInt(zeroes ? 0 : zone.getDefaultTime());
			long remainingTime = zeroes ? 0 : _player.getVariables().getLong("SPECIAL_HUNTING_ZONE_REMAINING_TIME_" + zone.getId(), zone.getDefaultTime() * 1000);
			remainingTime = remainingTime == -1 ? zone.getDefaultTime() * 1000 : remainingTime;
			buffer.writeInt(zeroes ? 0 : (int) (remainingTime / 1000));
			buffer.writeInt(zeroes ? 0 : zone.getMaxTime());
			long usedExtensionTime = zeroes ? 0 : _player.getVariables().getLong("SPECIAL_HUNTING_ZONE_EXTENDED_TIME_" + zone.getId(), 0);
			usedExtensionTime = usedExtensionTime == -1 ? 0 : usedExtensionTime;
			buffer.writeInt(zeroes ? 0 : zone.getExtensionTime() - (int) (usedExtensionTime / 1000)); // extension time remaining
			buffer.writeInt(zeroes ? 0 : zone.getExtensionTime());
			buffer.writeByte(zone.isEnabled() && isFieldActivated(zone, _player) ? 0x01 : 0x00);
			Instance instance = InstanceManager.getInstance().getPlayerInstance(_player, false);
			if (zone.getType() == SpecialHuntingZoneType.TRAINING) {
				final boolean alreadyInside = (instance != null) && (instance.getTemplateId() == 228);
				buffer.writeByte(_player.getVariables().getBoolean(PlayerVariables.TRAINING_ZONE_USED, false) && !alreadyInside ? 0x01 : 0x00);
			} else {
				final boolean alreadyInside = (instance != null) && (((instance.getTemplateId() >= 208) && (instance.getTemplateId() <= 213)) || instance.getTemplateId() == 229);
				buffer.writeByte(_player.getVariables().getBoolean(PlayerVariables.TRANSCENDENT_ZONE_USED, false) && !alreadyInside ? 0x01 : 0x00);
			}
			buffer.writeByte(0x00); // bCanReEnter;
			buffer.writeByte(0x00); // bIsInZonePCCafeUserOnly;
			buffer.writeByte(0x00); // bIsPCCafeUser;
			buffer.writeByte(zone.isWorld()); // bWorldInZone;

			// 388
			switch (zone.getType()) {
				case TRANSCENDENT -> {
					buffer.writeByte(!_player.getVariables().getBoolean(PlayerVariables.TRANSCENDENT_ZONE_RESETTED, false)); // bCanUseEntranceTicket
					final boolean alreadyInside = (instance != null) && (((instance.getTemplateId() >= 208) && (instance.getTemplateId() <= 213)) || instance.getTemplateId() == 229);
					int count = !_player.getVariables().getBoolean(PlayerVariables.TRANSCENDENT_ZONE_USED, false) ? 1 : 0;
					buffer.writeInt(count + (alreadyInside ? 1 : 0)); // nEntranceCount;
				}
				case TRAINING -> {
					buffer.writeByte(!_player.getVariables().getBoolean(PlayerVariables.TRAINING_ZONE_RESETTED, false)); // bCanUseEntranceTicket
					final boolean alreadyInside = (instance != null) && (instance.getTemplateId() == 228);
					int count = !_player.getVariables().getBoolean(PlayerVariables.TRAINING_ZONE_USED, false) ? 1 : 0;
					buffer.writeInt(count + (alreadyInside ? 1 : 0)); // nEntranceCount;
				}
				default -> {
					buffer.writeByte(0x00); // bCanUseEntranceTicket
					buffer.writeInt(0x00); // nEntranceCount;
				}
			}
		}
	}

	private boolean isFieldActivated(SpecialHuntingZoneHolder zone, PlayerInstance player) {
		return zone.checkTimeLimit(player, false) && !_player.isInSpecialHuntingZone(zone.getId());
	}
}