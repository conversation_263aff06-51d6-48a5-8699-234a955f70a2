/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.network.serverpackets.specialhuntingzones;

import club.projectessence.commons.network.ServerPacketId;
import club.projectessence.gameserver.network.GameClient;
import club.projectessence.gameserver.network.serverpackets.ServerPacket;
import io.github.joealisson.mmocore.WritableBuffer;

/**
 * <AUTHOR>
 */
public class ExTimeRestrictFieldUserEnter extends ServerPacket {
	private final int _zoneId;
	private final long _remainingTime;

	public ExTimeRestrictFieldUserEnter(int zoneId, long remainingTime) {
		_zoneId = zoneId;
		_remainingTime = remainingTime;
	}

	@Override
	public void writeImpl(GameClient client, WritableBuffer buffer) {
		// S: FE 2A02 01 01000000 0CC69A5F 201C0000
		writeId(ServerPacketId.EX_TIME_RESTRICT_FIELD_USER_ENTER, buffer);
		buffer.writeByte(1); // unk
		buffer.writeInt(_zoneId); // Zone Id?
		buffer.writeInt((int) (System.currentTimeMillis() / 1000));
		buffer.writeInt((int) (_remainingTime / 1000));
	}
}