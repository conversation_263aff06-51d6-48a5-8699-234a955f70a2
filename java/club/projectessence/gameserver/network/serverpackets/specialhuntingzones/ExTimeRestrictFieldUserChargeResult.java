/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.network.serverpackets.specialhuntingzones;

import club.projectessence.commons.network.ServerPacketId;
import club.projectessence.gameserver.network.GameClient;
import club.projectessence.gameserver.network.serverpackets.ServerPacket;
import io.github.joealisson.mmocore.WritableBuffer;

/**
 * <AUTHOR>
 */
public class ExTimeRestrictFieldUserChargeResult extends ServerPacket {
	private final int _zoneId;
	private final int _remainingTime;
	private final int _remainingExtension;
	private final int _addedTime;

	public ExTimeRestrictFieldUserChargeResult(int zoneId, long remainingTime, long remainingExtension, long addedTime) {
		_zoneId = zoneId;
		_remainingTime = (int) (remainingTime / 1000);
		_remainingExtension = (int) (remainingExtension / 1000);
		_addedTime = (int) (addedTime / 1000);
	}

	@Override
	public void writeImpl(GameClient client, WritableBuffer buffer) {
		writeId(ServerPacketId.EX_TIME_RESTRICT_FIELD_USER_CHARGE_RESULT, buffer);
		buffer.writeInt(_zoneId); // nFieldID
		buffer.writeInt(_remainingTime); // nResultRemainTime
		buffer.writeInt(_remainingExtension); // nResultRefillTime
		buffer.writeInt(_addedTime); // nResultChargeTime
	}
}