/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.network.serverpackets;

import club.projectessence.commons.network.ServerPacketId;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.network.GameClient;
import io.github.joealisson.mmocore.WritableBuffer;

/**
 * <AUTHOR>
 */
public class ExCubeGameExtendedChangePoints extends ServerPacket {
	int _timeLeft;
	int _bluePoints;
	int _redPoints;
	boolean _isRedTeam;
	PlayerInstance _player;
	int _playerPoints;

	/**
	 * Update a Secret Point Counter (used by client when receive ExCubeGameEnd)
	 *
	 * @param timeLeft     Time Left before Minigame's End
	 * @param bluePoints   Current Blue Team Points
	 * @param redPoints    Current Blue Team points
	 * @param isRedTeam    Is Player from Red Team?
	 * @param player       Player Instance
	 * @param playerPoints Current Player Points
	 */
	public ExCubeGameExtendedChangePoints(int timeLeft, int bluePoints, int redPoints, boolean isRedTeam, PlayerInstance player, int playerPoints) {
		_timeLeft = timeLeft;
		_bluePoints = bluePoints;
		_redPoints = redPoints;
		_isRedTeam = isRedTeam;
		_player = player;
		_playerPoints = playerPoints;
	}

	@Override
	public void writeImpl(GameClient client, WritableBuffer buffer) {
		writeId(ServerPacketId.EX_BLOCK_UP_SET_STATE, buffer);

		buffer.writeInt(0x00);

		buffer.writeInt(_timeLeft);
		buffer.writeInt(_bluePoints);
		buffer.writeInt(_redPoints);

		buffer.writeInt(_isRedTeam ? 0x01 : 0x00);
		buffer.writeInt(_player.getObjectId());
		buffer.writeInt(_playerPoints);
	}
}
