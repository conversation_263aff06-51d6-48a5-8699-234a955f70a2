/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.network.serverpackets.enchantingnew.challengepoint;

import club.projectessence.commons.network.ServerPacketId;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.network.GameClient;
import club.projectessence.gameserver.network.serverpackets.ServerPacket;
import io.github.joealisson.mmocore.WritableBuffer;

import java.util.Map;
import java.util.Map.Entry;

/**
 * <AUTHOR>
 */
public class ExEnchantChallengePointInfo extends ServerPacket {
	private final EnchantChallengePointInfo _info[];

	public ExEnchantChallengePointInfo(PlayerInstance player) {
		final Map<Integer, Integer> challengePoints = player.getChallengePoints();
		_info = new EnchantChallengePointInfo[challengePoints.size()];
		int i = 0;
		for (Entry<Integer, Integer> entry : challengePoints.entrySet()) {
			final int groupId = entry.getKey();
			_info[i] = new EnchantChallengePointInfo(groupId, entry.getValue(), //
					player.getChallengePointsRecharges(groupId, 0), //
					player.getChallengePointsRecharges(groupId, 1), //
					player.getChallengePointsRecharges(groupId, 2), //
					player.getChallengePointsRecharges(groupId, 3), //
					player.getChallengePointsRecharges(groupId, 4), //
					player.getChallengePointsRecharges(groupId, 5));
			i++;
		}
	}

	@Override
	public void writeImpl(GameClient client, WritableBuffer buffer) {
		writeId(ServerPacketId.EX_ENCHANT_CHALLENGE_POINT_INFO, buffer);
		buffer.writeInt(_info.length); // vCurrentPointInfo
		for (EnchantChallengePointInfo info : _info) {
			buffer.writeInt(info.pointGroupId); // nPointGroupId
			buffer.writeInt(info.challengePoint); // nChallengePoint
			buffer.writeInt(info.ticketPointOpt1); // nTicketPointOpt1
			buffer.writeInt(info.ticketPointOpt2); // nTicketPointOpt2
			buffer.writeInt(info.ticketPointOpt3); // nTicketPointOpt3
			buffer.writeInt(info.ticketPointOpt4); // nTicketPointOpt4
			buffer.writeInt(info.ticketPointOpt5); // nTicketPointOpt5
			buffer.writeInt(info.ticketPointOpt6); // nTicketPointOpt6
		}
	}

	public static class EnchantChallengePointInfo {
		private final int pointGroupId;
		private final int challengePoint;
		private final int ticketPointOpt1;
		private final int ticketPointOpt2;
		private final int ticketPointOpt3;
		private final int ticketPointOpt4;
		private final int ticketPointOpt5;
		private final int ticketPointOpt6;

		public EnchantChallengePointInfo(int pointGroupId, int challengePoint, int ticketPointOpt1, int ticketPointOpt2, int ticketPointOpt3, int ticketPointOpt4, int ticketPointOpt5, int ticketPointOpt6) {
			this.pointGroupId = pointGroupId;
			this.challengePoint = challengePoint;
			this.ticketPointOpt1 = ticketPointOpt1;
			this.ticketPointOpt2 = ticketPointOpt2;
			this.ticketPointOpt3 = ticketPointOpt3;
			this.ticketPointOpt4 = ticketPointOpt4;
			this.ticketPointOpt5 = ticketPointOpt5;
			this.ticketPointOpt6 = ticketPointOpt6;
		}
	}
}
