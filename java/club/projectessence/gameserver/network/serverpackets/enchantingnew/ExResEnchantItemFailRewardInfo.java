/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.network.serverpackets.enchantingnew;

import club.projectessence.commons.network.ServerPacketId;
import club.projectessence.gameserver.data.xml.EnchantChallengePointsData;
import club.projectessence.gameserver.data.xml.EnchantChallengePointsData.EnchantChallengePointsItemInfo;
import club.projectessence.gameserver.model.holders.ItemHolder;
import club.projectessence.gameserver.model.items.instance.ItemInstance;
import club.projectessence.gameserver.network.GameClient;
import club.projectessence.gameserver.network.serverpackets.ServerPacket;
import io.github.joealisson.mmocore.WritableBuffer;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
public class ExResEnchantItemFailRewardInfo extends ServerPacket {
	private final ItemInstance _item;
	private final List<ItemHolder> _failItems = new ArrayList<>();
	private final int _challengePointGroupId;
	private final int _challengePoints;

	public ExResEnchantItemFailRewardInfo(ItemInstance item) {
		_item = item;

		final int enhancementStonesCount = item.getEnhancementStoneCount();
		if (enhancementStonesCount > 0) {
			_failItems.add(new ItemHolder(item.getEnhancementStoneId(), enhancementStonesCount));
		}

		final EnchantChallengePointsItemInfo info = EnchantChallengePointsData.getInstance().getInfoByItemId(item.getId());
		_challengePointGroupId = info == null ? 0 : info.groupId();
		_challengePoints = info == null ? 0 : info.pointsByEnchantLevel().getOrDefault(item.getEnchantLevel(), 0);
	}

	@Override
	public void writeImpl(GameClient client, WritableBuffer buffer) {
		writeId(ServerPacketId.EX_RES_ENCHANT_ITEM_FAIL_REWARD_INFO, buffer);

		buffer.writeInt(_item.getObjectId()); // nItemServerId;
		buffer.writeInt(_challengePointGroupId); // nEnchantChallengePointGroupId;
		buffer.writeInt(_challengePoints); // nEnchantChallengePoint;

		buffer.writeInt(_failItems.size()); // vRewardItemList
		for (ItemHolder failItem : _failItems) {
			buffer.writeInt(failItem.getId()); // nItemClassID;
			buffer.writeInt((int) failItem.getCount()); // nCount;
		}
	}
}
