/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.network.serverpackets.enchantingnew;

import club.projectessence.commons.network.ServerPacketId;
import club.projectessence.gameserver.model.items.instance.ItemInstance;
import club.projectessence.gameserver.network.GameClient;
import club.projectessence.gameserver.network.serverpackets.ServerPacket;
import io.github.joealisson.mmocore.WritableBuffer;

import java.util.List;
import java.util.Map;
import java.util.Map.Entry;

/**
 * <AUTHOR>
 */
public class ExResMultiEnchantItemList extends ServerPacket {
	private final boolean _result;
	private final List<ItemInstance> _successItemList;
	private final List<ItemInstance> _failedItemList;
	private final Map<Integer, Integer> _failRewardItemList;
	private final Map<Integer, Integer> _failChallengePointInfoList;

	public ExResMultiEnchantItemList(boolean result, List<ItemInstance> successItemList, List<ItemInstance> failedItemList, Map<Integer, Integer> failRewardItemList, Map<Integer, Integer> failChallengePointInfoList) {
		_result = result;
		_successItemList = successItemList;
		_failedItemList = failedItemList;
		_failRewardItemList = failRewardItemList;
		_failChallengePointInfoList = failChallengePointInfoList;
	}

	@Override
	public void writeImpl(GameClient client, WritableBuffer buffer) {
		writeId(ServerPacketId.EX_RES_MULTI_ENCHANT_ITEM_LIST, buffer);
		buffer.writeByte(_result); // bResult

		if (!_result) {
			buffer.writeInt(0x00);
			buffer.writeInt(0x00);
			buffer.writeInt(0x00);
			buffer.writeInt(0x00);
			return;
		}

		buffer.writeInt(_successItemList.size()); // vSuccessItemList;
		for (ItemInstance item : _successItemList) {
			buffer.writeInt(item.getObjectId());
			buffer.writeInt(item.getEnchantLevel());
		}
		buffer.writeInt(_failedItemList.size()); // vFailedItemList;
		for (ItemInstance item : _failedItemList) {
			buffer.writeInt(item.getObjectId());
			buffer.writeInt(item.getEnchantLevel());
		}
		buffer.writeInt(_failRewardItemList.size()); // vFailRewardItemList;
		for (Entry<Integer, Integer> item : _failRewardItemList.entrySet()) {
			buffer.writeInt(item.getKey());
			buffer.writeInt(item.getValue());
		}
		buffer.writeInt(_failChallengePointInfoList.size()); // vFailChallengePointInfoList;
		for (Entry<Integer, Integer> item : _failChallengePointInfoList.entrySet()) {
			buffer.writeInt(item.getKey());
			buffer.writeInt(item.getValue());
		}
	}
}
