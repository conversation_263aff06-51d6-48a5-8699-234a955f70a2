/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.network.serverpackets;

import club.projectessence.commons.network.ServerPacketId;
import club.projectessence.gameserver.model.actor.Creature;
import club.projectessence.gameserver.model.holders.DamageTakenHolder;
import club.projectessence.gameserver.model.items.instance.ItemInstance;
import club.projectessence.gameserver.network.GameClient;
import io.github.joealisson.mmocore.WritableBuffer;

import java.util.Collection;

/**
 * <AUTHOR> Benetis
 */
public class ExDieInfo extends ServerPacket {
	private final Collection<ItemInstance> _droppedItems;
	private final Collection<DamageTakenHolder> _lastDamageTaken;

	public ExDieInfo(Collection<ItemInstance> droppedItems, Collection<DamageTakenHolder> lastDamageTaken) {
		_droppedItems = droppedItems;
		_lastDamageTaken = lastDamageTaken;
	}

	@Override
	public void writeImpl(GameClient client, WritableBuffer buffer) {
		writeId(ServerPacketId.EX_DIE_INFO, buffer);

		buffer.writeShort(_droppedItems.size());
		for (ItemInstance item : _droppedItems) {
			buffer.writeInt(item.getId());
			buffer.writeInt(item.getEnchantLevel());
			buffer.writeInt((int) item.getCount());
		}

		buffer.writeShort(_lastDamageTaken.size());
		for (DamageTakenHolder damageHolder : _lastDamageTaken) {
			final Creature creature = damageHolder.getCreature();
			if (creature.isPlayable()) {
				buffer.writeShort(2); // PVP
				buffer.writeString(creature.getName());
				buffer.writeString(creature.getClan() == null ? "" : creature.getClan().getName());
			} else {
				buffer.writeShort(1); // PVE
				buffer.writeInt(creature.getId());
				buffer.writeShort(0x00); // Clan name empty
			}
			buffer.writeInt(damageHolder.getSkillId());
			buffer.writeDouble(damageHolder.getDamage());
			buffer.writeShort(damageHolder.getDamageTakenType().getClientId());
		}
	}
}
