/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.network.serverpackets;

import club.projectessence.commons.network.ServerPacketId;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.model.henna.Henna;
import club.projectessence.gameserver.model.henna.HennaPoten;
import club.projectessence.gameserver.network.GameClient;
import io.github.joealisson.mmocore.WritableBuffer;

/**
 * <AUTHOR>
 */
public class HennaRemoveList extends ServerPacket {
	private final PlayerInstance _player;

	public HennaRemoveList(PlayerInstance player) {
		_player = player;
	}

	@Override
	public void writeImpl(GameClient client, WritableBuffer buffer) {
		writeId(ServerPacketId.HENNA_UNEQUIP_LIST, buffer);

		buffer.writeLong(_player.getAdena());
		buffer.writeInt(0x03); // seems to be max size
		buffer.writeInt(3 - _player.getHennaEmptySlots());

		for (HennaPoten hennaPoten : _player.getHennaPotenList()) {
			final Henna henna = hennaPoten.getHenna();
			if (henna != null) {
				buffer.writeInt(henna.getDyeId());
				buffer.writeInt(henna.getDyeItemId());
				buffer.writeLong(henna.getCancelCount());
				buffer.writeLong(henna.getCancelFee());
				buffer.writeInt(0x00);
			}
		}
	}
}
