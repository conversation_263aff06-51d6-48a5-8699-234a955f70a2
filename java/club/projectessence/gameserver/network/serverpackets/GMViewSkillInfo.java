/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.network.serverpackets;

import club.projectessence.commons.network.ServerPacketId;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.model.skills.Skill;
import club.projectessence.gameserver.network.GameClient;
import io.github.joealisson.mmocore.WritableBuffer;

import java.util.Collection;

public class GMViewSkillInfo extends ServerPacket {
	private final PlayerInstance _player;
	private final Collection<Skill> _skills;

	public GMViewSkillInfo(PlayerInstance player) {
		_player = player;
		_skills = _player.getSkillList();
	}

	@Override
	public void writeImpl(GameClient client, WritableBuffer buffer) {
		writeId(ServerPacketId.GM_VIEW_SKILL_INFO, buffer);

		buffer.writeString(_player.getName());
		buffer.writeInt(_skills.size());

		for (Skill skill : _skills) {
			buffer.writeInt(skill.isPassive() ? 1 : 0);
			buffer.writeShort(skill.getDisplayLevel());
			buffer.writeShort(skill.getSubLevel());
			buffer.writeInt(skill.getDisplayId());
			buffer.writeInt(0x00);
			buffer.writeByte(0x00); // isDisabled
			buffer.writeByte(skill.isEnchantable() ? 1 : 0);
		}
	}
}