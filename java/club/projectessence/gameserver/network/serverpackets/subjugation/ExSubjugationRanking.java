package club.projectessence.gameserver.network.serverpackets.subjugation;

import club.projectessence.commons.network.ServerPacketId;
import club.projectessence.gameserver.data.xml.PurgeData;
import club.projectessence.gameserver.model.Purge;
import club.projectessence.gameserver.model.StatSet;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.network.GameClient;
import club.projectessence.gameserver.network.serverpackets.ServerPacket;
import io.github.joealisson.mmocore.WritableBuffer;

public class ExSubjugationRanking extends ServerPacket
{
	private final PlayerInstance	_player;
	private final int				_id;
	
	public ExSubjugationRanking(PlayerInstance player, int id)
	{
		_player = player;
		_id = id;
	}
	
	@Override
	public void writeImpl(GameClient client, WritableBuffer buffer)
	{
		writeId(ServerPacketId.EX_SUBJUGATION_RANKING, buffer);
		Purge purge = PurgeData.getInstance().getPurge(_id);
		int size = Math.min(5, purge.getTop5().size());
		buffer.writeInt(size);
		int i = 0;
		for (StatSet player : purge.getTop5().values())
		{
			i++;
			buffer.writeSizedString(player.getString("name", "-----"));
			buffer.writeInt(player.getInt("points", 0));
			buffer.writeInt(i);
		}
		buffer.writeInt(_id);
		buffer.writeInt(purge.getPlayerPoints(_player.getObjectId()));
		buffer.writeInt(purge.getPlayerRank(_player.getObjectId()));
	}
}
