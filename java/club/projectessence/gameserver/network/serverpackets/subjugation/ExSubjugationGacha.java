package club.projectessence.gameserver.network.serverpackets.subjugation;

import club.projectessence.commons.network.ServerPacketId;
import club.projectessence.gameserver.network.GameClient;
import club.projectessence.gameserver.network.serverpackets.ServerPacket;
import io.github.joealisson.mmocore.WritableBuffer;

import java.util.Map;
import java.util.Map.Entry;

public class ExSubjugationGacha extends ServerPacket {
	private final Map<Integer, Integer> _obtainedItems;

	public ExSubjugationGacha(Map<Integer, Integer> obtainedItems) {
		_obtainedItems = obtainedItems;
	}

	@Override
	public void writeImpl(GameClient client, WritableBuffer buffer) {
		writeId(ServerPacketId.EX_SUBJUGATION_GACHA, buffer);
		buffer.writeInt(_obtainedItems.size());
		for (Entry<Integer, Integer> reward : _obtainedItems.entrySet()) {
			buffer.writeInt(reward.getKey());
			buffer.writeInt(reward.getValue());
		}
	}

}
