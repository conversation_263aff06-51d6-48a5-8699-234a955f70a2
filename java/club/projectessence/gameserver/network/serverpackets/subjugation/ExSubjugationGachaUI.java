package club.projectessence.gameserver.network.serverpackets.subjugation;

import club.projectessence.commons.network.ServerPacketId;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.model.variables.PlayerVariables;
import club.projectessence.gameserver.network.GameClient;
import club.projectessence.gameserver.network.serverpackets.ServerPacket;
import io.github.joealisson.mmocore.WritableBuffer;

public class ExSubjugationGachaUI extends ServerPacket {

	private final PlayerInstance _player;
	private final int _id;

	public ExSubjugationGachaUI(PlayerInstance player, int id) {
		_player = player;
		_id = id;
	}

	@Override
	public void writeImpl(GameClient client, WritableBuffer buffer) {
		writeId(ServerPacketId.EX_SUBJUGATION_GACHA_UI, buffer);
		buffer.writeInt(_player.getVariables().getInt("PURGE_DATA_CURRENT_KEYS_" + _id, 0));
	}

}
