package club.projectessence.gameserver.network.serverpackets.subjugation;

import java.util.Collection;

import club.projectessence.commons.network.ServerPacketId;
import club.projectessence.gameserver.data.xml.PurgeData;
import club.projectessence.gameserver.model.Purge;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.model.variables.PlayerVariables;
import club.projectessence.gameserver.network.GameClient;
import club.projectessence.gameserver.network.serverpackets.ServerPacket;
import io.github.joealisson.mmocore.WritableBuffer;

public class ExSubjugationList extends ServerPacket
{
	private final PlayerInstance _player;
	
	public ExSubjugationList(PlayerInstance player)
	{
		_player = player;
	}
	
	@Override
	public void writeImpl(GameClient client, WritableBuffer buffer)
	{
		writeId(ServerPacketId.EX_SUBJUGATION_LIST, buffer);
		Collection<Purge> purges = PurgeData.getInstance().getPurges();
		buffer.writeInt(purges.size()); // size
		for (Purge purge : purges)
		{
			buffer.writeInt(purge.getPurgeId()); // id
			buffer.writeInt(_player.getVariables().getInt("PURGE_DATA_POINTS_" + purge.getPurgeId(), 0) % purge.getMaxSubjugationPoint()); // current points
			buffer.writeInt(_player.getVariables().getInt("PURGE_DATA_CURRENT_KEYS_" + purge.getPurgeId(), 0)); // Gachapoint (keys)
			buffer.writeInt(purge.getMaxPeriodicGachaPoint() - _player.getVariables().getInt("PURGE_DATA_TOTAL_KEYS_" + purge.getPurgeId(), 0)); // RemainPeriodicGachaPoint (remaining keys)
		}
	}
}
