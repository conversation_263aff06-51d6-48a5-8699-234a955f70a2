package club.projectessence.gameserver.network.serverpackets.subjugation;

import club.projectessence.commons.network.ServerPacketId;
import club.projectessence.gameserver.network.GameClient;
import club.projectessence.gameserver.network.serverpackets.ServerPacket;
import io.github.joealisson.mmocore.WritableBuffer;

public class ExSubjugationSidebar extends ServerPacket {
	private final int _category;
	private final int _points;
	private final int _keys;

	public ExSubjugationSidebar(int category, int points, int keys) {
		_category = category;
		_points = points;
		_keys = keys;
	}

	@Override
	public void writeImpl(GameClient client, WritableBuffer buffer) {
		writeId(ServerPacketId.EX_SUBJUGATION_SIDEBAR, buffer);
		buffer.writeInt(_category);
		buffer.writeInt(_points % 1000000); // 1000000 = 100%
		buffer.writeInt(_keys);
	}
}
