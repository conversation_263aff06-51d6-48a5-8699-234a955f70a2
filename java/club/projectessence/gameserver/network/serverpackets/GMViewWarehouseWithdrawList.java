/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.network.serverpackets;

import club.projectessence.commons.network.ServerPacketId;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.model.clan.Clan;
import club.projectessence.gameserver.model.items.instance.ItemInstance;
import club.projectessence.gameserver.network.GameClient;
import io.github.joealisson.mmocore.WritableBuffer;

import java.util.Collection;

public class GMViewWarehouseWithdrawList extends AbstractItemPacket {
	private final int _sendType;
	private final Collection<ItemInstance> _items;
	private final String _playerName;
	private final long _money;

	public GMViewWarehouseWithdrawList(int sendType, PlayerInstance player) {
		_sendType = sendType;
		_items = player.getWarehouse().getItems();
		_playerName = player.getName();
		_money = player.getWarehouse().getAdena();
	}

	public GMViewWarehouseWithdrawList(int sendType, Clan clan) {
		_sendType = sendType;
		_playerName = clan.getLeaderName();
		_items = clan.getWarehouse().getItems();
		_money = clan.getWarehouse().getAdena();
	}

	@Override
	public void writeImpl(GameClient client, WritableBuffer buffer) {
		writeId(ServerPacketId.GM_VIEW_WAREHOUSE_WITHDRAW_LIST, buffer);

		buffer.writeByte(_sendType);
		if (_sendType == 2) {
			buffer.writeInt(_items.size());
			buffer.writeInt(_items.size());
			for (ItemInstance item : _items) {
				writeItem(item, buffer);
				buffer.writeInt(item.getObjectId());
			}
		} else {
			buffer.writeString(_playerName);
			buffer.writeLong(_money);
			buffer.writeInt(_items.size());
		}
	}
}
