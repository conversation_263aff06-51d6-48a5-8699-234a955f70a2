/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.network.serverpackets;

import club.projectessence.commons.network.ServerPacketId;
import club.projectessence.gameserver.model.Shortcut;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.network.GameClient;
import io.github.joealisson.mmocore.WritableBuffer;

public class ShortCutInit extends ServerPacket {
	private Shortcut[] _shortCuts;

	public ShortCutInit(PlayerInstance player) {
		if (player == null) {
			return;
		}

		_shortCuts = player.getAllShortCuts();
	}

	@Override
	public void writeImpl(GameClient client, WritableBuffer buffer) {
		writeId(ServerPacketId.SHORT_CUT_INIT, buffer);

		buffer.writeInt(_shortCuts.length);
		for (Shortcut sc : _shortCuts) {
			buffer.writeInt(sc.getType().ordinal());
			buffer.writeInt(sc.getSlot() + (sc.getPage() * 12));

			buffer.writeByte(0x00); // 228

			switch (sc.getType()) {
				case ITEM: {
					buffer.writeInt(sc.getId());
					buffer.writeInt(0x01); // Enabled or not
					buffer.writeInt(sc.getSharedReuseGroup());
					buffer.writeInt(0x00);
					buffer.writeInt(0x00);
					buffer.writeLong(0x00); // Augment id
					buffer.writeInt(0x00); // Visual id
					break;
				}
				case SKILL: {
					buffer.writeInt(sc.getId());
					buffer.writeShort(sc.getLevel());
					buffer.writeShort(sc.getSubLevel());
					buffer.writeInt(sc.getSharedReuseGroup());
					buffer.writeByte(0x00); // C5
					buffer.writeInt(0x01); // C6
					break;
				}
				case ACTION:
				case MACRO:
				case RECIPE:
				case BOOKMARK: {
					buffer.writeInt(sc.getId());
					buffer.writeInt(0x01); // C6
				}
			}
		}
	}
}
