/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.network.serverpackets;

import club.projectessence.commons.network.ServerPacketId;
import club.projectessence.gameserver.enums.ChatType;
import club.projectessence.gameserver.model.actor.Npc;
import club.projectessence.gameserver.network.GameClient;
import club.projectessence.gameserver.network.NpcStringId;
import club.projectessence.gameserver.network.NpcStringId.NSLocalisation;
import io.github.joealisson.mmocore.WritableBuffer;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 */
public class NpcSay extends ServerPacket {
	private final int _objectId;
	private final ChatType _textType;
	private final int _npcId;
	private final int _npcString;
	private String _text;
	private List<String> _parameters;
	private String _lang;

	/**
	 * @param objectId
	 * @param messageType
	 * @param npcId
	 * @param text
	 */
	public NpcSay(int objectId, ChatType messageType, int npcId, String text) {
		_objectId = objectId;
		_textType = messageType;
		_npcId = 1000000 + npcId;
		_npcString = -1;
		_text = text;
	}

	public NpcSay(Npc npc, ChatType messageType, String text) {
		_objectId = npc.getObjectId();
		_textType = messageType;
		_npcId = 1000000 + npc.getTemplate().getDisplayId();
		_npcString = -1;
		_text = text;
	}

	public NpcSay(int objectId, ChatType messageType, int npcId, NpcStringId npcString) {
		_objectId = objectId;
		_textType = messageType;
		_npcId = 1000000 + npcId;
		_npcString = npcString.getId();
	}

	public NpcSay(Npc npc, ChatType messageType, NpcStringId npcString) {
		_objectId = npc.getObjectId();
		_textType = messageType;
		_npcId = 1000000 + npc.getTemplate().getDisplayId();
		_npcString = npcString.getId();
	}

	/**
	 * @param text the text to add as a parameter for this packet's message (replaces S1, S2 etc.)
	 * @return this NpcSay packet object
	 */
	public NpcSay addStringParameter(String text) {
		if (_parameters == null) {
			_parameters = new ArrayList<>();
		}
		_parameters.add(text);
		return this;
	}

	/**
	 * @param params a list of strings to add as parameters for this packet's message (replaces S1, S2 etc.)
	 * @return this NpcSay packet object
	 */
	public NpcSay addStringParameters(String... params) {
		if ((params != null) && (params.length > 0)) {
			if (_parameters == null) {
				_parameters = new ArrayList<>();
			}

			for (String item : params) {
				if ((item != null) && (item.length() > 0)) {
					_parameters.add(item);
				}
			}
		}
		return this;
	}

	public void setLang(String lang) {
		_lang = lang;
	}

	@Override
	public void writeImpl(GameClient client, WritableBuffer buffer) {
		writeId(ServerPacketId.NPC_SAY, buffer);

		buffer.writeInt(_objectId);
		buffer.writeInt(_textType.getClientId());
		buffer.writeInt(_npcId);

		// Localisation related.
		if (_lang != null) {
			final NpcStringId ns = NpcStringId.getNpcStringId(_npcString);
			if (ns != null) {
				final NSLocalisation nsl = ns.getLocalisation(_lang);
				if (nsl != null) {
					buffer.writeInt(-1);
					buffer.writeString(nsl.getLocalisation(_parameters != null ? _parameters : Collections.emptyList()));
					return;
				}
			}
		}

		buffer.writeInt(_npcString);
		if (_npcString == -1) {
			buffer.writeString(_text);
		} else if (_parameters != null) {
			for (String s : _parameters) {
				buffer.writeString(s);
			}
		}
	}
}