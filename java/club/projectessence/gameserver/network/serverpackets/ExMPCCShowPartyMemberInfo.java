/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.network.serverpackets;

import club.projectessence.commons.network.ServerPacketId;
import club.projectessence.gameserver.model.CommandChannel;
import club.projectessence.gameserver.model.Party;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.network.GameClient;
import io.github.joealisson.mmocore.WritableBuffer;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class ExMPCCShowPartyMemberInfo extends ServerPacket {
	private final Party _party;
	private final Map<Integer, CommandChannel.CommandChannelPlayerKdInfo> _kdInfo;

	public ExMPCCShowPartyMemberInfo(Party party) {
		_party = party;
		final CommandChannel cc = party.getCommandChannel();
		_kdInfo = cc == null ? new HashMap<>() : cc.getPartyKDInfo(party);
	}

	@Override
	public void writeImpl(GameClient client, WritableBuffer buffer) {
		writeId(ServerPacketId.EX_MPCCSHOW_PARTY_MEMBER_INFO, buffer);

		buffer.writeInt(_party.getMemberCount());
		for (PlayerInstance pc : _party.getMembers()) {
			final CommandChannel.CommandChannelPlayerKdInfo kdInfo = _kdInfo.get(pc.getObjectId());
			if (kdInfo == null) {
				buffer.writeString("[Lv. " + pc.getLevel() + "] " + pc.getName() + ";0;0");
			} else {
				buffer.writeString("[Lv. " + pc.getLevel() + "] " + pc.getName() + ";" + kdInfo.getKills() + ";" + kdInfo.getDeaths());
			}
			buffer.writeInt(pc.getObjectId());
			buffer.writeInt(pc.getClassId().getId());
		}
	}
}