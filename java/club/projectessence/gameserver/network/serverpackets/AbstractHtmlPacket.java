/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.network.serverpackets;

import club.projectessence.gameserver.cache.HtmCache;
import club.projectessence.gameserver.enums.HtmlActionScope;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.util.Util;

import java.util.logging.Level;

/**
 * <AUTHOR>
 */
public abstract class AbstractHtmlPacket extends ServerPacket {
	public static final char VAR_PARAM_START_CHAR = '$';

	private final int _npcObjId;
	private String _html = null;
	private boolean _disabledValidation = false;

	protected AbstractHtmlPacket() {
		_npcObjId = 0;
	}

	protected AbstractHtmlPacket(int npcObjId) {
		if (npcObjId < 0) {
			throw new IllegalArgumentException();
		}

		_npcObjId = npcObjId;
	}

	protected AbstractHtmlPacket(String html) {
		_npcObjId = 0;
		setHtml(html);
	}

	protected AbstractHtmlPacket(int npcObjId, String html) {
		if (npcObjId < 0) {
			throw new IllegalArgumentException();
		}

		_npcObjId = npcObjId;
		setHtml(html);
	}

	public void disableValidation() {
		_disabledValidation = true;
	}

	public boolean setFile(PlayerInstance player, String path) {
		final String content = HtmCache.getInstance().getHtm(player, path);
		if (content == null) {
			setHtml("<html><body>My Text is missing:<br>" + path + "</body></html>");
			LOGGER.warning("missing html page " + path);
			return false;
		}

		setHtml(content);
		return true;
	}

	public void replace(String pattern, String value) {
		_html = _html.replaceAll(pattern, value.replaceAll("\\$", "\\\\\\$"));
	}

	public void replace(String pattern, CharSequence value) {
		replace(pattern, String.valueOf(value));
	}

	public void replace(String pattern, boolean value) {
		replace(pattern, String.valueOf(value));
	}

	public void replace(String pattern, int value) {
		replace(pattern, String.valueOf(value));
	}

	public void replace(String pattern, long value) {
		replace(pattern, String.valueOf(value));
	}

	public void replace(String pattern, double value) {
		replace(pattern, String.valueOf(value));
	}

	@Override
	public void runImpl(PlayerInstance player) {
		if (player != null) {
			player.clearHtmlActions(getScope());
		}

		if (_disabledValidation) {
			return;
		}

		if (player != null) {
			Util.buildHtmlActionCache(player, getScope(), _npcObjId, _html);
		}
	}

	public int getNpcObjId() {
		return _npcObjId;
	}

	public String getHtml() {
		return _html;
	}

	public void setHtml(String html) {
		if (html.length() > 17200) {
			LOGGER.log(Level.WARNING, "Html is too long! this will crash the client!", new Throwable());
			_html = html.substring(0, 17200);
		}

		if (!html.contains("<html") && !html.startsWith("..\\L2")) {
			_html = "<html><body>" + html + "</body></html>";
			return;
		}

		_html = html;
	}

	public abstract HtmlActionScope getScope();
}
