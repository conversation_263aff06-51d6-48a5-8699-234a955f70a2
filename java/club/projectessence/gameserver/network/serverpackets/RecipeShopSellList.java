/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.network.serverpackets;

import club.projectessence.commons.network.ServerPacketId;
import club.projectessence.gameserver.model.ManufactureItem;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.network.GameClient;
import io.github.joealisson.mmocore.WritableBuffer;

public class RecipeShopSellList extends ServerPacket {
	private final PlayerInstance _buyer;
	private final PlayerInstance _manufacturer;

	public RecipeShopSellList(PlayerInstance buyer, PlayerInstance manufacturer) {
		_buyer = buyer;
		_manufacturer = manufacturer;
	}

	@Override
	public void writeImpl(GameClient client, WritableBuffer buffer) {
		writeId(ServerPacketId.RECIPE_SHOP_SELL_LIST, buffer);

		buffer.writeInt(_manufacturer.getObjectId());
		buffer.writeInt((int) _manufacturer.getCurrentMp()); // Creator's MP
		buffer.writeInt(_manufacturer.getMaxMp()); // Creator's MP
		buffer.writeLong(_buyer.getAdena()); // Buyer Adena
		if (!_manufacturer.hasManufactureShop()) {
			buffer.writeInt(0x00);
		} else {
			buffer.writeInt(_manufacturer.getManufactureItems().size());
			for (ManufactureItem temp : _manufacturer.getManufactureItems().values()) {
				buffer.writeInt(temp.getRecipeId());
				buffer.writeInt(0x00); // unknown
				buffer.writeLong(temp.getCost());

				buffer.writeLong(0x00); // Classic - 166
				buffer.writeLong(0x00); // Classic - 166
				buffer.writeByte(0x00); // Classic - 166
			}
		}
	}
}
