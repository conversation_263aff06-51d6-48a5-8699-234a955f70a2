/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.network.serverpackets;

import club.projectessence.commons.network.ServerPacketId;
import club.projectessence.gameserver.network.GameClient;
import io.github.joealisson.mmocore.WritableBuffer;

import java.util.Map;
import java.util.Map.Entry;

/**
 * <AUTHOR>
 */
public class ExItemDeletionInfo extends ServerPacket {
	private final Map<Integer, Long> _items;
	private final Map<Integer, Long> _skills;

	public ExItemDeletionInfo(Map<Integer, Long> items, Map<Integer, Long> skills) {
		_items = items;
		_skills = skills;
	}

	@Override
	public void writeImpl(GameClient client, WritableBuffer buffer) {
		writeId(ServerPacketId.EX_ITEM_DELETION_INFO, buffer);

		buffer.writeInt(_items.size());
		for (Entry<Integer, Long> entry : _items.entrySet()) {
			buffer.writeInt(entry.getKey());
			buffer.writeInt((int) (entry.getValue() / 1000));
		}
		buffer.writeInt(_skills.size());
		for (Entry<Integer, Long> entry : _skills.entrySet()) {
			buffer.writeInt(entry.getKey());
			buffer.writeInt((int) (entry.getValue() / 1000));
		}
	}
}
