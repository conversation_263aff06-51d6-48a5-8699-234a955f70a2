/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.network.serverpackets.elementalspirits;

import club.projectessence.gameserver.enums.ElementalType;
import club.projectessence.gameserver.model.ElementalSpirit;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import io.github.joealisson.mmocore.WritableBuffer;

/**
 * <AUTHOR>
 */
public abstract class UpdateElementalSpiritPacket extends AbstractElementalSpiritPacket {
	private final PlayerInstance _player;
	private final byte _type;
	private final boolean _update;

	UpdateElementalSpiritPacket(PlayerInstance player, byte type, boolean update) {
		_player = player;
		_type = type;
		_update = update;
	}

	protected void writeUpdate(WritableBuffer buffer) {
		buffer.writeByte(_update ? 1 : 0);
		buffer.writeByte(_type);

		if (_update) {
			final ElementalSpirit spirit = _player.getElementalSpirit(ElementalType.of(_type));
			if (spirit == null) {
				return;
			}

			buffer.writeByte(_type);
			writeSpiritInfo(spirit, buffer);
		}
	}
}
