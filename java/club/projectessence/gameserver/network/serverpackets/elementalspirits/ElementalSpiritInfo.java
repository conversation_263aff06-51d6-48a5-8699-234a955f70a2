/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.network.serverpackets.elementalspirits;

import club.projectessence.commons.network.ServerPacketId;
import club.projectessence.gameserver.model.ElementalSpirit;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.network.GameClient;
import io.github.joealisson.mmocore.WritableBuffer;

/**
 * <AUTHOR>
 */
public class ElementalSpiritInfo extends AbstractElementalSpiritPacket {
	private final PlayerInstance _player;
	private final byte _spiritType;
	private final byte _type;

	public ElementalSpiritInfo(PlayerInstance player, byte spiritType, byte packetType) {
		_player = player;
		_spiritType = spiritType;
		_type = packetType;
	}

	@Override
	public void writeImpl(GameClient client, WritableBuffer buffer) {
		writeId(ServerPacketId.EX_ELEMENTAL_SPIRIT_INFO, buffer);

		final ElementalSpirit[] spirits = _player.getSpirits();
		if (spirits == null) {
			buffer.writeByte(0);
			buffer.writeByte(0);
			buffer.writeByte(0);
			return;
		}

		buffer.writeByte(_type); // show spirit info window 1; Change type 2; Only update 0
		buffer.writeByte(_spiritType);

		buffer.writeByte(spirits.length); // spirit count

		for (ElementalSpirit spirit : spirits) {
			buffer.writeByte(spirit.getType());
			buffer.writeByte(0x01); // spirit active ?
			// if active
			writeSpiritInfo(spirit, buffer);
		}

		buffer.writeInt(1); // Reset talent items count
		for (int j = 0; j < 1; j++) {
			buffer.writeInt(57);
			buffer.writeLong(50000);
		}
	}
}