/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.network.serverpackets.elementalspirits;

import club.projectessence.commons.network.ServerPacketId;
import club.projectessence.commons.util.CommonUtil;
import club.projectessence.gameserver.enums.ElementalType;
import club.projectessence.gameserver.model.ElementalSpirit;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.model.holders.ElementalSpiritAbsorbItemHolder;
import club.projectessence.gameserver.network.GameClient;
import club.projectessence.gameserver.network.serverpackets.ServerPacket;
import io.github.joealisson.mmocore.WritableBuffer;

import java.util.List;

/**
 * <AUTHOR>
 */
public class ElementalSpiritAbsorbInfo extends ServerPacket {
	private final PlayerInstance _player;
	private final byte _type;

	public ElementalSpiritAbsorbInfo(PlayerInstance player, byte type) {
		_player = player;
		_type = type;
	}

	@Override
	public void writeImpl(GameClient client, WritableBuffer buffer) {
		writeId(ServerPacketId.EX_ELEMENTAL_SPIRIT_ABSORB_INFO, buffer);

		final ElementalSpirit spirit = _player.getElementalSpirit(ElementalType.of(_type));
		if (spirit == null) {
			buffer.writeByte(0x00);
			buffer.writeByte(0x00);
			return;
		}

		buffer.writeByte(0x01);
		buffer.writeByte(_type);
		buffer.writeByte(spirit.getStage());
		buffer.writeLong(spirit.getExperience());
		buffer.writeLong(spirit.getExperienceToNextLevel()); // NextExp
		buffer.writeLong(spirit.getExperienceToNextLevel()); // MaxExp
		buffer.writeInt(spirit.getLevel());
		buffer.writeInt(spirit.getMaxLevel());

		final List<ElementalSpiritAbsorbItemHolder> absorbItems = spirit.getAbsorbItems();
		buffer.writeInt(absorbItems.size()); // AbsorbCount
		for (ElementalSpiritAbsorbItemHolder absorbItem : absorbItems) {
			buffer.writeInt(absorbItem.getId());
			buffer.writeInt(CommonUtil.zeroIfNullOrElse(_player.getInventory().getItemByItemId(absorbItem.getId()), item -> (int) item.getCount()));
			buffer.writeInt((int) absorbItem.getExperience());
		}
	}
}
