/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.network.serverpackets.dailymission;

import club.projectessence.commons.network.ServerPacketId;
import club.projectessence.gameserver.data.xml.DailyMissionData;
import club.projectessence.gameserver.model.DailyMissionDataHolder;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.network.GameClient;
import club.projectessence.gameserver.network.serverpackets.ServerPacket;
import io.github.joealisson.mmocore.WritableBuffer;
import it.sauronsoftware.cron4j.Predictor;

import java.time.LocalDate;
import java.util.Collection;
import java.util.Collections;
import java.util.function.Function;

/**
 * <AUTHOR>
 */
public class ExOneDayReceiveRewardList extends ServerPacket {
	private static final Function<String, Long> _remainTime = pattern -> (new Predictor(pattern).nextMatchingTime() - System.currentTimeMillis()) / 1000;
	final PlayerInstance _player;
	private final Collection<DailyMissionDataHolder> _rewards;
	private final long _dayRemainTime;
	private final long _weekRemainTime;
	private final long _monthRemainTime;

	public ExOneDayReceiveRewardList(PlayerInstance player, boolean sendRewards) {
		_player = player;
		_rewards = sendRewards ? DailyMissionData.getInstance().getDailyMissionData(player) : Collections.emptyList();
		_dayRemainTime = _remainTime.apply("30 6 * * *");
		_weekRemainTime = _remainTime.apply("30 6 * * 1");
		_monthRemainTime = _remainTime.apply("30 6 1 * *");
	}

	@Override
	public void writeImpl(GameClient client, WritableBuffer buffer) {
		if (!DailyMissionData.getInstance().isAvailable()) {
			return;
		}

		writeId(ServerPacketId.EX_ONE_DAY_RECEIVE_REWARD_LIST, buffer);

		buffer.writeInt((int) _dayRemainTime);
		buffer.writeInt((int) _weekRemainTime);
		buffer.writeInt((int) _monthRemainTime);
		buffer.writeByte(0x17);
		buffer.writeInt(_player.getClassId().getId());
		buffer.writeInt(LocalDate.now().getDayOfWeek().ordinal()); // Day of week
		buffer.writeInt(_rewards.size());
		for (DailyMissionDataHolder reward : _rewards) {
			buffer.writeShort(reward.getId());
			final int status = reward.getStatus(_player);
			buffer.writeByte(status);
			buffer.writeByte(reward.getRequiredCompletions() > 1 ? 0x01 : 0x00);
			buffer.writeInt(reward.getParams().getInt("level", -1) == -1 ? (status == 1 ? 0 : reward.getProgress(_player)) : _player.getLevel());
			buffer.writeInt(reward.getRequiredCompletions());
		}
	}
}
