/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.network.serverpackets;

import club.projectessence.gameserver.model.ItemInfo;
import club.projectessence.gameserver.model.items.instance.ItemInstance;
import io.github.joealisson.mmocore.WritableBuffer;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentSkipListMap;

/**
 * <AUTHOR>
 */
public abstract class AbstractInventoryUpdate extends AbstractItemPacket {
	private final Map<Integer, ItemInfo> _items = new ConcurrentSkipListMap<>();

	public AbstractInventoryUpdate() {
	}

	public AbstractInventoryUpdate(ItemInstance item) {
		addItem(item);
	}

	public AbstractInventoryUpdate(List<ItemInfo> items) {
		for (ItemInfo item : items) {
			_items.put(item.getObjectId(), item);
		}
	}

	public void addItem(ItemInstance item) {
		_items.put(item.getObjectId(), new ItemInfo(item));
	}

	public void addNewItem(ItemInstance item) {
		_items.put(item.getObjectId(), new ItemInfo(item, 1));
	}

	public void addModifiedItem(ItemInstance item) {
		_items.put(item.getObjectId(), new ItemInfo(item, 2));
	}

	public void addRemovedItem(ItemInstance item) {
		_items.put(item.getObjectId(), new ItemInfo(item, 3));
	}

	public void addItems(List<ItemInstance> items) {
		for (ItemInstance item : items) {
			_items.put(item.getObjectId(), new ItemInfo(item));
		}
	}

	public Collection<ItemInfo> getItems() {
		return _items.values();
	}

	protected final void writeItems(WritableBuffer buffer) {
		buffer.writeByte(0); // 140
		buffer.writeInt(0); // items size as well, is it needed?
		buffer.writeInt(_items.size()); // 140
		for (ItemInfo item : _items.values()) {
			buffer.writeShort(item.getChange()); // Update type : 01-add, 02-modify, 03-remove
			writeItem(item, buffer);
		}
	}
}
