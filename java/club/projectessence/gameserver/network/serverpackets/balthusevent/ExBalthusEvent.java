/*
 * This file is part of the L2J Mobius project.
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.network.serverpackets.balthusevent;

import club.projectessence.commons.network.ServerPacketId;
import club.projectessence.gameserver.network.GameClient;
import club.projectessence.gameserver.network.serverpackets.ServerPacket;
import io.github.joealisson.mmocore.WritableBuffer;

/**
 * <AUTHOR>
 */
public class ExBalthusEvent extends ServerPacket
{
	private final int		_currentState;
	private final int		_progress;
	private final int		_currentRewardItem;
	private final int		_rewardTokenCount;
	private final int		_currentTokenCount;
	private final boolean	_isParticipating;
	private final boolean	_isRunning;
	private final int		_time;
	
	public ExBalthusEvent(int currentState, int progress, int currentRewardItem, int rewardTokenCount, int currentTokenCount, boolean isParticipating, boolean isRunning, int time)
	{
		_currentState = currentState;
		_progress = progress;
		_currentRewardItem = currentRewardItem;
		_rewardTokenCount = rewardTokenCount;
		_currentTokenCount = currentTokenCount;
		_isParticipating = isParticipating;
		_isRunning = isRunning;
		_time = time;
	}
	
	@Override
	public void writeImpl(GameClient client, WritableBuffer buffer)
	{
		writeId(ServerPacketId.EX_BALTHUS_EVENT, buffer);
		buffer.writeInt(_currentState);
		buffer.writeInt(_progress);
		buffer.writeInt(_currentRewardItem);
		buffer.writeInt(_rewardTokenCount);
		buffer.writeInt(_currentTokenCount);
		buffer.writeInt(_isParticipating);
		buffer.writeByte(_isRunning);
		buffer.writeInt((int) ((System.currentTimeMillis() - _time) / 1000));
	}
}
