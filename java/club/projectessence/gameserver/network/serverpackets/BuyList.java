/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.network.serverpackets;

import club.projectessence.commons.network.ServerPacketId;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.model.buylist.Product;
import club.projectessence.gameserver.model.buylist.ProductList;
import club.projectessence.gameserver.network.GameClient;
import club.projectessence.gameserver.util.BuilderUtil;
import io.github.joealisson.mmocore.WritableBuffer;

import java.util.Collection;

public class BuyList extends AbstractItemPacket {
	private final int _listId;
	private final Collection<Product> _list;
	private final long _money;
	private final int _inventorySlots;
	private final double _castleTaxRate;

	public BuyList(ProductList list, PlayerInstance player, double castleTaxRate) {
		_listId = list.getListId();
		_list = list.getProducts().stream().filter(p -> p.getCount() > 0 || !p.hasLimitedStock()).toList();
		_money = player.getAdena();
		_inventorySlots = player.getInventory().getItems(item -> !item.isQuestItem()).size();
		_castleTaxRate = castleTaxRate;

		if (player.isGM() && player.getAdminDebug()) {
			BuilderUtil.sendSysMessage(player, "Buylist ID: " + _listId + "");
		}
	}

	@Override
	public void writeImpl(GameClient client, WritableBuffer buffer) {
		writeId(ServerPacketId.EX_BUY_SELL_LIST, buffer);

		buffer.writeInt(0x00); // Type BUY
		buffer.writeLong(_money); // current money
		buffer.writeInt(_listId);
		buffer.writeInt(_inventorySlots);
		buffer.writeShort(_list.size());
		for (Product product : _list) {
			writeItem(product, buffer);
			buffer.writeLong((long) (product.getPrice() * (1.0 + _castleTaxRate + product.getBaseTaxRate())));
		}
	}
}
