/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.network.serverpackets;

import club.projectessence.commons.network.ServerPacketId;
import club.projectessence.gameserver.enums.BroochJewel;
import club.projectessence.gameserver.model.Hit;
import club.projectessence.gameserver.model.Location;
import club.projectessence.gameserver.model.actor.Creature;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.network.GameClient;
import io.github.joealisson.mmocore.WritableBuffer;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

public class Attack extends ServerPacket {
	private final int _attackerObjId;
	private final Location _attackerLoc;
	private final Location _targetLoc;
	private final List<Hit> _hits = new ArrayList<>();
	private final int _soulshotVisualSubstitute;

	/**
	 * @param attacker
	 * @param target
	 */
	public Attack(Creature attacker, Creature target) {
		_attackerObjId = attacker.getObjectId();
		_attackerLoc = new Location(attacker);
		_targetLoc = new Location(target);

		final PlayerInstance player = attacker.getActingPlayer();
		if (player == null) {
			_soulshotVisualSubstitute = 0;
		} else {
			final BroochJewel activeOnyx = player.getActiveOnyxJewel();
			if (activeOnyx != null) {
				_soulshotVisualSubstitute = activeOnyx.getItemId();
			} else {
				_soulshotVisualSubstitute = 0;
			}
		}
	}

	/**
	 * Adds hit to the attack (Attacks such as dual dagger/sword/fist has two hits)
	 *
	 * @param hit
	 */
	public void addHit(Hit hit) {
		_hits.add(hit);
	}

	public List<Hit> getHits() {
		return _hits;
	}

	/**
	 * @return {@code true} if current attack contains at least 1 hit.
	 */
	public boolean hasHits() {
		return !_hits.isEmpty();
	}

	@Override
	public void writeImpl(GameClient client, WritableBuffer buffer) {
		final Iterator<Hit> it = _hits.iterator();
		final Hit firstHit = it.next();
		writeId(ServerPacketId.ATTACK, buffer);

		buffer.writeInt(_attackerObjId);
		buffer.writeInt(firstHit.getTargetId());
		buffer.writeInt(_soulshotVisualSubstitute); // Ertheia
		buffer.writeInt(firstHit.getDamage());
		buffer.writeInt(firstHit.getFlags());
		buffer.writeInt(firstHit.getGrade()); // GOD
		buffer.writeInt(_attackerLoc.getX());
		buffer.writeInt(_attackerLoc.getY());
		buffer.writeInt(_attackerLoc.getZ());

		buffer.writeShort(_hits.size() - 1);
		while (it.hasNext()) {
			writeHit(it.next(), buffer);
		}

		buffer.writeInt(_targetLoc.getX());
		buffer.writeInt(_targetLoc.getY());
		buffer.writeInt(_targetLoc.getZ());
	}

	private void writeHit(Hit hit, WritableBuffer buffer) {
		buffer.writeInt(hit.getTargetId());
		buffer.writeInt(hit.getDamage());
		buffer.writeInt(hit.getFlags());
		buffer.writeInt(hit.getGrade()); // GOD
	}
}
