/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.network.serverpackets;

import club.projectessence.commons.network.ServerPacketId;
import club.projectessence.gameserver.data.sql.ClanTable;
import club.projectessence.gameserver.enums.SiegeClanType;
import club.projectessence.gameserver.model.SiegeClan;
import club.projectessence.gameserver.model.clan.Clan;
import club.projectessence.gameserver.model.siege.Castle;
import club.projectessence.gameserver.network.GameClient;
import io.github.joealisson.mmocore.WritableBuffer;

/**
 * Populates the Siege Defender List in the SiegeInfo Window<br>
 * <br>
 * c = 0xcb<br>
 * d = CastleID<br>
 * d = unknow (0x00)<br>
 * d = unknow (0x01)<br>
 * d = unknow (0x00)<br>
 * d = Number of Defending Clans?<br>
 * d = Number of Defending Clans<br>
 * { //repeats<br>
 * d = ClanID<br>
 * S = ClanName<br>
 * S = ClanLeaderName<br>
 * d = ClanCrestID<br>
 * d = signed time (seconds)<br>
 * d = Type -> Owner = 0x01 || Waiting = 0x02 || Accepted = 0x03<br>
 * d = AllyID<br>
 * S = AllyName<br>
 * S = AllyLeaderName<br>
 * d = AllyCrestID<br>
 *
 * <AUTHOR>
 */
public class SiegeDefenderList extends ServerPacket {
	private final Castle _castle;

	public SiegeDefenderList(Castle castle) {
		_castle = castle;
	}

	@Override
	public void writeImpl(GameClient client, WritableBuffer buffer) {
		writeId(ServerPacketId.CASTLE_SIEGE_DEFENDER_LIST, buffer);

		buffer.writeInt(_castle.getResidenceId());
		buffer.writeInt(0x00); // Unknown
		buffer.writeInt(0x01); // Unknown
		buffer.writeInt(0x00); // Unknown

		final int size = _castle.getSiege().getDefenderWaitingClans().size() + _castle.getSiege().getDefenderClans().size() + (_castle.getOwner() != null ? 1 : 0);
		buffer.writeInt(size);
		buffer.writeInt(size);

		// Add owners
		final Clan ownerClan = _castle.getOwner();
		if (ownerClan != null) {
			buffer.writeInt(ownerClan.getId());
			buffer.writeString(ownerClan.getName());
			buffer.writeString(ownerClan.getLeaderName());
			buffer.writeInt(ownerClan.getCrestId());
			buffer.writeInt(0x00); // signed time (seconds) (not storated by L2J)
			buffer.writeInt(SiegeClanType.OWNER.ordinal());
			buffer.writeInt(ownerClan.getAllyId());
			buffer.writeString(ownerClan.getAllyName());
			buffer.writeString(""); // AllyLeaderName
			buffer.writeInt(ownerClan.getAllyCrestId());
		}

		// List of confirmed defenders
		for (SiegeClan siegeClan : _castle.getSiege().getDefenderClans()) {
			final Clan defendingClan = ClanTable.getInstance().getClan(siegeClan.getClanId());
			if ((defendingClan == null) || (defendingClan == _castle.getOwner())) {
				continue;
			}

			buffer.writeInt(defendingClan.getId());
			buffer.writeString(defendingClan.getName());
			buffer.writeString(defendingClan.getLeaderName());
			buffer.writeInt(defendingClan.getCrestId());
			buffer.writeInt(0x00); // signed time (seconds) (not storated by L2J)
			buffer.writeInt(SiegeClanType.DEFENDER.ordinal());
			buffer.writeInt(defendingClan.getAllyId());
			buffer.writeString(defendingClan.getAllyName());
			buffer.writeString(""); // AllyLeaderName
			buffer.writeInt(defendingClan.getAllyCrestId());
		}

		// List of not confirmed defenders
		for (SiegeClan siegeClan : _castle.getSiege().getDefenderWaitingClans()) {
			final Clan defendingClan = ClanTable.getInstance().getClan(siegeClan.getClanId());
			if (defendingClan == null) {
				continue;
			}

			buffer.writeInt(defendingClan.getId());
			buffer.writeString(defendingClan.getName());
			buffer.writeString(defendingClan.getLeaderName());
			buffer.writeInt(defendingClan.getCrestId());
			buffer.writeInt(0x00); // signed time (seconds) (not storated by L2J)
			buffer.writeInt(SiegeClanType.DEFENDER_PENDING.ordinal());
			buffer.writeInt(defendingClan.getAllyId());
			buffer.writeString(defendingClan.getAllyName());
			buffer.writeString(""); // AllyLeaderName
			buffer.writeInt(defendingClan.getAllyCrestId());
		}
	}
}
