/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.network.serverpackets;

import club.projectessence.commons.network.ServerPacketId;
import club.projectessence.gameserver.model.CommandChannel;
import club.projectessence.gameserver.model.Party;
import club.projectessence.gameserver.network.GameClient;
import io.github.joealisson.mmocore.WritableBuffer;

import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 */
public class ExMultiPartyCommandChannelInfo extends ServerPacket {
	private final CommandChannel _channel;
	private final Map<Party, Map<Integer, CommandChannel.CommandChannelPlayerKdInfo>> _kdInfo;

	public ExMultiPartyCommandChannelInfo(CommandChannel channel) {
		Objects.requireNonNull(channel);
		_channel = channel;
		_kdInfo = channel.getAllKDInfo();
	}

	@Override
	public void writeImpl(GameClient client, WritableBuffer buffer) {
		writeId(ServerPacketId.EX_MULTI_PARTY_COMMAND_CHANNEL_INFO, buffer);

		buffer.writeString(_channel.getLeader().getName());
		buffer.writeInt(0x00); // Channel loot 0 or 1
		buffer.writeInt(_channel.getMemberCount());

		buffer.writeInt(_channel.getParties().size());
		for (Party p : _channel.getParties()) {
			int kills = 0;
			int deaths = 0;
			final Map<Integer, CommandChannel.CommandChannelPlayerKdInfo> kdInfo = _kdInfo.get(p);
			if (kdInfo != null) {
				for (CommandChannel.CommandChannelPlayerKdInfo kd : kdInfo.values()) {
					kills += kd.getKills();
					deaths += kd.getDeaths();
				}
			}
			buffer.writeString(p.getLeader().getName() + ";" + kills + ";" + deaths);
			buffer.writeInt(p.getLeaderObjectId());
			buffer.writeInt(p.getMemberCount());
		}
	}
}