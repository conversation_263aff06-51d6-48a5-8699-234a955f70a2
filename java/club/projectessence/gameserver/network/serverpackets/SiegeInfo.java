/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.network.serverpackets;

import club.projectessence.Config;
import club.projectessence.commons.network.ServerPacketId;
import club.projectessence.gameserver.data.sql.ClanTable;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.model.clan.Clan;
import club.projectessence.gameserver.model.siege.Castle;
import club.projectessence.gameserver.network.GameClient;
import io.github.joealisson.mmocore.WritableBuffer;

import java.util.Calendar;

/**
 * Shows the Siege Info<br>
 * <br>
 * c = c9<br>
 * d = CastleID<br>
 * d = Show Owner Controls (0x00 default || >=0x02(mask?) owner)<br>
 * d = Owner ClanID<br>
 * S = Owner ClanName<br>
 * S = Owner Clan LeaderName<br>
 * d = Owner AllyID<br>
 * S = Owner AllyName<br>
 * d = current time (seconds)<br>
 * d = Siege time (seconds) (0 for selectable)<br>
 * d = (UNKNOW) Siege Time Select Related?
 *
 * <AUTHOR>
 */
public class SiegeInfo extends ServerPacket {
	private final Castle _castle;
	private final PlayerInstance _player;

	public SiegeInfo(Castle castle, PlayerInstance player) {
		_castle = castle;
		_player = player;
	}

	@Override
	public void writeImpl(GameClient client, WritableBuffer buffer) {
		writeId(ServerPacketId.CASTLE_SIEGE_INFO, buffer);

		if (_castle != null) {
			buffer.writeInt(_castle.getResidenceId());

			final int ownerId = _castle.getOwnerId();
			buffer.writeInt(((ownerId == _player.getClanId()) && (_player.isClanLeader())) ? 0x01 : 0x00);
			buffer.writeInt(ownerId);
			if (ownerId > 0) {
				final Clan owner = ClanTable.getInstance().getClan(ownerId);
				if (owner != null) {
					buffer.writeString(owner.getName()); // Clan Name
					buffer.writeString(owner.getLeaderName()); // Clan Leader Name
					buffer.writeInt(owner.getAllyId()); // Ally ID
					buffer.writeString(owner.getAllyName()); // Ally Name
				} else {
					LOGGER.warning("Null owner for castle: " + _castle.getName());
				}
			} else {
				buffer.writeString(""); // Clan Name
				buffer.writeString(""); // Clan Leader Name
				buffer.writeInt(0); // Ally ID
				buffer.writeString(""); // Ally Name
			}

			buffer.writeInt((int) (System.currentTimeMillis() / 1000));
			if (!_castle.isTimeRegistrationOver() && _player.isClanLeader() && (_player.getClanId() == _castle.getOwnerId())) {
				final Calendar cal = Calendar.getInstance();
				cal.setTimeInMillis(_castle.getSiegeDate().getTimeInMillis());
				cal.set(Calendar.MINUTE, 0);
				cal.set(Calendar.SECOND, 0);
				buffer.writeInt(0x00);
				buffer.writeInt(Config.SIEGE_HOUR_LIST.size());
				for (int hour : Config.SIEGE_HOUR_LIST) {
					cal.set(Calendar.HOUR_OF_DAY, hour);
					buffer.writeInt((int) (cal.getTimeInMillis() / 1000));
				}
			} else {
				buffer.writeInt((int) (_castle.getSiegeDate().getTimeInMillis() / 1000));
				buffer.writeInt(0x00);
			}
		}
	}
}
