/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.network.serverpackets;

import club.projectessence.commons.network.ServerPacketId;
import club.projectessence.gameserver.model.Message;
import club.projectessence.gameserver.model.itemcontainer.ItemContainer;
import club.projectessence.gameserver.model.items.instance.ItemInstance;
import club.projectessence.gameserver.network.GameClient;
import io.github.joealisson.mmocore.WritableBuffer;

import java.util.Collection;

/**
 * <AUTHOR> DS
 */
public class ExReplySentPost extends AbstractItemPacket {
	private final Message _msg;
	private Collection<ItemInstance> _items = null;

	public ExReplySentPost(Message msg) {
		_msg = msg;
		if (msg.hasAttachments()) {
			final ItemContainer attachments = msg.getAttachments();
			if ((attachments != null) && (attachments.getSize() > 0)) {
				_items = attachments.getItems();
			} else {
				LOGGER.warning("Message " + msg.getId() + " has attachments but itemcontainer is empty.");
			}
		}
	}

	@Override
	public void writeImpl(GameClient client, WritableBuffer buffer) {
		writeId(ServerPacketId.EX_REPLY_SENT_POST, buffer);

		buffer.writeInt(0x00); // GOD
		buffer.writeInt(_msg.getId());
		buffer.writeInt(_msg.isLocked() ? 1 : 0);
		buffer.writeString(_msg.getReceiverName());
		buffer.writeString(_msg.getSubject());
		buffer.writeString(_msg.getContent());

		if ((_items != null) && !_items.isEmpty()) {
			buffer.writeInt(_items.size());
			for (ItemInstance item : _items) {
				writeItem(item, buffer);
				buffer.writeInt(item.getObjectId());
			}
		} else {
			buffer.writeInt(0x00);
		}
		buffer.writeLong(_msg.getReqAdena());
		buffer.writeInt(_msg.hasAttachments() ? 0x01 : 0x00);
		buffer.writeInt(_msg.isReturned() ? 0x01 : 00);
	}
}
