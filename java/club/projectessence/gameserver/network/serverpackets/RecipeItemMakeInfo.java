/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.network.serverpackets;

import club.projectessence.commons.network.ServerPacketId;
import club.projectessence.gameserver.data.xml.RecipeData;
import club.projectessence.gameserver.model.RecipeList;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.network.GameClient;
import io.github.joealisson.mmocore.WritableBuffer;

public class RecipeItemMakeInfo extends ServerPacket {
	private final int _id;
	private final PlayerInstance _player;
	private final boolean _success;

	public RecipeItemMakeInfo(int id, PlayerInstance player, boolean success) {
		_id = id;
		_player = player;
		_success = success;
	}

	public RecipeItemMakeInfo(int id, PlayerInstance player) {
		_id = id;
		_player = player;
		_success = true;
	}

	@Override
	public void writeImpl(GameClient client, WritableBuffer buffer) {
		final RecipeList recipe = RecipeData.getInstance().getRecipeList(_id);
		if (recipe != null) {
			writeId(ServerPacketId.RECIPE_ITEM_MAKE_INFO, buffer);
			buffer.writeInt(_id);
			buffer.writeInt(recipe.isDwarvenRecipe() ? 0 : 1); // 0 = Dwarven - 1 = Common
			buffer.writeInt((int) _player.getCurrentMp());
			buffer.writeInt(_player.getMaxMp());
			buffer.writeInt(_success ? 1 : 0); // item creation success/failed
			buffer.writeByte(0x00);
			buffer.writeLong(0x00);
			return;
		}
		LOGGER.info("Character: " + _player + ": Requested unexisting recipe with id = " + _id);
	}
}
