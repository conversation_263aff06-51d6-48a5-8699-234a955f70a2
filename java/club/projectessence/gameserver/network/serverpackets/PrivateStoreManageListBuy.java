/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.network.serverpackets;

import club.projectessence.commons.network.ServerPacketId;
import club.projectessence.gameserver.model.TradeItem;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.model.items.instance.ItemInstance;
import club.projectessence.gameserver.network.GameClient;
import io.github.joealisson.mmocore.WritableBuffer;

import java.util.Collection;
import java.util.Set;

public class PrivateStoreManageListBuy extends AbstractItemPacket {
	private final int _sendType;
	private final int _objId;
	private final long _playerAdena;
	private final Collection<ItemInstance> _itemList;
	private final Set<TradeItem> _buyList;

	public PrivateStoreManageListBuy(int sendType, PlayerInstance player) {
		_sendType = sendType;
		_objId = player.getObjectId();
		_playerAdena = player.getAdena();
		_itemList = player.getInventory().getUniqueItems(false, true);
		_buyList = player.getBuyList().getItems();
	}

	@Override
	public void writeImpl(GameClient client, WritableBuffer buffer) {
		writeId(ServerPacketId.PRIVATE_STORE_BUY_MANAGE_LIST, buffer);
		buffer.writeByte(_sendType);
		if (_sendType == 1) {
			buffer.writeInt(_objId);
			buffer.writeLong(_playerAdena);
			buffer.writeInt(_buyList.size());
			for (TradeItem item2 : _buyList) {
				writeItem(item2, buffer);
				buffer.writeLong(item2.getPrice());
				buffer.writeLong(item2.getItem().getReferencePrice() * 2);
				buffer.writeLong(item2.getCount());
			}
			buffer.writeInt(_itemList.size());
		} else if (_sendType == 2) {
			buffer.writeInt(_itemList.size());
			buffer.writeInt(_itemList.size());
			for (ItemInstance item : _itemList) {
				writeItem(item, buffer);
				buffer.writeLong(item.getItem().getReferencePrice() * 2);
			}
		}
	}
}
