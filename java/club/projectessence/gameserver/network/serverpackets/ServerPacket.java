/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.network.serverpackets;

import club.projectessence.commons.network.ServerPacketId;
import club.projectessence.gameserver.model.actor.Creature;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.model.interfaces.ILocational;
import club.projectessence.gameserver.model.itemcontainer.Inventory;
import club.projectessence.gameserver.network.GameClient;
import io.github.joealisson.mmocore.WritableBuffer;
import io.github.joealisson.mmocore.WritablePacket;

import java.util.logging.Logger;

/**
 * <AUTHOR>
 * <AUTHOR>
 */
public abstract class ServerPacket extends WritablePacket<GameClient> {
	public static final Logger LOGGER = Logger.getLogger(Creature.class.getName());

	private final int[] PAPERDOLL_ORDER = new int[]
			{
					Inventory.PAPERDOLL_UNDER,
					Inventory.PAPERDOLL_REAR,
					Inventory.PAPERDOLL_LEAR,
					Inventory.PAPERDOLL_NECK,
					Inventory.PAPERDOLL_RFINGER,
					Inventory.PAPERDOLL_LFINGER,
					Inventory.PAPERDOLL_HEAD,
					Inventory.PAPERDOLL_RHAND,
					Inventory.PAPERDOLL_LHAND,
					Inventory.PAPERDOLL_GLOVES,
					Inventory.PAPERDOLL_CHEST,
					Inventory.PAPERDOLL_LEGS,
					Inventory.PAPERDOLL_FEET,
					Inventory.PAPERDOLL_CLOAK,
					Inventory.PAPERDOLL_RHAND,
					Inventory.PAPERDOLL_HAIR,
					Inventory.PAPERDOLL_HAIR2,
					Inventory.PAPERDOLL_RBRACELET,
					Inventory.PAPERDOLL_LBRACELET,
					Inventory.PAPERDOLL_AGATHION1,
					Inventory.PAPERDOLL_AGATHION2,
					Inventory.PAPERDOLL_AGATHION3,
					Inventory.PAPERDOLL_AGATHION4,
					Inventory.PAPERDOLL_AGATHION5,
					Inventory.PAPERDOLL_DECO1,
					Inventory.PAPERDOLL_DECO2,
					Inventory.PAPERDOLL_DECO3,
					Inventory.PAPERDOLL_DECO4,
					Inventory.PAPERDOLL_DECO5,
					Inventory.PAPERDOLL_DECO6,
					Inventory.PAPERDOLL_BELT,
					Inventory.PAPERDOLL_BROOCH,
					Inventory.PAPERDOLL_BROOCH_JEWEL1,
					Inventory.PAPERDOLL_BROOCH_JEWEL2,
					Inventory.PAPERDOLL_BROOCH_JEWEL3,
					Inventory.PAPERDOLL_BROOCH_JEWEL4,
					Inventory.PAPERDOLL_BROOCH_JEWEL5,
					Inventory.PAPERDOLL_BROOCH_JEWEL6,
					Inventory.PAPERDOLL_ARTIFACT_BOOK,
					Inventory.PAPERDOLL_ARTIFACT1,
					Inventory.PAPERDOLL_ARTIFACT2,
					Inventory.PAPERDOLL_ARTIFACT3,
					Inventory.PAPERDOLL_ARTIFACT4,
					Inventory.PAPERDOLL_ARTIFACT5,
					Inventory.PAPERDOLL_ARTIFACT6,
					Inventory.PAPERDOLL_ARTIFACT7,
					Inventory.PAPERDOLL_ARTIFACT8,
					Inventory.PAPERDOLL_ARTIFACT9,
					Inventory.PAPERDOLL_ARTIFACT10,
					Inventory.PAPERDOLL_ARTIFACT11,
					Inventory.PAPERDOLL_ARTIFACT12,
					Inventory.PAPERDOLL_ARTIFACT13,
					Inventory.PAPERDOLL_ARTIFACT14,
					Inventory.PAPERDOLL_ARTIFACT15,
					Inventory.PAPERDOLL_ARTIFACT16,
					Inventory.PAPERDOLL_ARTIFACT17,
					Inventory.PAPERDOLL_ARTIFACT18,
					Inventory.PAPERDOLL_ARTIFACT19,
					Inventory.PAPERDOLL_ARTIFACT20,
					Inventory.PAPERDOLL_ARTIFACT21,
			};

	private final int[] PAPERDOLL_ORDER_AUGMENT = new int[]
			{
					Inventory.PAPERDOLL_RHAND,
					Inventory.PAPERDOLL_LHAND,
					Inventory.PAPERDOLL_RHAND
			};

	private final int[] PAPERDOLL_ORDER_VISUAL_ID = new int[]
			{
					Inventory.PAPERDOLL_RHAND,
					Inventory.PAPERDOLL_LHAND,
					Inventory.PAPERDOLL_RHAND,
					Inventory.PAPERDOLL_GLOVES,
					Inventory.PAPERDOLL_CHEST,
					Inventory.PAPERDOLL_LEGS,
					Inventory.PAPERDOLL_FEET,
					Inventory.PAPERDOLL_HAIR,
					Inventory.PAPERDOLL_HAIR2
			};

	public int[] getPaperdollOrder() {
		return PAPERDOLL_ORDER;
	}

	public int[] getPaperdollOrderAugument() {
		return PAPERDOLL_ORDER_AUGMENT;
	}

	public int[] getPaperdollOrderVisualId() {
		return PAPERDOLL_ORDER_VISUAL_ID;
	}

	/**
	 * Sends this packet to the target player, useful for lambda operations like <br>
	 * {@code World.getInstance().getPlayers().forEach(packet::sendTo)}
	 *
	 * @param player to send the packet
	 */
	public void sendTo(PlayerInstance player) {
		player.sendPacket(this);
	}

	@Override
	protected boolean write(GameClient client, WritableBuffer buffer) {
		try {
			writeImpl(client, buffer);
			return true;
		} catch (Exception e) {
			LOGGER.warning("Error writing packet " + this + " to client (" + e.getMessage() + ") " + client + "]]");
			e.printStackTrace();
		}
		return false;
	}

	public void runImpl(PlayerInstance player) {
		// TODO remove this
	}

	protected void writeId(ServerPacketId packet, WritableBuffer buffer) {
		packet.writeId(buffer);
	}

	protected void writeOptionalD(int value, WritableBuffer buffer) {
		if (value >= Short.MAX_VALUE) {
			buffer.writeShort(Short.MAX_VALUE);
			buffer.writeInt(value);
		} else {
			buffer.writeShort(value);
		}
	}

	protected void writeLocation(ILocational location, WritableBuffer buffer) {
		buffer.writeInt(location.getX());
		buffer.writeInt(location.getY());
		buffer.writeInt(location.getZ());
	}

	protected abstract void writeImpl(GameClient client, WritableBuffer buffer) throws Exception;
}