/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.network.serverpackets;

import club.projectessence.Config;
import club.projectessence.commons.network.ServerPacketId;
import club.projectessence.gameserver.instancemanager.MentorManager;
import club.projectessence.gameserver.model.PlayerCondOverride;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.model.items.instance.ItemInstance;
import club.projectessence.gameserver.network.GameClient;
import io.github.joealisson.mmocore.WritableBuffer;

import java.util.Collection;

public class TradeStart extends AbstractItemPacket {
	private final int _sendType;
	private final PlayerInstance _player;
	private final PlayerInstance _partner;
	private final Collection<ItemInstance> _itemList;
	private int _mask = 0;

	public TradeStart(int sendType, PlayerInstance player) {
		_sendType = sendType;
		_player = player;
		_partner = player.getActiveTradeList().getPartner();
		_itemList = _player.getInventory().getAvailableItems(true, (_player.canOverrideCond(PlayerCondOverride.ITEM_CONDITIONS) && Config.GM_TRADE_RESTRICTED_ITEMS), false);
		if (_partner != null) {
			if (player.getFriendList().contains(_partner.getObjectId())) {
				_mask |= 0x01;
			}
			if ((player.getClanId() > 0) && (_partner.getClanId() == _partner.getClanId())) {
				_mask |= 0x02;
			}
			if ((MentorManager.getInstance().getMentee(player.getObjectId(), _partner.getObjectId()) != null) || (MentorManager.getInstance().getMentee(_partner.getObjectId(), player.getObjectId()) != null)) {
				_mask |= 0x04;
			}
			if ((player.getAllyId() > 0) && (player.getAllyId() == _partner.getAllyId())) {
				_mask |= 0x08;
			}

			// Does not shows level
			if (_partner.isGM()) {
				_mask |= 0x10;
			}
		}
	}

	@Override
	public void writeImpl(GameClient client, WritableBuffer buffer) {
		if ((_player.getActiveTradeList() == null) || (_partner == null)) {
			return;
		}

		writeId(ServerPacketId.TRADE_START, buffer);
		buffer.writeByte(_sendType);
		if (_sendType == 2) {
			buffer.writeInt(_itemList.size());
			buffer.writeInt(_itemList.size());
			for (ItemInstance item : _itemList) {
				writeItem(item, buffer);
			}
		} else {
			buffer.writeInt(_partner.getObjectId());
			buffer.writeByte(_mask); // some kind of mask
			if ((_mask & 0x10) == 0) {
				buffer.writeByte(_partner.getLevel());
			}
		}
	}
}
