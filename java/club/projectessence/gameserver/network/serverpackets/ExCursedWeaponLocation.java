/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.network.serverpackets;

import club.projectessence.commons.network.ServerPacketId;
import club.projectessence.gameserver.model.Location;
import club.projectessence.gameserver.network.GameClient;
import io.github.joealisson.mmocore.WritableBuffer;

import java.util.List;

/**
 * Format: (ch) d[ddddd]
 *
 * <AUTHOR>
 */
public class ExCursedWeaponLocation extends ServerPacket {
	private final List<CursedWeaponInfo> _cursedWeaponInfo;

	public ExCursedWeaponLocation(List<CursedWeaponInfo> cursedWeaponInfo) {
		_cursedWeaponInfo = cursedWeaponInfo;
	}

	@Override
	public void writeImpl(GameClient client, WritableBuffer buffer) {
		writeId(ServerPacketId.EX_CURSED_WEAPON_LOCATION, buffer);

		if (!_cursedWeaponInfo.isEmpty()) {
			buffer.writeInt(_cursedWeaponInfo.size());
			for (CursedWeaponInfo w : _cursedWeaponInfo) {
				buffer.writeInt(w.id);
				buffer.writeInt(w.activated);

				buffer.writeInt(w.pos.getX());
				buffer.writeInt(w.pos.getY());
				buffer.writeInt(w.pos.getZ());
			}
		} else {
			buffer.writeInt(0);
		}
	}

	public static class CursedWeaponInfo {
		public Location pos;
		public int id;
		public int activated; // 0 - not activated ? 1 - activated

		public CursedWeaponInfo(Location p, int cwId, int status) {
			pos = p;
			id = cwId;
			activated = status;
		}
	}
}
