/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.network.serverpackets;

import club.projectessence.commons.network.ServerPacketId;
import club.projectessence.gameserver.data.ItemTable;
import club.projectessence.gameserver.model.ItemInfo;
import club.projectessence.gameserver.model.holders.ItemChanceHolder;
import club.projectessence.gameserver.model.holders.MultisellEntryHolder;
import club.projectessence.gameserver.model.holders.PreparedMultisellListHolder;
import club.projectessence.gameserver.model.items.Item;
import club.projectessence.gameserver.network.GameClient;
import io.github.joealisson.mmocore.WritableBuffer;

import static club.projectessence.gameserver.data.xml.MultisellData.PAGE_SIZE;

public class MultiSellList extends AbstractItemPacket {
	private final PreparedMultisellListHolder _list;
	private final boolean _finished;
	private int _size;
	private int _index;

	public MultiSellList(PreparedMultisellListHolder list, int index) {
		_list = list;
		_index = index;
		_size = list.getEntries().size() - index;
		if (_size > PAGE_SIZE) {
			_finished = false;
			_size = PAGE_SIZE;
		} else {
			_finished = true;
		}
	}

	@Override
	public void writeImpl(GameClient client, WritableBuffer buffer) {
		writeId(ServerPacketId.MULTI_SELL_LIST, buffer);

		buffer.writeByte(0x00); // Helios
		buffer.writeInt(_list.getId()); // list id
		buffer.writeByte(0x00); // GOD Unknown
		buffer.writeInt(1 + (_index / PAGE_SIZE)); // page started from 1
		buffer.writeInt(_finished ? 0x01 : 0x00); // finished
		buffer.writeInt(PAGE_SIZE); // size of pages
		buffer.writeInt(_size); // list length
		buffer.writeByte(0x00); // Grand Crusade
		buffer.writeByte(_list.isChanceMultisell() ? 0x01 : 0x00); // new multisell window
		buffer.writeInt(0x20); // Helios - Always 32

		while (_size-- > 0) {
			final ItemInfo itemEnchantment = _list.getItemEnchantment(_index);
			final MultisellEntryHolder entry = _list.getEntries().get(_index++);
			buffer.writeInt(_index); // Entry ID. Start from 1.
			buffer.writeByte(entry.isStackable() ? 1 : 0);

			// Those values will be passed down to MultiSellChoose packet.
			buffer.writeShort(itemEnchantment != null ? itemEnchantment.getEnchantLevel() : 0); // enchant level
			writeItemAugment(itemEnchantment, buffer);
			writeItemElemental(itemEnchantment, buffer);
			writeItemEnsoulOptions(itemEnchantment, buffer);
			buffer.writeByte(0x00); // 286
			buffer.writeShort(entry.getProducts().size());
			buffer.writeShort(entry.getIngredients().size());

			for (ItemChanceHolder product : entry.getProducts()) {
				final Item template = ItemTable.getInstance().getTemplate(product.getId());
				final ItemInfo displayItemEnchantment = (_list.isMaintainEnchantment() && (itemEnchantment != null) && (template != null) && template.getClass().equals(itemEnchantment.getItem().getClass())) ? itemEnchantment : null;
				if (template != null) {
					buffer.writeInt(template.getDisplayId());
					buffer.writeLong(template.getBodyPart());
					buffer.writeShort(template.getType2());
				} else {
					buffer.writeInt(product.getId());
					buffer.writeLong(0);
					buffer.writeShort(65535);
				}
				buffer.writeLong(_list.getProductCount(product));
				buffer.writeShort(product.getEnchantmentLevel() > 0 ? product.getEnchantmentLevel() : displayItemEnchantment != null ? displayItemEnchantment.getEnchantLevel() : 0); // enchant level
				buffer.writeInt((int) product.getChance() * 1000000); // chance
				writeItemAugment(displayItemEnchantment, buffer);
				writeItemElemental(displayItemEnchantment, buffer);
				writeItemEnsoulOptions(displayItemEnchantment, buffer);
				buffer.writeByte(0x00); // Blessed
			}

			for (ItemChanceHolder ingredient : entry.getIngredients()) {
				final Item template = ItemTable.getInstance().getTemplate(ingredient.getId());
				final ItemInfo displayItemEnchantment = ((itemEnchantment != null) && (itemEnchantment.getItem().getId() == ingredient.getId())) ? itemEnchantment : null;
				if (template != null) {
					buffer.writeInt(template.getDisplayId());
					buffer.writeShort(template.getType2());
				} else {
					buffer.writeInt(ingredient.getId());
					buffer.writeShort(65535);
				}
				buffer.writeLong(_list.getIngredientCount(ingredient));
				buffer.writeShort(ingredient.getEnchantmentLevel() > 0 ? ingredient.getEnchantmentLevel() : displayItemEnchantment != null ? displayItemEnchantment.getEnchantLevel() : 0); // enchant level
				writeItemAugment(displayItemEnchantment, buffer);
				writeItemElemental(displayItemEnchantment, buffer);
				writeItemEnsoulOptions(displayItemEnchantment, buffer);
				buffer.writeByte(0x00); // Blessed
			}
		}
	}
}