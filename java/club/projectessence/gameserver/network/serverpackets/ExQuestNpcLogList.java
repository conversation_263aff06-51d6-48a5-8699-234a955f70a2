/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.network.serverpackets;

import club.projectessence.commons.network.ServerPacketId;
import club.projectessence.gameserver.model.holders.NpcLogListHolder;
import club.projectessence.gameserver.network.GameClient;
import club.projectessence.gameserver.network.NpcStringId;
import io.github.joealisson.mmocore.WritableBuffer;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
public class ExQuestNpcLogList extends ServerPacket {
	private final int _questId;
	private final List<NpcLogListHolder> _npcLogList = new ArrayList<>();

	public ExQuestNpcLogList(int questId) {
		_questId = questId;
	}

	public void addNpc(int npcId, int count) {
		_npcLogList.add(new NpcLogListHolder(npcId, false, count));
	}

	public void addNpcString(NpcStringId npcStringId, int count) {
		_npcLogList.add(new NpcLogListHolder(npcStringId.getId(), true, count));
	}

	public void add(NpcLogListHolder holder) {
		_npcLogList.add(holder);
	}

	@Override
	public void writeImpl(GameClient client, WritableBuffer buffer) {
		writeId(ServerPacketId.EX_QUEST_NPC_LOG_LIST, buffer);

		buffer.writeInt(_questId);
		buffer.writeByte(_npcLogList.size());
		for (NpcLogListHolder holder : _npcLogList) {
			buffer.writeInt(holder.isNpcString() ? holder.getId() : holder.getId() + 1000000);
			buffer.writeByte(holder.isNpcString() ? 0x01 : 0x00);
			buffer.writeInt(holder.getCount());
		}
	}
}