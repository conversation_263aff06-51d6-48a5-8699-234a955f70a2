/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.network.serverpackets;

import club.projectessence.Config;
import club.projectessence.commons.network.ServerPacketId;
import club.projectessence.gameserver.model.actor.Summon;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.model.items.instance.ItemInstance;
import club.projectessence.gameserver.network.GameClient;
import io.github.joealisson.mmocore.WritableBuffer;

import java.util.Collection;

/**
 * <AUTHOR>
 */
public class ExBuySellList extends AbstractItemPacket {
	private final Collection<ItemInstance> _sellList;
	private final boolean _done;
	private final int _inventorySlots;
	private Collection<ItemInstance> _refundList = null;

	public ExBuySellList(PlayerInstance player, boolean done) {
		final Summon pet = player.getPet();
		_sellList = player.getInventory().getItems(item -> !item.isEquipped() && item.isSellable() && ((pet == null) || (item.getObjectId() != pet.getControlObjectId())));
		_inventorySlots = player.getInventory().getItems(item -> !item.isQuestItem()).size();
		if (player.hasRefund()) {
			_refundList = player.getRefund().getItems();
		}
		_done = done;
	}

	@Override
	public void writeImpl(GameClient client, WritableBuffer buffer) {
		writeId(ServerPacketId.EX_BUY_SELL_LIST, buffer);

		buffer.writeInt(0x01); // Type SELL
		buffer.writeInt(_inventorySlots);

		if ((_sellList != null)) {
			buffer.writeShort(_sellList.size());
			for (ItemInstance item : _sellList) {
				writeItem(item, buffer);
				int price = item.getItem().getReferencePrice() / 4;
				buffer.writeLong(Config.MERCHANT_ZERO_SELL_PRICE ? 0 : price);
			}
		} else {
			buffer.writeShort(0x00);
		}

		if ((_refundList != null) && !_refundList.isEmpty()) {
			buffer.writeShort(_refundList.size());
			int i = 0;
			for (ItemInstance item : _refundList) {
				writeItem(item, buffer);
				buffer.writeInt(i++);
				int price = item.getItem().getReferencePrice() / 4;
				buffer.writeLong(Config.MERCHANT_ZERO_SELL_PRICE ? 0 : (price) * item.getCount());
			}
		} else {
			buffer.writeShort(0x00);
		}

		buffer.writeByte(_done ? 0x01 : 0x00);
	}
}
