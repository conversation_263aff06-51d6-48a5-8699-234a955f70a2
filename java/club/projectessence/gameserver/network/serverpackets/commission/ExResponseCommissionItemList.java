/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.network.serverpackets.commission;

import club.projectessence.commons.network.ServerPacketId;
import club.projectessence.gameserver.model.items.instance.ItemInstance;
import club.projectessence.gameserver.network.GameClient;
import club.projectessence.gameserver.network.serverpackets.AbstractItemPacket;
import io.github.joealisson.mmocore.WritableBuffer;

import java.util.Collection;

/**
 * <AUTHOR>
 */
public class ExResponseCommissionItemList extends AbstractItemPacket {
	private final int _sendType;
	private final Collection<ItemInstance> _items;

	public ExResponseCommissionItemList(int sendType, Collection<ItemInstance> items) {
		_sendType = sendType;
		_items = items;
	}

	@Override
	public void writeImpl(GameClient client, WritableBuffer buffer) {
		writeId(ServerPacketId.EX_RESPONSE_COMMISSION_ITEM_LIST, buffer);
		buffer.writeByte(_sendType);
		if (_sendType == 2) {
			buffer.writeInt(_items.size());
			buffer.writeInt(_items.size());
			for (ItemInstance itemInstance : _items) {
				writeItem(itemInstance, buffer);
			}
		} else {
			buffer.writeInt(0);
			buffer.writeInt(0);
		}
	}
}
