/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.network.serverpackets;

import club.projectessence.Config;
import club.projectessence.commons.network.ServerPacketId;
import club.projectessence.gameserver.network.GameClient;
import io.github.joealisson.mmocore.WritableBuffer;

import java.time.Instant;
import java.time.ZoneId;
import java.time.zone.ZoneRules;

/**
 * <AUTHOR>
 */
public class ExEnterWorld extends ServerPacket {
	private final int _zoneIdOffsetSeconds;
	private final int _epochInSeconds;
	private final int _daylight;

	public ExEnterWorld() {
		Instant now = Instant.now();
		_epochInSeconds = (int) now.getEpochSecond();
		ZoneRules rules = ZoneId.systemDefault().getRules();
		_zoneIdOffsetSeconds = rules.getOffset(now).getTotalSeconds();
		// hotfix when using rules.getStandardOffset instead of rules.getOffset
		// _zoneIdOffsetSeconds += 3600; // fix 1 hour error after switch to 3 gmt
		_daylight = (int) rules.getDaylightSavings(now).toSeconds();
	}

	@Override
	public void writeImpl(GameClient client, WritableBuffer buffer) {
		writeId(ServerPacketId.EX_ENTER_WORLD, buffer);
		buffer.writeInt(_epochInSeconds);
		buffer.writeInt(-_zoneIdOffsetSeconds);
		buffer.writeInt(_daylight);
		buffer.writeInt(Config.MAX_FREE_TELEPORT_LEVEL);
	}
}