/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.network.serverpackets;

import club.projectessence.commons.network.ServerPacketId;
import club.projectessence.gameserver.network.GameClient;
import io.github.joealisson.mmocore.WritableBuffer;

/**
 * Dialog with input field<br>
 * type 0 = char name (Selection screen)<br>
 * type 1 = clan name
 *
 * <AUTHOR> Benetis
 */
public class ExNeedToChangeName extends ServerPacket {
	public static final int TYPE_CHARACTER = 0;
	public static final int TYPE_CLAN = 1;

	public static final int SUBTYPE_MERGE = 0;
	public static final int SUBTYPE_ALREADY_EXIST = 1;

	private final int _type;
	private final int _subType;
	private final String _name;

	public ExNeedToChangeName(int type, int subType, String name) {
		super();
		_type = type;
		_subType = subType;
		_name = name;
	}

	@Override
	public void writeImpl(GameClient client, WritableBuffer buffer) {
		writeId(ServerPacketId.EX_NEED_TO_CHANGE_NAME, buffer);

		buffer.writeInt(_type);
		buffer.writeInt(_subType);
		buffer.writeString(_name);
	}
}
