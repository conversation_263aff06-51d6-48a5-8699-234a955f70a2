/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.network.serverpackets;

import club.projectessence.commons.network.ServerPacketId;
import club.projectessence.gameserver.model.Location;
import club.projectessence.gameserver.network.GameClient;
import io.github.joealisson.mmocore.WritableBuffer;

/**
 * <AUTHOR>
 */
public class ExMagicSkillUseGround extends ServerPacket {
	private final int _casterObjId;
	private final int _skillId;
	private final int _x;
	private final int _y;
	private final int _z;

	public ExMagicSkillUseGround(int casterObjId, int skillId, Location loc) {
		this(casterObjId, skillId, loc.getX(), loc.getY(), loc.getZ());
	}

	public ExMagicSkillUseGround(int casterObjId, int skillId, int x, int y, int z) {
		_casterObjId = casterObjId;
		_skillId = skillId;
		_x = x;
		_y = y;
		_z = z;
	}

	@Override
	public void writeImpl(GameClient client, WritableBuffer buffer) {
		writeId(ServerPacketId.EX_MAGIC_SKILL_USE_GROUND, buffer);
		buffer.writeInt(_casterObjId);
		buffer.writeInt(_skillId);
		buffer.writeInt(_x);
		buffer.writeInt(_y);
		buffer.writeInt(_z);
	}
}
