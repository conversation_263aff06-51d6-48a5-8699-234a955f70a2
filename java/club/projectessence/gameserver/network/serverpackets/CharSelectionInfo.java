/*
 * This file is part of the L2J Mobius project.
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.network.serverpackets;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.LinkedList;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

import club.projectessence.Config;
import club.projectessence.commons.database.DatabaseFactory;
import club.projectessence.commons.network.ServerPacketId;
import club.projectessence.gameserver.data.sql.ClanTable;
import club.projectessence.gameserver.data.xml.ExperienceData;
import club.projectessence.gameserver.enums.ClassId;
import club.projectessence.gameserver.instancemanager.PunishmentManager;
import club.projectessence.gameserver.model.CharSelectInfoPackage;
import club.projectessence.gameserver.model.VariationInstance;
import club.projectessence.gameserver.model.World;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.model.clan.Clan;
import club.projectessence.gameserver.model.itemcontainer.Inventory;
import club.projectessence.gameserver.model.olympiad.Hero;
import club.projectessence.gameserver.model.punishment.PunishmentAffect;
import club.projectessence.gameserver.model.punishment.PunishmentType;
import club.projectessence.gameserver.model.skills.CommonSkill;
import club.projectessence.gameserver.network.Disconnection;
import club.projectessence.gameserver.network.GameClient;
import io.github.joealisson.mmocore.WritableBuffer;

/**
 * 338: ddccdcc(SdSddddddddddffQQfddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddddhhhhhdddffdddcdddddddffddddcccddcd)
 */
public class CharSelectionInfo extends ServerPacket
{
	private static final Logger					LOGGER						= Logger.getLogger(CharSelectionInfo.class.getName());
	private static final int[]					PAPERDOLL_ORDER				= new int[]
	{
		Inventory.PAPERDOLL_UNDER,
		Inventory.PAPERDOLL_REAR,
		Inventory.PAPERDOLL_LEAR,
		Inventory.PAPERDOLL_NECK,
		Inventory.PAPERDOLL_RFINGER,
		Inventory.PAPERDOLL_LFINGER,
		Inventory.PAPERDOLL_HEAD,
		Inventory.PAPERDOLL_RHAND,
		Inventory.PAPERDOLL_LHAND,
		Inventory.PAPERDOLL_GLOVES,
		Inventory.PAPERDOLL_CHEST,
		Inventory.PAPERDOLL_LEGS,
		Inventory.PAPERDOLL_FEET,
		Inventory.PAPERDOLL_CLOAK,
		Inventory.PAPERDOLL_RHAND,
		Inventory.PAPERDOLL_HAIR,
		Inventory.PAPERDOLL_HAIR2,
		Inventory.PAPERDOLL_RBRACELET,
		Inventory.PAPERDOLL_LBRACELET,
		Inventory.PAPERDOLL_AGATHION1,																								// 152
		Inventory.PAPERDOLL_AGATHION2,																								// 152
		Inventory.PAPERDOLL_AGATHION3,																								// 152
		Inventory.PAPERDOLL_AGATHION4,																								// 152
		Inventory.PAPERDOLL_AGATHION5,																								// 152
		Inventory.PAPERDOLL_DECO1,
		Inventory.PAPERDOLL_DECO2,
		Inventory.PAPERDOLL_DECO3,
		Inventory.PAPERDOLL_DECO4,
		Inventory.PAPERDOLL_DECO5,
		Inventory.PAPERDOLL_DECO6,
		Inventory.PAPERDOLL_BELT,
		Inventory.PAPERDOLL_BROOCH,
		Inventory.PAPERDOLL_BROOCH_JEWEL1,
		Inventory.PAPERDOLL_BROOCH_JEWEL2,
		Inventory.PAPERDOLL_BROOCH_JEWEL3,
		Inventory.PAPERDOLL_BROOCH_JEWEL4,
		Inventory.PAPERDOLL_BROOCH_JEWEL5,
		Inventory.PAPERDOLL_BROOCH_JEWEL6,
		Inventory.PAPERDOLL_ARTIFACT_BOOK,																							// 152
		Inventory.PAPERDOLL_ARTIFACT1,																								// 152
		Inventory.PAPERDOLL_ARTIFACT2,																								// 152
		Inventory.PAPERDOLL_ARTIFACT3,																								// 152
		Inventory.PAPERDOLL_ARTIFACT4,																								// 152
		Inventory.PAPERDOLL_ARTIFACT5,																								// 152
		Inventory.PAPERDOLL_ARTIFACT6,																								// 152
		Inventory.PAPERDOLL_ARTIFACT7,																								// 152
		Inventory.PAPERDOLL_ARTIFACT8,																								// 152
		Inventory.PAPERDOLL_ARTIFACT9,																								// 152
		Inventory.PAPERDOLL_ARTIFACT10,																								// 152
		Inventory.PAPERDOLL_ARTIFACT11,																								// 152
		Inventory.PAPERDOLL_ARTIFACT12,																								// 152
		Inventory.PAPERDOLL_ARTIFACT13,																								// 152
		Inventory.PAPERDOLL_ARTIFACT14,																								// 152
		Inventory.PAPERDOLL_ARTIFACT15,																								// 152
		Inventory.PAPERDOLL_ARTIFACT16,																								// 152
		Inventory.PAPERDOLL_ARTIFACT17,																								// 152
		Inventory.PAPERDOLL_ARTIFACT18,																								// 152
		Inventory.PAPERDOLL_ARTIFACT19,																								// 152
		Inventory.PAPERDOLL_ARTIFACT20,																								// 152
		Inventory.PAPERDOLL_ARTIFACT21																								// 152
	};
	private static final int[]					PAPERDOLL_ORDER_VISUAL_ID	= new int[]
	{
		Inventory.PAPERDOLL_RHAND,
		Inventory.PAPERDOLL_LHAND,
		Inventory.PAPERDOLL_GLOVES,
		Inventory.PAPERDOLL_CHEST,
		Inventory.PAPERDOLL_LEGS,
		Inventory.PAPERDOLL_FEET,
		Inventory.PAPERDOLL_RHAND,
		Inventory.PAPERDOLL_HAIR,
		Inventory.PAPERDOLL_HAIR2,
	};
	private final String						_loginName;
	private final int							_sessionId;
	private final List<CharSelectInfoPackage>	_characterPackages;
	private int									_activeId;
	
	/**
	 * Constructor for CharSelectionInfo.
	 *
	 * @param loginName
	 * @param sessionId
	 */
	public CharSelectionInfo(String loginName, int sessionId)
	{
		_sessionId = sessionId;
		_loginName = loginName;
		_characterPackages = loadCharacterSelectInfo(_loginName);
		_activeId = -1;
	}
	
	public CharSelectionInfo(String loginName, int sessionId, int activeId)
	{
		_sessionId = sessionId;
		_loginName = loginName;
		_characterPackages = loadCharacterSelectInfo(_loginName);
		_activeId = activeId;
	}
	
	public static List<CharSelectInfoPackage> loadCharacterSelectInfo(String loginName)
	{
		CharSelectInfoPackage charInfopackage;
		final List<CharSelectInfoPackage> characterList = new LinkedList<>();
		try (Connection con = DatabaseFactory.getConnection(); PreparedStatement statement = con.prepareStatement("SELECT * FROM characters WHERE account_name=? ORDER BY createDate"))
		{
			statement.setString(1, loginName);
			try (ResultSet charList = statement.executeQuery())
			{
				while (charList.next()) // fills the package
				{
					// Handle Vanguard beast (Wild Evolution)
					final int objectId = charList.getInt("charId");
					final int classId = charList.getInt("classid");
					int vanguardBeastId = -1;
					if (classId == ClassId.VANGUARD_RIDER.getId()) // Vanguard 3rd Class
					{
						try (PreparedStatement tempPs = con.prepareStatement("SELECT 1 FROM character_skills WHERE charId=? AND class_index=? AND skill_id=?"))
						{
							tempPs.setInt(1, objectId);
							tempPs.setInt(2, 0);
							tempPs.setInt(3, CommonSkill.WILD_EVOLUTION.getId());
							try (ResultSet tempRs = tempPs.executeQuery())
							{
								if (tempRs.next())
								{
									vanguardBeastId = 4;
								}
							}
						}
					}
					if (vanguardBeastId < 0)
					{
						vanguardBeastId = ClassId.getClassId(classId).getVanguardBeastId();
					}
					charInfopackage = restoreChar(charList, vanguardBeastId);
					if (charInfopackage != null)
					{
						characterList.add(charInfopackage);
						final PlayerInstance player = World.getInstance().getPlayer(charInfopackage.getObjectId());
						if (player != null)
						{
							Disconnection.of(player).logout(false, false, false);
						}
					}
				}
			}
			return characterList;
		}
		catch (Exception e)
		{
			LOGGER.log(Level.WARNING, "Could not restore char info: " + e.getMessage(), e);
		}
		return characterList;
	}
	
	private static void loadCharacterSubclassInfo(CharSelectInfoPackage charInfopackage, int objectId, int activeClassId)
	{
		try (Connection con = DatabaseFactory.getConnection(); PreparedStatement statement = con.prepareStatement("SELECT exp, sp, level, sayha_grace_points FROM character_subclasses WHERE charId=? AND class_id=? ORDER BY charId"))
		{
			statement.setInt(1, objectId);
			statement.setInt(2, activeClassId);
			try (ResultSet charList = statement.executeQuery())
			{
				if (charList.next())
				{
					charInfopackage.setExp(charList.getLong("exp"));
					charInfopackage.setSp(charList.getLong("sp"));
					charInfopackage.setLevel(charList.getInt("level"));
					charInfopackage.setSayhaGracePoints(charList.getInt("sayha_grace_points"));
				}
			}
		}
		catch (Exception e)
		{
			LOGGER.log(Level.WARNING, "Could not restore char subclass info: " + e.getMessage(), e);
		}
	}
	
	public static CharSelectInfoPackage restoreChar(ResultSet chardata, int vanguardBeastId) throws Exception
	{
		final int objectId = chardata.getInt("charId");
		final String name = chardata.getString("char_name");
		// See if the char must be deleted
		final long deletetime = chardata.getLong("deletetime");
		if ((deletetime > 0) && (System.currentTimeMillis() > deletetime))
		{
			final Clan clan = ClanTable.getInstance().getClan(chardata.getInt("clanid"));
			if (clan != null)
			{
				clan.removeClanMember(objectId, 0);
			}
			GameClient.deleteCharByObjId(objectId);
			return null;
		}
		final CharSelectInfoPackage charInfopackage = new CharSelectInfoPackage(objectId, name);
		charInfopackage.setAccessLevel(chardata.getInt("accesslevel"));
		charInfopackage.setLevel(chardata.getInt("level"));
		charInfopackage.setMaxHp(chardata.getInt("maxhp"));
		charInfopackage.setCurrentHp(chardata.getDouble("curhp"));
		charInfopackage.setMaxMp(chardata.getInt("maxmp"));
		charInfopackage.setCurrentMp(chardata.getDouble("curmp"));
		charInfopackage.setReputation(chardata.getInt("reputation"));
		charInfopackage.setPkKills(chardata.getInt("pkkills"));
		charInfopackage.setPvPKills(chardata.getInt("pvpkills"));
		charInfopackage.setFace(chardata.getInt("face"));
		charInfopackage.setHairStyle(chardata.getInt("hairstyle"));
		charInfopackage.setHairColor(chardata.getInt("haircolor"));
		charInfopackage.setSex(chardata.getInt("sex"));
		charInfopackage.setExp(chardata.getLong("exp"));
		charInfopackage.setSp(chardata.getLong("sp"));
		charInfopackage.setSayhaGracePoints(chardata.getInt("sayha_grace_points"));
		charInfopackage.setClanId(chardata.getInt("clanid"));
		charInfopackage.setRace(chardata.getInt("race"));
		final int baseClassId = chardata.getInt("base_class");
		final int activeClassId = chardata.getInt("classid");
		charInfopackage.setX(chardata.getInt("x"));
		charInfopackage.setY(chardata.getInt("y"));
		charInfopackage.setZ(chardata.getInt("z"));
		final int faction = chardata.getInt("faction");
		if (faction == 1)
		{
			charInfopackage.setFire();
		}
		if (faction == 2)
		{
			charInfopackage.setWater();
		}
		// if is in subclass, load subclass exp, sp, level info
		if (baseClassId != activeClassId)
		{
			loadCharacterSubclassInfo(charInfopackage, objectId, activeClassId);
		}
		charInfopackage.setClassId(activeClassId);
		charInfopackage.setVanguardBeastId(vanguardBeastId);
		// Get the augmentation id for equipped weapon
		int weaponObjId = charInfopackage.getPaperdollObjectId(Inventory.PAPERDOLL_RHAND);
		if (weaponObjId < 1)
		{
			weaponObjId = charInfopackage.getPaperdollObjectId(Inventory.PAPERDOLL_RHAND);
		}
		if (weaponObjId > 0)
		{
			try (Connection con = DatabaseFactory.getConnection(); PreparedStatement statement = con.prepareStatement("SELECT mineralId,option1,option2 FROM item_variations WHERE itemId=?"))
			{
				statement.setInt(1, weaponObjId);
				try (ResultSet result = statement.executeQuery())
				{
					if (result.next())
					{
						final int mineralId = result.getInt("mineralId");
						final int option1 = result.getInt("option1");
						final int option2 = result.getInt("option2");
						if ((option1 != -1) && (option2 != -1))
						{
							charInfopackage.setAugmentation(new VariationInstance(mineralId, option1, option2));
						}
					}
				}
			}
			catch (Exception e)
			{
				LOGGER.log(Level.WARNING, "Could not restore augmentation info: " + e.getMessage(), e);
			}
		}
		// Check if the base class is set to zero and also doesn't match with the current active class, otherwise send the base class ID. This prevents chars created before base class was introduced from being displayed incorrectly.
		if ((baseClassId == 0) && (activeClassId > 0))
		{
			charInfopackage.setBaseClassId(activeClassId);
		}
		else
		{
			charInfopackage.setBaseClassId(baseClassId);
		}
		charInfopackage.setDeleteTimer(deletetime);
		charInfopackage.setLastAccess(chardata.getLong("lastAccess"));
		charInfopackage.setNoble(chardata.getInt("nobless") == 1);
		return charInfopackage;
	}
	
	public List<CharSelectInfoPackage> getCharInfo()
	{
		return _characterPackages;
	}
	
	@Override
	public void writeImpl(GameClient client, WritableBuffer buffer)
	{
		writeId(ServerPacketId.CHARACTER_SELECTION_INFO, buffer);
		/**
		 * 338: ddccdcc
		 */
		final int size = _characterPackages.size();
		buffer.writeInt(size); // Created character count
		buffer.writeInt(Config.MAX_CHARACTERS_NUMBER_PER_ACCOUNT); // Can prevent players from creating new characters (if 0); (if 1, the client will ask if chars may be created (0x13) Response: (0x0D) )
		buffer.writeByte(size == Config.MAX_CHARACTERS_NUMBER_PER_ACCOUNT ? 0x01 : 0x00); // if 1 can't create new char
		buffer.writeByte(0x01); // 0=can't play, 1=can play free until level 85, 2=100% free play
		buffer.writeInt(0x02); // if 1, Korean client
		buffer.writeByte(0x00); // Gift message for inactive accounts // 152
		buffer.writeByte(0x00); // Balthus Knights, if 1 suggests premium account
		long lastAccess = 0;
		if (_activeId == -1)
		{
			for (int i = 0; i < size; i++)
			{
				if (lastAccess < _characterPackages.get(i).getLastAccess())
				{
					lastAccess = _characterPackages.get(i).getLastAccess();
					_activeId = i;
				}
			}
		}
		/**
		 * 338: (SdSddddddddddffQQ
		 */
		for (int i = 0; i < size; i++)
		{
			final CharSelectInfoPackage charInfoPackage = _characterPackages.get(i);
			buffer.writeString(charInfoPackage.getName()); // Character name
			buffer.writeInt(charInfoPackage.getObjectId()); // Character ID
			buffer.writeString(_loginName); // Account name
			buffer.writeInt(_sessionId); // Account ID
			buffer.writeInt(0x00); // Clan ID
			buffer.writeInt(0x00); // Builder level
			buffer.writeInt(charInfoPackage.getSex()); // Sex
			buffer.writeInt(charInfoPackage.getRace()); // Race
			if (charInfoPackage.getClassId() == charInfoPackage.getBaseClassId())
			{
				buffer.writeInt(charInfoPackage.getClassId());
			}
			else
			{
				buffer.writeInt(charInfoPackage.getBaseClassId());
			}
			buffer.writeInt(0x01); // GameServerName
			buffer.writeInt(charInfoPackage.getX());
			buffer.writeInt(charInfoPackage.getY());
			buffer.writeInt(charInfoPackage.getZ());
			buffer.writeDouble(charInfoPackage.getCurrentHp());
			buffer.writeDouble(charInfoPackage.getCurrentMp());
			buffer.writeLong(charInfoPackage.getSp());
			buffer.writeLong(charInfoPackage.getExp());
			/**
			 * 338: fddddddddddddd
			 */
			buffer.writeDouble((float) (charInfoPackage.getExp() - ExperienceData.getInstance().getExpForLevel(charInfoPackage.getLevel())) / (ExperienceData.getInstance().getExpForLevel(charInfoPackage.getLevel() + 1) - ExperienceData.getInstance().getExpForLevel(charInfoPackage.getLevel())));
			buffer.writeInt(charInfoPackage.getLevel());
			buffer.writeInt(charInfoPackage.getReputation());
			buffer.writeInt(charInfoPackage.getPkKills());
			buffer.writeInt(charInfoPackage.getPvPKills());
			buffer.writeInt(0x00);
			buffer.writeInt(0x00);
			buffer.writeInt(0x00);
			buffer.writeInt(0x00);
			buffer.writeInt(0x00);
			buffer.writeInt(0x00);
			buffer.writeInt(0x00);
			buffer.writeInt(0x00); // Ertheia
			buffer.writeInt(0x00); // Ertheia
			/**
			 * 338: dddddddddddddddddddddddddddddddddddddddddddddddddddddddddddd
			 */
			for (int slot : getPaperdollOrder())
			{
				buffer.writeInt(charInfoPackage.getPaperdollItemId(slot));
			}
			/**
			 * 338: ddddddddd
			 */
			for (int slot : getPaperdollOrderVisualId())
			{
				buffer.writeInt(charInfoPackage.getPaperdollItemVisualId(slot));
			}
			/**
			 * 338: hhhhhdddff
			 */
			buffer.writeShort(charInfoPackage.getEnchantEffect(Inventory.PAPERDOLL_CHEST)); // Upper Body enchant level
			buffer.writeShort(charInfoPackage.getEnchantEffect(Inventory.PAPERDOLL_LEGS)); // Lower Body enchant level
			buffer.writeShort(charInfoPackage.getEnchantEffect(Inventory.PAPERDOLL_HEAD)); // Headgear enchant level
			buffer.writeShort(charInfoPackage.getEnchantEffect(Inventory.PAPERDOLL_GLOVES)); // Gloves enchant level
			buffer.writeShort(charInfoPackage.getEnchantEffect(Inventory.PAPERDOLL_FEET)); // Boots enchant level
			buffer.writeInt(charInfoPackage.getHairStyle());
			buffer.writeInt(charInfoPackage.getHairColor());
			buffer.writeInt(charInfoPackage.getFace());
			buffer.writeDouble(charInfoPackage.getMaxHp()); // Maximum HP
			buffer.writeDouble(charInfoPackage.getMaxMp()); // Maximum MP
			/**
			 * 338: dddcdddddddff
			 */
			buffer.writeInt(charInfoPackage.getDeleteTimer() > 0 ? (int) ((charInfoPackage.getDeleteTimer() - System.currentTimeMillis()) / 1000) : 0);
			buffer.writeInt(charInfoPackage.getClassId());
			buffer.writeInt(i == _activeId ? 1 : 0);
			buffer.writeByte(charInfoPackage.getEnchantEffect(Inventory.PAPERDOLL_RHAND) > 127 ? 127 : charInfoPackage.getEnchantEffect(Inventory.PAPERDOLL_RHAND));
			buffer.writeInt(charInfoPackage.getAugmentation() != null ? charInfoPackage.getAugmentation().getOption1Id() : 0);
			buffer.writeInt(charInfoPackage.getAugmentation() != null ? charInfoPackage.getAugmentation().getOption2Id() : 0);
			// buffer.writeInt(charInfoPackage.getTransformId()); // Used to display Transformations
			buffer.writeInt(0x00); // Currently on retail when you are on character select you don't see your transformation.
			buffer.writeInt(0x00); // Pet NpcId
			buffer.writeInt(0x00); // Pet level
			buffer.writeInt(0x00); // Pet Food
			buffer.writeInt(0x00); // Pet Food Level
			buffer.writeDouble(0x00); // Current pet HP
			buffer.writeDouble(0x00); // Current pet MP
			/**
			 * 338: ddddccc
			 */
			buffer.writeInt(charInfoPackage.getSayhaGracePoints()); // Sayha's Grace
			buffer.writeInt((int) Config.RATE_SAYHA_GRACE_EXP_MULTIPLIER * 100); // Sayha's Grace Percent
			buffer.writeInt(charInfoPackage.getSayhaGraceItemsUsed()); // Remaining Sayha's Grace item uses
			boolean isAccBan = PunishmentManager.getInstance().hasPunishment(_loginName, PunishmentAffect.ACCOUNT, PunishmentType.BAN);
			boolean isCharBan = PunishmentManager.getInstance().hasPunishment(charInfoPackage.getObjectId(), PunishmentAffect.CHARACTER, PunishmentType.BAN);
			buffer.writeInt(((charInfoPackage.getAccessLevel() < 0) || isCharBan || isAccBan) ? 0x00 : 0x01); // Char is active or not
			buffer.writeByte(charInfoPackage.isNoble() ? 0x01 : 0x00);
			buffer.writeByte(Hero.getInstance().isHero(charInfoPackage.getObjectId()) ? 0x02 : 0x00); // Hero glow
			buffer.writeByte(charInfoPackage.isHairAccessoryEnabled() ? 0x01 : 0x00); // Show hair accessory if enabled
			/**
			 * 338: dd
			 */
			if (!isCharBan && !isAccBan)
			{
				buffer.writeInt(0x00);
			}
			else
			{
				long expiration = -1;
				if (isCharBan)
				{
					expiration = PunishmentManager.getInstance().getPunishmentExpiration(charInfoPackage.getObjectId(), PunishmentAffect.CHARACTER, PunishmentType.BAN);
				}
				else if (isAccBan)
				{
					expiration = PunishmentManager.getInstance().getPunishmentExpiration(_loginName, PunishmentAffect.ACCOUNT, PunishmentType.BAN);
				}
				expiration = expiration == 0 ? -1 : (expiration - System.currentTimeMillis()) / 1000;
				buffer.writeInt((int) expiration); // 235 - ban time left
			}
			buffer.writeInt((int) (charInfoPackage.getLastAccess())); // 235 - last play time
			buffer.writeByte(0x00); // 338
			buffer.writeInt(0x00); // 338 _player.getVisualHairColor() + 1 (DK color?)
			buffer.writeByte(charInfoPackage.getVanguardBeastId()); // 362
		}
	}
	
	@Override
	public int[] getPaperdollOrder()
	{
		return PAPERDOLL_ORDER;
	}
	
	@Override
	public int[] getPaperdollOrderVisualId()
	{
		return PAPERDOLL_ORDER_VISUAL_ID;
	}
}
