/*
 * This file is part of the L2J Mobius project.
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.network.serverpackets;

import java.util.Set;

import club.projectessence.Config;
import club.projectessence.commons.network.ServerPacketId;
import club.projectessence.gameserver.enums.ClassId;
import club.projectessence.gameserver.instancemanager.CastleManager;
import club.projectessence.gameserver.instancemanager.CursedWeaponsManager;
import club.projectessence.gameserver.instancemanager.RankManager;
import club.projectessence.gameserver.model.CharSelectInfoPackage;
import club.projectessence.gameserver.model.VariationInstance;
import club.projectessence.gameserver.model.actor.instance.BuffzoneNpcInstance;
import club.projectessence.gameserver.model.actor.instance.DecoyInstance;
import club.projectessence.gameserver.features.museum.MuseumStatueInstance;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.model.ceremonyofchaos.CeremonyOfChaosEvent;
import club.projectessence.gameserver.model.ceremonyofchaos.CeremonyOfChaosMember;
import club.projectessence.gameserver.model.interfaces.ILocational;
import club.projectessence.gameserver.model.itemcontainer.Inventory;
import club.projectessence.gameserver.model.skills.AbnormalVisualEffect;
import club.projectessence.gameserver.model.zone.ZoneId;
import club.projectessence.gameserver.network.GameClient;
import io.github.joealisson.mmocore.WritableBuffer;

/**
 * 338: cdddddShcdddddddddddddddddddcdddddddddcdddhhhhhhhhffffdddSddddccccccch(h)cchdddccdcccdddddchdcdddcdddddcd(h)cccdddhcdcdd
 */
public class CharInfo extends ServerPacket
{
	private static final int[]				PAPERDOLL_ORDER	= new int[]
	{
		Inventory.PAPERDOLL_UNDER,
		Inventory.PAPERDOLL_HEAD,
		Inventory.PAPERDOLL_RHAND,
		Inventory.PAPERDOLL_LHAND,
		Inventory.PAPERDOLL_GLOVES,
		Inventory.PAPERDOLL_CHEST,
		Inventory.PAPERDOLL_LEGS,
		Inventory.PAPERDOLL_FEET,
		Inventory.PAPERDOLL_CLOAK,
		Inventory.PAPERDOLL_RHAND,
		Inventory.PAPERDOLL_HAIR,
		Inventory.PAPERDOLL_HAIR2
	};
	private final PlayerInstance			_player;
	private final int						_mAtkSpd;
	private final int						_pAtkSpd;
	private final int						_runSpd;
	private final int						_walkSpd;
	private final int						_swimRunSpd;
	private final int						_swimWalkSpd;
	private final int						_flyRunSpd;
	private final int						_flyWalkSpd;
	private final double					_moveMultiplier;
	private final float						_attackSpeedMultiplier;
	private final boolean					_gmSeeInvis;
	private int								_objId;
	private int								_x;
	private int								_y;
	private int								_z;
	private int								_heading;
	private int								_enchantLevel	= 0;
	private int								_armorEnchant	= 0;
	private int								_vehicleId		= 0;
	private final AbnormalVisualEffect[]	_additionalAves;
	private MuseumStatueInstance			_museumStatue;
	
	public CharInfo(PlayerInstance player, boolean gmSeeInvis, AbnormalVisualEffect... additionalAves)
	{
		_player = player;
		_objId = player.getObjectId();
		if ((_player.getVehicle() != null) && (_player.getInVehiclePosition() != null))
		{
			_x = _player.getInVehiclePosition().getX();
			_y = _player.getInVehiclePosition().getY();
			_z = _player.getInVehiclePosition().getZ();
			_vehicleId = _player.getVehicle().getObjectId();
		}
		else
		{
			_x = _player.getX();
			_y = _player.getY();
			_z = _player.getZ();
		}
		_heading = _player.getHeading();
		_mAtkSpd = _player.getMAtkSpd();
		_pAtkSpd = _player.getPAtkSpd();
		_attackSpeedMultiplier = (float) _player.getAttackSpeedMultiplier();
		_moveMultiplier = player.getMovementSpeedMultiplier();
		_runSpd = (int) Math.round(player.getRunSpeed() / _moveMultiplier);
		_walkSpd = (int) Math.round(player.getWalkSpeed() / _moveMultiplier);
		_swimRunSpd = (int) Math.round(player.getSwimRunSpeed() / _moveMultiplier);
		_swimWalkSpd = (int) Math.round(player.getSwimWalkSpeed() / _moveMultiplier);
		_flyRunSpd = player.isFlying() ? _runSpd : 0;
		_flyWalkSpd = player.isFlying() ? _walkSpd : 0;
		_enchantLevel = player.getInventory().getWeaponEnchant();
		_armorEnchant = player.getInventory().getArmorMinEnchant();
		_gmSeeInvis = gmSeeInvis;
		_additionalAves = additionalAves;
	}
	
	public CharInfo(PlayerInstance player, boolean gmSeeInvis)
	{
		this(player, gmSeeInvis, null);
	}
	
	public CharInfo(DecoyInstance decoy, boolean gmSeeInvis, AbnormalVisualEffect... additionalAves)
	{
		this(decoy.getActingPlayer(), gmSeeInvis, additionalAves); // init
		_objId = decoy.getObjectId();
		_x = decoy.getX();
		_y = decoy.getY();
		_z = decoy.getZ();
		_heading = decoy.getHeading();
	}
	
	public CharInfo(DecoyInstance decoy, boolean gmSeeInvis)
	{
		this(decoy, gmSeeInvis, null);
	}

	/**
	 * Constructor for museum statue display - simplified approach
	 */
	public CharInfo(MuseumStatueInstance statue, boolean gmSeeInvis)
	{
		// Initialize with null player for museum statue
		_player = null;
		_objId = statue.getObjectId();
		_x = statue.getX();
		_y = statue.getY();
		_z = statue.getZ();
		_heading = statue.getHeading();
		_vehicleId = 0;

		// Set default values for museum statue
		_mAtkSpd = 0;
		_pAtkSpd = 0;
		_attackSpeedMultiplier = 1.0f;
		_moveMultiplier = 1.0;
		_runSpd = 0;
		_walkSpd = 0;
		_swimRunSpd = 0;
		_swimWalkSpd = 0;
		_flyRunSpd = 0;
		_flyWalkSpd = 0;
		_enchantLevel = 0;
		_armorEnchant = 0;
		_gmSeeInvis = false; // Never show as invisible
		_additionalAves = null; // No abnormal effects

		// Store statue reference for writeImpl
		_museumStatue = statue;
	}
	
	public CharInfo(BuffzoneNpcInstance buffzone, boolean gmSeeInvis, AbnormalVisualEffect... additionalAves)
	{
		this(buffzone.getActingPlayer(), gmSeeInvis, additionalAves); // init
		_objId = buffzone.getObjectId();
		_x = buffzone.getX();
		_y = buffzone.getY();
		_z = buffzone.getZ();
		_heading = buffzone.getHeading();
	}
	
	public CharInfo(BuffzoneNpcInstance buffzone, boolean gmSeeInvis)
	{
		this(buffzone, gmSeeInvis, null);
	}
	
	@Override
	public void writeImpl(GameClient client, WritableBuffer buffer)
	{
		// Check if this is a museum statue
		if (_museumStatue != null) {
			writeMuseumStatueInfo(client, buffer);
			return;
		}

		writeId(ServerPacketId.CHAR_INFO, buffer);
		final CeremonyOfChaosEvent event = _player.getEvent(CeremonyOfChaosEvent.class);
		final CeremonyOfChaosMember cocPlayer = event != null ? event.getMember(_player.getObjectId()) : null;
		buffer.writeByte(0x00); // Grand Crusade
		buffer.writeInt(_x); // Confirmed
		buffer.writeInt(_y); // Confirmed
		buffer.writeInt(_z); // Confirmed
		buffer.writeInt(_vehicleId); // Confirmed
		buffer.writeInt(_objId); // Confirmed
		buffer.writeString(_player.getAppearance().getVisibleName()); // Confirmed
		buffer.writeShort(_player.getRace().ordinal()); // Confirmed
		buffer.writeByte(_player.getAppearance().isFemale() ? 0x01 : 0x00); // Confirmed
		buffer.writeInt(ClassId.getClassId(_player.getBaseTemplate().getClassId().getId()).getRootClassId().getId());
		for (int slot : getPaperdollOrder())
		{
			int visualId = _player.getInventory().getPaperdollItemVisualId(slot);
			if ((slot == Inventory.PAPERDOLL_CLOAK) && (visualId > 0))
			{
				buffer.writeInt(_player.getInventory().getPaperdollItemVisualId(slot));
			}
			else
			{
				buffer.writeInt(_player.getInventory().getPaperdollItemDisplayId(slot)); // Confirmed
			}
		}
		for (int slot : getPaperdollOrderAugument())
		{
			final VariationInstance augment = _player.getInventory().getPaperdollAugmentation(slot);
			buffer.writeInt(augment != null ? augment.getOption1Id() : 0); // Confirmed
			buffer.writeInt(augment != null ? augment.getOption2Id() : 0); // Confirmed
		}
		buffer.writeByte(_armorEnchant);
		for (int slot : getPaperdollOrderVisualId())
		{
			buffer.writeInt(_player.getInventory().getPaperdollItemVisualId(slot));
		}
		buffer.writeByte(_player.getPvpFlag());
		buffer.writeInt(_player.getReputation());
		buffer.writeInt(_mAtkSpd);
		buffer.writeInt(_pAtkSpd);
		buffer.writeShort(_runSpd);
		buffer.writeShort(_walkSpd);
		buffer.writeShort(_swimRunSpd);
		buffer.writeShort(_swimWalkSpd);
		buffer.writeShort(_flyRunSpd);
		buffer.writeShort(_flyWalkSpd);
		buffer.writeShort(_flyRunSpd);
		buffer.writeShort(_flyWalkSpd);
		buffer.writeDouble(_moveMultiplier);
		buffer.writeDouble(_attackSpeedMultiplier);
		buffer.writeDouble(_player.getCollisionRadius());
		buffer.writeDouble(_player.getCollisionHeight());
		buffer.writeInt(_player.getVisualHair());
		buffer.writeInt(_player.getVisualHairColor());
		buffer.writeInt(_player.getVisualFace());
		buffer.writeString(/* _gmSeeInvis ? "Invisible" : */_player.getAppearance().getVisibleTitle());
		buffer.writeInt(_player.getAppearance().getVisibleClanId());
		buffer.writeInt(_player.getAppearance().getVisibleClanCrestId());
		buffer.writeInt(_player.getAppearance().getVisibleAllyId());
		buffer.writeInt(_player.getAppearance().getVisibleAllyCrestId());
		buffer.writeByte(_player.isSitting() ? 0x00 : 0x01); // Confirmed
		buffer.writeByte(_player.isRunning() ? 0x01 : 0x00); // Confirmed
		buffer.writeByte(_player.isInCombat() ? 0x01 : 0x00); // Confirmed
		buffer.writeByte(!_player.isInOlympiadMode() && _player.isAlikeDead() ? 0x01 : 0x00); // Confirmed
		buffer.writeByte(_player.isInvisible() ? 0x01 : 0x00);
		buffer.writeByte(_player.getMountType().ordinal()); // 1-on Strider, 2-on Wyvern, 3-on Great Wolf, 0-no mount
		buffer.writeByte(_player.getPrivateStoreType().getId()); // Confirmed
		buffer.writeShort(_player.getCubics().size()); // Confirmed
		_player.getCubics().keySet().forEach(buffer::writeShort);
		buffer.writeByte(_player.isInMatchingRoom() ? 0x01 : 0x00); // Confirmed
		buffer.writeByte(_player.isInsideZone(ZoneId.WATER) ? 1 : _player.isFlyingMounted() ? 2 : 0);
		buffer.writeShort(_player.getRecomHave()); // Confirmed
		buffer.writeInt(_player.getMountNpcId() == 0 ? 0 : _player.getMountNpcId() + 1000000);
		buffer.writeInt(_player.getClassId().getId()); // Confirmed
		buffer.writeInt(0x00); // TODO: Find me!
		buffer.writeByte(_player.isMounted() ? 0 : _enchantLevel); // Confirmed
		buffer.writeByte(_player.getTeam().getId()); // Confirmed
		buffer.writeInt(_player.getClanCrestLargeId());
		buffer.writeByte(_player.isNoble() ? 1 : 0); // Confirmed
		buffer.writeByte(_player.isHero() || (_player.isGM() && Config.GM_HERO_AURA) ? 2 : 0); // 152 - Value for enabled changed to 2?
		buffer.writeByte(_player.isFishing() ? 1 : 0); // Confirmed
		final ILocational baitLocation = _player.getFishing().getBaitLocation();
		if (baitLocation != null)
		{
			buffer.writeInt(baitLocation.getX()); // Confirmed
			buffer.writeInt(baitLocation.getY()); // Confirmed
			buffer.writeInt(baitLocation.getZ()); // Confirmed
		}
		else
		{
			buffer.writeInt(0);
			buffer.writeInt(0);
			buffer.writeInt(0);
		}
		buffer.writeInt(_player.getAppearance().getNameColor()); // Confirmed
		buffer.writeInt(_heading); // Confirmed
		buffer.writeByte(_player.getPledgeClass());
		buffer.writeShort(_player.getPledgeType());
		buffer.writeInt(_player.getAppearance().getTitleColor()); // Confirmed
		buffer.writeByte(_player.isCursedWeaponEquipped() ? CursedWeaponsManager.getInstance().getLevel(_player.getCursedWeaponEquippedId()) : 0);
		buffer.writeInt(0x00); // clan rep
		buffer.writeInt(_player.getTransformationDisplayId()); // Confirmed
		buffer.writeInt(_player.getAgathionId()); // Confirmed
		buffer.writeByte(0x00); // TODO: Find me!
		buffer.writeInt((int) Math.round(_player.getCurrentCp())); // Confirmed
		buffer.writeInt(_player.getMaxHp()); // Confirmed
		buffer.writeInt((int) Math.round(_player.getCurrentHp())); // Confirmed
		buffer.writeInt(_player.getMaxMp()); // Confirmed
		buffer.writeInt((int) Math.round(_player.getCurrentMp())); // Confirmed
		buffer.writeByte(0x00); // TODO: Find me!
		final Set<AbnormalVisualEffect> abnormalVisualEffects = _player.getEffectList().getAbnormalVisualEffectsFiltered();
		buffer.writeInt(abnormalVisualEffects.size() + (_gmSeeInvis ? 1 : 0) + (_additionalAves != null ? _additionalAves.length : 0)); // Confirmed
		for (AbnormalVisualEffect abnormalVisualEffect : abnormalVisualEffects)
		{
			buffer.writeShort(abnormalVisualEffect.getClientId()); // Confirmed
		}
		if (_gmSeeInvis)
		{
			buffer.writeShort(AbnormalVisualEffect.STEALTH.getClientId());
		}
		if (_additionalAves != null)
		{
			for (AbnormalVisualEffect ave : _additionalAves)
			{
				buffer.writeShort(ave.getClientId());
			}
		}
		buffer.writeByte(cocPlayer != null ? cocPlayer.getPosition() : _player.isTrueHero() ? 100 : 0);
		buffer.writeByte(_player.isHairAccessoryEnabled() ? 0x01 : 0x00); // Hair accessory
		buffer.writeByte(_player.getAbilityPointsUsed()); // Used Ability Points
		buffer.writeInt(0x00);
		// AFK animation. d
		if ((_player.getClan() != null) && (CastleManager.getInstance().getCastleByOwner(_player.getClan()) != null))
		{
			buffer.writeInt(_player.isClanLeader() ? 100 : 101);
		}
		else
		{
			buffer.writeInt(0x00);
		}
		// Rank. d
		buffer.writeInt(RankManager.getInstance().getPlayerRealRank(_player.getObjectId()) == 1 ? 1 : RankManager.getInstance().getPlayerRealRaceRank(_player.getObjectId()) == 1 ? 2 : 0);
		// hcdcdd
		buffer.writeShort(0x00); // 338
		buffer.writeByte(0x00); // 338 Antharas / Aden new Sieges ?
		buffer.writeInt(0x00); // 338
		buffer.writeByte(0x00); // 338
		buffer.writeInt(0x00); // 338 _player.getVisualHairColor() + 1 (DK color?)
		buffer.writeInt(0x00); // 338
		buffer.writeByte(_player.getVanguardBeastId()); // 362
	}
	
	@Override
	public int[] getPaperdollOrder()
	{
		return PAPERDOLL_ORDER;
	}

	/**
	 * Write museum statue info using CharSelectInfoPackage data
	 */
	private void writeMuseumStatueInfo(GameClient client, WritableBuffer buffer)
	{
		CharSelectInfoPackage charLooks = _museumStatue.getCharLooks();

		if (charLooks == null) {
			return;
		}

		writeId(ServerPacketId.CHAR_INFO, buffer);

		// Match CharInfo structure exactly
		buffer.writeByte(0x00); // Grand Crusade
		buffer.writeInt(_x);
		buffer.writeInt(_y);
		buffer.writeInt(_z);
		buffer.writeInt(_vehicleId);
		buffer.writeInt(_objId);
		buffer.writeString(charLooks.getName());
		buffer.writeShort(charLooks.getRace());
		buffer.writeByte(charLooks.getSex() == 1 ? 0x01 : 0x00);
		buffer.writeInt(charLooks.getBaseClassId());

		// Equipment (paperdoll) - use exact order from CharInfo
		for (int slot : getPaperdollOrder()) {
			buffer.writeInt(charLooks.getPaperdollItemId(slot));
		}

		// Augmentations (none for statues)
		for (int slot : getPaperdollOrderAugument()) {
			buffer.writeInt(0); // option1Id
			buffer.writeInt(0); // option2Id
		}

		buffer.writeByte(_armorEnchant);

		// Visual IDs (same as item IDs)
		for (int slot : getPaperdollOrderVisualId()) {
			buffer.writeInt(charLooks.getPaperdollItemId(slot));
		}

		// Basic statue info
		buffer.writeByte(0); // pvp flag
		buffer.writeInt(0); // reputation
		buffer.writeInt(_mAtkSpd);
		buffer.writeInt(_pAtkSpd);
		buffer.writeShort(_runSpd);
		buffer.writeShort(_walkSpd);
		buffer.writeShort(_swimRunSpd);
		buffer.writeShort(_swimWalkSpd);
		buffer.writeShort(_flyRunSpd);
		buffer.writeShort(_flyWalkSpd);
		buffer.writeShort(_flyRunSpd);
		buffer.writeShort(_flyWalkSpd);
		buffer.writeDouble(_moveMultiplier);
		buffer.writeDouble(_attackSpeedMultiplier);
		buffer.writeDouble(8.0); // collision radius
		buffer.writeDouble(23.0); // collision height
		buffer.writeInt(charLooks.getHairStyle());
		buffer.writeInt(charLooks.getHairColor());
		buffer.writeInt(charLooks.getFace());
		buffer.writeString(_museumStatue.getTitle());

		// Clan info (none)
		buffer.writeInt(0); // clan id
		buffer.writeInt(0); // clan crest
		buffer.writeInt(0); // ally id
		buffer.writeInt(0); // ally crest

		// Status flags
		buffer.writeByte(0x01); // sitting (standing)
		buffer.writeByte(0x00); // running
		buffer.writeByte(0x00); // in combat
		buffer.writeByte(0x00); // dead
		buffer.writeByte(0x00); // invisible
		buffer.writeByte(0x00); // mount type
		buffer.writeByte(0x00); // private store

		// Cubics (none)
		buffer.writeShort(0);

		// More status
		buffer.writeByte(0x00); // matching room
		buffer.writeByte(0x00); // water/flying
		buffer.writeShort(0); // recommendations
		buffer.writeInt(0); // mount npc id
		buffer.writeInt(charLooks.getClassId());
		buffer.writeInt(0x00); // unknown
		buffer.writeByte(_enchantLevel);
		buffer.writeByte(0x00); // team
		buffer.writeInt(0); // clan crest large
		buffer.writeByte(charLooks.isNoble() ? 1 : 0);
		buffer.writeByte(0); // hero
		buffer.writeByte(0); // fishing

		// Fishing location (none)
		buffer.writeInt(0);
		buffer.writeInt(0);
		buffer.writeInt(0);

		// Name color (white)
		buffer.writeInt(0xFFFFFF); // name color
		buffer.writeInt(_heading);
		buffer.writeByte(0); // pledge class
		buffer.writeShort(0); // pledge type
		buffer.writeInt(0xFFFF77); // title color
		buffer.writeByte(0); // cursed weapon
		buffer.writeInt(0); // clan rep
		buffer.writeInt(0); // transformation
		buffer.writeInt(0); // agathion
		buffer.writeByte(0x00); // unknown

		// HP/MP (full for display)
		buffer.writeInt(1000); // current cp
		buffer.writeInt(1000); // max hp
		buffer.writeInt(1000); // current hp
		buffer.writeInt(1000); // max mp
		buffer.writeInt(1000); // current mp
		buffer.writeByte(0x00); // unknown

		// Abnormal effects (add both stone and hero weapon effects for beautiful statue appearance)
		buffer.writeInt(2); // Two effects: stone + hero weapon
		buffer.writeShort(AbnormalVisualEffect.FLESH_STONE.getClientId());
		buffer.writeShort(AbnormalVisualEffect.V_WORLDCASTLEWAR_HERO_WEAPON_AVE.getClientId());

		// Final fields
		buffer.writeByte(0); // ceremony position
		buffer.writeByte(0x00); // hair accessory
		buffer.writeByte(0); // ability points
		buffer.writeInt(0x00); // unknown
		buffer.writeInt(0x00); // castle owner
		buffer.writeInt(0); // rank
		buffer.writeShort(0x00);
		buffer.writeByte(0x00);
		buffer.writeInt(0x00);
		buffer.writeByte(0x00);
		buffer.writeInt(0x00);
		buffer.writeInt(0x00);
		buffer.writeByte(0); // vanguard beast


	}
}
