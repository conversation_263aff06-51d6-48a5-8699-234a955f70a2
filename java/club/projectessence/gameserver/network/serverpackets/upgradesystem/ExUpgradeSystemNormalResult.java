/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.network.serverpackets.upgradesystem;

import club.projectessence.commons.network.ServerPacketId;
import club.projectessence.gameserver.model.holders.ItemHolder;
import club.projectessence.gameserver.network.GameClient;
import club.projectessence.gameserver.network.serverpackets.ServerPacket;
import io.github.joealisson.mmocore.WritableBuffer;

import java.util.List;

/**
 * <AUTHOR>
 */
public class ExUpgradeSystemNormalResult extends ServerPacket {
	private final List<ItemHolder> _items;
	private final boolean _success;

	public ExUpgradeSystemNormalResult(List<ItemHolder> items, boolean success) {
		_items = items;
		_success = success;
	}

	@Override
	public void writeImpl(GameClient client, WritableBuffer buffer) {
		writeId(ServerPacketId.EX_UPGRADE_SYSTEM_NORMAL_RESULT, buffer);
		if ((_items == null) || (_items.size() == 0)) {
			buffer.writeShort(0);
			return;
		}
		buffer.writeShort(0x01); // 1 - animation, 2 - no animation
		buffer.writeInt(0x0); // obj?
		buffer.writeShort(_success ? 0x01 : 0x00); // 0-1 success
		buffer.writeInt(0x00); // ??
		buffer.writeInt(_items.size()); // Obtained items list size
		for (ItemHolder item : _items) {
			buffer.writeInt(item.getId()); // Actually object ID
			buffer.writeInt(0); // ?
			buffer.writeInt(0); // ?
			buffer.writeInt((int) item.getCount()); // Count
		}
	}
}
