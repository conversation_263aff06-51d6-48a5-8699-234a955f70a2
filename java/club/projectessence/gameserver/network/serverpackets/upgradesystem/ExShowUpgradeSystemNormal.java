/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.network.serverpackets.upgradesystem;

import club.projectessence.commons.network.ServerPacketId;
import club.projectessence.gameserver.network.GameClient;
import club.projectessence.gameserver.network.serverpackets.ServerPacket;
import io.github.joealisson.mmocore.WritableBuffer;

/**
 * <AUTHOR>
 */
public class ExShowUpgradeSystemNormal extends ServerPacket {
	private static final int[] CRYSTALS =
			{
					1458,
					1459,
					1460,
					1461
			};
	private final UpgradeSystemNormalType _type;

	public ExShowUpgradeSystemNormal(UpgradeSystemNormalType type) {
		_type = type;
	}

	@Override
	public void writeImpl(GameClient client, WritableBuffer buffer) {
		writeId(ServerPacketId.EX_SHOW_UPGRADE_SYSTEM_NORMAL, buffer);
		buffer.writeShort(0x01); // ?
		buffer.writeShort(_type.ordinal()); // type (Essence uses only 1. Classic 1- D-A, 2 - A-S (client dat param)
		buffer.writeShort(100); // ?
		int unkSize = 4;
		buffer.writeInt(unkSize);
		for (int crystalId : CRYSTALS) {
			buffer.writeInt(crystalId);
		}
		buffer.writeInt(unkSize); // size
		for (int i = 0; i < unkSize; i++) {
			buffer.writeInt(10); // ?
		}
	}

	public enum UpgradeSystemNormalType {
		UNUSED,
		RARE_ACCESSORIES
	}
}
