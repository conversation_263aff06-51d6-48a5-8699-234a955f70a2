/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.network.serverpackets;

import club.projectessence.commons.network.ServerPacketId;
import club.projectessence.gameserver.model.residences.AbstractResidence;
import club.projectessence.gameserver.model.residences.ResidenceFunctionType;
import club.projectessence.gameserver.network.GameClient;
import io.github.joealisson.mmocore.WritableBuffer;

/**
 * <AUTHOR> UnAfraid
 */
public class AgitDecoInfo extends ServerPacket {
	private final AbstractResidence _residense;

	public AgitDecoInfo(AbstractResidence residense) {
		_residense = residense;
	}

	@Override
	public void writeImpl(GameClient client, WritableBuffer buffer) {
		writeId(ServerPacketId.AGIT_DECO_INFO, buffer);
		buffer.writeInt(_residense.getResidenceId());
		for (ResidenceFunctionType type : ResidenceFunctionType.values()) {
			if (type == ResidenceFunctionType.NONE) {
				continue;
			}
			buffer.writeByte(_residense.hasFunction(type) ? 0x01 : 0x00);
		}

		// Unknown
		buffer.writeInt(0); // TODO: Find me!
		buffer.writeInt(0); // TODO: Find me!
		buffer.writeInt(0); // TODO: Find me!
		buffer.writeInt(0); // TODO: Find me!
		buffer.writeInt(0); // TODO: Find me!
	}
}
