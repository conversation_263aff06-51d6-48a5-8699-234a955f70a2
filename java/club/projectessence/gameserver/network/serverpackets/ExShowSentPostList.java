/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.network.serverpackets;

import club.projectessence.commons.network.ServerPacketId;
import club.projectessence.gameserver.instancemanager.MailManager;
import club.projectessence.gameserver.model.Message;
import club.projectessence.gameserver.network.GameClient;
import io.github.joealisson.mmocore.WritableBuffer;

import java.util.List;

/**
 * <AUTHOR> DS
 */
public class ExShowSentPostList extends ServerPacket {
	private final List<Message> _outbox;

	public ExShowSentPostList(int objectId) {
		_outbox = MailManager.getInstance().getOutbox(objectId);
	}

	@Override
	public void writeImpl(GameClient client, WritableBuffer buffer) {
		writeId(ServerPacketId.EX_SHOW_SENT_POST_LIST, buffer);

		buffer.writeInt((int) (System.currentTimeMillis() / 1000));
		if ((_outbox != null) && !_outbox.isEmpty()) {
			buffer.writeInt(_outbox.size());
			for (Message msg : _outbox) {
				buffer.writeInt(msg.getId());
				buffer.writeString(msg.getSubject());
				buffer.writeString(msg.getReceiverName());
				buffer.writeInt(msg.isLocked() ? 0x01 : 0x00);
				buffer.writeInt(msg.getExpirationSeconds());
				buffer.writeInt(msg.isUnread() ? 0x01 : 0x00);
				buffer.writeInt(0x01);
				buffer.writeInt(msg.hasAttachments() ? 0x01 : 0x00);
				buffer.writeInt(0x00);
			}
		} else {
			buffer.writeInt(0x00);
		}
	}
}
