/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.network.serverpackets;

import club.projectessence.commons.network.ServerPacketId;
import club.projectessence.gameserver.model.Location;
import club.projectessence.gameserver.model.Party;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.network.GameClient;
import io.github.joealisson.mmocore.WritableBuffer;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class PartyMemberPosition extends ServerPacket {
	private final Map<Integer, Location> locations = new HashMap<>();

	public PartyMemberPosition(Party party) {
		reuse(party);
	}

	public void reuse(Party party) {
		locations.clear();
		for (PlayerInstance member : party.getMembers()) {
			if (member == null) {
				continue;
			}
			locations.put(member.getObjectId(), member.getLocation());
		}
	}

	@Override
	public void writeImpl(GameClient client, WritableBuffer buffer) {
		writeId(ServerPacketId.PARTY_MEMBER_POSITION, buffer);

		buffer.writeInt(locations.size());
		for (Map.Entry<Integer, Location> entry : locations.entrySet()) {
			final Location loc = entry.getValue();
			buffer.writeInt(entry.getKey());
			buffer.writeInt(loc.getX());
			buffer.writeInt(loc.getY());
			buffer.writeInt(loc.getZ());
		}
	}
}
