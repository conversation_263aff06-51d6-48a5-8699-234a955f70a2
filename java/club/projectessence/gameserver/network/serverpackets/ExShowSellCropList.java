/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.network.serverpackets;

import club.projectessence.commons.network.ServerPacketId;
import club.projectessence.gameserver.instancemanager.CastleManorManager;
import club.projectessence.gameserver.model.CropProcure;
import club.projectessence.gameserver.model.Seed;
import club.projectessence.gameserver.model.itemcontainer.PlayerInventory;
import club.projectessence.gameserver.model.items.instance.ItemInstance;
import club.projectessence.gameserver.network.GameClient;
import io.github.joealisson.mmocore.WritableBuffer;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class ExShowSellCropList extends ServerPacket {
	private final int _manorId;
	private final Map<Integer, ItemInstance> _cropsItems = new HashMap<>();
	private final Map<Integer, CropProcure> _castleCrops = new HashMap<>();

	public ExShowSellCropList(PlayerInventory inventory, int manorId) {
		_manorId = manorId;
		for (int cropId : CastleManorManager.getInstance().getCropIds()) {
			final ItemInstance item = inventory.getItemByItemId(cropId);
			if (item != null) {
				_cropsItems.put(cropId, item);
			}
		}

		for (CropProcure crop : CastleManorManager.getInstance().getCropProcure(_manorId, false)) {
			if (_cropsItems.containsKey(crop.getId()) && (crop.getAmount() > 0)) {
				_castleCrops.put(crop.getId(), crop);
			}
		}
	}

	@Override
	public void writeImpl(GameClient client, WritableBuffer buffer) {
		writeId(ServerPacketId.EX_SHOW_SELL_CROP_LIST, buffer);

		buffer.writeInt(_manorId); // manor id
		buffer.writeInt(_cropsItems.size()); // size
		for (ItemInstance item : _cropsItems.values()) {
			final Seed seed = CastleManorManager.getInstance().getSeedByCrop(item.getId());
			buffer.writeInt(item.getObjectId()); // Object id
			buffer.writeInt(item.getId()); // crop id
			buffer.writeInt(seed.getLevel()); // seed level
			buffer.writeByte(0x01);
			buffer.writeInt(seed.getReward(1)); // reward 1 id
			buffer.writeByte(0x01);
			buffer.writeInt(seed.getReward(2)); // reward 2 id
			if (_castleCrops.containsKey(item.getId())) {
				final CropProcure crop = _castleCrops.get(item.getId());
				buffer.writeInt(_manorId); // manor
				buffer.writeLong(crop.getAmount()); // buy residual
				buffer.writeLong(crop.getPrice()); // buy price
				buffer.writeByte(crop.getReward()); // reward
			} else {
				buffer.writeInt(0xFFFFFFFF); // manor
				buffer.writeLong(0x00); // buy residual
				buffer.writeLong(0x00); // buy price
				buffer.writeByte(0x00); // reward
			}
			buffer.writeLong(item.getCount()); // my crops
		}
	}
}