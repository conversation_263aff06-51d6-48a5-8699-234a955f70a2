/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.network.serverpackets;

import club.projectessence.commons.network.ServerPacketId;
import club.projectessence.gameserver.instancemanager.InstanceManager;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.model.instancezone.Instance;
import club.projectessence.gameserver.network.GameClient;
import io.github.joealisson.mmocore.WritableBuffer;

import java.util.Map;
import java.util.Map.Entry;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
public class ExInzoneWaiting extends ServerPacket {
	private final int _currentTemplateId;
	private final Map<Integer, Long> _instanceTimes;
	private final boolean _hide;

	public ExInzoneWaiting(PlayerInstance player, boolean hide) {
		final Instance instance = InstanceManager.getInstance().getPlayerInstance(player, false);
		_currentTemplateId = ((instance != null) && (instance.getTemplateId() >= 0)) ? instance.getTemplateId() : -1;
		_instanceTimes = InstanceManager.getInstance().getAllInstanceTimes(player);
		_hide = hide;
	}

	@Override
	public void writeImpl(GameClient client, WritableBuffer buffer) {
		writeId(ServerPacketId.EX_INZONE_WAITING_INFO, buffer);

		buffer.writeByte(_hide ? 0x00 : 0x01); // Grand Crusade
		buffer.writeInt(_currentTemplateId);
		buffer.writeInt(_instanceTimes.size());
		for (Entry<Integer, Long> entry : _instanceTimes.entrySet()) {
			final long instanceTime = TimeUnit.MILLISECONDS.toSeconds(entry.getValue() - System.currentTimeMillis());
			buffer.writeInt(entry.getKey());
			buffer.writeInt((int) instanceTime);
		}
	}
}
