/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.network.serverpackets;

import club.projectessence.commons.network.ServerPacketId;
import club.projectessence.gameserver.model.interfaces.ILocational;
import club.projectessence.gameserver.network.GameClient;
import io.github.joealisson.mmocore.WritableBuffer;

import java.util.ArrayList;
import java.util.List;

/**
 * Note: <b>There is known issue with this packet, it cannot be removed unless game client is restarted!</b>
 *
 * <AUTHOR>
 */
public class ExShowTerritory extends ServerPacket {
	private final int _minZ;
	private final int _maxZ;
	private final List<ILocational> _vertices = new ArrayList<>();

	public ExShowTerritory(int minZ, int maxZ) {
		_minZ = minZ;
		_maxZ = maxZ;
	}

	public void addVertice(ILocational loc) {
		_vertices.add(loc);
	}

	@Override
	public void writeImpl(GameClient client, WritableBuffer buffer) {
		writeId(ServerPacketId.EX_SHOW_TERRITORY, buffer);

		buffer.writeInt(_vertices.size());
		buffer.writeInt(_minZ);
		buffer.writeInt(_maxZ);
		for (ILocational loc : _vertices) {
			buffer.writeInt(loc.getX());
			buffer.writeInt(loc.getY());
		}
	}
}
