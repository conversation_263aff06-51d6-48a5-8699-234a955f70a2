/*
 * This file is part of the L2J Mobius project.
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.network.serverpackets;

import java.util.HashMap;
import java.util.Map;
import java.util.Map.Entry;

import club.projectessence.Config;
import club.projectessence.commons.network.ServerPacketId;
import club.projectessence.gameserver.data.xml.ExperienceData;
import club.projectessence.gameserver.enums.ClassId;
import club.projectessence.gameserver.enums.UserInfoType;
import club.projectessence.gameserver.instancemanager.CastleManager;
import club.projectessence.gameserver.instancemanager.CursedWeaponsManager;
import club.projectessence.gameserver.instancemanager.RankManager;
import club.projectessence.gameserver.model.Party;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.model.clan.Clan;
import club.projectessence.gameserver.model.henna.Henna;
import club.projectessence.gameserver.model.henna.HennaPoten;
import club.projectessence.gameserver.model.stats.BaseStat;
import club.projectessence.gameserver.model.stats.Stat;
import club.projectessence.gameserver.model.variables.PlayerVariables;

import club.projectessence.gameserver.model.zone.ZoneId;
import club.projectessence.gameserver.network.GameClient;
import io.github.joealisson.mmocore.WritableBuffer;

/**
 * <AUTHOR> UnAfraid
 */
public class UserInfo extends AbstractMaskPacket<UserInfoType>
{
	private final int		_flRunSpd	= 0;
	private final int		_flWalkSpd	= 0;
	private final byte[]	_masks		= new byte[]
	{
		(byte) 0x00,
		(byte) 0x00,
		(byte) 0x00,
		(byte) 0x00
	};
	private PlayerInstance	_player;
	private int				_relation;
	private int				_runSpd;
	private int				_walkSpd;
	private int				_swimRunSpd;
	private int				_swimWalkSpd;
	private int				_flyRunSpd;
	private int				_flyWalkSpd;
	private double			_moveMultiplier;
	private int				_enchantLevel;
	private int				_armorEnchant;
	private String			_title;
	private int				_initSize	= 5;
	
	public UserInfo(PlayerInstance player)
	{
		this(player, true);
	}
	
	public UserInfo(PlayerInstance player, boolean addAll)
	{
		if (!player.isSubclassLocked()) // Changing class.
		{
			_player = player;
			_relation = calculateRelation(player);
			_moveMultiplier = player.getMovementSpeedMultiplier();
			_runSpd = (int) Math.round(player.getRunSpeed() / _moveMultiplier);
			_walkSpd = (int) Math.round(player.getWalkSpeed() / _moveMultiplier);
			_swimRunSpd = (int) Math.round(player.getSwimRunSpeed() / _moveMultiplier);
			_swimWalkSpd = (int) Math.round(player.getSwimWalkSpeed() / _moveMultiplier);
			_flyRunSpd = player.isFlying() ? _runSpd : 0;
			_flyWalkSpd = player.isFlying() ? _walkSpd : 0;
			_enchantLevel = player.getInventory().getWeaponEnchant();
			_armorEnchant = player.getInventory().getArmorMinEnchant();
			_title = player.getTitle();
			if (player.getStat().getValue(Stat.DEMONIC_MOVEMENT, 0) > 0)
			{
				if (player.isInvisible())
				{
					_title = "Invisible Shifting";
				}
				else
				{
					_title = "Phaze Shifting";
				}
			}
			else if (player.isInvisible())
			{
				_title = "Invisible";
			}
			// if (player.isGM() && player.isInvisible())
			// {
			// _title = "[Invisible]";
			// }
			if (addAll)
			{
				addComponentType(UserInfoType.values());
			}
		}
	}
	
	@Override
	protected byte[] getMasks()
	{
		return _masks;
	}
	
	@Override
	protected void onNewMaskAdded(UserInfoType component)
	{
		calcBlockSize(component);
	}
	
	private void calcBlockSize(UserInfoType type)
	{
		switch (type)
		{
			case BASIC_INFO:
			{
				_initSize += type.getBlockLength() + (_player.getAppearance().getVisibleName().length() * 2);
				break;
			}
			case CLAN:
			{
				_initSize += type.getBlockLength() + (_title.length() * 2);
				break;
			}
			default:
			{
				_initSize += type.getBlockLength();
				break;
			}
		}
	}
	
	@Override
	public void writeImpl(GameClient client, WritableBuffer buffer)
	{
		if (_player == null)
		{
			return;
		}
		writeId(ServerPacketId.USER_INFO, buffer);
		buffer.writeInt(_player.getObjectId());
		buffer.writeInt(_initSize);
		buffer.writeShort(29); // 362 - 29
		buffer.writeBytes(_masks);
		if (containsMask(UserInfoType.RELATION))
		{
			buffer.writeInt(_relation);
		}
		if (containsMask(UserInfoType.BASIC_INFO))
		{
			buffer.writeShort(23 + (_player.getAppearance().getVisibleName().length() * 2));
			buffer.writeSizedString(_player.getName());
			buffer.writeByte(_player.isGM() ? 0x01 : 0x00);
			buffer.writeByte(_player.getRace().ordinal());
			buffer.writeByte(_player.getAppearance().isFemale() ? 0x01 : 0x00);
			buffer.writeInt(ClassId.getClassId(_player.getBaseTemplate().getClassId().getId()).getRootClassId().getId());
			buffer.writeInt(_player.getClassId().getId());
			buffer.writeInt(_player.getLevel());
			buffer.writeInt(_player.getClassId().getId()); // 286
		}
		if (containsMask(UserInfoType.BASE_STATS))
		{
			buffer.writeShort(18);
			buffer.writeShort(_player.getSTR());
			buffer.writeShort(_player.getDEX());
			buffer.writeShort(_player.getCON());
			buffer.writeShort(_player.getINT());
			buffer.writeShort(_player.getWIT());
			buffer.writeShort(_player.getMEN());
			buffer.writeShort(0); // LUC
			buffer.writeShort(0); // CHA
		}
		if (containsMask(UserInfoType.MAX_HPCPMP))
		{
			buffer.writeShort(14);
			buffer.writeInt(_player.getMaxHp());
			buffer.writeInt(_player.getMaxMp());
			buffer.writeInt(_player.getMaxCp());
		}
		if (containsMask(UserInfoType.CURRENT_HPMPCP_EXP_SP))
		{
			buffer.writeShort(38);
			buffer.writeInt((int) Math.round(_player.getCurrentHp()));
			buffer.writeInt((int) Math.round(_player.getCurrentMp()));
			buffer.writeInt((int) Math.round(_player.getCurrentCp()));
			buffer.writeLong(_player.getSp());
			buffer.writeLong(_player.getExp());
			buffer.writeDouble((float) (_player.getExp() - ExperienceData.getInstance().getExpForLevel(_player.getLevel())) / (ExperienceData.getInstance().getExpForLevel(_player.getLevel() + 1) - ExperienceData.getInstance().getExpForLevel(_player.getLevel())));
		}
		if (containsMask(UserInfoType.ENCHANTLEVEL))
		{
			buffer.writeShort(5);
			buffer.writeByte(_enchantLevel);
			buffer.writeByte(_armorEnchant);
			buffer.writeByte(0x00); // 338 - cBackEnchant?
		}
		if (containsMask(UserInfoType.APPAREANCE))
		{
			buffer.writeShort(19); // 338
			buffer.writeInt(_player.getVisualHair());
			buffer.writeInt(_player.getVisualHairColor());
			buffer.writeInt(_player.getVisualFace());
			buffer.writeByte(_player.isHairAccessoryEnabled());
			buffer.writeInt(_player.getVisualHairColor() + 1); // 338 - DK color.
		}
		if (containsMask(UserInfoType.STATUS))
		{
			buffer.writeShort(6);
			buffer.writeByte(_player.getMountType().ordinal());
			buffer.writeByte(_player.getPrivateStoreType().getId());
			buffer.writeByte(_player.hasDwarvenCraft() || (_player.getSkillLevel(248) > 0));
			buffer.writeByte(0); // Ability Points
		}
		if (containsMask(UserInfoType.STATS))
		{
			buffer.writeShort(64); // 270
			buffer.writeShort(_player.getActiveWeaponItem() != null ? 40 : 20); // 70 with weapon on Blue
			buffer.writeInt(_player.getPAtk());
			buffer.writeInt(_player.getPAtkSpd());
			buffer.writeInt(_player.getPDef());
			buffer.writeInt(_player.getEvasionRate());
			buffer.writeInt(_player.getAccuracy());
			buffer.writeInt(_player.getCriticalHit());
			buffer.writeInt(_player.getMAtk());
			buffer.writeInt(_player.getMAtkSpd());
			buffer.writeInt(_player.getPAtkSpd()); // Seems like atk speed - 1
			buffer.writeInt(_player.getMagicEvasionRate());
			buffer.writeInt(_player.getMDef());
			buffer.writeInt(_player.getMagicAccuracy());
			buffer.writeInt(_player.getMCriticalHit());
			buffer.writeInt(0); // 270 (pAtkAdd?)
			buffer.writeInt(0); // 270 (mAtkAdd?)
		}
		if (containsMask(UserInfoType.ELEMENTALS))
		{
			buffer.writeShort(14);
			buffer.writeShort(0);
			buffer.writeShort(0);
			buffer.writeShort(0);
			buffer.writeShort(0);
			buffer.writeShort(0);
			buffer.writeShort(0);
		}
		if (containsMask(UserInfoType.POSITION))
		{
			buffer.writeShort(18);
			buffer.writeInt(_player.getX());
			buffer.writeInt(_player.getY());
			buffer.writeInt(_player.getZ());
			buffer.writeInt(_player.isInVehicle() ? _player.getVehicle().getObjectId() : 0);
		}
		if (containsMask(UserInfoType.SPEED))
		{
			buffer.writeShort(18);
			buffer.writeShort(_runSpd);
			buffer.writeShort(_walkSpd);
			buffer.writeShort(_swimRunSpd);
			buffer.writeShort(_swimWalkSpd);
			buffer.writeShort(_flRunSpd);
			buffer.writeShort(_flWalkSpd);
			buffer.writeShort(_flyRunSpd);
			buffer.writeShort(_flyWalkSpd);
		}
		if (containsMask(UserInfoType.MULTIPLIER))
		{
			buffer.writeShort(18);
			buffer.writeDouble(_moveMultiplier);
			buffer.writeDouble(_player.getAttackSpeedMultiplier());
		}
		if (containsMask(UserInfoType.COL_RADIUS_HEIGHT))
		{
			buffer.writeShort(18);
			buffer.writeDouble(_player.getCollisionRadius());
			buffer.writeDouble(_player.getCollisionHeight());
		}
		if (containsMask(UserInfoType.ATK_ELEMENTAL))
		{
			buffer.writeShort(5);
			buffer.writeByte(0);
			buffer.writeShort(0);
		}
		if (containsMask(UserInfoType.CLAN))
		{
			buffer.writeShort(32 + (_title.length() * 2));
			buffer.writeSizedString(_title);
			buffer.writeShort(_player.getPledgeType());
			buffer.writeInt(_player.getClanId());
			buffer.writeInt(_player.getClanCrestLargeId());
			buffer.writeInt(_player.getClanCrestId());
			buffer.writeInt(_player.getClanPrivileges().getBitmask());
			buffer.writeByte(_player.isClanLeader() ? 0x01 : 0x00);
			buffer.writeInt(_player.getAllyId());
			buffer.writeInt(_player.getAllyCrestId());
			buffer.writeByte(_player.isInMatchingRoom() ? 0x01 : 0x00);
		}
		if (containsMask(UserInfoType.SOCIAL))
		{
			buffer.writeShort(30); // 228
			buffer.writeByte(_player.getPvpFlag());
			buffer.writeInt(_player.getReputation()); // Reputation
			buffer.writeByte(_player.isNoble());
			buffer.writeByte(_player.isHero() || (_player.isGM() && Config.GM_HERO_AURA) ? 2 : 0); // 152 - Value for enabled changed to 2?
			buffer.writeByte(_player.getPledgeClass());
			buffer.writeInt(_player.getPkKills());
			buffer.writeInt(_player.getPvpKills());
			buffer.writeShort(_player.getRecomLeft());
			buffer.writeShort(_player.getRecomHave());
			// AFK animation.
			if ((_player.getClan() != null) && (CastleManager.getInstance().getCastleByOwner(_player.getClan()) != null)) // 196
			{
				buffer.writeInt(_player.isClanLeader() ? 100 : 101);
			}
			else
			{
				buffer.writeInt(0);
			}
			buffer.writeInt(0); // 228
		}
		if (containsMask(UserInfoType.VITA_FAME))
		{
			buffer.writeShort(19); // 196
			buffer.writeInt(_player.getSayhaGracePoints());
			buffer.writeByte(0); // Vita Bonus
			buffer.writeInt(0); // _player.getFame()
			buffer.writeInt(0); // _player.getRaidbossPoints()
			buffer.writeByte(0); // 196
			buffer.writeShort(0); // Henna Seal Engraving Gauge
			buffer.writeByte(0); // 196
		}
		if (containsMask(UserInfoType.SLOTS))
		{
			buffer.writeShort(12); // 152
			buffer.writeByte(_player.getInventory().getTalismanSlots());
			buffer.writeByte(_player.getInventory().getBroochJewelSlots());
			buffer.writeByte(_player.getTeam().getId());
			buffer.writeInt(0);
			if (_player.getInventory().getAgathionSlots() > 0)
			{
				buffer.writeByte(1); // Charm slots
				buffer.writeByte(_player.getInventory().getAgathionSlots() - 1);
			}
			else
			{
				buffer.writeByte(0); // Charm slots
				buffer.writeByte(0);
			}
			buffer.writeByte(_player.getInventory().getArtifactSlots()); // Artifact set slots // 152
		}
		if (containsMask(UserInfoType.MOVEMENTS))
		{
			buffer.writeShort(4);
			buffer.writeByte(_player.isInsideZone(ZoneId.WATER) ? 1 : _player.isFlyingMounted() ? 2 : 0);
			buffer.writeByte(_player.isRunning() ? 0x01 : 0x00);
		}
		if (containsMask(UserInfoType.COLOR))
		{
			buffer.writeShort(10);
			buffer.writeInt(_player.getAppearance().getNameColor());
			buffer.writeInt(_player.getAppearance().getTitleColor());
		}
		if (containsMask(UserInfoType.INVENTORY_LIMIT))
		{
			buffer.writeShort(13);
			buffer.writeShort(0);
			buffer.writeShort(0);
			buffer.writeShort(_player.getInventoryLimit());
			buffer.writeByte(_player.isCursedWeaponEquipped() ? CursedWeaponsManager.getInstance().getLevel(_player.getCursedWeaponEquippedId()) : 0);
			buffer.writeByte(0); // 196
			buffer.writeByte(0); // 196
			buffer.writeByte(0); // 196
			buffer.writeByte(0); // 196
		}
		if (containsMask(UserInfoType.TRUE_HERO)) // TODO: cdcc
		{
			buffer.writeShort(9);
			buffer.writeInt(0);
			buffer.writeShort(0);
			buffer.writeByte(_player.isTrueHero() ? 100 : 0);
		}
		if (containsMask(UserInfoType.ATT_SPIRITS)) // 152
		{
			buffer.writeShort(26);
			buffer.writeInt((int) _player.getActiveElementalSpiritAttack());
			buffer.writeInt((int) _player.getFireSpiritDefense());
			buffer.writeInt((int) _player.getWaterSpiritDefense());
			buffer.writeInt((int) _player.getWindSpiritDefense());
			buffer.writeInt((int) _player.getEarthSpiritDefense());
			buffer.writeInt(_player.getActiveElementalSpiritType());
		}
		if (containsMask(UserInfoType.RANKING)) // 196
		{
			buffer.writeShort(6);
			buffer.writeInt(RankManager.getInstance().getPlayerRealRank(_player.getObjectId()) == 1 ? 1 : RankManager.getInstance().getPlayerRealRaceRank(_player.getObjectId()) == 1 ? 2 : 0);
		}
		int elixirAvailable = -1;
		if (containsMask(UserInfoType.STAT_POINTS)) // 235
		{
			final PlayerVariables vars = _player.getVariables();
			buffer.writeShort(16);
			elixirAvailable = vars.getInt(PlayerVariables.STAT_ELIXIR, 0);
			buffer.writeShort(_player.getLevel() < 76 ? 0 : (_player.getLevel() - 75) + elixirAvailable); // Usable points
			buffer.writeShort(vars.getInt(PlayerVariables.STAT_STR, 0)); // STR points
			buffer.writeShort(vars.getInt(PlayerVariables.STAT_DEX, 0)); // DEX points
			buffer.writeShort(vars.getInt(PlayerVariables.STAT_CON, 0)); // CON points
			buffer.writeShort(vars.getInt(PlayerVariables.STAT_INT, 0)); // INT points
			buffer.writeShort(vars.getInt(PlayerVariables.STAT_WIT, 0)); // WIT points
			buffer.writeShort(vars.getInt(PlayerVariables.STAT_MEN, 0)); // MEN points
		}
		if (containsMask(UserInfoType.STAT_ABILITIES)) // 235
		{
			Map<BaseStat, Integer> stats = new HashMap<>();
			for (HennaPoten hennaPoten : _player.getHennaPotenList())
			{
				final Henna henna = hennaPoten.getHenna();
				if (henna != null)
				{
					for (Entry<BaseStat, Integer> entry : henna.getBaseStats().entrySet())
					{
						stats.put(entry.getKey(), stats.getOrDefault(entry.getKey(), 0) + entry.getValue());
					}
				}
			}
			buffer.writeShort(18);
			buffer.writeShort(stats.getOrDefault(BaseStat.STR, 0)); // additional STR
			buffer.writeShort(stats.getOrDefault(BaseStat.DEX, 0)); // additional DEX
			buffer.writeShort(stats.getOrDefault(BaseStat.CON, 0)); // additional CON
			buffer.writeShort(stats.getOrDefault(BaseStat.INT, 0)); // additional INT
			buffer.writeShort(stats.getOrDefault(BaseStat.WIT, 0)); // additional WIT
			buffer.writeShort(stats.getOrDefault(BaseStat.MEN, 0)); // additional MEN
			buffer.writeShort(0x01);
			buffer.writeShort(0x01);
		}
		if (containsMask(UserInfoType.ELIXIR_USED)) // 286
		{
			if (elixirAvailable < 0)
			{
				elixirAvailable = _player.getVariables().getInt(PlayerVariables.STAT_ELIXIR, 0);
			}
			buffer.writeInt(elixirAvailable); // count
		}
		if (containsMask(UserInfoType.VANGUARD_BEAST)) // 362
		{
			buffer.writeByte(_player.getVanguardBeastId()); // 362
		}
		// Send exp bonus change.
		// if (containsMask(UserInfoType.VITA_FAME))
		// {
		// _player.sendExpBoostInfo();
		// }
	}
	
	@Override
	public void runImpl(PlayerInstance player)
	{
		if (_player == null)
		{
			return;
		}
		// Send exp bonus change.
		if (containsMask(UserInfoType.VITA_FAME))
		{
			_player.getSayhaGracePoints();
		}
	}
	
	private int calculateRelation(PlayerInstance player)
	{
		int relation = 0;
		final Party party = player.getParty();
		final Clan clan = player.getClan();
		if (party != null)
		{
			relation |= 0x08; // Party member
			if (party.getLeader() == _player)
			{
				relation |= 0x10; // Party leader
			}
		}
		if (clan != null)
		{
			relation |= 0x20; // Clan member
			if (clan.getLeaderId() == player.getObjectId())
			{
				relation |= 0x40; // Clan leader
			}
		}
		if (player.isInSiege())
		{
			relation |= 0x80; // In siege
		}
		return relation;
	}
}
