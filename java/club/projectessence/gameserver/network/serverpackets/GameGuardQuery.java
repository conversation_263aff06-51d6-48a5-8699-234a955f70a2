/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.network.serverpackets;

import club.projectessence.commons.network.ServerPacketId;
import club.projectessence.gameserver.network.GameClient;
import io.github.joealisson.mmocore.WritableBuffer;

/**
 * Lets drink to code!
 *
 * <AUTHOR>
 */
public class GameGuardQuery extends ServerPacket {
	public static final GameGuardQuery STATIC_PACKET = new GameGuardQuery();

	private GameGuardQuery() {
	}

	@Override
	public void writeImpl(GameClient client, WritableBuffer buffer) {
		writeId(ServerPacketId.GAME_GUARD_QUERY, buffer);

		buffer.writeInt(0x27533DD9);
		buffer.writeInt(0x2E72A51D);
		buffer.writeInt(0x2017038B);
		buffer.writeInt(0xC35B1EA3);
	}
}
