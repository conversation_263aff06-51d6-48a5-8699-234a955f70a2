/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.network.serverpackets.augmentation;

import club.projectessence.commons.network.ServerPacketId;
import club.projectessence.gameserver.network.GameClient;
import club.projectessence.gameserver.network.serverpackets.ServerPacket;
import io.github.joealisson.mmocore.WritableBuffer;

/**
 * <AUTHOR>
 */
public class ExApplyVariationOption extends ServerPacket {
	private final boolean _result;
	private final int _variationItemObjId;
	private final int _option1;
	private final int _option2;

	public ExApplyVariationOption(boolean result, int variationItemObjId, int option1, int option2) {
		_result = result;
		_variationItemObjId = variationItemObjId;
		_option1 = option1;
		_option2 = option2;
	}

	@Override
	public void writeImpl(GameClient client, WritableBuffer buffer) {
		writeId(ServerPacketId.EX_APPLY_VARIATION_OPTION, buffer);

		buffer.writeByte(_result); // bResult
		buffer.writeInt(_variationItemObjId); // nVariationItemSID
		buffer.writeInt(_option1); // nItemOption1
		buffer.writeInt(_option2); // nItemOption1
	}
}
