/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.network.serverpackets.augmentation;

import club.projectessence.commons.network.ServerPacketId;
import club.projectessence.gameserver.network.GameClient;
import club.projectessence.gameserver.network.serverpackets.ServerPacket;
import io.github.joealisson.mmocore.WritableBuffer;

public class ExVariationCancelResult extends ServerPacket {
	public static final ExVariationCancelResult STATIC_PACKET_SUCCESS = new ExVariationCancelResult(1);
	public static final ExVariationCancelResult STATIC_PACKET_FAILURE = new ExVariationCancelResult(0);

	private final int _result;

	private ExVariationCancelResult(int result) {
		_result = result;
	}

	@Override
	public void writeImpl(GameClient client, WritableBuffer buffer) {
		writeId(ServerPacketId.EX_VARIATION_CANCEL_RESULT, buffer);

		buffer.writeInt(_result);
	}
}
