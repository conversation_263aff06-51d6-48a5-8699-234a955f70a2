/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.network.serverpackets;

import club.projectessence.commons.network.ServerPacketId;
import club.projectessence.gameserver.instancemanager.CastleManorManager;
import club.projectessence.gameserver.model.CropProcure;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.model.items.instance.ItemInstance;
import club.projectessence.gameserver.network.GameClient;
import io.github.joealisson.mmocore.WritableBuffer;

import java.util.HashMap;
import java.util.Map;
import java.util.Map.Entry;

public class SellListProcure extends ServerPacket {
	private final long _money;
	private final Map<ItemInstance, Long> _sellList = new HashMap<>();

	public SellListProcure(PlayerInstance player, int castleId) {
		_money = player.getAdena();
		for (CropProcure c : CastleManorManager.getInstance().getCropProcure(castleId, false)) {
			final ItemInstance item = player.getInventory().getItemByItemId(c.getId());
			if ((item != null) && (c.getAmount() > 0)) {
				_sellList.put(item, c.getAmount());
			}
		}
	}

	@Override
	public void writeImpl(GameClient client, WritableBuffer buffer) {
		writeId(ServerPacketId.SELL_LIST_PROCURE, buffer);

		buffer.writeLong(_money); // money
		buffer.writeInt(0x00); // lease ?
		buffer.writeShort(_sellList.size()); // list size

		for (Entry<ItemInstance, Long> entry : _sellList.entrySet()) {
			final ItemInstance item = entry.getKey();
			buffer.writeShort(item.getItem().getType1());
			buffer.writeInt(item.getObjectId());
			buffer.writeInt(item.getDisplayId());
			buffer.writeLong(entry.getValue()); // count
			buffer.writeShort(item.getItem().getType2());
			buffer.writeShort(0); // unknown
			buffer.writeLong(0); // price, u shouldnt get any adena for crops, only raw materials
		}
	}
}
