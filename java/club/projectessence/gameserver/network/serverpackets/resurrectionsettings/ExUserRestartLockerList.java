/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.network.serverpackets.resurrectionsettings;

import club.projectessence.commons.network.ServerPacketId;
import club.projectessence.gameserver.data.xml.ResurrectionFeesData;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.model.variables.PlayerVariables;

import club.projectessence.gameserver.network.GameClient;
import club.projectessence.gameserver.network.serverpackets.ServerPacket;
import io.github.joealisson.mmocore.WritableBuffer;

import java.util.List;
import java.util.Map.Entry;

/**
 * <AUTHOR>
 */
public class ExUserRestartLockerList extends ServerPacket {
	public final PlayerInstance _player;

	public ExUserRestartLockerList(PlayerInstance player) {
		_player = player;
	}

	@Override
	public void writeImpl(GameClient client, WritableBuffer buffer) {
		writeId(ServerPacketId.EX_USER_RESTART_LOCKER_LIST, buffer);

		buffer.writeByte(1); // bExpDown
		buffer.writeInt(ResurrectionFeesData.getInstance().getResurrectionSettingsSize());
		final PlayerVariables vars = _player.getVariables();
		for (Entry<Integer, List<Integer>> setting : ResurrectionFeesData.getInstance().getResurrectionSettings().entrySet()) {
			final int restartPoint = setting.getKey();
			for (int itemId : setting.getValue()) {
				buffer.writeInt(restartPoint);
				buffer.writeInt(itemId);
				buffer.writeByte(vars.getBoolean("RESURRECTION_SETTING_" + restartPoint + "_" + (itemId < 0 ? "M" + -itemId : itemId), false));
			}
		}
	}
}
