/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.network.serverpackets;

import club.projectessence.commons.network.ServerPacketId;
import club.projectessence.gameserver.model.actor.Playable;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.network.GameClient;
import io.github.joealisson.mmocore.WritableBuffer;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
public class RelationChanged extends ServerPacket {
	// TODO: Enum
	public static final int RELATION_INSIDE_BATTLEFIELD = 0x00001;
	public static final int RELATION_IN_PVP = 0x00002;
	public static final int RELATION_CHAOTIC = 0x00004;
	public static final int RELATION_IN_PARTY = 0x00008;
	public static final int RELATION_PARTY_LEADER = 0x00010;
	public static final int RELATION_SAME_PARTY = 0x00020;
	public static final int RELATION_IN_PLEDGE = 0x00040;
	public static final int RELATION_PLEDGE_LEADER = 0x00080;
	public static final int RELATION_SAME_PLEDGE = 0x00100;
	public static final int RELATION_SIEGE_PARTICIPANT = 0x00200;
	public static final int RELATION_SIEGE_ATTACKER = 0x00400;
	public static final int RELATION_SIEGE_ALLY = 0x00800;
	public static final int RELATION_SIEGE_ENEMY = 0x01000;
	public static final int RELATION_IN_ALLIANCE = 0x10000;
	public static final int RELATION_ONE_SIDED_WAR = 0x12000;
	public static final int RELATION_MUTUAL_WAR = 0x16000;
	public static final int RELATION_ALLIANCE_LEADER = 0x20000;
	public static final int RELATION_SAME_ALLIANCE = 0x40000;
	public static final int RELATION_DEATH_KNIGHT_PK = 0x20000000;
	public static final int RELATION_SURVEILLANCE = 0x80000000;

	// Masks
	public static final byte SEND_DEFAULT = 0x01;
	public static final byte SEND_ONE = 0x02;
	public static final byte SEND_MULTI = 0x04;
	private final List<Relation> _datas = new ArrayList<>(1);
	private byte _mask = 0x00;
	public RelationChanged(Playable about, PlayerInstance target) {
		addRelation(about, target);
	}

	public RelationChanged() {
	}

	public boolean hasData() {
		return (_datas != null) && (_datas.size() > 0);
	}

	public void addRelation(Playable about, PlayerInstance target) {
		if (about.isInvisible()) {
			// throw new IllegalArgumentException("Cannot add invisible character to multi relation packet");
			return;
		}
		final Relation data = new Relation();
		data._objId = about.getObjectId();
		data._reputation = about.getReputation();
		data._pvpFlag = about.getPvpFlag();
		data._autoAttackable = about.getActingPlayer().isInOlympiadMode() || about.isAutoAttackable(target) ? 0x01 : 0x00;
		data._relation = about.getRelation(target);
		_datas.add(data);

		if (_datas.size() > 1) {
			_mask |= SEND_MULTI;
		} else if (_datas.size() == 1) {
			_mask |= SEND_ONE;
		}
	}

	@Override
	public void writeImpl(GameClient client, WritableBuffer buffer) {
		// writeId(ServerPacketId.RELATION_CHANGED, buffer);
		//
		// buffer.writeByte(_mask);
		// if (_multi == null)
		// {
		// writeRelation(packet, _singled);
		// }
		// else
		// {
		// buffer.writeShort(_multi.size());
		// for (Relation r : _multi)
		// {
		// writeRelation(packet, r);
		// }
		// }
		// return true;

		writeId(ServerPacketId.RELATION_CHANGED, buffer);
		buffer.writeByte(_mask);
		if ((_mask & SEND_MULTI) == SEND_MULTI) {
			buffer.writeShort(_datas.size());
			for (Relation data : _datas) {
				writeRelation(data, buffer);
			}
		} else if ((_mask & SEND_ONE) == SEND_ONE) {
			writeRelation(_datas.get(0), buffer);
		} else if ((_mask & SEND_DEFAULT) == SEND_DEFAULT) {
			buffer.writeInt(_datas.get(0)._objId);
		}
	}

	private void writeRelation(Relation relation, WritableBuffer buffer) {
		buffer.writeInt(relation._objId);

		// if ((_mask & SEND_DEFAULT) == 0)
		{
			buffer.writeInt(relation._relation);
			buffer.writeInt(relation._reputation);
			buffer.writeByte(relation._autoAttackable);
			buffer.writeInt(0);
			buffer.writeByte(relation._pvpFlag);
		}
	}

	protected static class Relation {
		int _objId;
		int _relation;
		int _autoAttackable;
		int _reputation;
		int _pvpFlag;
	}
}
