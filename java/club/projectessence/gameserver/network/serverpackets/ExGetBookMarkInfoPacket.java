/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.network.serverpackets;

import club.projectessence.commons.network.ServerPacketId;
import club.projectessence.gameserver.model.TeleportBookmark;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.model.stats.Stat;
import club.projectessence.gameserver.network.GameClient;
import io.github.joealisson.mmocore.WritableBuffer;

/**
 * <AUTHOR>
 */
public class ExGetBookMarkInfoPacket extends ServerPacket {
	private final PlayerInstance _player;

	public ExGetBookMarkInfoPacket(PlayerInstance player) {
		_player = player;
	}

	@Override
	public void writeImpl(GameClient client, WritableBuffer buffer) {
		writeId(ServerPacketId.EX_GET_BOOK_MARK_INFO, buffer);

		buffer.writeInt(0x00); // Dummy
		buffer.writeInt(_player.getBookMarkSlot() + (int) _player.getStat().getValue(Stat.EXTRA_BOOKMARK_SLOT, 0));
		buffer.writeInt(_player.getTeleportBookmarks().size());

		for (TeleportBookmark tpbm : _player.getTeleportBookmarks()) {
			buffer.writeInt(tpbm.getId());
			buffer.writeInt(tpbm.getX());
			buffer.writeInt(tpbm.getY());
			buffer.writeInt(tpbm.getZ());
			buffer.writeString(tpbm.getName());
			buffer.writeInt(tpbm.getIcon());
			buffer.writeString(tpbm.getTag());
		}
	}
}
