/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.network.serverpackets;

import club.projectessence.commons.network.ServerPacketId;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.model.items.instance.ItemInstance;
import club.projectessence.gameserver.network.GameClient;
import io.github.joealisson.mmocore.WritableBuffer;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

public class WareHouseWithdrawalList extends AbstractItemPacket {
	public static final int PRIVATE = 1;
	public static final int CLAN = 2;
	public static final int CASTLE = 3; // not sure
	public static final int FREIGHT = 1;
	private final int _sendType;
	private final int _invSize;
	private final List<Integer> _itemsStackable = new ArrayList<>();
	private PlayerInstance _player;
	private long _playerAdena;
	private Collection<ItemInstance> _items;
	/**
	 * <ul>
	 * <li>0x01-Private Warehouse</li>
	 * <li>0x02-Clan Warehouse</li>
	 * <li>0x03-Castle Warehouse</li>
	 * <li>0x04-Warehouse</li>
	 * </ul>
	 */
	private int _whType;

	public WareHouseWithdrawalList(int sendType, PlayerInstance player, int type) {
		_sendType = sendType;
		_player = player;
		_whType = type;
		_playerAdena = _player.getAdena();
		_invSize = player.getInventory().getSize();
		if (_player.getActiveWarehouse() == null) {
			LOGGER.warning("error while sending withdraw request to: " + _player.getName());
			return;
		}

		// Prevent client crash by many cancelled mails
		if (_whType == PRIVATE) {
			int limit = player.getWareHouseLimit();
			_items = _player.getActiveWarehouse().getItems();
			if (limit < _items.size()) {
				_items = _items.stream().limit(limit).collect(Collectors.toCollection(ArrayList::new));
				player.sendPacket(new ExShowScreenMessage("Your warehouse exceeds the limit. Showing only " + limit + " items.", ExShowScreenMessage.TOP_CENTER, 3000, 0, true, true));
			}
		} else {
			_items = _player.getActiveWarehouse().getItems();
		}
		for (ItemInstance item : _items) {
			if (item.isStackable()) {
				_itemsStackable.add(item.getDisplayId());
			}
		}
	}

	@Override
	public void writeImpl(GameClient client, WritableBuffer buffer) {
		writeId(ServerPacketId.WAREHOUSE_WITHDRAW_LIST, buffer);
		buffer.writeByte(_sendType);
		if (_sendType == 2) {
			buffer.writeShort(0x00);
			buffer.writeInt(_invSize);
			buffer.writeInt(_items.size());
			for (ItemInstance item : _items) {
				writeItem(item, buffer);
				buffer.writeInt(item.getObjectId());
				buffer.writeInt(0x00);
				buffer.writeInt(0x00);
			}
		} else {
			buffer.writeShort(_whType);
			buffer.writeLong(_playerAdena);
			buffer.writeInt(_invSize);
			buffer.writeInt(_items.size());
		}
	}
}
