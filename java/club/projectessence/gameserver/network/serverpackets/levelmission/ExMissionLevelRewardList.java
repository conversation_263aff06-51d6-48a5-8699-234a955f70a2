/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.network.serverpackets.levelmission;

import club.projectessence.commons.network.ServerPacketId;
import club.projectessence.gameserver.data.xml.MissionLevelData;
import club.projectessence.gameserver.data.xml.MissionLevelData.MissionLevelRewardState;
import club.projectessence.gameserver.data.xml.MissionLevelData.MissionLevelRewardType;
import club.projectessence.gameserver.data.xml.MissionLevelData.MissionLevelSeasonInfo;
import club.projectessence.gameserver.data.xml.MissionLevelData.MissionLevelStepInfo;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.model.holders.ItemHolder;
import club.projectessence.gameserver.model.itemcontainer.PlayerMissionLevel;
import club.projectessence.gameserver.network.GameClient;
import club.projectessence.gameserver.network.serverpackets.ServerPacket;
import io.github.joealisson.mmocore.WritableBuffer;

import java.util.Map;

/**
 * <AUTHOR>
 */
public class ExMissionLevelRewardList extends ServerPacket {
	private final MissionLevelSeasonInfo _season;
	private final PlayerMissionLevel _playerInfo;

	private final int _unclaimedRewardsCount;
	private final int _isExtraRewardAvailable;
	private final int _seasonEndDate;

	public ExMissionLevelRewardList(PlayerInstance player) {
		_season = MissionLevelData.getInstance().getSeasonInfo();
		_playerInfo = player.getMissionLevel();

		_unclaimedRewardsCount = _playerInfo.getUnclaimedRewardsCount();
		_isExtraRewardAvailable = _playerInfo.isExtraRewardAvailable() ? 1 : 0;
		_seasonEndDate = MissionLevelData.getInstance().getSeasonEndDate();
	}

	@Override
	public void writeImpl(GameClient client, WritableBuffer buffer) {
		writeId(ServerPacketId.EX_MISSION_LEVEL_REWARD_LIST, buffer);

		int size = _season.getRewardCount();
		if (_season.getSpecialRewardItem() != null) {
			size++;
		}
		buffer.writeInt(size); // var array<_PkMissionLevelReward> rewards;
		final Map<Integer, MissionLevelStepInfo> rewards = _season.getSeasonRewards();
		for (MissionLevelStepInfo reward : rewards.values()) {
			final ItemHolder baseRewardItem = reward.getBaseRewardItem();
			final ItemHolder keyRewardItem = reward.getKeyRewardItem();
			final int level = reward.getLevel();
			if (baseRewardItem != null) {
				buffer.writeInt(MissionLevelRewardType.REWARD_BASE.ordinal()); // nType
				buffer.writeInt(level); // nLevel
				if (_playerInfo.getBaseRewardLevelClaimed() >= level) {
					buffer.writeInt(MissionLevelRewardState.ALREADY_RECEIVED.ordinal()); // nState
				} else if (_playerInfo.getLevel() >= level) {
					buffer.writeInt(MissionLevelRewardState.AVAILABLE.ordinal()); // nState
				} else {
					buffer.writeInt(MissionLevelRewardState.UNAVAILABLE.ordinal()); // nState
				}
			}
			if (keyRewardItem != null) {
				buffer.writeInt(MissionLevelRewardType.REWARD_KEY.ordinal()); // nType
				buffer.writeInt(level); // nLevel
				if (_playerInfo.getKeyRewardLevelClaimed() >= level) {
					buffer.writeInt(MissionLevelRewardState.ALREADY_RECEIVED.ordinal()); // nState
				} else if (_playerInfo.getLevel() >= level) {
					buffer.writeInt(MissionLevelRewardState.AVAILABLE.ordinal()); // nState
				} else {
					buffer.writeInt(MissionLevelRewardState.UNAVAILABLE.ordinal()); // nState
				}
			}
		}
		if (_season.getSpecialRewardItem() != null) {
			buffer.writeInt(MissionLevelRewardType.REWARD_SPECIAL.ordinal()); // nType
			buffer.writeInt(-1); // nLevel
			if (_playerInfo.getSpecialRewardClaimed()) {
				buffer.writeInt(MissionLevelRewardState.ALREADY_RECEIVED.ordinal()); // nState
			} else if (_playerInfo.getLevel() >= _season.getLimitLv()) {
				buffer.writeInt(MissionLevelRewardState.AVAILABLE.ordinal()); // nState
			} else {
				buffer.writeInt(MissionLevelRewardState.UNAVAILABLE.ordinal()); // nState
			}
		}
		buffer.writeInt(_playerInfo.getLevel()); // nLevel;
		buffer.writeInt(_playerInfo.getPoints()); // nPointPercent;
		buffer.writeInt(_season.getSeasonYear()); // nSeasonYear;
		buffer.writeInt(_season.getSeasonMonth()); // nSeasonMonth;
		buffer.writeInt(_unclaimedRewardsCount); // nTotalRewardsAvailable;
		buffer.writeInt(_isExtraRewardAvailable); // nExtraRewardsAvailable;
		buffer.writeInt(_seasonEndDate); // nRemainSeasonTime;
	}
}
