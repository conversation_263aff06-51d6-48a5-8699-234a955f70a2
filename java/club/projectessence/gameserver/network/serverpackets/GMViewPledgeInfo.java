/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.network.serverpackets;

import club.projectessence.commons.network.ServerPacketId;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.model.clan.Clan;
import club.projectessence.gameserver.model.clan.ClanMember;
import club.projectessence.gameserver.network.GameClient;
import io.github.joealisson.mmocore.WritableBuffer;

public class GMViewPledgeInfo extends ServerPacket {
	private final Clan _clan;
	private final PlayerInstance _player;

	public GMViewPledgeInfo(Clan clan, PlayerInstance player) {
		_clan = clan;
		_player = player;
	}

	@Override
	public void writeImpl(GameClient client, WritableBuffer buffer) {
		writeId(ServerPacketId.GM_VIEW_PLEDGE_INFO, buffer);

		buffer.writeInt(0x00);
		buffer.writeString(_player.getName());
		buffer.writeInt(_clan.getId());
		buffer.writeInt(0x00);
		buffer.writeString(_clan.getName());
		buffer.writeString(_clan.getLeaderName());

		buffer.writeInt(_clan.getCrestId()); // -> no, it's no longer used (nuocnam) fix by game
		buffer.writeInt(_clan.getLevel());
		buffer.writeInt(_clan.getCastleId());
		buffer.writeInt(_clan.getHideoutId());
		buffer.writeInt(_clan.getFortId());
		buffer.writeInt(_clan.getRank());
		buffer.writeInt(0x00); // clan rep
		buffer.writeInt(0x00);
		buffer.writeInt(0x00);
		buffer.writeInt(0x00);
		buffer.writeInt(_clan.getAllyId()); // c2
		buffer.writeString(_clan.getAllyName()); // c2
		buffer.writeInt(_clan.getAllyCrestId()); // c2
		buffer.writeInt(_clan.isAtWar() ? 1 : 0); // c3
		buffer.writeInt(0x00); // T3 Unknown

		buffer.writeInt(_clan.getMembers().size());
		for (ClanMember member : _clan.getMembers()) {
			if (member != null) {
				buffer.writeString(member.getName());
				buffer.writeInt(member.getLevel());
				buffer.writeInt(member.getClassId());
				buffer.writeInt(member.getSex() ? 1 : 0);
				buffer.writeInt(member.getRaceOrdinal());
				buffer.writeInt(member.isOnline() ? member.getObjectId() : 0);
				buffer.writeInt(member.getSponsor() != 0 ? 1 : 0);
			}
		}
	}
}
