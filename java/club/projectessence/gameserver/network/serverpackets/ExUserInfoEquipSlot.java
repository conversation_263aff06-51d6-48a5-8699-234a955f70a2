/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.network.serverpackets;

import club.projectessence.commons.network.ServerPacketId;
import club.projectessence.gameserver.enums.InventorySlot;
import club.projectessence.gameserver.model.VariationInstance;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.model.itemcontainer.PlayerInventory;
import club.projectessence.gameserver.model.variables.PlayerVariables;
import club.projectessence.gameserver.network.GameClient;
import io.github.joealisson.mmocore.WritableBuffer;

/**
 * <AUTHOR>
 */
public class ExUserInfoEquipSlot extends AbstractMaskPacket<InventorySlot> {
	private final PlayerInstance _player;

	private final byte[] _masks = new byte[]
			{
					(byte) 0x00,
					(byte) 0x00,
					(byte) 0x00,
					(byte) 0x00,
					(byte) 0x00,
					(byte) 0x00, // 152
					(byte) 0x00, // 152
					(byte) 0x00, // 152
			};

	public ExUserInfoEquipSlot(PlayerInstance player) {
		this(player, true);
	}

	public ExUserInfoEquipSlot(PlayerInstance player, boolean addAll) {
		_player = player;
		if (addAll) {
			addComponentType(InventorySlot.values());
		}
	}

	@Override
	protected byte[] getMasks() {
		return _masks;
	}

	@Override
	public void writeImpl(GameClient client, WritableBuffer buffer) {
		writeId(ServerPacketId.EX_USER_INFO_EQUIP_SLOT, buffer);

		buffer.writeInt(_player.getObjectId());
		buffer.writeShort(InventorySlot.values().length); // 152
		buffer.writeBytes(_masks);

		final PlayerInventory inventory = _player.getInventory();
		for (InventorySlot slot : InventorySlot.values()) {
			if (containsMask(slot)) {
				final VariationInstance augment = inventory.getPaperdollAugmentation(slot.getSlot());
				buffer.writeShort(22); // 10 + 4 * 3
				buffer.writeInt(inventory.getPaperdollObjectId(slot.getSlot()));
				int visualId = inventory.getPaperdollItemVisualId(slot.getSlot());
				int itemId = inventory.getPaperdollItemId(slot.getSlot());
				if ((slot == InventorySlot.CLOAK) && (_player.getVisualArmorTransformationId() > 0) && !_player.getVariables().getBoolean(PlayerVariables.SHOW_CLOAK_WITH_COSTUME, false)) {
					buffer.writeInt(0x00);
					buffer.writeInt(0x00);
					buffer.writeInt(0x00);
					buffer.writeInt(0x00);
				} else if ((visualId > 0) && (slot == InventorySlot.CLOAK)) {
					buffer.writeInt(visualId);
					buffer.writeInt(augment != null ? augment.getOption1Id() : 0);
					buffer.writeInt(augment != null ? augment.getOption2Id() : 0);
					buffer.writeInt(itemId);
				} else {
					buffer.writeInt(itemId);
					buffer.writeInt(augment != null ? augment.getOption1Id() : 0);
					buffer.writeInt(augment != null ? augment.getOption2Id() : 0);
					buffer.writeInt(visualId);
				}
			}
		}
	}
}