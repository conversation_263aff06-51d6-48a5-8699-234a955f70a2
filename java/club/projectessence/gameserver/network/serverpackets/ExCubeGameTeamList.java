/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.network.serverpackets;

import club.projectessence.commons.network.ServerPacketId;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.network.GameClient;
import io.github.joealisson.mmocore.WritableBuffer;

import java.util.List;

/**
 * <AUTHOR>
 */
public class ExCubeGameTeamList extends ServerPacket {
	// Players Lists
	private final List<PlayerInstance> _bluePlayers;
	private final List<PlayerInstance> _redPlayers;

	// Common Values
	private final int _roomNumber;

	/**
	 * Show Minigame Waiting List to Player
	 *
	 * @param redPlayers  Red Players List
	 * @param bluePlayers Blue Players List
	 * @param roomNumber  Arena/Room ID
	 */
	public ExCubeGameTeamList(List<PlayerInstance> redPlayers, List<PlayerInstance> bluePlayers, int roomNumber) {
		_redPlayers = redPlayers;
		_bluePlayers = bluePlayers;
		_roomNumber = roomNumber - 1;
	}

	@Override
	public void writeImpl(GameClient client, WritableBuffer buffer) {
		writeId(ServerPacketId.EX_BLOCK_UP_SET_LIST, buffer);

		buffer.writeInt(0x00);

		buffer.writeInt(_roomNumber);
		buffer.writeInt(0xffffffff);

		buffer.writeInt(_bluePlayers.size());
		for (PlayerInstance player : _bluePlayers) {
			buffer.writeInt(player.getObjectId());
			buffer.writeString(player.getName());
		}
		buffer.writeInt(_redPlayers.size());
		for (PlayerInstance player : _redPlayers) {
			buffer.writeInt(player.getObjectId());
			buffer.writeString(player.getName());
		}
	}
}
