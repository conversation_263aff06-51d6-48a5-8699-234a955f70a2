/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.network.serverpackets;

import club.projectessence.commons.network.ServerPacketId;
import club.projectessence.gameserver.model.Shortcut;
import club.projectessence.gameserver.network.GameClient;
import io.github.joealisson.mmocore.WritableBuffer;

public class ShortCutRegister extends ServerPacket {
	private final Shortcut _shortcut;

	/**
	 * Register new skill shortcut
	 *
	 * @param shortcut
	 */
	public ShortCutRegister(Shortcut shortcut) {
		_shortcut = shortcut;
	}

	@Override
	public void writeImpl(GameClient client, WritableBuffer buffer) {
		writeId(ServerPacketId.SHORT_CUT_REGISTER, buffer);

		buffer.writeInt(_shortcut.getType().ordinal());
		buffer.writeInt(_shortcut.getSlot() + (_shortcut.getPage() * 12)); // C4 Client

		buffer.writeByte(0x00); // 228

		switch (_shortcut.getType()) {
			case ITEM: {
				buffer.writeInt(_shortcut.getId());
				buffer.writeInt(_shortcut.getCharacterType());
				buffer.writeInt(_shortcut.getSharedReuseGroup());
				buffer.writeInt(0x00); // unknown
				buffer.writeInt(0x00); // unknown
				buffer.writeInt(0x00); // item augment id
				buffer.writeInt(0x00); // TODO: Find me, item visual id ?
				break;
			}
			case SKILL: {
				buffer.writeInt(_shortcut.getId());
				buffer.writeShort(_shortcut.getLevel());
				buffer.writeShort(_shortcut.getSubLevel());
				buffer.writeInt(_shortcut.getSharedReuseGroup());
				buffer.writeByte(0x00); // C5
				buffer.writeInt(_shortcut.getCharacterType());
				buffer.writeInt(0x00); // TODO: Find me
				buffer.writeInt(0x00); // TODO: Find me
				break;
			}
			case ACTION:
			case MACRO:
			case RECIPE:
			case BOOKMARK: {
				buffer.writeInt(_shortcut.getId());
				buffer.writeInt(_shortcut.getCharacterType());
				break;
			}
		}
	}
}
