/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.network.serverpackets;

import club.projectessence.commons.network.ServerPacketId;
import club.projectessence.gameserver.data.xml.SkillData;
import club.projectessence.gameserver.model.TimeStamp;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.network.GameClient;
import io.github.joealisson.mmocore.WritableBuffer;

import java.util.ArrayList;
import java.util.List;

/**
 * Skill Cool Time server packet implementation.
 *
 * <AUTHOR> Zoey76, Mobius
 */
public class SkillCoolTime extends ServerPacket {
	private final long _currentTime;
	private final List<TimeStamp> _skillReuseTimeStamps = new ArrayList<>();

	public SkillCoolTime(PlayerInstance player) {
		_currentTime = System.currentTimeMillis();
		for (TimeStamp ts : player.getSkillReuseTimeStamps().values()) {
			if ((_currentTime < ts.getStamp()) && !SkillData.getInstance().getSkill(ts.getSkillId(), ts.getSkillLevel(), ts.getSkillSubLevel()).isNotBroadcastable()) {
				_skillReuseTimeStamps.add(ts);
			}
		}
	}

	@Override
	public void writeImpl(GameClient client, WritableBuffer buffer) {
		writeId(ServerPacketId.SKILL_COOL_TIME, buffer);

		buffer.writeInt(_skillReuseTimeStamps.size());
		for (TimeStamp ts : _skillReuseTimeStamps) {
			buffer.writeInt(ts.getSkillId());
			buffer.writeInt(0x00); // ?
			buffer.writeInt((int) ts.getReuse() / 1000);
			buffer.writeInt((int) Math.max(ts.getStamp() - _currentTime, 0) / 1000);
		}
	}
}
