/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.network.serverpackets;

import club.projectessence.commons.network.ServerPacketId;
import club.projectessence.gameserver.instancemanager.CastleManorManager;
import club.projectessence.gameserver.model.Seed;
import club.projectessence.gameserver.network.GameClient;
import io.github.joealisson.mmocore.WritableBuffer;

import java.util.List;

/**
 * <AUTHOR>
 */
public class ExShowManorDefaultInfo extends ServerPacket {
	private final List<Seed> _crops;
	private final boolean _hideButtons;

	public ExShowManorDefaultInfo(boolean hideButtons) {
		_crops = CastleManorManager.getInstance().getCrops();
		_hideButtons = hideButtons;
	}

	@Override
	public void writeImpl(GameClient client, WritableBuffer buffer) {
		writeId(ServerPacketId.EX_SHOW_MANOR_DEFAULT_INFO, buffer);

		buffer.writeByte(_hideButtons ? 0x01 : 0x00); // Hide "Seed Purchase" and "Crop Sales" buttons
		buffer.writeInt(_crops.size());
		for (Seed crop : _crops) {
			buffer.writeInt(crop.getCropId()); // crop Id
			buffer.writeInt(crop.getLevel()); // level
			buffer.writeInt(crop.getSeedReferencePrice()); // seed price
			buffer.writeInt(crop.getCropReferencePrice()); // crop price
			buffer.writeByte(1); // Reward 1 type
			buffer.writeInt(crop.getReward(1)); // Reward 1 itemId
			buffer.writeByte(1); // Reward 2 type
			buffer.writeInt(crop.getReward(2)); // Reward 2 itemId
		}
	}
}