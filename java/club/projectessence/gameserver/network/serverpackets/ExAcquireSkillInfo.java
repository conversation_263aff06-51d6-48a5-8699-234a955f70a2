/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.network.serverpackets;

import club.projectessence.commons.network.ServerPacketId;
import club.projectessence.gameserver.model.SkillLearn;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.model.holders.AlternativeItemHolder;
import club.projectessence.gameserver.model.skills.Skill;
import club.projectessence.gameserver.network.GameClient;
import io.github.joealisson.mmocore.WritableBuffer;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public class ExAcquireSkillInfo extends ServerPacket {
	private final int _id;
	private final int _level;
	private final int _dualClassLevel;
	private final long _spCost;
	private final int _minLevel;
	private final List<AlternativeItemHolder> _itemReq;
	private final List<Skill> _skillRem;

	/**
	 * Special constructor for Alternate Skill Learning system.<br>
	 * Sets a custom amount of SP.
	 *
	 * @param player
	 * @param skillLearn the skill learn.
	 */
	public ExAcquireSkillInfo(PlayerInstance player, SkillLearn skillLearn) {
		if (player.getReplacedSkills().containsKey(skillLearn.getSkillId())) {
			_id = player.getReplacedSkills().get(skillLearn.getSkillId());
		} else {
			_id = skillLearn.getSkillId();
		}
		_level = skillLearn.getSkillLevel();
		_dualClassLevel = skillLearn.getDualClassLevel();
		_spCost = skillLearn.getLevelUpSp();
		_minLevel = skillLearn.getGetLevel();
		_itemReq = skillLearn.getRequiredItems();
		_skillRem = skillLearn.getRemoveSkills().stream().map(player::getKnownSkill).filter(Objects::nonNull).collect(Collectors.toList());
	}

	@Override
	public void writeImpl(GameClient client, WritableBuffer buffer) {
		writeId(ServerPacketId.EX_ACQUIRE_SKILL_INFO, buffer);

		buffer.writeInt(_id);
		buffer.writeInt(_level);
		buffer.writeLong(_spCost);
		buffer.writeShort(_minLevel);
		buffer.writeShort(_dualClassLevel);
		buffer.writeInt(_itemReq.size());
		for (AlternativeItemHolder holder : _itemReq) {
			buffer.writeInt(holder.getIds().get(0));
			buffer.writeLong(holder.getCount());
		}

		buffer.writeInt(_skillRem.size());
		for (Skill skill : _skillRem) {
			buffer.writeInt(skill.getId());
			buffer.writeInt(skill.getLevel());
		}
	}
}
