/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.network.serverpackets;

import club.projectessence.commons.network.ServerPacketId;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.model.quest.Quest;
import club.projectessence.gameserver.model.quest.QuestState;
import club.projectessence.gameserver.network.GameClient;
import io.github.joealisson.mmocore.WritableBuffer;

import java.util.List;

/**
 * <AUTHOR>
 */
public class GmViewQuestInfo extends ServerPacket {
	private final PlayerInstance _player;
	private final List<Quest> _questList;

	public GmViewQuestInfo(PlayerInstance player) {
		_player = player;
		_questList = player.getAllActiveQuests();
	}

	@Override
	public void writeImpl(GameClient client, WritableBuffer buffer) {
		writeId(ServerPacketId.GM_VIEW_QUEST_INFO, buffer);
		buffer.writeString(_player.getName());
		buffer.writeShort(_questList.size()); // quest count

		for (Quest quest : _questList) {
			final QuestState qs = _player.getQuestState(quest.getName());
			buffer.writeInt(quest.getId());
			buffer.writeInt(qs == null ? 0 : qs.getCond());
		}
		buffer.writeShort(0x00); // some size
		// for size; ddQQ
	}
}
