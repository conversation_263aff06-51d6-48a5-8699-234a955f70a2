package club.projectessence.gameserver.communitybbs.SunriseBoards.dropCalc;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.logging.Logger;

import club.projectessence.gameserver.data.xml.NpcData;
import club.projectessence.gameserver.enums.DropType;
import club.projectessence.gameserver.model.actor.templates.NpcTemplate;
import club.projectessence.gameserver.model.holders.DropHolder;

public class DropInfoHandler
{
	protected static final Logger							_log				= Logger.getLogger(DropInfoHandler.class.getName());
	private final Map<Integer, ArrayList<DropInfoHolder>>	allItemDropIndex	= new HashMap<>();
	private final Map<Integer, Boolean>						npcHasDrop			= new HashMap<>();
	private final Map<Integer, Boolean>						npcHasSpoil			= new HashMap<>();
	public static final Set<Integer>						HERBS				= new HashSet<>();
	
	public DropInfoHandler()
	{
		load();
	}
	
	public void load()
	{
		// _log.info("Starting to load drop data for DropInfoHandler...");
		loadHerbList();
		buildDropIndex();
		_log.info(getClass().getSimpleName() + ": Loaded " + allItemDropIndex.size() + " drop data for calculator.");
	}
	
	private static void loadHerbList()
	{
		HERBS.addAll(new Range(8154, 8158).values()); // HERBs
		HERBS.addAll(new Range(8600, 8615).values()); // HERBs
		HERBS.addAll(new Range(8952, 8954).values()); // HERBs
		HERBS.addAll(new Range(10655, 10658).values()); // HERBs
		HERBS.addAll(new Range(10655, 10658).values()); // HERBs
		HERBS.add(13028);// Vitality Replenishing Herb
		HERBS.addAll(new Range(10432, 10434).values());// Kertin's Herb
	}
	
	public void addDropInfo(int npcId, DropInfoHolder drop)
	{
		getInfo().computeIfAbsent(drop.getItemId(), k -> new ArrayList<>()).add(drop);
		if (drop.isSweep())
		{
			npcHasSpoil.put(npcId, true);
		}
		else
		{
			npcHasDrop.put(npcId, true);
		}
	}
	
	public boolean hasDropOrSpoil(int npcId)
	{
		return npcHasDrop.getOrDefault(npcId, false) || npcHasSpoil.getOrDefault(npcId, false);
	}
	
	private void buildDropIndex()
	{
		// _log.info("Building drop index for DropInfoHandler...");
		int npcCount = 0;
		int dropCount = 0;
		allItemDropIndex.clear(); // Xóa dữ liệu cũ
		for (NpcTemplate npcTemplate : NpcData.getInstance().getTemplates(npc -> npc.getDropList(DropType.DROP) != null || npc.getDropList(DropType.SPOIL) != null))
		{
			npcCount++;
			List<DropHolder> dropList = npcTemplate.getDropList(DropType.DROP);
			if (dropList != null)
			{
				for (DropHolder dropHolder : dropList)
				{
					if (!HERBS.contains(dropHolder.getItemId()))
					{
						addDropInfo(npcTemplate.getId(), new DropInfoHolder(npcTemplate.getId(), npcTemplate.getName(), npcTemplate.getLevel(), dropHolder.getMin(), dropHolder.getMax(), dropHolder.getChance(), dropHolder.getDropType() == DropType.SPOIL, dropHolder.getItemId()));
						dropCount++;
					}
				}
			}
			List<DropHolder> spoilList = npcTemplate.getDropList(DropType.SPOIL);
			if (spoilList != null)
			{
				for (DropHolder dropHolder : spoilList)
				{
					if (!HERBS.contains(dropHolder.getItemId()))
					{
						addDropInfo(npcTemplate.getId(), new DropInfoHolder(npcTemplate.getId(), npcTemplate.getName(), npcTemplate.getLevel(), dropHolder.getMin(), dropHolder.getMax(), dropHolder.getChance(), dropHolder.getDropType() == DropType.SPOIL, dropHolder.getItemId()));
						dropCount++;
					}
				}
			}
		}
		// _log.info("Processed " + npcCount + " NPCs, " + dropCount + " drops");
		allItemDropIndex.values().forEach(list -> list.sort((d1, d2) -> Byte.valueOf((byte) d1.getLevel()).compareTo(Byte.valueOf((byte) d2.getLevel()))));
	}
	
	public ArrayList<DropInfoHolder> getDrop(int itemId)
	{
		return allItemDropIndex.get(itemId);
	}
	
	public Map<Integer, ArrayList<DropInfoHolder>> getInfo()
	{
		return allItemDropIndex;
	}
	
	public Comparator<DropInfoHolder> compareByChances = (o1, o2) ->
	{
		double level1 = o1.getChance();
		double level2 = o2.getChance();
		return level1 < level2 ? 1 : level1 == level2 ? 0 : -1;
	};
	
	public static DropInfoHandler getInstance()
	{
		return SingletonHolder._instance;
	}
	
	private static class SingletonHolder
	{
		protected static final DropInfoHandler _instance = new DropInfoHandler();
	}
}
