package club.projectessence.gameserver.communitybbs.SunriseBoards.dropCalc;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import club.projectessence.Config;
import club.projectessence.gameserver.cache.HtmCache;
import club.projectessence.gameserver.communitybbs.SunriseBoards.dropCalc.utils.Util;
import club.projectessence.gameserver.data.ItemTable;
import club.projectessence.gameserver.data.xml.NpcData;
import club.projectessence.gameserver.instancemanager.PremiumManager;
import club.projectessence.gameserver.enums.DropType;
import club.projectessence.gameserver.model.World;
import club.projectessence.gameserver.model.actor.Npc;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.model.actor.templates.NpcTemplate;
import club.projectessence.gameserver.model.holders.DropHolder;
import club.projectessence.gameserver.model.itemcontainer.Inventory;
import club.projectessence.gameserver.model.items.Item;
import club.projectessence.gameserver.model.skills.Skill;
import club.projectessence.gameserver.model.stats.MoveType;
import club.projectessence.gameserver.model.stats.Stat;
import club.projectessence.gameserver.network.serverpackets.NpcHtmlMessage;

/**
 * <AUTHOR>
 */
public class DropInfoFunctions
{
	public static String getMinMaxDropCounts(NpcTemplate monster, int itemId, boolean drop)
	{
		long min = getDropMinMaxCounts(monster, itemId, drop, true);
		long max = getDropMinMaxCounts(monster, itemId, drop, false);
		String formattedCounts = "[" + min + "..." + max + ']';
		return formattedCounts;
	}
	
	private static long getDropMinMaxCounts(NpcTemplate template, int itemId, boolean isSpoil, boolean min)
	{
		long result = 0;
		List<DropInfoHolder> drops = DropInfoHandler.getInstance().getDrop(itemId);
		for (DropInfoHolder drop : drops)
		{
			if (drop.getNpcId() == template.getId())
			{
				if (drop.isSweep() && isSpoil)
				{
					return min ? drop.getMin() : drop.getMax();
				}
				else if (!drop.isSweep() && !isSpoil)
				{
					return min ? drop.getMin() : drop.getMax();
				}
			}
		}
		return result;
	}
	
	public static String getDropChance(NpcTemplate template, int itemId, boolean isSpoil, PlayerInstance player)
	{
		double chance = 0.0;
		List<DropInfoHolder> drops = DropInfoHandler.getInstance().getDrop(itemId);
		if (drops == null)
		{
			return Util.formatDouble(chance, "#.####");
		}
		double rateChance = 1.0;
		for (DropInfoHolder drop : drops)
		{
			if (drop.getNpcId() == template.getId())
			{
				if (drop.isSweep() && isSpoil)
				{
					rateChance = Config.RATE_SPOIL_DROP_CHANCE_MULTIPLIER;
					rateChance *= player.getStat().getMul(Stat.BONUS_SPOIL_RATE, 1);
					if (Config.PREMIUM_SYSTEM_ENABLED && PremiumManager.getInstance().hasAnyPremiumBenefits(player))
					{
						rateChance *= PremiumManager.getInstance().getEffectivePremiumRate(player, "SPOIL_CHANCE");
					}
					return Util.formatDouble(drop.getChance() * rateChance, "#.####");
				}
				else if (!drop.isSweep() && !isSpoil)
				{
					if (Config.RATE_DROP_CHANCE_BY_ID.get(drop.getItemId()) != null)
					{
						rateChance *= Config.RATE_DROP_CHANCE_BY_ID.get(drop.getItemId());
					}
					else if (ItemTable.getInstance().getTemplate(drop.getItemId()).hasExImmediateEffect())
					{
						rateChance *= Config.RATE_HERB_DROP_CHANCE_MULTIPLIER;
					}
					else if (template.getType().equalsIgnoreCase("RaidBoss") || template.getType().equalsIgnoreCase("GrandBoss"))
					{
						rateChance *= Config.RATE_RAID_DROP_CHANCE_MULTIPLIER;
					}
					else
					{
						rateChance *= Config.RATE_DEATH_DROP_CHANCE_MULTIPLIER;
					}
					rateChance *= player.getStat().getMul(Stat.BONUS_DROP_RATE, 1);
					if (Config.PREMIUM_SYSTEM_ENABLED && PremiumManager.getInstance().hasAnyPremiumBenefits(player))
					{
						float premiumDropChanceById = PremiumManager.getInstance().getEffectivePremiumRate(player, "DROP_CHANCE_BY_ID_" + drop.getItemId());
						if (premiumDropChanceById > 1.0f)
						{
							rateChance *= premiumDropChanceById;
						}
						else if (ItemTable.getInstance().getTemplate(drop.getItemId()).hasExImmediateEffect())
						{
							// Không áp dụng Premium cho herb
						}
						else if (template.getType().equalsIgnoreCase("RaidBoss") || template.getType().equalsIgnoreCase("GrandBoss"))
						{
							// Không áp dụng thêm cho raid
						}
						else
						{
							rateChance *= PremiumManager.getInstance().getEffectivePremiumRate(player, "DROP_CHANCE");
						}
					}
					boolean isMobForgottenIsland = false;
					switch (template.getId())
					{
						case 21746:
						case 21737:
						case 21748:
						case 21747:
						case 21753:
						case 21750:
						case 21749:
						case 21739:
						case 21738:
						case 21752:
						case 21741:
						case 21757:
						case 21740:
						case 21733:
						case 21734:
						case 21735:
						case 21736:
						case 21742:
						case 21743:
						case 21744:
						case 21745:
						case 21756:
						case 21759:
						case 21760:
							isMobForgottenIsland = true;
							break;
					}
					if (isMobForgottenIsland && (drop.getItemId() == Inventory.ADENA_ID))
					{
						rateChance *= 0.5;
					}
					return Util.formatDouble(drop.getChance() * rateChance, "#.####");
				}
			}
		}
		return Util.formatDouble(chance, "#.####");
	}
	
	public static int getDropsCount(NpcTemplate template, boolean spoil)
	{
		int dropCounts = 0;
		for (ArrayList<DropInfoHolder> drop : DropInfoHandler.getInstance().getInfo().values())
		{
			for (int i = 0; i < drop.size(); i++)
			{
				DropInfoHolder itemDrop = drop.get(i);
				if (itemDrop.getNpcId() == template.getId())
				{
					if (itemDrop.isSweep() && spoil)
					{
						dropCounts++;
					}
					else if (!itemDrop.isSweep() && !spoil)
					{
						dropCounts++;
					}
				}
			}
		}
		return dropCounts;
	}
	
	// Trong class DropInfoFunctions
	public static void showNpcDropList(PlayerInstance activeChar, String dropType, int npcId, int page)
	{
		// Lấy template của NPC
		NpcTemplate npcData = NpcData.getInstance().getTemplate(npcId);
		if (npcData == null)
		{
			activeChar.sendMessage("Unknown NPC template ID " + npcId);
			return;
		}
		// Tạo HTML trả lời
		final StringBuilder replyMSG = new StringBuilder();
		replyMSG.append("<html><title>Show drop/spoil list page ").append(page).append("</title><body><br><center><font color=\"LEVEL\">").append(npcData.getName()).append(" (").append(npcId).append(")</font><br>");
		// Khai báo biến itemsPerPage
		int itemsPerPage = 10; // Số lượng item mỗi trang
		// Tạo danh sách drop tổng hợp
		List<DropHolder> combinedDropList = new ArrayList<>();
		if (dropType.equals("DROP"))
		{
			// Lấy danh sách drop cơ bản
			List<DropHolder> dropList = npcData.getDropList(DropType.DROP); // Sửa: Thêm DropType.DROP
			if (dropList != null)
			{
				combinedDropList.addAll(dropList);
			}
		}
		else if (dropType.equals("SPOIL"))
		{
			List<DropHolder> spoilList = npcData.getDropList(DropType.SPOIL); // Sửa: Thêm DropType.SPOIL
			if (spoilList != null)
			{
				combinedDropList.addAll(spoilList);
			}
		}
		else
		{
			activeChar.sendMessage("Invalid drop type: " + dropType);
			return;
		}
		// Kiểm tra danh sách drop/spoil tổng hợp
		if (combinedDropList.isEmpty())
		{
			replyMSG.append("<br><center>No ").append(dropType.equals("DROP") ? "drops" : "spoils").append(" available for this NPC.</center><br>");
		}
		else
		{
			// Tính toán phân trang
			int totalItems = combinedDropList.size();
			int totalPages = (int) Math.ceil((double) totalItems / itemsPerPage);
			if (page > totalPages)
			{
				page = totalPages;
			}
			int startIndex = (page - 1) * itemsPerPage;
			int endIndex = Math.min(startIndex + itemsPerPage, totalItems);
			// Hiển thị từng item
			for (int i = startIndex; i < endIndex; i++)
			{
				DropHolder dropHolder = combinedDropList.get(i);
				// Bỏ qua các loại HERB (nếu cần thiết)
				if (DropInfoHandler.HERBS.contains(dropHolder.getItemId()))
				{
					continue;
				}
				// Lấy thông tin item
				Item item = ItemTable.getInstance().getTemplate(dropHolder.getItemId());
				if (item != null)
				{
					// Hiển thị thông tin item, bao gồm tỷ lệ rơi và số lượng min-max
					replyMSG.append("<br1><center><img src=\"l2ui.squaregray\" width=\"280\" height=\"2\"></center>").append("<center><table border=0 cellpadding=0 cellspacing=0 width=\"280\" height=\"40\" bgcolor=\"2E2E2E\"><tr>").append("<td FIXWIDTH=32><table><tr><td width=32 align=right valign=top><table border=0 cellspacing=0 cellpadding=0 width=32 height=32 background=\"").append(item.getIcon()).append("\"><tr><td width=32 height=32 align=center valign=top>").append("<button value=\" \" action=\"bypass -h _bbssearchdropMonstersByItem_").append(item.getId()).append("_1\" width=32 height=32 back=\"L2UI_CT1.ItemWindow_DF_Frame_Down\" fore=\"L2UI_CT1.ItemWindow_DF_Frame\"></button>").append("</td></tr></table></td></tr></table></td>").append("<td FIXWIDTH=200 align=center valign=top><font color=\"F4FA58\" name=\"hs9\">").append(getNameLong(item.getName())).append("</font><br1>").append("<font color=\"F4FA58\">Amount: ").append(dropHolder.getMin()).append(" - ").append(dropHolder.getMax()).append("</font><br1><font color=\"5882FA\">Chance: ").append(Util.formatDouble(dropHolder.getChance(), "#.#####")).append("%</font></td></tr></table></center>");
				}
			}
			// Logic phân trang
			replyMSG.append("<br><table width=280 bgcolor=666666 border=0><tr>");
			if (page > 1)
			{
				replyMSG.append("<td width=120><button value=\" \" action=\"bypass -h _bbssearchNpcDropList_").append(dropType).append("_").append(npcId).append("_").append(page - 1).append("\" width=16 height=16 back=\"L2UI_CH3.shortcut_prev_down\" fore=\"L2UI_CH3.shortcut_prev\"></button></td>");
			}
			replyMSG.append("<td width=100 align=center>Page ").append(page).append(" of ").append(totalPages).append("</td>");
			if (page < totalPages)
			{
				replyMSG.append("<td width=120 align=right><button value=\" \" action=\"bypass -h _bbssearchNpcDropList_").append(dropType).append("_").append(npcId).append("_").append(page + 1).append("\" width=16 height=16 back=\"L2UI_CH3.shortcut_next_down\" fore=\"L2UI_CH3.shortcut_next\"></button></td>");
			}
			replyMSG.append("</tr></table>");
		}
		replyMSG.append("</body></html>");
		// Gửi HTML trả lời cho người chơi
		NpcHtmlMessage adminReply = new NpcHtmlMessage();
		adminReply.setHtml(replyMSG.toString());
		activeChar.sendPacket(adminReply);
	}
	
	public static void showNpcSkillList(PlayerInstance activeChar, int npcId, int page)
	{
		NpcTemplate npcData = NpcData.getInstance().getTemplate(npcId);
		if (npcData == null)
		{
			activeChar.sendMessage("Template id unknown: " + npcId);
			return;
		}
		Map<Integer, Skill> skills = new HashMap<>(npcData.getSkills());
		int _skillsize = skills.size();
		int MaxSkillsPerPage = 20;
		int MaxPages = _skillsize / MaxSkillsPerPage;
		if (_skillsize > (MaxSkillsPerPage * MaxPages))
		{
			MaxPages++;
		}
		if (page > MaxPages)
		{
			page = MaxPages;
		}
		int SkillsStart = MaxSkillsPerPage * page;
		int SkillsEnd = _skillsize;
		if ((SkillsEnd - SkillsStart) > MaxSkillsPerPage)
		{
			SkillsEnd = SkillsStart + MaxSkillsPerPage;
		}
		StringBuffer replyMSG = new StringBuffer("<html><title>NPC Skill List</title><body><center><font color=\"LEVEL\">");
		replyMSG.append(npcData.getName());
		replyMSG.append(" (");
		replyMSG.append(npcData.getId());
		replyMSG.append("): ");
		replyMSG.append(_skillsize);
		replyMSG.append(" skills</font></center><table width=300 bgcolor=666666><tr>");
		for (int x = 0; x < MaxPages; x++)
		{
			int pagenr = x + 1;
			if (page == x)
			{
				replyMSG.append("<td>Page ");
				replyMSG.append(pagenr);
				replyMSG.append("</td>");
			}
			else
			{
				replyMSG.append("<td><a action=\"bypass -h _bbssearchShowSkills_");
				replyMSG.append(npcData.getId());
				replyMSG.append("_");
				replyMSG.append(x);
				replyMSG.append("\"> Page ");
				replyMSG.append(pagenr);
				replyMSG.append(" </a></td>");
			}
		}
		replyMSG.append("</tr></table><br><table width=\"100%\" border=0><tr><td>Skill name [skill id-skill lvl]</td></tr>");
		Iterator<Skill> skillite = skills.values().iterator();
		for (int i = 0; i < SkillsStart; i++)
		{
			if (skillite.hasNext())
			{
				skillite.next();
			}
		}
		int cnt = SkillsStart;
		Skill sk;
		while (skillite.hasNext())
		{
			cnt++;
			if (cnt > SkillsEnd)
			{
				break;
			}
			sk = skillite.next();
			replyMSG.append("<tr><td width=240>");
			replyMSG.append(sk.getName());
			replyMSG.append(" [");
			replyMSG.append(sk.getId());
			replyMSG.append("-");
			replyMSG.append(sk.getLevel());
			replyMSG.append("]</td></tr>");
		}
		NpcHtmlMessage adminReply = new NpcHtmlMessage();
		adminReply.setHtml(replyMSG.toString());
		activeChar.sendPacket(adminReply);
	}
	
	public static void showStats(PlayerInstance activeChar, int npcId)
	{
		final NpcHtmlMessage html = new NpcHtmlMessage();
		String html1 = HtmCache.getInstance().getHtm(activeChar, "data/html/CommunityBoard/Custom/DropCalculator/bbs_mobStats.htm");
		NpcTemplate target = NpcData.getInstance().getTemplate(npcId);
		if (target != null)
		{
			// Combat Stats
			html1 = html1.replace("%patk%", Util.formatDouble(target.getBasePAtk(), "#.##"));
			html1 = html1.replace("%matk%", Util.formatDouble(target.getBaseMAtk(), "#.##"));
			html1 = html1.replace("%pdef%", Util.formatDouble(target.getBasePDef(), "#.##"));
			html1 = html1.replace("%mdef%", Util.formatDouble(target.getBaseMDef(), "#.##"));
			html1 = html1.replace("%accu%", "N/A");
			html1 = html1.replace("%evas%", "N/A");
			html1 = html1.replace("%crit%", Util.formatDouble(target.getBaseCritRate(), "#.##"));
			html1 = html1.replace("%rspd%", Util.formatDouble(target.getBaseMoveSpeed(MoveType.RUNNING), "#.##"));
			html1 = html1.replace("%aspd%", Util.formatDouble(target.getBasePAtkSpd(), "#.##"));
			html1 = html1.replace("%cspd%", Util.formatDouble(target.getBaseMAtkSpd(), "#.##"));
			// Basic Stats
			html1 = html1.replace("%str%", Util.formatDouble(target.getBaseSTR(), "#.##"));
			html1 = html1.replace("%dex%", Util.formatDouble(target.getBaseDEX(), "#.##"));
			html1 = html1.replace("%con%", Util.formatDouble(target.getBaseCON(), "#.##"));
			html1 = html1.replace("%int%", Util.formatDouble(target.getBaseINT(), "#.##"));
			html1 = html1.replace("%wit%", Util.formatDouble(target.getBaseWIT(), "#.##"));
			html1 = html1.replace("%men%", Util.formatDouble(target.getBaseMEN(), "#.##"));
			// Elements Stats
			html1 = html1.replace("%ele_atk%", "N/A");
			html1 = html1.replace("%ele_atk_value%", "N/A");
			html1 = html1.replace("%ele_dfire%", Util.formatDouble(target.getBaseFireRes(), "#.##"));
			html1 = html1.replace("%ele_dwater%", Util.formatDouble(target.getBaseWaterRes(), "#.##"));
			html1 = html1.replace("%ele_dwind%", Util.formatDouble(target.getBaseWindRes(), "#.##"));
			html1 = html1.replace("%ele_dearth%", Util.formatDouble(target.getBaseEarthRes(), "#.##"));
			html1 = html1.replace("%ele_dholy%", Util.formatDouble(target.getBaseHolyRes(), "#.##"));
			html1 = html1.replace("%ele_ddark%", Util.formatDouble(target.getBaseDarkRes(), "#.##"));
		}
		html.setHtml(html1.toString());
		activeChar.sendPacket(html);
	}
	
	public static int getDroplistsCountByItemId(int itemId, boolean isSpoil)
	{
		List<DropInfoHolder> drops = DropInfoHandler.getInstance().getDrop(itemId);
		if (drops == null)
		{
			return 0;
		}
		int dropCounts = 0;
		for (DropInfoHolder drop : drops)
		{
			if (drop.isSweep() && isSpoil)
			{
				dropCounts++;
			}
			else if (!drop.isSweep() && !isSpoil)
			{
				dropCounts++;
			}
		}
		return dropCounts;
	}
	
	public static String getName(String name)
	{
		if (name.length() > 20)
		{
			return name.substring(0, 19) + "...";
		}
		return name;
	}
	
	public static String getNameLong(String name)
	{
		if (name.length() > 36)
		{
			return name.substring(0, 35) + "...";
		}
		return name;
	}
	
	public static Npc getAliveNpc(int npcId)
	{
		List<Npc> instances = World.getInstance().getAllByNpcId(npcId);
		return instances.isEmpty() ? null : instances.get(0);
	}
	
	public static List<Item> getItemsByNameContainingString(String itemName)
	{
		List<Item> itemsByName = new ArrayList<>();
		for (Item item : ItemTable.getInstance().getAllItems())
		{
			if ((item != null) && (item.getName() != null))
			{
				if (item.getName().toLowerCase().contains(itemName.toLowerCase()))
				{
					itemsByName.add(item);
				}
			}
		}
		return itemsByName;
	}
	
	public static List<Item> sortItemTemplates(List<Item> items, int sort)
	{
		Collections.sort(items, new ItemTemplateComparator(sort));
		return items;
	}
	
	public static List<NpcTemplate> getNpcsContainingString(String monsterName)
	{
		List<NpcTemplate> npcTemplates = new ArrayList<>();
		for (NpcTemplate npcTemplate : NpcData.getInstance().getAllNpcs())
		{
			if (npcTemplate.getName().toLowerCase().contains(monsterName.toLowerCase()))
			{
				npcTemplates.add(npcTemplate);
			}
		}
		return npcTemplates;
	}
	
	public static List<Item> sortItems(List<Item> itemsByName, int sort)
	{
		Collections.sort(itemsByName, new ItemComparator(sort));
		return itemsByName;
	}
	
	private static class ItemComparator implements Comparator<Item>, Serializable
	{
		private static final long	serialVersionUID	= -6389059445439769861L;
		private final int			sort;
		
		protected ItemComparator(int sort)
		{
			this.sort = sort;
		}
		
		@Override
		public int compare(Item o1, Item o2)
		{
			switch (sort)
			{
				case 0: // By name
					return o1.getName().compareTo(o2.getName());
				case 1: // By drops count
					return Integer.compare(getDroplistsCountByItemId(o2.getId(), false), getDroplistsCountByItemId(o1.getId(), false));
				case 2:// By spoil count
					return Integer.compare(getDroplistsCountByItemId(o2.getId(), true), getDroplistsCountByItemId(o1.getId(), true));
				default:
					return o1.getName().compareTo(o2.getName());
			}
		}
	}
	
	public static List<NpcTemplate> sortMonsters(List<NpcTemplate> npcTemplates, int sort)
	{
		Collections.sort(npcTemplates, new MonsterComparator(sort));
		return npcTemplates;
	}
	
	private static class MonsterComparator implements Comparator<NpcTemplate>, Serializable
	{
		private static final long	serialVersionUID	= 2116090903265145828L;
		private final int			sort;
		
		protected MonsterComparator(int sort)
		{
			this.sort = sort;
		}
		
		@Override
		public int compare(NpcTemplate o1, NpcTemplate o2)
		{
			switch (sort)
			{
				case 0: // By name
					return o1.getName().compareTo(o2.getName());
				case 1:// By drops count
					return Integer.compare(getDropsCount(o2, false), getDropsCount(o1, false));
				case 2:// By spoil count
					return Integer.compare(getDropsCount(o2, true), getDropsCount(o1, true));
				default:
					return o1.getName().compareTo(o2.getName());
			}
		}
	}
	
	public static List<DropInfoHolder> sortMonsters2(List<DropInfoHolder> templates, int sort)
	{
		if (templates == null)
		{
			templates = new ArrayList<>();
		}
		Collections.sort(templates, new MonstersComparator(sort));
		return templates;
	}
	
	private static class MonstersComparator implements Comparator<DropInfoHolder>, Serializable
	{
		private static final long	serialVersionUID	= -3803552841261367731L;
		private final int			sort;
		
		protected MonstersComparator(int sort)
		{
			this.sort = sort;
		}
		
		@Override
		public int compare(DropInfoHolder o1, DropInfoHolder o2)
		{
			switch (sort)
			{
				case 0: // By name
					return o1.getName().compareTo(o2.getName());
				case 1:// By level
					return Integer.compare(o2.getLevel(), o1.getLevel());
				case 2:// By chance
					return Double.compare(o2.getChance(), o1.getChance());
				case 3:// By type
					return Boolean.compare(o2.isSweep(), o1.isSweep());
				case 4:// By drop count
					return Long.compare(o2.getMin() + o2.getMax(), o1.getMin() + o1.getMax());
				default:
					return o1.getName().compareTo(o2.getName());
			}
		}
	}
	
	private static class ItemTemplateComparator implements Comparator<Item>, Serializable
	{
		private static final long	serialVersionUID	= -6389059445439769861L;
		private final int			sort;
		
		protected ItemTemplateComparator(int sort)
		{
			this.sort = sort;
		}
		
		@Override
		public int compare(Item o1, Item o2)
		{
			switch (sort)
			{
				case 0:
					return o1.getName().compareTo(o2.getName());
				case 1:
					return Integer.compare(DropInfoFunctions.getDroplistsCountByItemId(o2.getId(), false), DropInfoFunctions.getDroplistsCountByItemId(o1.getId(), false));
				case 2:
					return Integer.compare(DropInfoFunctions.getDroplistsCountByItemId(o2.getId(), true), DropInfoFunctions.getDroplistsCountByItemId(o1.getId(), true));
				default:
					return o1.getName().compareTo(o2.getName());
			}
		}
	}
}
