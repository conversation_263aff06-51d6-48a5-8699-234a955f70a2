package club.projectessence.gameserver.communitybbs.SunriseBoards.dropCalc.packets;

import club.projectessence.Config;
import club.projectessence.commons.network.ServerPacketId;
import club.projectessence.gameserver.network.GameClient;
import club.projectessence.gameserver.network.serverpackets.ServerPacket;
import io.github.joealisson.mmocore.WritableBuffer;

public class DropInfoPledgeCrest extends ServerPacket
{
	private final int		_crestId;
	private final byte[]	_data;
	
	public DropInfoPledgeCrest(int crestId, byte[] data)
	{
		_crestId = crestId;
		_data = data;
	}
	
	@Override
	protected void writeImpl(GameClient client, WritableBuffer buffer)
	{
		writeId(ServerPacketId.PLEDGE_CREST, buffer);
		buffer.writeInt(Config.SERVER_ID);
		buffer.writeInt(_crestId);
		if (_data != null)
		{
			buffer.writeInt(_data.length);
			buffer.writeBytes(_data);
		}
		else
		{
			buffer.writeInt(0);
		}
	}
}
