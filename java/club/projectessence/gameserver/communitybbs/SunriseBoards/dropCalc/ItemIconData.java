package club.projectessence.gameserver.communitybbs.SunriseBoards.dropCalc;

import java.io.File;
import java.util.HashMap;
import java.util.Map;

import org.w3c.dom.Document;
import org.w3c.dom.NamedNodeMap;
import org.w3c.dom.Node;

import club.projectessence.commons.util.IXmlReader;

public class ItemIconData implements IXmlReader
{
	private final Map<Integer, String> _itemIcons = new HashMap<>();
	
	protected ItemIconData()
	{
		load();
	}
	
	@Override
	public void load()
	{
		_itemIcons.clear();
		parseDatapackFile("data/itemIcons.xml");
		LOGGER.info(getClass().getSimpleName() + ": Loaded " + _itemIcons.size() + " item icons.");
	}
	
	@Override
	public void parseDocument(Document doc, File f)
	{
		final Node table = doc.getFirstChild();
		NamedNodeMap attrs;
		for (Node n = table.getFirstChild(); n != null; n = n.getNextSibling())
		{
			if ("item".equals(n.getNodeName()))
			{
				attrs = n.getAttributes();
				_itemIcons.put(parseInteger(attrs, "id"), parseString(attrs, "icon"));
			}
		}
	}
	
	public boolean hasIcon(int id)
	{
		return _itemIcons.containsKey(id);
	}
	
	public String getIcon(int id)
	{
		final String icon = hasIcon(id) ? _itemIcons.get(id) : "";
		System.out.println("icon is: " + icon);
		return icon;
	}
	
	/**
	 * Gets the single instance of ActionData.
	 * 
	 * @return single instance of ActionData
	 */
	public static final ItemIconData getInstance()
	{
		return SingletonHolder._instance;
	}
	
	private static class SingletonHolder
	{
		protected static final ItemIconData _instance = new ItemIconData();
	}
}
