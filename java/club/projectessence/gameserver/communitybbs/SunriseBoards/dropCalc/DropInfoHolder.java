package club.projectessence.gameserver.communitybbs.SunriseBoards.dropCalc;

/**
 * <AUTHOR>
 */
public class DropInfoHolder
{
	public static final int	MAX_CHANCE	= 1000000;
	private int				_npcId;
	private String			_npcName;
	private int				_npcLevel;
	private long			_minDrop;
	private long			_maxDrop;
	private double			_chance;
	private boolean			_isSweep;
	private final int		_itemId;
	
	public DropInfoHolder(int npcId, String npcName, int level, long min, long max, double chance, boolean sweep, int itemId)
	{
		_npcId = npcId;
		_npcName = npcName;
		_npcLevel = level;
		_minDrop = min;
		_maxDrop = max;
		_chance = chance;
		_isSweep = sweep;
		_itemId = itemId;
	}
	
	public int getNpcId()
	{
		return _npcId;
	}
	
	public void setNpcId(int npcId)
	{
		_npcId = npcId;
	}
	
	public String getName()
	{
		return _npcName;
	}
	
	public void setName(String name)
	{
		_npcName = name;
	}
	
	public int getLevel()
	{
		return _npcLevel;
	}
	
	public void setLevel(byte level)
	{
		_npcLevel = level;
	}
	
	public boolean isSweep()
	{
		return _isSweep;
	}
	
	public void setIsSweep(boolean isSweep)
	{
		_isSweep = isSweep;
	}
	
	public int getItemId()
	{
		return _itemId;
	}
	
	/**
	 * Returns the minimum quantity of items dropped
	 * 
	 * @return int
	 */
	public long getMin()
	{
		return _minDrop;
	}
	
	/**
	 * Returns the maximum quantity of items dropped
	 * 
	 * @return int
	 */
	public long getMax()
	{
		return _maxDrop;
	}
	
	/**
	 * Returns the chance of having a drop
	 * 
	 * @return int
	 */
	public double getChance()
	{
		return _chance;
	}
	
	/**
	 * Sets the value for minimal quantity of dropped items
	 * 
	 * @param mindrop
	 *            : int designating the quantity
	 */
	public void setMinDrop(int mindrop)
	{
		_minDrop = mindrop;
	}
	
	/**
	 * Sets the value for maximal quantity of dopped items
	 * 
	 * @param maxdrop
	 *            : int designating the quantity of dropped items
	 */
	public void setMaxDrop(int maxdrop)
	{
		_maxDrop = maxdrop;
	}
	
	/**
	 * Sets the chance of having the item for a drop
	 * 
	 * @param chance
	 *            : int designating the chance
	 */
	public void setChance(double chance)
	{
		_chance = chance;
	}
}
