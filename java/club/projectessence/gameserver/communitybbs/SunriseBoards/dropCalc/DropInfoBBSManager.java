package club.projectessence.gameserver.communitybbs.SunriseBoards.dropCalc;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.StringTokenizer;
import java.util.logging.Logger;

import club.projectessence.Config;
import club.projectessence.commons.util.Rnd;
import club.projectessence.gameserver.cache.HtmCache;
import club.projectessence.gameserver.communitybbs.Manager.BaseBBSManager;
import club.projectessence.gameserver.communitybbs.SunriseBoards.dropCalc.utils.Util;
import club.projectessence.gameserver.data.ItemTable;
import club.projectessence.gameserver.data.xml.NpcData;
import club.projectessence.gameserver.data.xml.SpawnData;
import club.projectessence.gameserver.enums.ChatType;
import club.projectessence.gameserver.enums.DropType;
import club.projectessence.gameserver.handler.CommunityBoardHandler;
import club.projectessence.gameserver.model.Location;
import club.projectessence.gameserver.model.actor.Npc;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.model.actor.templates.NpcTemplate;
import club.projectessence.gameserver.model.holders.DropHolder;
import club.projectessence.gameserver.model.itemcontainer.Inventory;
import club.projectessence.gameserver.model.items.Item;
import club.projectessence.gameserver.model.spawns.NpcSpawnTemplate;
import club.projectessence.gameserver.model.zone.ZoneId;
import club.projectessence.gameserver.network.serverpackets.CreatureSay;
import club.projectessence.gameserver.network.serverpackets.RadarControl;

public class DropInfoBBSManager extends BaseBBSManager
{
	private static final Logger						LOGGER				= Logger.getLogger(DropInfoBBSManager.class.getName());
	private final Map<Integer, List<CBDropHolder>>	DROP_INDEX_CACHE	= new HashMap<>();
	private final Map<String, List<Integer>>		itemNameIndex		= new HashMap<>();
	private final Map<String, List<Integer>>		npcNameIndex		= new HashMap<>();
	// Cache với kích thước tối đa 100, xóa entry cũ nhất khi đầy
	private static final int						MAX_CACHE_SIZE		= 100;
	private final Map<String, List<Item>>			itemSearchCache		= new LinkedHashMap<String, List<Item>>(MAX_CACHE_SIZE, 0.75f, true)
																		{
																			@Override
																			protected boolean removeEldestEntry(Map.Entry<String, List<Item>> eldest)
																			{
																				return size() > MAX_CACHE_SIZE;
																			}
																		};
	private final Map<String, List<NpcTemplate>>	npcSearchCache		= new LinkedHashMap<String, List<NpcTemplate>>(MAX_CACHE_SIZE, 0.75f, true)
																		{
																			@Override
																			protected boolean removeEldestEntry(Map.Entry<String, List<NpcTemplate>> eldest)
																			{
																				return size() > MAX_CACHE_SIZE;
																			}
																		};
	// nonsupport items
	private static final Set<Integer>				BLOCK_ID			= new HashSet<>();
	static
	{
		BLOCK_ID.add(Inventory.ADENA_ID);
	}
	
	@Override
	public void parsecmd(String command, PlayerInstance player)
	{
		if (!Config.ENABLE_DROP_CALCULATOR)
		{
			player.sendMessage("Drop Calculator is disabled by admin.");
			return;
		}
		StringTokenizer st = new StringTokenizer(command, "_");
		st.nextToken();
		String html = HtmCache.getInstance().getHtm(player, "data/html/CommunityBoard/KhoaCustom/DropCalculator/bbs_dropCalcMain.htm");
		if (html == null)
		{
			html = "<html><body><br><br><center>404 :File not found: 'data/html/CommunityBoard/KhoaCustom/DropCalculator/bbs_dropCalcMain.htm'</center></body></html>";
		}
		if (command.equals("_bbssearchdropCalc"))
		{
			showMainPage(player);
			return;
		}
		else if (command.equals("_bbssearchdropItemsByName_"))
		{
			String itemName = command.substring(command.indexOf("_") + 1).trim();
			if (itemName.isEmpty())
			{
				player.sendMessage("Invalid item name.");
				showMainPage(player);
				return;
			}
			int itemsPage = st.hasMoreTokens() ? Integer.parseInt(st.nextToken()) : 1;
			int sortMethod = st.hasMoreTokens() ? Integer.parseInt(st.nextToken()) : 0;
			html = showDropItemsByNamePage(player, itemName, itemsPage, sortMethod);
		}
		else if (command.equals("_bbssearchdropMonstersByItem_"))
		{
			int itemId = Integer.parseInt(st.nextToken());
			int monstersPage = st.hasMoreTokens() ? Integer.parseInt(st.nextToken()) : 1;
			int sortMethod = st.hasMoreTokens() ? Integer.parseInt(st.nextToken()) : 0;
			html = showDropMonstersByItem(player, itemId, monstersPage, sortMethod);
		}
		else if (command.equals("_bbssearchdropMonsterDetailsByItem_"))
		{
			int monsterId = Integer.parseInt(st.nextToken());
			html = showdropMonsterDetailsByItem(player, monsterId);
			CommunityBoardHandler.separateAndSend(html, player);
			if (st.hasMoreTokens())
			{
				manageButton(player, Integer.parseInt(st.nextToken()), monsterId);
			}
			return;
		}
		else if (command.equals("_bbssearchdropMonstersByName_"))
		{
			if (!st.hasMoreTokens())
			{
				showMainPage(player);
				return;
			}
			String monsterName = st.nextToken().trim();
			int monsterPage = st.hasMoreTokens() ? Integer.parseInt(st.nextToken()) : 1;
			int sortMethod = st.hasMoreTokens() ? Integer.parseInt(st.nextToken()) : 0;
			html = showDropMonstersByName(player, monsterName, monsterPage, sortMethod);
		}
		else if (command.equals("_bbssearchdropMonsterDetailsByName_"))
		{
			int chosenMobId = Integer.parseInt(st.nextToken());
			html = showDropMonsterDetailsByName(player, chosenMobId);
			CommunityBoardHandler.separateAndSend(html, player);
			if (st.hasMoreTokens())
			{
				manageButton(player, Integer.parseInt(st.nextToken()), chosenMobId);
			}
			return;
		}
		else if (command.equals("_bbssearchNpcDropList"))
		{
			player.getVariables().set("DCDropType", command.split("_")[2]);
			DropInfoFunctions.showNpcDropList(player, command.split("_")[2], Integer.parseInt(command.split("_")[3]), Integer.parseInt(command.split("_")[4]));
			return;
		}
		else if (command.equals("_bbssearchShowSkills"))
		{
			DropInfoFunctions.showNpcSkillList(player, Integer.parseInt(command.split("_")[2]), Integer.parseInt(command.split("_")[3]));
			return;
		}
		CommunityBoardHandler.separateAndSend(html, player);
	}
	
	public void showMainPage(PlayerInstance player)
	{
		player.resetLoadedImages();
		String html = HtmCache.getInstance().getHtm(player, "data/html/CommunityBoard/KhoaCustom/DropCalculator/bbs_dropCalcMain.htm");
		CommunityBoardHandler.separateAndSend(html, player);
	}
	
	public String showDropMonstersByName(PlayerInstance player, String monsterName, int page, int sort)
	{
		if (DROP_INDEX_CACHE.isEmpty())
		{
			buildDropIndex();
		}
		player.getVariables().set("DCMonsterSort", sort);
		player.getVariables().set("DCMonsterName", monsterName);
		player.getVariables().set("DCMonstersPage", page);
		String html = HtmCache.getInstance().getHtm(player, "data/html/CommunityBoard/KhoaCustom/DropCalculator/bbs_dropMonstersByName.htm");
		return replaceMonstersByName(html, monsterName, page, sort);
	}
	
	private String replaceMonstersByName(String html, String monsterName, int page, int sort)
	{
		String newHtml = html;
		String cacheKey = monsterName.toLowerCase() + "_" + sort;
		List<NpcTemplate> npcTemplates = npcSearchCache.get(cacheKey);
		if (npcTemplates == null)
		{
			Set<Integer> uniqueNpcIds = new HashSet<>();
			npcTemplates = new ArrayList<>();
			long startTime = System.nanoTime();
			String searchLower = monsterName.toLowerCase();
			for (Map.Entry<String, List<Integer>> entry : npcNameIndex.entrySet())
			{
				String keyword = entry.getKey();
				if (keyword.contains(searchLower) || searchLower.contains(keyword))
				{
					for (int npcId : entry.getValue())
					{
						NpcTemplate npc = NpcData.getInstance().getTemplate(npcId);
						if (npc != null && npc.getName().toLowerCase().contains(searchLower))
						{
							if (uniqueNpcIds.add(npcId))
							{
								npcTemplates.add(npc);
							}
						}
					}
				}
			}
			npcTemplates.removeIf(npc -> !DropInfoHandler.getInstance().hasDropOrSpoil(npc.getId()));
			npcTemplates = DropInfoFunctions.sortMonsters(npcTemplates, sort);
			npcSearchCache.put(cacheKey, npcTemplates);
			long endTime = System.nanoTime();
			// LOGGER.info("Monster search for '" + monsterName + "' took: " + (endTime - startTime) / 1_000_000.0 + " ms, found: " + npcTemplates.size() + " monsters");
		}
		if (npcTemplates.isEmpty())
		{
			newHtml = newHtml.replace("%name_0%", "No Match");
			for (int i = 1; i < 8; i++)
			{
				newHtml = newHtml.replace("%name_" + i + "%", "...");
				newHtml = newHtml.replace("%drop_" + i + "%", "...");
				newHtml = newHtml.replace("%spoil_" + i + "%", "...");
				newHtml = newHtml.replace("%bp_" + i + "%", "...");
			}
		}
		else
		{
			int displayedIndex = 0;
			int totalNpcs = npcTemplates.size();
			for (int i = 0; i < 8; i++)
			{
				int npcIndex = i + ((page - 1) * 8);
				NpcTemplate npc = totalNpcs > npcIndex ? npcTemplates.get(npcIndex) : null;
				if ((npc != null) && ((DropInfoFunctions.getDropsCount(npc, false) > 0) || (DropInfoFunctions.getDropsCount(npc, true) > 0)))
				{
					newHtml = newHtml.replace("%name_" + displayedIndex + "%", npc.getName());
					newHtml = newHtml.replace("%drop_" + displayedIndex + "%", String.valueOf(DropInfoFunctions.getDropsCount(npc, false)));
					newHtml = newHtml.replace("%spoil_" + displayedIndex + "%", String.valueOf(DropInfoFunctions.getDropsCount(npc, true)));
					newHtml = newHtml.replace("%bp_" + displayedIndex + "%", "<button value=\"show\" action=\"bypass _bbssearchdropMonsterDetailsByName_" + npc.getId() + "\" width=40 height=12 back=\"L2UI_CT1.ListCTRL_DF_Title_Down\" fore=\"L2UI_CT1.ListCTRL_DF_Title\">");
					displayedIndex++;
				}
			}
			for (int i = displayedIndex; i < 8; i++)
			{
				newHtml = newHtml.replace("%name_" + i + "%", "...");
				newHtml = newHtml.replace("%drop_" + i + "%", "...");
				newHtml = newHtml.replace("%spoil_" + i + "%", "...");
				newHtml = newHtml.replace("%bp_" + i + "%", "...");
			}
		}
		newHtml = newHtml.replace("%previous%", page > 1 ? "<button value=\" \" action=\"bypass _bbssearchdropMonstersByName_" + monsterName + "_" + (page - 1) + "_" + sort + "\" width=16 height=16 back=\"L2UI_CH3.shortcut_prev_down\" fore=\"L2UI_CH3.shortcut_prev\">" : "<br>");
		newHtml = newHtml.replace("%next%", npcTemplates.size() > (page * 8) ? "<button value=\" \" action=\"bypass _bbssearchdropMonstersByName_" + monsterName + "_" + (page + 1) + "_" + sort + "\" width=16 height=16 back=\"L2UI_CH3.shortcut_next_down\" fore=\"L2UI_CH3.shortcut_next\">" : "<br>");
		newHtml = newHtml.replace("%search%", monsterName);
		newHtml = newHtml.replace("%size%", Util.formatAdena(npcTemplates.size()));
		newHtml = newHtml.replace("%page%", String.valueOf(page));
		newHtml = newHtml.replace("%monsterName%", sort == 0 ? "<font color=\"bbbbbb\">Monster Name</font>" : "<a action=\"bypass _bbssearchdropMonstersByName_" + monsterName + "_" + page + "_" + 0 + "\"><font color=\"bbbbbb\">Monster Name</font></a>");
		newHtml = newHtml.replace("%droppingItems%", sort == 1 ? "<font color=\"bbbbbb\">Dropping Items</font>" : "<a action=\"bypass _bbssearchdropMonstersByName_" + monsterName + "_" + page + "_" + 1 + "\"><font color=\"bbbbbb\">Dropping Items</font></a>");
		newHtml = newHtml.replace("%spoilingItems%", sort == 2 ? "<font color=\"bbbbbb\">Spoiling Items</font>" : "<a action=\"bypass _bbssearchdropMonstersByName_" + monsterName + "_" + page + "_" + 2 + "\"><font color=\"bbbbbb\">Spoiling Items</font></a>");
		return newHtml;
	}
	
	public String showDropItemsByNamePage(PlayerInstance player, String itemName, int page, int sort)
	{
		if (DROP_INDEX_CACHE.isEmpty())
		{
			buildDropIndex();
		}
		player.getVariables().set("DCItemSort", sort);
		player.getVariables().set("DCItemName", itemName);
		player.getVariables().set("DCItemsPage", page);
		String html = HtmCache.getInstance().getHtm(player, "data/html/CommunityBoard/KhoaCustom/DropCalculator/bbs_dropItemsByName.htm");
		return replaceItemsByNamePage(html, itemName, page, sort);
	}
	
	private String replaceItemsByNamePage(String html, String itemName, int page, int sort)
	{
		String newHtml = html;
		// Sử dụng Set để loại bỏ các item trùng lặp theo tên
		Set<String> uniqueItemNames = new HashSet<>();
		List<Item> uniqueItemsByName = new ArrayList<>();
		// Duyệt qua DROP_INDEX_CACHE để tìm các item chứa tên tương ứng
		for (Map.Entry<Integer, List<CBDropHolder>> entry : DROP_INDEX_CACHE.entrySet())
		{
			int itemId = entry.getKey();
			Item item = ItemTable.getInstance().getTemplate(itemId);
			if (item != null && item.getName().toLowerCase().contains(itemName.toLowerCase()) && (DropInfoFunctions.getDroplistsCountByItemId(item.getId(), false) > 0 || DropInfoFunctions.getDroplistsCountByItemId(item.getId(), true) > 0))
			{
				if (uniqueItemNames.add(item.getName()))
				{
					uniqueItemsByName.add(item);
				}
			}
		}
		// Nếu không tìm thấy item nào
		if (uniqueItemsByName.isEmpty())
		{
			newHtml = newHtml.replace("%name_0%", "No Match");
			for (int i = 1; i < 8; i++)
			{
				newHtml = newHtml.replace("%name_" + i + "%", "...");
				newHtml = newHtml.replace("%drop_" + i + "%", "...");
				newHtml = newHtml.replace("%spoil_" + i + "%", "...");
				newHtml = newHtml.replace("%bp_" + i + "%", "...");
			}
		}
		// Sắp xếp danh sách item theo tham số sort
		uniqueItemsByName = DropInfoFunctions.sortItemTemplates(uniqueItemsByName, sort);
		// Phân trang và hiển thị item
		int itemIndex = 0;
		for (int i = 0; i < 8; i++)
		{
			itemIndex = i + ((page - 1) * 8);
			Item item = uniqueItemsByName.size() > itemIndex ? uniqueItemsByName.get(itemIndex) : null;
			newHtml = newHtml.replace("%name_" + i + "%", item != null ? item.getName() : "...");
			newHtml = newHtml.replace("%drop_" + i + "%", item != null ? String.valueOf(DropInfoFunctions.getDroplistsCountByItemId(item.getId(), false)) : "...");
			newHtml = newHtml.replace("%spoil_" + i + "%", item != null ? String.valueOf(DropInfoFunctions.getDroplistsCountByItemId(item.getId(), true)) : "...");
			newHtml = newHtml.replace("%bp_" + i + "%", item != null ? "<button value=\"show\" action=\"bypass _bbssearchdropMonstersByItem_" + item.getId() + "_1\" width=40 height=12 back=\"L2UI_CT1.ListCTRL_DF_Title_Down\" fore=\"L2UI_CT1.ListCTRL_DF_Title\">" : "...");
		}
		// Nút Previous và Next
		newHtml = newHtml.replace("%previous%", page > 1 ? "<button value=\" \" action=\"bypass _bbssearchdropItemsByName_" + itemName + "_" + (page - 1) + "_" + sort + "\" width=16 height=16 back=\"L2UI_CH3.shortcut_prev_down\" fore=\"L2UI_CH3.shortcut_prev\">" : "<br>");
		newHtml = newHtml.replace("%next%", uniqueItemsByName.size() > (itemIndex + 1) ? "<button value=\" \"  action=\"bypass _bbssearchdropItemsByName_" + itemName + "_" + (page + 1) + "_" + sort + "\" width=16 height=16 back=\"L2UI_CH3.shortcut_next_down\" fore=\"L2UI_CH3.shortcut_next\">" : "<br>");
		// Thay thế các trường khác
		newHtml = newHtml.replace("%search%", itemName);
		newHtml = newHtml.replace("%size%", Util.formatAdena(uniqueItemsByName.size()));
		newHtml = newHtml.replace("%page%", String.valueOf(page));
		newHtml = newHtml.replace("%itemName%", sort == 0 ? "<font color=\"bbbbbb\">Name</font>" : "<a action=\"bypass _bbssearchdropItemsByName_" + itemName + "_" + page + "_" + 0 + "\"><font color=\"bbbbbb\">Name</font></a>");
		newHtml = newHtml.replace("%dropLists%", sort == 1 ? "<font color=\"bbbbbb\">Number of Drop Lists</font>" : "<a action=\"bypass _bbssearchdropItemsByName_" + itemName + "_" + page + "_" + 1 + "\"><font color=\"bbbbbb\">Number of Drop Lists</font></a>");
		newHtml = newHtml.replace("%spoilLists%", sort == 2 ? "<font color=\"bbbbbb\">Number of Spoil Lists</font>" : "<a action=\"bypass _bbssearchdropItemsByName_" + itemName + "_" + page + "_" + 2 + "\"><font color=\"bbbbbb\">Number of Spoil Lists</font></a>");
		return newHtml;
	}
	
	public String showDropMonstersByItem(PlayerInstance player, int itemId, int page, int sort)
	{
		player.getVariables().set("DCMonster2Sort", sort);
		player.getVariables().set("DCItemId", itemId);
		player.getVariables().set("DCMonstersPage", page);
		String html = HtmCache.getInstance().getHtm(player, "data/html/CommunityBoard/KhoaCustom/DropCalculator/bbs_dropMonstersByItem.htm");
		return replaceMonstersByItemPage(player, html, itemId, page, sort);
	}
	
	private String replaceMonstersByItemPage(PlayerInstance player, String html, int itemId, int page, int sort)
	{
		String newHtml = html;
		List<DropInfoHolder> templates = DropInfoHandler.getInstance().getDrop(itemId);
		// Kiểm tra nếu templates là null và khởi tạo danh sách rỗng nếu cần
		if (templates == null)
		{
			templates = new ArrayList<>(); // Gán danh sách trống để tránh lỗi NullPointerException
		}
		// Sắp xếp danh sách templates
		templates = DropInfoFunctions.sortMonsters2(templates, sort);
		// Thiết lập các biến cần thiết cho phân trang
		final int itemsPerPage = 8; // Số lượng mục trên mỗi trang
		final int totalItems = templates.size(); // Tổng số mục
		final int totalPages = (int) Math.ceil((double) totalItems / itemsPerPage); // Tổng số trang
		// Kiểm tra và đảm bảo trang hiện tại nằm trong phạm vi hợp lệ
		page = Math.max(1, Math.min(page, totalPages)); // Đảm bảo page >= 1 và <= totalPages
		int startIndex = (page - 1) * itemsPerPage; // Vị trí bắt đầu
		int endIndex = Math.min(startIndex + itemsPerPage, totalItems); // Vị trí kết thúc
		int npcIndex = 0;
		for (int i = 0; i < itemsPerPage; i++)
		{
			npcIndex = startIndex + i;
			DropInfoHolder drops = templates.size() > npcIndex ? templates.get(npcIndex) : null;
			NpcTemplate npc = templates.size() > npcIndex ? NpcData.getInstance().getTemplate(templates.get(npcIndex).getNpcId()) : null;
			newHtml = newHtml.replace("%name_" + i + "%", npc != null ? npc.getName() : "...");
			newHtml = newHtml.replace("%level_" + i + "%", npc != null ? String.valueOf(npc.getLevel()) : "...");
			newHtml = newHtml.replace("%type_" + i + "%", (npc != null) && (drops != null) ? drops.isSweep() ? "Spoil" : "Drop" : "...");
			newHtml = newHtml.replace("%count_" + i + "%", (npc != null) && (drops != null) ? DropInfoFunctions.getMinMaxDropCounts(npc, itemId, drops.isSweep()) : "...");
			newHtml = newHtml.replace("%chance_" + i + "%", (npc != null) && (drops != null) ? DropInfoFunctions.getDropChance(npc, itemId, drops.isSweep(), player) : "...");
			newHtml = newHtml.replace("%bp_" + i + "%", npc != null ? "<button value=\"show\" action=\"bypass _bbssearchdropMonsterDetailsByItem_" + npc.getId() + "\" width=40 height=12 back=\"L2UI_CT1.ListCTRL_DF_Title_Down\" fore=\"L2UI_CT1.ListCTRL_DF_Title\">" : "...");
		}
		// Xóa các mục còn thừa khi số phần tử trên trang ít hơn itemsPerPage
		for (int i = endIndex - startIndex; i < itemsPerPage; i++)
		{
			newHtml = newHtml.replace("%name_" + i + "%", "...");
			newHtml = newHtml.replace("%level_" + i + "%", "...");
			newHtml = newHtml.replace("%type_" + i + "%", "...");
			newHtml = newHtml.replace("%count_" + i + "%", "...");
			newHtml = newHtml.replace("%chance_" + i + "%", "...");
			newHtml = newHtml.replace("%bp_" + i + "%", "...");
		}
		// Nút phân trang 'Trước' và 'Tiếp theo'
		newHtml = newHtml.replace("%previous%", page > 1 ? "<button value=\" \" action=\"bypass _bbssearchdropMonstersByItem_" + itemId + "_" + (page - 1) + "_" + sort + "\" width=16 height=16 back=\"L2UI_CH3.shortcut_prev_down\" fore=\"L2UI_CH3.shortcut_prev\">" : "<br>");
		newHtml = newHtml.replace("%next%", page < totalPages ? "<button value=\" \" action=\"bypass _bbssearchdropMonstersByItem_" + itemId + "_" + (page + 1) + "_" + sort + "\" width=16 height=16 back=\"L2UI_CH3.shortcut_next_down\" fore=\"L2UI_CH3.shortcut_next\">" : "<br>");
		// Thay thế các biến khác trong HTML
		newHtml = newHtml.replace("%search%", player.getVariables().getString("DCItemName", ItemTable.getInstance().getTemplate(itemId).getName()));
		newHtml = newHtml.replace("%item%", ItemTable.getInstance().getTemplate(itemId).getName());
		newHtml = newHtml.replace("%size%", Util.formatAdena(totalItems));
		newHtml = newHtml.replace("%back%", String.valueOf(player.getVariables().getInt("DCItemsPage", 1)));
		newHtml = newHtml.replace("%page%", String.valueOf(page));
		newHtml = newHtml.replace("%monsterName%", sort == 0 ? "<font color=\"bbbbbb\">Name</font>" : "<a action=\"bypass _bbssearchdropMonstersByItem_" + itemId + "_" + page + "_" + 0 + "\"><font color=\"bbbbbb\">Name</font></a>");
		newHtml = newHtml.replace("%level%", sort == 1 ? "<font color=\"bbbbbb\">Level</font>" : "<a action=\"bypass _bbssearchdropMonstersByItem_" + itemId + "_" + page + "_" + 1 + "\"><font color=\"bbbbbb\">Level</font></a>");
		newHtml = newHtml.replace("%chance%", sort == 2 ? "<font color=\"bbbbbb\">Chance</font>" : "<a action=\"bypass _bbssearchdropMonstersByItem_" + itemId + "_" + page + "_" + 2 + "\"><font color=\"bbbbbb\">Chance</font></a>");
		newHtml = newHtml.replace("%type%", sort == 3 ? "<font color=\"bbbbbbz\">Type</font>" : "<a action=\"bypass _bbssearchdropMonstersByItem_" + itemId + "_" + page + "_" + 3 + "\"><font color=\"bbbbbb\">Type</font></a>");
		newHtml = newHtml.replace("%count%", sort == 4 ? "<font color=\"bbbbbb\">Count [Min...Max]</font>" : "<a action=\"bypass _bbssearchdropMonstersByItem_" + itemId + "_" + page + "_" + 4 + "\"><font color=\"bbbbbb\">Count [Min...Max]</font></a>");
		newHtml = newHtml.replace("%sort%", String.valueOf(player.getVariables().getInt("DCItemSort", 0)));
		return newHtml;
	}
	
	public String showdropMonsterDetailsByItem(PlayerInstance player, int monsterId)
	{
		String html = HtmCache.getInstance().getHtm(player, "data/html/CommunityBoard/KhoaCustom/DropCalculator/bbs_dropMonsterDetailsByItem.htm");
		return replaceMonsterDetails(player, html, monsterId);
	}
	
	public String showDropMonsterDetailsByName(PlayerInstance player, int monsterId)
	{
		String html = HtmCache.getInstance().getHtm(player, "data/html/CommunityBoard/KhoaCustom/DropCalculator/bbs_dropMonsterDetailsByName.htm");
		return replaceMonsterDetails(player, html, monsterId);
	}
	
	private String replaceMonsterDetails(PlayerInstance player, String html, int monsterId)
	{
		String newHtml = html;
		int itemId = player.getVariables().getInt("DCItemId", -1);
		NpcTemplate template = NpcData.getInstance().getTemplate(monsterId);
		Item item = itemId > -1 ? ItemTable.getInstance().getTemplate(itemId) : null;
		if (template == null)
		{
			return newHtml.replace("%drop%", "...").replace("%spoil%", "...");
		}
		String monsterName = player.getVariables().getString("DCMonsterName", "");
		if (monsterName.isEmpty())
		{
			monsterName = "Unknown";
		}
		newHtml = newHtml.replace("%name%", monsterName);
		newHtml = newHtml.replace("%monster_name%", template.getName());
		newHtml = newHtml.replace("%item%", item != null ? item.getName() : "...");
		newHtml = newHtml.replace("%item_id%", item != null ? String.valueOf(item.getId()) : "...");
		newHtml = newHtml.replace("%back%", String.valueOf(player.getVariables().getString("DCMonstersPage")));
		newHtml = newHtml.replace("%monster%", String.valueOf(monsterId));
		newHtml = newHtml.replace("%level%", String.valueOf(template.getLevel()));
		newHtml = newHtml.replace("%aggro%", template.isAggressive() ? "TRUE" : "FALSE");
		newHtml = newHtml.replace("%hp%", Util.formatAdena((int) template.getBaseHpMax()));
		newHtml = newHtml.replace("%mp%", Util.formatAdena((int) template.getBaseMpMax()));
		newHtml = newHtml.replace("%drop%", item != null ? DropInfoFunctions.getDropChance(template, item.getId(), false, player) : "...");
		newHtml = newHtml.replace("%spoil%", item != null ? DropInfoFunctions.getDropChance(template, item.getId(), true, player) : "...");
		newHtml = newHtml.replace("%droping%", String.valueOf(DropInfoFunctions.getDropsCount(template, false)));
		newHtml = newHtml.replace("%spoiling%", String.valueOf(DropInfoFunctions.getDropsCount(template, true)));
		newHtml = newHtml.replace("%sort%", String.valueOf(player.getVariables().getString("DCMonsterSort", "Unknow")));
		newHtml = newHtml.replace("%sort2%", String.valueOf(player.getVariables().getString("DCMonster2Sort", "Unknow")));
		newHtml = newHtml.replace("%image%", "Crest.pledge_crest_" + String.valueOf(Config.SERVER_ID) + "_" + String.valueOf(monsterId));
		ImagesCache.getInstance().sendImageToPlayer(player, monsterId);
		return newHtml;
	}
	
	public void manageButton(PlayerInstance player, int buttonId, int monsterId)
	{
		switch (buttonId)
		{
			case 1:
				player.sendPacket(new RadarControl(2, 2, 0, 0, 0));
				break;
			case 2:
				DropInfoFunctions.showNpcDropList(player, "DEATH", monsterId, 1);
				break;
			case 3:
				if (Config.ENABLE_TELEPORT_FUNCTION)
				{
					if (Config.ALLOW_TELEPORT_FROM_PEACE_ZONE_ONLY && !player.isInsideZone(ZoneId.PEACE))
					{
						player.sendMessage("Teleport is only allowed from peace zones only.");
						return;
					}
					Npc aliveInstance = DropInfoFunctions.getAliveNpc(monsterId);
					if ((aliveInstance != null) && !Config.RESTRICTED_TELEPORT_IDS.contains(aliveInstance.getId()))
					{
						if (!Config.ALLOW_FREE_TELEPORT && !player.destroyItemByItemId("DropCalc", Config.TELEPORT_PRICE[0], Config.TELEPORT_PRICE[1], player, true))
						{
							player.sendMessage("Incorrect item count.");
							return;
						}
						player.teleToLocation(aliveInstance.getLocation());
					}
					else
					{
						player.sendMessage("Monster isn't alive or teleport is not allowed.");
					}
				}
				else
				{
					player.sendMessage("Teleport function is disabled.");
				}
				break;
			case 4:
				final List<NpcSpawnTemplate> spawnList = SpawnData.getInstance().getNpcSpawns(npc -> npc.getId() == monsterId);
				if (spawnList.isEmpty())
				{
					player.sendMessage("Cannot find any spawn. Maybe dropped by a boss or instance monster.");
				}
				else
				{
					final NpcSpawnTemplate spawn = spawnList.get(Rnd.get(spawnList.size()));
					int x = spawn.getSpawnLocation().getX();
					int y = spawn.getSpawnLocation().getY();
					int z = spawn.getSpawnLocation().getZ();
					if (player.inObserverMode())
					{
						player.leaveObserverMode();
					}
					final Location npcLocation = new Location(x, y, z);
					player.enterObserverMode(npcLocation);
					player.sendMessage("The camera has been moved to the NPC's location. You can view to locate the NPC. Press Return if you want");
					player.sendPacket(new CreatureSay(player, ChatType.WHISPER, "DropCalc", "The camera has been moved to the NPC's location. You can view to locate the NPC. Press Return if you want", 0));
				}
				break;
			case 5:
				DropInfoFunctions.showStats(player, monsterId);
				break;
		}
	}
	
	private void buildDropIndex()
	{
		// LOGGER.info("Building DROP_INDEX_CACHE, DropInfoHandler, itemNameIndex, and npcNameIndex...");
		int[] npcCount =
		{
			0
		};
		int[] dropCount =
		{
			0
		};
		DROP_INDEX_CACHE.clear();
		DropInfoHandler.getInstance().getInfo().clear();
		itemNameIndex.clear();
		npcNameIndex.clear();
		itemSearchCache.clear();
		npcSearchCache.clear();
		NpcData.getInstance().getTemplates(npc -> npc.getDropList(DropType.DROP) != null || npc.getDropList(DropType.SPOIL) != null).forEach(npcTemplate ->
		{
			npcCount[0]++;
			String npcName = npcTemplate.getName().toLowerCase();
			String[] npcKeywords = npcName.split("[\\s\\-_]+");
			for (String keyword : npcKeywords)
			{
				if (keyword.length() >= 3)
				{
					npcNameIndex.computeIfAbsent(keyword, k -> new ArrayList<>()).add(npcTemplate.getId());
				}
			}
			List<DropHolder> dropList = npcTemplate.getDropList(DropType.DROP);
			if (dropList != null)
			{
				for (DropHolder dropHolder : dropList)
				{
					if (!DropInfoHandler.HERBS.contains(dropHolder.getItemId()))
					{
						addToDropList(npcTemplate, dropHolder);
						DropInfoHandler.getInstance().addDropInfo(npcTemplate.getId(), new DropInfoHolder(npcTemplate.getId(), npcTemplate.getName(), npcTemplate.getLevel(), dropHolder.getMin(), dropHolder.getMax(), dropHolder.getChance(), false, dropHolder.getItemId()));
						Item item = ItemTable.getInstance().getTemplate(dropHolder.getItemId());
						if (item != null)
						{
							String name = item.getName().toLowerCase();
							String[] keywords = name.split("[\\s\\-_]+");
							for (String keyword : keywords)
							{
								if (keyword.length() >= 3)
								{
									itemNameIndex.computeIfAbsent(keyword, k -> new ArrayList<>()).add(dropHolder.getItemId());
								}
							}
						}
						dropCount[0]++;
					}
				}
			}
			List<DropHolder> spoilList = npcTemplate.getDropList(DropType.SPOIL);
			if (spoilList != null)
			{
				for (DropHolder dropHolder : spoilList)
				{
					if (!DropInfoHandler.HERBS.contains(dropHolder.getItemId()))
					{
						addToDropList(npcTemplate, dropHolder);
						DropInfoHandler.getInstance().addDropInfo(npcTemplate.getId(), new DropInfoHolder(npcTemplate.getId(), npcTemplate.getName(), npcTemplate.getLevel(), dropHolder.getMin(), dropHolder.getMax(), dropHolder.getChance(), true, dropHolder.getItemId()));
						Item item = ItemTable.getInstance().getTemplate(dropHolder.getItemId());
						if (item != null)
						{
							String name = item.getName().toLowerCase();
							String[] keywords = name.split("[\\s\\-_]+");
							for (String keyword : keywords)
							{
								if (keyword.length() >= 3)
								{
									itemNameIndex.computeIfAbsent(keyword, k -> new ArrayList<>()).add(dropHolder.getItemId());
								}
							}
						}
						dropCount[0]++;
					}
				}
			}
		});
		// LOGGER.info("DROP_INDEX_CACHE built: " + npcCount[0] + " NPCs, " + dropCount[0] + " drops, " + DROP_INDEX_CACHE.size() + " unique items");
		// LOGGER.info("DropInfoHandler updated: " + DropInfoHandler.getInstance().getInfo().size() + " items");
		// LOGGER.info("itemNameIndex built: " + itemNameIndex.size() + " unique item keywords");
		// LOGGER.info("npcNameIndex built: " + npcNameIndex.size() + " unique NPC keywords");
	}
	
	private void addToDropList(NpcTemplate npcTemplate, DropHolder dropHolder)
	{
		if (BLOCK_ID.contains(dropHolder.getItemId()))
		{
			return;
		}
		List<CBDropHolder> dropList = DROP_INDEX_CACHE.get(dropHolder.getItemId());
		if (dropList == null)
		{
			dropList = new ArrayList<>();
			DROP_INDEX_CACHE.put(dropHolder.getItemId(), dropList);
		}
		dropList.add(new CBDropHolder(npcTemplate, dropHolder));
	}
	
	public static DropInfoBBSManager getInstance()
	{
		return SingletonHolder.INSTANCE;
	}
	
	private static class SingletonHolder
	{
		protected static final DropInfoBBSManager INSTANCE = new DropInfoBBSManager();
	}
	
	private class CBDropHolder implements Serializable
	{
		private static final long	serialVersionUID	= 1L;
		final int					itemId;
		final int					npcId;
		final short					min;						// Dùng short thay long
		final short					max;
		final float					chance;						// Dùng float thay double
		final boolean				isSpoil;
		
		public CBDropHolder(NpcTemplate npcTemplate, DropHolder dropHolder)
		{
			itemId = dropHolder.getItemId();
			npcId = npcTemplate.getId();
			min = (short) Math.min(dropHolder.getMin(), Short.MAX_VALUE);
			max = (short) Math.min(dropHolder.getMax(), Short.MAX_VALUE);
			chance = (float) dropHolder.getChance();
			isSpoil = dropHolder.getDropType() == DropType.SPOIL;
		}
		
		@Override
		public String toString()
		{
			return "CBDropHolder [itemId=" + itemId + ", npcId=" + npcId + ", min=" + min + ", max=" + max + ", chance=" + chance + ", isSpoil=" + isSpoil + "]";
		}
	}
	
	@Override
	public void parsewrite(String ar1, String ar2, String ar3, String ar4, String ar5, PlayerInstance player)
	{}
}