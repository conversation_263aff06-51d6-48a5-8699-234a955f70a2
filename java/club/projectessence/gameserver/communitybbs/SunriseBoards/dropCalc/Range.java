package club.projectessence.gameserver.communitybbs.SunriseBoards.dropCalc;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class Range
{
	private int[] nums;
	
	/**
	 * num between start to end <font color=FF0000>dont contains end</font>
	 * 
	 * @param start
	 * @param end
	 **/
	public Range(int start, int end)
	{
		if (start >= end)
		{
			nums = new int[]
			{
				0
			};
			throw new RuntimeException("Wrong Range! start must less than end");
		}
		nums = new int[end - start];
		for (int i = 0; i < nums.length; i++)
		{
			nums[i] = start++;
		}
	}
	
	public List<Integer> values()
	{
		List<Integer> list = new ArrayList<>();
		for (int i : getNums())
		{
			list.add(i);
		}
		return list;
	}
	
	public int[] getNums()
	{
		return nums;
	}
	
	public int[] add(int... values)
	{
		int oldLength = nums.length;
		nums = Arrays.copyOf(nums, nums.length + values.length);
		for (int i = 0; i < values.length; i++)
		{
			nums[oldLength + i] = values[i];
		}
		return nums;
	}
	
	@Override
	public String toString()
	{
		return Arrays.toString(nums);
	}
}
