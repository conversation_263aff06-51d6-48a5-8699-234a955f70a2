/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.scripting;

import club.projectessence.Config;
import club.projectessence.commons.util.IXmlReader;
import club.projectessence.gameserver.scripting.java.JavaExecutionContext;
import club.projectessence.gameserver.scripting.java.JavaScriptingEngine;
import org.w3c.dom.Document;

import java.io.File;
import java.io.IOException;
import java.nio.file.*;
import java.nio.file.attribute.BasicFileAttributes;
import java.util.*;
import java.util.Map.Entry;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * <AUTHOR>
 */
public class ScriptEngineManager implements IXmlReader {
	public static final Path SCRIPT_FOLDER = Config.SCRIPT_ROOT.toPath();
	public static final Path MASTER_HANDLER_FILE = Paths.get(SCRIPT_FOLDER.toString(), "handlers", "MasterHandler.java");
	public static final Path EFFECT_MASTER_HANDLER_FILE = Paths.get(SCRIPT_FOLDER.toString(), "handlers", "EffectMasterHandler.java");
	public static final Path SKILL_CONDITION_HANDLER_FILE = Paths.get(SCRIPT_FOLDER.toString(), "handlers", "SkillConditionMasterHandler.java");
	public static final Path CONDITION_HANDLER_FILE = Paths.get(SCRIPT_FOLDER.toString(), "handlers", "ConditionMasterHandler.java");
	public static final Path ONE_DAY_REWARD_MASTER_HANDLER = Paths.get(SCRIPT_FOLDER.toString(), "handlers", "DailyMissionMasterHandler.java");
	protected static final List<String> _exclusions = new ArrayList<>();
	private static final Logger LOGGER = Logger.getLogger(ScriptEngineManager.class.getName());
	private static final JavaExecutionContext _javaExecutionContext = new JavaScriptingEngine().createExecutionContext();

	protected ScriptEngineManager() {
		// Load Scripts.xml
		load();
	}

	public static ScriptEngineManager getInstance() {
		return SingletonHolder.INSTANCE;
	}

	@Override
	public void load() {
		_exclusions.clear();
		parseDatapackFile("config/Scripts.xml");
		LOGGER.info("Loaded " + _exclusions.size() + " files to exclude.");
	}

	@Override
	public void parseDocument(Document doc, File f) {
		try {
			final Map<String, List<String>> excludePaths = new HashMap<>();
			forEach(doc, "list", listNode -> forEach(listNode, "exclude", excludeNode ->
			{
				final String excludeFile = parseString(excludeNode.getAttributes(), "file");
				excludePaths.putIfAbsent(excludeFile, new ArrayList<>());

				forEach(excludeNode, "include", includeNode -> excludePaths.get(excludeFile).add(parseString(includeNode.getAttributes(), "file")));
			}));

			final int nameCount = SCRIPT_FOLDER.getNameCount();
			Files.walkFileTree(SCRIPT_FOLDER, new SimpleFileVisitor<Path>() {
				@Override
				public FileVisitResult visitFile(Path file, BasicFileAttributes attrs) throws IOException {
					final String fileName = file.getFileName().toString();
					if (fileName.endsWith(".java")) {
						final Iterator<Path> relativePath = file.subpath(nameCount, file.getNameCount()).iterator();
						while (relativePath.hasNext()) {
							final String nextPart = relativePath.next().toString();
							if (excludePaths.containsKey(nextPart)) {
								boolean excludeScript = true;

								final List<String> includePath = excludePaths.get(nextPart);
								if (includePath != null) {
									while (relativePath.hasNext()) {
										if (includePath.contains(relativePath.next().toString())) {
											excludeScript = false;
											break;
										}
									}
								}
								if (excludeScript) {
									_exclusions.add(file.toUri().getPath());
									break;
								}
							}
						}
					}
					return super.visitFile(file, attrs);
				}
			});
		} catch (IOException e) {
			LOGGER.log(Level.WARNING, "Couldn't load script exclusions.", e);
		}
	}

	private void processDirectory(File dir, List<Path> files) {
		for (File file : dir.listFiles()) {
			if (file.isFile()) {
				final String filePath = file.toURI().getPath();
				if (filePath.endsWith(".java") && !_exclusions.contains(filePath)) {
					files.add(file.toPath().toAbsolutePath());
				}
			} else if (file.isDirectory()) {
				processDirectory(file, files);
			}
		}
	}

	public void executeScript(Path sourceFiles) throws Exception {
		Path path = sourceFiles;
		if (!path.isAbsolute()) {
			path = SCRIPT_FOLDER.resolve(path);
		}

		path = path.toAbsolutePath();

		final Entry<Path, Throwable> error = _javaExecutionContext.executeScript(path);
		if (error != null) {
			throw new Exception("ScriptEngine: " + error.getKey() + " failed execution!", error.getValue());
		}
	}

	public void executeScriptList() throws Exception {
		if (Config.ALT_DEV_NO_QUESTS) {
			return;
		}

		// Check if script folder exists
		if (!SCRIPT_FOLDER.toFile().exists()) {
			LOGGER.severe("Script folder does not exist: " + SCRIPT_FOLDER.toAbsolutePath());
			throw new Exception("Script folder not found: " + SCRIPT_FOLDER.toAbsolutePath());
		}

		final List<Path> files = new ArrayList<>();
		processDirectory(SCRIPT_FOLDER.toFile(), files);

		if (files.isEmpty()) {
			LOGGER.warning("No script files found in: " + SCRIPT_FOLDER.toAbsolutePath());
			return;
		}

		LOGGER.info("Found " + files.size() + " script files to compile and execute.");

		final Map<Path, Throwable> invokationErrors = _javaExecutionContext.executeScripts(files);
		for (Entry<Path, Throwable> entry : invokationErrors.entrySet()) {
			LOGGER.log(Level.WARNING, "ScriptEngine: " + entry.getKey() + " failed execution!", entry.getValue());
		}
	}

	public Path getCurrentLoadingScript() {
		return _javaExecutionContext.getCurrentExecutingScript();
	}

	private static class SingletonHolder {
		protected static final ScriptEngineManager INSTANCE = new ScriptEngineManager();
	}
}