/*
 * This file is part of the L2J Mobius project.
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.model.holders;

import java.util.List;

import club.projectessence.Config;
import club.projectessence.commons.util.Rnd;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.model.variables.PlayerVariables;

/**
 * A DTO for items; contains item ID, count and chance.<br>
 * Complemented by {@link QuestItemHolder}.
 *
 * <AUTHOR>
 */
public class ItemChanceHolder extends ItemHolder
{
	private final double	_chance;
	private final byte		_enchantmentLevel;
	private final boolean	_maintainIngredient;
	private boolean			_isAnnounced	= false;
	
	public ItemChanceHolder(ItemChanceHolder ich)
	{
		super(ich.getId(), ich.getCount());
		_chance = getChance();
		_enchantmentLevel = getEnchantmentLevel();
		_isAnnounced = isAnnounced();
		_maintainIngredient = isMaintainIngredient();
	}
	
	public ItemChanceHolder(int id, double chance)
	{
		this(id, chance, 1);
	}
	
	public ItemChanceHolder(int id, double chance, long count)
	{
		super(id, count);
		_chance = chance;
		_enchantmentLevel = 0;
		_maintainIngredient = false;
	}
	
	public ItemChanceHolder(int id, double chance, long count, byte enchantmentLevel)
	{
		super(id, count);
		_chance = chance;
		_enchantmentLevel = enchantmentLevel;
		_maintainIngredient = false;
	}
	
	public ItemChanceHolder(int id, double chance, long count, byte enchantmentLevel, boolean maintainIngredient)
	{
		super(id, count);
		_chance = chance;
		_enchantmentLevel = enchantmentLevel;
		_maintainIngredient = maintainIngredient;
	}
	
	/**
	 * Calculates a cumulative chance of all given holders. If all holders' chance sum up to 100% or above, there is 100% guarantee a holder will be selected.
	 *
	 * @param holders
	 *            list of holders to calculate chance from.
	 * @return {@code ItemChanceHolder} of the successful random roll or {@code null} if there was no lucky holder selected.
	 */
	public static ItemChanceHolder getRandomHolder(List<ItemChanceHolder> holders)
	{
		double itemRandom = 100 * Rnd.nextDouble();
		for (ItemChanceHolder holder : holders)
		{
			// Any mathmatical expression including NaN will result in either NaN or 0 of converted to something other than double.
			// We would usually want to skip calculating any holders that include NaN as a chance, because that ruins the overall process.
			if (!Double.isNaN(holder.getChance()))
			{
				// Calculate chance
				if (holder.getChance() > itemRandom)
				{
					return holder;
				}
				itemRandom -= holder.getChance();
			}
		}
		return null;
	}
	
	public static ItemChanceHolder getRandomHolder(PlayerInstance player, List<ItemChanceHolder> holders)
	{
		double itemRandom = 100 * Rnd.nextDouble();
		for (ItemChanceHolder holder : holders)
		{
			double chance = holder.getChance();
			if (holder.getId() == 94096 && player.getVariables().getLong(PlayerVariables.SPECIAL_CRAFT_TAKE_GIRAN_SEALED, 0) < Config.SPECIAL_CRAFT_PET_TRAINING_GUIDE_TAKE_GIRAN_SEALED)
			{
				chance = 0;
			}
			if (holder.getId() == 94117 && player.getVariables().getLong(PlayerVariables.SPECIAL_CRAFT_TAKE_GIRAN_SEALED_ADVANCE_PET, 0) < Config.SPECIAL_CRAFT_ADVANCED_PET_TRAINING_GUIDE_TAKE_GIRAN_SEALED)
			{
				chance = 0;
			}
			if (holder.getId() == 94096 && player.getVariables().getLong(PlayerVariables.SPECIAL_CRAFT_TAKE_GIRAN_SEALED, 0) > Config.SPECIAL_CRAFT_PET_TRAINING_GUIDE_TAKE_GIRAN_SEALED)
			{
				chance = 10;
			}
			if (holder.getId() == 94117 && player.getVariables().getLong(PlayerVariables.SPECIAL_CRAFT_TAKE_GIRAN_SEALED_ADVANCE_PET, 0) > Config.SPECIAL_CRAFT_ADVANCED_PET_TRAINING_GUIDE_TAKE_GIRAN_SEALED)
			{
				chance = 10;
			}
			// Any mathmatical expression including NaN will result in either NaN or 0 of converted to something other than double.
			// We would usually want to skip calculating any holders that include NaN as a chance, because that ruins the overall process.
			if (!Double.isNaN(chance))
			{
				// Calculate chance
				if (chance > itemRandom)
				{
					return holder;
				}
				if (chance == 0)
				{
					return holders.get(1);
				}
				itemRandom -= chance;
			}
		}
		return null;
	}
	
	/**
	 * @param val
	 *            Used by special craft and such
	 */
	public void setIsAnnounced(boolean val)
	{
		_isAnnounced = val;
	}
	
	public boolean isAnnounced()
	{
		return _isAnnounced;
	}
	
	/**
	 * Gets the chance.
	 *
	 * @return the drop chance of the item contained in this object
	 */
	public double getChance()
	{
		return _chance;
	}
	
	/**
	 * Gets the enchant level.
	 *
	 * @return the enchant level of the item contained in this object
	 */
	public byte getEnchantmentLevel()
	{
		return _enchantmentLevel;
	}
	
	public boolean isMaintainIngredient()
	{
		return _maintainIngredient;
	}
	
	@Override
	public String toString()
	{
		return "[" + getClass().getSimpleName() + "] ID: " + getId() + ", count: " + getCount() + ", chance: " + _chance;
	}
}
