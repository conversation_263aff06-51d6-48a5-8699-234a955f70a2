package club.projectessence.gameserver.fakeplayers;

import club.projectessence.gameserver.instancemanager.ZoneManager;
import club.projectessence.gameserver.model.Location;
import club.projectessence.gameserver.model.World;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;

public class FakePlayerSpot {
	private final int _zoneId;
	private final Location _teleportLoc;
	private final int _minLevel;
	private final int _maxLevel;
	private final int _maxPlayerCount;

	public FakePlayerSpot(int zoneId, Location teleportLoc, int minLevel, int maxLevel, int maxPlayerCount) {
		_zoneId = zoneId;
		_teleportLoc = teleportLoc;
		_minLevel = minLevel;
		_maxLevel = maxLevel;
		_maxPlayerCount = maxPlayerCount;
	}

	public int getId() {
		return _zoneId;
	}

	public Location getTeleportLoc() {
		return _teleportLoc;
	}

	public int getMinLevel() {
		return _minLevel;
	}

	public int getMaxLevel() {
		return _maxLevel;
	}

	public int getMaxPlayerCount() {
		return _maxPlayerCount;
	}

	public boolean isStillGoodByLevel(PlayerInstance player) {
		if ((_minLevel <= player.getLevel()) && (player.getLevel() <= _maxLevel)) {
			return true;
		}
		return false;
	}

	public int getAvailableSlots() {
		int takenSlots = 0;
		for (PlayerInstance p : ZoneManager.getInstance().getZoneById(getId()).getAllPlayersInside()) {
			if (p.isGM()) {
				continue;
			}
			if (!p.isFakePlayer()) {
				if (getMaxPlayerCount() <= 6) {
					takenSlots += 2;
				}
			} else if (p.getFakePlayerInfo().TARGET_ZONE == this) {
				takenSlots++;
			}
		}
		return getMaxPlayerCount() - takenSlots - getGoingBotsCount();
	}

	public int getGoingBotsCount() {
		int fpC = 0;
		for (PlayerInstance fp : World.getInstance().getFakePlayers()) {
			FakePlayerSpot fpLoc = fp.getFakePlayerInfo().TARGET_ZONE;
			if ((fpLoc != null) && !ZoneManager.getInstance().getZoneById(fpLoc.getId()).isCharacterInZone(fp) && fpLoc.equals(this)) {
				fpC++;
			}
		}
		return fpC;
	}
}
