package club.projectessence.gameserver.fakeplayers;

import club.projectessence.commons.util.Rnd;
import club.projectessence.gameserver.data.xml.TeleportListData;
import club.projectessence.gameserver.model.Location;
import club.projectessence.gameserver.model.World;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;

import java.util.List;

public class FakePlayerTown {
	private static final int RADIUS = 80;

	private final String _name;
	private final int _townId;
	private final int _maxBotCount;
	private final List<Location> _paths;

	public FakePlayerTown(String name, int townId, int maxBotCount, List<Location> paths) {
		_name = name;
		_townId = townId;
		_maxBotCount = maxBotCount;
		_paths = paths;
	}

	public String getName() {
		return _name;
	}

	public int getId() {
		return _townId;
	}

	public int getMaxBotCount() {
		return _maxBotCount;
	}

	public List<Location> getPaths() {
		return _paths;
	}

	public Location getRandomPath(PlayerInstance player) {
		return _paths.get(Rnd.get(_paths.size()));
	}

	public boolean isAtTeleport(PlayerInstance player) {
		return player.isInsideRadius3D(TeleportListData.getInstance().getTeleport(_townId).getLocation(), RADIUS);
	}

	public int getAvailableSlots() {
		int fpC = 0;
		for (PlayerInstance fp : World.getInstance().getFakePlayers()) {
			FakePlayerTown fpTown = fp.getFakePlayerInfo().TARGET_TOWN;
			if (fpTown == this) {
				fpC++;
			}
		}
		return getMaxBotCount() - fpC;
	}
}
