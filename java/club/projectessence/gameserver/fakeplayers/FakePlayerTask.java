package club.projectessence.gameserver.fakeplayers;

import club.projectessence.Config;
import club.projectessence.commons.concurrent.ThreadPool;
import club.projectessence.commons.util.Rnd;
import club.projectessence.gameserver.ai.CtrlIntention;
import club.projectessence.gameserver.enums.TeleportWhereType;
import club.projectessence.gameserver.fakeplayers.data.FakePlayerTownData;
import club.projectessence.gameserver.fakeplayers.data.FakePlayerZoneData;
import club.projectessence.gameserver.geoengine.GeoEngine;
import club.projectessence.gameserver.instancemanager.MapRegionManager;
import club.projectessence.gameserver.instancemanager.ZoneManager;
import club.projectessence.gameserver.model.Location;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.model.zone.ZoneId;
import club.projectessence.gameserver.network.Disconnection;
import club.projectessence.gameserver.network.clientpackets.autoplay.ExAutoPlaySetting.NextTargetMode;
import club.projectessence.gameserver.taskmanager.autoplay.AutoPlayTaskManager;

/**
 * <AUTHOR>
 */
public class FakePlayerTask implements Runnable
{
	private static final int		TOWN_SPOT_OFFSET	= 300;
	private final PlayerInstance	_player;
	private final FakePlayerInfo	_info;
	
	public FakePlayerTask(PlayerInstance player)
	{
		_player = player;
		_info = player.getFakePlayerInfo();
	}
	
	@Override
	public void run()
	{
		if (Config.ONLY_CHARACTER_CREATE)
		{
			return;
		}
		if (_info.TASK_DELAY > System.currentTimeMillis())
		{
			return;
		}
		if (_player.isDead())
		{
			_info.TASK_DELAY = System.currentTimeMillis() + 30000;
			AutoPlayTaskManager.getInstance().stopAutoPlay(_player);
			Location loc = MapRegionManager.getInstance().getTeleToLocation(_player, TeleportWhereType.TOWN);
			if (loc != null)
			{
				_player.setIsPendingRevive(true);
				ThreadPool.get().schedule(() ->
				{
					_info.TARGET_ZONE = null;
					_player.teleToLocation(loc, true, _player.getInstanceWorld());
				}, Rnd.get(3000, 6000));
				return;
			}
		}
		if (_player.getLevel() <= 6)
		{
			if (!AutoPlayTaskManager.getInstance().isPlayerAutoHunting(_player))
			{
				ThreadPool.get().schedule(() -> AutoPlayTaskManager.getInstance().doAutoPlay(_player, true, true, true, NextTargetMode.MONSTER), Rnd.get(2000, 6000));
			}
		}
		else
		{
			FakePlayerSpot zone = FakePlayerZoneData.getInstance().getCurrentZone(_player);
			if (zone == null)
			{
				_player.setInsideZone(ZoneId.FAKE_PLAYER, false); // In case zone wouldn't trigger leaving the zone
			}
			if ((zone != null) && ((_info.TARGET_ZONE != null) && (zone == _info.TARGET_ZONE)) && _player.isInsideZone(ZoneId.FAKE_PLAYER) && ((zone.getMinLevel() <= _player.getLevel()) && (_player.getLevel() <= zone.getMaxLevel())))
			{
				if (!AutoPlayTaskManager.getInstance().isPlayerAutoHunting(_player))
				{
					ThreadPool.get().schedule(() -> AutoPlayTaskManager.getInstance().doAutoPlay(_player, true, true, true, NextTargetMode.MONSTER), Rnd.get(2000, 6000));
				}
				else
				{
					int rnd = Rnd.get(0, 100);
					if ((rnd < 3) && !_player.isInsideZone(ZoneId.PEACE))
					{
						Location loc = _player.getLocation();
						Location newLoc = new Location(loc.getX() + Rnd.get(-TOWN_SPOT_OFFSET, TOWN_SPOT_OFFSET), loc.getY() + Rnd.get(-TOWN_SPOT_OFFSET, TOWN_SPOT_OFFSET), loc.getZ(), loc.getHeading());
						newLoc = GeoEngine.getInstance().canMoveToTargetLoc(loc.getX(), loc.getY(), loc.getZ(), newLoc.getX(), newLoc.getY(), newLoc.getZ(), _player.getInstanceWorld());
						if (ZoneManager.getInstance().getZoneById(zone.getId()).isInsideZone(newLoc) && GeoEngine.getInstance().canMoveToTarget(loc.getX(), loc.getY(), loc.getZ(), newLoc.getX(), newLoc.getY(), newLoc.getZ(), _player.getInstanceWorld()))
						{
							_player.getAI().setIntention(CtrlIntention.AI_INTENTION_MOVE_TO, newLoc);
						}
					}
				}
			}
			else
			{
				zone = _info.TARGET_ZONE;
				if ((zone == null) || !zone.isStillGoodByLevel(_player))
				{
					zone = FakePlayerZoneData.getInstance().getBestZone(_player);
					_info.TARGET_ZONE = zone;
				}
				if (zone == null)
				{
					if (!_player.isInsideZone(ZoneId.PEACE) || (_info.TARGET_TOWN == null))
					{
						if (!_player.isCastingNow())
						{
							FakePlayerTown town = FakePlayerTownData.getInstance().getTownForAfk();
							if (town == null)
							{
								FakePlayerManager.getInstance().scheduleLogout(_player, 0);
								return;
							}
							_player.getFakePlayerInfo().DONT_SELECT_MORE_TARGETS = _player.isInCombat();
							if (!_player.getFakePlayerInfo().DONT_SELECT_MORE_TARGETS && !_player.isInCombat())
							{
								_player.abortAttack();
								_player.abortCast();
								_info.TARGET_TOWN = town;
								_info.TARGET_ZONE = null;
								// TODO: town with location teleport
								// FakePlayerManager.getInstance().teleport(_player, town.getId());
								// TEMP:
								Disconnection.of(_player.getClient(), _player).logout(false, false);
								_info.TASK_DELAY = System.currentTimeMillis() + Rnd.get(5000, 9000);
							}
						}
					}
					else
					{
						if (_info.TARGET_TOWN != null)
						{
							if (_info.TARGET_TOWN.isAtTeleport(_player) && !_player.isMoving())
							{
								Location loc = _player.getLocation();
								Location newLoc = null;
								Location nextPath = _info.TARGET_TOWN.getRandomPath(_player);
								if (nextPath != null)
								{
									newLoc = new Location(nextPath.getX() + Rnd.get(-TOWN_SPOT_OFFSET, TOWN_SPOT_OFFSET), nextPath.getY() + Rnd.get(-TOWN_SPOT_OFFSET, TOWN_SPOT_OFFSET), nextPath.getZ());
									if (GeoEngine.getInstance().canMoveToTarget(loc.getX(), loc.getY(), loc.getZ(), nextPath.getX(), newLoc.getY(), newLoc.getZ(), _player.getInstanceWorld()))
									{
										_player.getAI().setIntention(CtrlIntention.AI_INTENTION_MOVE_TO, newLoc);
									}
								}
								_info.TASK_DELAY = System.currentTimeMillis() + Rnd.get(10000, 30000);
							}
						}
					}
					return;
				}
				if (!_player.isInsideZone(ZoneId.FAKE_PLAYER))
				{
					if (_info.TASK_DELAY < System.currentTimeMillis())
					{
						FakePlayerManager.getInstance().teleport(_player, zone.getTeleportLoc());
						_info.TASK_DELAY = System.currentTimeMillis() + Rnd.get(40000, 80000);
					}
				}
				if (!AutoPlayTaskManager.getInstance().isPlayerAutoHunting(_player))
				{
					ThreadPool.get().schedule(() -> AutoPlayTaskManager.getInstance().doAutoPlay(_player, true, true, true, NextTargetMode.MONSTER), Rnd.get(2000, 4000));
				}
			}
		}
	}
}
