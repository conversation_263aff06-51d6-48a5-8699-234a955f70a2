package club.projectessence.gameserver.fakeplayers.data;

import club.projectessence.Config;
import club.projectessence.commons.util.Rnd;
import club.projectessence.gameserver.data.sql.CharNameTable;

import java.io.BufferedReader;
import java.io.FileReader;
import java.io.LineNumberReader;
import java.util.ArrayList;
import java.util.List;
import java.util.logging.Logger;

public class FakePlayerShopNameData {
	public static final String FAKE_PLAYERS_LIST = "data/fakeplayers/fp_shopNames.txt";
	private static final Logger LOG = Logger.getLogger(FakePlayerShopNameData.class.getName());
	public static List<String> _fakePlayers = new ArrayList<>();

	public FakePlayerShopNameData() {
		try (FileReader fr = new FileReader(FAKE_PLAYERS_LIST)) {
			try (BufferedReader br = new BufferedReader(fr)) {
				try (LineNumberReader lnr = new LineNumberReader(br)) {
					String line;
					while ((line = lnr.readLine()) != null) {
						if (line.trim().length() == 0) {
							continue;
						}
						if (line.startsWith("#")) {
							continue;
						}
						if (!Config.CHARNAME_TEMPLATE_PATTERN.matcher(line).matches()) {
							continue;
						}
						if (CharNameTable.getInstance().getIdByName(line) > 0) {
							continue;
						}
						_fakePlayers.add(line);
					}
					LOG.info("Loaded " + _fakePlayers.size() + " Unused Fake Player shop names.");
				}
			}
		} catch (Exception e) {
			LOG.warning(e.getMessage());
		}
	}

	public static FakePlayerShopNameData getInstance() {
		return SingletonHolder._instance;
	}

	public int getCount() {
		return _fakePlayers.size();
	}

	public List<String> getNames() {
		return _fakePlayers;
	}

	public String getRandomName() {
		return _fakePlayers.get(Rnd.get(_fakePlayers.size()));
	}

	private static class SingletonHolder {
		protected static final FakePlayerShopNameData _instance = new FakePlayerShopNameData();
	}
}
