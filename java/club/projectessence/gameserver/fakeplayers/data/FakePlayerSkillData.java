package club.projectessence.gameserver.fakeplayers.data;

import java.io.File;
import java.util.ArrayList;
import java.util.EnumMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Logger;

import org.w3c.dom.Document;
import org.w3c.dom.NamedNodeMap;
import org.w3c.dom.Node;

import club.projectessence.commons.util.IXmlReader;
import club.projectessence.gameserver.enums.AutoPlaySkillType;
import club.projectessence.gameserver.enums.AutoUseItemType;
import club.projectessence.gameserver.enums.ClassId;
import club.projectessence.gameserver.enums.ShortcutType;
import club.projectessence.gameserver.model.StatSet;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.model.holders.FakePlayerSkillListHolder;
import club.projectessence.gameserver.model.items.instance.ItemInstance;
import club.projectessence.gameserver.model.skills.Skill;
import club.projectessence.gameserver.taskmanager.autoplay.AutoPlayTaskManager;
import club.projectessence.gameserver.taskmanager.autoplay.AutoUseTaskManager;
import club.projectessence.gameserver.taskmanager.autoplay.AutoUseTaskManager.AutoUsePotionType;

/**
 * <AUTHOR>
 */
public class FakePlayerSkillData implements IXmlReader
{
	private static final Logger									LOGGER	= Logger.getLogger(FakePlayerSkillData.class.getName());
	private static final String									PATH	= "data/fakeplayers/fp_skills.xml";
	private final Map<ClassId, List<FakePlayerSkillListHolder>>	_skills	= new EnumMap<>(ClassId.class);
	
	protected FakePlayerSkillData()
	{
		load();
	}
	
	public static FakePlayerSkillData getInstance()
	{
		return SingletonHolder.INSTANCE;
	}
	
	@Override
	public void load()
	{
		_skills.clear();
		parseDatapackFile(PATH);
		LOGGER.info(getClass().getSimpleName() + ": Loaded " + _skills.size() + " classes Fake Player skill data.");
	}
	
	@Override
	public void parseDocument(Document doc, File f)
	{
		for (Node n = doc.getFirstChild(); n != null; n = n.getNextSibling())
		{
			if ("list".equalsIgnoreCase(n.getNodeName()))
			{
				for (Node d = n.getFirstChild(); d != null; d = d.getNextSibling())
				{
					if ("skills".equalsIgnoreCase(d.getNodeName()))
					{
						NamedNodeMap attrs = d.getAttributes();
						ClassId classId = ClassId.getClassId(parseInteger(attrs, "classId"));
						int minLevel = parseInteger(attrs, "minLevel");
						int maxLevel = parseInteger(attrs, "maxLevel");
						FakePlayerSkillListHolder skillList = new FakePlayerSkillListHolder(minLevel, maxLevel);
						for (Node b = d.getFirstChild(); b != null; b = b.getNextSibling())
						{
							if ("command".equals(b.getNodeName()))
							{
								attrs = b.getAttributes();
								final ShortcutType type = parseEnum(attrs, ShortcutType.class, "type");
								int id = parseInteger(attrs, "id");
								skillList.add(type, id);
							}
						}
						List<FakePlayerSkillListHolder> skills = _skills.get(classId);
						if (skills == null)
						{
							skills = new ArrayList<>();
						}
						skills.add(skillList);
						_skills.put(classId, skills);
					}
				}
			}
		}
	}
	
	public void updateSkills(PlayerInstance player)
	{
		player.getAutoUseSettings().getAutoPotionItems().clear();
		player.getAutoUseSettings().getAutoSupplyItems().clear();
		player.getAutoUseSettings().getAutoSkills().clear();
		player.getAutoPlaySettings().getAutoSkills().clear();
		List<FakePlayerSkillListHolder> skillLists = _skills.get(player.getClassId());
		if (skillLists == null)
		{
			return;
		}
		player.giveAvailableSkills(true, true);
		for (FakePlayerSkillListHolder skillList : skillLists)
		{
			if ((skillList.getMinLevel() <= player.getLevel()) && (skillList.getMaxLevel() >= player.getLevel()))
			{
				for (StatSet set : skillList.getSkills())
				{
					ShortcutType type = set.getEnum("type", ShortcutType.class);
					int id = set.getInt("id");
					switch (type)
					{
						case SKILL:
						{
							Skill skill = player.getKnownSkill(id);
							if (skill == null)
							{
								continue;
							}
							if (skill.getShortcutToggleType() == AutoPlaySkillType.BUFF)
							{
								AutoUseTaskManager.getInstance().addAutoSkill(player, id);
							}
							else if (skill.getShortcutToggleType() == AutoPlaySkillType.ATTACK)
							{
								AutoPlayTaskManager.getInstance().addAutoSkill(player, id);
							}
							break;
						}
						case ITEM:
						{
							ItemInstance item = player.getInventory().getItemByItemId(id);
							if (item == null)
							{
								continue;
							}
							if (item.getItem().getShortcutToggleType() == AutoUseItemType.BUFF)
							{
								AutoUseTaskManager.getInstance().addAutoSupplyItem(player, id);
							}
							else if (item.getItem().getShortcutToggleType() == AutoUseItemType.POTION)
							{
								player.getAutoPlaySettings().setAutoHpPotionPercent(95);
								player.getAutoPlaySettings().setAutoHp2PotionPercent(95);
								player.getAutoPlaySettings().setAutoMpPotionPercent(95);
								AutoUseTaskManager.getInstance().addAutoPotionItem(player, AutoUsePotionType.HP, id);
								AutoUseTaskManager.getInstance().addAutoPotionItem(player, AutoUsePotionType.HP2, id);
								AutoUseTaskManager.getInstance().addAutoPotionItem(player, AutoUsePotionType.MP, id);
							}
							break;
						}
						case ACTION:
						{
							AutoPlayTaskManager.getInstance().addAutoAction(player, id);
						}
					}
				}
				return;
			}
		}
		LOGGER.warning("No skill data found for classId: " + player.getClassIndex() + " level: " + player.getLevel() + " " + player);
	}
	
	private static class SingletonHolder
	{
		protected static final FakePlayerSkillData INSTANCE = new FakePlayerSkillData();
	}
}