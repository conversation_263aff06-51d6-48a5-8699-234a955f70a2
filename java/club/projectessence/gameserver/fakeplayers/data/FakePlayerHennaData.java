package club.projectessence.gameserver.fakeplayers.data;

import club.projectessence.commons.util.IXmlReader;
import club.projectessence.gameserver.enums.ClassId;
import club.projectessence.gameserver.model.StatSet;
import org.w3c.dom.Document;
import org.w3c.dom.NamedNodeMap;
import org.w3c.dom.Node;

import java.io.File;
import java.util.*;
import java.util.logging.Logger;

/**
 * <AUTHOR>
 */
public class FakePlayerHennaData implements IXmlReader {
	private static final Logger LOGGER = Logger.getLogger(FakePlayerHennaData.class.getName());
	private static final String PATH = "data/fakeplayers/fp_dyes.xml";
	private final Map<ClassId, Map<Integer, List<Integer>>> _hennas = new EnumMap<>(ClassId.class);

	protected FakePlayerHennaData() {
		load();
	}

	public static FakePlayerHennaData getInstance() {
		return SingletonHolder.INSTANCE;
	}

	@Override
	public void load() {
		_hennas.clear();
		parseDatapackFile(PATH);
		LOGGER.info(getClass().getSimpleName() + ": Loaded " + _hennas.size() + " Fake Player dyes data.");
	}

	@Override
	public void parseDocument(Document doc, File f) {
		for (Node n = doc.getFirstChild(); n != null; n = n.getNextSibling()) {
			if ("list".equalsIgnoreCase(n.getNodeName())) {
				for (Node d = n.getFirstChild(); d != null; d = d.getNextSibling()) {
					if ("dyes".equalsIgnoreCase(d.getNodeName())) {
						parseHenna(d);
					}
				}
			}
		}
	}

	private void parseHenna(Node d) {
		NamedNodeMap attrs = d.getAttributes();
		final ClassId classId = ClassId.getClassId(Integer.parseInt(attrs.getNamedItem("classId").getNodeValue()));
		final int level = Integer.parseInt(attrs.getNamedItem("level").getNodeValue());
		final List<Integer> hennaList = new ArrayList<>();
		for (Node c = d.getFirstChild(); c != null; c = c.getNextSibling()) {
			if ("dye".equalsIgnoreCase(c.getNodeName())) {
				final StatSet set = new StatSet();
				attrs = c.getAttributes();
				for (int i = 0; i < attrs.getLength(); i++) {
					final Node attr = attrs.item(i);
					set.set(attr.getNodeName(), attr.getNodeValue());
					if (attr.getNodeName().equalsIgnoreCase("symbolId")) {
						hennaList.add(Integer.parseInt(attr.getNodeValue()));
					}
				}
			}
		}
		Map<Integer, List<Integer>> map = null;
		map = _hennas.get(classId);
		if (map == null) {
			map = new HashMap<>();
		}
		map.put(level, hennaList);
		_hennas.put(classId, map);
	}

	public List<Integer> getHennaList(ClassId classId, int level) {
		Map<Integer, List<Integer>> map = _hennas.get(classId);
		if (map == null) {
			return null;
		}
		return map.get(level);
	}

	private static class SingletonHolder {
		protected static final FakePlayerHennaData INSTANCE = new FakePlayerHennaData();
	}
}