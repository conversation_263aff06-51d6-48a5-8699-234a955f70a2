package club.projectessence.gameserver.fakeplayers.data;

import club.projectessence.commons.util.IXmlReader;
import club.projectessence.gameserver.fakeplayers.FakePlayerSpot;
import club.projectessence.gameserver.instancemanager.ZoneManager;
import club.projectessence.gameserver.model.Location;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.model.zone.type.FakePlayerZone;
import org.w3c.dom.Document;
import org.w3c.dom.NamedNodeMap;
import org.w3c.dom.Node;

import java.io.File;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.logging.Logger;

/**
 * <AUTHOR>
 */
public class FakePlayerZoneData implements IXmlReader {
	private static final Logger LOGGER = Logger.getLogger(FakePlayerZoneData.class.getName());
	private static final String PATH = "data/fakeplayers/fp_zones.xml";
	private final List<FakePlayerSpot> _spots = new ArrayList<>();

	protected FakePlayerZoneData() {
		load();
	}

	public static FakePlayerZoneData getInstance() {
		return SingletonHolder.INSTANCE;
	}

	@Override
	public void load() {
		_spots.clear();
		parseDatapackFile(PATH);
		LOGGER.info(getClass().getSimpleName() + ": Loaded " + _spots.size() + " Fake Player zones data.");
	}

	@Override
	public void parseDocument(Document doc, File f) {
		for (Node n = doc.getFirstChild(); n != null; n = n.getNextSibling()) {
			if ("list".equalsIgnoreCase(n.getNodeName())) {
				List<Integer> existing = new ArrayList<>();
				for (Node d = n.getFirstChild(); d != null; d = d.getNextSibling()) {
					if ("zone".equalsIgnoreCase(d.getNodeName())) {
						NamedNodeMap attrs = d.getAttributes();
						int zoneId = parseInteger(attrs, "zoneId");
						if (existing.contains(zoneId)) {
							LOGGER.warning("Duplicated Fake Player Zone: " + zoneId);
							continue;
						}
						existing.add(zoneId);
						int minLevel = parseInteger(attrs, "minLevel");
						int maxLevel = parseInteger(attrs, "maxLevel");
						int maxPlayerCount = parseInteger(attrs, "maxPlayerCount");
						for (Node b = d.getFirstChild(); b != null; b = b.getNextSibling()) {
							if ("teleport".equals(b.getNodeName())) {
								attrs = b.getAttributes();
								int x = parseInteger(attrs, "x");
								int y = parseInteger(attrs, "y");
								int z = parseInteger(attrs, "z");
								_spots.add(new FakePlayerSpot(zoneId, new Location(x, y, z), minLevel, maxLevel, maxPlayerCount));
							}
						}
					}
				}
			}
		}
	}

	public FakePlayerSpot getBestZone(PlayerInstance player) {
		synchronized (this) {
			List<FakePlayerSpot> available = new ArrayList<>();
			ZONE:
			for (FakePlayerSpot zone : _spots) {
				if ((zone.getMinLevel() <= player.getLevel()) && (player.getLevel() <= zone.getMaxLevel()) && (zone.getAvailableSlots() > 0)) {
					FakePlayerZone z = (FakePlayerZone) ZoneManager.getInstance().getZoneById(zone.getId());
					for (PlayerInstance p : z.getAllPlayersInside()) {
						if ((p.getReputation() < 0) && !p.isGM()) {
							continue ZONE;
						}
					}
					available.add(zone);
				}
			}
			if (available.isEmpty()) {
				return null;
			}
			Collections.sort(available, (z1, z2) ->
			{
				Double val1 = ((z1.getMaxPlayerCount() - z1.getAvailableSlots()) * 100.0) / z1.getMaxPlayerCount();
				Double val2 = ((z2.getMaxPlayerCount() - z2.getAvailableSlots()) * 100.0) / z2.getMaxPlayerCount();
				if (val1.equals(val2)) {
					return 0;
				}
				return val1.compareTo(val2);
			});
			return available.get(0);
			// return available.get(Rnd.get(available.size()));
		}
	}

	public FakePlayerSpot getCurrentZone(PlayerInstance player) {
		for (FakePlayerSpot zone : _spots) {
			FakePlayerZone fpzone = (FakePlayerZone) ZoneManager.getInstance().getZoneById(zone.getId());
			if (fpzone == null) {
				return null;
			}
			if (fpzone.isCharacterInZone(player)) {
				return zone;
			}
		}
		return null;
	}

	public FakePlayerSpot getZoneById(int id) {
		for (FakePlayerSpot zone : _spots) {
			if (zone.getId() == id) {
				return zone;
			}
		}
		return null;
	}

	public List<FakePlayerSpot> getAllZones() {
		return _spots;
	}

	private static class SingletonHolder {
		protected static final FakePlayerZoneData INSTANCE = new FakePlayerZoneData();
	}
}