package club.projectessence.gameserver.fakeplayers.data;

import club.projectessence.commons.util.IXmlReader;
import club.projectessence.commons.util.Rnd;
import club.projectessence.gameserver.fakeplayers.FakePlayerTown;
import club.projectessence.gameserver.model.Location;
import org.w3c.dom.Document;
import org.w3c.dom.NamedNodeMap;
import org.w3c.dom.Node;

import java.io.File;
import java.util.ArrayList;
import java.util.List;
import java.util.logging.Logger;

/**
 * <AUTHOR>
 */
public class FakePlayerTownData implements IXmlReader {
	private static final Logger LOGGER = Logger.getLogger(FakePlayerTownData.class.getName());
	private static final String PATH = "data/fakeplayers/fp_towns.xml";
	private final List<FakePlayerTown> _towns = new ArrayList<>();

	protected FakePlayerTownData() {
		load();
	}

	public static FakePlayerTownData getInstance() {
		return SingletonHolder.INSTANCE;
	}

	@Override
	public void load() {
		_towns.clear();
		parseDatapackFile(PATH);
		LOGGER.info(getClass().getSimpleName() + ": Loaded " + _towns.size() + " Fake Player towns data.");
	}

	@Override
	public void parseDocument(Document doc, File f) {
		for (Node n = doc.getFirstChild(); n != null; n = n.getNextSibling()) {
			if ("list".equalsIgnoreCase(n.getNodeName())) {
				List<Integer> existing = new ArrayList<>();
				for (Node d = n.getFirstChild(); d != null; d = d.getNextSibling()) {
					if ("town".equalsIgnoreCase(d.getNodeName())) {
						NamedNodeMap attrs = d.getAttributes();
						final List<Location> paths = new ArrayList<>(1);
						String name = parseString(attrs, "name");
						int townId = parseInteger(attrs, "townId");
						if (existing.contains(townId)) {
							LOGGER.warning("Duplicated Fake Player Town: " + townId);
							continue;
						}
						existing.add(townId);
						int maxBotCount = parseInteger(attrs, "maxBotCount");
						for (Node b = d.getFirstChild(); b != null; b = b.getNextSibling()) {
							if ("path".equals(b.getNodeName())) {
								attrs = b.getAttributes();
								int x = parseInteger(attrs, "x");
								int y = parseInteger(attrs, "y");
								int z = parseInteger(attrs, "z");
								paths.add(new Location(x, y, z));
							}
						}
						_towns.add(new FakePlayerTown(name, townId, maxBotCount, paths));
					}
				}
			}
		}
	}

	public FakePlayerTown getTownForAfk() {
		List<FakePlayerTown> available = new ArrayList<>();
		for (FakePlayerTown town : _towns) {
			if (town.getAvailableSlots() > 0) {
				available.add(town);
			}
		}
		if (available.isEmpty()) {
			return null;
		}
		return available.get(Rnd.get(available.size()));
	}

	public FakePlayerTown getTownForLogout() {
		return _towns.get(Rnd.get(_towns.size()));
	}

	private static class SingletonHolder {
		protected static final FakePlayerTownData INSTANCE = new FakePlayerTownData();
	}
}