package club.projectessence.gameserver.fakeplayers.data;

import club.projectessence.commons.util.IXmlReader;
import club.projectessence.gameserver.enums.ClassId;
import club.projectessence.gameserver.model.StatSet;
import club.projectessence.gameserver.model.items.PlayerItemTemplate;
import org.w3c.dom.Document;
import org.w3c.dom.NamedNodeMap;
import org.w3c.dom.Node;

import java.io.File;
import java.util.*;
import java.util.logging.Logger;

/**
 * <AUTHOR>
 */
public class FakePlayerItemData implements IXmlReader {
	private static final Logger LOGGER = Logger.getLogger(FakePlayerItemData.class.getName());
	private static final String PATH = "data/fakeplayers/fp_items.xml";
	private final Map<ClassId, Map<Integer, List<PlayerItemTemplate>>> _items = new EnumMap<>(ClassId.class);
	private final Map<Integer, List<PlayerItemTemplate>> _commonItems = new HashMap<>();

	protected FakePlayerItemData() {
		load();
	}

	public static FakePlayerItemData getInstance() {
		return SingletonHolder.INSTANCE;
	}

	@Override
	public void load() {
		_items.clear();
		_commonItems.clear();
		parseDatapackFile(PATH);
		LOGGER.info(getClass().getSimpleName() + ": Loaded " + _items.size() + " Fake Player items data.");
	}

	@Override
	public void parseDocument(Document doc, File f) {
		for (Node n = doc.getFirstChild(); n != null; n = n.getNextSibling()) {
			if ("list".equalsIgnoreCase(n.getNodeName())) {
				for (Node d = n.getFirstChild(); d != null; d = d.getNextSibling()) {
					if ("equipment".equalsIgnoreCase(d.getNodeName())) {
						parseEquipment(d);
					}
				}
			}
		}
	}

	private void parseEquipment(Node d) {
		NamedNodeMap attrs = d.getAttributes();
		int cId = Integer.parseInt(attrs.getNamedItem("classId").getNodeValue());
		if (cId == -1) {
			final int level = Integer.parseInt(attrs.getNamedItem("level").getNodeValue());
			final List<PlayerItemTemplate> equipList = new ArrayList<>();
			for (Node c = d.getFirstChild(); c != null; c = c.getNextSibling()) {
				if ("item".equalsIgnoreCase(c.getNodeName())) {
					final StatSet set = new StatSet();
					attrs = c.getAttributes();
					for (int i = 0; i < attrs.getLength(); i++) {
						final Node attr = attrs.item(i);
						set.set(attr.getNodeName(), attr.getNodeValue());
					}
					equipList.add(new PlayerItemTemplate(set));
				}
			}
			_commonItems.put(level, equipList);
		} else {
			final ClassId classId = ClassId.getClassId(cId);
			final int level = Integer.parseInt(attrs.getNamedItem("level").getNodeValue());
			final List<PlayerItemTemplate> equipList = new ArrayList<>();
			for (Node c = d.getFirstChild(); c != null; c = c.getNextSibling()) {
				if ("item".equalsIgnoreCase(c.getNodeName())) {
					final StatSet set = new StatSet();
					attrs = c.getAttributes();
					for (int i = 0; i < attrs.getLength(); i++) {
						final Node attr = attrs.item(i);
						set.set(attr.getNodeName(), attr.getNodeValue());
					}
					equipList.add(new PlayerItemTemplate(set));
				}
			}
			Map<Integer, List<PlayerItemTemplate>> map = null;
			map = _items.get(classId);
			if (map == null) {
				map = new HashMap<>();
			}
			map.put(level, equipList);
			_items.put(classId, map);
		}
	}

	public List<PlayerItemTemplate> getEquipmentList(ClassId classId, int level) {
		Map<Integer, List<PlayerItemTemplate>> map = _items.get(classId);
		if (map == null) {
			return null;
		}
		return map.get(level);
	}

	public List<PlayerItemTemplate> getCommonEquipmentList(int level) {
		return _commonItems.get(level);
	}

	private static class SingletonHolder {
		protected static final FakePlayerItemData INSTANCE = new FakePlayerItemData();
	}
}