package club.projectessence.gameserver.fakeplayers;

import club.projectessence.gameserver.enums.ClassId;

/**
 * <AUTHOR>
 */

public class FakePlayerSetting {
	private final ClassId _endClassId;
	private boolean _spawnAfterRestart;

	public FakePlayerSetting(ClassId endClassId, boolean spawnAfterRestart) {
		_endClassId = endClassId;
		_spawnAfterRestart = spawnAfterRestart;
	}

	public ClassId getEndClassId() {
		return _endClassId;
	}

	public boolean getSpawnAfterRestart() {
		return _spawnAfterRestart;
	}

	public void setSpawnAfterRestart(boolean val) {
		_spawnAfterRestart = val;
	}
}
