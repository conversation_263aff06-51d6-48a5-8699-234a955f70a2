package club.projectessence.gameserver.fakeplayers;

import static club.projectessence.gameserver.model.itemcontainer.Inventory.MAX_ADENA;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Random;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ScheduledFuture;
import java.util.logging.Level;
import java.util.logging.Logger;

import club.projectessence.Config;
import club.projectessence.commons.concurrent.ThreadPool;
import club.projectessence.commons.database.DatabaseFactory;
import club.projectessence.commons.util.Rnd;
import club.projectessence.gameserver.data.sql.CharNameTable;
import club.projectessence.gameserver.data.xml.HennaData;
import club.projectessence.gameserver.data.xml.PlayerTemplateData;
import club.projectessence.gameserver.data.xml.SkillData;
import club.projectessence.gameserver.data.xml.SkillTreeData;
import club.projectessence.gameserver.enums.ClassId;
import club.projectessence.gameserver.enums.PrivateStoreType;
import club.projectessence.gameserver.enums.Race;
import club.projectessence.gameserver.enums.TeleportWhereType;
import club.projectessence.gameserver.fakeplayers.data.FakePlayerHennaData;
import club.projectessence.gameserver.fakeplayers.data.FakePlayerItemData;
import club.projectessence.gameserver.fakeplayers.data.FakePlayerNameData;
import club.projectessence.gameserver.fakeplayers.data.FakePlayerShopNameData;
import club.projectessence.gameserver.fakeplayers.data.FakePlayerSkillData;
import club.projectessence.gameserver.instancemanager.MapRegionManager;
import club.projectessence.gameserver.model.Location;
import club.projectessence.gameserver.model.SkillLearn;
import club.projectessence.gameserver.model.TradeList;
import club.projectessence.gameserver.model.World;
import club.projectessence.gameserver.model.actor.appearance.PlayerAppearance;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.model.actor.templates.PlayerTemplate;
import club.projectessence.gameserver.model.henna.Henna;
import club.projectessence.gameserver.model.holders.FakeShopItemHolder;
import club.projectessence.gameserver.model.holders.SkillHolder;
import club.projectessence.gameserver.model.itemcontainer.Inventory;
import club.projectessence.gameserver.model.items.PlayerItemTemplate;
import club.projectessence.gameserver.model.items.instance.ItemInstance;
import club.projectessence.gameserver.model.zone.ZoneId;
import club.projectessence.gameserver.network.Disconnection;
import club.projectessence.gameserver.network.serverpackets.PrivateStoreMsgBuy;
import club.projectessence.gameserver.network.serverpackets.PrivateStoreMsgSell;
import club.projectessence.gameserver.network.serverpackets.elementalspirits.ElementalSpiritInfo;
import club.projectessence.gameserver.taskmanager.AttackStanceTaskManager;
import club.projectessence.gameserver.taskmanager.autoplay.AutoPlayTaskManager;
import club.projectessence.gameserver.util.OfflineTradeUtil;
import club.projectessence.gameserver.util.Util;

public class FakePlayerManager
{
	private static final Logger								LOGGER				= Logger.getLogger(FakePlayerManager.class.getName());
	private static final String								LOAD_FAKE_PLAYERS	= "SELECT * FROM fake_players";
	private static final String								DELETE_FAKE_PLAYERS	= "TRUNCATE TABLE fake_players";
	private static final String								INSERT_FAKE_PLAYERS	= "INSERT INTO fake_players (charId, end_class_id, spawn_after_restart) VALUES (?,?,?)";
	private static final Map<Integer, ScheduledFuture<?>>	logoutTasks			= new ConcurrentHashMap<>();
	private static final Map<Integer, ScheduledFuture<?>>	SpawnTasks			= new ConcurrentHashMap<>();
	private static final long								MIN_PLAY_TIME		= 9999;
	private static final long								MAX_PLAY_TIME		= 99999;
	private static final long								RR_SPAWN_DELAY		= 60;
	private static final Map<Integer, FakePlayerSetting>	_settings			= new HashMap<>();
	private static final SkillHolder						TELEPORT			= new SkillHolder(60018, 1);
	private static final Map<Integer, SpawnInfo>			_classes			= new HashMap<>();
	public static boolean									INIT_STARTED		= false;
	private static int										_initBotCount		= 0;
	
	private FakePlayerManager()
	{
		loadFakePlayers();
	}
	
	public static FakePlayerManager getInstance()
	{
		return SingletonHolder.INSTANCE;
	}
	
	private void loadFakePlayers()
	{
		Connection con = null;
		PreparedStatement statement = null;
		ResultSet rset = null;
		try
		{
			con = DatabaseFactory.getConnection();
			statement = con.prepareStatement(LOAD_FAKE_PLAYERS);
			rset = statement.executeQuery();
			long startTime = System.currentTimeMillis();
			int toSpawn = 0;
			while (rset.next())
			{
				int objId = rset.getInt("charId");
				boolean spawn = rset.getBoolean("spawn_after_restart");
				_settings.put(objId, new FakePlayerSetting(ClassId.getClassId(rset.getInt("end_class_id")), spawn));
				if (spawn)
				{
					long minutes = Rnd.get(0, RR_SPAWN_DELAY);
					SpawnTasks.put(objId, ThreadPool.get().schedule(() -> spawnExistingFakePlayer(objId), (minutes * 60000) + Rnd.get(0, 60000)));
					toSpawn++;
				}
			}
			int elapsedTime = (int) (System.currentTimeMillis() - startTime);
			LOGGER.info("Loaded " + _settings.size() + " Fake Players in " + elapsedTime + " ms. (" + toSpawn + " will be spawned in the next " + RR_SPAWN_DELAY + " minutes)");
		}
		catch (Exception e)
		{}
		finally
		{
			if (rset != null)
			{
				try
				{
					rset.close();
				}
				catch (Exception e)
				{}
			}
			if (statement != null)
			{
				try
				{
					statement.close();
				}
				catch (Exception e)
				{}
			}
			if (con != null)
			{
				try
				{
					con.close();
				}
				catch (Exception e)
				{}
			}
		}
	}
	
	public void storeFakePlayers()
	{
		try (Connection con = DatabaseFactory.getConnection())
		{
			long sTime = System.currentTimeMillis();
			int count = 0;
			try (PreparedStatement st = con.prepareStatement(DELETE_FAKE_PLAYERS))
			{
				st.execute();
			}
			try (PreparedStatement st = con.prepareStatement(INSERT_FAKE_PLAYERS))
			{
				for (Entry<Integer, FakePlayerSetting> entry : _settings.entrySet())
				{
					FakePlayerSetting setting = entry.getValue();
					if (setting != null)
					{
						st.setInt(1, entry.getKey());
						st.setInt(2, setting.getEndClassId().getId());
						st.setBoolean(3, setting.getSpawnAfterRestart());
						st.addBatch();
						count++;
					}
				}
				st.executeBatch();
			}
			LOGGER.info("FakePlayerManager: " + count + " Fake Players saved(" + (System.currentTimeMillis() - sTime) + "ms).");
		}
		catch (Exception e)
		{
			LOGGER.warning("FakePlayerManager: Error while saving settings");
			e.printStackTrace();
		}
	}
	
	public void onLevelUp(PlayerInstance player, int oldLv, int newLv)
	{
		if (newLv < oldLv)
		{
			return;
		}
		player.giveAvailableSkills(true, true);
		ThreadPool.get().schedule(() ->
		{
			for (int i = oldLv + 1; i <= newLv; i++)
			{
				int level = i;
				if (level == 20)
				{
					changeClass(player, _settings.get(player.getObjectId()).getEndClassId().getParent().getParent().getId());
				}
				else if (level == 40)
				{
					changeClass(player, _settings.get(player.getObjectId()).getEndClassId().getParent().getId());
				}
				else if (level == 76)
				{
					changeClass(player, _settings.get(player.getObjectId()).getEndClassId().getId());
				}
				considerNewGear(player, level, FakePlayerType.NORMAL);
				setAutoShots(player);
				FakePlayerSkillData.getInstance().updateSkills(player);
			}
		}, Rnd.get(5000, 8000));
	}
	
	public List<PlayerInstance> spawnRandomExistingFakePlayers(int count)
	{
		Map<Integer, FakePlayerSetting> available = new HashMap<>();
		for (Entry<Integer, FakePlayerSetting> entry : _settings.entrySet())
		{
			PlayerInstance plr = World.getInstance().getPlayer(entry.getKey());
			if (((plr == null) || !plr.isOnline()) && !available.containsKey(entry.getKey()))
			{
				available.put(entry.getKey(), entry.getValue());
			}
		}
		if (available.isEmpty())
		{
			return null;
		}
		List<PlayerInstance> spawned = new ArrayList<>();
		int i = 0;
		for (Entry<Integer, FakePlayerSetting> entry : available.entrySet())
		{
			if (i >= count)
			{
				break;
			}
			PlayerInstance fp = spawnExistingFakePlayer(entry.getKey());
			if (fp != null)
			{
				spawned.add(fp);
				i++;
			}
		}
		return spawned;
	}
	
	public PlayerInstance spawnRandomExistingFakePlayer()
	{
		List<Entry<Integer, FakePlayerSetting>> available = new ArrayList<>();
		for (Entry<Integer, FakePlayerSetting> entry : _settings.entrySet())
		{
			PlayerInstance plr = World.getInstance().getPlayer(entry.getKey());
			if ((plr == null) || !plr.isOnline())
			{
				available.add(entry);
			}
		}
		if (available.isEmpty())
		{
			return null;
		}
		int rnd = Rnd.get(available.size());
		int objectId = available.get(rnd).getKey();
		return spawnExistingFakePlayer(objectId);
	}
	
	public PlayerInstance spawnExistingFakePlayer(int objectId)
	{
		PlayerInstance testPlayer = World.getInstance().getPlayer(objectId);
		if ((testPlayer == null) || ((testPlayer.getClient() != null) && testPlayer.getClient().isDetached()))
		{
			final PlayerInstance player = PlayerInstance.load(objectId);
			player.setOnlineStatus(true, false);
			player.setInvisible(true);
			// GameClient client = new GameClient(null);
			// client.setDetached(true);
			player.setFakePlayer(true);
			player.setCurrentCp(player.getMaxCp());
			player.setCurrentHp(player.getMaxHp());
			player.setCurrentMp(player.getMaxMp());
			setAutoShots(player);
			Location loc = null;
			if (player.getLevel() >= 7)
			{
				loc = MapRegionManager.getInstance().getTeleToLocation(player, TeleportWhereType.TOWN);
			}
			else
			{
				loc = player.getTemplate().getCreationPoint();
			}
			player.setHeading(Rnd.get(1, 50000));
			player.setXYZInvisible(loc.getX(), loc.getY(), loc.getZ());
			// client.setPlayer(player);
			// client.setAccountName(player.getAccountNamePlayer());
			// player.setClient(client);
			ThreadPool.get().schedule(() ->
			{
				if (player.isDead())
				{
					player.doRevive();
				}
				if (!player.isSpawned())
				{
					player.spawnMe();
				}
				else
				{
					System.out.println(player.getName() + " is already spawned ???");
				}
				for (SkillLearn skill : SkillTreeData.getInstance().getAvailableSkills(player, player.getClassId(), false, true))
				{
					player.addSkill(SkillData.getInstance().getSkill(skill.getSkillId(), skill.getSkillLevel()), true);
				}
				player.setRunning();
				player.startFakePlayerTask();
				scheduleLogout(player, Rnd.get(MIN_PLAY_TIME, MAX_PLAY_TIME));
				player.setInvisible(false);
				FakePlayerSkillData.getInstance().updateSkills(player);
			}, Rnd.get(5000, 10000));
			ScheduledFuture<?> future = SpawnTasks.get(objectId);
			if (future != null)
			{
				SpawnTasks.get(objectId).cancel(false);
				SpawnTasks.remove(objectId);
			}
			return player;
		}
		return null;
	}
	
	public PlayerInstance spawnNewFakePlayer(int endClassId)
	{
		// appearance
		final int sex = Rnd.get(2);
		final byte hairStyle = (byte) Rnd.get(sex == 0 ? 5 : 7);
		final byte hairColor = (byte) Rnd.get(4);
		final byte face = (byte) Rnd.get(3);
		//
		int startClassId = ClassId.getClassId(endClassId).getParent().getParent().getParent().getId();
		String name = "";
		int tries = 0;
		while (name.isEmpty())
		{
			tries++;
			if (tries >= 100)
			{
				LOGGER.warning("FakePlayerManager: No valid new name found");
				return null;
			}
			String rndName = FakePlayerNameData.getInstance().getRandomName();
			if (CharNameTable.getInstance().getIdByName(rndName) > 0)
			{
				continue;
			}
			name = rndName;
		}
		String accName = createFakeAccount();
		// GameClient client = new GameClient(null);
		// client.setDetached(true);
		PlayerTemplate template = PlayerTemplateData.getInstance().getTemplate(startClassId);
		PlayerInstance newChar = PlayerInstance.create(template, accName, name, new PlayerAppearance(face, hairColor, hairStyle, sex != 0));
		newChar.setFakePlayer(true);
		newChar.setTitle(Config.NEWBIE_TITLE, true, false);
		newChar.setCurrentCp(newChar.getMaxCp());
		newChar.setCurrentHp(newChar.getMaxHp());
		newChar.setCurrentMp(newChar.getMaxMp());
		/////////////////////////////////////////
		// init //
		/////////////////////////////////////////
		final Location createLoc = template.getCreationPoint();
		newChar.setHeading(Rnd.get(1, 50000));
		// newChar.setXYZInvisible(createLoc.getX(), createLoc.getY(), createLoc.getZ());
		if (newChar.getRace() == Race.ORC)
		{
			newChar.setXYZInvisible(-39543, -118663, -1868);
		}
		else if (newChar.getRace() == Race.HUMAN)
		{
			newChar.setXYZInvisible(-109630, 216257, -3220);
		}
		else if (newChar.getRace() == Race.ELF)
		{
			newChar.setXYZInvisible(25556, 48825, -3566);
		}
		else if (newChar.getRace() == Race.DARK_ELF)
		{
			newChar.setXYZInvisible(-20492, 40179, -3430);
		}
		else
		{
			newChar.setXYZInvisible(createLoc.getX(), createLoc.getY(), createLoc.getZ());
		}
		newChar.setTitle(Config.NEWBIE_TITLE, true, false);
		setAutoShots(newChar);
		// client.setPlayer(newChar);
		newChar.setOnlineStatus(true, false);
		// client.setAccountName(newChar.getAccountNamePlayer());
		// newChar.setClient(client);
		newChar.spawnMe();
		///////////////////////////////////////////////////////////////////////////////
		newChar.setCreateDate(Calendar.getInstance());
		newChar.setBaseClass(newChar.getClassId());
		// create init
		for (SkillLearn skill : SkillTreeData.getInstance().getAvailableSkills(newChar, newChar.getClassId(), false, true))
		{
			newChar.addSkill(SkillData.getInstance().getSkill(skill.getSkillId(), skill.getSkillLevel()), true);
		}
		// EventDispatcher.getInstance().notifyEvent(new OnPlayerCreate(newChar, newChar.getObjectId(), newChar.getName(), client), Containers.Players());
		//
		considerNewGear(newChar, newChar.getLevel(), FakePlayerType.NORMAL);
		newChar.setRunning();
		FakePlayerSkillData.getInstance().updateSkills(newChar);
		_settings.put(newChar.getObjectId(), new FakePlayerSetting(ClassId.getClassId(endClassId), true));
		ThreadPool.get().schedule(() -> newChar.startFakePlayerTask(), Rnd.get(4000, 12000));
		scheduleLogout(newChar, Rnd.get(MIN_PLAY_TIME, MAX_PLAY_TIME));
		return newChar;
	}
	
	public PlayerInstance spawnAfkFakePlayer(PlayerInstance admin, int classId, String grade, FakePlayerType type)
	{
		// appearance
		final int sex = Rnd.get(2);
		final byte hairStyle = (byte) Rnd.get(sex == 0 ? 5 : 7);
		final byte hairColor = (byte) Rnd.get(4);
		final byte face = (byte) Rnd.get(3);
		String name = "";
		int tries = 0;
		while (name.isEmpty())
		{
			tries++;
			if (tries >= 100)
			{
				LOGGER.warning("FakePlayerManager: No valid new name found");
				return null;
			}
			String rndName = (type == FakePlayerType.SHOP) || (type == FakePlayerType.AFK) ? FakePlayerShopNameData.getInstance().getRandomName() : FakePlayerNameData.getInstance().getRandomName();
			if (CharNameTable.getInstance().getIdByName(rndName) > 0)
			{
				continue;
			}
			name = rndName;
		}
		String accName = createFakeAccount();
		// GameClient client = new GameClient(null);
		// client.setDetached(true);
		PlayerTemplate template = PlayerTemplateData.getInstance().getTemplate(classId == -1 ? 53 : classId);
		PlayerInstance newChar = PlayerInstance.create(template, accName, name, new PlayerAppearance(face, hairColor, hairStyle, sex != 0));
		newChar.getStat().setLevel((byte) (type == FakePlayerType.SHOP ? Rnd.get(40, 60) : Rnd.get(40, 72)));
		newChar.setFakePlayer(true);
		newChar.setTitle(Config.NEWBIE_TITLE, true, false);
		newChar.setCurrentCp(newChar.getMaxCp());
		newChar.setCurrentHp(newChar.getMaxHp());
		newChar.setCurrentMp(newChar.getMaxMp());
		/////////////////////////////////////////
		// init //
		/////////////////////////////////////////
		Location loc = admin.getLocation();
		newChar.setHeading(admin.getHeading());
		newChar.setXYZInvisible(loc.getX(), loc.getY(), loc.getZ());
		newChar.setTitle(Config.NEWBIE_TITLE, true, false);
		setAutoShots(newChar);
		// client.setPlayer(newChar);
		newChar.setOnlineStatus(true, false);
		// client.setAccountName(newChar.getAccountNamePlayer());
		// newChar.setClient(client);
		// newChar.spawnMe();
		///////////////////////////////////////////////////////////////////////////////
		newChar.setCreateDate(Calendar.getInstance());
		newChar.setBaseClass(newChar.getClassId());
		// create init
		for (SkillLearn skill : SkillTreeData.getInstance().getAvailableSkills(newChar, newChar.getClassId(), false, true))
		{
			newChar.addSkill(SkillData.getInstance().getSkill(skill.getSkillId(), skill.getSkillLevel()), true);
		}
		// EventDispatcher.getInstance().notifyEvent(new OnPlayerCreate(newChar, newChar.getObjectId(), newChar.getName(), client), Containers.Players());
		//
		String identifier = (classId + grade).toLowerCase();
		int[] dcRobe =
		{
			90472,
			2407,
			5767,
			5779
		};
		int[] mjRobe =
		{
			90478,
			2409,
			5776,
			5778
		};
		int[] maRobe =
		{
			6386,
			6383,
			6384,
			6385
		};
		int[] mjHeavy =
		{
			90476,
			2383,
			5774,
			5786
		};
		int[] icHeavy =
		{
			6378,
			6373,
			6374,
			6375,
			6376
		};
		int[] mjLight =
		{
			90477,
			2395,
			5775,
			5787
		};
		int[] dracLight =
		{
			6382,
			6379,
			6380,
			6381
		};
		int daimon = 8688;
		int imperial = 6366;
		int ipos = 8679;
		int heavens = 6372;
		int sobekk = 8685;
		int splinter = 6371;
		int carnage = 288;
		int dbow = 7575;
		int naga = 8682;
		int aslayer = 6367;
		int epics[] =
		{
			49578,
			90765,
			49577,
			49579,
			924
		};
		switch (identifier)
		{
			// Archmage | Soultaker | Mystic Muse | Storm Screamer | Dominator | Cardinal
			case ("94a"):
			case ("95a"):
			case ("103a"):
			case ("110a"):
			case ("115a"):
			case ("97a"):
			{
				int[] set = Rnd.get(0, 10) < 2 ? mjRobe : dcRobe;
				for (int id : set)
				{
					newChar.getInventory().equipItem(newChar.addItem("ObtFakePlayer", id, 1, newChar, false));
				}
				for (int id : epics)
				{
					newChar.getInventory().equipItem(newChar.addItem("ObtFakePlayer", id, 1, newChar, false));
				}
				newChar.getInventory().equipItem(newChar.addItem("ObtFakePlayer", daimon, 1, newChar, false));
				break;
			}
			case ("94s"):
			case ("95s"):
			case ("103s"):
			case ("110s"):
			case ("115s"):
			case ("97s"):
			{
				for (int id : maRobe)
				{
					newChar.getInventory().equipItem(newChar.addItem("ObtFakePlayer", id, 1, newChar, false));
				}
				for (int id : epics)
				{
					newChar.getInventory().equipItem(newChar.addItem("ObtFakePlayer", id, 1, newChar, false));
				}
				newChar.getInventory().equipItem(newChar.addItem("ObtFakePlayer", imperial, 1, newChar, false));
				break;
			}
			// Titan
			case ("113a"):
			{
				for (int id : mjHeavy)
				{
					newChar.getInventory().equipItem(newChar.addItem("ObtFakePlayer", id, 1, newChar, false));
				}
				for (int id : epics)
				{
					newChar.getInventory().equipItem(newChar.addItem("ObtFakePlayer", id, 1, newChar, false));
				}
				newChar.getInventory().equipItem(newChar.addItem("ObtFakePlayer", ipos, 1, newChar, false));
				break;
			}
			case ("113s"):
			{
				int[] set = Rnd.get(0, 1) == 0 ? icHeavy : dracLight;
				for (int id : set)
				{
					newChar.getInventory().equipItem(newChar.addItem("ObtFakePlayer", id, 1, newChar, false));
				}
				for (int id : epics)
				{
					newChar.getInventory().equipItem(newChar.addItem("ObtFakePlayer", id, 1, newChar, false));
				}
				newChar.getInventory().equipItem(newChar.addItem("ObtFakePlayer", heavens, 1, newChar, false));
				break;
			}
			// Grand Khavatari
			case ("114a"):
			{
				for (int id : mjHeavy)
				{
					newChar.getInventory().equipItem(newChar.addItem("ObtFakePlayer", id, 1, newChar, false));
				}
				for (int id : epics)
				{
					newChar.getInventory().equipItem(newChar.addItem("ObtFakePlayer", id, 1, newChar, false));
				}
				newChar.getInventory().equipItem(newChar.addItem("ObtFakePlayer", sobekk, 1, newChar, false));
				break;
			}
			case ("114s"):
			{
				int[] set = Rnd.get(0, 1) == 0 ? icHeavy : dracLight;
				for (int id : set)
				{
					newChar.getInventory().equipItem(newChar.addItem("ObtFakePlayer", id, 1, newChar, false));
				}
				for (int id : epics)
				{
					newChar.getInventory().equipItem(newChar.addItem("ObtFakePlayer", id, 1, newChar, false));
				}
				newChar.getInventory().equipItem(newChar.addItem("ObtFakePlayer", splinter, 1, newChar, false));
				break;
			}
			// Sagittarius | Moonlight Sentinel | Ghost Sentinel
			case ("92a"):
			case ("102a"):
			case ("109a"):
			{
				for (int id : mjLight)
				{
					newChar.getInventory().equipItem(newChar.addItem("ObtFakePlayer", id, 1, newChar, false));
				}
				for (int id : epics)
				{
					newChar.getInventory().equipItem(newChar.addItem("ObtFakePlayer", id, 1, newChar, false));
				}
				newChar.getInventory().equipItem(newChar.addItem("ObtFakePlayer", carnage, 1, newChar, false));
				break;
			}
			case ("92s"):
			case ("102s"):
			case ("109s"):
			{
				for (int id : dracLight)
				{
					newChar.getInventory().equipItem(newChar.addItem("ObtFakePlayer", id, 1, newChar, false));
				}
				for (int id : epics)
				{
					newChar.getInventory().equipItem(newChar.addItem("ObtFakePlayer", id, 1, newChar, false));
				}
				newChar.getInventory().equipItem(newChar.addItem("ObtFakePlayer", dbow, 1, newChar, false));
				break;
			}
			// Adventurer | Wind Rider | Ghost Hunter
			case ("93a"):
			case ("101a"):
			case ("108a"):
			{
				for (int id : mjLight)
				{
					newChar.getInventory().equipItem(newChar.addItem("ObtFakePlayer", id, 1, newChar, false));
				}
				for (int id : epics)
				{
					newChar.getInventory().equipItem(newChar.addItem("ObtFakePlayer", id, 1, newChar, false));
				}
				newChar.getInventory().equipItem(newChar.addItem("ObtFakePlayer", naga, 1, newChar, false));
				break;
			}
			case ("93s"):
			case ("101s"):
			case ("108s"):
			{
				for (int id : dracLight)
				{
					newChar.getInventory().equipItem(newChar.addItem("ObtFakePlayer", id, 1, newChar, false));
				}
				for (int id : epics)
				{
					newChar.getInventory().equipItem(newChar.addItem("ObtFakePlayer", id, 1, newChar, false));
				}
				newChar.getInventory().equipItem(newChar.addItem("ObtFakePlayer", aslayer, 1, newChar, false));
				break;
			}
			case ("-1"):
			{
				FakePlayerManager.getInstance().considerNewGear(newChar, 1, type);
				break;
			}
		}
		if (type == FakePlayerType.AFK)
		{
			considerNewGear(newChar, 40, type);
		}
		newChar.spawnMe();
		if (type == FakePlayerType.NORMAL)
		{
			newChar.setRunning();
			_settings.put(newChar.getObjectId(), new FakePlayerSetting(ClassId.getClassId(classId), true));
			scheduleLogout(newChar, Rnd.get(MIN_PLAY_TIME, MAX_PLAY_TIME));
		}
		return newChar;
	}
	
	public void spawnExistingObtFakePlayer(PlayerInstance admin)
	{
		List<Integer> available = new ArrayList<>();
		for (Integer objId : _settings.keySet())
		{
			PlayerInstance plr = World.getInstance().getPlayer(objId);
			if (((plr == null) || ((!plr.isOnline()) && !available.contains(objId))))
			{
				available.add(objId);
			}
		}
		if (available.isEmpty())
		{
			admin.sendMessage("No existing fake player found.");
			return;
		}
		int objId = available.get(Rnd.get(available.size()));
		PlayerInstance testPlayer = World.getInstance().getPlayer(available.get(0));
		if ((testPlayer == null) || ((testPlayer.getClient() != null) && testPlayer.getClient().isDetached()))
		{
			final PlayerInstance player = PlayerInstance.load(objId);
			player.setOnlineStatus(true, false);
			player.setInvisible(true);
			// GameClient client = new GameClient(null);
			// client.setDetached(true);
			player.setFakePlayer(true);
			player.setHeading(Rnd.get(1, 50000));
			player.setCurrentCp(player.getMaxCp());
			player.setCurrentHp(player.getMaxHp());
			player.setCurrentMp(player.getMaxMp());
			setAutoShots(player);
			// Location loc = admin.getLocation();
			// player.setXYZInvisible(loc.getX(), loc.getY(), loc.getZ());
			admin.sendMessage("NAME: " + player.getName());
			// client.setPlayer(player);
			// client.setAccountName(player.getAccountNamePlayer());
			// player.setClient(client);
			ThreadPool.get().schedule(() ->
			{
				if (player.isDead())
				{
					player.doRevive();
				}
				if (!player.isSpawned())
				{
					player.spawnMe();
				}
				else
				{
					System.out.println(player.getName() + " is already spawned ???");
				}
				for (SkillLearn skill : SkillTreeData.getInstance().getAvailableSkills(player, player.getClassId(), false, true))
				{
					player.addSkill(SkillData.getInstance().getSkill(skill.getSkillId(), skill.getSkillLevel()), true);
				}
				player.setRunning();
				scheduleLogout(player, Rnd.get(MIN_PLAY_TIME, MAX_PLAY_TIME));
				player.setInvisible(false);
			}, Rnd.get(5000, 10000));
		}
	}
	
	public void createNewShop(PlayerInstance admin, String type, List<Long[]> items, String title)
	{
		List<Integer> firstClassId = Arrays.asList(0, 10, 18, 25, 31, 38, 44, 49, 53);
		Random rand = new Random();
		int nClassId = firstClassId.get(rand.nextInt(firstClassId.size()));
		admin.sendMessage("" + nClassId);
		switch (type)
		{
			case "sell":
			{
				PlayerInstance shop = spawnAfkFakePlayer(admin, Rnd.get(nClassId), "", FakePlayerType.SHOP);
				FakePlayerManager.getInstance().considerNewGear(shop, 1, FakePlayerType.SHOP);
				final TradeList tradeList = shop.getSellList();
				tradeList.clear();
				tradeList.setPackaged(false);
				long totalCost = shop.getAdena();
				List<FakeShopItemHolder> list = new ArrayList<>();
				for (Long[] itemData : items)
				{
					if (itemData.length != 3)
					{
						admin.sendMessage("Incorrect format");
						scheduleLogout(shop, 0);
						return;
					}
					int itemId = itemData[0].intValue();
					long itemCount = itemData[1];
					long itemPrice = itemData[2];
					ItemInstance newItem = shop.addItem("fakeShop", itemId, itemCount, admin, false);
					list.add(new FakeShopItemHolder(newItem.getObjectId(), itemCount, itemPrice));
				}
				for (FakeShopItemHolder i : list)
				{
					if (!i.addToSellList(tradeList))
					{
						Util.handleIllegalPlayerAction(shop, "Warning!! Character " + shop.getName() + " of account " + shop.getAccountName() + " tried to set price more than " + MAX_ADENA + " adena in Private Store - Sell.", Config.DEFAULT_PUNISH);
						return;
					}
					totalCost += i.getPrice();
					if (totalCost > MAX_ADENA)
					{
						Util.handleIllegalPlayerAction(shop, "Warning!! Character " + shop.getName() + " of account " + shop.getAccountName() + " tried to set total price more than " + MAX_ADENA + " adena in Private Store - Sell.", Config.DEFAULT_PUNISH);
						return;
					}
				}
				shop.sitDown();
				shop.setPrivateStoreType(PrivateStoreType.SELL);
				shop.getSellList().setTitle(title);
				shop.broadcastUserInfo();
				shop.broadcastPacket(new PrivateStoreMsgSell(shop));
				OfflineTradeUtil.enteredOfflineMode(shop);
				break;
			}
			case "buy":
			{
				PlayerInstance shop = spawnAfkFakePlayer(admin, Rnd.get(nClassId), "", FakePlayerType.SHOP);
				FakePlayerManager.getInstance().considerNewGear(shop, 1, FakePlayerType.SHOP);
				final TradeList tradeList = shop.getBuyList();
				tradeList.clear();
				long totalCost = shop.getAdena();
				List<FakeShopItemHolder> list = new ArrayList<>();
				for (Long[] itemData : items)
				{
					if (itemData.length != 3)
					{
						admin.sendMessage("Incorrect format");
						scheduleLogout(shop, 0);
						return;
					}
					int itemId = itemData[0].intValue();
					long itemCount = itemData[1];
					long itemPrice = itemData[2];
					shop.addItem("fakeShopAdena", 57, **********, admin, false);
					ItemInstance newItem = shop.addItem("fakeShop", itemId, itemCount, admin, false);
					list.add(new FakeShopItemHolder(newItem.getId(), itemCount, itemPrice));
				}
				for (FakeShopItemHolder i : list)
				{
					if (!i.addToBuyList(tradeList))
					{
						Util.handleIllegalPlayerAction(shop, "Warning!! Character " + shop.getName() + " of account " + shop.getAccountName() + " tried to set price more than " + MAX_ADENA + " adena in Private Store - Buy.", Config.DEFAULT_PUNISH);
						return;
					}
					totalCost += i.getPrice();
					if (totalCost > MAX_ADENA)
					{
						Util.handleIllegalPlayerAction(shop, "Warning!! Character " + shop.getName() + " of account " + shop.getAccountName() + " tried to set total price more than " + MAX_ADENA + " adena in Private Store - Buy.", Config.DEFAULT_PUNISH);
						return;
					}
				}
				shop.sitDown();
				shop.setPrivateStoreType(PrivateStoreType.BUY);
				shop.getBuyList().setTitle(title);
				shop.broadcastUserInfo();
				shop.broadcastPacket(new PrivateStoreMsgBuy(shop));
				OfflineTradeUtil.enteredOfflineMode(shop);
				break;
			}
		}
	}
	
	private String createFakeAccount()
	{
		String login = null;
		try (Connection con = DatabaseFactory.getConnection(); PreparedStatement ps = con.prepareStatement("INSERT INTO accounts (login, password, lastactive, accessLevel, lastIP) values (?, ?, ?, ?, ?)"))
		{
			login = Util.generateRandomString(16);
			String password = Util.generateRandomString(16);
			ps.setString(1, login);
			ps.setString(2, password);
			ps.setLong(3, System.currentTimeMillis());
			ps.setInt(4, 0);
			ps.setString(5, "127.0.0.1");
			ps.execute();
		}
		catch (Exception e)
		{
			LOGGER.log(Level.WARNING, "Exception while auto creating FAKE account '" + login + "'!", e);
			return null;
		}
		return login;
	}
	
	public void scheduleLogout(PlayerInstance player, long minutes)
	{
		ScheduledFuture<?> schedule = logoutTasks.get(player.getObjectId());
		if (schedule != null)
		{
			schedule.cancel(false);
			schedule = null;
		}
		logoutTasks.put(player.getObjectId(), ThreadPool.get().schedule(() ->
		{
			int logoutDelay = player.isInsideZone(ZoneId.PEACE) ? 0 : TELEPORT.getSkill().getHitTime() + 500 + Rnd.get(3000, 8000) + (AttackStanceTaskManager.getInstance().hasAttackStanceTask(player) ? 15000 : 0);
			if (!player.isOnline() || !player.isFakePlayer())
			{
				return;
			}
			player.stopFakePlayerTask();
			AutoPlayTaskManager.getInstance().stopAutoPlay(player);
			player.abortAttack();
			player.abortCast();
			if (!player.isDead() && !player.isInsideZone(ZoneId.PEACE) && (player.getLevel() > 6))
			{
				// TODO: town with location teleport
				// ThreadPool.get().schedule(() -> teleport(player, FakePlayerTownData.getInstance().getTownForLogout().getId(), true), 500);
				// TEMP:
				Disconnection.of(player.getClient(), player).logout(false, false);
			}
			ThreadPool.get().schedule(() ->
			{
				Disconnection.of(player.getClient(), player).logout(false, false);
			}, logoutDelay);
		}, minutes * 60 * 1000));
		player.getFakePlayerInfo().LOGOUT_TIME = System.currentTimeMillis() + (minutes * 60 * 1000);
	}
	
	public FakePlayerSetting getSetting(int objectId)
	{
		return _settings.get(objectId);
	}
	
	public Map<Integer, FakePlayerSetting> getAllSettings()
	{
		return _settings;
	}
	
	public void setAutoShots(PlayerInstance player)
	{
		player.disableAutoShotsAll();
		player.addAutoSoulShot(91927);
		player.addAutoSoulShot(91930);
	}
	
	public void considerNewDyes(PlayerInstance player, int level)
	{
		for (int i = level; i > 0; i--)
		{
			final List<Integer> symbolIds = FakePlayerHennaData.getInstance().getHennaList(player.getClassId(), level);
			if (symbolIds != null)
			{
				for (int j = 1; j <= 3; j++)
				{
					player.removeHenna(j, true, false);
				}
				int slot = 0;
				for (int symbolId : symbolIds)
				{
					final Henna henna = HennaData.getInstance().getHennaByDyeId(symbolId);
					if (henna == null)
					{
						LOGGER.warning("Invalid henna: " + symbolId + " for fake player: " + player.getName());
						continue;
					}
					slot++;
					player.addHenna(slot, henna);
				}
				return;
			}
		}
	}
	
	public void considerNewGear(PlayerInstance player, int level, FakePlayerType type)
	{
		final List<PlayerItemTemplate> items = FakePlayerItemData.getInstance().getEquipmentList(player.getClassId(), level);
		if (items != null)
		{
			for (PlayerItemTemplate it : items)
			{
				final ItemInstance item = player.addItem("ItemForFakePlayer", it.getId(), it.getCount(), player, false);
				if (item == null)
				{
					LOGGER.warning("Could not create item for fake player: (" + it.getId() + ", " + it.getCount() + ") + Name: " + player.getName());
					continue;
				}
				if ((item.getItem().getBodyPart() == Inventory.PAPERDOLL_LHAND) || (item.getItem().getBodyPart() == Inventory.PAPERDOLL_REAR) || (item.getItem().getBodyPart() == Inventory.PAPERDOLL_LFINGER) || (item.getItem().getBodyPart() == Inventory.PAPERDOLL_RFINGER))
				{
					player.getInventory().unEquipItemInSlot(Inventory.PAPERDOLL_LEAR);
					player.getInventory().unEquipItemInSlot(Inventory.PAPERDOLL_REAR);
					player.getInventory().unEquipItemInSlot(Inventory.PAPERDOLL_LFINGER);
					player.getInventory().unEquipItemInSlot(Inventory.PAPERDOLL_RFINGER);
					break;
				}
			}
			for (PlayerItemTemplate it : items)
			{
				final ItemInstance item = player.addItem("ItemForFakePlayer", it.getId(), it.getCount(), player, false);
				if (item == null)
				{
					LOGGER.warning("Could not create item for fake player: (" + it.getId() + ", " + it.getCount() + ") + Name: " + player.getName());
					continue;
				}
				if (it.getEnchant() > 0)
				{
					item.setEnchantLevel(it.getEnchant());
				}
				if (item.isEquipable() && it.isEquipped())
				{
					player.getInventory().equipItem(item);
				}
			}
			player.broadcastCharInfo();
		}
		final List<PlayerItemTemplate> commonItems = FakePlayerItemData.getInstance().getCommonEquipmentList(level);
		if (commonItems != null)
		{
			for (PlayerItemTemplate it : commonItems)
			{
				final ItemInstance item = player.addItem("ItemForFakePlayer", it.getId(), it.getCount(), player, false);
				if (item == null)
				{
					LOGGER.warning("Could not create item for fake player: (" + it.getId() + ", " + it.getCount() + ") + Name: " + player.getName());
					continue;
				}
				if (it.getEnchant() > 0)
				{
					item.setEnchantLevel(it.getEnchant());
				}
				if (item.isEquipable() && it.isEquipped())
				{
					player.getInventory().equipItem(item);
				}
			}
		}
		// considerNewDyes(player, level);
	}
	
	private void changeClass(PlayerInstance player, int newClassId)
	{
		player.setClassId(newClassId);
		player.setBaseClass(player.getActiveClass());
		if (player.getClassId().level() == 3)
		{
			player.initElementalSpirits();
			player.sendPacket(new ElementalSpiritInfo(player, (byte) 1, (byte) 0));
		}
		if (Config.AUTO_LEARN_SKILLS)
		{
			player.giveAvailableSkills(Config.AUTO_LEARN_FS_SKILLS, true);
		}
		player.store(false);
		player.broadcastCharInfo();
	}
	
	public void teleport(PlayerInstance player, Location loc)
	{
		teleport(player, loc, false);
	}
	
	public void teleport(PlayerInstance player, Location loc, boolean ignoreCasting)
	{
		if ((player.isTeleporting() || player.isCastingNow()) && !ignoreCasting)
		{
			return;
		}
		if ((player.getFakePlayerInfo().LAST_TELEPORT_TIME + (TELEPORT.getSkill().getHitTime() * 1.2)) > System.currentTimeMillis())
		{
			return;
		}
		if ((player.isInCombat() || (player.getPvpFlag() != 0)) && !player.isInsidePeaceZone(player))
		{
			return;
		}
		if (player.isMoving())
		{
			player.stopMove(player.getLocation());
		}
		// AutoPlayTaskManager.getInstance().stopAutoPlay(player);
		player.setTeleportLoc(loc);
		player.doCast(TELEPORT.getSkill());
		player.getFakePlayerInfo().LAST_TELEPORT_TIME = System.currentTimeMillis();
	}
	
	public boolean isMage(PlayerInstance player)
	{
		switch (player.getClassId().getId())
		{
			case 10:
			case 11:
			case 12:
			case 13:
			case 14:
			case 15:
			case 16:
			case 17:
			case 25:
			case 26:
			case 27:
			case 28:
			case 29:
			case 30:
			case 38:
			case 39:
			case 40:
			case 41:
			case 42:
			case 43:
			case 49:
			case 50:
			case 51:
			case 52:
			case 94:
			case 95:
			case 96:
			case 97:
			case 98:
			case 103:
			case 104:
			case 105:
			case 110:
			case 111:
			case 112:
			case 115:
			case 116:
			{
				return true;
			}
			default:
				return false;
		}
	}
	
	public void init()
	{
		INIT_STARTED = !INIT_STARTED;
		if (!INIT_STARTED)
		{
			LOGGER.info("FakePlayerManager: init paused");
			return;
		}
		_initBotCount = 0;
		// 500
		/*
		 * _classes.put(92, new SpawnInfo(0, 30)); // Sagittarius _classes.put(88, new SpawnInfo(0, 20)); // Duelist _classes.put(95, new SpawnInfo(1, 120)); // Soultaker _classes.put(94, new SpawnInfo(1, 30)); // Archmage _classes.put(103, new SpawnInfo(2, 30)); // Mystic Muse _classes.put(102, new
		 * SpawnInfo(2, 30)); // Moonlight Sentinel _classes.put(110, new SpawnInfo(3, 120)); // Storm Screamer _classes.put(109, new SpawnInfo(3, 30)); // Ghost Sentinel _classes.put(113, new SpawnInfo(4, 20)); // Titan _classes.put(114, new SpawnInfo(4, 20)); // Grand Khavatari _classes.put(115,
		 * new SpawnInfo(4, 50)); // Dominator
		 */
		// 300
		_classes.put(92, new SpawnInfo(0, 15)); // Sagittarius
		_classes.put(88, new SpawnInfo(0, 20)); // Duelist
		_classes.put(95, new SpawnInfo(1, 70)); // Soultaker
		_classes.put(94, new SpawnInfo(1, 15)); // Archmage
		_classes.put(103, new SpawnInfo(2, 15)); // Mystic Muse
		_classes.put(102, new SpawnInfo(2, 15)); // Moonlight Sentinel
		_classes.put(110, new SpawnInfo(3, 70)); // Storm Screamer
		_classes.put(109, new SpawnInfo(3, 15)); // Ghost Sentinel
		_classes.put(113, new SpawnInfo(4, 15)); // Titan
		_classes.put(114, new SpawnInfo(4, 15)); // Grand Khavatari
		_classes.put(115, new SpawnInfo(4, 35)); // Dominator
		for (SpawnInfo spawnInfo : _classes.values())
		{
			_initBotCount += spawnInfo.MAX_COUNT;
		}
		LOGGER.info("FakePlayerManager: started spawning bots. Count: " + _initBotCount);
		ThreadPool.get().schedule(() -> initSpawnWave(), 1000);
	}
	
	private void initSpawnWave()
	{
		int spawnedBots = (int) _settings.values().stream().filter(s -> s.getSpawnAfterRestart()).count();
		if (!INIT_STARTED)
		{
			return;
		}
		if (spawnedBots >= _initBotCount)
		{
			LOGGER.info("FakePlayerManager: init finished");
			return;
		}
		int waveSize = spawnedBots == 0 ? 15 : ((100.0 * spawnedBots) / _initBotCount) <= 60 ? 3 : 1;
		for (int groupId = 0; groupId <= 4; groupId++)
		{
			for (int i = 0, count = 0; (i < waveSize) && (count < waveSize); i++)
			{
				for (Entry<Integer, SpawnInfo> entry : _classes.entrySet())
				{
					if (count >= waveSize)
					{
						break;
					}
					SpawnInfo spawnInfo = entry.getValue();
					if (spawnInfo.GROUP_ID == groupId)
					{
						int classId = entry.getKey();
						int spawnedCount = (int) _settings.values().stream().filter(s -> (s.getEndClassId().getId() == classId) && s.getSpawnAfterRestart()).count();
						if (spawnedCount < spawnInfo.MAX_COUNT)
						{
							ThreadPool.get().schedule(() -> spawnNewFakePlayer(classId), Rnd.get(0, 30000));
							count++;
						}
					}
				}
			}
		}
		ThreadPool.get().schedule(() -> storeFakePlayers(), 31000);
		ThreadPool.get().schedule(() -> initSpawnWave(), 60000);
	}
	
	private static class SingletonHolder
	{
		protected static final FakePlayerManager INSTANCE = new FakePlayerManager();
	}
	
	protected class SpawnInfo
	{
		protected int	GROUP_ID;
		protected int	MAX_COUNT;
		
		protected SpawnInfo(int group_id, int max_count)
		{
			GROUP_ID = group_id;
			MAX_COUNT = max_count;
		}
	}
}