/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.loginserver;

import club.projectessence.Config;

import java.io.IOException;
import java.net.Socket;
import java.util.Collection;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 */
public class GameServerListener extends FloodProtectedListener {
	private static Collection<GameServerThread> _gameServers = ConcurrentHashMap.newKeySet();

	public GameServerListener() throws IOException {
		super(Config.GAME_SERVER_LOGIN_HOST, Config.GAME_SERVER_LOGIN_PORT);
		setName(getClass().getSimpleName());
	}

	@Override
	public void addClient(Socket s) {
		_gameServers.add(new GameServerThread(s));
	}

	public void removeGameServer(GameServerThread gst) {
		_gameServers.remove(gst);
	}
}
