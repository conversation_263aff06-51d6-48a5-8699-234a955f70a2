/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.loginserver;

import club.projectessence.Config;
import club.projectessence.commons.database.DatabaseBackup;
import club.projectessence.commons.database.DatabaseFactory;
import club.projectessence.commons.enums.ServerMode;
import club.projectessence.gameserver.network.loginserverpackets.game.ServerStatus;
import club.projectessence.loginserver.network.ClientNetworkManager;

import java.awt.*;
import java.io.*;
import java.net.UnknownHostException;
import java.security.GeneralSecurityException;
import java.util.logging.Level;
import java.util.logging.LogManager;
import java.util.logging.Logger;

/**
 * <AUTHOR>
 */
public class LoginServer {
	public static final int PROTOCOL_REV = 0x0106;
	private static LoginServer INSTANCE;
	private static int _loginStatus = ServerStatus.STATUS_NORMAL;
	public Logger LOGGER = Logger.getLogger(LoginServer.class.getName());
	private GameServerListener _gameServerListener;
	private Thread _restartLoginServer;

	private LoginServer() throws Exception {
		// GUI
		if (!GraphicsEnvironment.isHeadless()) {
			// System.out.println("LoginServer: Running in GUI mode.");
			// new Gui();
		}

		// Create log folder
		final File logFolder = new File(".", "log");
		logFolder.mkdir();

		// Create input stream for log file -- or store file data into memory

		try (InputStream is = new FileInputStream(new File("./log.cfg"))) {
			LogManager.getLogManager().readConfiguration(is);
		} catch (IOException e) {
			LOGGER.warning(getClass().getSimpleName() + ": " + e.getMessage());
		}

		// Load Config
		Config.load(ServerMode.LOGIN);

		// Prepare Database
		DatabaseFactory.init();

		try {
			LoginController.load();
		} catch (GeneralSecurityException e) {
			LOGGER.log(Level.SEVERE, "FATAL: Failed initializing LoginController. Reason: " + e.getMessage(), e);
			System.exit(1);
		}

		GameServerTable.getInstance();

		loadBanFile();

		if (Config.LOGIN_SERVER_SCHEDULE_RESTART) {
			LOGGER.info("Scheduled LS restart after " + Config.LOGIN_SERVER_SCHEDULE_RESTART_TIME + " hours");
			_restartLoginServer = new LoginServerRestart();
			_restartLoginServer.setDaemon(true);
			_restartLoginServer.start();
		}

		try {
			_gameServerListener = new GameServerListener();
			_gameServerListener.start();
			LOGGER.info("Listening for GameServers on " + Config.GAME_SERVER_LOGIN_HOST + ":" + Config.GAME_SERVER_LOGIN_PORT);
		} catch (IOException e) {
			LOGGER.log(Level.SEVERE, "FATAL: Failed to start the Game Server Listener. Reason: " + e.getMessage(), e);
			System.exit(1);
		}

		ClientNetworkManager.getInstance().start();
	}

	public static void main(String[] args) throws Exception {
		INSTANCE = new LoginServer();
	}

	public static LoginServer getInstance() {
		return INSTANCE;
	}

	public GameServerListener getGameServerListener() {
		return _gameServerListener;
	}

	public void loadBanFile() {
		final File bannedFile = new File("./banned_ip.cfg");
		if (bannedFile.exists() && bannedFile.isFile()) {
			try (FileInputStream fis = new FileInputStream(bannedFile);
			     InputStreamReader is = new InputStreamReader(fis);
			     LineNumberReader lnr = new LineNumberReader(is)) {
				//@formatter:off
				lnr.lines()
						.map(String::trim)
						.filter(l -> !l.isEmpty() && (l.charAt(0) != '#'))
						.forEach(lineValue ->
						{
							String line = lineValue;
							String[] parts = line.split("#", 2); // address[ duration][ # comments]
							line = parts[0];
							parts = line.split("\\s+"); // durations might be aligned via multiple spaces
							final String address = parts[0];
							long duration = 0;
							if (parts.length > 1) {
								try {
									duration = Long.parseLong(parts[1]);
								} catch (NumberFormatException nfe) {
									LOGGER.warning("Skipped: Incorrect ban duration (" + parts[1] + ") on (" + bannedFile.getName() + "). Line: " + lnr.getLineNumber());
									return;
								}
							}

							try {
								LoginController.getInstance().addBanForAddress(address, duration);
							} catch (UnknownHostException e) {
								LOGGER.warning("Skipped: Invalid address (" + address + ") on (" + bannedFile.getName() + "). Line: " + lnr.getLineNumber());
							}
						});
				//@formatter:on
			} catch (IOException e) {
				LOGGER.log(Level.WARNING, "Error while reading the bans file (" + bannedFile.getName() + "). Details: " + e.getMessage(), e);
			}
			LOGGER.info("Loaded " + LoginController.getInstance().getBannedIps().size() + " IP Bans.");
		} else {
			LOGGER.warning("IP Bans file (" + bannedFile.getName() + ") is missing or is a directory, skipped.");
		}
	}

	public void shutdown(boolean restart) {
		if (Config.BACKUP_DATABASE) {
			DatabaseBackup.performBackup();
		}
		Runtime.getRuntime().exit(restart ? 2 : 0);
	}

	public int getStatus() {
		return _loginStatus;
	}

	public void setStatus(int status) {
		_loginStatus = status;
	}

	class LoginServerRestart extends Thread {
		public LoginServerRestart() {
			setName("LoginServerRestart");
		}

		@Override
		public void run() {
			while (!isInterrupted()) {
				try {
					Thread.sleep(Config.LOGIN_SERVER_SCHEDULE_RESTART_TIME * 3600000);
				} catch (InterruptedException e) {
					return;
				}
				shutdown(true);
			}
		}
	}
}
