/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.loginserver.network.clientpackets;

import club.projectessence.Config;
import club.projectessence.commons.network.IIncomingPacket;
import club.projectessence.commons.network.PacketReader;
import club.projectessence.loginserver.LoginController;
import club.projectessence.loginserver.LoginServer;
import club.projectessence.loginserver.SessionKey;
import club.projectessence.loginserver.network.LoginClient;
import club.projectessence.loginserver.network.gameserverpackets.ServerStatus;
import club.projectessence.loginserver.network.serverpackets.LoginFail.LoginFailReason;
import club.projectessence.loginserver.network.serverpackets.PlayFail.PlayFailReason;
import club.projectessence.loginserver.network.serverpackets.PlayOk;

/**
 * <pre>
 * Format is ddc
 * d: first part of session id
 * d: second part of session id
 * c: server ID
 * </pre>
 */
public class RequestServerLogin implements IIncomingPacket<LoginClient> {
	private int _skey1;
	private int _skey2;
	private int _serverId;

	@Override
	public boolean read(LoginClient client, PacketReader packet) {
		if (packet.getReadableBytes() >= 9) {
			_skey1 = packet.readD();
			_skey2 = packet.readD();
			_serverId = packet.readC();
			return true;
		}
		return false;
	}

	@Override
	public void run(LoginClient client) {
		final SessionKey sk = client.getSessionKey();

		// if we didnt showed the license we cant check these values
		if (!Config.SHOW_LICENCE || sk.checkLoginPair(_skey1, _skey2)) {
			if ((LoginServer.getInstance().getStatus() == ServerStatus.STATUS_DOWN) || ((LoginServer.getInstance().getStatus() == ServerStatus.STATUS_GM_ONLY) && (client.getAccessLevel() < 1))) {
				client.close(LoginFailReason.REASON_ACCESS_FAILED);
			} else if (LoginController.getInstance().isLoginPossible(client, _serverId)) {
				client.setJoinedGS(true);
				client.sendPacket(new PlayOk(sk));
			} else {
				client.close(PlayFailReason.REASON_SERVER_OVERLOADED);
			}
		} else {
			client.close(LoginFailReason.REASON_ACCESS_FAILED);
		}
	}
}
