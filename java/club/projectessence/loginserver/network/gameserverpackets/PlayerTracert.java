/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.loginserver.network.gameserverpackets;

import club.projectessence.commons.network.BaseRecievePacket;
import club.projectessence.loginserver.LoginController;

import java.util.logging.Logger;

/**
 * <AUTHOR>
 */
public class PlayerTracert extends BaseRecievePacket {
	protected static final Logger LOGGER = Logger.getLogger(PlayerTracert.class.getName());

	/**
	 * @param decrypt
	 */
	public PlayerTracert(byte[] decrypt) {
		super(decrypt);
		final String account = readS();
		final String pcIp = readS();
		final String hop1 = readS();
		final String hop2 = readS();
		final String hop3 = readS();
		final String hop4 = readS();
		LoginController.getInstance().setAccountLastTracert(account, pcIp, hop1, hop2, hop3, hop4);
	}
}
