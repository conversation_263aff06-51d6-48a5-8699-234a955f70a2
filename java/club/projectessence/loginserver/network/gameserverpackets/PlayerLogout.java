/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.loginserver.network.gameserverpackets;

import club.projectessence.commons.network.BaseRecievePacket;
import club.projectessence.loginserver.GameServerThread;

import java.util.logging.Logger;

/**
 * <AUTHOR>
 */
public class PlayerLogout extends BaseRecievePacket {
	protected static final Logger LOGGER = Logger.getLogger(PlayerLogout.class.getName());

	/**
	 * @param decrypt
	 * @param server
	 */
	public PlayerLogout(byte[] decrypt, GameServerThread server) {
		super(decrypt);
		final String account = readS();
		server.removeAccountOnGameServer(account);
	}
}
