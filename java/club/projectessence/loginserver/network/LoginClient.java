/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.loginserver.network;

import club.projectessence.commons.network.ChannelInboundHandler;
import club.projectessence.commons.network.IIncomingPacket;
import club.projectessence.commons.network.IOutgoingPacket;
import club.projectessence.commons.network.codecs.ClientPacketError;
import club.projectessence.commons.util.Rnd;
import club.projectessence.commons.util.crypt.ScrambledKeyPair;
import club.projectessence.loginserver.LoginController;
import club.projectessence.loginserver.SessionKey;
import club.projectessence.loginserver.network.serverpackets.Init;
import club.projectessence.loginserver.network.serverpackets.LoginFail;
import club.projectessence.loginserver.network.serverpackets.LoginFail.LoginFailReason;
import club.projectessence.loginserver.network.serverpackets.PlayFail;
import club.projectessence.loginserver.network.serverpackets.PlayFail.PlayFailReason;
import io.netty.channel.Channel;
import io.netty.channel.ChannelHandlerContext;

import javax.crypto.SecretKey;
import java.net.InetAddress;
import java.net.InetSocketAddress;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.logging.Logger;

/**
 * Represents a client connected into the LoginServer
 *
 * <AUTHOR>
 */
public class LoginClient extends ChannelInboundHandler<LoginClient> {
	public static final int ALLOWED_PACKET_COUNT = 3;
	public static final int PACKET_TIMEOUT = 500;
	private static final Logger LOGGER = Logger.getLogger(LoginClient.class.getName());
	private static final int MAX_SPAMED_PACKETS = 10;
	private static final int MAX_PACKETS = 100;
	// Crypt
	private final ScrambledKeyPair _scrambledPair;
	private final SecretKey _blowfishKey;
	private final Map<Integer, long[]> _packetsTimes = new HashMap<>();
	private final long _lastScheduleReset = System.currentTimeMillis();
	private InetAddress _addr;
	private Channel _channel;
	private String _account;
	private int _accessLevel;
	private int _lastServer;
	private SessionKey _sessionKey;
	private int _sessionId;
	private boolean _joinedGS;
	private Map<Integer, Integer> _charsOnServers;
	private Map<Integer, long[]> _charsToDelete;
	private long _connectionStartTime;
	private int _spammedPackets = 0;
	private int _packetsCount = 0;
	private long _lastPacketError = 0;
	private boolean _canSendPackets = true;

	public LoginClient(SecretKey blowfishKey) {
		super();
		_blowfishKey = blowfishKey;
		_scrambledPair = LoginController.getInstance().getScrambledRSAKeyPair();
	}

	@Override
	public void channelActive(ChannelHandlerContext ctx) {
		super.channelActive(ctx);

		setConnectionState(ConnectionState.CONNECTED);
		final InetSocketAddress address = (InetSocketAddress) ctx.channel().remoteAddress();
		_addr = address.getAddress();
		_channel = ctx.channel();
		_sessionId = Rnd.nextInt();
		_connectionStartTime = System.currentTimeMillis();
		sendPacket(new Init(_scrambledPair.getScrambledModulus(), _blowfishKey.getEncoded(), _sessionId));
	}

	@Override
	public void channelInactive(ChannelHandlerContext ctx) {
		if (!_joinedGS || ((_connectionStartTime + LoginController.LOGIN_TIMEOUT) < System.currentTimeMillis())) {
			LoginController.getInstance().removeAuthedLoginClient(getAccount());
		}
	}

	@Override
	protected void channelRead0(ChannelHandlerContext ctx, IIncomingPacket<LoginClient> packet) {
		try {
			packet.run(this);
		} catch (Exception e) {
			LOGGER.warning(getClass().getSimpleName() + ": " + e.getMessage());
		}
	}

	@Override
	public void exceptionCaught(ChannelHandlerContext ctx, Throwable cause) {
	}

	public InetAddress getConnectionAddress() {
		return _addr;
	}

	public String getAccount() {
		return _account;
	}

	public void setAccount(String account) {
		_account = account;
	}

	public int getAccessLevel() {
		return _accessLevel;
	}

	public void setAccessLevel(int accessLevel) {
		_accessLevel = accessLevel;
	}

	public int getLastServer() {
		return _lastServer;
	}

	public void setLastServer(int lastServer) {
		_lastServer = lastServer;
	}

	public int getSessionId() {
		return _sessionId;
	}

	public ScrambledKeyPair getScrambledKeyPair() {
		return _scrambledPair;
	}

	public boolean hasJoinedGS() {
		return _joinedGS;
	}

	public void setJoinedGS(boolean value) {
		_joinedGS = value;
	}

	public SessionKey getSessionKey() {
		return _sessionKey;
	}

	public void setSessionKey(SessionKey sessionKey) {
		_sessionKey = sessionKey;
	}

	public long getConnectionStartTime() {
		return _connectionStartTime;
	}

	public void sendPacket(IOutgoingPacket packet) {
		if ((packet == null)) {
			return;
		}

		// Write into the channel.
		_channel.writeAndFlush(packet);
	}

	public void close(LoginFailReason reason) {
		close(new LoginFail(reason));
	}

	public void close(PlayFailReason reason) {
		close(new PlayFail(reason));
	}

	public void close(IOutgoingPacket packet) {
		sendPacket(packet);
		closeNow();
	}

	public void closeNow() {
		if (_channel != null) {
			_channel.close();
		}
	}

	public void setCharsOnServ(int servId, int chars) {
		if (_charsOnServers == null) {
			_charsOnServers = new HashMap<>();
		}
		_charsOnServers.put(servId, chars);
	}

	public Map<Integer, Integer> getCharsOnServ() {
		return _charsOnServers;
	}

	public void serCharsWaitingDelOnServ(int servId, long[] charsToDel) {
		if (_charsToDelete == null) {
			_charsToDelete = new HashMap<>();
		}
		_charsToDelete.put(servId, charsToDel);
	}

	public Map<Integer, long[]> getCharsWaitingDelOnServ() {
		return _charsToDelete;
	}

	public boolean canUsePacket(int packetId, int extendedId) {
		if (!_canSendPackets) {
			return false;
		}
		_packetsCount++;
		if (_packetsCount > MAX_PACKETS) {
			onPacketError(ClientPacketError.TOO_MANY_SPAMMED, packetId);
		}
		long currTime = System.currentTimeMillis();
		long[] ta = null;
		ta = _packetsTimes.get(packetId + extendedId);
		if (ta == null) {
			ta = new long[ALLOWED_PACKET_COUNT];
			Arrays.fill(ta, 0);
		}
		int timeout = packetId == 0x0F ? 60 // MoveBackwardToLocation (for WASD movement)
				: packetId == 0x3D ? 3000 // RequestShortcutReg
				: packetId == 0x3F ? 3000 // RequestShortcutDel
				: packetId == 0x97 ? 10000 // SetPrivateStoreMsgSell Exploit
				: packetId == 0x6E ? 60000 // RequestRecordInfo Exploit
				: (packetId == 0xD0) && (extendedId == 0x167) ? 10000 // ExRequestTeleport (Avoid Info packets spam)
				: (packetId == 0xD0) && (extendedId == 0x11D) ? 5000 // RequestTodoList
				: (packetId == 0xD0) && (extendedId == 0x11F) ? 1000 // RequestOneDayRewardReceive
				: packetId == 0x3A ? 10000 // ExRequestTeleport (Avoid Info packets spam)
				: PACKET_TIMEOUT;
		for (int i = 0; i < ALLOWED_PACKET_COUNT; i++) {
			if ((ta[i] + timeout) < currTime) {
				ta[i] = currTime;
				_packetsTimes.put(packetId + extendedId, ta);
				return true;
			}
		}
		_spammedPackets++;
		if (_spammedPackets > MAX_SPAMED_PACKETS) {
			onPacketError(ClientPacketError.TOO_MANY_SPAMMED_SAME, packetId);
		}
		return false;
	}

	public void onPacketError(ClientPacketError errorCode, int packetId) {
		_canSendPackets = false;
		String err = errorCode.getMessage();
		if ((errorCode == ClientPacketError.TOO_MANY_SPAMMED) || (errorCode == ClientPacketError.TOO_MANY_SPAMMED_SAME)) {
			err += " (in " + ((System.currentTimeMillis() - _lastScheduleReset) / 1000.0) + " s)";
		}
		if ((_lastPacketError + 5000) < System.currentTimeMillis()) {
			_lastPacketError = System.currentTimeMillis();
			if (getConnectionAddress() != null) {
				if (getConnectionAddress().getHostAddress() != null) {
					LOGGER.warning(err + ": [Account: " + _account + "] [Last Packet: " + String.format("0x%02X", packetId) + "] [IP: " + getConnectionAddress().getHostAddress() + "]");
				} else {
					LOGGER.warning(err + ": [Account: " + _account + "] [Last Packet: " + String.format("0x%02X", packetId) + "] [IP: " + getConnectionAddress() + "]");
				}
			} else {
				LOGGER.warning(err + ": [Account: " + _account + "] [Last Packet: " + String.format("0x%02X", packetId) + "] [IP: UNKNOWN]");
			}
		}
		closeNow();
	}
}
