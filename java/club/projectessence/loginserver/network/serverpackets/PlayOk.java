/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.loginserver.network.serverpackets;

import club.projectessence.commons.network.IOutgoingPacket;
import club.projectessence.commons.network.PacketWriter;
import club.projectessence.loginserver.SessionKey;
import club.projectessence.loginserver.network.OutgoingPackets;

public class PlayOk implements IOutgoingPacket {
	private final int _playOk1;
	private final int _playOk2;

	public PlayOk(SessionKey sessionKey) {
		_playOk1 = sessionKey.playOkID1;
		_playOk2 = sessionKey.playOkID2;
	}

	@Override
	public boolean write(PacketWriter packet) {
		OutgoingPackets.PLAY_OK.writeId(packet);
		packet.writeD(_playOk1);
		packet.writeD(_playOk2);
		return true;
	}
}
