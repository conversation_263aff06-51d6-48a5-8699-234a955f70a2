-- =====================================================
-- WIPE ALL DATA EXCEPT SYSTEM/DEFAULT DATA
-- Ngày tạo: 2025-01-06
-- Mô tả: Xóa toàn bộ dữ liệu player nhưng GIỮ LẠI data mặc định hệ thống
-- =====================================================

-- ⚠️  CẢNH BÁO: SCRIPT NÀY SẼ XÓA TẤT CẢ DỮ LIỆU PLAYER!
-- ✅  NHƯNG GIỮ LẠI: Castle, Artifact, System tables

-- Tắt foreign key checks và safe updates
SET FOREIGN_KEY_CHECKS = 0;
SET SQL_SAFE_UPDATES = 0;

-- =====================================================
-- 1. MANDATORY BACKUP CHECK
-- =====================================================
-- PHẢI chạy lệnh backup này trước:
-- mysqldump -u root -p gve_db_388 > gve_db_388_FULL_backup_20250106.sql

SELECT 'WARNING: This will delete ALL PLAYER DATA!' as warning;
SELECT 'System data (castles, artifacts) will be preserved' as preserved;
SELECT 'Make sure you have created a backup!' as backup_reminder;

-- =====================================================
-- 2. WIPE PLAYER & ACCOUNT DATA
-- =====================================================

-- Xóa tài khoản và dữ liệu liên quan
TRUNCATE TABLE `accounts`;
TRUNCATE TABLE `account_data`;
TRUNCATE TABLE `account_gsdata`;
TRUNCATE TABLE `accounts_ipauth`;

-- Xóa nhân vật và dữ liệu liên quan
TRUNCATE TABLE `characters`;
TRUNCATE TABLE `character_offline_trade_items`;
TRUNCATE TABLE `character_premium_items`;
TRUNCATE TABLE `character_variables`;

-- =====================================================
-- 3. WIPE ITEMS & INVENTORY
-- =====================================================

-- Xóa tất cả items
TRUNCATE TABLE `items`;
TRUNCATE TABLE `items_transactions_history`;
TRUNCATE TABLE `itemsonground`;

-- =====================================================
-- 4. WIPE CLAN SYSTEM
-- =====================================================

-- Xóa clan data
TRUNCATE TABLE `clan_data`;
TRUNCATE TABLE `clan_notices`;
TRUNCATE TABLE `clan_privs`;
TRUNCATE TABLE `clan_skills`;
TRUNCATE TABLE `clan_subpledges`;
TRUNCATE TABLE `clan_variables`;
TRUNCATE TABLE `clan_wars`;

-- Xóa clan DKP system
TRUNCATE TABLE `clan_dkp_auction_history`;
TRUNCATE TABLE `clan_dkp_auction_settings_presets`;
TRUNCATE TABLE `clan_dkp_data`;
TRUNCATE TABLE `clan_dkp_dynamic_stars_chars`;
TRUNCATE TABLE `clan_dkp_dynamic_stars_classes`;
TRUNCATE TABLE `clan_dkp_dynamic_stars_collections`;
TRUNCATE TABLE `clan_dkp_dynamic_stars_items`;
TRUNCATE TABLE `clan_dkp_dynamic_stars_levels`;
TRUNCATE TABLE `clan_dkp_dynamic_stars_skills`;
TRUNCATE TABLE `clan_dkp_item_price`;
TRUNCATE TABLE `clan_dkp_player_data`;
TRUNCATE TABLE `clan_dkp_player_history`;
TRUNCATE TABLE `clan_dkp_shop_history`;

-- =====================================================
-- 5. WIPE SIEGE & FORT DATA (PLAYER RELATED)
-- =====================================================

-- Xóa dữ liệu siege của players
TRUNCATE TABLE `siege_clans`;
TRUNCATE TABLE `fortsiege_clans`;

-- Xóa đấu giá clan hall
TRUNCATE TABLE `clanhall_auctions_bidders`;

-- =====================================================
-- 6. WIPE TRADING & COMMERCE
-- =====================================================

-- Xóa giao dịch
TRUNCATE TABLE `commission_items`;
TRUNCATE TABLE `world_exchange_items`;

-- =====================================================
-- 7. WIPE EVENT & ACTIVITY DATA
-- =====================================================

-- Xóa dữ liệu event
TRUNCATE TABLE `gabriel_event_playervalue_items`;

-- Xóa bảng xếp hạng
TRUNCATE TABLE `rankings_clan`;

-- =====================================================
-- 8. WIPE PLAYER SPECIFIC DATA
-- =====================================================

-- Xóa dữ liệu player cụ thể
TRUNCATE TABLE `steady_box`;
TRUNCATE TABLE `toggled_shortcuts`;
TRUNCATE TABLE `unique_online`;
TRUNCATE TABLE `unique_online_hwids`;

-- =====================================================
-- 9. BẢNG GIỮ LẠI (KHÔNG XÓA) - SYSTEM DATA
-- =====================================================

-- ✅ KHÔNG XÓA những bảng này vì chứa data mặc định:
-- - `castle` - Thông tin castle mặc định
-- - `artifact_state` - Trạng thái artifact mặc định  
-- - `castle_siege_guards` - Guards mặc định
-- - `clanhall` - Clan hall mặc định
-- - `global_variables` - Biến hệ thống
-- - Các bảng cấu hình khác

SELECT 'The following tables are PRESERVED (contain default system data):' as preserved_tables;
SELECT 'castle, artifact_state, castle_siege_guards, clanhall, global_variables' as preserved_list;

-- =====================================================
-- 10. RESET AUTO_INCREMENT COUNTERS
-- =====================================================

-- Reset auto increment cho các bảng đã wipe
ALTER TABLE `accounts` AUTO_INCREMENT = 1;
ALTER TABLE `characters` AUTO_INCREMENT = *********; -- L2 character ID start
ALTER TABLE `clan_data` AUTO_INCREMENT = 1;
ALTER TABLE `items` AUTO_INCREMENT = 1;
ALTER TABLE `commission_items` AUTO_INCREMENT = 1;

-- =====================================================
-- 11. KHÔI PHỤC SETTINGS
-- =====================================================

SET FOREIGN_KEY_CHECKS = 1;
SET SQL_SAFE_UPDATES = 1;

-- =====================================================
-- 12. VERIFICATION QUERIES
-- =====================================================

SELECT 'Complete player data wipe finished!' as result;
SELECT 'All player data deleted, system data preserved' as info;

-- Kiểm tra số lượng records (should be 0 for player tables)
SELECT 'Player Tables (should be 0):' as check_title;
SELECT 'accounts' as table_name, COUNT(*) as records FROM accounts
UNION ALL
SELECT 'characters', COUNT(*) FROM characters  
UNION ALL
SELECT 'clan_data', COUNT(*) FROM clan_data
UNION ALL
SELECT 'items', COUNT(*) FROM items
UNION ALL
SELECT 'character_variables', COUNT(*) FROM character_variables;

-- Kiểm tra system tables (should have data)
SELECT 'System Tables (should have data):' as system_check;
SELECT 'castle' as table_name, COUNT(*) as records FROM castle
UNION ALL
SELECT 'artifact_state', COUNT(*) FROM artifact_state;

-- =====================================================
-- 13. POST-WIPE SETUP (OPTIONAL)
-- =====================================================

-- Tạo admin account mới
INSERT INTO `accounts` (`login`, `password`, `accessLevel`, `lastIP`) 
VALUES ('admin', 'Bf50YcYHwzIpdy1AJQVgEBan0Oo=', 100, '127.0.0.1');

-- Thêm account data cho admin
INSERT INTO `account_gsdata` (`account_name`, `var`, `value`) 
VALUES ('admin', 'START_BONUS_PREMIUM', 'Y'),
       ('admin', 'START_BONUS_START', 'Y');

SELECT 'Admin account created: login=admin, password=admin123' as admin_info;
SELECT 'Server ready for fresh start!' as final_result;
