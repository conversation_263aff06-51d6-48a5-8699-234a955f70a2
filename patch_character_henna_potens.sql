RENAME TABLE character_henna_potens TO character_henna_potens_old;
CREATE TABLE IF NOT EXISTS character_henna_potens
(
    account_name VARCHAR(45)  DEFAULT NULL,
    level_1      INT NOT NULL DEFAULT 0,
    exp_1        INT NOT NULL DEFAULT 0,
    potenId_1    INT NOT NULL DEFAULT 0,
    level_2      INT NOT NULL DEFAULT 0,
    exp_2        INT NOT NULL DEFAULT 0,
    potenId_2    INT NOT NULL DEFAULT 0,
    level_3      INT NOT NULL DEFAULT 0,
    exp_3        INT NOT NULL DEFAULT 0,
    potenId_3    INT NOT NULL DEFAULT 0,
    level_4      INT NOT NULL DEFAULT 0,
    exp_4        INT NOT NULL DEFAULT 0,
    potenId_4    INT NOT NULL DEFAULT 0,
    PRIMARY KEY (account_name)
) ENGINE = MyISAM
  DEFAULT CHARSET = latin1
  COLLATE = latin1_general_ci;
INSERT INTO character_henna_potens (SELECT BEST_CHAR.account_name,
                                           character_henna_potens_old.level_1,
                                           character_henna_potens_old.exp_1,
                                           character_henna_potens_old.potenId_1,
                                           character_henna_potens_old.level_2,
                                           character_henna_potens_old.exp_2,
                                           character_henna_potens_old.potenId_2,
                                           character_henna_potens_old.level_3,
                                           character_henna_potens_old.exp_3,
                                           character_henna_potens_old.potenId_3,
                                           character_henna_potens_old.level_4,
                                           character_henna_potens_old.exp_4,
                                           character_henna_potens_old.potenId_4
                                    FROM character_henna_potens_old
                                             INNER JOIN (SELECT characters.account_name,
                                                                character_henna_potens_old.charId,
                                                                MAX(
                                                                            character_henna_potens_old.exp_1 +
                                                                            CASE character_henna_potens_old.level_1
                                                                                WHEN 1 THEN 20
                                                                                WHEN 2 THEN 50
                                                                                WHEN 3 THEN 90
                                                                                WHEN 4 THEN 140
                                                                                WHEN 5 THEN 240
                                                                                WHEN 6 THEN 350
                                                                                WHEN 7 THEN 470
                                                                                WHEN 8 THEN 600
                                                                                WHEN 9 THEN 740
                                                                                WHEN 10 THEN 890
                                                                                WHEN 11 THEN 1060
                                                                                WHEN 12 THEN 1250
                                                                                WHEN 13 THEN 1460
                                                                                WHEN 14 THEN 1690
                                                                                WHEN 15 THEN 1940
                                                                                WHEN 16 THEN 2240
                                                                                WHEN 17 THEN 2590
                                                                                WHEN 18 THEN 2990
                                                                                WHEN 19 THEN 3490
                                                                                WHEN 20 THEN 4090
                                                                                END +
                                                                            character_henna_potens_old.exp_2 +
                                                                            CASE character_henna_potens_old.level_2
                                                                                WHEN 1 THEN 20
                                                                                WHEN 2 THEN 50
                                                                                WHEN 3 THEN 90
                                                                                WHEN 4 THEN 140
                                                                                WHEN 5 THEN 240
                                                                                WHEN 6 THEN 350
                                                                                WHEN 7 THEN 470
                                                                                WHEN 8 THEN 600
                                                                                WHEN 9 THEN 740
                                                                                WHEN 10 THEN 890
                                                                                WHEN 11 THEN 1060
                                                                                WHEN 12 THEN 1250
                                                                                WHEN 13 THEN 1460
                                                                                WHEN 14 THEN 1690
                                                                                WHEN 15 THEN 1940
                                                                                WHEN 16 THEN 2240
                                                                                WHEN 17 THEN 2590
                                                                                WHEN 18 THEN 2990
                                                                                WHEN 19 THEN 3490
                                                                                WHEN 20 THEN 4090
                                                                                END + character_henna_potens_old.exp_3 +
                                                                            CASE character_henna_potens_old.level_3
                                                                                WHEN 1 THEN 20
                                                                                WHEN 2 THEN 50
                                                                                WHEN 3 THEN 90
                                                                                WHEN 4 THEN 140
                                                                                WHEN 5 THEN 240
                                                                                WHEN 6 THEN 350
                                                                                WHEN 7 THEN 470
                                                                                WHEN 8 THEN 600
                                                                                WHEN 9 THEN 740
                                                                                WHEN 10 THEN 890
                                                                                WHEN 11 THEN 1060
                                                                                WHEN 12 THEN 1250
                                                                                WHEN 13 THEN 1460
                                                                                WHEN 14 THEN 1690
                                                                                WHEN 15 THEN 1940
                                                                                WHEN 16 THEN 2240
                                                                                WHEN 17 THEN 2590
                                                                                WHEN 18 THEN 2990
                                                                                WHEN 19 THEN 3490
                                                                                WHEN 20 THEN 4090
                                                                                END + character_henna_potens_old.exp_4 +
                                                                            CASE character_henna_potens_old.level_4
                                                                                WHEN 1 THEN 20
                                                                                WHEN 2 THEN 50
                                                                                WHEN 3 THEN 90
                                                                                WHEN 4 THEN 140
                                                                                WHEN 5 THEN 240
                                                                                WHEN 6 THEN 350
                                                                                WHEN 7 THEN 470
                                                                                WHEN 8 THEN 600
                                                                                WHEN 9 THEN 740
                                                                                WHEN 10 THEN 890
                                                                                WHEN 11 THEN 1060
                                                                                WHEN 12 THEN 1250
                                                                                WHEN 13 THEN 1460
                                                                                WHEN 14 THEN 1690
                                                                                WHEN 15 THEN 1940
                                                                                WHEN 16 THEN 2240
                                                                                WHEN 17 THEN 2590
                                                                                WHEN 18 THEN 2990
                                                                                WHEN 19 THEN 3490
                                                                                WHEN 20 THEN 4090
                                                                                END
                                                                    ) AS BEST_EXP
                                                         FROM character_henna_potens_old
                                                                  INNER JOIN characters ON character_henna_potens_old.charId = characters.charId
                                                         GROUP BY characters.account_name
                                    ) BEST_CHAR ON character_henna_potens_old.charId = BEST_CHAR.charId);
