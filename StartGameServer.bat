@echo off
title L2J Essence Crusader GVE - Game Server
color 0B

echo.
echo ===============================================
echo    L2J Essence Crusader GVE - Game Server
echo ===============================================
echo.

REM Check if Java is installed
java -version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Java is not installed or not in PATH!
    echo Please install Java 8 or higher and try again.
    pause
    exit /b 1
)

REM Create logs directory if it doesn't exist
if not exist "logs" mkdir logs

REM Display system information
echo System Information:
echo - Java Version: 
java -version 2>&1 | findstr "version"
echo - Available Memory: 
for /f "tokens=2 delims=:" %%a in ('systeminfo ^| findstr "Available Physical Memory"') do echo   %%a
echo.

REM Set classpath
set CLASSPATH=.
for %%i in ("dist\libs\*.jar") do call :addcp "%%i"
for %%i in ("dist\game\*.jar") do call :addcp "%%i"

:start
echo ========================================
echo Starting Game Server...
echo ========================================
echo.
echo Memory Settings: 4GB Initial, 8GB Maximum
echo Garbage Collector: G1GC (Optimized for low latency)
echo Target Players: 2000+ concurrent
echo.

REM JVM Arguments for Game Server (optimized for Java 21 + 2000+ players)
java ^
-server ^
-Xms4G ^
-Xmx8G ^
-XX:NewRatio=2 ^
-XX:SurvivorRatio=8 ^
-XX:+UseG1GC ^
-XX:MaxGCPauseMillis=200 ^
-XX:G1HeapRegionSize=16m ^
-XX:G1NewSizePercent=20 ^
-XX:G1MaxNewSizePercent=40 ^
-XX:G1MixedGCCountTarget=8 ^
-XX:G1MixedGCLiveThresholdPercent=85 ^
-XX:+DisableExplicitGC ^
-XX:+UseStringDeduplication ^
-XX:+OptimizeStringConcat ^
-XX:+UseCompressedOops ^
-XX:+UnlockExperimentalVMOptions ^
-XX:+UseTransparentHugePages ^
-XX:+UseLargePages ^
-XX:+HeapDumpOnOutOfMemoryError ^
-XX:HeapDumpPath=./logs/ ^
-Xlog:gc*:./logs/game_gc.log:time,tags ^
-Xlog:gc+heap=info ^
-XX:+FlightRecorder ^
-XX:StartFlightRecording=duration=60s,filename=./logs/startup.jfr ^
--add-opens java.base/java.lang=ALL-UNNAMED ^
--add-opens java.base/java.lang.reflect=ALL-UNNAMED ^
--add-opens java.base/java.util=ALL-UNNAMED ^
--add-opens java.base/java.util.concurrent=ALL-UNNAMED ^
--add-opens java.base/java.util.concurrent.atomic=ALL-UNNAMED ^
--add-opens java.base/java.net=ALL-UNNAMED ^
--add-opens java.base/java.nio=ALL-UNNAMED ^
--add-opens java.base/java.io=ALL-UNNAMED ^
--add-opens java.base/sun.nio.ch=ALL-UNNAMED ^
--add-opens java.management/sun.management=ALL-UNNAMED ^
-Djava.net.preferIPv4Stack=true ^
-Dfile.encoding=UTF-8 ^
-Djava.util.logging.manager=club.projectessence.commons.util.L2LogManager ^
-Djava.util.logging.config.file=./dist/game/log.cfg ^
-cp "%CLASSPATH%" ^
club.projectessence.gameserver.GameServer

REM Check exit code and handle accordingly
if %errorlevel% equ 2 (
    echo.
    echo ========================================
    echo    Admin Restart Requested
    echo ========================================
    echo.
    echo Restarting server in 10 seconds...
    timeout /t 10 /nobreak >nul
    goto start
)

if %errorlevel% equ 1 (
    echo.
    echo ========================================
    echo    Server terminated with errors
    echo ========================================
    echo.
    echo Check the following for troubleshooting:
    echo - logs/java.log for Java errors
    echo - logs/game_gc.log for GC issues
    echo - Database connection settings
    echo.
    echo Would you like to restart the server? (Y/N)
    set /p restart="Enter choice: "
    if /i "%restart%"=="Y" (
        echo.
        echo Restarting in 5 seconds...
        timeout /t 5 /nobreak >nul
        goto start
    )
    goto end
)

if %errorlevel% equ 0 (
    echo.
    echo ========================================
    echo    Server shutdown normally
    echo ========================================
    echo.
    goto end
)

REM Handle unexpected shutdown
echo.
echo ========================================
echo    Unexpected shutdown (Code: %errorlevel%)
echo ========================================
echo.
echo This might indicate:
echo - Out of Memory Error (check heap dump in logs/)
echo - System crash or forced termination
echo - Network issues
echo.
echo Would you like to restart the server? (Y/N)
set /p restart="Enter choice: "
if /i "%restart%"=="Y" (
    echo.
    echo Restarting in 5 seconds...
    timeout /t 5 /nobreak >nul
    goto start
)

:end
echo.
echo ========================================
echo    Game Server stopped
echo ========================================
echo.
echo Logs location: ./logs/
echo GC Log: ./logs/game_gc.log
echo Java Log: ./logs/java.log
echo.
echo Thank you for using L2J Essence Crusader GVE!
echo.
pause
exit /b 0

:addcp
set CLASSPATH=%CLASSPATH%;%1
goto :eof
