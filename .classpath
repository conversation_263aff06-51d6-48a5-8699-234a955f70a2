<?xml version="1.0" encoding="UTF-8"?>
<classpath>
	<classpathentry kind="src" path="java"/>
	<classpathentry including="**/*.java" kind="src" path="dist/game/data/scripts"/>
	<classpathentry kind="con" path="org.eclipse.jdt.launching.JRE_CONTAINER">
		<attributes>
			<attribute name="module" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="lib" path="dist/libs/cron4j-2.2.5.jar" sourcepath="dist/libs/cron4j-2.2.5-sources.jar"/>
	<classpathentry kind="lib" path="dist/libs/exp4j-0.4.8.jar" sourcepath="dist/libs/exp4j-0.4.8-sources.jar"/>
	<classpathentry kind="lib" path="dist/libs/mariadb-java-client-2.6.1.jar" sourcepath="dist/libs/mariadb-java-client-2.6.1-sources.jar"/>
	<classpathentry kind="lib" path="dist/libs/JDA-4.2.0_203-withDependencies.jar"/>
	<classpathentry kind="lib" path="dist/libs/commons-dbcp-1.4.jar"/>
	<classpathentry kind="lib" path="dist/libs/commons-pool-1.5.4.jar"/>
	<classpathentry kind="lib" path="dist/libs/netty-all-4.1.50.Final.jar"/>
	<classpathentry kind="lib" path="dist/libs/activation.jar"/>
	<classpathentry kind="lib" path="dist/libs/javax.mail-1.6.2.jar"/>
	<classpathentry kind="lib" path="dist/libs/telegrambots-5.7.1-jar-with-dependencies.jar"/>
	<classpathentry kind="lib" path="dist/libs/async-mmocore-3.4.0.jar"/>
	<classpathentry kind="output" path="bin"/>
</classpath>
