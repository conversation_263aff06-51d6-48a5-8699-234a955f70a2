@echo off
title L2J Essence Crusader GVE - Server Launcher
color 0E

echo.
echo ===============================================
echo    L2J Essence Crusader GVE - Server Launcher
echo ===============================================
echo.

REM Check if Java is installed
java -version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Java is not installed or not in PATH!
    echo Please install Java 8 or higher and try again.
    pause
    exit /b 1
)

REM Display system requirements check
echo System Requirements Check:
echo.

REM Check available memory
for /f "tokens=2 delims=:" %%a in ('systeminfo ^| findstr "Available Physical Memory"') do (
    set "available_memory=%%a"
    echo Available Physical Memory: %%a
)

REM Check Java version
echo Java Version:
java -version 2>&1 | findstr "version"
echo.

echo Recommended System Requirements:
echo - RAM: 12GB+ (8GB for GameServer + 1GB for LoginServer + 3GB for OS)
echo - CPU: 4+ cores
echo - Java: 21 (Current: Detected and Optimized)
echo.

echo ========================================
echo    Starting Servers
echo ========================================
echo.

echo [1/2] Starting Login Server...
echo.

REM Start Login Server in a new window
start "L2J Login Server" /min cmd /c "StartLoginServer.bat"

REM Wait a bit for Login Server to initialize
echo Waiting for Login Server to initialize...
timeout /t 10 /nobreak >nul

echo [2/2] Starting Game Server...
echo.

REM Start Game Server in a new window
start "L2J Game Server" cmd /c "StartGameServer.bat"

echo.
echo ========================================
echo    Both servers are starting...
echo ========================================
echo.
echo Login Server: Starting in minimized window
echo Game Server: Starting in new window
echo.
echo Important Notes:
echo - Wait for Login Server to fully start before connecting
echo - Game Server will connect to Login Server automatically
echo - Check individual server windows for status messages
echo - Use Ctrl+C in server windows to shutdown gracefully
echo.
echo Server Management:
echo - To restart: Close both windows and run this script again
echo - To stop: Close Game Server first, then Login Server
echo - Logs are saved in ./logs/ directory
echo.
echo ========================================
echo    Startup Complete
echo ========================================
echo.
echo Both servers have been launched!
echo.
echo Useful Commands:
echo - Check server status: Look at individual server windows
echo - View logs: Check ./logs/ directory
echo - Memory monitoring: Use //debug memory_check in game
echo.

pause
