# Flexible Player Variables System Migration

## Tổng quan

Dự án này đã được chuyển đổi từ hệ thống PlayerVariables cứng (enum-based) sang hệ thống linh hoạt (key-value) như L2JMobius gốc.

## Thay đổi chính

### 1. Database Schema
- **Bảng cũ**: `player_variables` với các cột cố định
- **Bảng mới**: `character_variables` với cấu trúc key-value linh hoạt

```sql
CREATE TABLE `character_variables` (
  `charId` int(10) unsigned NOT NULL,
  `var` varchar(255) NOT NULL,
  `val` text NOT NULL,
  PRIMARY KEY (`charId`, `var`),
  KEY `idx_charId` (`charId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8_unicode_ci;
```

### 2. PlayerVariables Class
- **Trước**: Sử dụng enum `PLAYER_VARIABLE` với các field cố định
- **Sau**: <PERSON><PERSON> dụng string constants và key-value storage

### 3. Cách sử dụng

#### Trước (enum-based):
```java
player.getVariables().getInt(PLAYER_VARIABLE.GVE_SKILL_POINTS.toString(), 0);
player.getVariables().set(PLAYER_VARIABLE.GVE_SKILL_POINTS.toString(), 100);
```

#### Sau (string-based):
```java
player.getVariables().getInt(PlayerVariables.GVE_SKILL_POINTS, 0);
player.getVariables().set(PlayerVariables.GVE_SKILL_POINTS, 100);
```

## Hướng dẫn Migration

### Bước 1: Backup Database
```bash
mysqldump -u root -p gameserver > backup_before_migration.sql
```

### Bước 2: Chạy Migration Script
```bash
# Windows
run_migration.bat

# Linux/Mac
mysql -u root -p gameserver < sql_updates/migrate_to_flexible_player_variables.sql
```

### Bước 3: Restart Server
Khởi động lại game server để áp dụng các thay đổi code.

### Bước 4: Kiểm tra
- Đăng nhập game và kiểm tra các chức năng liên quan đến player variables
- Kiểm tra GvE system, premium rates, stat points, etc.

### Bước 5: Cleanup (Tùy chọn)
Sau khi đã kiểm tra và chắc chắn mọi thứ hoạt động tốt:
```sql
DROP TABLE player_variables;
DROP TABLE player_premium_dynamic_rates; -- Nếu không còn sử dụng
```

## Lợi ích của hệ thống mới

### 1. Linh hoạt
- Có thể thêm variables mới mà không cần thay đổi database schema
- Không cần restart server khi thêm variables mới

### 2. Hiệu suất
- Chỉ lưu những variables có giá trị khác default
- Giảm dung lượng database đáng kể

### 3. Dễ bảo trì
- Code đơn giản hơn, ít phức tạp hơn
- Tương thích với L2JMobius standard

### 4. Backward Compatibility
- Tất cả constants được giữ nguyên tên
- Migration tự động từ hệ thống cũ

## Constants có sẵn

Tất cả các constants từ enum `PLAYER_VARIABLE` cũ đã được chuyển đổi thành string constants:

```java
// GVE System
PlayerVariables.GVE_SKILL_POINTS
PlayerVariables.GVE_DAILY_POINTS
PlayerVariables.GVE_DAILY_DATE

// Stats
PlayerVariables.STAT_STR
PlayerVariables.STAT_DEX
PlayerVariables.STAT_CON
PlayerVariables.STAT_INT
PlayerVariables.STAT_WIT
PlayerVariables.STAT_MEN

// Premium Rates
PlayerVariables.PREMIUM_EXP_RATE
PlayerVariables.PREMIUM_SP_RATE
PlayerVariables.PREMIUM_ADENA_RATE

// Và nhiều constants khác...
```

## Thêm Variables mới

### Cách cũ (cần thay đổi database):
1. Thêm vào enum `PLAYER_VARIABLE`
2. Thêm cột vào bảng `player_variables`
3. Restart server

### Cách mới (không cần thay đổi database):
1. Thêm constant vào `PlayerVariables.java` (tùy chọn)
2. Sử dụng trực tiếp:
```java
player.getVariables().set("MY_NEW_VARIABLE", value);
```

## Troubleshooting

### Lỗi "Table doesn't exist"
- Chắc chắn đã chạy migration script
- Kiểm tra database connection

### Dữ liệu bị mất
- Kiểm tra migration script đã chạy thành công
- Restore từ backup nếu cần

### Performance issues
- Đảm bảo index trên `charId` column
- Kiểm tra query optimization

## Files đã thay đổi

### Core Files:
- `PlayerVariables.java` - Hoàn toàn mới
- `migrate_to_flexible_player_variables.sql` - Migration script

### Updated Files:
- `PlayerStat.java` - Premium rates và stat calculations
- `CreatureStat.java` - Stat bonuses
- `ExSetStatusBonus.java` - Stat point allocation
- `ExBalrogWarGetReward.java` - Balok rewards
- `ExRequestSaveItemAnnounceSetting.java` - Item announce settings
- `PremiumManager.java` - Premium rate management
- `CeremonyOfChaosEvent.java` - CoC marks
- `MainCBH.java` - Community board
- `EnterWorld.java` - Login handling
- `ClanTable.java` - Clan variables
- `DailyTaskManager.java` - Daily tasks và database queries
- `RequestPurchaseLimitShopItemBuy.java` - Shop purchases
- `ExUISetting.java` - UI settings
- `PetStat.java` - Pet premium rates
- `RequestLuckyGamePlay.java` - Fortune telling
- `ExRequestTimeRestrictFieldUserEnter.java` - Special hunting zones
- `ExRequestPledgeDonationRequest.java` - Clan donations
- `ExRequestTeleportFavoritesAddDel.java` - Favorite teleports
- `RequestTeleportToRaidPosition.java` - Raid teleports
- `RequestSaveKeyMapping.java` - UI key mapping
- `UserInfo.java` - User information
- `WatermelonFever.java` - Event script
- `ExPledgeDonationRequest.java` - Pledge donation packets
- `AddHuntingTime.java` - Hunting time effects
- `Formulas.java` - Debug calculations
- `ExPledgeDonationInfo.java` - Pledge donation info
- `Elixir.java` - Elixir item handler
- `Clan.java` - Clan join logic
- `ExSubjugationList.java` - Purge data
- `ResetTranscendentInstanceZone.java` - Zone reset effects
- `SpecialHuntingZone.java` - Zone logic
- `ExTeleportFavoritesList.java` - Teleport favorites
- `ResetTrainingInstanceZone.java` - Training zone reset
- `PinkRabbitChasing.java` - Event script
- `DiscordCBH.java` - Discord community board
- `Purge.java` - Purge system và database queries
- `ExTimeRestrictFieldList.java` - Special hunting zone UI
- `CeremonyOfChaosManager.java` - CoC manager và database queries
- `TelegramBot.java` - Telegram integration
- `ExUserInfoEquipSlot.java` - Equipment display

## Liên hệ

Nếu có vấn đề gì trong quá trình migration, vui lòng liên hệ để được hỗ trợ.
