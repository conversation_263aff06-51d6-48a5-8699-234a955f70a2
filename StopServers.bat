@echo off
title L2J Essence Crusader GVE - Server Stopper
color 0C

echo.
echo ===============================================
echo    L2J Essence Crusader GVE - Server Stopper
echo ===============================================
echo.

echo This will gracefully shutdown all L2J servers.
echo.
echo WARNING: This will disconnect all online players!
echo.
set /p confirm="Are you sure you want to stop all servers? (Y/N): "

if /i not "%confirm%"=="Y" (
    echo.
    echo Operation cancelled.
    pause
    exit /b 0
)

echo.
echo ========================================
echo    Stopping L2J Servers...
echo ========================================
echo.

REM Try to find and terminate Java processes running L2J servers
echo [1/3] Looking for running L2J processes...

REM Find GameServer process
for /f "tokens=2" %%i in ('tasklist /fi "imagename eq java.exe" /fo csv ^| findstr "GameServer"') do (
    echo Found GameServer process: %%i
    set gameserver_pid=%%i
)

REM Find LoginServer process  
for /f "tokens=2" %%i in ('tasklist /fi "imagename eq java.exe" /fo csv ^| findstr "LoginServer"') do (
    echo Found LoginServer process: %%i
    set loginserver_pid=%%i
)

echo.
echo [2/3] Sending shutdown signals...

REM Try graceful shutdown first by sending Ctrl+C to console windows
echo Attempting graceful shutdown...

REM Send Ctrl+C to Game Server window
tasklist /fi "windowtitle eq L2J Game Server*" >nul 2>&1
if %errorlevel% equ 0 (
    echo Sending shutdown signal to Game Server...
    REM Note: This is a simplified approach. In practice, you might need a more sophisticated method
)

REM Send Ctrl+C to Login Server window
tasklist /fi "windowtitle eq L2J Login Server*" >nul 2>&1
if %errorlevel% equ 0 (
    echo Sending shutdown signal to Login Server...
)

echo.
echo Waiting 15 seconds for graceful shutdown...
timeout /t 15 /nobreak >nul

echo.
echo [3/3] Force terminating remaining processes...

REM Force kill any remaining Java processes that might be L2J servers
for /f "tokens=2,9" %%i in ('tasklist /fi "imagename eq java.exe" /fo csv') do (
    echo Checking process %%i with command line...
    
    REM Check if it's a L2J process by looking for our main classes
    wmic process where "ProcessId=%%i" get CommandLine /format:csv | findstr "club.projectessence" >nul 2>&1
    if !errorlevel! equ 0 (
        echo Terminating L2J process %%i...
        taskkill /pid %%i /f >nul 2>&1
        if !errorlevel! equ 0 (
            echo Successfully terminated process %%i
        ) else (
            echo Failed to terminate process %%i
        )
    )
)

echo.
echo ========================================
echo    Cleanup Complete
echo ========================================
echo.

REM Check if any Java processes are still running
tasklist /fi "imagename eq java.exe" | findstr "java.exe" >nul 2>&1
if %errorlevel% equ 0 (
    echo WARNING: Some Java processes are still running.
    echo These might be other applications or stubborn L2J processes.
    echo.
    echo Current Java processes:
    tasklist /fi "imagename eq java.exe"
    echo.
    echo If these are L2J processes, you may need to:
    echo 1. Close the server console windows manually
    echo 2. Use Task Manager to end the processes
    echo 3. Restart your computer if processes are stuck
) else (
    echo All Java processes have been terminated.
)

echo.
echo ========================================
echo    Server Shutdown Summary
echo ========================================
echo.
echo - Login Server: Stopped
echo - Game Server: Stopped
echo - All player connections: Terminated
echo - Logs preserved in: ./logs/
echo.
echo To restart servers:
echo - Run StartBothServers.bat
echo - Or run individual server batch files
echo.
echo Thank you for using L2J Essence Crusader GVE!
echo.

pause
