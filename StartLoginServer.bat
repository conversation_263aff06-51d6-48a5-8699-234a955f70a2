@echo off
title L2J Essence Crusader GVE - Login Server
color 0A

echo.
echo ===============================================
echo    L2J Essence Crusader GVE - Login Server
echo ===============================================
echo.

REM Check if Java is installed
java -version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Java is not installed or not in PATH!
    echo Please install Java 8 or higher and try again.
    pause
    exit /b 1
)

REM Create logs directory if it doesn't exist
if not exist "logs" mkdir logs

REM Set classpath
set CLASSPATH=.
for %%i in ("dist\libs\*.jar") do call :addcp "%%i"
for %%i in ("dist\login\*.jar") do call :addcp "%%i"

:start
echo Starting Login Server...
echo.

REM JVM Arguments for Login Server (optimized for Java 21)
java ^
-server ^
-Xms512M ^
-Xmx1G ^
-XX:+UseG1GC ^
-XX:MaxGCPauseMillis=100 ^
-XX:G1HeapRegionSize=8m ^
-XX:+DisableExplicitGC ^
-XX:+UseCompressedOops ^
-XX:+UseStringDeduplication ^
-XX:+HeapDumpOnOutOfMemoryError ^
-XX:HeapDumpPath=./logs/ ^
-Xlog:gc*:./logs/login_gc.log:time,tags ^
-XX:+UnlockExperimentalVMOptions ^
-XX:+UseTransparentHugePages ^
-XX:+OptimizeStringConcat ^
--add-opens java.base/java.lang=ALL-UNNAMED ^
--add-opens java.base/java.util=ALL-UNNAMED ^
--add-opens java.base/java.util.concurrent=ALL-UNNAMED ^
--add-opens java.base/java.net=ALL-UNNAMED ^
--add-opens java.base/java.io=ALL-UNNAMED ^
-Djava.net.preferIPv4Stack=true ^
-Dfile.encoding=UTF-8 ^
-Djava.util.logging.manager=club.projectessence.commons.util.L2LogManager ^
-Djava.util.logging.config.file=./dist/login/log.cfg ^
-cp "%CLASSPATH%" ^
club.projectessence.loginserver.LoginServer

REM Check exit code
if %errorlevel% equ 2 (
    echo.
    echo ========================================
    echo    Admin Restart Requested
    echo ========================================
    echo.
    timeout /t 5 /nobreak >nul
    goto start
)

if %errorlevel% equ 1 (
    echo.
    echo ========================================
    echo    Server terminated with errors
    echo ========================================
    echo.
    echo Check logs for details.
    goto end
)

if %errorlevel% equ 0 (
    echo.
    echo ========================================
    echo    Server shutdown normally
    echo ========================================
    echo.
    goto end
)

echo.
echo ========================================
echo    Unexpected shutdown (Code: %errorlevel%)
echo ========================================
echo.

:end
echo.
echo Login Server stopped.
echo.
pause
exit /b 0

:addcp
set CLASSPATH=%CLASSPATH%;%1
goto :eof
