### Eclipse Workspace Patch 1.0
#P L2J_SunriseProject_Core
Index: java/l2r/features/museum/TopPlayer.java
===================================================================
--- java/l2r/features/museum/TopPlayer.java	(revision 0)
+++ java/l2r/features/museum/TopPlayer.java	(working copy)
@@ -0,0 +1,30 @@
+package l2r.features.museum;
+
+public class TopPlayer
+{
+	int _objectId;
+	long _count;
+	String _name;
+	
+	public TopPlayer(final int objectId, final String name, final long count)
+	{
+		_objectId = objectId;
+		_name = name;
+		_count = count;
+	}
+	
+	public int getObjectId()
+	{
+		return _objectId;
+	}
+	
+	public String getName()
+	{
+		return _name;
+	}
+	
+	public long getCount()
+	{
+		return _count;
+	}
+}
Index: java/l2r/gameserver/model/quest/QuestState.java
===================================================================
--- java/l2r/gameserver/model/quest/QuestState.java	(revision 1211)
+++ java/l2r/gameserver/model/quest/QuestState.java	(working copy)
@@ -1205,6 +1205,7 @@
 		getQuest().removeRegisteredQuestItems(_player);
 		
 		Quest.deleteQuestInDb(this, repeatable);
+		_player.getMuseumPlayer().addData("quests_clear", 1);
 		if (repeatable)
 		{
 			_player.delQuestState(getQuestName());
Index: java/l2r/gameserver/network/serverpackets/CharSelectionInfo.java
===================================================================
--- java/l2r/gameserver/network/serverpackets/CharSelectionInfo.java	(revision 1211)
+++ java/l2r/gameserver/network/serverpackets/CharSelectionInfo.java	(working copy)
@@ -175,7 +175,7 @@
 		}
 	}
 	
-	private static List<CharSelectInfoPackage> loadCharacterSelectInfo(String loginName)
+	public static List<CharSelectInfoPackage> loadCharacterSelectInfo(String loginName)
 	{
 		final List<CharSelectInfoPackage> characterList = new ArrayList<>();
 		try (Connection con = L2DatabaseFactory.getInstance().getConnection();
@@ -224,7 +224,7 @@
 		}
 	}
 	
-	private static CharSelectInfoPackage restoreChar(ResultSet chardata) throws Exception
+	public static CharSelectInfoPackage restoreChar(ResultSet chardata) throws Exception
 	{
 		int objectId = chardata.getInt("charId");
 		String name = chardata.getString("char_name");
Index: java/l2r/gameserver/model/actor/instance/L2PcInstance.java
===================================================================
--- java/l2r/gameserver/model/actor/instance/L2PcInstance.java	(revision 1220)
+++ java/l2r/gameserver/model/actor/instance/L2PcInstance.java	(working copy)
@@ -49,6 +49,8 @@
 
 import l2r.Config;
 import l2r.L2DatabaseFactory;
+import l2r.features.museum.MuseumManager;
+import l2r.features.museum.MuseumPlayer;
 import l2r.gameserver.GameTimeController;
 import l2r.gameserver.GeoData;
 import l2r.gameserver.ItemsAutoDestroy;
@@ -380,6 +382,7 @@
 	private volatile boolean _isOnline = false;
 	private long _onlineTime;
 	private long _onlineBeginTime;
+	private long _museumOnlineTime;
 	private long _lastAccess;
 	private long _uptime;
 	private long _zoneRestartLimitTime = 0;
@@ -2818,6 +2821,11 @@
 		_dwarvenRecipeBook.clear();
 	}
 	
+	public void refreshMuseumOnlineTime()
+	{
+		_museumOnlineTime = System.currentTimeMillis();
+	}
+	
 	public long getZoneRestartLimitTime()
 	{
 		return _zoneRestartLimitTime;
@@ -3467,6 +3475,10 @@
 						ward.activate(this, createdItem);
 					}
 				}
+				if (itemId == 57)
+				{
+					getMuseumPlayer().addData("adena", count);
+				}
 				return createdItem;
 			}
 		}
@@ -5453,6 +5465,11 @@
 		
 		AntiFeedManager.getInstance().setLastDeathTime(getObjectId());
 		
+		if (killer instanceof L2Attackable)
+		{
+			getMuseumPlayer().addData("monster_deaths", 1);
+		}
+		
 		return true;
 	}
 	
@@ -5699,6 +5716,9 @@
 				
 				setPvpKills(getPvpKills() + 1);
 				
+				getMuseumPlayer().addData("pvp_victories", 1);
+				((L2PcInstance) target).getMuseumPlayer().addData("pvp_defeats", 1);
+				
 				// Send a Server->Client UserInfo packet to attacker with its Karma and PK Counter
 				sendUserInfo(true);
 			}
@@ -5729,6 +5749,8 @@
 		if (target.isPlayer())
 		{
 			setPkKills(getPkKills() + 1);
+			getMuseumPlayer().addData("pk_victories", 1);
+			((L2PcInstance) target).getMuseumPlayer().addData("pk_defeats", 1);
 		}
 		
 		// Color system
@@ -7434,6 +7456,8 @@
 						player.getClanPrivileges().clear();
 					}
 					
+					player.refreshMuseumOnlineTime();
+					
 					// Retrieve the name and ID of the other characters assigned to this account.
 					try (PreparedStatement stmt = con.prepareStatement("SELECT charId, char_name FROM characters WHERE account_name=? AND charId<>?"))
 					{
@@ -7504,6 +7528,8 @@
 			
 			player.restoreFriendList();
 			
+			MuseumManager.getInstance().restoreDataForChar(player);
+			
 			if (Config.STORE_UI_SETTINGS)
 			{
 				player.restoreUISettings();
@@ -7663,6 +7689,8 @@
 		}
 		SevenSigns.getInstance().saveSevenSignsData(getObjectId());
 		
+		MuseumManager.getInstance().updateDataForChar(this);
+		
 		final PlayerVariables vars = getScript(PlayerVariables.class);
 		if (vars != null)
 		{
@@ -7735,6 +7763,12 @@
 				totalOnlineTime += (System.currentTimeMillis() - _onlineBeginTime) / 1000;
 			}
 			
+			if (getMuseumPlayer() != null)
+			{
+				getMuseumPlayer().addData("play_duration", (System.currentTimeMillis() - _museumOnlineTime) / 1000);
+			}
+			_museumOnlineTime = System.currentTimeMillis();
+			
 			statement.setLong(35, totalOnlineTime);
 			statement.setInt(36, getNewbie());
 			statement.setInt(37, isNoble() ? 1 : 0);
@@ -14357,6 +14391,18 @@
 		return System.currentTimeMillis() - _onlineBeginTime;
 	}
 	
+	MuseumPlayer _mp = null;
+	
+	public MuseumPlayer getMuseumPlayer()
+	{
+		return _mp;
+	}
+	
+	public void setMuseumPlayer(MuseumPlayer player)
+	{
+		_mp = player;
+	}
+	
 	private final List<Integer> loadedImages = new ArrayList<>();
 	
 	/**
Index: java/l2r/features/museum/MuseumCategory.java
===================================================================
--- java/l2r/features/museum/MuseumCategory.java	(revision 0)
+++ java/l2r/features/museum/MuseumCategory.java	(working copy)
@@ -0,0 +1,119 @@
+package l2r.features.museum;
+
+import java.util.ArrayList;
+import java.util.HashMap;
+
+import l2r.gameserver.model.Location;
+
+public class MuseumCategory
+{
+	int _categoryId;
+	int _typeId;
+	String _categoryName;
+	String _typeName;
+	String _type;
+	String _additionalText;
+	RefreshTime _refreshTime;
+	boolean _timer;
+	HashMap<Integer, TopPlayer> _players;
+	HashMap<Integer, TopPlayer> _totalTopPlayers;
+	HashMap<Integer, TopPlayer> _statuePlayers;
+	ArrayList<L2MuseumStatueInstance> _spawnedStatues;
+	ArrayList<Location> _statueSpawns;
+	ArrayList<MuseumReward> _rewards;
+	
+	public MuseumCategory(final int categoryId, final int typeId, final String categoryName, final String typeName, final String type, final String refreshTime, final boolean timer, final String additionalText, final ArrayList<Location> statueSpawns, final ArrayList<MuseumReward> rewards)
+	{
+		_players = new HashMap<>();
+		_totalTopPlayers = new HashMap<>();
+		_statuePlayers = new HashMap<>();
+		_spawnedStatues = new ArrayList<>();
+		_categoryId = categoryId;
+		_typeId = typeId;
+		_categoryName = categoryName;
+		_typeName = typeName;
+		_type = type;
+		_timer = timer;
+		_additionalText = additionalText;
+		_statueSpawns = statueSpawns;
+		_rewards = rewards;
+		for (final RefreshTime time : RefreshTime.values())
+		{
+			if (time.name().toLowerCase().equals(refreshTime))
+			{
+				_refreshTime = time;
+				break;
+			}
+		}
+	}
+	
+	public int getCategoryId()
+	{
+		return _categoryId;
+	}
+	
+	public int getTypeId()
+	{
+		return _typeId;
+	}
+	
+	public String getCategoryName()
+	{
+		return _categoryName;
+	}
+	
+	public String getTypeName()
+	{
+		return _typeName;
+	}
+	
+	public String getType()
+	{
+		return _type;
+	}
+	
+	public String getAdditionalText()
+	{
+		return _additionalText;
+	}
+	
+	public RefreshTime getRefreshTime()
+	{
+		return _refreshTime;
+	}
+	
+	public boolean isTimer()
+	{
+		return _timer;
+	}
+	
+	public ArrayList<Location> getStatueSpawns()
+	{
+		return _statueSpawns;
+	}
+	
+	public ArrayList<MuseumReward> getRewards()
+	{
+		return _rewards;
+	}
+	
+	public HashMap<Integer, TopPlayer> getAllTops()
+	{
+		return _players;
+	}
+	
+	public HashMap<Integer, TopPlayer> getAllTotalTops()
+	{
+		return _totalTopPlayers;
+	}
+	
+	public HashMap<Integer, TopPlayer> getAllStatuePlayers()
+	{
+		return _statuePlayers;
+	}
+	
+	public ArrayList<L2MuseumStatueInstance> getAllSpawnedStatues()
+	{
+		return _spawnedStatues;
+	}
+}
Index: java/l2r/gameserver/model/actor/instance/L2GrandBossInstance.java
===================================================================
--- java/l2r/gameserver/model/actor/instance/L2GrandBossInstance.java	(revision 1211)
+++ java/l2r/gameserver/model/actor/instance/L2GrandBossInstance.java	(working copy)
@@ -108,6 +108,7 @@
 					{
 						Hero.getInstance().setRBkilled(member.getObjectId(), getId());
 					}
+					member.getMuseumPlayer().addData("raid_kill_" + getId(), 1);
 				}
 			}
 			else
@@ -117,6 +118,7 @@
 				{
 					Hero.getInstance().setRBkilled(player.getObjectId(), getId());
 				}
+				player.getMuseumPlayer().addData("raid_kill_" + getId(), 1);
 			}
 		}
 		return true;
Index: java/l2r/features/museum/L2MuseumStatueInstance.java
===================================================================
--- java/l2r/features/museum/L2MuseumStatueInstance.java	(revision 0)
+++ java/l2r/features/museum/L2MuseumStatueInstance.java	(working copy)
@@ -0,0 +1,194 @@
+package l2r.features.museum;
+
+import java.sql.Connection;
+import java.sql.PreparedStatement;
+import java.sql.ResultSet;
+
+import l2r.L2DatabaseFactory;
+import l2r.gameserver.communitybbs.Managers.MuseumBBSManager;
+import l2r.gameserver.enums.InstanceType;
+import l2r.gameserver.model.CharSelectInfoPackage;
+import l2r.gameserver.model.actor.L2Npc;
+import l2r.gameserver.model.actor.instance.L2PcInstance;
+import l2r.gameserver.model.actor.templates.L2NpcTemplate;
+import l2r.gameserver.network.serverpackets.ActionFailed;
+import l2r.gameserver.network.serverpackets.CharSelectionInfo;
+import l2r.gameserver.network.serverpackets.ShowBoard;
+
+import org.slf4j.Logger;
+import org.slf4j.LoggerFactory;
+
+public final class L2MuseumStatueInstance extends L2Npc
+{
+	public static final Logger _log = LoggerFactory.getLogger(L2MuseumStatueInstance.class);
+	int _type;
+	int _playerObjectId;
+	CharSelectInfoPackage _charLooks;
+	MuseumCategory _category;
+	
+	public L2MuseumStatueInstance(final L2NpcTemplate template, final int playerObjectId, final int type)
+	{
+		super(template);
+		setInstanceType(InstanceType.L2MuseumStatueInstance);
+		_playerObjectId = playerObjectId;
+		_type = type;
+		restoreCharLooks();
+		_category = MuseumManager.getInstance().getAllCategories().get(type);
+		setTitle(_category.getTypeName());
+	}
+	
+	public void restoreCharLooks()
+	{
+		try (final Connection con = L2DatabaseFactory.getInstance().getConnection();
+			final PreparedStatement statement = con.prepareStatement("SELECT * FROM characters WHERE charId=?"))
+		{
+			statement.setInt(1, _playerObjectId);
+			try (final ResultSet rset = statement.executeQuery())
+			{
+				if (rset.next())
+				{
+					_charLooks = CharSelectionInfo.restoreChar(rset);
+					if (_charLooks == null)
+					{
+						System.out.println("Player with id[" + _playerObjectId + "] not found.");
+					}
+				}
+			}
+		}
+		catch (Exception e)
+		{
+			_log.warn("Could not restore char info: " + e.getMessage(), e);
+		}
+	}
+	
+	public CharSelectInfoPackage getCharLooks()
+	{
+		return _charLooks;
+	}
+	
+	@Override
+	public void onBypassFeedback(final L2PcInstance player, final String command)
+	{
+		player.sendPacket(ActionFailed.STATIC_PACKET);
+		super.onBypassFeedback(player, command);
+	}
+	
+	@Override
+	public void showChatWindow(final L2PcInstance player)
+	{
+		String html = "<html><body scroll=no>";
+		html += showStatue();
+		html += "</body></html>";
+		separateAndSend(html, player);
+	}
+	
+	public String showStatue()
+	{
+		String html = "";
+		html += "<br><br><br><center><table><tr><td width=25></td><td><table border=1 bgcolor=3b3c34><tr><td>";
+		html = html + "<br><center><font name=\"ScreenMessageLarge\" color=b7b8b2>" + _category.getTypeName() + "</font></center>";
+		html += "<table><tr>";
+		if (!_category.getRefreshTime().equals(RefreshTime.Total))
+		{
+			html += "<td align=center width=260>";
+			html = html + "<button value=\"" + _category.getRefreshTime().name() + " Rankings\" action=\"\" fore=\"L2UI_CT1.Button_DF_Calculator\" back=\"L2UI_CT1.Button_DF_Calculator_Down\" width=\"257\" height=\"24\"/>";
+			html += "</td>";
+		}
+		html = html + "<td align=center width=" + (_category.getRefreshTime().equals(RefreshTime.Total) ? 520 : 260) + ">";
+		html += "<button value=\"Total Rankings\" action=\"\" fore=\"L2UI_CT1.Button_DF_Calculator\" back=\"L2UI_CT1.Button_DF_Calculator_Down\" width=\"257\" height=\"24\"/>";
+		html += "</td>";
+		html += "</tr><tr>";
+		if (!_category.getRefreshTime().equals(RefreshTime.Total))
+		{
+			html += "<td align=center width=260>";
+			for (int i = 0; i < 5; ++i)
+			{
+				String name = "No information.";
+				String value = "No information.";
+				int cellSpacing = -1;
+				if (_category.getAllStatuePlayers().size() > i)
+				{
+					final TopPlayer player = _category.getAllStatuePlayers().get(i + 1);
+					if (player != null)
+					{
+						name = player.getName();
+						final long count = player.getCount();
+						value = MuseumBBSManager.getInstance().convertToValue(count, _category.isTimer(), _category.getAdditionalText());
+						cellSpacing = ((count > 999L) ? -3 : -2);
+					}
+				}
+				final String bgColor = (i == 0) ? "746833" : (((i % 2) == 1) ? "171612" : "23221e");
+				final String numberColor = (i == 0) ? "ffca37" : "dededf";
+				final String nameColor = (i == 0) ? "eac842" : "e2e2e0";
+				final String valueColor = (i == 0) ? "eee79f" : "a78d6c";
+				html = html + "<table width=250 bgcolor=" + bgColor + " height=42><tr>";
+				html = html + "<td width=50 align=center><font color=" + numberColor + " name=ScreenMessageLarge />" + ((i < 1) ? ("{" + (i + 1) + "}") : (i + 1)) + "</font></td>";
+				html += "<td width=200 align=left>";
+				html = html + "<table cellspacing=" + cellSpacing + "><tr><td width=200><font color=" + nameColor + " name=ScreenMessageSmall>" + name + "</font></td></tr><tr><td width=200><font color=" + valueColor + " name=ScreenMessageSmall>" + value + "</font></td></tr></table>";
+				html += "<img src=\"L2UI.SquareBlank\" width=1 height=5/></td>";
+				html += "";
+				html += "</tr></table><img src=\"L2UI.SquareGray\" width=250 height=1/>";
+			}
+			html += "</td>";
+		}
+		html = html + "<td align=center width=" + (_category.getRefreshTime().equals(RefreshTime.Total) ? 520 : 260) + ">";
+		for (int i = 0; i < 5; ++i)
+		{
+			String name = "No information.";
+			String value = "No information.";
+			int cellSpacing = -1;
+			if (_category.getAllTotalTops().size() > i)
+			{
+				final TopPlayer player = _category.getAllTotalTops().get(i + 1);
+				if (player != null)
+				{
+					name = player.getName();
+					final long count = player.getCount();
+					value = MuseumBBSManager.getInstance().convertToValue(count, _category.isTimer(), _category.getAdditionalText());
+					cellSpacing = ((count > 999L) ? -3 : -2);
+				}
+			}
+			final String bgColor = (i == 0) ? "746833" : (((i % 2) == 1) ? "171612" : "23221e");
+			final String numberColor = (i == 0) ? "ffca37" : "dededf";
+			final String nameColor = (i == 0) ? "eac842" : "e2e2e0";
+			final String valueColor = (i == 0) ? "eee79f" : "a78d6c";
+			html = html + "<table width=250 bgcolor=" + bgColor + " height=42><tr>";
+			html = html + "<td width=50 align=center><font color=" + numberColor + " name=ScreenMessageLarge />" + ((i < 1) ? ("{" + (i + 1) + "}") : (i + 1)) + "</font></td>";
+			html += "<td width=200 align=left>";
+			html = html + "<table cellspacing=" + cellSpacing + "><tr><td width=200><font color=" + nameColor + " name=ScreenMessageSmall>" + name + "</font></td></tr><tr><td width=200><font color=" + valueColor + " name=ScreenMessageSmall>" + value + "</font></td></tr></table>";
+			html += "<img src=\"L2UI.SquareBlank\" width=1 height=5/></td>";
+			html += "";
+			html += "</tr></table><img src=\"L2UI.SquareGray\" width=250 height=1/>";
+		}
+		html += "</td>";
+		html += "</tr></table><br><br></td></tr></table></td></tr></table>";
+		html += "</center>";
+		return html;
+	}
+	
+	protected void separateAndSend(final String html, final L2PcInstance acha)
+	{
+		if (html == null)
+		{
+			return;
+		}
+		if (html.length() < 4096)
+		{
+			acha.sendPacket(new ShowBoard(html, "101"));
+			acha.sendPacket(new ShowBoard((String) null, "102"));
+			acha.sendPacket(new ShowBoard((String) null, "103"));
+		}
+		else if (html.length() < 8192)
+		{
+			acha.sendPacket(new ShowBoard(html.substring(0, 4096), "101"));
+			acha.sendPacket(new ShowBoard(html.substring(4096), "102"));
+			acha.sendPacket(new ShowBoard((String) null, "103"));
+		}
+		else if (html.length() < 16384)
+		{
+			acha.sendPacket(new ShowBoard(html.substring(0, 4096), "101"));
+			acha.sendPacket(new ShowBoard(html.substring(4096, 8192), "102"));
+			acha.sendPacket(new ShowBoard(html.substring(8192), "103"));
+		}
+	}
+}
Index: java/l2r/features/museum/MuseumCategory.java
===================================================================
--- java/l2r/features/museum/MuseumCategory.java	(revision 0)
+++ java/l2r/features/museum/MuseumCategory.java	(working copy)
@@ -0,0 +1,119 @@
+package l2r.features.museum;
+
+import java.util.ArrayList;
+import java.util.HashMap;
+
+import l2r.gameserver.model.Location;
+
+public class MuseumCategory
+{
+	int _categoryId;
+	int _typeId;
+	String _categoryName;
+	String _typeName;
+	String _type;
+	String _additionalText;
+	RefreshTime _refreshTime;
+	boolean _timer;
+	HashMap<Integer, TopPlayer> _players;
+	HashMap<Integer, TopPlayer> _totalTopPlayers;
+	HashMap<Integer, TopPlayer> _statuePlayers;
+	ArrayList<L2MuseumStatueInstance> _spawnedStatues;
+	ArrayList<Location> _statueSpawns;
+	ArrayList<MuseumReward> _rewards;
+	
+	public MuseumCategory(final int categoryId, final int typeId, final String categoryName, final String typeName, final String type, final String refreshTime, final boolean timer, final String additionalText, final ArrayList<Location> statueSpawns, final ArrayList<MuseumReward> rewards)
+	{
+		_players = new HashMap<>();
+		_totalTopPlayers = new HashMap<>();
+		_statuePlayers = new HashMap<>();
+		_spawnedStatues = new ArrayList<>();
+		_categoryId = categoryId;
+		_typeId = typeId;
+		_categoryName = categoryName;
+		_typeName = typeName;
+		_type = type;
+		_timer = timer;
+		_additionalText = additionalText;
+		_statueSpawns = statueSpawns;
+		_rewards = rewards;
+		for (final RefreshTime time : RefreshTime.values())
+		{
+			if (time.name().toLowerCase().equals(refreshTime))
+			{
+				_refreshTime = time;
+				break;
+			}
+		}
+	}
+	
+	public int getCategoryId()
+	{
+		return _categoryId;
+	}
+	
+	public int getTypeId()
+	{
+		return _typeId;
+	}
+	
+	public String getCategoryName()
+	{
+		return _categoryName;
+	}
+	
+	public String getTypeName()
+	{
+		return _typeName;
+	}
+	
+	public String getType()
+	{
+		return _type;
+	}
+	
+	public String getAdditionalText()
+	{
+		return _additionalText;
+	}
+	
+	public RefreshTime getRefreshTime()
+	{
+		return _refreshTime;
+	}
+	
+	public boolean isTimer()
+	{
+		return _timer;
+	}
+	
+	public ArrayList<Location> getStatueSpawns()
+	{
+		return _statueSpawns;
+	}
+	
+	public ArrayList<MuseumReward> getRewards()
+	{
+		return _rewards;
+	}
+	
+	public HashMap<Integer, TopPlayer> getAllTops()
+	{
+		return _players;
+	}
+	
+	public HashMap<Integer, TopPlayer> getAllTotalTops()
+	{
+		return _totalTopPlayers;
+	}
+	
+	public HashMap<Integer, TopPlayer> getAllStatuePlayers()
+	{
+		return _statuePlayers;
+	}
+	
+	public ArrayList<L2MuseumStatueInstance> getAllSpawnedStatues()
+	{
+		return _spawnedStatues;
+	}
+}
Index: java/l2r/features/museum/MuseumManager.java
===================================================================
--- java/l2r/features/museum/MuseumManager.java	(revision 0)
+++ java/l2r/features/museum/MuseumManager.java	(working copy)
@@ -0,0 +1,590 @@
+package l2r.features.museum;
+
+import java.io.File;
+import java.sql.Connection;
+import java.sql.PreparedStatement;
+import java.sql.ResultSet;
+import java.util.ArrayList;
+import java.util.Calendar;
+import java.util.HashMap;
+import java.util.Map;
+
+import javax.xml.parsers.DocumentBuilderFactory;
+
+import l2r.Config;
+import l2r.L2DatabaseFactory;
+import l2r.gameserver.ThreadPoolManager;
+import l2r.gameserver.data.sql.NpcTable;
+import l2r.gameserver.instancemanager.GlobalVariablesManager;
+import l2r.gameserver.model.L2World;
+import l2r.gameserver.model.Location;
+import l2r.gameserver.model.actor.instance.L2PcInstance;
+
+import org.slf4j.Logger;
+import org.slf4j.LoggerFactory;
+import org.w3c.dom.Document;
+import org.w3c.dom.Node;
+
+public class MuseumManager
+{
+	public static final Logger _log = LoggerFactory.getLogger(MuseumManager.class);
+	private final HashMap<Integer, String> _categoryNames;
+	private final HashMap<Integer, MuseumCategory> _categories;
+	private final HashMap<Integer, ArrayList<MuseumCategory>> _categoriesByCategoryId;
+	private final HashMap<Integer, ArrayList<Integer>> _playersWithReward;
+	private int refreshTotal;
+	
+	public MuseumManager()
+	{
+		refreshTotal = 3600;
+		_categoryNames = new HashMap<>();
+		_categories = new HashMap<>();
+		_categoriesByCategoryId = new HashMap<>();
+		_playersWithReward = new HashMap<>();
+		loadCategories();
+		final long monthlyUpdate = Math.max(100L, (GlobalVariablesManager.getInstance().hasVariable("museum_monthly") ? GlobalVariablesManager.getInstance().getLong("museum_monthly") : 0L) - System.currentTimeMillis());
+		final long weeklyUpdate = Math.max(100L, (GlobalVariablesManager.getInstance().hasVariable("museum_weekly") ? GlobalVariablesManager.getInstance().getLong("museum_weekly") : 0L) - System.currentTimeMillis());
+		final long dailyUpdate = Math.max(100L, (GlobalVariablesManager.getInstance().hasVariable("museum_daily") ? GlobalVariablesManager.getInstance().getLong("museum_daily") : 0L) - System.currentTimeMillis());
+		ThreadPoolManager.getInstance().scheduleGeneralAtFixedRate(new UpdateStats(RefreshTime.Total), 100L, refreshTotal * 1000);
+		ThreadPoolManager.getInstance().scheduleGeneral(new UpdateStats(RefreshTime.Monthly), monthlyUpdate);
+		ThreadPoolManager.getInstance().scheduleGeneral(new UpdateStats(RefreshTime.Weekly), weeklyUpdate);
+		ThreadPoolManager.getInstance().scheduleGeneral(new UpdateStats(RefreshTime.Daily), dailyUpdate);
+	}
+	
+	public void giveRewards()
+	{
+		final ArrayList<Integer> withReward = new ArrayList<>();
+		for (final Map.Entry<Integer, ArrayList<Integer>> entry : _playersWithReward.entrySet())
+		{
+			final L2PcInstance player = L2World.getInstance().getPlayer(entry.getKey());
+			if (player != null)
+			{
+				if (!player.isOnline())
+				{
+					continue;
+				}
+				final ArrayList<Integer> cats = entry.getValue();
+				for (final int catId : cats)
+				{
+					if (!_categories.containsKey(catId))
+					{
+						withReward.add(entry.getKey());
+					}
+					else
+					{
+						final MuseumCategory cat = _categories.get(catId);
+						if (cat == null)
+						{
+							withReward.add(entry.getKey());
+						}
+						else
+						{
+							for (final MuseumReward reward : cat.getRewards())
+							{
+								reward.giveReward(player);
+								withReward.add(entry.getKey());
+							}
+						}
+					}
+				}
+			}
+		}
+		for (final int i : withReward)
+		{
+			_playersWithReward.remove(i);
+		}
+		if (_playersWithReward.size() == 0)
+		{
+			return;
+		}
+	}
+	
+	public void giveReward(final L2PcInstance player)
+	{
+		if (!_playersWithReward.containsKey(player.getObjectId()))
+		{
+			return;
+		}
+		final ArrayList<Integer> cats = _playersWithReward.get(player.getObjectId());
+		if (cats.size() < 1)
+		{
+			_playersWithReward.remove(player.getObjectId());
+			return;
+		}
+		for (final int catId : cats)
+		{
+			if (!_categories.containsKey(catId))
+			{
+				continue;
+			}
+			final MuseumCategory cat = _categories.get(catId);
+			for (final MuseumReward reward : cat.getRewards())
+			{
+				reward.giveReward(player);
+			}
+		}
+		_playersWithReward.remove(player.getObjectId());
+	}
+	
+	public void restoreLastTops(final RefreshTime time)
+	{
+		for (final MuseumCategory cat : getAllCategories().values())
+		{
+			int i = 1;
+			if (!cat.getRefreshTime().equals(time) && !time.equals(RefreshTime.Total))
+			{
+				continue;
+			}
+			cat.getAllStatuePlayers().clear();
+			try (final Connection con = L2DatabaseFactory.getInstance().getConnection();
+				final PreparedStatement statement = con.prepareStatement("SELECT * FROM museum_last_statistics as mls INNER JOIN museum_statistics as ms ON ms.objectId=mls.objectId WHERE mls.category = ? AND mls.category = ms.category AND mls.count > 0 AND mls.timer = '" + cat.getRefreshTime().name().toLowerCase() + "' ORDER BY mls.count DESC LIMIT 5"))
+			{
+				statement.setString(1, cat.getType());
+				try (final ResultSet rset = statement.executeQuery())
+				{
+					while (rset.next())
+					{
+						final int objectId = rset.getInt("objectId");
+						final String name = rset.getString("name");
+						final long count = rset.getLong("count");
+						cat.getAllStatuePlayers().put(i, new TopPlayer(objectId, name, count));
+						if (i == 1)
+						{
+							spawnStatue(cat);
+						}
+						++i;
+					}
+				}
+			}
+			catch (Exception e)
+			{
+				_log.error("Failed loading character museum data.", e);
+			}
+		}
+	}
+	
+	public void spawnStatue(final MuseumCategory cat)
+	{
+		for (final L2MuseumStatueInstance statue : cat.getAllSpawnedStatues())
+		{
+			statue.deleteMe();
+		}
+		cat.getAllSpawnedStatues().clear();
+		if (cat.getAllStatuePlayers().size() > 0)
+		{
+			final TopPlayer player = cat.getAllStatuePlayers().get(1);
+			for (final Location loc : cat.getStatueSpawns())
+			{
+				final L2MuseumStatueInstance statue2 = new L2MuseumStatueInstance(NpcTable.getInstance().getTemplate(30001), player.getObjectId(), (cat.getCategoryId() * 256) + cat.getTypeId());
+				statue2.setXYZ(loc);
+				statue2.setHeading(loc.getHeading());
+				statue2.spawnMe();
+				cat.getAllSpawnedStatues().add(statue2);
+			}
+		}
+	}
+	
+	public void cleanLastTops(final RefreshTime time)
+	{
+		try (final Connection con = L2DatabaseFactory.getInstance().getConnection();
+			final PreparedStatement statement = con.prepareStatement("DELETE FROM museum_last_statistics WHERE timer='" + time.name().toLowerCase() + "'"))
+		{
+			statement.execute();
+		}
+		catch (Exception e)
+		{
+			_log.warn("Could not store char museum data: " + e.getMessage(), e);
+		}
+	}
+	
+	public void refreshTopsFromDatabase(final RefreshTime time)
+	{
+		_playersWithReward.clear();
+		try (final Connection con = L2DatabaseFactory.getInstance().getConnection())
+		{
+			for (final MuseumCategory cat : _categories.values())
+			{
+				if (!cat.getRefreshTime().equals(time) && !time.equals(RefreshTime.Total))
+				{
+					continue;
+				}
+				cat.getAllTops().clear();
+				cat.getAllTotalTops().clear();
+				cat.getAllStatuePlayers().clear();
+				int i = 1;
+				int h = 1;
+				try (
+					final PreparedStatement statement = con.prepareStatement("SELECT * FROM museum_statistics WHERE category = ? AND " + cat.getRefreshTime().name().toLowerCase() + "_count > 0 ORDER BY " + cat.getRefreshTime().name().toLowerCase() + "_count DESC LIMIT " + (cat.getRefreshTime().equals(RefreshTime.Total) ? 20 : 10)))
+				{
+					statement.setString(1, cat.getType());
+					try (final ResultSet rset = statement.executeQuery())
+					{
+						while (rset.next())
+						{
+							final int objectId = rset.getInt("objectId");
+							final String name = rset.getString("name");
+							final long count = rset.getLong(cat.getRefreshTime().name().toLowerCase() + "_count");
+							final boolean hasReward = rset.getBoolean("hasReward");
+							if (hasReward)
+							{
+								if (!_playersWithReward.containsKey(objectId))
+								{
+									_playersWithReward.put(objectId, new ArrayList<Integer>());
+								}
+								_playersWithReward.get(objectId).add((cat.getCategoryId() * 256) + cat.getTypeId());
+							}
+							if (cat.getRefreshTime().equals(RefreshTime.Total))
+							{
+								cat.getAllTotalTops().put(i, new TopPlayer(objectId, name, count));
+							}
+							else
+							{
+								cat.getAllTops().put(i, new TopPlayer(objectId, name, count));
+							}
+							if ((i < 6) && time.equals(cat.getRefreshTime()))
+							{
+								try (final PreparedStatement stat = con.prepareStatement("REPLACE museum_last_statistics SET objectId=" + objectId + ", name='" + name + "', category='" + cat.getType() + "', count=" + count + ", timer='" + time.name().toLowerCase() + "';"))
+								{
+									stat.execute();
+								}
+								if (i == 1)
+								{
+									try (final PreparedStatement stat = con.prepareStatement("UPDATE museum_statistics SET hasReward = 1 WHERE objectId = " + objectId + " AND category = '" + cat.getType() + "'"))
+									{
+										stat.execute();
+									}
+									if (!_playersWithReward.containsKey(objectId))
+									{
+										_playersWithReward.put(objectId, new ArrayList<Integer>());
+									}
+									_playersWithReward.get(objectId).add((cat.getCategoryId() * 256) + cat.getTypeId());
+								}
+							}
+							++i;
+						}
+					}
+				}
+				if (cat.getRefreshTime().equals(RefreshTime.Total))
+				{
+					continue;
+				}
+				try (final PreparedStatement statement = con.prepareStatement("SELECT * FROM museum_statistics WHERE category = ? AND total_count > 0 ORDER BY total_count DESC LIMIT 10"))
+				{
+					statement.setString(1, cat.getType());
+					try (final ResultSet rset = statement.executeQuery())
+					{
+						while (rset.next())
+						{
+							final int objectId = rset.getInt("objectId");
+							final String name = rset.getString("name");
+							final long count = rset.getLong("total_count");
+							cat.getAllTotalTops().put(h, new TopPlayer(objectId, name, count));
+							++h;
+						}
+					}
+				}
+			}
+			if (!time.equals(RefreshTime.Total))
+			{
+				try (final PreparedStatement statement2 = con.prepareStatement("UPDATE museum_statistics SET " + time.name().toLowerCase() + "_count = 0"))
+				{
+					statement2.execute();
+				}
+			}
+		}
+		catch (Exception e)
+		{
+			_log.warn("Could not store char museum data: " + e.getMessage(), e);
+		}
+		restoreLastTops(time);
+		giveRewards();
+	}
+	
+	public void loadCategories()
+	{
+		_log.info(this.getClass().getSimpleName() + ": Initializing");
+		_categoryNames.clear();
+		_categories.clear();
+		_categoriesByCategoryId.clear();
+		final DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
+		factory.setValidating(false);
+		factory.setIgnoringComments(true);
+		final File file = new File(Config.DATAPACK_ROOT, "data/xml/sunrise/MuseumCategories.xml");
+		Document doc = null;
+		if (file.exists())
+		{
+			try
+			{
+				doc = factory.newDocumentBuilder().parse(file);
+			}
+			catch (Exception e)
+			{
+				_log.warn("Could not parse MuseumCategories.xml file: " + e.getMessage(), e);
+				return;
+			}
+			int categoryId = 0;
+			final Node n = doc.getFirstChild();
+			for (Node d = n.getFirstChild(); d != null; d = d.getNextSibling())
+			{
+				if (d.getNodeName().equalsIgnoreCase("set"))
+				{
+					final String name = d.getAttributes().getNamedItem("name").getNodeValue();
+					final String val = d.getAttributes().getNamedItem("val").getNodeValue();
+					if (name.equalsIgnoreCase("refreshAllStatisticsIn"))
+					{
+						refreshTotal = Integer.parseInt(val);
+					}
+				}
+				if (d.getNodeName().equalsIgnoreCase("category"))
+				{
+					final ArrayList<MuseumCategory> list = new ArrayList<>();
+					final String categoryName = d.getAttributes().getNamedItem("name").getNodeValue();
+					int typeId = 0;
+					for (Node h = d.getFirstChild(); h != null; h = h.getNextSibling())
+					{
+						if (h.getNodeName().equalsIgnoreCase("type"))
+						{
+							final String typeName = h.getAttributes().getNamedItem("name").getNodeValue();
+							final String type = h.getAttributes().getNamedItem("type").getNodeValue();
+							final String refreshTime = h.getAttributes().getNamedItem("refreshTime").getNodeValue();
+							boolean timer = false;
+							if (h.getAttributes().getNamedItem("timer") != null)
+							{
+								timer = Boolean.parseBoolean(h.getAttributes().getNamedItem("timer").getNodeValue());
+							}
+							String additionalText = "";
+							if (h.getAttributes().getNamedItem("additionalText") != null)
+							{
+								additionalText = h.getAttributes().getNamedItem("additionalText").getNodeValue();
+							}
+							final ArrayList<Location> statueSpawns = new ArrayList<>();
+							final ArrayList<MuseumReward> rewards = new ArrayList<>();
+							int rewardId = 0;
+							for (Node a = h.getFirstChild(); a != null; a = a.getNextSibling())
+							{
+								if (a.getNodeName().equalsIgnoreCase("spawn"))
+								{
+									final int x = Integer.parseInt(a.getAttributes().getNamedItem("x").getNodeValue());
+									final int y = Integer.parseInt(a.getAttributes().getNamedItem("y").getNodeValue());
+									final int z = Integer.parseInt(a.getAttributes().getNamedItem("z").getNodeValue());
+									final int heading = (a.getAttributes().getNamedItem("heading") != null) ? Integer.parseInt(a.getAttributes().getNamedItem("heading").getNodeValue()) : 0;
+									statueSpawns.add(new Location(x, y, z, heading));
+								}
+								if (a.getNodeName().equalsIgnoreCase("reward"))
+								{
+									String rewardType = "";
+									int itemId = 0;
+									int minCount = 0;
+									int maxCount = 0;
+									double chance = 0.0;
+									if (a.getAttributes().getNamedItem("type") != null)
+									{
+										rewardType = a.getAttributes().getNamedItem("type").getNodeValue();
+									}
+									if (a.getAttributes().getNamedItem("id") != null)
+									{
+										itemId = Integer.parseInt(a.getAttributes().getNamedItem("id").getNodeValue());
+									}
+									if (a.getAttributes().getNamedItem("min") != null)
+									{
+										minCount = Integer.parseInt(a.getAttributes().getNamedItem("min").getNodeValue());
+									}
+									if (a.getAttributes().getNamedItem("max") != null)
+									{
+										maxCount = Integer.parseInt(a.getAttributes().getNamedItem("max").getNodeValue());
+									}
+									if (a.getAttributes().getNamedItem("chance") != null)
+									{
+										chance = Double.parseDouble(a.getAttributes().getNamedItem("chance").getNodeValue());
+									}
+									rewards.add(new MuseumReward(rewardId, rewardType, itemId, minCount, maxCount, chance));
+									++rewardId;
+								}
+							}
+							final int key = (categoryId * 256) + typeId;
+							final MuseumCategory category = new MuseumCategory(categoryId, typeId, categoryName, typeName, type, refreshTime, timer, additionalText, statueSpawns, rewards);
+							list.add(category);
+							_categories.put(key, category);
+							++typeId;
+						}
+					}
+					_categoriesByCategoryId.put(categoryId, list);
+					_categoryNames.put(categoryId, categoryName);
+					++categoryId;
+				}
+			}
+		}
+		_log.info(this.getClass().getSimpleName() + ": Successfully loaded " + _categoryNames.size() + " categories and " + _categories.size() + " post categories.");
+	}
+	
+	public HashMap<Integer, String> getAllCategoryNames()
+	{
+		return _categoryNames;
+	}
+	
+	public HashMap<Integer, MuseumCategory> getAllCategories()
+	{
+		return _categories;
+	}
+	
+	public ArrayList<MuseumCategory> getAllCategoriesByCategoryId(final int id)
+	{
+		if (_categoriesByCategoryId.containsKey(id))
+		{
+			return _categoriesByCategoryId.get(id);
+		}
+		return null;
+	}
+	
+	public void restoreDataForChar(final L2PcInstance player)
+	{
+		final HashMap<String, long[]> data = new HashMap<>();
+		try (final Connection con = L2DatabaseFactory.getInstance().getConnection();
+			final PreparedStatement statement = con.prepareStatement("SELECT * FROM museum_statistics WHERE objectId = ?"))
+		{
+			statement.setInt(1, player.getObjectId());
+			try (final ResultSet rset = statement.executeQuery())
+			{
+				while (rset.next())
+				{
+					final long[] d =
+					{
+						rset.getLong("total_count"),
+						rset.getLong("monthly_count"),
+						rset.getLong("weekly_count"),
+						rset.getLong("daily_count")
+					};
+					final String category = rset.getString("category");
+					data.put(category, d);
+				}
+			}
+		}
+		catch (Exception e)
+		{
+			_log.error("Failed loading character museum data.", e);
+		}
+		player.setMuseumPlayer(new MuseumPlayer(player.getObjectId(), player.getName(), data));
+	}
+	
+	public void updateDataForChar(final L2PcInstance player)
+	{
+		if (player.getMuseumPlayer() == null)
+		{
+			return;
+		}
+		String update = "";
+		try (final Connection con = L2DatabaseFactory.getInstance().getConnection())
+		{
+			for (final Map.Entry<String, long[]> entry : player.getMuseumPlayer().getData().entrySet())
+			{
+				update = "REPLACE museum_statistics SET objectId=" + player.getObjectId() + ", name='" + player.getName() + "', category='" + entry.getKey() + "', total_count=" + entry.getValue()[0] + ", monthly_count=" + entry.getValue()[1] + ", weekly_count=" + entry.getValue()[2] + ", daily_count=" + entry.getValue()[3] + ", hasReward=0;";
+				try (final PreparedStatement statement = con.prepareStatement(update))
+				{
+					statement.execute();
+				}
+			}
+		}
+		catch (Exception e)
+		{
+			_log.warn("Could not store char museum data: " + e.getMessage(), e);
+		}
+	}
+	
+	public void reloadConfigs()
+	{
+		loadCategories();
+		restoreLastTops(RefreshTime.Total);
+	}
+	
+	public static MuseumManager getInstance()
+	{
+		return SingletonHolder._instance;
+	}
+	
+	public class UpdateStats implements Runnable
+	{
+		RefreshTime _time;
+		
+		public UpdateStats(final RefreshTime time)
+		{
+			_time = time;
+		}
+		
+		@Override
+		public void run()
+		{
+			long time = 0L;
+			switch (_time)
+			{
+				case Monthly:
+				{
+					final Calendar c = Calendar.getInstance();
+					c.set(2, c.get(2) + 1);
+					c.set(5, 1);
+					c.set(11, 0);
+					c.set(12, 0);
+					c.set(13, 0);
+					time = Math.max(100L, c.getTimeInMillis() - System.currentTimeMillis());
+					GlobalVariablesManager.getInstance().set("museum_monthly", c.getTimeInMillis());
+					ThreadPoolManager.getInstance().scheduleGeneral(new UpdateStats(RefreshTime.Monthly), time);
+					cleanLastTops(_time);
+					break;
+				}
+				case Weekly:
+				{
+					final Calendar c = Calendar.getInstance();
+					c.set(7, 2);
+					c.set(11, 0);
+					c.set(12, 0);
+					c.set(13, 0);
+					if (c.getTimeInMillis() < System.currentTimeMillis())
+					{
+						c.setTimeInMillis(c.getTimeInMillis() + 604800000L);
+					}
+					time = Math.max(100L, c.getTimeInMillis() - System.currentTimeMillis());
+					GlobalVariablesManager.getInstance().set("museum_weekly", c.getTimeInMillis());
+					ThreadPoolManager.getInstance().scheduleGeneral(new UpdateStats(_time), time);
+					cleanLastTops(_time);
+					break;
+				}
+				case Daily:
+				{
+					final Calendar c = Calendar.getInstance();
+					c.set(6, c.get(6) + 1);
+					c.set(11, 0);
+					c.set(12, 0);
+					c.set(13, 0);
+					time = Math.max(100L, c.getTimeInMillis() - System.currentTimeMillis());
+					GlobalVariablesManager.getInstance().set("museum_daily", c.getTimeInMillis());
+					ThreadPoolManager.getInstance().scheduleGeneral(new UpdateStats(_time), time);
+					cleanLastTops(_time);
+					break;
+				}
+			}
+			refreshTops();
+			restoreLastTops(RefreshTime.Total);
+		}
+		
+		public void refreshTops()
+		{
+			for (final L2PcInstance player : L2World.getInstance().getPlayers())
+			{
+				if (player.getMuseumPlayer() != null)
+				{
+					player.getMuseumPlayer().resetData(_time);
+				}
+			}
+			refreshTopsFromDatabase(_time);
+		}
+	}
+	
+	private static class SingletonHolder
+	{
+		protected static final MuseumManager _instance;
+		
+		static
+		{
+			_instance = new MuseumManager();
+		}
+	}
+}
Index: java/l2r/features/museum/MuseumPlayer.java
===================================================================
--- java/l2r/features/museum/MuseumPlayer.java	(revision 0)
+++ java/l2r/features/museum/MuseumPlayer.java	(working copy)
@@ -0,0 +1,92 @@
+package l2r.features.museum;
+
+import java.util.HashMap;
+import java.util.Map;
+
+public class MuseumPlayer
+{
+	int _objectId;
+	String _name;
+	HashMap<String, long[]> _data;
+	
+	public MuseumPlayer(final int objectId, final String name, final HashMap<String, long[]> data)
+	{
+		_objectId = objectId;
+		_name = name;
+		_data = data;
+	}
+	
+	public long getData(final RefreshTime time, final String type)
+	{
+		if (!_data.containsKey(type))
+		{
+			return 0L;
+		}
+		return _data.get(type)[time.ordinal()];
+	}
+	
+	public long[] getData(final String type)
+	{
+		if (!_data.containsKey(type))
+		{
+			return null;
+		}
+		return _data.get(type);
+	}
+	
+	public void resetData(final RefreshTime time)
+	{
+		if (time.equals(RefreshTime.Total))
+		{
+			return;
+		}
+		final HashMap<String, long[]> data = new HashMap<>();
+		long[] d =
+		{
+			0L,
+			0L,
+			0L,
+			0L
+		};
+		for (final Map.Entry<String, long[]> entry : _data.entrySet())
+		{
+			d = entry.getValue();
+			d[time.ordinal()] = 0L;
+			data.put(entry.getKey(), d);
+		}
+		_data = data;
+	}
+	
+	public HashMap<String, long[]> getData()
+	{
+		return _data;
+	}
+	
+	public void addData(final String type, final long data)
+	{
+		long[] d =
+		{
+			0L,
+			0L,
+			0L,
+			0L
+		};
+		if (this.getData(type) != null)
+		{
+			d = this.getData(type);
+		}
+		final long[] array = d;
+		final int n = 0;
+		array[n] += data;
+		final long[] array2 = d;
+		final int n2 = 1;
+		array2[n2] += data;
+		final long[] array3 = d;
+		final int n3 = 2;
+		array3[n3] += data;
+		final long[] array4 = d;
+		final int n4 = 3;
+		array4[n4] += data;
+		_data.put(type, d);
+	}
+}
Index: java/l2r/features/museum/MuseumReward.java
===================================================================
--- java/l2r/features/museum/MuseumReward.java	(revision 0)
+++ java/l2r/features/museum/MuseumReward.java	(working copy)
@@ -0,0 +1,172 @@
+package l2r.features.museum;
+
+import l2r.gameserver.idfactory.IdFactory;
+import l2r.gameserver.model.actor.instance.L2PcInstance;
+import l2r.gameserver.model.items.instance.L2ItemInstance;
+import l2r.util.Rnd;
+
+public class MuseumReward
+{
+	String type;
+	int id;
+	int itemId;
+	int minCount;
+	int maxCount;
+	double chance;
+	
+	public MuseumReward(final int _id, final String _type, final int _itemId, final int _minCount, final int _maxCount, final double _chance)
+	{
+		id = _id;
+		type = _type;
+		itemId = _itemId;
+		minCount = _minCount;
+		maxCount = _maxCount;
+		chance = _chance;
+	}
+	
+	public int getId()
+	{
+		return id;
+	}
+	
+	public String getType()
+	{
+		return type;
+	}
+	
+	public void setType(final String _type)
+	{
+		type = _type;
+	}
+	
+	public int getItemId()
+	{
+		return itemId;
+	}
+	
+	public void setItemId(final int _itemId)
+	{
+		itemId = _itemId;
+	}
+	
+	public int getMinCount()
+	{
+		return minCount;
+	}
+	
+	public void setMinCount(final int _minCount)
+	{
+		minCount = _minCount;
+	}
+	
+	public int getMaxCount()
+	{
+		return maxCount;
+	}
+	
+	public void setMaxCount(final int _maxCount)
+	{
+		maxCount = _maxCount;
+	}
+	
+	public double getChance()
+	{
+		return chance;
+	}
+	
+	public void setChance(final double _chance)
+	{
+		chance = _chance;
+	}
+	
+	public void giveReward(final L2PcInstance player)
+	{
+		if (getType().equalsIgnoreCase("item"))
+		{
+			giveItems(player);
+		}
+		else if (getType().equalsIgnoreCase("clanpoints"))
+		{
+			giveClanPoints(player);
+		}
+		else if (getType().equalsIgnoreCase("skillpoints"))
+		{
+			giveSkillPoints(player);
+		}
+		else if (getType().equalsIgnoreCase("experience"))
+		{
+			giveExperience(player);
+		}
+	}
+	
+	private void giveClanPoints(final L2PcInstance player)
+	{
+		if (player.getClan() == null)
+		{
+			return;
+		}
+		final double _chance = getChance() * 1000.0;
+		final int min = getMinCount();
+		int max = getMaxCount();
+		if (min > max)
+		{
+			max = min;
+		}
+		final int count = Rnd.get(min, max);
+		if ((Rnd.get(0, 100000) < _chance) && (player.getClan() != null))
+		{
+			player.getClan().addReputationScore(count, true);
+		}
+	}
+	
+	private void giveSkillPoints(final L2PcInstance player)
+	{
+		final double _chance = getChance() * 1000.0;
+		final int min = getMinCount();
+		int max = getMaxCount();
+		if (min > max)
+		{
+			max = min;
+		}
+		final int count = Rnd.get(min, max);
+		if (Rnd.get(0, 100000) <= _chance)
+		{
+			player.getStat().addExpAndSp(0L, count, false);
+		}
+	}
+	
+	private void giveExperience(final L2PcInstance player)
+	{
+		final double _chance = getChance() * 1000.0;
+		final int min = getMinCount();
+		int max = getMaxCount();
+		if (min > max)
+		{
+			max = min;
+		}
+		final long count = Rnd.get(min, max);
+		if (Rnd.get(0, 100000) <= _chance)
+		{
+			player.getStat().addExpAndSp(count, 0, false);
+		}
+	}
+	
+	private void giveItems(final L2PcInstance player)
+	{
+		final double _chance = getChance() * 1000.0;
+		final int _itemId = getItemId();
+		final int min = getMinCount();
+		int max = getMaxCount();
+		if (min > max)
+		{
+			max = min;
+		}
+		final int count = Rnd.get(min, max);
+		if (Rnd.get(0, 100000) < _chance)
+		{
+			final L2ItemInstance item = new L2ItemInstance(IdFactory.getInstance().getNextId(), _itemId);
+			item.setCount(count);
+			player.addItem("RewardItem", item, player, true);
+		}
+	}
+}
Index: java/l2r/features/museum/RefreshTime.java
===================================================================
--- java/l2r/features/museum/RefreshTime.java	(revision 0)
+++ java/l2r/features/museum/RefreshTime.java	(working copy)
@@ -0,0 +1,9 @@
+package l2r.features.museum;
+
+public enum RefreshTime
+{
+	Total,
+	Monthly,
+	Weekly,
+	Daily;
+}
Index: java/l2r/features/museum/TopPlayer.java
===================================================================
--- java/l2r/features/museum/TopPlayer.java	(revision 0)
+++ java/l2r/features/museum/TopPlayer.java	(working copy)
@@ -0,0 +1,30 @@
+package l2r.features.museum;
+
+public class TopPlayer
+{
+	int _objectId;
+	long _count;
+	String _name;
+	
+	public TopPlayer(final int objectId, final String name, final long count)
+	{
+		_objectId = objectId;
+		_name = name;
+		_count = count;
+	}
+	
+	public int getObjectId()
+	{
+		return _objectId;
+	}
+	
+	public String getName()
+	{
+		return _name;
+	}
+	
+	public long getCount()
+	{
+		return _count;
+	}
+}
Index: java/l2r/features/museum/RefreshTime.java
===================================================================
--- java/l2r/features/museum/RefreshTime.java	(revision 0)
+++ java/l2r/features/museum/RefreshTime.java	(working copy)
@@ -0,0 +1,9 @@
+package l2r.features.museum;
+
+public enum RefreshTime
+{
+	Total,
+	Monthly,
+	Weekly,
+	Daily;
+}
Index: java/l2r/gameserver/network/clientpackets/RequestPrivateStoreBuy.java
===================================================================
--- java/l2r/gameserver/network/clientpackets/RequestPrivateStoreBuy.java	(revision 1211)
+++ java/l2r/gameserver/network/clientpackets/RequestPrivateStoreBuy.java	(working copy)
@@ -150,7 +150,7 @@
 			}
 			return;
 		}
-		
+		storePlayer.getMuseumPlayer().addData("private_stores_sales", _items.size());
 		if (storeList.getItemCount() == 0)
 		{
 			storePlayer.setPrivateStoreType(PrivateStoreType.NONE);
Index: java/l2r/gameserver/model/actor/stat/PcStat.java
===================================================================
--- java/l2r/gameserver/model/actor/stat/PcStat.java	(revision 1211)
+++ java/l2r/gameserver/model/actor/stat/PcStat.java	(working copy)
@@ -219,7 +219,7 @@
 		{
 			return false;
 		}
-		
+		activeChar.getMuseumPlayer().addData("xp", addToExp);
 		SystemMessage sm = null;
 		if ((addToExp == 0) && (addToSp != 0))
 		{
Index: java/l2r/gameserver/model/actor/instance/L2RaidBossInstance.java
===================================================================
--- java/l2r/gameserver/model/actor/instance/L2RaidBossInstance.java	(revision 1211)
+++ java/l2r/gameserver/model/actor/instance/L2RaidBossInstance.java	(working copy)
@@ -127,6 +127,7 @@
 					{
 						Hero.getInstance().setRBkilled(member.getObjectId(), getId());
 					}
+					member.getMuseumPlayer().addData("raid_kill_" + getId(), 1);
 				}
 			}
 			else
@@ -136,6 +137,7 @@
 				{
 					Hero.getInstance().setRBkilled(player.getObjectId(), getId());
 				}
+				player.getMuseumPlayer().addData("raid_kill_" + getId(), 1);
 			}
 			
 			RaidManager.getInstance().onRaidDeath(this, player);
Index: java/l2r/gameserver/GameServer.java
===================================================================
--- java/l2r/gameserver/GameServer.java	(revision 1211)
+++ java/l2r/gameserver/GameServer.java	(working copy)
@@ -33,6 +33,7 @@
 import l2r.L2DatabaseFactory;
 import l2r.Server;
 import l2r.UPnPService;
+import l2r.features.museum.MuseumManager;
 import l2r.gameserver.cache.HtmCache;
 import l2r.gameserver.dao.factory.impl.DAOFactory;
 import l2r.gameserver.data.EventDroplist;
@@ -383,6 +384,7 @@
 		AutoSpawnHandler.getInstance();
 		
 		FaenorScriptEngine.getInstance();
+		MuseumManager.getInstance();
 		TaskManager.getInstance();
 		AntiFeedManager.getInstance().registerEvent(AntiFeedManager.GAME_ID);
 		PunishmentManager.getInstance();
Index: java/l2r/gameserver/network/serverpackets/AbstractNpcInfo.java
===================================================================
--- java/l2r/gameserver/network/serverpackets/AbstractNpcInfo.java	(revision 1211)
+++ java/l2r/gameserver/network/serverpackets/AbstractNpcInfo.java	(working copy)
@@ -21,6 +21,7 @@
 import java.text.DecimalFormat;
 
 import l2r.Config;
+import l2r.features.museum.L2MuseumStatueInstance;
 import l2r.gameserver.data.sql.ClanTable;
 import l2r.gameserver.data.xml.impl.PlayerTemplateData;
 import l2r.gameserver.enums.PcCondOverride;
@@ -38,6 +39,7 @@
 import l2r.gameserver.model.actor.instance.L2TrapInstance;
 import l2r.gameserver.model.actor.templates.L2PcTemplate;
 import l2r.gameserver.model.effects.AbnormalEffect;
+import l2r.gameserver.model.itemcontainer.Inventory;
 import l2r.gameserver.model.zone.type.L2TownZone;
 
 import gr.sr.datatables.FakePcsTable;
@@ -155,16 +157,161 @@
 			_displayEffect = cha.getDisplayEffect();
 		}
 		
+		private static final int[] PAPERDOLL_ORDER = new int[]
+		{
+			Inventory.PAPERDOLL_UNDER,
+			Inventory.PAPERDOLL_HEAD,
+			Inventory.PAPERDOLL_RHAND,
+			Inventory.PAPERDOLL_LHAND,
+			Inventory.PAPERDOLL_GLOVES,
+			Inventory.PAPERDOLL_CHEST,
+			Inventory.PAPERDOLL_LEGS,
+			Inventory.PAPERDOLL_FEET,
+			Inventory.PAPERDOLL_CLOAK,
+			Inventory.PAPERDOLL_RHAND,
+			Inventory.PAPERDOLL_HAIR,
+			Inventory.PAPERDOLL_HAIR2,
+			Inventory.PAPERDOLL_RBRACELET,
+			Inventory.PAPERDOLL_LBRACELET,
+			Inventory.PAPERDOLL_DECO1,
+			Inventory.PAPERDOLL_DECO2,
+			Inventory.PAPERDOLL_DECO3,
+			Inventory.PAPERDOLL_DECO4,
+			Inventory.PAPERDOLL_DECO5,
+			Inventory.PAPERDOLL_DECO6,
+			Inventory.PAPERDOLL_BELT
+		};
+		
+		@SuppressWarnings("unused")
 		@Override
 		protected void writeImpl()
 		{
 			FakePc fpc = FakePcsTable.getInstance().getFakePc(_npc.getId());
-			if (fpc != null)
+			if (_npc instanceof L2MuseumStatueInstance)
 			{
+				L2MuseumStatueInstance statue = (L2MuseumStatueInstance) _npc;
 				writeC(0x31);
 				writeD(_x);
 				writeD(_y);
 				writeD(_z);
+				writeD(0);
+				writeD(_npc.getObjectId());
+				writeS(statue.getCharLooks().getName());
+				writeD(statue.getCharLooks().getRace());
+				writeD(statue.getCharLooks().getSex());
+				writeD(statue.getCharLooks().getBaseClassId());
+				
+				for (int slot : PAPERDOLL_ORDER)
+				{
+					writeD(statue.getCharLooks().getPaperdollItemId(slot));
+				}
+				
+				for (int element : PAPERDOLL_ORDER)
+				{
+					writeD(0);
+				}
+				
+				writeD(0);
+				writeD(0);
+				
+				writeD(0);
+				writeD(0);
+				
+				writeD(_mAtkSpd);
+				writeD(_pAtkSpd);
+				
+				writeD(0x00); // ?
+				
+				writeD(_runSpd);
+				writeD(_walkSpd);
+				writeD(_swimRunSpd);
+				writeD(_swimWalkSpd);
+				writeD(_flyRunSpd);
+				writeD(_flyWalkSpd);
+				writeD(_flyRunSpd);
+				writeD(_flyWalkSpd);
+				writeF(_moveMultiplier);
+				writeF(0);
+				L2PcTemplate cl = PlayerTemplateData.getInstance().getTemplate(statue.getCharLooks().getBaseClassId());
+				writeF(statue.getCharLooks().getSex() == 1 ? cl.getFCollisionRadiusFemale() : cl.getfCollisionRadius());
+				writeF(statue.getCharLooks().getSex() == 1 ? cl.getFCollisionHeightFemale() : cl.getfCollisionHeight());
+				
+				writeD(statue.getCharLooks().getHairStyle());
+				writeD(statue.getCharLooks().getHairColor());
+				writeD(statue.getCharLooks().getFace());
+				
+				writeS(_npc.getTitle());
+				
+				writeD(0);
+				writeD(0);
+				writeD(0);
+				writeD(0);
+				
+				writeC(1); // standing = 1 sitting = 0
+				writeC(0); // running = 1 walking = 0
+				writeC(0);
+				
+				writeC(0);
+				
+				writeC(0); // invisible = 1 visible =0
+				
+				writeC(0); // 1-on Strider, 2-on Wyvern, 3-on Great Wolf, 0-no mount
+				writeC(1);
+				
+				writeH(0);
+				
+				writeC(0);
+				
+				writeD(AbnormalEffect.HOLD_1.getMask());
+				
+				writeC(0);
+				
+				writeH(0); // Blue value for name (0 = white, 255 = pure blue)
+				writeD(0);
+				writeD(statue.getCharLooks().getClassId());
+				writeD(0x00); // ?
+				writeC(statue.getCharLooks().getEnchantEffect());
+				
+				writeC(0);
+				
+				writeD(0);
+				writeC(0); // Symbol on char menu ctrl+I
+				writeC(0); // Hero Aura
+				
+				writeC(0); // 0x01: Fishing Mode (Cant be undone by setting back to 0)
+				writeD(0);
+				writeD(0);
+				writeD(0);
+				
+				writeD(0xFFFFFF);
+				
+				writeD(_heading);
+				
+				writeD(0);
+				writeD(0);
+				
+				writeD(0x7d7d7b);
+				
+				writeD(0);
+				
+				writeD(0);
+				
+				// T1
+				writeD(0);
+				writeD(0);
+				
+				// T2
+				writeD(0x01);
+				
+				// T2.3
+				writeD(0);
+			}
+			else if (fpc != null)
+			{
+				writeC(0x31);
+				writeD(_x);
+				writeD(_y);
+				writeD(_z);
 				writeD(0x00); // vehicle id
 				writeD(_npc.getObjectId());
 				writeS(fpc.name); // visible name
Index: java/l2r/gameserver/model/actor/L2Npc.java
===================================================================
--- java/l2r/gameserver/model/actor/L2Npc.java	(revision 1211)
+++ java/l2r/gameserver/model/actor/L2Npc.java	(working copy)
@@ -13,6 +13,7 @@
 import java.util.concurrent.ConcurrentHashMap;
 
 import l2r.Config;
+import l2r.features.museum.L2MuseumStatueInstance;
 import l2r.gameserver.ItemsAutoDestroy;
 import l2r.gameserver.SevenSigns;
 import l2r.gameserver.SevenSignsFestival;
@@ -328,7 +329,7 @@
 	 */
 	public boolean hasRandomAnimation()
 	{
-		return ((Config.MAX_NPC_ANIMATION > 0) && _isRandomAnimationEnabled && !getAiType().equals(AIType.CORPSE));
+		return ((Config.MAX_NPC_ANIMATION > 0) && _isRandomAnimationEnabled && !getAiType().equals(AIType.CORPSE)) && !(this instanceof L2MuseumStatueInstance);
 	}
 	
 	/**
Index: java/l2r/gameserver/enums/InstanceType.java
===================================================================
--- java/l2r/gameserver/enums/InstanceType.java	(revision 1211)
+++ java/l2r/gameserver/enums/InstanceType.java	(working copy)
@@ -110,7 +110,8 @@
 	L2AioNpcInstance(L2Npc),
 	L2ServicesManagerInstance(L2NpcInstance),
 	L2DonateManagerInstance(L2Npc),
-	L2CustomGatekeeperInstance(L2Npc);
+	L2CustomGatekeeperInstance(L2Npc),
+	L2MuseumStatueInstance(L2Npc);
 	
 	private final InstanceType _parent;
 	private final long _typeL;
Index: java/l2r/gameserver/communitybbs/Managers/MuseumBBSManager.java
===================================================================
--- java/l2r/gameserver/communitybbs/Managers/MuseumBBSManager.java	(revision 0)
+++ java/l2r/gameserver/communitybbs/Managers/MuseumBBSManager.java	(working copy)
@@ -0,0 +1,471 @@
+package l2r.gameserver.communitybbs.Managers;
+
+import java.text.NumberFormat;
+import java.util.ArrayList;
+import java.util.HashMap;
+import java.util.Locale;
+import java.util.Map;
+import java.util.StringTokenizer;
+
+import l2r.features.museum.MuseumCategory;
+import l2r.features.museum.MuseumManager;
+import l2r.features.museum.RefreshTime;
+import l2r.features.museum.TopPlayer;
+import l2r.gameserver.model.actor.instance.L2PcInstance;
+
+public class MuseumBBSManager extends BaseBBSManager
+{
+	public static String MUSEUM_BBS_CMD = "_bbsmuseum";
+	
+	@Override
+	public void cbByPass(String command, final L2PcInstance activeChar)
+	{
+		String html = "<html><body scroll=no><title>Museum Statistics</title><img src=L2UI.SquareBlank width=1 height=6/>";
+		command = command.substring((command.length() > MuseumBBSManager.MUSEUM_BBS_CMD.length()) ? (MuseumBBSManager.MUSEUM_BBS_CMD.length() + 1) : MuseumBBSManager.MUSEUM_BBS_CMD.length());
+		final StringTokenizer st = new StringTokenizer(command, ";");
+		String cmd = "main";
+		int type = 0;
+		int postType = 0;
+		if (st.hasMoreTokens())
+		{
+			cmd = st.nextToken();
+		}
+		if (st.hasMoreTokens())
+		{
+			type = Integer.parseInt(st.nextToken());
+		}
+		if (st.hasMoreTokens())
+		{
+			postType = Integer.parseInt(st.nextToken());
+		}
+		if (cmd.startsWith("main"))
+		{
+			html += this.showTops(type, postType);
+		}
+		else if (cmd.startsWith("personal"))
+		{
+			html += this.showPlayerTops(activeChar, type);
+		}
+		html += "</body></html>";
+		this.separateAndSend(html, activeChar);
+	}
+	
+	public String showTops(final int type, final int postType)
+	{
+		final MuseumCategory cat = MuseumManager.getInstance().getAllCategories().get((type * 256) + postType);
+		String html = "";
+		html += "<table cellspacing=-4><tr>";
+		html += "<td width=12><img src=L2UI_CT1.Tab_DF_Bg_line height=23 width=12/></td>";
+		html += "<td align=center><img src=L2UI.SquareBlank width=1 height=5/><table background=L2UI_CT1.Tab_DF_Tab_Selected width=150 height=24><tr><td align=center width=150><font color=e6dcbe>View Server Record</font></td></tr></table></td>";
+		html += "<td><img src=L2UI.SquareBlank width=1 height=5/><button value=\"View My Record\" action=\"bypass " + MuseumBBSManager.MUSEUM_BBS_CMD + ";personal\" fore=\"L2UI_CT1.Tab_DF_Tab_Unselected\" back=\"L2UI_CT1.Tab_DF_Tab_Unselected_Over\" width=\"150\" height=\"24\"/></td>";
+		html += "<td><img src=L2UI_CT1.Tab_DF_Bg_line height=23 width=800/></td>";
+		html += "</tr></table>";
+		html += "<img src=L2UI.SquareBlank width=1 height=5/>";
+		html += "<table><tr><td width=5></td>";
+		html += "<td>";
+		html += "<table cellspacing=-6>";
+		for (final Map.Entry<Integer, String> entry : MuseumManager.getInstance().getAllCategoryNames().entrySet())
+		{
+			final ArrayList<MuseumCategory> categories = MuseumManager.getInstance().getAllCategoriesByCategoryId(entry.getKey());
+			if (categories == null)
+			{
+				continue;
+			}
+			if (entry.getKey() == type)
+			{
+				html += "<tr><td><table background=L2UI_CT1.Button_DF_Down width=255 height=24><tr><td align=center width=255>[-] " + entry.getValue() + "</td></tr></table></td></tr>";
+				html += "<tr><td width=240 align=center><img src=L2UI.SquareBlank width=1 height=11/>";
+				for (final MuseumCategory category : categories)
+				{
+					html += "<table width=240 bgcolor=" + ((category.getTypeId() == postType) ? "4C3D28" : (((category.getTypeId() % 2) == 0) ? "000000" : "111111")) + "><tr><td width=240 align=center><font color=FFFFFF name=hs><a action=\"bypass " + MuseumBBSManager.MUSEUM_BBS_CMD + ";main;" + type + ";" + category.getTypeId() + "\">" + category.getTypeName() + "</a></font></td></tr></table>";
+				}
+				html += "<img src=L2UI.SquareBlank width=1 height=10/></td></tr>";
+			}
+			else
+			{
+				html += "<tr><td><button value=\"[+] " + entry.getValue() + "\" action=\"bypass " + MuseumBBSManager.MUSEUM_BBS_CMD + ";main;" + entry.getKey() + "\" fore=\"L2UI_CT1.Button_DF\" back=\"L2UI_CT1.Button_DF_Down\" width=\"255\" height=\"24\"/></td></tr>";
+			}
+		}
+		html += "</table>";
+		html += "</td>";
+		html += "<td>";
+		html += "<table cellspacing=-6><tr>";
+		if (!cat.getRefreshTime().equals(RefreshTime.Total))
+		{
+			html += "<td><button value=\"" + cat.getRefreshTime().name() + " Rankings\" action=\"\" fore=\"L2UI_CT1.Button_DF_Calculator\" back=\"L2UI_CT1.Button_DF_Calculator_Down\" width=\"257\" height=\"24\"/></td><td width=12></td>";
+		}
+		html += "<td><button value=\"Total Rankings\" action=\"\" fore=\"L2UI_CT1.Button_DF_Calculator\" back=\"L2UI_CT1.Button_DF_Calculator_Down\" width=\"" + (cat.getRefreshTime().equals(RefreshTime.Total) ? 517 : 257) + "\" height=\"24\"/></td>";
+		html += "</tr></table>";
+		html += "<table cellspacing=-3><tr>";
+		html += "<td>";
+		html += "<table><tr><td><img src=\"L2UI.SquareGray\" width=249 height=1/>";
+		final HashMap<Integer, TopPlayer> players = cat.getRefreshTime().equals(RefreshTime.Total) ? cat.getAllTotalTops() : cat.getAllTops();
+		for (int i = 0; i < 10; ++i)
+		{
+			String name = "No information.";
+			String value = "No information.";
+			if (players.size() > i)
+			{
+				final TopPlayer player = players.get(i + 1);
+				if (player != null)
+				{
+					name = player.getName();
+					final long count = player.getCount();
+					value = this.convertToValue(count, cat.isTimer(), cat.getAdditionalText());
+				}
+			}
+			
+			final String bgColor = (i == 0) ? "746833" : ((i == 1) ? "4e4332" : ((i == 2) ? "352e1e" : (((i % 2) == 1) ? "171612" : "23221e")));
+			html += "<table width=250 bgcolor=" + bgColor + " height=44>";
+			html += "<tr>";
+			html += "<td width=20><img src=L2UI_CT1.Gauge_DF_Attribute_Earth width=16 height=8></td>";
+			
+			switch (i + 1)
+			{
+				case 10:
+					html += "<td width=20 align=center><img src=L2UI_CT1.MiniGame_DF_Text_Score_1 width=16 height=32></td>";
+					html += "<td width=20 align=center><img src=L2UI_CT1.MiniGame_DF_Text_Score_0 width=16 height=32></td>";
+					break;
+				default:
+					html += "<td width=40 align=center><img src=L2UI_CT1.MiniGame_DF_Text_Score_" + (i + 1) + " width=16 height=32></td>";
+					break;
+			}
+			
+			html += "<td width=90 align=center>" + name + "<br1>";
+			html += "<font color=9f906a>" + value + "</font></td>";
+			
+			html += "<td width=40 valign=top align=center>";
+			switch (i + 1)
+			{
+				case 1:
+					html += "<img src=L2UI_CT1.Minimap_DF_ICN_TerritoryWar_Giran width=32 height=32>";
+					break;
+				case 2:
+					html += "<img src=L2UI_CT1.Minimap_DF_ICN_TerritoryWar_Innadril width=32 height=32>";
+					break;
+				case 3:
+					html += "<img src=L2UI_CT1.Minimap_DF_ICN_TerritoryWar_Aden width=32 height=32>";
+					break;
+			}
+			html += "</td>";
+			
+			html += "</tr>";
+			html += "</table>";
+		}
+		html += "</td></tr></table>";
+		html += "</td>";
+		html += "<td>";
+		html += "<table><tr><td><img src=\"L2UI.SquareGray\" width=249 height=1/>";
+		
+		for (int i = 10 - (cat.getRefreshTime().equals(RefreshTime.Total) ? 0 : 10); i < (20 - (cat.getRefreshTime().equals(RefreshTime.Total) ? 0 : 10)); ++i)
+		{
+			if (i >= 10)
+			{
+				break;
+			}
+			
+			String name = "No information.";
+			String value = "No information.";
+			if (cat.getAllTotalTops().size() > i)
+			{
+				final TopPlayer player = cat.getAllTotalTops().get(i + 1);
+				if (player != null)
+				{
+					name = player.getName();
+					value = this.convertToValue(player.getCount(), cat.isTimer(), cat.getAdditionalText());
+				}
+			}
+			
+			final String bgColor = (i == 0) ? "746833" : ((i == 1) ? "4e4332" : ((i == 2) ? "352e1e" : (((i % 2) == 1) ? "171612" : "23221e")));
+			html += "<table width=250 bgcolor=" + bgColor + " height=44>";
+			html += "<tr>";
+			html += "<td width=20><img src=L2UI_CT1.Gauge_DF_Attribute_Earth width=16 height=8></td>";
+			
+			switch (i + 1)
+			{
+				case 10:
+					html += "<td width=20 align=center><img src=L2UI_CT1.MiniGame_DF_Text_Score_1 width=16 height=32></td>";
+					html += "<td width=20 align=center><img src=L2UI_CT1.MiniGame_DF_Text_Score_0 width=16 height=32></td>";
+					break;
+				default:
+					html += "<td width=40 align=center><img src=L2UI_CT1.MiniGame_DF_Text_Score_" + (i + 1) + " width=16 height=32></td>";
+					break;
+			}
+			
+			html += "<td width=90 align=center>" + name + "<br1>";
+			html += "<font color=9f906a>" + value + "</font></td>";
+			
+			html += "<td width=40 valign=top align=center>";
+			switch (i + 1)
+			{
+				case 1:
+					html += "<img src=L2UI_CT1.Minimap_DF_ICN_TerritoryWar_Giran width=32 height=32>";
+					break;
+				case 2:
+					html += "<img src=L2UI_CT1.Minimap_DF_ICN_TerritoryWar_Innadril width=32 height=32>";
+					break;
+				case 3:
+					html += "<img src=L2UI_CT1.Minimap_DF_ICN_TerritoryWar_Aden width=32 height=32>";
+					break;
+			}
+			html += "</td>";
+			
+			html += "</tr>";
+			html += "</table>";
+		}
+		html += "</td></tr></table>";
+		html += "</td></tr></table>";
+		html += "</td>";
+		html += "</tr></table>";
+		return html;
+	}
+	
+	public String showPlayerTops(final L2PcInstance player, final int type)
+	{
+		final String[] dailyType =
+		{
+			"Monthly",
+			"Weekly",
+			"Daily"
+		};
+		String html = "";
+		html += "<table cellspacing=-4><tr>";
+		html += "<td width=12><img src=L2UI_CT1.Tab_DF_Bg_line height=23 width=12/></td>";
+		html += "<td><img src=L2UI.SquareBlank width=1 height=5/><button value=\"View Server Record\" action=\"bypass " + MuseumBBSManager.MUSEUM_BBS_CMD + ";main\" fore=\"L2UI_CT1.Tab_DF_Tab_Unselected\" back=\"L2UI_CT1.Tab_DF_Tab_Unselected_Over\" width=\"150\" height=\"24\"/></td>";
+		html += "<td align=center><img src=L2UI.SquareBlank width=1 height=5/><table background=L2UI_CT1.Tab_DF_Tab_Selected width=150 height=24><tr><td align=center width=150><font color=e6dcbe>View My Record</font></td></tr></table></td>";
+		html += "<td><img src=L2UI_CT1.Tab_DF_Bg_line height=23 width=800/></td>";
+		html += "</tr></table>";
+		html += "<img src=L2UI.SquareBlank width=1 height=5/>";
+		html += "<table><tr><td width=5></td>";
+		html += "<td>";
+		html += "<table cellspacing=-6>";
+		for (final Map.Entry<Integer, String> entry : MuseumManager.getInstance().getAllCategoryNames().entrySet())
+		{
+			final ArrayList<MuseumCategory> categories = MuseumManager.getInstance().getAllCategoriesByCategoryId(entry.getKey());
+			if (categories == null)
+			{
+				continue;
+			}
+			if (entry.getKey() == type)
+			{
+				html += "<tr><td><table background=L2UI_CT1.Button_DF_Down width=155 height=24><tr><td align=center width=155>[-] " + entry.getValue() + "</td></tr></table><img src=L2UI.SquareBlank width=1 height=6/></td></tr>";
+			}
+			else
+			{
+				html += "<tr><td><button value=\"[+] " + entry.getValue() + "\" action=\"bypass " + MuseumBBSManager.MUSEUM_BBS_CMD + ";personal;" + entry.getKey() + "\" fore=\"L2UI_CT1.Button_DF\" back=\"L2UI_CT1.Button_DF_Down\" width=\"155\" height=\"24\"/></td></tr>";
+			}
+		}
+		html += "</table>";
+		html += "</td>";
+		html += "<td>";
+		final ArrayList<MuseumCategory> categories2 = MuseumManager.getInstance().getAllCategoriesByCategoryId(type);
+		final String[] typeHtml1 =
+		{
+			"",
+			"",
+			"",
+			""
+		};
+		final String[] typeHtml2 =
+		{
+			"",
+			"",
+			"",
+			""
+		};
+		final String[] typeHtml3 =
+		{
+			"",
+			"",
+			"",
+			""
+		};
+		final int[] c =
+		{
+			0,
+			0,
+			0,
+			0
+		};
+		for (final MuseumCategory cat : categories2)
+		{
+			final int h = cat.getRefreshTime().ordinal();
+			if (typeHtml1[h].equals(""))
+			{
+				final StringBuilder sb = new StringBuilder();
+				final String[] array = typeHtml1;
+				final int n = h;
+				array[n] = sb.append(array[n]).append("<table cellspacing=-5><tr>").toString();
+				final StringBuilder sb2 = new StringBuilder();
+				final String[] array2 = typeHtml1;
+				final int n2 = h;
+				array2[n2] = sb2.append(array2[n2]).append("<td width=10></td>").toString();
+				final StringBuilder sb3 = new StringBuilder();
+				final String[] array3 = typeHtml1;
+				final int n3 = h;
+				array3[n3] = sb3.append(array3[n3]).append("<td><button value=\"Item\" action=\"\" fore=\"L2UI_CT1.Button_DF_Calculator\" back=\"L2UI_CT1.Button_DF_Calculator_Down\" width=\"").append((h > 0) ? 267 : 400).append("\" height=\"24\"/></td>").toString();
+				if (h > 0)
+				{
+					final StringBuilder sb4 = new StringBuilder();
+					final String[] array4 = typeHtml1;
+					final int n4 = h;
+					array4[n4] = sb4.append(array4[n4]).append("<td><button value=\"").append(dailyType[h - 1]).append(" Total\" action=\"\" fore=\"L2UI_CT1.Button_DF_Calculator\" back=\"L2UI_CT1.Button_DF_Calculator_Down\" width=\"176\" height=\"24\"/></td>").toString();
+				}
+				final StringBuilder sb5 = new StringBuilder();
+				final String[] array5 = typeHtml1;
+				final int n5 = h;
+				array5[n5] = sb5.append(array5[n5]).append("<td><button value=\"Total\" action=\"\" fore=\"L2UI_CT1.Button_DF_Calculator\" back=\"L2UI_CT1.Button_DF_Calculator_Down\" width=\"").append((h > 0) ? 176 : 216).append("\" height=\"24\"/></td>").toString();
+				final StringBuilder sb6 = new StringBuilder();
+				final String[] array6 = typeHtml1;
+				final int n6 = h;
+				array6[n6] = sb6.append(array6[n6]).append("</tr></table>").toString();
+				final StringBuilder sb7 = new StringBuilder();
+				final String[] array7 = typeHtml1;
+				final int n7 = h;
+				array7[n7] = sb7.append(array7[n7]).append("<table><tr>").toString();
+				final StringBuilder sb8 = new StringBuilder();
+				final String[] array8 = typeHtml1;
+				final int n8 = h;
+				array8[n8] = sb8.append(array8[n8]).append("<td>").toString();
+				final StringBuilder sb9 = new StringBuilder();
+				final String[] array9 = typeHtml1;
+				final int n9 = h;
+				array9[n9] = sb9.append(array9[n9]).append("<font name=hs12>").toString();
+				final StringBuilder sb10 = new StringBuilder();
+				final String[] array10 = typeHtml1;
+				final int n10 = h;
+				array10[n10] = sb10.append(array10[n10]).append("<img src=L2UI.SquareGray width=660 height=1 />").toString();
+			}
+			if (typeHtml3[h].equals(""))
+			{
+				final StringBuilder sb11 = new StringBuilder();
+				final String[] array11 = typeHtml3;
+				final int n11 = h;
+				array11[n11] = sb11.append(array11[n11]).append("</font>").toString();
+				final StringBuilder sb12 = new StringBuilder();
+				final String[] array12 = typeHtml3;
+				final int n12 = h;
+				array12[n12] = sb12.append(array12[n12]).append("</td>").toString();
+				final StringBuilder sb13 = new StringBuilder();
+				final String[] array13 = typeHtml3;
+				final int n13 = h;
+				array13[n13] = sb13.append(array13[n13]).append("</tr>").toString();
+				final StringBuilder sb14 = new StringBuilder();
+				final String[] array14 = typeHtml3;
+				final int n14 = h;
+				array14[n14] = sb14.append(array14[n14]).append("</table>").toString();
+				if (h < 3)
+				{
+					final StringBuilder sb15 = new StringBuilder();
+					final String[] array15 = typeHtml3;
+					final int n15 = h;
+					array15[n15] = sb15.append(array15[n15]).append("<br><br>").toString();
+				}
+			}
+			final StringBuilder sb16 = new StringBuilder();
+			final String[] array16 = typeHtml2;
+			final int n16 = h;
+			array16[n16] = sb16.append(array16[n16]).append("<table bgcolor=").append(((c[h] % 2) == 0) ? "111111" : "000000").append("><tr><td width=").append((h > 0) ? 270 : 400).append(" align=center>").append(cat.getTypeName()).append("</td>").toString();
+			long[] d = player.getMuseumPlayer().getData(cat.getType());
+			if (d == null)
+			{
+				d = new long[]
+				{
+					0L,
+					0L,
+					0L,
+					0L
+				};
+			}
+			String value = "";
+			value = this.convertToValue(d[h], cat.isTimer(), cat.getAdditionalText());
+			String totalValue = "";
+			if (h > 0)
+			{
+				totalValue = this.convertToValue(d[0], cat.isTimer(), cat.getAdditionalText());
+			}
+			if (h > 0)
+			{
+				final StringBuilder sb17 = new StringBuilder();
+				final String[] array17 = typeHtml2;
+				final int n17 = h;
+				array17[n17] = sb17.append(array17[n17]).append("<td width=168 align=center>").append(value).append("</td>").toString();
+			}
+			final StringBuilder sb18 = new StringBuilder();
+			final String[] array18 = typeHtml2;
+			final int n18 = h;
+			array18[n18] = sb18.append(array18[n18]).append("<td width=").append((h > 0) ? 168 : 208).append(" align=center>").append((h > 0) ? totalValue : value).append("</td>").toString();
+			final StringBuilder sb19 = new StringBuilder();
+			final String[] array19 = typeHtml2;
+			final int n19 = h;
+			array19[n19] = sb19.append(array19[n19]).append("</tr></table>").toString();
+			final StringBuilder sb20 = new StringBuilder();
+			final String[] array20 = typeHtml2;
+			final int n20 = h;
+			array20[n20] = sb20.append(array20[n20]).append("<img src=L2UI.SquareGray width=660 height=1 />").toString();
+			final int[] array21 = c;
+			final int n21 = h;
+			++array21[n21];
+		}
+		for (int i = 0; i < 4; ++i)
+		{
+			html += typeHtml1[i];
+			html += typeHtml2[i];
+			html += typeHtml3[i];
+		}
+		html += "</td>";
+		html += "</tr></table>";
+		return html;
+	}
+	
+	public String convertToValue(final long count, final boolean isTimer, final String additionalText)
+	{
+		String value = "";
+		if (!isTimer)
+		{
+			value = NumberFormat.getNumberInstance(Locale.US).format(count);
+			value = value + " " + additionalText;
+		}
+		else
+		{
+			final long days = count / 86400L;
+			final long hours = (count % 86400L) / 3600L;
+			final long mins = (count % 3600L) / 60L;
+			value = "";
+			if (days > 0L)
+			{
+				value = value + days + " day(s) ";
+			}
+			value = value + hours + " hour(s) ";
+			if ((mins > 0L) && (days < 1L))
+			{
+				value = value + mins + " min(s) ";
+			}
+			if ((days < 1L) && (hours < 1L) && (mins < 1L))
+			{
+				value = "0 min(s) " + count + " sec(s)";
+			}
+		}
+		return value;
+	}
+	
+	@Override
+	public void parsewrite(final String url, final String ar1, final String ar2, final String ar3, final String ar4, final String ar5, final L2PcInstance activeChar)
+	{
+	}
+	
+	public static MuseumBBSManager getInstance()
+	{
+		return SingletonHolder._instance;
+	}
+	
+	private static class SingletonHolder
+	{
+		protected static final MuseumBBSManager _instance;
+		
+		static
+		{
+			_instance = new MuseumBBSManager();
+		}
+	}
+}
Index: java/l2r/features/museum/MuseumManager.java
===================================================================
--- java/l2r/features/museum/MuseumManager.java	(revision 0)
+++ java/l2r/features/museum/MuseumManager.java	(working copy)
@@ -0,0 +1,590 @@
+package l2r.features.museum;
+
+import java.io.File;
+import java.sql.Connection;
+import java.sql.PreparedStatement;
+import java.sql.ResultSet;
+import java.util.ArrayList;
+import java.util.Calendar;
+import java.util.HashMap;
+import java.util.Map;
+
+import javax.xml.parsers.DocumentBuilderFactory;
+
+import l2r.Config;
+import l2r.L2DatabaseFactory;
+import l2r.gameserver.ThreadPoolManager;
+import l2r.gameserver.data.sql.NpcTable;
+import l2r.gameserver.instancemanager.GlobalVariablesManager;
+import l2r.gameserver.model.L2World;
+import l2r.gameserver.model.Location;
+import l2r.gameserver.model.actor.instance.L2PcInstance;
+
+import org.slf4j.Logger;
+import org.slf4j.LoggerFactory;
+import org.w3c.dom.Document;
+import org.w3c.dom.Node;
+
+public class MuseumManager
+{
+	public static final Logger _log = LoggerFactory.getLogger(MuseumManager.class);
+	private final HashMap<Integer, String> _categoryNames;
+	private final HashMap<Integer, MuseumCategory> _categories;
+	private final HashMap<Integer, ArrayList<MuseumCategory>> _categoriesByCategoryId;
+	private final HashMap<Integer, ArrayList<Integer>> _playersWithReward;
+	private int refreshTotal;
+	
+	public MuseumManager()
+	{
+		refreshTotal = 3600;
+		_categoryNames = new HashMap<>();
+		_categories = new HashMap<>();
+		_categoriesByCategoryId = new HashMap<>();
+		_playersWithReward = new HashMap<>();
+		loadCategories();
+		final long monthlyUpdate = Math.max(100L, (GlobalVariablesManager.getInstance().hasVariable("museum_monthly") ? GlobalVariablesManager.getInstance().getLong("museum_monthly") : 0L) - System.currentTimeMillis());
+		final long weeklyUpdate = Math.max(100L, (GlobalVariablesManager.getInstance().hasVariable("museum_weekly") ? GlobalVariablesManager.getInstance().getLong("museum_weekly") : 0L) - System.currentTimeMillis());
+		final long dailyUpdate = Math.max(100L, (GlobalVariablesManager.getInstance().hasVariable("museum_daily") ? GlobalVariablesManager.getInstance().getLong("museum_daily") : 0L) - System.currentTimeMillis());
+		ThreadPoolManager.getInstance().scheduleGeneralAtFixedRate(new UpdateStats(RefreshTime.Total), 100L, refreshTotal * 1000);
+		ThreadPoolManager.getInstance().scheduleGeneral(new UpdateStats(RefreshTime.Monthly), monthlyUpdate);
+		ThreadPoolManager.getInstance().scheduleGeneral(new UpdateStats(RefreshTime.Weekly), weeklyUpdate);
+		ThreadPoolManager.getInstance().scheduleGeneral(new UpdateStats(RefreshTime.Daily), dailyUpdate);
+	}
+	
+	public void giveRewards()
+	{
+		final ArrayList<Integer> withReward = new ArrayList<>();
+		for (final Map.Entry<Integer, ArrayList<Integer>> entry : _playersWithReward.entrySet())
+		{
+			final L2PcInstance player = L2World.getInstance().getPlayer(entry.getKey());
+			if (player != null)
+			{
+				if (!player.isOnline())
+				{
+					continue;
+				}
+				final ArrayList<Integer> cats = entry.getValue();
+				for (final int catId : cats)
+				{
+					if (!_categories.containsKey(catId))
+					{
+						withReward.add(entry.getKey());
+					}
+					else
+					{
+						final MuseumCategory cat = _categories.get(catId);
+						if (cat == null)
+						{
+							withReward.add(entry.getKey());
+						}
+						else
+						{
+							for (final MuseumReward reward : cat.getRewards())
+							{
+								reward.giveReward(player);
+								withReward.add(entry.getKey());
+							}
+						}
+					}
+				}
+			}
+		}
+		for (final int i : withReward)
+		{
+			_playersWithReward.remove(i);
+		}
+		if (_playersWithReward.size() == 0)
+		{
+			return;
+		}
+	}
+	
+	public void giveReward(final L2PcInstance player)
+	{
+		if (!_playersWithReward.containsKey(player.getObjectId()))
+		{
+			return;
+		}
+		final ArrayList<Integer> cats = _playersWithReward.get(player.getObjectId());
+		if (cats.size() < 1)
+		{
+			_playersWithReward.remove(player.getObjectId());
+			return;
+		}
+		for (final int catId : cats)
+		{
+			if (!_categories.containsKey(catId))
+			{
+				continue;
+			}
+			final MuseumCategory cat = _categories.get(catId);
+			for (final MuseumReward reward : cat.getRewards())
+			{
+				reward.giveReward(player);
+			}
+		}
+		_playersWithReward.remove(player.getObjectId());
+	}
+	
+	public void restoreLastTops(final RefreshTime time)
+	{
+		for (final MuseumCategory cat : getAllCategories().values())
+		{
+			int i = 1;
+			if (!cat.getRefreshTime().equals(time) && !time.equals(RefreshTime.Total))
+			{
+				continue;
+			}
+			cat.getAllStatuePlayers().clear();
+			try (final Connection con = L2DatabaseFactory.getInstance().getConnection();
+				final PreparedStatement statement = con.prepareStatement("SELECT * FROM museum_last_statistics as mls INNER JOIN museum_statistics as ms ON ms.objectId=mls.objectId WHERE mls.category = ? AND mls.category = ms.category AND mls.count > 0 AND mls.timer = '" + cat.getRefreshTime().name().toLowerCase() + "' ORDER BY mls.count DESC LIMIT 5"))
+			{
+				statement.setString(1, cat.getType());
+				try (final ResultSet rset = statement.executeQuery())
+				{
+					while (rset.next())
+					{
+						final int objectId = rset.getInt("objectId");
+						final String name = rset.getString("name");
+						final long count = rset.getLong("count");
+						cat.getAllStatuePlayers().put(i, new TopPlayer(objectId, name, count));
+						if (i == 1)
+						{
+							spawnStatue(cat);
+						}
+						++i;
+					}
+				}
+			}
+			catch (Exception e)
+			{
+				_log.error("Failed loading character museum data.", e);
+			}
+		}
+	}
+	
+	public void spawnStatue(final MuseumCategory cat)
+	{
+		for (final L2MuseumStatueInstance statue : cat.getAllSpawnedStatues())
+		{
+			statue.deleteMe();
+		}
+		cat.getAllSpawnedStatues().clear();
+		if (cat.getAllStatuePlayers().size() > 0)
+		{
+			final TopPlayer player = cat.getAllStatuePlayers().get(1);
+			for (final Location loc : cat.getStatueSpawns())
+			{
+				final L2MuseumStatueInstance statue2 = new L2MuseumStatueInstance(NpcTable.getInstance().getTemplate(30001), player.getObjectId(), (cat.getCategoryId() * 256) + cat.getTypeId());
+				statue2.setXYZ(loc);
+				statue2.setHeading(loc.getHeading());
+				statue2.spawnMe();
+				cat.getAllSpawnedStatues().add(statue2);
+			}
+		}
+	}
+	
+	public void cleanLastTops(final RefreshTime time)
+	{
+		try (final Connection con = L2DatabaseFactory.getInstance().getConnection();
+			final PreparedStatement statement = con.prepareStatement("DELETE FROM museum_last_statistics WHERE timer='" + time.name().toLowerCase() + "'"))
+		{
+			statement.execute();
+		}
+		catch (Exception e)
+		{
+			_log.warn("Could not store char museum data: " + e.getMessage(), e);
+		}
+	}
+	
+	public void refreshTopsFromDatabase(final RefreshTime time)
+	{
+		_playersWithReward.clear();
+		try (final Connection con = L2DatabaseFactory.getInstance().getConnection())
+		{
+			for (final MuseumCategory cat : _categories.values())
+			{
+				if (!cat.getRefreshTime().equals(time) && !time.equals(RefreshTime.Total))
+				{
+					continue;
+				}
+				cat.getAllTops().clear();
+				cat.getAllTotalTops().clear();
+				cat.getAllStatuePlayers().clear();
+				int i = 1;
+				int h = 1;
+				try (
+					final PreparedStatement statement = con.prepareStatement("SELECT * FROM museum_statistics WHERE category = ? AND " + cat.getRefreshTime().name().toLowerCase() + "_count > 0 ORDER BY " + cat.getRefreshTime().name().toLowerCase() + "_count DESC LIMIT " + (cat.getRefreshTime().equals(RefreshTime.Total) ? 20 : 10)))
+				{
+					statement.setString(1, cat.getType());
+					try (final ResultSet rset = statement.executeQuery())
+					{
+						while (rset.next())
+						{
+							final int objectId = rset.getInt("objectId");
+							final String name = rset.getString("name");
+							final long count = rset.getLong(cat.getRefreshTime().name().toLowerCase() + "_count");
+							final boolean hasReward = rset.getBoolean("hasReward");
+							if (hasReward)
+							{
+								if (!_playersWithReward.containsKey(objectId))
+								{
+									_playersWithReward.put(objectId, new ArrayList<Integer>());
+								}
+								_playersWithReward.get(objectId).add((cat.getCategoryId() * 256) + cat.getTypeId());
+							}
+							if (cat.getRefreshTime().equals(RefreshTime.Total))
+							{
+								cat.getAllTotalTops().put(i, new TopPlayer(objectId, name, count));
+							}
+							else
+							{
+								cat.getAllTops().put(i, new TopPlayer(objectId, name, count));
+							}
+							if ((i < 6) && time.equals(cat.getRefreshTime()))
+							{
+								try (final PreparedStatement stat = con.prepareStatement("REPLACE museum_last_statistics SET objectId=" + objectId + ", name='" + name + "', category='" + cat.getType() + "', count=" + count + ", timer='" + time.name().toLowerCase() + "';"))
+								{
+									stat.execute();
+								}
+								if (i == 1)
+								{
+									try (final PreparedStatement stat = con.prepareStatement("UPDATE museum_statistics SET hasReward = 1 WHERE objectId = " + objectId + " AND category = '" + cat.getType() + "'"))
+									{
+										stat.execute();
+									}
+									if (!_playersWithReward.containsKey(objectId))
+									{
+										_playersWithReward.put(objectId, new ArrayList<Integer>());
+									}
+									_playersWithReward.get(objectId).add((cat.getCategoryId() * 256) + cat.getTypeId());
+								}
+							}
+							++i;
+						}
+					}
+				}
+				if (cat.getRefreshTime().equals(RefreshTime.Total))
+				{
+					continue;
+				}
+				try (final PreparedStatement statement = con.prepareStatement("SELECT * FROM museum_statistics WHERE category = ? AND total_count > 0 ORDER BY total_count DESC LIMIT 10"))
+				{
+					statement.setString(1, cat.getType());
+					try (final ResultSet rset = statement.executeQuery())
+					{
+						while (rset.next())
+						{
+							final int objectId = rset.getInt("objectId");
+							final String name = rset.getString("name");
+							final long count = rset.getLong("total_count");
+							cat.getAllTotalTops().put(h, new TopPlayer(objectId, name, count));
+							++h;
+						}
+					}
+				}
+			}
+			if (!time.equals(RefreshTime.Total))
+			{
+				try (final PreparedStatement statement2 = con.prepareStatement("UPDATE museum_statistics SET " + time.name().toLowerCase() + "_count = 0"))
+				{
+					statement2.execute();
+				}
+			}
+		}
+		catch (Exception e)
+		{
+			_log.warn("Could not store char museum data: " + e.getMessage(), e);
+		}
+		restoreLastTops(time);
+		giveRewards();
+	}
+	
+	public void loadCategories()
+	{
+		_log.info(this.getClass().getSimpleName() + ": Initializing");
+		_categoryNames.clear();
+		_categories.clear();
+		_categoriesByCategoryId.clear();
+		final DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
+		factory.setValidating(false);
+		factory.setIgnoringComments(true);
+		final File file = new File(Config.DATAPACK_ROOT, "data/xml/sunrise/MuseumCategories.xml");
+		Document doc = null;
+		if (file.exists())
+		{
+			try
+			{
+				doc = factory.newDocumentBuilder().parse(file);
+			}
+			catch (Exception e)
+			{
+				_log.warn("Could not parse MuseumCategories.xml file: " + e.getMessage(), e);
+				return;
+			}
+			int categoryId = 0;
+			final Node n = doc.getFirstChild();
+			for (Node d = n.getFirstChild(); d != null; d = d.getNextSibling())
+			{
+				if (d.getNodeName().equalsIgnoreCase("set"))
+				{
+					final String name = d.getAttributes().getNamedItem("name").getNodeValue();
+					final String val = d.getAttributes().getNamedItem("val").getNodeValue();
+					if (name.equalsIgnoreCase("refreshAllStatisticsIn"))
+					{
+						refreshTotal = Integer.parseInt(val);
+					}
+				}
+				if (d.getNodeName().equalsIgnoreCase("category"))
+				{
+					final ArrayList<MuseumCategory> list = new ArrayList<>();
+					final String categoryName = d.getAttributes().getNamedItem("name").getNodeValue();
+					int typeId = 0;
+					for (Node h = d.getFirstChild(); h != null; h = h.getNextSibling())
+					{
+						if (h.getNodeName().equalsIgnoreCase("type"))
+						{
+							final String typeName = h.getAttributes().getNamedItem("name").getNodeValue();
+							final String type = h.getAttributes().getNamedItem("type").getNodeValue();
+							final String refreshTime = h.getAttributes().getNamedItem("refreshTime").getNodeValue();
+							boolean timer = false;
+							if (h.getAttributes().getNamedItem("timer") != null)
+							{
+								timer = Boolean.parseBoolean(h.getAttributes().getNamedItem("timer").getNodeValue());
+							}
+							String additionalText = "";
+							if (h.getAttributes().getNamedItem("additionalText") != null)
+							{
+								additionalText = h.getAttributes().getNamedItem("additionalText").getNodeValue();
+							}
+							final ArrayList<Location> statueSpawns = new ArrayList<>();
+							final ArrayList<MuseumReward> rewards = new ArrayList<>();
+							int rewardId = 0;
+							for (Node a = h.getFirstChild(); a != null; a = a.getNextSibling())
+							{
+								if (a.getNodeName().equalsIgnoreCase("spawn"))
+								{
+									final int x = Integer.parseInt(a.getAttributes().getNamedItem("x").getNodeValue());
+									final int y = Integer.parseInt(a.getAttributes().getNamedItem("y").getNodeValue());
+									final int z = Integer.parseInt(a.getAttributes().getNamedItem("z").getNodeValue());
+									final int heading = (a.getAttributes().getNamedItem("heading") != null) ? Integer.parseInt(a.getAttributes().getNamedItem("heading").getNodeValue()) : 0;
+									statueSpawns.add(new Location(x, y, z, heading));
+								}
+								if (a.getNodeName().equalsIgnoreCase("reward"))
+								{
+									String rewardType = "";
+									int itemId = 0;
+									int minCount = 0;
+									int maxCount = 0;
+									double chance = 0.0;
+									if (a.getAttributes().getNamedItem("type") != null)
+									{
+										rewardType = a.getAttributes().getNamedItem("type").getNodeValue();
+									}
+									if (a.getAttributes().getNamedItem("id") != null)
+									{
+										itemId = Integer.parseInt(a.getAttributes().getNamedItem("id").getNodeValue());
+									}
+									if (a.getAttributes().getNamedItem("min") != null)
+									{
+										minCount = Integer.parseInt(a.getAttributes().getNamedItem("min").getNodeValue());
+									}
+									if (a.getAttributes().getNamedItem("max") != null)
+									{
+										maxCount = Integer.parseInt(a.getAttributes().getNamedItem("max").getNodeValue());
+									}
+									if (a.getAttributes().getNamedItem("chance") != null)
+									{
+										chance = Double.parseDouble(a.getAttributes().getNamedItem("chance").getNodeValue());
+									}
+									rewards.add(new MuseumReward(rewardId, rewardType, itemId, minCount, maxCount, chance));
+									++rewardId;
+								}
+							}
+							final int key = (categoryId * 256) + typeId;
+							final MuseumCategory category = new MuseumCategory(categoryId, typeId, categoryName, typeName, type, refreshTime, timer, additionalText, statueSpawns, rewards);
+							list.add(category);
+							_categories.put(key, category);
+							++typeId;
+						}
+					}
+					_categoriesByCategoryId.put(categoryId, list);
+					_categoryNames.put(categoryId, categoryName);
+					++categoryId;
+				}
+			}
+		}
+		_log.info(this.getClass().getSimpleName() + ": Successfully loaded " + _categoryNames.size() + " categories and " + _categories.size() + " post categories.");
+	}
+	
+	public HashMap<Integer, String> getAllCategoryNames()
+	{
+		return _categoryNames;
+	}
+	
+	public HashMap<Integer, MuseumCategory> getAllCategories()
+	{
+		return _categories;
+	}
+	
+	public ArrayList<MuseumCategory> getAllCategoriesByCategoryId(final int id)
+	{
+		if (_categoriesByCategoryId.containsKey(id))
+		{
+			return _categoriesByCategoryId.get(id);
+		}
+		return null;
+	}
+	
+	public void restoreDataForChar(final L2PcInstance player)
+	{
+		final HashMap<String, long[]> data = new HashMap<>();
+		try (final Connection con = L2DatabaseFactory.getInstance().getConnection();
+			final PreparedStatement statement = con.prepareStatement("SELECT * FROM museum_statistics WHERE objectId = ?"))
+		{
+			statement.setInt(1, player.getObjectId());
+			try (final ResultSet rset = statement.executeQuery())
+			{
+				while (rset.next())
+				{
+					final long[] d =
+					{
+						rset.getLong("total_count"),
+						rset.getLong("monthly_count"),
+						rset.getLong("weekly_count"),
+						rset.getLong("daily_count")
+					};
+					final String category = rset.getString("category");
+					data.put(category, d);
+				}
+			}
+		}
+		catch (Exception e)
+		{
+			_log.error("Failed loading character museum data.", e);
+		}
+		player.setMuseumPlayer(new MuseumPlayer(player.getObjectId(), player.getName(), data));
+	}
+	
+	public void updateDataForChar(final L2PcInstance player)
+	{
+		if (player.getMuseumPlayer() == null)
+		{
+			return;
+		}
+		String update = "";
+		try (final Connection con = L2DatabaseFactory.getInstance().getConnection())
+		{
+			for (final Map.Entry<String, long[]> entry : player.getMuseumPlayer().getData().entrySet())
+			{
+				update = "REPLACE museum_statistics SET objectId=" + player.getObjectId() + ", name='" + player.getName() + "', category='" + entry.getKey() + "', total_count=" + entry.getValue()[0] + ", monthly_count=" + entry.getValue()[1] + ", weekly_count=" + entry.getValue()[2] + ", daily_count=" + entry.getValue()[3] + ", hasReward=0;";
+				try (final PreparedStatement statement = con.prepareStatement(update))
+				{
+					statement.execute();
+				}
+			}
+		}
+		catch (Exception e)
+		{
+			_log.warn("Could not store char museum data: " + e.getMessage(), e);
+		}
+	}
+	
+	public void reloadConfigs()
+	{
+		loadCategories();
+		restoreLastTops(RefreshTime.Total);
+	}
+	
+	public static MuseumManager getInstance()
+	{
+		return SingletonHolder._instance;
+	}
+	
+	public class UpdateStats implements Runnable
+	{
+		RefreshTime _time;
+		
+		public UpdateStats(final RefreshTime time)
+		{
+			_time = time;
+		}
+		
+		@Override
+		public void run()
+		{
+			long time = 0L;
+			switch (_time)
+			{
+				case Monthly:
+				{
+					final Calendar c = Calendar.getInstance();
+					c.set(2, c.get(2) + 1);
+					c.set(5, 1);
+					c.set(11, 0);
+					c.set(12, 0);
+					c.set(13, 0);
+					time = Math.max(100L, c.getTimeInMillis() - System.currentTimeMillis());
+					GlobalVariablesManager.getInstance().set("museum_monthly", c.getTimeInMillis());
+					ThreadPoolManager.getInstance().scheduleGeneral(new UpdateStats(RefreshTime.Monthly), time);
+					cleanLastTops(_time);
+					break;
+				}
+				case Weekly:
+				{
+					final Calendar c = Calendar.getInstance();
+					c.set(7, 2);
+					c.set(11, 0);
+					c.set(12, 0);
+					c.set(13, 0);
+					if (c.getTimeInMillis() < System.currentTimeMillis())
+					{
+						c.setTimeInMillis(c.getTimeInMillis() + 604800000L);
+					}
+					time = Math.max(100L, c.getTimeInMillis() - System.currentTimeMillis());
+					GlobalVariablesManager.getInstance().set("museum_weekly", c.getTimeInMillis());
+					ThreadPoolManager.getInstance().scheduleGeneral(new UpdateStats(_time), time);
+					cleanLastTops(_time);
+					break;
+				}
+				case Daily:
+				{
+					final Calendar c = Calendar.getInstance();
+					c.set(6, c.get(6) + 1);
+					c.set(11, 0);
+					c.set(12, 0);
+					c.set(13, 0);
+					time = Math.max(100L, c.getTimeInMillis() - System.currentTimeMillis());
+					GlobalVariablesManager.getInstance().set("museum_daily", c.getTimeInMillis());
+					ThreadPoolManager.getInstance().scheduleGeneral(new UpdateStats(_time), time);
+					cleanLastTops(_time);
+					break;
+				}
+			}
+			refreshTops();
+			restoreLastTops(RefreshTime.Total);
+		}
+		
+		public void refreshTops()
+		{
+			for (final L2PcInstance player : L2World.getInstance().getPlayers())
+			{
+				if (player.getMuseumPlayer() != null)
+				{
+					player.getMuseumPlayer().resetData(_time);
+				}
+			}
+			refreshTopsFromDatabase(_time);
+		}
+	}
+	
+	private static class SingletonHolder
+	{
+		protected static final MuseumManager _instance;
+		
+		static
+		{
+			_instance = new MuseumManager();
+		}
+	}
+}
Index: java/l2r/gameserver/communitybbs/BoardsManager.java
===================================================================
--- java/l2r/gameserver/communitybbs/BoardsManager.java	(revision 1211)
+++ java/l2r/gameserver/communitybbs/BoardsManager.java	(working copy)
@@ -24,8 +24,8 @@
 import l2r.Config;
 import l2r.gameserver.communitybbs.Managers.ClanBBSManager;
 import l2r.gameserver.communitybbs.Managers.DonateBBSManager;
-import l2r.gameserver.communitybbs.Managers.FavoriteBBSManager;
 import l2r.gameserver.communitybbs.Managers.MailBBSManager;
+import l2r.gameserver.communitybbs.Managers.MuseumBBSManager;
 import l2r.gameserver.communitybbs.Managers.PostBBSManager;
 import l2r.gameserver.communitybbs.Managers.ServicesBBSManager;
 import l2r.gameserver.communitybbs.Managers.TopBBSManager;
@@ -111,9 +111,14 @@
 		{
 			// RegionBBSManager.getInstance().cbByPass(command, activeChar);
 		}
-		else if (command.startsWith("_bbsgetfav") || command.startsWith("bbs_add_fav") || command.startsWith("_bbsdelfav_"))
+		else if (command.startsWith("_bbsgetfav") || command.startsWith("bbs_add_fav") || command.startsWith("_bbsdelfav_") || command.startsWith("_bbsmuseum"))
 		{
-			FavoriteBBSManager.getInstance().cbByPass(command, activeChar);
+			if (!command.startsWith("_bbsmuseum"))
+			{
+				command = "_bbsmuseum";
+			}
+			MuseumBBSManager.getInstance().cbByPass(command, activeChar);
+			// FavoriteBBSManager.getInstance().cbByPass(command, activeChar);
 		}
 		else if (command.startsWith("_bbslink"))
 		{
Index: java/l2r/gameserver/model/L2Party.java
===================================================================
--- java/l2r/gameserver/model/L2Party.java	(revision 1211)
+++ java/l2r/gameserver/model/L2Party.java	(working copy)
@@ -982,7 +982,7 @@
 				}
 				
 				addexp = calculateExpSpPartyCutoff(member.getActingPlayer(), topLvl, addexp, addsp, useVitalityRate);
-				
+				member.getMuseumPlayer().addData("monster_kill_xp", addexp);
 				if (addexp > 0)
 				{
 					// vGodFather for nevit system
Index: java/l2r/gameserver/model/actor/L2Attackable.java
===================================================================
--- java/l2r/gameserver/model/actor/L2Attackable.java	(revision 1211)
+++ java/l2r/gameserver/model/actor/L2Attackable.java	(working copy)
@@ -370,6 +370,11 @@
 				final int respawnTime = Config.MINIONS_RESPAWN_TIME.containsKey(getId()) ? Config.MINIONS_RESPAWN_TIME.get(getId()) * 1000 : -1;
 				mob.getLeader().getMinionList().onMinionDie(mob, respawnTime);
 			}
+			if (killer instanceof L2PcInstance)
+			{
+				L2PcInstance player = (L2PcInstance) killer;
+				player.getMuseumPlayer().addData("monster_kills", 1);
+			}
 		}
 		return true;
 	}
@@ -528,6 +533,7 @@
 								final long addexp = Math.round(attacker.calcStat(Stats.EXPSP_RATE, exp, null, null));
 								final int addsp = (int) attacker.calcStat(Stats.EXPSP_RATE, sp, null, null);
 								
+								attacker.getMuseumPlayer().addData("monster_kill_xp", addexp);
 								attacker.addExpAndSp(addexp, addsp, useVitalityRate());
 								if (addexp > 0)
 								{
Index: java/l2r/features/museum/MuseumReward.java
===================================================================
--- java/l2r/features/museum/MuseumReward.java	(revision 0)
+++ java/l2r/features/museum/MuseumReward.java	(working copy)
@@ -0,0 +1,172 @@
+package l2r.features.museum;
+
+import l2r.gameserver.idfactory.IdFactory;
+import l2r.gameserver.model.actor.instance.L2PcInstance;
+import l2r.gameserver.model.items.instance.L2ItemInstance;
+import l2r.util.Rnd;
+
+public class MuseumReward
+{
+	String type;
+	int id;
+	int itemId;
+	int minCount;
+	int maxCount;
+	double chance;
+	
+	public MuseumReward(final int _id, final String _type, final int _itemId, final int _minCount, final int _maxCount, final double _chance)
+	{
+		id = _id;
+		type = _type;
+		itemId = _itemId;
+		minCount = _minCount;
+		maxCount = _maxCount;
+		chance = _chance;
+	}
+	
+	public int getId()
+	{
+		return id;
+	}
+	
+	public String getType()
+	{
+		return type;
+	}
+	
+	public void setType(final String _type)
+	{
+		type = _type;
+	}
+	
+	public int getItemId()
+	{
+		return itemId;
+	}
+	
+	public void setItemId(final int _itemId)
+	{
+		itemId = _itemId;
+	}
+	
+	public int getMinCount()
+	{
+		return minCount;
+	}
+	
+	public void setMinCount(final int _minCount)
+	{
+		minCount = _minCount;
+	}
+	
+	public int getMaxCount()
+	{
+		return maxCount;
+	}
+	
+	public void setMaxCount(final int _maxCount)
+	{
+		maxCount = _maxCount;
+	}
+	
+	public double getChance()
+	{
+		return chance;
+	}
+	
+	public void setChance(final double _chance)
+	{
+		chance = _chance;
+	}
+	
+	public void giveReward(final L2PcInstance player)
+	{
+		if (getType().equalsIgnoreCase("item"))
+		{
+			giveItems(player);
+		}
+		else if (getType().equalsIgnoreCase("clanpoints"))
+		{
+			giveClanPoints(player);
+		}
+		else if (getType().equalsIgnoreCase("skillpoints"))
+		{
+			giveSkillPoints(player);
+		}
+		else if (getType().equalsIgnoreCase("experience"))
+		{
+			giveExperience(player);
+		}
+	}
+	
+	private void giveClanPoints(final L2PcInstance player)
+	{
+		if (player.getClan() == null)
+		{
+			return;
+		}
+		final double _chance = getChance() * 1000.0;
+		final int min = getMinCount();
+		int max = getMaxCount();
+		if (min > max)
+		{
+			max = min;
+		}
+		final int count = Rnd.get(min, max);
+		if ((Rnd.get(0, 100000) < _chance) && (player.getClan() != null))
+		{
+			player.getClan().addReputationScore(count, true);
+		}
+	}
+	
+	private void giveSkillPoints(final L2PcInstance player)
+	{
+		final double _chance = getChance() * 1000.0;
+		final int min = getMinCount();
+		int max = getMaxCount();
+		if (min > max)
+		{
+			max = min;
+		}
+		final int count = Rnd.get(min, max);
+		if (Rnd.get(0, 100000) <= _chance)
+		{
+			player.getStat().addExpAndSp(0L, count, false);
+		}
+	}
+	
+	private void giveExperience(final L2PcInstance player)
+	{
+		final double _chance = getChance() * 1000.0;
+		final int min = getMinCount();
+		int max = getMaxCount();
+		if (min > max)
+		{
+			max = min;
+		}
+		final long count = Rnd.get(min, max);
+		if (Rnd.get(0, 100000) <= _chance)
+		{
+			player.getStat().addExpAndSp(count, 0, false);
+		}
+	}
+	
+	private void giveItems(final L2PcInstance player)
+	{
+		final double _chance = getChance() * 1000.0;
+		final int _itemId = getItemId();
+		final int min = getMinCount();
+		int max = getMaxCount();
+		if (min > max)
+		{
+			max = min;
+		}
+		final int count = Rnd.get(min, max);
+		if (Rnd.get(0, 100000) < _chance)
+		{
+			final L2ItemInstance item = new L2ItemInstance(IdFactory.getInstance().getNextId(), _itemId);
+			item.setCount(count);
+			player.addItem("RewardItem", item, player, true);
+		}
+	}
+}
Index: java/l2r/gameserver/network/clientpackets/EnterWorld.java
===================================================================
--- java/l2r/gameserver/network/clientpackets/EnterWorld.java	(revision 1211)
+++ java/l2r/gameserver/network/clientpackets/EnterWorld.java	(working copy)
@@ -19,6 +19,7 @@
 package l2r.gameserver.network.clientpackets;
 
 import l2r.Config;
+import l2r.features.museum.MuseumManager;
 import l2r.gameserver.LoginServerThread;
 import l2r.gameserver.SevenSigns;
 import l2r.gameserver.cache.HtmCache;
@@ -641,6 +642,8 @@
 			}
 		}
 		
+		MuseumManager.getInstance().giveReward(activeChar);
+		
 		// Unstuck players that had client open when server crashed.
 		activeChar.sendPacket(ActionFailed.STATIC_PACKET);
 	}
Index: java/l2r/features/museum/MuseumPlayer.java
===================================================================
--- java/l2r/features/museum/MuseumPlayer.java	(revision 0)
+++ java/l2r/features/museum/MuseumPlayer.java	(working copy)
@@ -0,0 +1,92 @@
+package l2r.features.museum;
+
+import java.util.HashMap;
+import java.util.Map;
+
+public class MuseumPlayer
+{
+	int _objectId;
+	String _name;
+	HashMap<String, long[]> _data;
+	
+	public MuseumPlayer(final int objectId, final String name, final HashMap<String, long[]> data)
+	{
+		_objectId = objectId;
+		_name = name;
+		_data = data;
+	}
+	
+	public long getData(final RefreshTime time, final String type)
+	{
+		if (!_data.containsKey(type))
+		{
+			return 0L;
+		}
+		return _data.get(type)[time.ordinal()];
+	}
+	
+	public long[] getData(final String type)
+	{
+		if (!_data.containsKey(type))
+		{
+			return null;
+		}
+		return _data.get(type);
+	}
+	
+	public void resetData(final RefreshTime time)
+	{
+		if (time.equals(RefreshTime.Total))
+		{
+			return;
+		}
+		final HashMap<String, long[]> data = new HashMap<>();
+		long[] d =
+		{
+			0L,
+			0L,
+			0L,
+			0L
+		};
+		for (final Map.Entry<String, long[]> entry : _data.entrySet())
+		{
+			d = entry.getValue();
+			d[time.ordinal()] = 0L;
+			data.put(entry.getKey(), d);
+		}
+		_data = data;
+	}
+	
+	public HashMap<String, long[]> getData()
+	{
+		return _data;
+	}
+	
+	public void addData(final String type, final long data)
+	{
+		long[] d =
+		{
+			0L,
+			0L,
+			0L,
+			0L
+		};
+		if (this.getData(type) != null)
+		{
+			d = this.getData(type);
+		}
+		final long[] array = d;
+		final int n = 0;
+		array[n] += data;
+		final long[] array2 = d;
+		final int n2 = 1;
+		array2[n2] += data;
+		final long[] array3 = d;
+		final int n3 = 2;
+		array3[n3] += data;
+		final long[] array4 = d;
+		final int n4 = 3;
+		array4[n4] += data;
+		_data.put(type, d);
+	}
+}
Index: java/l2r/features/museum/L2MuseumStatueInstance.java
===================================================================
--- java/l2r/features/museum/L2MuseumStatueInstance.java	(revision 0)
+++ java/l2r/features/museum/L2MuseumStatueInstance.java	(working copy)
@@ -0,0 +1,194 @@
+package l2r.features.museum;
+
+import java.sql.Connection;
+import java.sql.PreparedStatement;
+import java.sql.ResultSet;
+
+import l2r.L2DatabaseFactory;
+import l2r.gameserver.communitybbs.Managers.MuseumBBSManager;
+import l2r.gameserver.enums.InstanceType;
+import l2r.gameserver.model.CharSelectInfoPackage;
+import l2r.gameserver.model.actor.L2Npc;
+import l2r.gameserver.model.actor.instance.L2PcInstance;
+import l2r.gameserver.model.actor.templates.L2NpcTemplate;
+import l2r.gameserver.network.serverpackets.ActionFailed;
+import l2r.gameserver.network.serverpackets.CharSelectionInfo;
+import l2r.gameserver.network.serverpackets.ShowBoard;
+
+import org.slf4j.Logger;
+import org.slf4j.LoggerFactory;
+
+public final class L2MuseumStatueInstance extends L2Npc
+{
+	public static final Logger _log = LoggerFactory.getLogger(L2MuseumStatueInstance.class);
+	int _type;
+	int _playerObjectId;
+	CharSelectInfoPackage _charLooks;
+	MuseumCategory _category;
+	
+	public L2MuseumStatueInstance(final L2NpcTemplate template, final int playerObjectId, final int type)
+	{
+		super(template);
+		setInstanceType(InstanceType.L2MuseumStatueInstance);
+		_playerObjectId = playerObjectId;
+		_type = type;
+		restoreCharLooks();
+		_category = MuseumManager.getInstance().getAllCategories().get(type);
+		setTitle(_category.getTypeName());
+	}
+	
+	public void restoreCharLooks()
+	{
+		try (final Connection con = L2DatabaseFactory.getInstance().getConnection();
+			final PreparedStatement statement = con.prepareStatement("SELECT * FROM characters WHERE charId=?"))
+		{
+			statement.setInt(1, _playerObjectId);
+			try (final ResultSet rset = statement.executeQuery())
+			{
+				if (rset.next())
+				{
+					_charLooks = CharSelectionInfo.restoreChar(rset);
+					if (_charLooks == null)
+					{
+						System.out.println("Player with id[" + _playerObjectId + "] not found.");
+					}
+				}
+			}
+		}
+		catch (Exception e)
+		{
+			_log.warn("Could not restore char info: " + e.getMessage(), e);
+		}
+	}
+	
+	public CharSelectInfoPackage getCharLooks()
+	{
+		return _charLooks;
+	}
+	
+	@Override
+	public void onBypassFeedback(final L2PcInstance player, final String command)
+	{
+		player.sendPacket(ActionFailed.STATIC_PACKET);
+		super.onBypassFeedback(player, command);
+	}
+	
+	@Override
+	public void showChatWindow(final L2PcInstance player)
+	{
+		String html = "<html><body scroll=no>";
+		html += showStatue();
+		html += "</body></html>";
+		separateAndSend(html, player);
+	}
+	
+	public String showStatue()
+	{
+		String html = "";
+		html += "<br><br><br><center><table><tr><td width=25></td><td><table border=1 bgcolor=3b3c34><tr><td>";
+		html = html + "<br><center><font name=\"ScreenMessageLarge\" color=b7b8b2>" + _category.getTypeName() + "</font></center>";
+		html += "<table><tr>";
+		if (!_category.getRefreshTime().equals(RefreshTime.Total))
+		{
+			html += "<td align=center width=260>";
+			html = html + "<button value=\"" + _category.getRefreshTime().name() + " Rankings\" action=\"\" fore=\"L2UI_CT1.Button_DF_Calculator\" back=\"L2UI_CT1.Button_DF_Calculator_Down\" width=\"257\" height=\"24\"/>";
+			html += "</td>";
+		}
+		html = html + "<td align=center width=" + (_category.getRefreshTime().equals(RefreshTime.Total) ? 520 : 260) + ">";
+		html += "<button value=\"Total Rankings\" action=\"\" fore=\"L2UI_CT1.Button_DF_Calculator\" back=\"L2UI_CT1.Button_DF_Calculator_Down\" width=\"257\" height=\"24\"/>";
+		html += "</td>";
+		html += "</tr><tr>";
+		if (!_category.getRefreshTime().equals(RefreshTime.Total))
+		{
+			html += "<td align=center width=260>";
+			for (int i = 0; i < 5; ++i)
+			{
+				String name = "No information.";
+				String value = "No information.";
+				int cellSpacing = -1;
+				if (_category.getAllStatuePlayers().size() > i)
+				{
+					final TopPlayer player = _category.getAllStatuePlayers().get(i + 1);
+					if (player != null)
+					{
+						name = player.getName();
+						final long count = player.getCount();
+						value = MuseumBBSManager.getInstance().convertToValue(count, _category.isTimer(), _category.getAdditionalText());
+						cellSpacing = ((count > 999L) ? -3 : -2);
+					}
+				}
+				final String bgColor = (i == 0) ? "746833" : (((i % 2) == 1) ? "171612" : "23221e");
+				final String numberColor = (i == 0) ? "ffca37" : "dededf";
+				final String nameColor = (i == 0) ? "eac842" : "e2e2e0";
+				final String valueColor = (i == 0) ? "eee79f" : "a78d6c";
+				html = html + "<table width=250 bgcolor=" + bgColor + " height=42><tr>";
+				html = html + "<td width=50 align=center><font color=" + numberColor + " name=ScreenMessageLarge />" + ((i < 1) ? ("{" + (i + 1) + "}") : (i + 1)) + "</font></td>";
+				html += "<td width=200 align=left>";
+				html = html + "<table cellspacing=" + cellSpacing + "><tr><td width=200><font color=" + nameColor + " name=ScreenMessageSmall>" + name + "</font></td></tr><tr><td width=200><font color=" + valueColor + " name=ScreenMessageSmall>" + value + "</font></td></tr></table>";
+				html += "<img src=\"L2UI.SquareBlank\" width=1 height=5/></td>";
+				html += "";
+				html += "</tr></table><img src=\"L2UI.SquareGray\" width=250 height=1/>";
+			}
+			html += "</td>";
+		}
+		html = html + "<td align=center width=" + (_category.getRefreshTime().equals(RefreshTime.Total) ? 520 : 260) + ">";
+		for (int i = 0; i < 5; ++i)
+		{
+			String name = "No information.";
+			String value = "No information.";
+			int cellSpacing = -1;
+			if (_category.getAllTotalTops().size() > i)
+			{
+				final TopPlayer player = _category.getAllTotalTops().get(i + 1);
+				if (player != null)
+				{
+					name = player.getName();
+					final long count = player.getCount();
+					value = MuseumBBSManager.getInstance().convertToValue(count, _category.isTimer(), _category.getAdditionalText());
+					cellSpacing = ((count > 999L) ? -3 : -2);
+				}
+			}
+			final String bgColor = (i == 0) ? "746833" : (((i % 2) == 1) ? "171612" : "23221e");
+			final String numberColor = (i == 0) ? "ffca37" : "dededf";
+			final String nameColor = (i == 0) ? "eac842" : "e2e2e0";
+			final String valueColor = (i == 0) ? "eee79f" : "a78d6c";
+			html = html + "<table width=250 bgcolor=" + bgColor + " height=42><tr>";
+			html = html + "<td width=50 align=center><font color=" + numberColor + " name=ScreenMessageLarge />" + ((i < 1) ? ("{" + (i + 1) + "}") : (i + 1)) + "</font></td>";
+			html += "<td width=200 align=left>";
+			html = html + "<table cellspacing=" + cellSpacing + "><tr><td width=200><font color=" + nameColor + " name=ScreenMessageSmall>" + name + "</font></td></tr><tr><td width=200><font color=" + valueColor + " name=ScreenMessageSmall>" + value + "</font></td></tr></table>";
+			html += "<img src=\"L2UI.SquareBlank\" width=1 height=5/></td>";
+			html += "";
+			html += "</tr></table><img src=\"L2UI.SquareGray\" width=250 height=1/>";
+		}
+		html += "</td>";
+		html += "</tr></table><br><br></td></tr></table></td></tr></table>";
+		html += "</center>";
+		return html;
+	}
+	
+	protected void separateAndSend(final String html, final L2PcInstance acha)
+	{
+		if (html == null)
+		{
+			return;
+		}
+		if (html.length() < 4096)
+		{
+			acha.sendPacket(new ShowBoard(html, "101"));
+			acha.sendPacket(new ShowBoard((String) null, "102"));
+			acha.sendPacket(new ShowBoard((String) null, "103"));
+		}
+		else if (html.length() < 8192)
+		{
+			acha.sendPacket(new ShowBoard(html.substring(0, 4096), "101"));
+			acha.sendPacket(new ShowBoard(html.substring(4096), "102"));
+			acha.sendPacket(new ShowBoard((String) null, "103"));
+		}
+		else if (html.length() < 16384)
+		{
+			acha.sendPacket(new ShowBoard(html.substring(0, 4096), "101"));
+			acha.sendPacket(new ShowBoard(html.substring(4096, 8192), "102"));
+			acha.sendPacket(new ShowBoard(html.substring(8192), "103"));
+		}
+	}
+}
