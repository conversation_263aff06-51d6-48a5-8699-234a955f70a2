# Memory Leak và RAM Usage Analysis Report

## Tổng quan dự án
- **Dự án**: L2<PERSON>ssence Crusader GVE Server
- **Database**: MariaDB với connection pool 50 connections
- **Thread Pool**: 5 scheduled pools, 1 instant pool
- **Cấu hình RAM**: <PERSON><PERSON><PERSON> kiểm tra JVM heap size

## 🔍 C<PERSON>c vấn đề Memory Leak đã phát hiện

### 1. **CRITICAL - Static Collections không được cleanup**

#### A. World Object Management
```java
// File: java/club/projectessence/gameserver/model/World.java
private static final Map<Integer, PlayerInstance> _allPlayers = new ConcurrentHashMap<>();
private static final Map<Integer, PlayerInstance> _allWaterPlayers = new ConcurrentHashMap<>();
private static final Map<Integer, WorldObject> _allObjects = new ConcurrentHashMap<>();
private static final Map<Integer, PetInstance> _petsInstance = new ConcurrentHashMap<>();
```
**Rủi ro**: <PERSON><PERSON>c <PERSON> này lưu trữ tất cả players và objects, có thể tích lũy theo thời gian nếu không cleanup đúng cách.

#### B. Effect Lists và Buff Management
```java
// File: java/club/projectessence/gameserver/model/EffectList.java
private final Queue<BuffInfo> _actives = new ConcurrentLinkedQueue<>();
```
**Rủi ro**: BuffInfo objects có thể không được remove hoàn toàn khi player disconnect.

### 2. **HIGH - Database Connection Issues**

#### A. Connection Pool Configuration
- **MaximumDbConnections**: 50 (có thể quá thấp cho 2000 players)
- **Rủi ro**: Connection starvation và potential leaks

#### B. PreparedStatement Usage
- Một số nơi có thể không close PreparedStatement đúng cách
- Try-with-resources được sử dụng ở hầu hết nơi (tốt)

### 3. **MEDIUM - Caching Mechanisms**

#### A. HTML Cache
```java
// File: java/club/projectessence/gameserver/cache/HtmCache.java
private static final Map<String, String> HTML_CACHE = Config.LAZY_CACHE ? new ConcurrentHashMap<>() : new HashMap<>();
```
**Cấu hình hiện tại**: `LazyCache = False` - Load tất cả HTML vào memory lúc startup
**Rủi ro**: Tốn RAM nhưng performance tốt hơn

#### B. Warehouse Cache
**Cấu hình hiện tại**: `WarehouseCache = False` - Đã tắt để tiết kiệm RAM

### 4. **MEDIUM - Thread Pool và Background Tasks**

#### A. ThreadPool Purge
```java
// File: java/club/projectessence/commons/concurrent/ThreadPool.java
scheduleAtFixedRate(this::purge, 60000, 60000); // Purge mỗi 60 giây
```
**Tình trạng**: Có cơ chế purge tự động (tốt)

#### B. Scheduled Tasks
- PrecautionaryRestartManager: Monitor CPU/Memory
- DeathDisconnectionManager: Cleanup dead players
- DecayTaskManager: Cleanup corpses

## 🛠️ Khuyến nghị tối ưu hóa

### 1. **JVM Memory Settings** (CRITICAL)
Cần kiểm tra và tối ưu JVM heap size cho 2000 players:

```bash
# Khuyến nghị cho 2000 players
-Xms4G -Xmx8G
-XX:+UseG1GC
-XX:MaxGCPauseMillis=200
-XX:+DisableExplicitGC
```

### 2. **Database Connection Pool** (HIGH)
```ini
# Tăng connection pool cho 2000 players
MaximumDbConnections = 100
```

### 3. **Memory Monitoring** (HIGH)
Bật PrecautionaryRestartManager:
```ini
PrecautionaryRestartEnabled = True
PrecautionaryRestartMemory = True
PrecautionaryRestartPercentage = 85
```

### 4. **Cleanup Optimizations** (MEDIUM)
```ini
# Tăng tần suất cleanup
CharacterDataStoreInterval = 5
DatabaseCleanUp = True
SaveDroppedItem = False  # Tắt để tiết kiệm RAM
```

### 5. **Cache Optimizations** (MEDIUM)
```ini
# Bật lazy cache để tiết kiệm RAM
LazyCache = True
CacheCharNames = False  # Tắt để tiết kiệm RAM startup
```

## 📊 Monitoring Tools có sẵn

### 1. **Built-in Memory Monitoring**
- `GameServer.getUsedMemoryMB()` - Theo dõi RAM usage
- `PrecautionaryRestartManager` - Auto restart khi RAM cao
- Admin command `//debug gc_task_start` - Manual GC

### 2. **Performance Metrics**
- DeadLock Detector - Phát hiện thread deadlocks
- SystemPanel UI - Hiển thị real-time stats

## ⚠️ Cảnh báo quan trọng

### 1. **Potential Memory Leaks**
- Player disconnect không cleanup hoàn toàn effects
- Minion references có thể leak (đã có `minion.setLeader(null)`)
- Event listeners có thể không được remove

### 2. **High RAM Usage Areas**
- World object storage (players, NPCs, items)
- Effect/Buff management
- HTML caching (nếu không dùng lazy cache)
- Database connection pool

## 🔧 Script kiểm tra Memory

Tạo script để monitor memory usage real-time:
```java
// Admin command để check memory
//debug memory_status
```

## 📈 Khuyến nghị cho 2000 players

1. **RAM tối thiểu**: 8GB heap size
2. **Database connections**: 100-150
3. **Bật lazy loading** cho HTML và character names
4. **Monitor memory** với PrecautionaryRestartManager
5. **Regular GC tuning** với G1GC
6. **Cleanup tasks** chạy thường xuyên hơn

## 🎯 Kết luận

Dự án có **cơ chế memory management tương đối tốt** nhưng cần tối ưu cho 2000 players:

**Ưu điểm**:
- Sử dụng try-with-resources cho database
- Có ThreadPool purge mechanism
- Có memory monitoring tools
- Cleanup tasks cho objects

**Cần cải thiện**:
- JVM heap size configuration
- Database connection pool size
- Enable memory monitoring
- Optimize caching strategies

**Risk Level**: **MEDIUM** - Không có memory leak nghiêm trọng nhưng cần tối ưu cho high load.
